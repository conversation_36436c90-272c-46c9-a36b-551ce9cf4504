
/*****************************************************************************
 Description  : 【NERG交付】远端通信支持会话ID校验、连接最长生命周期设置、防暴力破解
 Notes        :
                001.鉴权模式下，建连时不指定防暴力破解注册函数预期报错，建连失败
                002.userPolicyMode为0 ，建连时指定防暴力破解注册函数预期建连成功
                003.userPolicyMode为1 ，建连时指定防暴力破解注册函数预期建连成功

                004.使用gmadmin 修改配置项 tcpRetryTimeout失败
                005.使用客户端接口修改配置项 tcpRetryTimeout值失败
                006.userPolicyMode为0 tcpRetryTimeout改为-1，启动服务，预期启动失败
                007.userPolicyMode为1 tcpRetryTimeout改为-1，启动服务，预期启动失败

                008.userPolicyMode为2 tcpRetryTimeout改为-1，启动服务，预期启动失败
                009.userPolicyMode为0 tcpRetryTimeout改为101，启动服务，预期启动失败
                010.userPolicyMode为1 tcpRetryTimeout改为101，启动服务，预期启动失败
                011.userPolicyMode为2 tcpRetryTimeout改为101，启动服务，预期启动失败

                012.使用gmadmin 修改配置项tcpConnTimeout失败
                013.使用客户端接口修改配置项值tcpConnTimeout失败
                014.tcpConnTimeout改为-1，启动服务，预期启动失败
                015.tcpConnTimeout改为101，启动服务，预期启动失败

                016.userPolicyMode为0，建连失败5次后，再次建连断连能够成功，预期非强鉴权模式，防暴力破解不生效
                017.userPolicyMode为1，建连失败5次后，再次建连断连能够成功 预期非强鉴权模式，防暴力破解不生效
                018.userPolicyMode为2，开启防暴力破解配置项
构造建连失败4次后，再次建连断连能够成功，预期失败次数低于5次不会拉黑ip 019.userPolicyMode为2，开启防暴力破解配置项
构造建连失败5次后，再次建连失败，预期失败次数等于5次时，会拉黑ip

                020.userPolicyMode为2，开启防暴力破解配置项
构造建连失败5次后，再次建连失败，超过ip拉黑时间后能够建连成功，预期拉黑时，建连失败，超过拉黑时间建连成功
                021.userPolicyMode为2，开启防暴力破解配置项
构造建连失败4次后，再次建连成功，再次构造建连失败后，再次建连成功，预期失败计数能够重置
                022.userPolicyMode为2，开启防暴力破解配置项
构造建连失败4次后，再次建连成功，再次构造建连失败5次后，再次建连失败，预期失败计数能够重置
                023.userPolicyMode为2，开启防暴力破解配置项
构造建连失败5次后，再次建连失败，超过拉黑时间后，再次构造建连失败4次后，再次建连成功，预期失败计数能够重置

                024.userPolicyMode为2，关闭防暴力破解配置项 构造建连失败5次后，再次建连成功，预期防暴力破解不生效
                025.使用测试接口GmcModifyCliSessionId修改session id 为不同值，预期请求失败
                026.设置连接不超时，超过默认10min超时时间，连接正常
                027.设置连接100min超时，预期100min连接无请求，100min后连接自动断开，

                028.设置连接100min超时，预期99min连接存在请求，100min后连接正常，
                029.设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功
                030.设置连接1min超时，1min内连接请求正常处理，等待1min内后，连接自动断开，请求处理失败，再次建连请求处理成功
                031.设置连接1min超时，1min内连接请求正常处理，等待1min内后，连接自动断开，请求处理失败，再次建连请求处理成功（订阅关系被回收）
                032.userPolicyMode为2 ，开启防暴力破解配置项
建连失败5次后，再次建连直到超过ip拉黑时间（5min）后能够建连成功
                033.多线程并发10个线程，设置连接超时时间为1min，每个线程100次循环建连等待70s，连接超时回收，再次建连
 History      :
 Author       : youwanyong ywx1157510
 Modification :
 Date         : 2025/04/28
*****************************************************************************/
#include "t_rd_nerg.h"

#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024

class ConnEnhance : public testing::Test {
protected:
    static void SetUpTestCase() {

    };
    static void TearDownTestCase() {

    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ConnEnhance::SetUp()
{
    char ipValue[128] = "ifconfig eth0 | awk '/inet / {print $2}' | cut -d: -f2";
    char cmd[128] = "iptables -D INPUT -s %s -j REJECT";

    (void)sprintf(cmd, "iptables -D INPUT -s ${%s} -j REJECT", ipValue);
    AW_FUN_Log(LOG_INFO, "cmd is %s", cmd);
    system("sudo systemctl stop firewalld");
    system("sudo systemctl disable firewalld");
}

void ConnEnhance::TearDown()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    char ipValue[128] = "ifconfig eth0 | awk '/inet / {print $2}' | cut -d: -f2";
    char cmd[128] = "iptables -D INPUT -s %s -j REJECT";

    (void)sprintf(cmd, "iptables -D INPUT -s ${%s} -j REJECT", ipValue);
    AW_FUN_Log(LOG_INFO, "cmd is %s", cmd);
    system("sudo iptables -L -v -n");
    system(cmd);
    system("sudo systemctl start firewalld");
    system("sudo systemctl enable firewalld");
}

// 修改配置项
void testChangeConfig(int32_t userPolicyMode, int32_t tcpRetryTimeout = 10, int32_t tcpConnTimeout = 10)
{
    char cmd[1024] = "0";
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sed -i '/tcpRetryTimeout/d' /usr/local/file/gmserver.ini");
    system("sed -i '/tcpConnTimeout/d' /usr/local/file/gmserver.ini");
    // 新增nerg配置項
    system("sed -i '1i tcpRetryTimeout = 5' /usr/local/file/gmserver.ini");
    system("sed -i '1i tcpConnTimeout = 10' /usr/local/file/gmserver.ini");
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=%d\"", userPolicyMode);
    system(cmd);
    if (tcpRetryTimeout != 10) {
        (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh \"tcpRetryTimeout=%d\"", tcpRetryTimeout);
        system(cmd);
    }
    if (tcpConnTimeout != 10) {
        (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh \"tcpConnTimeout=%d\"", tcpConnTimeout);
        system(cmd);
    }
    system("sh ${TEST_HOME}/tools/stop.sh -f");
}

// 起服务
void testStartService()
{
    AW_FUN_Log(LOG_STEP, "服务启动.");
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sed -i '/tcpConnTimeout/d' /usr/local/file/gmserver.ini");
    system("sed -i '/tcpConnTimeout/d' /usr/local/file/gmserver.ini");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 使用自定义的配置启动服务
void testStartServiceSelf()
{
    AW_FUN_Log(LOG_STEP, "服务启动.");
    system("mv ../092_NEGRTcpCommunication/01_ServerAdpt/096TestServerAdpt gmserver");
    system("gmserver -p /usr/local/file/gmserver.ini -b &");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
Status DbTcpIpControlStub(const char *ip, uint32_t cmd)
{
    char command[1024];
    DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "！！！DbTcpIpControlStub exec inv: %s\n", command);
    if (ip == NULL) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (cmd == 1) {
        // 拒绝该IP连接
        (void)snprintf_s(command, sizeof(command), sizeof(command), "iptables -A INPUT -s \"%s\" -j REJECT", ip);
    } else if (cmd == 0) {
        // 删除IP连接
        snprintf_s(command, sizeof(command), sizeof(command) - 1, "iptables -D INPUT -s \"%s\" -j REJECT", ip);
    }
    if (system(command) != 0) {
        DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "exec inv: %s\n", command);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    AW_FUN_Log(LOG_STEP, "DbTcpIpControlStub !!!!.");
    return GMERR_OK;
}

// 鉴权文件导入
void inportAuthFile(int32_t userPolicyMode)
{
    char cmd[1024] = "0";
    if (userPolicyMode != 2) {
        // 非强鉴权模式不需要导入鉴权
        return;
    }
    AW_FUN_Log(LOG_STEP, "导入权限.");
    int32_t ret = 1;
    const char *allow_list_file = "./allow_list/allow_list.gmuser";
    snprintf(
        cmd, MAX_CMD_SIZE, "sh $TEST_HOME/tools/importPermiss.sh -c %s -f %s", "import_allowlist", allow_list_file);
    AW_FUN_Log(LOG_STEP, "[INFO]%s\n", cmd);
    ret = executeCommand(cmd, "Import single allow list file", "successfully.");
    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "import_allowlist no ok！！");
        return;
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 鉴权文件撤销
void revokeAuthFile(int32_t userPolicyMode)
{
    if (userPolicyMode != 2) {
        // 非强鉴权模式不需要撤销
        return;
    }
    AW_FUN_Log(LOG_STEP, "撤销权限.");
    char cmd[1024] = "0";
    int32_t ret = 1;
    const char *allow_list_file = "./allow_list/allow_list.gmuser";
    memset(cmd, 0, sizeof(cmd));
    snprintf(
        cmd, MAX_CMD_SIZE, "sh $TEST_HOME/tools/importPermiss.sh -c %s -f %s", "remove_allowlist", allow_list_file);
    AW_FUN_Log(LOG_STEP, "[INFO]%s\n", cmd);
    ret = executeCommand(cmd, "remove allowlist", "successfully.");
    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "remove_allowlist no ok！！");
        return;
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
void TestRegAdaptFuncsProcPolicyTCPFun(Status expectRet)
{
    GmAdptFuncsHandle handle = NULL;
    int32_t ret = GmCreateAdptFuncsHandle(&handle);
    ASSERT_EQ(ret, STATUS_OK);
    // TCP
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_OPEN_LISTEN, (GmAdptFunc)DbTcpOpenListenStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_ACCEPT, (GmAdptFunc)DbTcpAcceptStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_CONNECT, (GmAdptFunc)DbTcpConnectStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_LOCAL_NAME, (GmAdptFunc)TcpGetLocalNameStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_PEER_NAME, (GmAdptFunc)DbTcpGetPeerNameStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_OPEN_LISTEN, (GmAdptFunc)DbUsockOpenListenStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_ACCEPT, (GmAdptFunc)DbUsockAcceptStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_CONNECT, (GmAdptFunc)DbUsockConnectStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_GET_CRED, (GmAdptFunc)DbUsockGetCredStub);
    // SOCK
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_CLOSE, (GmAdptFunc)DbSockClosePipeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_NONBLOCK, (GmAdptFunc)DbSockSetNoBlockStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_RECV_TIMEOUT, (GmAdptFunc)DbBlockSockSetRecvTimeoutStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_SEND_TIMEOUT, (GmAdptFunc)DbBlockSockSetSendTimeoutStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_BOLCK_RECV, (GmAdptFunc)DbBlockSockRecvStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_BOLCK_SEND, (GmAdptFunc)DbBlockSockSendStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_NONBOLCK_RECV, (GmAdptFunc)DbNonBlockSockRecvStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_NONBOLCK_SEND, (GmAdptFunc)DbNonBlockSockSendStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_RECV_BUFF_SIZE, (GmAdptFunc)DbSockGetRecvBuffSizeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_SEND_BUFF_SIZE, (GmAdptFunc)DbSockGetSendBuffSizeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_REMAIN_RECV_BYTES, (GmAdptFunc)DbSockGetUnRecvBytesStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_REMAIN_SEND_BYTES, (GmAdptFunc)DbSockGetUnSendBytesStub);
    // policy
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_CREDIT, (GmAdptFunc)DbTcpGetCreditStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_LOGIN_RULE, (GmAdptFunc)DbTcpLoginVerifyStub);
    ret |= GmRegAdaptFuncs(handle);
    GmDestroyAdptFuncsHandle(&handle);
    // 避免重复注册报错
    if (ret != GMERR_DUPLICATE_OBJECT) {
        ASSERT_EQ(ret, STATUS_OK);
    }
}

void testRegAdaptFuncsProcTCP096(Status expectRet)
{
    GmAdptFuncsHandle handle = NULL;
    int32_t ret = GmCreateAdptFuncsHandle(&handle);
    ASSERT_EQ(ret, STATUS_OK);
    AW_FUN_Log(LOG_STEP, "testRegAdaptFuncsProcTCP096.");
    // TCP
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_OPEN_LISTEN, (GmAdptFunc)DbTcpOpenListenStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_ACCEPT, (GmAdptFunc)DbTcpAcceptStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_CONNECT, (GmAdptFunc)DbTcpConnectStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_LOCAL_NAME, (GmAdptFunc)TcpGetLocalNameStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_PEER_NAME, (GmAdptFunc)DbTcpGetPeerNameStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_OPEN_LISTEN, (GmAdptFunc)DbUsockOpenListenStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_ACCEPT, (GmAdptFunc)DbUsockAcceptStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_CONNECT, (GmAdptFunc)DbUsockConnectStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_USOCKET_GET_CRED, (GmAdptFunc)DbUsockGetCredStub);
    // SOCK
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_CLOSE, (GmAdptFunc)DbSockClosePipeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_NONBLOCK, (GmAdptFunc)DbSockSetNoBlockStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_RECV_TIMEOUT, (GmAdptFunc)DbBlockSockSetRecvTimeoutStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_SET_SEND_TIMEOUT, (GmAdptFunc)DbBlockSockSetSendTimeoutStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_BOLCK_RECV, (GmAdptFunc)DbBlockSockRecvStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_BOLCK_SEND, (GmAdptFunc)DbBlockSockSendStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_NONBOLCK_RECV, (GmAdptFunc)DbNonBlockSockRecvStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_NONBOLCK_SEND, (GmAdptFunc)DbNonBlockSockSendStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_RECV_BUFF_SIZE, (GmAdptFunc)DbSockGetRecvBuffSizeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_SEND_BUFF_SIZE, (GmAdptFunc)DbSockGetSendBuffSizeStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_REMAIN_RECV_BYTES, (GmAdptFunc)DbSockGetUnRecvBytesStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_REMAIN_SEND_BYTES, (GmAdptFunc)DbSockGetUnSendBytesStub);
    // policy
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_GET_CREDIT, (GmAdptFunc)DbTcpGetCreditStub);
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_LOGIN_RULE, (GmAdptFunc)DbTcpLoginVerifyStub);
    // 防暴力破解注册函数
    ret |= GmAddAdptFunc(handle, GM_ADPT_FUNC_SOCKET_IP_CONTROL, (GmAdptFunc)DbTcpIpControlStub);
    ret |= GmRegAdaptFuncs(handle);
    GmDestroyAdptFuncsHandle(&handle);
    AW_FUN_Log(LOG_STEP, "testRegAdaptFuncsProcTCP096 ret = %d.", ret);
    // 避免重复注册报错
    if (ret != GMERR_DUPLICATE_OBJECT) {
        ASSERT_EQ(ret, GMERR_OK);
    }
}
/* ****************************************************************************
 Description: 001.鉴权模式下，建连时不指定防暴力破解注册函数预期报错，建连失败
**************************************************************************** */
TEST_F(ConnEnhance, Other_096_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "001.鉴权模式下，建连时不指定防暴力破解注册函数预期报错，建连失败.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    testChangeConfig(userPolicyMode);

    testStartServiceSelf();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    TestRegAdaptFuncsProcPolicyTCPFun(expectRetParam);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连时不指定防暴力破解注册函数预期报错，建连失败
    AW_FUN_Log(LOG_STEP, "建连时不指定防暴力破解注册函数预期报错，建连失败.");
    // 导入白名单后再次建连失败，拉黑默认机制生效
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        // 建连失败.特殊报错
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        conn = NULL;
        stmt = NULL;
    }

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    // 建连失败
   AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description: 002.userPolicyMode为0 ，建连时指定防暴力破解注册函数预期建连成功
**************************************************************************** */
TEST_F(ConnEnhance, Other_096_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为0 ，建连时指定防暴力破解注册函数预期建连成功.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    // userPolicyMode为0
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.userPolicyMode为1 ，建连时指定防暴力破解注册函数预期建连成功
TEST_F(ConnEnhance, Other_096_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为1 ，建连时指定防暴力破解注册函数预期建连成功.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 1;
    // userPolicyMode为0
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.使用gmadmin 修改配置项 tcpRetryTimeout失败
TEST_F(ConnEnhance, Other_096_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "使用gmadmin 修改配置项 tcpRetryTimeout失败.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = 99;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "gmadmin 修改配置项 tcpRetryTimeout失败.");
    char cmd[1024] = {0};
    (void)sprintf(cmd, "gmadmin -cfgName %s -cfgVal %d -s %s", configName, configVal, g_connServer);
    // 预期当前值
    system(cmd);
    ret = executeCommand(cmd, "before setting config:", "config name: tcpRetryTimeout", "config current value: 5",
        "config default value: 5", "config change mode: not allowed");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.使用客户端接口修改配置项 tcpRetryTimeout值失败
TEST_F(ConnEnhance, Other_096_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "使用客户端接口修改配置项 tcpRetryTimeout值失败.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = 99;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用客户端接口修改配置项 tcpRetryTimeout值失败.");
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.userPolicyMode为0 tcpRetryTimeout改为-1，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为0 tcpRetryTimeout改为-1，启动服务，预期启动失败.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = -1;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, configVal);
    testStartService();

    AW_CHECK_LOG_BEGIN();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.userPolicyMode为1 tcpRetryTimeout改为-1，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为1 tcpRetryTimeout改为-1，启动服务，预期启动失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = -1;
    int32_t userPolicyMode = 1;
    testChangeConfig(userPolicyMode, configVal);
    testStartService();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.userPolicyMode为2 tcpRetryTimeout改为-1，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为2 tcpRetryTimeout改为-1，启动服务，预期启动失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = -1;
    int32_t userPolicyMode = 2;
    testChangeConfig(userPolicyMode, configVal);
    testStartService();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.userPolicyMode为0 tcpRetryTimeout改为101，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为0 tcpRetryTimeout改为101，启动服务，预期启动失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = 101;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, configVal);
    testStartService();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.userPolicyMode为1 tcpRetryTimeout改为101，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为1 tcpRetryTimeout改为101，启动服务，预期启动失败  ");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = 101;
    int32_t userPolicyMode = 1;
    testChangeConfig(userPolicyMode, configVal);
    testStartService();

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.userPolicyMode为2 tcpRetryTimeout改为101，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为2 tcpRetryTimeout改为101，启动服务，预期启动失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = 101;
    int32_t userPolicyMode = 2;
    testChangeConfig(userPolicyMode, configVal);
    testStartService();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.使用gmadmin 修改配置项tcpConnTimeout失败
TEST_F(ConnEnhance, Other_096_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "使用gmadmin 修改配置项 tcpConnTimeout失败.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpConnTimeout";
    int32_t configVal = 99;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "gmadmin 修改配置项 tcpConnTimeout失败.");
    char cmd[1024] = {0};
    (void)sprintf(cmd, "gmadmin -cfgName %s -cfgVal %d -s %s", configName, configVal, g_connServer);
    system(cmd);
    // 预期当前值
    ret = executeCommand(cmd, "before setting config:", "config name: tcpConnTimeout", "config current value: 10",
        "config default value: 10", "config change mode: not allowed");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.使用客户端接口修改配置项值tcpConnTimeout失败
TEST_F(ConnEnhance, Other_096_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "使用客户端接口修改配置项值tcpConnTimeout失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpConnTimeout";
    int32_t configVal = 99;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用客户端接口修改配置项值tcpConnTimeout失败");
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.tcpConnTimeout改为-1，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "tcpConnTimeout改为-1，启动服务，预期启动失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpConnTimeout";
    int32_t configVal = -1;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, configVal);
    testStartService();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.tcpConnTimeout改为101，启动服务，预期启动失败
TEST_F(ConnEnhance, Other_096_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "tcpConnTimeout改为101，启动服务，预期启动失败");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 配置项名
    char configName[48] = "tcpConnTimeout";
    int32_t configVal = 101;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, configVal);
    testStartService();
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_CHECK_LOG_BEGIN();

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.userPolicyMode为0，建连失败5次后，再次建连断连能够成功，预期非强鉴权模式，防暴力破解不生效
TEST_F(ConnEnhance, Other_096_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为0 ，建连时指定防暴力破解注册函数预期建连成功.");
    int ret = 1;
    int32_t failCycle = 5;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *connArray[MAX_CONN_SIZE + failCycle] = {0};
    GmcStmtT *stmtArray[MAX_CONN_SIZE + failCycle] = {0};
    int32_t userPolicyMode = 0;
    // 开启强鉴权模式
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t actualConnNum = MAX_CONN_SIZE - existConnNum;
    for (int32_t i = 0; i < actualConnNum + failCycle; i++) {
        ret = testGmcConnect(&connArray[i], &stmtArray[i]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "conn no ok!!! ");
            continue;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < actualConnNum; i++) {
        ret = testGmcDisconnect(connArray[i], stmtArray[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        connArray[i] = NULL;
        stmtArray[i] = NULL;
    }

    inportAuthFile(userPolicyMode);
    // 失败次数累计5次后还能再次建连成功
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.userPolicyMode为1，建连失败5次后，再次建连断连能够成功 预期非强鉴权模式，防暴力破解不生效
TEST_F(ConnEnhance, Other_096_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为1 ，建连时指定防暴力破解注册函数预期建连成功.");
    int ret = 1;
    int32_t failCycle = 5;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *connArray[MAX_CONN_SIZE + failCycle] = {0};
    GmcStmtT *stmtArray[MAX_CONN_SIZE + failCycle] = {0};
    int32_t userPolicyMode = 1;
    // 开启强鉴权模式
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t actualConnNum = MAX_CONN_SIZE - existConnNum;
    for (int32_t i = 0; i < actualConnNum + failCycle; i++) {
        ret = testGmcConnect(&connArray[i], &stmtArray[i]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "conn no ok!!! ");
            continue;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < actualConnNum; i++) {
        ret = testGmcDisconnect(connArray[i], stmtArray[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        connArray[i] = NULL;
        stmtArray[i] = NULL;
    }

    inportAuthFile(userPolicyMode);
    // 失败次数累计5次后还能再次建连成功
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.userPolicyMode为2，开启防暴力破解配置项 构造建连失败4次后，再次建连断连能够成功，预期失败次数低于5次不会拉黑ip
TEST_F(ConnEnhance, Other_096_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为2，开启防暴力破解配置项 "
                         "构造建连失败4次后，再次建连断连能够成功，预期失败次数低于5次不会拉黑ip.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连4次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连4次失败.");
    int32_t cycle = 4;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    }

    // /导入白名单后再次建连成功
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.userPolicyMode为2，开启防暴力破解配置项 构造建连失败5次后，再次建连失败，预期失败次数等于5次时，会拉黑ip
TEST_F(ConnEnhance, Other_096_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP,
        "userPolicyMode为2，开启防暴力破解配置项 构造建连失败5次后，再次建连失败，预期失败次数等于5次时，会拉黑ip.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    int32_t tcpRetryTimeout = 1;
    int32_t tcpConnTimeout = 1; // 连接1min没有请求，后台释放连接
    testChangeConfig(userPolicyMode, tcpRetryTimeout, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // sudo ip addr add *************/24 dev eth0

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连5次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连5次失败.");
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        conn = NULL;
        stmt = NULL;
    }
    // ip被拉黑建连失败
    AW_FUN_Log(LOG_STEP, "ip被拉黑建连失败.");
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    sleep(66);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.userPolicyMode为2，开启防暴力破解配置项
// 构造建连失败5次后，再次建连失败，超过ip拉黑时间后能够建连成功，预期拉黑时，建连失败，超过拉黑时间建连成功
// 拉黑時客户端建连超时，报错18004
TEST_F(ConnEnhance, Other_096_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP,
        "构造建连失败5次后，再次建连失败，超过ip拉黑时间后能够建连成功，预期拉黑时，建连失败，超过拉黑时间建连成功");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
   int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    int32_t tcpRetryTimeout = 5;
    int32_t tcpConnTimeout = 1; // 连接2min没有请求，后台释放连接
    testChangeConfig(userPolicyMode, tcpRetryTimeout, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连5次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连5次失败.");
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "ret is %d.",ret);
        conn = NULL;
        stmt = NULL;
    }
    // ip被拉黑建连失败
    AW_FUN_Log(LOG_STEP, "ip被拉黑建连失败.");
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ret is %d.",ret);
    // 重试3分钟+2分钟
    sleep(130);
    // 导入白名单后再次建连成功
    AW_FUN_Log(LOG_STEP, "拉黑结束导入权限.");
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.userPolicyMode为2，开启防暴力破解配置项
// 构造建连失败4次后，再次建连成功，再次构造建连失败后，再次建连成功，预期失败计数能够重置
TEST_F(ConnEnhance, Other_096_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为2，开启防暴力破解配置项 "
                         "构造建连失败4次后，再次建连断连能够成功，预期失败次数低于5次不会拉黑ip.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    int32_t tcpRetryTimeout = 1;
    int32_t tcpConnTimeout = 1; // 连接1min没有请求，后台释放连接
    testChangeConfig(userPolicyMode, tcpRetryTimeout, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连4次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连4次失败.");
    int32_t cycle = 4;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    }

    // // 导入白名单后再次建连成功
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;

    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    // 再次建连失败
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    revokeAuthFile(userPolicyMode);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.userPolicyMode为2，开启防暴力破解配置项
// 构造建连失败4次后，再次建连成功，再次构造建连失败5次后，再次建连失败，预期失败计数能够重置
TEST_F(ConnEnhance, Other_096_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "userPolicyMode为2，开启防暴力破解配置项 "
                         "构造建连失败4次后，再次建连成功，再次构造建连失败5次后，再次建连失败，预期失败计数能够重置.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    int32_t tcpRetryTimeout = 4;
    int32_t tcpConnTimeout = 1; // 连接1min没有请求，后台释放连接
    testChangeConfig(userPolicyMode, tcpRetryTimeout, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连4次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连4次失败.");
    int32_t cycle = 4;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    }

    // // 导入白名单后再次建连成功
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;

    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    // 再次建连失败
    cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    }
    // 等待超时时间
    sleep(250);
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    revokeAuthFile(userPolicyMode);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.userPolicyMode为2，开启防暴力破解配置项
// 构造建连失败5次后，再次建连失败，超过拉黑时间后，再次构造建连失败4次后，再次建连成功，预期失败计数能够重置
TEST_F(ConnEnhance, Other_096_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP,
        "userPolicyMode为2，开启防暴力破解配置项 "
        "构造建连失败5次后，再次建连失败，超过ip拉黑时间后能够建连成功，预期拉黑时，建连失败，超过拉黑时间建连成功.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 开启强鉴权模式
    int32_t tcpRetryTimeout = 4;
    int32_t tcpConnTimeout = 1; // 连接1min没有请求，后台释放连接
    testChangeConfig(userPolicyMode, tcpRetryTimeout, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连5次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连5次失败.");
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        conn = NULL;
        stmt = NULL;
    }
    // ip被拉黑建连失败
    AW_FUN_Log(LOG_STEP, "ip被拉黑建连失败.");
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // 等待拉黑时间结束 第六次建连失败超时重试耗时3min+66s
    sleep(66);

    // 再次建连失败4次后导入白名单后再次建连成功
    AW_FUN_Log(LOG_STEP, "拉黑结束导入权限.");
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.userPolicyMode为2，关闭防暴力破解配置项 构造建连失败5次后，再次建连成功，预期防暴力破解不生效
TEST_F(ConnEnhance, Other_096_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(
        LOG_STEP, "userPolicyMode为2，关闭防暴力破解配置项 构造建连失败5次后，再次建连成功，预期防暴力破解不生效.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 关闭防暴力破解
    int32_t tcpRetryTimeout = 0;
    // 开启强鉴权模式
    testChangeConfig(userPolicyMode,tcpRetryTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连5次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连5次失败.");
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        conn = NULL;
        stmt = NULL;
    }
    // 导入白名单后再次建连成功
    AW_FUN_Log(LOG_STEP, "防暴力破解不生效导入权限.");
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.使用测试接口GmcModifyCliSessionId修改session id 为不同值，预期请求失败（查询日志sessionid报错长度）
TEST_F(ConnEnhance, Other_096_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "使用测试接口GmcModifyCliSessionId修改session id 为不同值，预期请求失败.");
    int ret = 1;
    int32_t configVal = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode);
    testStartService();
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_CONNECTION_EXCEPTION);
    char configName[48] = "enableLogFold";

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用测试接口GmcModifyCliSessionId修改session id 为不同值
    unsigned char newSessionId[26] = "1234567890123456123456789";
    // 修改session id成功
#if defined(FEATURE_PERSISTENCE) && defined(ENV_EULER)
    AW_FUN_Log(LOG_STEP, "仅nergc存在该接口.");
    GmcModifyCliSessionId(conn, newSessionId);
#endif
    // 使用该连接，预期session id校验失败 请求失败
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    testGmcGetLastError();

    configVal = 1;
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    testGmcGetLastError();

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.设置连接不超时，超过默认10min超时时间，连接正常
TEST_F(ConnEnhance, Other_096_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "设置连接不超时，超过默认10min超时时间，连接正常.");
    int ret = 1;
    int32_t configVal = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, 0);
    testStartService();
    AW_CHECK_LOG_BEGIN();
    char configName[48] = "enableLogFold";

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待连接超时10min —->构建超时减少时间
    sleep(500);

    // 使用该连接，预期请求正常
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();

    configVal = 1;
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.设置连接100min超时，预期100min连接无请求，100min后连接自动断开，
TEST_F(ConnEnhance, Other_096_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "设置连接不超时，超过默认10min超时时间，连接正常.");
    int ret = 1;
    int32_t configVal = 100;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    char configName[48] = "enableLogFold";
    int32_t tcpRetryTimeout = 5;
    int32_t tcpConnTimeout = 1; // 连接1min没有请求，后台释放连接
    testChangeConfig(userPolicyMode, tcpRetryTimeout, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待连接超时100min
    sleep(66);

    // 使用该连接，预期连接已经被回收
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    testGmcGetLastError();

    configVal = 1;
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    testGmcGetLastError();

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.设置连接100min超时，预期99min连接存在请求，100min后连接正常
TEST_F(ConnEnhance, Other_096_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "设置连接不超时，超过默认10min超时时间，连接正常.");
    int ret = 1;
    char configName[48] = "enableLogFold";
    int32_t configVal = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, 2);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待连接99min
    sleep(60);

    // 使用该连接，预期连接已经被回收
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();

    configVal = 1;
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.同步连接
// 设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功
TEST_F(ConnEnhance, Other_096_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP,
        "同步连接 "
        "设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功.");
    int ret = 1;
    char configName[48] = "enableLogFold";
    int32_t configVal = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, 1);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待55s
    sleep(55);

    // 使用该连接，预期连接正常
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待连接超时
    sleep(63);
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    // 再次建连正常
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030.异步连接
// 设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功
TEST_F(ConnEnhance, Other_096_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP,
        "异步连接 "
        "设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功.");
    int ret = 1;
    int32_t configVal = 100;
    char configName[48] = "enableLogFold";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, 1);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待55s
    sleep(55);
    AsyncUserDataT data{0};
    ret = GmcUseNamespaceAsync(stmt, "public", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 等待连接超时
    sleep(63);
    ret = GmcUseNamespaceAsync(stmt, "public", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, "public", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    AW_FUN_Log(LOG_STEP,"[INFO] info->labelCount is %d\r\n", info->labelCount);
}
void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
void insertTable(GmcStmtT *stmt, const char *label_name,int32_t startValue = 0,int32_t endValue = 100)
{
    int32_t ret = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int expectAffectRows = 0;
    // insert Vertex
    for (int i = startValue; i < endValue; i++) {
        set_VertexProperty_PK(stmt, i);
        set_VertexProperty_SK(stmt, i);
        set_VertexProperty(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
// 031.订阅连接
//设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功（订阅关系被回收）
TEST_F(ConnEnhance, Other_096_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_FUN_Log(LOG_STEP,
        "订阅连接 "
        "设置连接1min超时，1min内连接请求正常处理，等待1min后，连接自动断开，请求处理失败，再次建连请求处理成功.");
    int ret = 1;
    int32_t configVal = 100;
    char configName[48] = "enableLogFold";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *connSnSync = NULL;
    GmcStmtT *stmtSnSync = NULL;
    char labelName[128] = "T39_all_type";
    int32_t userPolicyMode = 0;
    testChangeConfig(userPolicyMode, 10, 1);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 256;
    connSnSync = NULL;
    stmtSnSync = NULL;
    SnUserDataT *userData = NULL;
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));

    char *schema= NULL;
    char labelConfig[] = "{\"max_record_count\":300000}";
    readJanssonFile("schema/VertexSub_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabel(stmt, schema, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 创建订阅连接
    const char *subConnName = "subConnName";
    ret = testSubConnect(&connSnSync, &stmtSnSync, 1, g_epoll_reg_info, subConnName, &chanRingLen, NULL, NULL, -1, "TcpConnUser");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int userDataIdx = 0;
    int i;
    char *subInfo = NULL;
    readJanssonFile("schema/Vertex_SubInfo_001.gmjson", &subInfo);
    EXPECT_NE((void *)NULL, subInfo);

    GmcSubConfigT tmpSubInfo;
    const char *subName = "subVertexLabel";
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(stmt, &tmpSubInfo, connSnSync, sn_callback, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待55s
    sleep(40);
    AW_FUN_Log(LOG_STEP, "写数据触发推送.");
    // 写数据触发推送
    insertTable(stmt, labelName,0,100);
    AW_FUN_Log(LOG_STEP, "数据写入成功.");
    sleep(30);
    // 保持同步连接不超时
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
     ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "保持同步连接不超时.");
    ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 等待订阅连接超时
    sleep(36);
    AW_FUN_Log(LOG_STEP, "等待订阅连接超时.");

    // 查询表相关订阅信息，此时已经被回收
    AW_FUN_Log(LOG_STEP, "查询表相关订阅信息，此时已经被回收.");
    char cmd[256]="g_connServer";
    (void)sprintf(cmd,"gmsysview -q V\\$DRT_CONN_SUBS_STAT -s %s|grep subConnName",g_connServer);
    system(cmd);
    ret = executeCommand(cmd, "subConnName");
    AW_MACRO_ASSERT_NE_INT(GMERR_OK, ret);
    (void)sprintf(cmd,"gmsysview -q V\\$CATA_LABEL_SUBS_INFO -s %s|grep subVertexLabel",g_connServer);
    system(cmd);
    ret = executeCommand(cmd, "subVertexLabel");
    AW_MACRO_ASSERT_NE_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(connSnSync, stmtSnSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    free(userData);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
     ret = testSubConnect(&connSnSync, &stmtSnSync, 1, g_epoll_reg_info, subConnName, &chanRingLen, NULL, NULL, -1, "TcpConnUser");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(stmt, &tmpSubInfo, connSnSync, sn_callback, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 写数据触发推送
    insertTable(stmt, labelName,100,200);
    (void)sprintf(cmd,"gmsysview -q V\\$DRT_CONN_SUBS_STAT -s %s|grep subConnName",g_connServer);
    system(cmd);
    ret = executeCommand(cmd, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(cmd,"gmsysview -q V\\$CATA_LABEL_SUBS_INFO -s %s|grep subVertexLabel",g_connServer);
    system(cmd);
    ret = executeCommand(cmd, "subVertexLabel");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(connSnSync, stmtSnSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // drop
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    free(userData);

    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032.userPolicyMode为2 ，开启防暴力破解配置项 建连失败5次后，再次建连直到超过ip拉黑时间（5min）后能够建连成功
TEST_F(ConnEnhance, Other_096_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP,
        "userPolicyMode为2 ，开启防暴力破解配置项 建连失败5次后，再次建连直到超过ip拉黑时间（5min）后能够建连成功.");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 2;
    // 防暴力破解预期生效时，拉黑ip 5min
    int32_t tcpRetryTimeout = 5;
    testChangeConfig(userPolicyMode, tcpRetryTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不加载白名单直接建连5次失败
    AW_FUN_Log(LOG_STEP, "不加载白名单直接建连5次失败.");
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        conn = NULL;
        stmt = NULL;
    }
    // ip被拉黑建连失败
    AW_FUN_Log(LOG_STEP, "ip被拉黑建连失败.");
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 等待后台拉黑结束
    sleep(130);
    // 导入白名单后再次建连成功
    AW_FUN_Log(LOG_STEP, "拉黑结束导入权限.");
    inportAuthFile(userPolicyMode);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
void *testTheadConn(void *arg)
{
    AW_FUN_Log(LOG_STEP, "testTheadConn begin.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char configName[48] = "tcpRetryTimeout";
    int32_t configVal = 5;
    int32_t ret = 1;
    int32_t cycle = 5;
    for (int32_t i = 0; i < cycle; i++) {
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 等待连接超时
        sleep(66);
        ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &configVal, sizeof(int32_t));
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "testTheadConn %d.",i);

        // 断开连接再次建连
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        conn = NULL;
        stmt = NULL;

        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, "TcpConnUser");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        conn = NULL;
        stmt = NULL;
    }
    AW_FUN_Log(LOG_STEP, "testTheadConn end.");
}
// 033.多线程并发10个线程，设置连接超时时间为1min，每个线程100次循环建连等待70s，连接超时回收，再次建连
TEST_F(ConnEnhance, Other_096_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(
        LOG_STEP,
        "多线程并发10个线程，设置连接超时时间为1min，每个线程100次循环建连等待70s，连接超时回收，再次建连");
    int ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t userPolicyMode = 0;
    // 超时时间为1min
    int32_t tcpConnTimeout = 1;
    testChangeConfig(userPolicyMode, 10, tcpConnTimeout);
    testStartService();
    AW_CHECK_LOG_BEGIN();

    // 注册信号函数
    int32_t expectRetParam = 0;
    testRegAdaptFuncsProcTCP096(expectRetParam);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程
    int32_t thread_num = 10;
    pthread_t client_thr[thread_num];
    for (int32_t i = 0; i < thread_num; i++) {
        ret = pthread_create(&client_thr[i], NULL, testTheadConn, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 撤销鉴权文件
    revokeAuthFile(userPolicyMode);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}
