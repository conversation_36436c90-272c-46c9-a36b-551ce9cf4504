#include "tools.h"

class longitudinal_isolation_scene_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void longitudinal_isolation_scene_test::SetUpTestCase(){}
void longitudinal_isolation_scene_test::TearDownTestCase(){}
void longitudinal_isolation_scene_test::SetUp(){}
void longitudinal_isolation_scene_test::TearDown(){}

// 008.建表占满共享内存的系统区，建满后可建订阅连接
TEST_F(longitudinal_isolation_scene_test, Other_061_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=12\"");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string labelJsonStr = WriteBigVertexLabel(1000);
    const char *labelJson = labelJsonStr.c_str();

    int res = 0;
    int times = 0;
    system("gmsysview -q V\\$COM_SHMEM_GROUP");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"");
    while (res == GMERR_OK) {
        string labelNameStr = "label" + to_string(times);
        const char *labelName = labelNameStr.c_str();
        res = GmcCreateVertexLabelWithName(g_stmt, labelJson, g_vertexLabelConfig, labelName);
        if (res == GMERR_OK) {
            times++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, res);
        }
    }
    system("gmsysview -q V\\$COM_SHMEM_GROUP");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"");

    int fullM = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\"|grep GROUP_TOTAL_PHYSIZE", "GROUP_TOTAL_PHYSIZE: [");
    int fullK = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\"|grep GROUP_TOTAL_PHYSIZE", "] MB [");
    int fullB = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\"|grep GROUP_TOTAL_PHYSIZE", "] KB [");
    int full = fullM * 1024 * 1024 + fullK * 1024 + fullB;

    int sysMMax = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\"|grep GROUP_MAX_SIZE", "GROUP_MAX_SIZE: [");
    int sysKMax = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\"|grep GROUP_MAX_SIZE", "] MB [");
    int sysBMax = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\"|grep GROUP_MAX_SIZE", "] KB [");
    int sysMax = sysMMax * 1024 * 1024 + sysKMax * 1024 + sysBMax;

    int stepM = GetValueBetween("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"|grep \"step size\"",
                                ":[", "step size", "max size");
    int stepK = GetValueBetween("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"|grep \"step size\"",
                                "] MB [", "step size", "max size");
    int stepB = GetValueBetween("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"|grep \"step size\"",
                                "] KB [", "step size", "max size");
    int step = stepM * 1024 * 1024 + stepK * 1024 + stepB;

    int sysRest = sysMax - full;
    EXPECT_LT(sysRest, step);

    // 建订阅连接
    GmcConnT *connSub[2000] = {NULL};
    GmcStmtT *stmtSub[2000] = {NULL};
    const char *subConnName = "sub_conn_1";
    const char *subName = "sub1";
    int chanRingLen = 256;
    int subConnTimes = 0;
    res = GMERR_OK;
    while (res == GMERR_OK) {
        string subConnNameStr = "subConn" + to_string(subConnTimes);
        const char *subConnName = subConnNameStr.c_str();
        res = testSubConnect(&connSub[subConnTimes], &stmtSub[subConnTimes], 1, g_epoll_reg_info, subConnName, &chanRingLen);
        if (res == GMERR_OK) {
            subConnTimes++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, res);
        }
    }
    cout << endl << "subConnTimes:" << subConnTimes << endl;
    EXPECT_GT(subConnTimes, 0);

    for (int i = 0; i < subConnTimes; i++) {
        ret = testSubDisConnect(connSub[i], stmtSub[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);   
    }
    // 结束

    for (int i = 0; i < times; i++) {
        string labelNameStr = "label" + to_string(i);
        const char *labelName = labelNameStr.c_str();
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 009.开启纵向隔离，循环建表，直到报错，记录表数量，关闭纵向隔离，重启服务，循环建表，直到报错
TEST_F(longitudinal_isolation_scene_test, Other_061_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 开启纵向隔离
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=12\"");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string labelJsonStr = WriteBigVertexLabel(1000);
    const char *labelJson = labelJsonStr.c_str();

    int res = 0;
    int timesOpen = 0;
    system("gmsysview -q V\\$COM_SHMEM_GROUP");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"");
    while (res == GMERR_OK) {
        string labelNameStr = "label" + to_string(timesOpen);
        const char *labelName = labelNameStr.c_str();
        res = GmcCreateVertexLabelWithName(g_stmt, labelJson, g_vertexLabelConfig, labelName);
        if (res == GMERR_OK) {
            timesOpen++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, res);
        }
    }

    for (int i = 0; i < timesOpen; i++) {
        string labelNameStr = "label" + to_string(i);
        const char *labelName = labelNameStr.c_str();
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 关闭纵向隔离
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=12\"");
    system("start.sh -f");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    res = 0;
    int timesClose = 0;
    system("gmsysview -q V\\$COM_SHMEM_GROUP");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"");
    while (res == GMERR_OK) {
        string labelNameStr = "label" + to_string(timesClose);
        const char *labelName = labelNameStr.c_str();
        res = GmcCreateVertexLabelWithName(g_stmt, labelJson, g_vertexLabelConfig, labelName);
        if (res == GMERR_OK) {
            timesClose++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, res);
        }
    }

    for (int i = 0; i < timesClose; i++) {
        string labelNameStr = "label" + to_string(i);
        const char *labelName = labelNameStr.c_str();
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    EXPECT_GT(timesClose, timesOpen);
    cout << endl << "timesClose:" << timesClose << endl << "timesOpen:" << timesOpen << endl;

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
