
/*****************************************************************************
 Description  : yang自增字段和索引
 Notes        : 
            Other_074_001_001 container包含1个自增字段，写入数据（不含自增之列），subtree查询自增列数据,刷盘重启后，查询yang模型,再写入数据（不含自增之列），subtree查询自增列数据。
            Other_074_001_002 list包含1个自增字段，刷盘重启后，查询表模型，刷盘重启后 校验yang模型 添加写入数据（不含自增之列），subtree查询自增列数据。
            Other_074_001_003 leaflist包含1个自增字段，刷盘重启后，查询表模型，写入数据，subtree查询（id  PID（固定值） 第三个字段添加自增
            Other_074_001_004 case包含:自增字段，刷盘重启后，查询表模型，写入数据，subtree查询
            Other_074_001_005 list包含1个自增字段  修改自增列 初始值500 ，刷盘重启后，查询表模型，刷盘重启后 校验yang模型 添加写入数据（不含自增之列），subtree查询自增列数据。，从100开始
            Other_074_001_006 container节点包含，local唯一索引
            Other_074_001_007 container节点包含 loacl非唯一索引
            Other_074_001_008 container节点包含 localhash唯一索引
            Other_074_001_009 container节点包含 localhash非唯一索引
            Other_074_001_010 list节点包含 local唯一索引
            Other_074_001_011 list节点包含 local非唯一索引
            Other_074_001_012 list节点包含 localhash唯一索引
            Other_074_001_013 list节点包含 localhash非唯一索引
            Other_074_001_014 leaflist节点包含 local唯一索引
            Other_074_001_015 leaflist节点包含 local非唯一索引
            Other_074_001_016 leaflist节点包含 localhash唯一索引
            Other_074_001_017 leaflist节点包含 localhash非唯一
            Other_074_001_018 yang表第一个自增列添加local唯一索引
            Other_074_001_019 yang表第一个自增列添加local非唯一索引
            Other_074_001_020 list节点node1 使用local索引进行操作 暂不支持 GmcBatchAddDML会返回9007
            Other_074_001_021 yang表第一个自增列添加localhash唯一索引
            Other_074_001_022 yang表第一个自增列添加localhash非唯一索引
            Other_074_001_023 yang表第一个自增列添加list_localhash索引
            Other_074_001_024 主键多个自增字段，第一个必须自增字段，其他的不限制
            Other_074_001_025 主键多个自增字段，第一个非自增字段，第二个自增字段 建表失败
            Other_074_001_026 leaflist（PID字段为添加local索引）ID字段添加local索引
            Other_074_001_027 case下添加自增列 预期建表失败
            Other_074_001_028 yang表 自增列 第二个字段 （非第一个字段）预期失败
            Other_074_001_029 非yang持久化 两个自增列建表失败
            Other_074_001_030 创建yang模型 包含（‘F0...F10）11自增字段，创建成功、查询表模型，创建yang模型 包含(F0...F11)12自增字段，创建失败
            Other_074_001_031 单表local索引上限[1,32]个 包含主键包含localhash local索引

备注：
自增列 类型必须是uint32 uint64
 History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 2024-03-23
*****************************************************************************/
#include "tools.h"


char g_dbFilePath[1024] = {0};
char g_newDbFilePath[1024] = {0};

class Incremental_check : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        ;
    }
    static void TearDownTestCase()
    {
        ;
    }
};
void Incremental_check::SetUp()
{
    int ret;
#if defined(ENV_SUSE) || defined(FEATURE_PERSISTENCE)
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
    printf("get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    (void)sprintf(g_newDbFilePath, "%s/new_gmdb", pwdDir);
    char cmd_1[1024];
    sprintf(cmd_1, "mkdir %s", g_dbFilePath);
    system(cmd_1);
    system("sh $TEST_HOME/tools/stop.sh -f");
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
#endif
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 异步建连
    AsyncUserDataT data = {0};
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcAllocStmt(g_connAsync, &g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtLeafList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 生成后天目录
    system("rm -rf dynFolder;mkdir dynFolder");

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_NO_DATA);
}
void Incremental_check::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};

    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
#if defined(ENV_SUSE) || defined(FEATURE_PERSISTENCE)
    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
#endif
    testEnvClean();
}



void TestReStartServer()
{
#if defined(ENV_SUSE) || defined(FEATURE_PERSISTENCE)
    int ret;
    AsyncUserDataT data = {0};
    // 同步连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化刷盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmtRoot);
    GmcFreeStmt(g_stmtList1);
    GmcFreeStmt(g_stmtList2);
    GmcFreeStmt(g_stmtLeafList1);
    GmcFreeStmt(g_stmtLeafList2);
    
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_connAsync = NULL;
    g_stmtAsync = NULL;
    g_stmtRoot = NULL;

    g_rootNode = NULL;
    g_stmtList1 = NULL;
    g_stmtList2 = NULL;
    g_listNode1= NULL;
    g_listNode2= NULL;
    g_stmtLeafList1 = NULL;
    g_stmtLeafList2 = NULL;


    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcAllocStmt(g_connAsync, &g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtLeafList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
}

// 001. container包含1个自增字段，写入数据（不含自增之列），subtree查询自增列数据,刷盘重启后，查询yang模型,再写入数据（不含自增之列），subtree查询自增列数据。
TEST_F(Incremental_check, Other_074_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/1_reply.json");
    
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/1_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
}

// 002. list包含1个自增字段，写入数据（不含自增之列），subtree查询自增列数据,刷盘重启后，查询yang模型,再写入数据（不含自增之列），subtree查询自增列数据。
TEST_F(Incremental_check, Other_074_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");
    
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
// 003. leaflist包含1个自增字段，写入数据（不含自增之列），subtree查询自增列数据,刷盘重启后，查询yang模型,再写入数据（不含自增之列），subtree查询自增列数据。
TEST_F(Incremental_check, Other_074_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}



// 004.case节点包含1个自增字段，写入数据（不含自增之列），subtree查询自增列数据,刷盘重启后，查询yang模型,再写入数据（不含自增之列），subtree查询自增列数据。
// Property case_1_1_F1 can only be defined on the root node because of the field type  无效表定义
TEST_F(Incremental_check, Other_074_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
}

const char *g_labelConfig005 = R"(
{
    "auto_increment":[100,100,100,100,100,100,100,100,100,100,100],
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "yang_model":1
})";

// 005.自增字段初始值非0
TEST_F(Incremental_check, Other_074_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_5.gmjson", g_labelConfig005);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/5_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/5_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

static const char *g_yangcfgJson006 = R"({"auto_increment": [100,100,100], "isFastReadUncommitted": 0, "yang_model": 1})";
// 006.container local唯一
TEST_F(Incremental_check, Other_074_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_6.gmjson", g_yangcfgJson006);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valuerootF2 = 105;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_INT32, &valuerootF2, sizeof(int32_t),"root_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
    }
    


    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/6_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/6_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 007.container loacl非唯一
TEST_F(Incremental_check, Other_074_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_7.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valuerootF2 = 105;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_INT32, &valuerootF2, sizeof(int32_t),"root_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }


    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/7_reply.json");
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/7_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.container localhash唯一
TEST_F(Incremental_check, Other_074_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_8.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valuerootF2 = 105;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_INT32, &valuerootF2, sizeof(int32_t),"root_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }


    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply.json");
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.container localhash非唯一
TEST_F(Incremental_check, Other_074_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_9.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/9_reply.json");
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/9_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// list local唯一
TEST_F(Incremental_check, Other_074_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/10_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/10_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 011.list local非唯一
TEST_F(Incremental_check, Other_074_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_11.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/11_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/11_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 012.list localhash唯一
TEST_F(Incremental_check, Other_074_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_12.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/12_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/12_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 013.list localhash非唯一
TEST_F(Incremental_check, Other_074_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_13.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/13_reply.json");
 
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/13_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.leaflist local唯一
TEST_F(Incremental_check, Other_074_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_14.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/14_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/14_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.leaflist local非唯一
TEST_F(Incremental_check, Other_074_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_15.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/15_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/15_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 016.leaflist localhash唯一
TEST_F(Incremental_check, Other_074_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_16.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/16_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/16_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 017.leaflist localhash非唯一
TEST_F(Incremental_check, Other_074_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_17.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 018.yang表第一个自增列添加local唯一索引
TEST_F(Incremental_check, Other_074_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_18.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/18_reply.json");
 
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/18_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 019.yang表第一个自增列添加local非唯一索引
TEST_F(Incremental_check, Other_074_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_19.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/19_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/19_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

 static const char *g_yangcfgJson020 = R"({"auto_increment": [100,100,100], "isFastReadUncommitted": 0, "yang_model": 1})";
// 020.list节点node1 使用local索引进行操作 暂不支持 GmcBatchAddDML会返回9007
TEST_F(Incremental_check, Other_074_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_20.gmjson", g_yangcfgJson020);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务1
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch1参数
    GmcBatchT *batch1 = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch1, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch1, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 95;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch1, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch1, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch1, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data1 = {0};
    ret = GmcBatchExecuteAsync(batch1, batch_execute_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    AW_MACRO_EXPECT_EQ_INT(7, data1.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data1.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/20_trans_1_reply.json");
    
    GmcBatchDestroy(batch1);
    memset(&data1, 0, sizeof(AsyncUserDataT));

    // 提交事务1
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务2
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch2参数
    GmcBatchT *batch2 = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch2, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

     // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    
    ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, g_stmtRoot, g_stmtList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置属性值
    ret = GmcSetIndexKeyName(g_stmtList1, "local2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t keyValue = 95;
    
    ret = GmcSetIndexKeyValue(g_stmtList1, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch2, g_stmtList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);// 迭代三暂不支持yang结构的local索引DML

    // 批处理提交
    AsyncUserDataT data2 = {0};
    ret = GmcBatchExecuteAsync(batch2, batch_execute_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    AW_MACRO_EXPECT_EQ_INT(2, data2.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data2.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/20_reply.json");
    
    GmcBatchDestroy(batch2);
    memset(&data2, 0, sizeof(AsyncUserDataT));

    // 提交事务2
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/20_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 021.yang表第一个自增列添加localhash唯一索引
TEST_F(Incremental_check, Other_074_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_21.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/21_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/21_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 022.yang表第一个自增列添加localhash非唯一索引
TEST_F(Incremental_check, Other_074_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_22.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/22_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/22_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 023.yang表第一个自增列添加list_localhash索引
TEST_F(Incremental_check, Other_074_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_23.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/23_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/23_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 024.主键多个自增字段，第一个必须自增字段，其他的不限制
TEST_F(Incremental_check, Other_074_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_24_extra.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_24.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/24_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/24_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 025.主键多个自增字段，第一个非自增字段，第二个自增字段 建表失败
TEST_F(Incremental_check, Other_074_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_25.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

}

// 41. leaflist（PID字段为添加local索引）ID字段添加local索引
TEST_F(Incremental_check, Other_074_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_26.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t valueList1F3 = 105;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_INT32, &valueList1F3, sizeof(int32_t),
                            "list_1_F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/26_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/26_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    
}

// case下添加自增列 预期建表失败
TEST_F(Incremental_check, Other_074_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_27.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
}

// yang表 自增列 第二个字段 （非第一个字段）预期失败
TEST_F(Incremental_check, Other_074_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_28.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
}

const char *g_config029 = "{\"max_record_num\" : 5000000, \"isFastReadUncommitted\":0, \"defragmentation\":false }";
// 非yang持久化 单个自增列持久化
TEST_F(Incremental_check, Other_074_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    char *g_complexSchema = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *lableName = (char *)"vl_special_complex";
    // 建表
    (void)GmcDropVertexLabel(g_stmt, lableName);
    readJanssonFile("schema/vl_two_autoinc.gmjson", &g_complexSchema);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config029);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema);
}

// 创建yang模型 包含（‘F0...F10）11自增字段，创建成功、查询表模型，创建yang模型 包含(F0...F11)12自增字段，创建失败
TEST_F(Incremental_check, Other_074_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_30_extra.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_30.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置leaf-list_1节点和其属性值
    ret = testGmcPrepareStmtByLabelName(g_stmtLeafList1, "leaf-list_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtLeafList1, &g_leafListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t valueLeafList1F1 = 105;
    ret = SetNodeProperty(g_leafListNode1, GMC_DATATYPE_UINT32, &valueLeafList1F1, sizeof(uint32_t),
                        "leaf-list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/30_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/30_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}

// 单表local索引上限[1,32]个 包含主键包含localhash local索引
TEST_F(Incremental_check, Other_074_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_31_extra.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_31.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置leaf-list_1节点和其属性值
    ret = testGmcPrepareStmtByLabelName(g_stmtLeafList1, "leaf-list_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtLeafList1, &g_leafListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t valueLeafList1F1 = 105;
    ret = SetNodeProperty(g_leafListNode1, GMC_DATATYPE_UINT32, &valueLeafList1F1, sizeof(uint32_t),
                        "leaf-list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/31_reply.json");
 
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/31_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
