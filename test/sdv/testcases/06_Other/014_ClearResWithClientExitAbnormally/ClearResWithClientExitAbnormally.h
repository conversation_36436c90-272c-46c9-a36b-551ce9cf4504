/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 客户端异常退出资源清理测试头文件
 * Author: guopanpan
 * Create: 2021-04-26
 * History:
 */
#ifndef _CLEAR_RES_WITH_CLIENT_EXIT_ABNORMALLY_H
#define _CLEAR_RES_WITH_CLIENT_EXIT_ABNORMALLY_H 1

#include <errno.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <stdarg.h>
#include <signal.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_NAME_LENGTH 128
#define MAX_LOOP_NUM 3
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

#if ENABLE_INFO
#define TEST_INFO(log, args...)                                                                 \
    do {                                                                                        \
        fprintf(stdout, "Info: pid-%d %s:%d: " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)
#else
#define TEST_INFO(log, args...)
#endif

#define TEST_ERROR(log, args...)                                                                \
    do {                                                                                        \
        fprintf(stdout, "Error: pid-%d %s:%d " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)

#define CHECK_AND_BREAK(ret, log, args...)                                                           \
    if ((ret) != GMERR_OK) {                                                                         \
        fprintf(stdout, "Error: %s:%d " log ", " #ret " = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                       \
    }

#define CHECK_AND_RETURN(ret, log, args...)                                                              \
    do {                                                                                                 \
        if ((ret) != GMERR_OK) {                                                                         \
            fprintf(stdout, "Error: %s:%d " log ", " #ret " = %d\n", __FILE__, __LINE__, ##args, (ret)); \
            return ret;                                                                                  \
        }                                                                                                \
    } while (0)

// 当status状态为失败(非0)时, 将ret设置为status
#define SET_RET_IFERR(ret, status, log, args...)                                                               \
    do {                                                                                                       \
        if ((status) != GMERR_OK) {                                                                            \
            fprintf(stdout, "Error: %s:%d " log ", " #status " = %d\n", __FILE__, __LINE__, ##args, (status)); \
            ret = (status);                                                                                    \
        }                                                                                                      \
    } while (0)

#define LOG_LAST_ERROR_IFERR(ret, conn)                                    \
    do {                                                                   \
        if ((ret) != GMERR_OK) {                                           \
            const char *_lastError_ = "";                                  \
            _lastError_ = GmcGetLastError();                           \
            TEST_ERROR("ret = %d, last error = \"%s\"", ret, _lastError_); \
        }                                                                  \
    } while (0)

// 静态全局变量
static const char *gLabelName = "VertexLabel";
static const char *gLabelConfig = R"({"max_record_num":100000})";
static const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int64", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const char *gSecondLabelName = "SecondVertexLabel";
static const char *gSecondLabelConfig = R"({"max_record_num":1000})";
static const char *gSecondLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"SecondVertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

const int32_t gSubChanRingLen = 256;
const char *gSubName = "SubVertexLabel";
const char *gSubInfo =
    R"({
        "name":"SubVertexLabel",
        "label_name":"VertexLabel",
        "type":"before_commit",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"update", "msgTypes": ["new object", "old object"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":false,
        "persist":false,
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {
                            "property": "F2"
                        }
                    ]
            }
    })";

const char *gDeltaStoreName = "DeltaStore";
const char *gDeltaStoreJson =
    R"({"delta_stores":
        [{
            "name": "DeltaStore",
            "init_mem_size": 32768,
            "max_mem_size": 131072,
            "extend_mem_size": 8192,
            "page_size": 4096,
            "hash_capacity": 10000
    }]})";

// sysconfig.ini -> 0:EULER, 1:docker+DAP(lite), 2:docker(yang)
typedef enum tagRunMode { GT_RUN_MODE_EULER = 0, GT_RUN_MODE_DAP = 1, GT_RUN_MODE_YANG = 2 } GtRunModeE;

/******************************************************* 子进程 *******************************************************/
// 子进程回调
typedef int (*GtForkCallbackT)(void *arg);

// 创建子进程
pid_t GtFork(GtForkCallbackT callback, void *arg)
{
    int ret;
    pid_t pid = fork();
    if (pid == 0) {
        TEST_INFO("pid-%d was successfully created by ppid-%d.", getpid(), getppid());
        ret = callback(arg);
        if (ret != GMERR_OK) {
            TEST_ERROR("pid-%d execute callback failed, ret = %d.", getpid(), ret);
            exit(FAILED);
        }
        exit(GMERR_OK);
    } else if (pid < 0) {
        TEST_ERROR("create child process failed, pid = %d, %s.", pid, strerror(errno));
    }
    return pid;
}

/**************************************************** 进程间信号量 ****************************************************/
typedef union tgsem_u {
    int val;               /* value for SETVAL */
    struct semid_ds *buf;  /* buffer for IPC_STAT, IPC_SET */
    unsigned short *arry;  /* array for GETALL, SETALL */
    struct seminfo *__buf; /* buffer for IPC_INFO */
} sem_u;

/**
 * Function: 创建或获取信号量集
 * [Out] semId     ：返回信号量集的标识符
 * [In ] key       ：指定0(IPC_PRIVATE)建立新信号量集对象或指定大于0的32位整数，视参数semflg来确定操作
 * [In ] semNum    ：创建信号量集中信号量的个数，该参数只在创建信号量集时有效
 * [In ] key       : 创建信号量所使用的key, 默认通过ftok生成(全局唯一)
 * [In ] initVal   : 信号量集内信号量的初始值, 默认是0
 * Return status code
 * Notice:
 *  1. 使用结束必须调用GtSemRemove()删除信号量集
 *  2. ipcs -s :查看信号量集, ipcrm -S semkey :移除用semkey创建的信号量集, ipcrm -s semid :移除用semid标识的信号量集
 */
int GtSemget(int *semId, int semNum, key_t key = -1, int initVal = 0)
{
    errno = 0;
    key_t semKey = key;
    if (semKey == -1) {
        semKey = ftok("/tmp", 0x66);
    }
    if (semKey < 0) {
        TEST_ERROR("get key failed, %s", strerror(errno));
        return FAILED;
    }

    // 先从系统中获取指定key对应的信号量集, 如果不存在 (errno == ENOENT) 则新建1个信号量集并进行初始化
    int tmpSemId = semget(key, 0, 0666);
    if (tmpSemId == -1) {
        TEST_INFO("no sems exist when key = %d, create it", semKey);
        tmpSemId = semget(key, semNum, 0666 | IPC_CREAT);
        if (tmpSemId == -1) {
            TEST_ERROR("create sems failed, tmpSemId = %d, errno = %d, %s", tmpSemId, errno, strerror(errno));
            return errno;
        }

        sem_u semun;
        semun.val = initVal;
        for (int i = 0; i < semNum; i++) {
            int ret = semctl(tmpSemId, i, SETVAL, semun);
            if (ret == -1) {
                TEST_ERROR("init sems failed, errno = %d, %s", errno, strerror(errno));
                return FAILED;
            }
        }
    }

    *semId = tmpSemId;
    return GMERR_OK;
}

/**
 * Function: 初始化信号量
 * [In ] semId     ：信号量集标识符
 * [In ] semIdx    ：信号量集中的信号量下标，0代表第1个信号量
 * [In ] semCnt    : 初始化[semIdx, semidx + semCnt)范围内的信号量
 * [In ] semVal    : 信号量的初始化值
 * Return: status code
 */
int GtSemInit(int semId, int semIdx, int semCnt = 1, int semVal = 0)
{
    errno = 0;
    int ret;
    sem_u semun;
    semun.val = semVal;
    for (int i = semIdx; i < semIdx + semCnt; i++) {
        ret = semctl(semId, i, SETVAL, semun);
        if (ret == -1) {
            TEST_ERROR("init sem failed, errno = %d, %s", errno, strerror(errno));
            return FAILED;
        }
    }
    return GMERR_OK;
}

/**
 * Function: 获取信号量的值
 * [In ] semId     ：信号量集标识符
 * [In ] semIdx    ：信号量集中的信号量下标，0代表第1个信号量
 * [In ] cmd       : 信号量属性 GETNCNT->等待资源的进程数量, GETVAl->信号量的值
 * Return semVal
 */
int GtSemGetval(int semId, int semIdx, int cmd = GETNCNT)
{
    sem_u semun;
    int semVal = semctl(semId, semIdx, cmd, semun);
    TEST_INFO("semId = %d, semIdx = %d, semVal = %d", semId, semIdx, semVal);
    return semVal;
}

// 删除信号量
int GtSemRemove(int semId)
{
    int ret;
    errno = 0;
    sem_u semun;
    ret = semctl(semId, 0, IPC_RMID, semun);
    if (ret == -1) {
        TEST_ERROR("remove sem failed, errno = %d, %s", errno, strerror(errno));
        return FAILED;
    }
    return GMERR_OK;
}

/**
 * Function: 信号量P/V操作(等待/释放)
 * [In ] semId     ：信号量集标识符
 * [In ] semIdx    ：操作的信号量集中的信号量下标，0代表第1个信号量
 * [In ] op         ：信号量操作，发送信号量(op > 0)、等待信号量(op < 0)
 * Return status code
 */
int GtSemop(int semId, uint32_t semIdx, int32_t op)
{
    int ret;
    errno = 0;
    struct sembuf buff;
    buff.sem_num = (unsigned short)semIdx;
    buff.sem_op = (short)op;
    buff.sem_flg =
        SEM_UNDO; /* 设置信号量的默认操作，IPC_NOWAIT设置信号量操作不等待；SEM_UNDO设置进程崩溃恢复信号量值 */
    ret = semop(semId, &buff, 1);
    if (ret == -1) {
        TEST_ERROR("sem op failed, semId = %d, semIdx = %u, sem_op = %d, errno = %d, %s", semId, semIdx, op, errno,
            strerror(errno));
        return FAILED;
    }
    return GMERR_OK;
}

// 等待信号量(wait p)
// NOTICE 多个进程(a, b和c)等待同一个信号量时，其中一个进程(如
// a)退出后会将该进程运行过程中申请过的所有信号量资源归还，该操作
//        可能导致其他进程(b和c)退出阻塞状态. 该场景可考虑每两个进程间单独维护一个信号量.
int GtSemWait(int semId, int semIdx)
{
    return GtSemop(semId, semIdx, -1);
}

// 发送信号量(post v)
int GtSemPost(int semId, int semIdx)
{
    return GtSemop(semId, semIdx, +1);
}

/****************************************************** 通用工具 ******************************************************/
// 暂停进程, 仅用于临时调试用例
void GtPause(const char *notice = "")
{
    printf("Info: [%s] pause, enter anything to continue...\n", notice);
    getchar();
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSystemCmd(char **result, const char *format, ...)
{
    int ret;
    errno = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d, %s.", ret, strerror(errno));
        va_end(args);
        return FAILED;
    }
    va_end(args);

    TEST_INFO("cmd = \"%s\"", cmd);
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, %s.", strerror(errno));
        return FAILED;
    }

    // XXX 优化为动态获取流长度
    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(fd);
        TEST_ERROR("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }
    *result = tmpResult;
    return GMERR_OK;
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSysviewCmd(char **result, const char *viewName, const char *filter = "", ...)
{
    int ret;
    va_list args;
    va_start(args, filter);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), filter, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    char *buf = NULL;
    ret = GtExecSystemCmd(&buf, "%s/gmsysview -s %s -q '%s' %s", g_toolPath, g_connServer, viewName, cmd);
    if (ret != GMERR_OK) {
        TEST_ERROR("exec system cmd failed, ret = %d.", ret);
        free(buf);
        buf = NULL;
        return ret;
    }

    buf[strlen(buf) - 1] = '\0';
    TEST_INFO("sysview result = \"%s\"", buf);
    *result = buf;
    return GMERR_OK;
}

int GtExecSysviewCmd(const char *viewName, const char *filter = "", ...)
{
    int ret;
    va_list args;
    va_start(args, filter);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), filter, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    return GtExecSystemCmd(
        "%s/gmsysview -s %s -q '%s' %s", g_toolPath, g_connServer, viewName, cmd);
}

/******************************************************* DB工具 *******************************************************/
int GtCheckAffectRows(GmcStmtT *stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    CHECK_AND_RETURN(ret, "get stmt attr failed");
    if (affect != expect) {
        TEST_ERROR("checke effect rows failed, expect is %d, actual is %d", expect, affect);
        return FAILED;
    }
    return ret;
}

int GtGetAndCheckProperty(GmcStmtT *stmt, int32_t expectF0, int32_t expectF1, int64_t expectF2)
{
    int ret;
    bool isNull;

    uint32_t F0Size;
    int32_t F0Value;
    ret = GmcGetVertexPropertySizeByName(stmt, "F0", &F0Size);
    CHECK_AND_RETURN(ret, "get vertex property size");
    ret = GmcGetVertexPropertyByName(stmt, "F0", &F0Value, F0Size, &isNull);
    CHECK_AND_RETURN(ret, "get vertex property");
    if (F0Value != expectF0) {
        TEST_ERROR("check F0 failed when index = %d, expect \"%d\", actual \"%d\"", expectF0, expectF0, F0Value);
        return FAILED;
    }

    uint32_t F1Size;
    int32_t F1Value;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &F1Size);
    CHECK_AND_RETURN(ret, "get vertex property size");
    ret = GmcGetVertexPropertyByName(stmt, "F1", &F1Value, F1Size, &isNull);
    CHECK_AND_RETURN(ret, "get vertex property");
    if (F1Value != expectF1) {
        TEST_ERROR("check F1 failed when index = %d, expect \"%d\", actual \"%d\"", expectF0, expectF1, F1Value);
        return FAILED;
    }

    uint32_t F2Size;
    int64_t F2Value;
    ret = GmcGetVertexPropertySizeByName(stmt, "F2", &F2Size);
    CHECK_AND_RETURN(ret, "get vertex property size");
    ret = GmcGetVertexPropertyByName(stmt, "F2", &F2Value, F2Size, &isNull);
    CHECK_AND_RETURN(ret, "get vertex property");
    if (F2Value != expectF2) {
        TEST_ERROR("check F2 failed when index = %d, expect \"%ld\", actual \"%ld\"", expectF0, expectF2, F2Value);
        return FAILED;
    }
    return ret;
}

bool gIsSnCallbackWait = false;
SnUserDataT gSnUserData = {0};
void GtSnCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (gIsSnCallbackWait) {
        sleep(1);
    }

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        CHECK_AND_BREAK(ret, "fetch from stmt");
        if (eof) {
            break;
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                TEST_ERROR("invalid eventType = %d", info->eventType);
                abort();
            }
        }
    }
}

int GtSetInsertProperty(GmcStmtT *stmt, int32_t F0, int32_t F1, int64_t F2, SnUserDataT *userData = NULL)
{
    int ret;
    int32_t F0Value = F0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    CHECK_AND_RETURN(ret, "set vertex property");
    int32_t F1Value = F1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    CHECK_AND_RETURN(ret, "set vertex property");
    int64_t F2Value = F2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
    CHECK_AND_RETURN(ret, "set vertex property");
    return GMERR_OK;
}

int GtSetUpdateProperty(GmcStmtT *stmt, int32_t F1, int64_t F2, SnUserDataT *userData = NULL)
{
    int ret;
    int32_t F1Value = F1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    CHECK_AND_RETURN(ret, "set vertex property");
    int64_t F2Value = F2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
    CHECK_AND_RETURN(ret, "set vertex property");
    return GMERR_OK;
}

// 删除测试期间创建的表资源 (gLabelName)
int GtProcessToCleanRes(void *argu)
{
    int ret;
    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        testEnvClean();
        return ret;
    }

    int tmpRet = GmcDropVertexLabel(stmt, gLabelName);
    if (tmpRet != GMERR_OK && tmpRet != GMERR_UNDEFINED_TABLE) {
        TEST_ERROR("drop vertex label failed, ret = %d", tmpRet);
        ret = FAILED;
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    if (tmpRet != GMERR_OK) {
        TEST_ERROR("clean test env failed, ret = %d", tmpRet);
        ret = FAILED;
    }
    return ret;
}

/****************************************************** 资源工具 ******************************************************/
// 检查latch锁状态
int GtCheckLatchStatus(int32_t expectLatchCnt)
{
    int ret;
    char *result = NULL;
    ret = GtExecSysviewCmd(&result, "V$STORAGE_RES_SESSION_STAT", "| grep LATCH_COUNT | wc -l");
    if (ret != GMERR_OK) {
        TEST_ERROR("execute sysview command failed, ret = %d", ret);
        free(result);
        result = NULL;
        return FAILED;
    }

    int32_t latchCnt = atoi(result);
    free(result);
    result = NULL;

    if (latchCnt != expectLatchCnt) {
        TEST_ERROR("check latch count failed, expect = %d, actual = %d", expectLatchCnt, latchCnt);
        return FAILED;
    }

    return GMERR_OK;
}

int GtProcessCheckTableRefCnt(void *expectRefCnt)
{
    int64_t refCnt = (int64_t)expectRefCnt;

    int ret;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcTruncateVertexLabel(stmt, gLabelName);
        CHECK_AND_BREAK(ret, "truncate vertex label, label name = %s", gLabelName);
    } while (0);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();

    return ret;
}

// NOTICE 2021-07-29 truncate或droptable时会自动清理引用计数，因此目前表引用计数不具备可测性
// 另起客户端检查表引用计数
int GtCheckTableRefCnt(int32_t expectRefCnt)
{
    pid_t pid = GtFork(GtProcessCheckTableRefCnt, (void *)(int64_t)expectRefCnt);
    if (pid < 0) {
        TEST_ERROR("create child process failed");
        return FAILED;
    }

    int ret;
    pid = wait(&ret);
    ret = WEXITSTATUS(ret);
    if ((ret == GMERR_OK) && (expectRefCnt != 0)) {
        TEST_ERROR("check table ref count failed, ret = %d, expectRefcnt = %d", ret, expectRefCnt);
        ret = FAILED;
    } else if ((ret != GMERR_OK) && (expectRefCnt <= 0)) {
        TEST_ERROR("check table ref count failed, ret = %d, expectRefcnt = %d", ret, expectRefCnt);
        ret = FAILED;
    }

    return GMERR_OK;
}

// NOTICE 2021-07-29 truncate或droptable时会自动清理引用计数，因此目前表引用计数不具备可测性
// 在当前客户端检查表资源引用计数
int GtCheckTableRefCnt(GmcStmtT *stmt, const char *labelName, int32_t expectRefCnt)
{
    // 如果 truncate 表失败，判断存在表引用计数
    int ret;
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    /*if ((ret == GMERR_OK) && (expectRefCnt != 0)) {
        TEST_ERROR("check table ref count failed, ret = %d, expectRefcnt = %d", ret, expectRefCnt);
        ret = FAILED;
    } else if ((ret == OBJECT_IN_USE) && (expectRefCnt <= 0)) {
        TEST_ERROR("check table ref count failed, ret = %d, expectRefcnt = %d", ret, expectRefCnt);
        ret = FAILED;
    } else if (ret == OBJECT_IN_USE) {
        ret = GMERR_OK;
    }*/

    return GMERR_OK;
}

// 检查事务资源状态: 事务锁, 事务池
int GtCheckTransStatus(int32_t expectLockCnt, int32_t expectTransCnt)
{
    // 事务锁
    int ret;
    char *result = NULL;

    // 锁释放存在一定延时
    int32_t timeout = 6;
    for (int32_t i = 0; i < timeout; i++) {
        ret = GtExecSysviewCmd(&result, "V$STORAGE_LOCK_OVERVIEW", "| grep USED_LOCK_CNT | awk '{print $2}'");
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            return FAILED;
        }

        int32_t lockCnt = atoi(result);
        free(result);
        result = NULL;
        if (lockCnt != expectLockCnt) {
            TEST_ERROR("check lock count failed, expect lockCnt = %d, actual = %d", expectLockCnt, lockCnt);
            ret = FAILED;
            sleep(1);
            continue;
        } else {
            ret = GMERR_OK;
        }

        // 事务池 ACT_TRX_NUM TRX_USED_SLOT_NUM
        ret = GtExecSysviewCmd(&result, "V$STORAGE_TRX_STAT", "| grep TRX_USED_SLOT_NUM | awk '{print $2}'");
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            return FAILED;
        }

        // HISTORY 2021-06-28 空载状态占用 1 个事务槽，查询视图占用 2 个事务槽
        // 2022.02.08 现在空载不占用事务槽，变为-2，DTS2022012007931
        // 2022.04.09 DTS2022040604420，开发优化查TRX_USED_SLOT_NUM视图现在只占用1个事务槽,且当前版本创建DS时会起一个merge线程占用1个事务槽，只有杀服务才会被释放
        int32_t transCnt = atoi(result) - 1;
        free(result);
        result = NULL;

        if ((transCnt = expectTransCnt) || (transCnt = expectTransCnt + 1)) {
            ret = GMERR_OK;
            break;
        } else {
            TEST_ERROR("check trans count failed, expect = %d, actual = %d", expectTransCnt, transCnt);
            ret = FAILED;
            sleep(1);
            continue;
        }
    }

    return ret;
}

// 获取事务锁
int CheckTransLock()
{
    char *LockCnt = NULL;
    int ret = GtExecSysviewCmd(&LockCnt, "V$STORAGE_LOCK_OVERVIEW", "| grep USED_LOCK_CNT | awk '{print $2}'");
    EXPECT_EQ(GMERR_OK, ret);
    int LockCount = atoi(LockCnt);
    free(LockCnt);
    return LockCount;
}

// 获取事务槽
int CheckTransSlot()
{
    char *TransCnt = NULL;
    int ret = GtExecSysviewCmd(&TransCnt, "V$STORAGE_TRX_STAT", "| grep TRX_USED_SLOT_NUM | awk '{print $2}'");
    EXPECT_EQ(GMERR_OK, ret);
    int TransCount = atoi(TransCnt);
    free(TransCnt);
    return TransCount;
}

// 检查订阅资源状态
int GtCheckSnStatus(int32_t expectSubCnt)
{
    int ret;
    int32_t timeout = 5;
    for (int i = 0; i < timeout; i++) {
        char *result = NULL;
        ret = GtExecSysviewCmd(&result, "V$CATA_LABEL_SUBS_INFO", "| grep SUBS_NAME | wc -l");
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            return FAILED;
        }
        int32_t subCnt = atoi(result);
        free(result);
        result = NULL;

        ret = GtExecSysviewCmd(
            &result, "V$CATA_LABEL_SUBS_INFO", "| grep IS_DELETED | awk 'BEGIN{total=0}{total+=$2}END{print total}'");
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            return FAILED;
        }
        int32_t subDeleteCnt = atoi(result);
        free(result);
        result = NULL;

        if (subCnt - subDeleteCnt != expectSubCnt) {
            TEST_ERROR("check sub count failed, expect SubCnt = %d, actual subCnt = %d, subDeleteCnt = %d",
                expectSubCnt,
                subCnt,
                subDeleteCnt);
            ret = FAILED;
            sleep(1);
        } else {
            ret = GMERR_OK;
            break;
        }
    }
    if (ret == FAILED) {
        return ret;
    }

    return GMERR_OK;
}

int GtCheckConnMemCtxCount(int32_t expectConnMemCtxCnt)
{
    int ret;
    char *result = NULL;
    char *result2 = NULL;
    char *viewName = NULL;

    // HPE环境链路资源通过共享内存存储，欧拉环境通过堆内存存储
#if defined (RUN_DATACOM_DAP) || defined (RUN_SIMULATE)
        viewName = (char *)"V$COM_SHMEM_CTX";
#else
        viewName = (char *)"V$COM_DYN_CTX";
#endif

    // 连接较多时资源回收较慢，允许最长 6s 延时
#if defined ENV_RTOSV2X
    int32_t timeout = 6;
#elif defined CPU_BIT_32
    int32_t timeout = 6;
#elif defined (ENV_RTOSV2)
    int32_t timeout = 15; // AC回收资源比较慢，开发建议改为15s
#else
    int32_t timeout = 6;
#endif

    for (int i = 0; i < timeout; i++) {
#if defined (RUN_DATACOM_DAP) || defined (RUN_SIMULATE)
        ret = GtExecSysviewCmd(&result, viewName, "| grep \"CTX_NAME: Conn \" | wc -l");
        ret = GtExecSysviewCmd(&result2, viewName, "| grep \"CTX_NAME: SubConn \" | wc -l");  // HPE上存在订阅连接
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            free(result2);
            result = NULL;
            return FAILED;
        }
#else
        ret =
            GtExecSysviewCmd(&result, viewName, "-f CTX_NAME='DRT_CONN_MGR_MEMCTX' |grep CHILD_NUM |awk '{print $2}'");
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            return FAILED;
        }
#endif

// 通过视图工具查询连接内存上下文，工具本身占用1个上下文
#if defined (RUN_DATACOM_DAP) || defined (RUN_SIMULATE)
        int32_t memCtxCnt = atoi(result) + atoi(result2) - 1;
#else
        int32_t memCtxCnt = atoi(result) - 1;
#endif

#if defined (RUN_DATACOM_DAP) || defined (RUN_SIMULATE)
        free(result);
        result = NULL;
        free(result2);
        result2 = NULL;
#else
        free(result);
        result = NULL;
#endif

        if (memCtxCnt != expectConnMemCtxCnt) {
            TEST_ERROR("check conn count failed, timeout = %d, expect memCtxCnt = %d, actual memCtxCnt = %d", i,
                expectConnMemCtxCnt, memCtxCnt);
            ret = FAILED;
            sleep(1);
        } else {
            return GMERR_OK;
        }
    }

    return ret;
}

// 检查链路资源状态: 链路资源, worker资源, 连接通道
int GtCheckConnStatus(int32_t expectConnCnt)
{
    // 连接较多时资源回收较慢，允许最长 8s 延时
    int ret;
    int32_t timeout = 8;
    for (int i = 0; i < timeout; i++) {
        char *result = NULL;
        ret = GtExecSysviewCmd(&result, "V$DRT_CONN_STAT", "| grep CONN_STATUS | wc -l");
        if (ret != GMERR_OK) {
            TEST_ERROR("execute sysview command failed, ret = %d", ret);
            free(result);
            result = NULL;
            return FAILED;
        }

        // 通过视图工具查询连接信息时，工具本身占用1个连接
        int32_t connCnt = atoi(result) - 1;
        free(result);
        result = NULL;

        if (connCnt != expectConnCnt) {
            TEST_ERROR("check conn count failed, timeout = %d, expect connCnt = %d, actual connCnt = %d", i,
                expectConnCnt, connCnt);
            ret = FAILED;
            sleep(1);
        } else {
            ret = GMERR_OK;
            break;
        }
    }
    if (ret == FAILED) {
        return ret;
    }

    // 检查链路资源上下文数量
#if defined ENV_RTOSV2X
    return GtCheckConnMemCtxCount(expectConnCnt - 2); // IOT，SOHO2个预留连接不含memctx
#elif defined CPU_BIT_32 && defined RUN_INDEPENDENT
    return GtCheckConnMemCtxCount(expectConnCnt);
#elif (ENV_RTOSV2) && (!RUN_SIMULATE)
    return GtCheckConnMemCtxCount(expectConnCnt - 6); // AC6个预留连接不含memctx
#else
    return GtCheckConnMemCtxCount(expectConnCnt);
#endif
}

// 检查worker资源，通过ps -T -p 查询worker线程
int GtCheckWorkerStatus(int32_t expectWorkerCnt)
{
    // hpe环境采用协程代替线程，暂不支持自动化查询协程资源，由于每创建一个连接，服务端新起一个worker协程，因此替换为检查链路资源
    if (g_runMode == GT_RUN_MODE_DAP) {
        return GtCheckConnStatus(expectWorkerCnt);
    }

    int ret;
    const char *serverName = "gmserver";
    char *workerCntStr = NULL;
    ret = GtExecSystemCmd(&workerCntStr, "ps -T -p $(pidof %s) | grep DRT_WORKER_ | wc -l", serverName);
    if (ret != GMERR_OK) {
        TEST_ERROR("get server worker count failed, ret = %d", ret);
        free(workerCntStr);
        workerCntStr = NULL;
        return FAILED;
    }

    // 服务端始终维护一条监控线程 DRT_WORKER_MONI 备注：已变更为WORKER_MONITOR
    workerCntStr[strlen(workerCntStr)] = '\0';
    int32_t workerCnt = atoi(workerCntStr);
    free(workerCntStr);
    workerCntStr = NULL;

    if (workerCnt != expectWorkerCnt) {
        TEST_ERROR(
            "check worker count failed, expect workerCnt = %d, actual workerCnt = %d", expectWorkerCnt, workerCnt);
        GtExecSystemCmd("ps -T -p $(pidof %s)", serverName);
        return FAILED;
    }

    return GMERR_OK;
}

// 检查DeltaStore资源状态
int GtCheckDeltaStoreStatus(int32_t expectPagecnt)
{
    // ds回收存在一定的延时，校验时允许最多 6s 的超时时间
    int ret;
    int32_t timeout = 6;
    for (int i = 0; i < timeout; i++) {
        char *pageCntStr = NULL;
        ret = GtExecSysviewCmd(
            &pageCntStr, "V$DSTORE_MEMORY_STAT", "| grep PAGE_NUM | awk 'BEGIN{total=0}{total+=$2}END{print total}'");
        if (ret != GMERR_OK) {
            TEST_ERROR("get server worker count failed, ret = %d", ret);
            free(pageCntStr);
            pageCntStr = NULL;
            return FAILED;
        }

        int32_t pageCnt = atoi(pageCntStr);
        free(pageCntStr);
        pageCntStr = NULL;

        // ds空载状态 ACTIVE_PAGE_NUM 数量为 1, FREE_PAGE_NUM 数量为 7, 其它 PAGE_NUM 数量为 0
        // 由于page数量无法精确预估, 因此根据实际情况按如下条件判断:
        //      0: 未创建ds
        //      8: 空载ds
        //      9~无穷大: 负载ds
        if (expectPagecnt <= 8 && pageCnt != expectPagecnt) {
            TEST_ERROR("check page count failed, i = %d, expect = %d, actual = %d", i, expectPagecnt, pageCnt);
            ret = FAILED;
        } else {
            ret = GMERR_OK;
            break;
        }
        if (expectPagecnt > 8 && pageCnt <= 8) {
            TEST_ERROR("check page count failed, i = %d, expect = %d, actual = %d", i, expectPagecnt, pageCnt);
            ret = FAILED;
        } else {
            ret = GMERR_OK;
            break;
        }

        sleep(1);
    }

    return ret;
}

// 检查客户端退出状态
int GtCheckClientStatus(int32_t expectClientCnt)
{
    // 通过worker线程数量判断服务端是否检测到客户端异常退出
    return GtCheckWorkerStatus(expectClientCnt);
}

#endif /* _CLEAR_RES_WITH_CLIENT_EXIT_ABNORMALLY_H */
