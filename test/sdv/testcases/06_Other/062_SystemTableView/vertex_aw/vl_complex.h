/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: 复杂表的基本功能函数, (特殊复杂表和一般复杂表仅special_complex属性有区别, 其他结构相同)
 * Author: guopanpan
 * Create: 2022-10-14
 * History:
 * Note:
 *  1. schema根节点内存占用(KB): 36+18+bytsLen*2
 *  2. schema子节点内存占用(KB): 8+bytesLen*4
 */
#ifndef VL_COMPLEX_H
#define VL_COMPLEX_H 1

#include "../common/test_tools_gmdb.h"
#include "../common/test_tools_common.h"
#include "../common/test_log.h"

// 修改该配置项的同时必须同步修改schema json文件 ./schema/VertexLabelTypical.gmjson
#define VL_COMPLEX_FIELD_BITMAP_LEN 16  // 单位: bit  (建议size: 16, 8192)
#define VL_COMPLEX_FIELD_FIXED_LEN 16   // 单位: byte (建议size: 16, 4096)

// 修改该配置项的同时必须同步修改 GtResetTypicalFieldLenth()
uint32_t g_typicalFieldBytesLen = 16;   // 单位: byte (建议size: 16, 4096, 8019, 13312)
uint32_t g_typicalFieldStringLen = 16;  // 单位: byte (建议size: 16, 4096, 8019, 13312)

#define VL_GENERAL_COMPLEX_CONFIG_PATH GtGetLabelConfigJsonPath()
#define VL_GENERAL_COMPLEX_JSON_PATH VlComplexGetJsonPath()
#define VL_GENERAL_COMPLEX_NAME "vl_general_complex"

const char *const VL_SPECIAL_COMPLEX_JSON_PATH = "./schema/vl_special_complex.gmjson";
const char *const VL_SPECIAL_COMPLEX_NAME = "vl_special_complex";

// 获取点标签schema文件的绝对路径 (性能测试热点路径禁止使用该函数)
char *VlComplexGetJsonPath()
{
    // static修饰的变量不会重复初始化，第一次初始化后，后续使用直接返回即可
    static char simpleJsonPath[MAX_PATH_LEN] = {0};
    if (strlen(simpleJsonPath) != 0) {
        return simpleJsonPath;
    }

    static pthread_spinlock_t spinlock = 0;
    (void)pthread_spin_lock(&spinlock);
    const char *relativePath = "schema/vl_general_complex.gmjson";
    int len = snprintf(simpleJsonPath, sizeof(simpleJsonPath), "%s/%s", GtGetVertexAwPath(), relativePath);
    if (len <= 0) {
        TEST_ERROR("set json path failed, len = %d, errno = %d.\n", len, errno);
        memset(simpleJsonPath, 0, sizeof(simpleJsonPath));
        (void)pthread_spin_unlock(&spinlock);
        return NULL;
    }
    (void)pthread_spin_unlock(&spinlock);

    return simpleJsonPath;
}

#pragma pack(1)
typedef struct TagVlComplexSuperField {
    int64_t a1;
    uint32_t a2;
    uint64_t a3;
    float a4;
    double a5;
    uint8_t a6[VL_COMPLEX_FIELD_BITMAP_LEN / 8];
    uint8_t a7[VL_COMPLEX_FIELD_FIXED_LEN];
} VlComplexSuperFieldT;
#pragma pack()

typedef enum TagVlComplexChildOpType {
    VL_COMPLEX_CHILD_OP_APPEND = 0,
    VL_COMPLEX_CHILD_OP_MODIFY = 1,
    VL_COMPLEX_CHILD_OP_DELETE = 2,
    VL_COMPLEX_CHILD_OP_NONE = 3,
} VlComplexChildOpTypeE;

typedef struct TagVlComplexRecordCtx {
    int32_t opStart;        // 主键或其他非成员索引的起始值
    uint32_t opCount;       // 主键或其他非成员索引的数量
    int32_t startMkVal;     // 成员索引的起始值
    uint32_t childCount;    // 子节点数量
    int32_t coefficient;    // 字段值生成系数, 通过opStart和coefficient组合生成不同的整形和浮点型字段值
    GmcOperationTypeE optType;          // 操作数据类型, 通过replace/merge/insert
    VlComplexChildOpTypeE childOpType;  // 更新子节点的操作类型 (注意该配置仅update/merge操作有效)
    uint32_t strLen;
    char labelName[MAX_NAME_LEN];
} VlComplexRecordCtxT;

// 设置Typical表的非主键属性
// 数据生成规则：
//  1. 整型和浮点型: value + coefficient
//  2. 位数组类型:   abs(coefficient) % 255 (即生成8位的值, 重复len / 8遍)
//  3. 字符串类型:   abs(coefficient) % 26 + 'a' (即生成1字节的值, 重复len遍)
//  4. 字节数组类型: abs(coefficient) % 26 + 'A' (即生成1字节的值, 重复len遍)
// NOTICE 修改规则时必须同步适配Set/Get方法, 涉及:
//      GtAddTypicalArrayElement, GtGetAndVerifyTypicalChildProperty, GtGetAndVerifyTypicalProperty
int GtSetTypicalUpdateRootProperty(GmcStmtT *stmt, int32_t value, int32_t coefficient)
{
    int64_t eleNum = value + coefficient;
    uint8_t eleBit = abs(coefficient) % 255;
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';

    int64_t a1 = eleNum;
    int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
    RETURN_IFERR(ret);
    uint32_t a2 = eleNum;
    ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
    RETURN_IFERR(ret);
    uint64_t a3 = eleNum;
    ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    RETURN_IFERR(ret);
    float a4 = eleNum;
    ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
    RETURN_IFERR(ret);
    double a5 = eleNum;
    ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
    RETURN_IFERR(ret);

    uint8_t bits[VL_COMPLEX_FIELD_BITMAP_LEN / 8] = {0};
    GtFillBytes(bits, sizeof(bits), eleBit, false);
    GmcBitMapT a6 = {0};
    a6.beginPos = 0;
    a6.endPos = VL_COMPLEX_FIELD_BITMAP_LEN - 1;
    a6.bits = bits;
    ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
    RETURN_IFERR(ret);

    uint8_t a7[VL_COMPLEX_FIELD_FIXED_LEN] = {0};
    GtFillBytes(a7, sizeof(a7), eleFixed, false);
    ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
    RETURN_IFERR(ret);

    uint8_t a8[g_typicalFieldBytesLen] = {0};
    GtFillBytes(a8, sizeof(a8), eleFixed, false);
    ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
    RETURN_IFERR(ret);

    uint8_t a9[g_typicalFieldStringLen] = {0};
    GtFillBytes(a9, sizeof(a9), eleStr, true);
    ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

// 设置Typical表的所有属性
int GtSetTypicalAllRootProperty(GmcStmtT *stmt, int32_t value, int32_t coefficient)
{
    // 设置主键属性
    int32_t a0 = value;
    int ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
    RETURN_IFERR(ret);

    // 设置非主键属性
    ret = GtSetTypicalUpdateRootProperty(stmt, value, coefficient);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

// 增加数组元素至Typical表
int GtAddTypicalArrayElement(GmcNodeT *childNode, int32_t startMkVal, uint32_t childCount, int32_t coefficient)
{
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';

    for (int i = startMkVal; i < startMkVal + childCount; i++) {
        int64_t eleNum = i + coefficient;
        GmcNodeT *elementNode = NULL;
        int ret = GmcNodeAppendElement(childNode, &elementNode);
        RETURN_IFERR(ret);

        int32_t b0 = i;
        ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
        RETURN_IFERR(ret);
        uint32_t b1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
        RETURN_IFERR(ret);

        uint8_t b2[g_typicalFieldBytesLen] = {0};
        GtFillBytes(b2, sizeof(b2), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
        RETURN_IFERR(ret);
        uint8_t b3[g_typicalFieldBytesLen] = {0};
        GtFillBytes(b3, sizeof(b3), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
        RETURN_IFERR(ret);

        uint8_t b4[g_typicalFieldStringLen] = {0};
        GtFillBytes(b4, sizeof(b4), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
        RETURN_IFERR(ret);
        uint8_t b5[g_typicalFieldStringLen] = {0};
        GtFillBytes(b5, sizeof(b5), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
        RETURN_IFERR(ret);

        // 如果还有子节点, 通过该接口获取其句柄: GmcNodeGetChild()
    }
    return GMERR_OK;
}

// 修改Typical表中的数组元素
int GtModifyTypicalArrayElement(GmcNodeT *childNode, int32_t startMkVal, uint32_t childCount, int32_t coefficient)
{
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';

    int ret = GMERR_OK;
    GmcIndexKeyT *mk = NULL;
    for (int i = startMkVal; i < startMkVal + childCount; i++) {
        // 分配成员索引并设置索引属性
        ret = GmcNodeAllocKey(childNode, "M0MemberKey", &mk);
        BREAK_IFERR(ret);
        int32_t m0 = i;
        ret = GmcNodeSetKeyValue(mk, 0, GMC_DATATYPE_INT32, &m0, sizeof(m0));
        BREAK_IFERR(ret);

        // 通过成员索引获取子节点元素
        GmcNodeT *elementNode = NULL;
        ret = GmcNodeGetElementByKey(childNode, mk, &elementNode);
        BREAK_IFERR(ret);

        // 设置子节点元素属性
        int64_t eleNum = i + coefficient;
        uint32_t b1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
        RETURN_IFERR(ret);

        uint8_t b2[g_typicalFieldBytesLen] = {0};
        GtFillBytes(b2, sizeof(b2), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
        RETURN_IFERR(ret);
        uint8_t b3[g_typicalFieldBytesLen] = {0};
        GtFillBytes(b3, sizeof(b3), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
        RETURN_IFERR(ret);

        uint8_t b4[g_typicalFieldStringLen] = {0};
        GtFillBytes(b4, sizeof(b4), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
        RETURN_IFERR(ret);
        uint8_t b5[g_typicalFieldStringLen] = {0};
        GtFillBytes(b5, sizeof(b5), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
        RETURN_IFERR(ret);

        ret = GmcNodeFreeKey(mk);
        BREAK_IFERR(ret);
        mk = NULL;
    }

    if (mk != NULL) {
        GmcNodeFreeKey(mk);
    }
    return ret;
}

// 删除Typical表中的数组元素
int GtRemoveTypicalArrayElement(GmcNodeT *childNode, int32_t startMkVal, uint32_t childCount)
{
    int ret = GMERR_OK;
    GmcIndexKeyT *mk = NULL;
    for (int i = startMkVal; i < startMkVal + childCount; i++) {
        // 通过成员索引删除
        ret = GmcNodeAllocKey(childNode, "M0MemberKey", &mk);
        BREAK_IFERR(ret);
        int32_t m0 = i;
        ret = GmcNodeSetKeyValue(mk, 0, GMC_DATATYPE_INT32, &m0, sizeof(m0));
        BREAK_IFERR(ret);
        ret = GmcNodeRemoveElementByKey(childNode, mk);
        BREAK_IFERR(ret);
        ret = GmcNodeFreeKey(mk);
        BREAK_IFERR(ret);
        mk = NULL;

        // 通过数组下标删除 GmcNodeRemoveElementByIndex(childNode, 1);
        // 清空数组 GmcNodeClear(childNode);
    }

    if (mk != NULL) {
        GmcNodeFreeKey(mk);
    }
    return ret;
}

// 通过member key从stmt中获取并校验子节点数据
int GtGetAndVerifyTypicalChildProperty(GmcStmtT *stmt, int32_t coefficient, int32_t startMkVal, uint32_t childCount)
{
    bool isNull;
    GmcNodeT *rootNode = NULL;
    int ret = GmcGetRootNode(stmt, &rootNode);
    RETURN_IFERR(ret);
    GmcNodeT *childNode = NULL;
    ret = GmcNodeGetChild(rootNode, "M0", &childNode);
    RETURN_IFERR(ret);
#if IS_VERIFY_CHILD_COUNT
    uint32_t opCount;
    ret = GmcNodeGetElementCount(childNode, &opCount);
    RETURN_IFERR(ret);
    if (opCount != childCount) {
        TEST_ERROR("checkout child opCount failed, expect is %d, actual is %d", childCount, opCount);
        return FAILED;
    }
#endif

    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    GmcIndexKeyT *mk = NULL;
    GmcNodeT *eleNode = NULL;
    for (int32_t i = startMkVal; i < startMkVal + childCount; i++) {
        ret = GmcNodeAllocKey(childNode, "M0MemberKey", &mk);
        BREAK_IFERR(ret);
        int32_t b0 = i;
        ret = GmcNodeSetKeyValue(mk, 0, GMC_DATATYPE_INT32, &b0, sizeof(b0));
        BREAK_IFERR(ret);
        ret = GmcNodeGetElementByKey(childNode, mk, &eleNode);
        BREAK_IFERR(ret);

        uint32_t b1;
        ret = GmcNodeGetPropertyByName(eleNode, "B1", &b1, sizeof(b1), &isNull);
        BREAK_IFERR(ret);
        if (b1 != i + coefficient) {
            TEST_ERROR("check vertex B1 falied, expect %d, actual %u", i + coefficient, b1);
            return FAILED;
        }

        uint32_t size;
        ret = GmcNodeGetPropertySizeByName(eleNode, "B2", &size);
        RETURN_IFERR(ret);
        uint8_t b2[size];
        ret = GmcNodeGetPropertyByName(eleNode, "B2", &b2, sizeof(b2), &isNull);
        RETURN_IFERR(ret);
        uint8_t expectB2[g_typicalFieldBytesLen];
        GtFillBytes(expectB2, sizeof(expectB2), eleBytes, false);
        if (memcmp(b2, expectB2, sizeof(expectB2)) != 0) {
            TEST_ERROR("check vertex B2 falied");
            return FAILED;
        }

        ret = GmcNodeGetPropertySizeByName(eleNode, "B3", &size);
        RETURN_IFERR(ret);
        uint8_t b3[size];
        ret = GmcNodeGetPropertyByName(eleNode, "B2", &b3, sizeof(b3), &isNull);
        RETURN_IFERR(ret);
        uint8_t expectB3[g_typicalFieldBytesLen];
        GtFillBytes(expectB3, sizeof(expectB3), eleBytes, false);
        if (memcmp(b3, expectB3, sizeof(expectB3)) != 0) {
            TEST_ERROR("check vertex B3 falied");
            return FAILED;
        }

        ret = GmcNodeGetPropertySizeByName(eleNode, "B4", &size);
        RETURN_IFERR(ret);
        char b4[size];
        ret = GmcNodeGetPropertyByName(eleNode, "B4", &b4, sizeof(b4), &isNull);
        RETURN_IFERR(ret);
        uint8_t expectB4[g_typicalFieldStringLen];
        GtFillBytes(expectB4, sizeof(expectB4), eleStr, true);
        if (memcmp(b4, expectB4, sizeof(expectB4)) != 0) {
            TEST_ERROR("check vertex B4 falied");
            return FAILED;
        }

        ret = GmcNodeGetPropertySizeByName(eleNode, "B5", &size);
        RETURN_IFERR(ret);
        char b5[size];
        ret = GmcNodeGetPropertyByName(eleNode, "B5", &b5, sizeof(b5), &isNull);
        RETURN_IFERR(ret);
        uint8_t expectB5[g_typicalFieldStringLen];
        GtFillBytes(expectB5, sizeof(expectB5), eleStr, true);
        if (memcmp(b5, expectB5, sizeof(expectB5)) != 0) {
            TEST_ERROR("check vertex B5 falied");
            return FAILED;
        }

        ret = GmcNodeFreeKey(mk);
        BREAK_IFERR(ret);
        mk = NULL;

#if ALLOW_PRINT_SCAN_RESULT
        printf("  [\n");
        printf("    B0 = %d\n", b0);
        printf("    B1 = %u\n", b1);
        printf("    B2 = %s, sizeof = %u\n", GtByteToStr(b2, sizeof(b2)), sizeof(b2));
        printf("    B3 = %s, sizeof = %u\n", GtByteToStr(b3, sizeof(b3)), sizeof(b3));
        printf("    B4 = %s, strlen = %u\n", b4, strlen(b4));
        printf("    B5 = %s, strlen = %u\n", b5, strlen(b5));
        printf("  ]\n");
#endif
    }
    if (mk != NULL) {
        GmcNodeFreeKey(mk);
    }

    return ret;
}

bool g_isScanTypicalVerifyProperty = true;

void GtSetTypicalScanVerifyRule(bool isVerify)
{
    g_isScanTypicalVerifyProperty = isVerify;
}

// 从stmt中获取并校验数据
int GtGetAndVerifyTypicalProperty(
    GmcStmtT *stmt, int32_t keyVal, int32_t coefficient, int32_t startMkVal, uint32_t childCount)
{
    if (!g_isScanTypicalVerifyProperty) {
        return GMERR_OK;
    }
    int64_t eleNum = keyVal + coefficient;
    uint8_t eleBit = abs(coefficient) % 255;
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';

    bool isNull;
    int32_t a0;
    int ret = GmcGetVertexPropertyByName(stmt, "A0", &a0, sizeof(a0), &isNull);
    RETURN_IFERR(ret);
    if (a0 != keyVal) {
        TEST_ERROR("check vertex A0 falied, expect %d, actual %d", keyVal, a0);
        return FAILED;
    }

    int64_t a1;
    ret = GmcGetVertexPropertyByName(stmt, "A1", &a1, sizeof(a1), &isNull);
    RETURN_IFERR(ret);
    if (a1 != eleNum) {
        TEST_ERROR("check vertex A1 falied, expect %d, actual %ld", eleNum, a1);
        return FAILED;
    }

    uint32_t a2;
    ret = GmcGetVertexPropertyByName(stmt, "A2", &a2, sizeof(a2), &isNull);
    RETURN_IFERR(ret);
    if (a2 != eleNum) {
        TEST_ERROR("check vertex A2 falied, expect %d, actual %u", eleNum, a2);
        return FAILED;
    }

    uint64_t a3;
    ret = GmcGetVertexPropertyByName(stmt, "A3", &a3, sizeof(a3), &isNull);
    RETURN_IFERR(ret);
    if (a3 != eleNum) {
        TEST_ERROR("check vertex A3 falied, expect %d, actual %lu", eleNum, a3);
        return FAILED;
    }

    float a4;
    ret = GmcGetVertexPropertyByName(stmt, "A4", &a4, sizeof(a4), &isNull);
    RETURN_IFERR(ret);
    if ((int32_t)(a4 * 100) != (int32_t)(eleNum * 100)) {
        TEST_ERROR("check vertex A4 falied, expect %d, actual %.2f", eleNum, a4);
        return FAILED;
    }

    double a5;
    ret = GmcGetVertexPropertyByName(stmt, "A5", &a5, sizeof(a5), &isNull);
    RETURN_IFERR(ret);
    if ((int32_t)(a4 * 100) != (int32_t)(eleNum * 100)) {
        TEST_ERROR("check vertex A5 falied, expect %d, actual %.2f", eleNum, a5);
        return FAILED;
    }

    uint32_t size;
    ret = GmcGetVertexPropertySizeByName(stmt, "A6", &size);
    RETURN_IFERR(ret);
    // bitmap类型字段返回的长度是位长度
    uint8_t a6[size / 8];
    ret = GmcGetVertexPropertyByName(stmt, "A6", &a6, size, &isNull);
    RETURN_IFERR(ret);
    uint8_t expectA6[VL_COMPLEX_FIELD_BITMAP_LEN / 8];
    GtFillBytes(expectA6, sizeof(expectA6), eleBit, false);
    if (memcmp(a6, expectA6, sizeof(expectA6)) != 0) {
        TEST_ERROR("check vertex A6 falied");
        return FAILED;
    }

    ret = GmcGetVertexPropertySizeByName(stmt, "A7", &size);
    RETURN_IFERR(ret);
    uint8_t a7[size];
    ret = GmcGetVertexPropertyByName(stmt, "A7", &a7, sizeof(a7), &isNull);
    RETURN_IFERR(ret);
    uint8_t expectA7[VL_COMPLEX_FIELD_FIXED_LEN];
    GtFillBytes(expectA7, sizeof(expectA7), eleFixed, false);
    if (memcmp(a7, expectA7, sizeof(expectA7)) != 0) {
        TEST_ERROR("check vertex A7 falied");
        return FAILED;
    }

    ret = GmcGetVertexPropertySizeByName(stmt, "A8", &size);
    RETURN_IFERR(ret);
    uint8_t a8[size];
    ret = GmcGetVertexPropertyByName(stmt, "A8", &a8, sizeof(a8), &isNull);
    RETURN_IFERR(ret);
    uint8_t expectA8[g_typicalFieldBytesLen];
    GtFillBytes(expectA8, sizeof(expectA8), eleBytes, false);
    if (memcmp(a8, expectA8, sizeof(expectA8)) != 0) {
        TEST_ERROR("check vertex A8 falied");
        return FAILED;
    }

    ret = GmcGetVertexPropertySizeByName(stmt, "A9", &size);
    RETURN_IFERR(ret);
    char a9[size];
    ret = GmcGetVertexPropertyByName(stmt, "A9", &a9, sizeof(a9), &isNull);
    RETURN_IFERR(ret);
    uint8_t expectA9[g_typicalFieldStringLen];
    GtFillBytes(expectA9, sizeof(expectA9), eleStr, true);
    if (memcmp(a9, expectA9, sizeof(expectA9)) != 0) {
        TEST_ERROR("check vertex A9 falied");
        return FAILED;
    }

#if ALLOW_PRINT_SCAN_RESULT
    printf("{\n");
    printf("  A0 = %d\n", a0);
    printf("  A1 = %ld\n", a1);
    printf("  A2 = %u\n", a2);
    printf("  A3 = %lu\n", a3);
    printf("  A4 = %.6f\n", a4);
    printf("  A5 = %.6f\n", a5);

    printf("  A6 = 0X");
    for (int j = 0; j < sizeof(a6); j++) {
        printf("%02X", a6[j]);
        if ((j + 1) % 4 == 0)
            printf(" ");
    }
    printf(", len = %u\n", sizeof(a6));

    printf("  A7 = %s, sizeof = %u\n", GtByteToStr(a7, sizeof(a7)), sizeof(a7));
    printf("  A8 = %s, sizeof = %u\n", GtByteToStr(a8, sizeof(a8)), sizeof(a8));
    printf("  A9 = %s, strlen = %u\n", a9, strlen(a9));
#endif

    ret = GtGetAndVerifyTypicalChildProperty(stmt, coefficient, startMkVal, childCount);
    RETURN_IFERR(ret);

#if ALLOW_PRINT_SCAN_RESULT
    printf("}\n");
#endif
    return GMERR_OK;
}

// 通过insert写入数据至Typical表
int VlComplexInsert(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_INSERT);
        RETURN_IFERR(ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GtCheckAffectRows(stmt, 1);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 通过replace写入数据至Typical表
int VlComplexReplace(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_REPLACE);
        RETURN_IFERR(ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

int GtSetTypicalVertexIndexKey(GmcStmtT *stmt, int32_t keyValue, const char *keyName)
{
    int ret = GmcSetIndexKeyName(stmt, keyName);
    RETURN_IFERR(ret);
    if (strcmp(keyName, "PrimaryKey") == 0) {
        int32_t tmpValue = keyValue;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmpValue, sizeof(tmpValue));
        RETURN_IFERR(ret);
    } else if (strcmp(keyName, "LocalHashKey") == 0) {
        uint64_t tmpValue = keyValue;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &tmpValue, sizeof(tmpValue));
        RETURN_IFERR(ret);
    } else if (strcmp(keyName, "HashClusterKey") == 0) {
        uint64_t tmpValue = keyValue;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &tmpValue, sizeof(tmpValue));
        RETURN_IFERR(ret);
    } else if (strcmp(keyName, "LocalKey") == 0) {
        uint32_t leftVal = keyValue;
        GmcPropValueT left;
        left.type = GMC_DATATYPE_UINT32;
        left.size = sizeof(leftVal);
        left.value = &leftVal;

        uint32_t rightVal = keyValue + 1;
        GmcPropValueT right;
        right.type = GMC_DATATYPE_UINT32;
        right.size = sizeof(rightVal);
        right.value = &rightVal;

        GmcRangeItemT items[1];
        items[0].lValue = &left;
        items[0].rValue = &right;
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = GMC_ORDER_ASC;

        ret = GmcSetKeyRange(stmt, items, 1);
        RETURN_IFERR(ret);
    } else {
        TEST_ERROR("keyName is invalid, keyName = %s", keyName);
        return FAILED;
    }
    return GMERR_OK;
}

// 通过索引删除Typical表中的数据
int VlComplexDeleteByIndex(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_DELETE);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 通过filter删除Typical表中的数据
int VlComplexDeleteByFilter(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *filter)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t coefficient = vertexCfg.coefficient;

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    ret = GmcSetFilter(stmt, filter);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    return GMERR_OK;
}

// 通过merge写入数据至Typical表
int VlComplexMerge(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_MERGE);
        RETURN_IFERR(ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalVertexIndexKey(stmt, i, "PrimaryKey");
        RETURN_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                return FAILED;
                break;
        }

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 通过异步merge写入数据至Typical表
int VlComplexMergeAsync(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    AsyncUserDataT data = {0};
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalVertexIndexKey(stmt, i, "PrimaryKey");
        RETURN_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                return FAILED;
                break;
        }
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &mergeRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        RETURN_IFERR(data.status);
    }
    return GMERR_OK;
}

// 异步通过replace/insert/merge写入数据至Typical表
int VlComplexWriteAsync(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    AsyncUserDataT data = {0};
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
            RETURN_IFERR(ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
            RETURN_IFERR(ret);
        }
        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        RETURN_IFERR(ret);

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        RETURN_IFERR(data.status);
    }
    return GMERR_OK;
}

// 异步通过索引删除Typical表中的数据
int VlComplexDeleteByIndexAsync(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t coefficient = vertexCfg.coefficient;
    AsyncUserDataT data = {0};

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_DELETE);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        RETURN_IFERR(data.status);
    }
    return GMERR_OK;
}

// 通过索引更新Typical表中的数据
int VlComplexUpdateByIndex(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_UPDATE);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                return FAILED;
                break;
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 异步通过索引更新Typical表中的数据
int VlComplexUpdateByIndexAsync(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;
    GmcOperationTypeE optType = vertexCfg.optType;
    AsyncUserDataT data = {0};

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                return FAILED;
                break;
        }
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(ret);
        RETURN_IFERR(data.status);
        TEST_INFO("The data.affectRows is %d", data.affectRows);
    }
    return GMERR_OK;
}

// 通过filter更新Typical表中的数据
int VlComplexUpdateByFilter(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *filter)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    ret = GtSetTypicalUpdateRootProperty(stmt, opStart, coefficient);
    RETURN_IFERR(ret);
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "M0", &childNode);
    RETURN_IFERR(ret);
    switch (childOpType) {
        case VL_COMPLEX_CHILD_OP_APPEND:
            ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
            RETURN_IFERR(ret);
            break;
        case VL_COMPLEX_CHILD_OP_MODIFY:
            ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
            RETURN_IFERR(ret);
            break;
        case VL_COMPLEX_CHILD_OP_DELETE:
            ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
            RETURN_IFERR(ret);
            break;
        case VL_COMPLEX_CHILD_OP_NONE:
            break;
        default:
            TEST_ERROR("invalid childOpType = %d", childOpType);
            return FAILED;
            break;
    }

    ret = GmcSetFilter(stmt, filter);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    return GMERR_OK;
}

// 异步通过filter更新Typical表中的数据
int VlComplexUpdateByFilterAsync(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *filter)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;
    AsyncUserDataT data = {0};

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    ret = GtSetTypicalUpdateRootProperty(stmt, opStart, coefficient);
    RETURN_IFERR(ret);
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "M0", &childNode);
    RETURN_IFERR(ret);
    switch (childOpType) {
        case VL_COMPLEX_CHILD_OP_APPEND:
            ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
            RETURN_IFERR(ret);
            break;
        case VL_COMPLEX_CHILD_OP_MODIFY:
            ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
            RETURN_IFERR(ret);
            break;
        case VL_COMPLEX_CHILD_OP_DELETE:
            ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
            RETURN_IFERR(ret);
            break;
        case VL_COMPLEX_CHILD_OP_NONE:
            break;
        default:
            TEST_ERROR("invalid childOpType = %d", childOpType);
            return FAILED;
            break;
    }
    ret = GmcSetFilter(stmt, filter);
    RETURN_IFERR(ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmt, &updateRequestCtx);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&data);
    RETURN_IFERR(ret);
    RETURN_IFERR(data.status);
    return GMERR_OK;
}

// 通过filter删除Typical表中的数据
int VlComplexDeleteByFilterAsync(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *filter)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t coefficient = vertexCfg.coefficient;
    AsyncUserDataT data = {0};

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    ret = GmcSetFilter(stmt, filter);
    RETURN_IFERR(ret);
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&data);
    RETURN_IFERR(ret);
    RETURN_IFERR(data.status);
    return GMERR_OK;
}

// 通过hash索引扫描Typical表中的数据
int VlComplexScanByHashIndex(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    int32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);

        bool isFinish = false;
        for (; !isFinish;) {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            if (isFinish) {
                break;
            }
            ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
            RETURN_IFERR(ret);
        }
    }

    return GMERR_OK;
}

// 通过local索引扫描Typical表中的数据
int VlComplexScanByLocalIndex(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName,
    uint32_t leftVal, uint32_t rightVal, GmcOrderDirectionE order)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);

    // 设置扫描条件
    ret = GmcSetIndexKeyName(stmt, keyName);
    RETURN_IFERR(ret);
    GmcPropValueT left;
    left.type = GMC_DATATYPE_UINT32;
    left.size = sizeof(leftVal);
    left.value = &leftVal;

    GmcPropValueT right;
    right.type = GMC_DATATYPE_UINT32;
    right.size = sizeof(rightVal);
    right.value = &rightVal;

    GmcRangeItemT items[1];
    items[0].lValue = &left;
    items[0].rValue = &right;
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = order;
    ret = GmcSetKeyRange(stmt, items, 1);
    RETURN_IFERR(ret);

    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    bool isFinish = false;
    uint32_t i = (order == GMC_ORDER_ASC ? opStart : opStart + opCount - 1);
    for (; !isFinish; i = (order == GMC_ORDER_ASC ? i + 1 : i - 1)) {
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (isFinish) {
            break;
        }
        ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 通过filter扫描Typical表中的数据
int VlComplexScanByFilter(
    GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *filter, GmcOrderDirectionE order)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    ret = GmcSetFilter(stmt, filter);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    for (int32_t i = opStart; !isFinish; i++) {
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (isFinish) {
            break;
        }
        ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 全表扫描
int VlComplexScanFullLable(GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, GmcOrderDirectionE order)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    int ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    // NOTICE 不支持树形表扫描排序
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);

    bool isFinish = false;
    for (int32_t i = opStart; !isFinish; i++) {
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (isFinish) {
            break;
        }
        ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

// 同步通过批量写入数据至Typical表
int VlComplexBatchWrite(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
            RETURN_IFERR(ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
            RETURN_IFERR(ret);
        }
        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        RETURN_IFERR(ret);

        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        RETURN_IFERR(ret);
        if (successNum != 1 || totalNum != 1) {
            TEST_ERROR("batch oper falied, ret = %d.\n", ret);
            return FAILED;
        }
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 异步通过批量写入数据至Typical表
int VlComplexBatchWriteAsync(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
            RETURN_IFERR(ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
            RETURN_IFERR(ret);
        }
        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(data.status);
        TEST_INFO("The data.totalNum : \t%d \tdata.succNum :\t%d", data.totalNum, data.succNum);
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 异步批量通过主键索引更新Typical表中的数据
int VlComplexBatchUpdateByIndexAsync(
    GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;
    GmcOperationTypeE optType = vertexCfg.optType;
    AsyncUserDataT data = {0};
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                return FAILED;
                break;
        }
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(data.status);
        TEST_INFO("The data.totalNum : \t%d \tdata.succNum :\t%d", data.totalNum, data.succNum);
        ret = GmcBatchReset(batch);
        RETURN_IFERR(ret);
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 同步批量通过主键索引更新Typical表中的数据
int VlComplexBatchUpdateByIndex(
    GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                RETURN_IFERR(ret);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                return FAILED;
                break;
        }
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        RETURN_IFERR(ret);
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 同步批量通过主键索引更新Typical表中的数据
int GtBatchDelTypicalVertexByIndex(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        RETURN_IFERR(ret);
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 异步批量通过主键索引删除Typical表中的数据
int GtAsyncBatchDelTypicalVertexByIndex(
    GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg, const char *keyName)
{
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;
    GmcOperationTypeE optType = vertexCfg.optType;
    AsyncUserDataT data = {0};
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, keyName);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(data.status);
        TEST_INFO("The data.totalNum : \t%d \tdata.succNum :\t%d", data.totalNum, data.succNum);
        ret = GmcBatchReset(batch);
        RETURN_IFERR(ret);
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 异步通过批量写入数据至Typical表 buff 超过2M
int GtAsyncBatchWriteTypicalVertexOver(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    int32_t loop = 0;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
            RETURN_IFERR(ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
            RETURN_IFERR(ret);
        }
        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(stmt, &rootNode);
        RETURN_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        RETURN_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        RETURN_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (loop == 3 || i == 99) {
            GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            testWaitAsyncRecv(&data);
            GmcBatchReset(batch);
            loop = 0;
        }
        loop++;
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// 循环对同一批数据异步通过批量写入数据至Typical表 buff 超过2M
int GtAsyncBatchWriteOverSamePK(GmcConnT *conn, GmcStmtT *stmt, VlComplexRecordCtxT vertexCfg)
{
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    int32_t loop = 0;

    while (loop < 100) {
        for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
            ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, optType);
            RETURN_IFERR(ret);
            if (optType == GMC_OPERATION_MERGE) {
                ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
                RETURN_IFERR(ret);
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
                RETURN_IFERR(ret);
            }
            GmcNodeT *rootNode = NULL;
            ret = GmcGetRootNode(stmt, &rootNode);
            RETURN_IFERR(ret);

            // 设置所有根节点属性
            ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
            RETURN_IFERR(ret);

            // 设置子节点属性
            GmcNodeT *childNode = NULL;
            ret = GmcGetChildNode(stmt, "M0", &childNode);
            RETURN_IFERR(ret);
            ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
            RETURN_IFERR(ret);
            ret = GmcBatchAddDML(batch, stmt);
            if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
                TEST_WARN("program limit exceeded, ret = %d.\n", ret);
            }
            RETURN_IFERR(ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        RETURN_IFERR(data.status);
        TEST_INFO("The data.totalNum : \t%d \tdata.succNum :\t%d", data.totalNum, data.succNum);
        loop++;
    }
    GmcBatchDestroy(batch);
    return GMERR_OK;
}

// XXX 专有方法和定义建议增加专属标识
typedef struct TagSnUserData {
    int insertNum;
    int updateNum;
    int deleteNum;
    int replaceInsertNum;
    int replaceUpdateNum;
    int kvSetNum;
    int scanNum;
    int scanEofNum;
    int agedNum;
    int newCoefficient;  // 预期的新值
    int oldCoefficient;  // 预期的旧值
    int newVertexNum;    // 统计推送的新数据的数据量
    int oldVertexNum;    // 统计推送的老数据的数据量
    int keyNum;          // 统计推送的key的数据量
} GtSnUserDataT;

bool g_isSnCallbackSleep = false;
bool g_isSnVerifyVertexProperty = false;

void GtSnSetCallbackSleep(bool isSleep)
{
    g_isSnCallbackSleep = isSleep;
}

void GtSnSetVerifyProperty(bool isVerify)
{
    g_isSnVerifyVertexProperty = isVerify;
}

// 订阅回调
void sn_callback_simple_02(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i, ret = 0;
    char labelName[MAX_NAME_LEN] = {0};
    unsigned int labelNameLen = MAX_NAME_LEN;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                printf("[info] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                user_data->scanEofNum++;
            }
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                TEST_ERROR("eventType error");
                break;
            }
        }
    }
}

void GtSnCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    GtSnUserDataT *data = (GtSnUserDataT *)userData;
    bool eof = false;

    // 判断是否阻塞客户端消费 (默认不阻塞)
    while (g_isSnCallbackSleep) {
        sleep(1);
    };

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            break;
        }
        ret = GmcFetch(subStmt, &eof);
        BREAK_IFERR(ret);
        if (eof) {
            break;
        }

        for (int32_t i = 0; i < info->labelCount; i++) {
            char labelName[MAX_NAME_LEN] = {0};
            uint32_t labelNameLen = MAX_NAME_LEN;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            BREAK_IFERR(ret);

            // 注意位运算优先级
            // 处理旧数据 (1: old object)
            if ((info->msgType & 0x1) != 0) {
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                if (ret == GMERR_OK) {
                    if (g_isSnVerifyVertexProperty) {
                        bool isNull;
                        int32_t keyValue;
                        ret = GmcGetVertexPropertyByName(subStmt, "A0", &keyValue, sizeof(keyValue), &isNull);
                        BREAK_IFERR(ret);
                        int coefficient = data->oldCoefficient;
                        ret = GtGetAndVerifyTypicalProperty(subStmt, keyValue, coefficient, 0, 0);
                        BREAK_IFERR(ret);
                    }
                    data->oldVertexNum++;
                } else if (ret != GMERR_INVALID_PARAMETER_VALUE) {
                    TEST_ERROR("sub set fetch mode failed, ret = %d", ret);
                    break;
                }
            }

            // 处理新数据 (2: new object)
            if ((info->msgType & 0x2) != 0) {
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                if (ret == GMERR_OK) {
                    if (g_isSnVerifyVertexProperty) {
                        bool isNull;
                        int32_t keyValue;
                        ret = GmcGetVertexPropertyByName(subStmt, "A0", &keyValue, sizeof(keyValue), &isNull);
                        BREAK_IFERR(ret);
                        int coefficient = data->newCoefficient;
                        ret = GtGetAndVerifyTypicalProperty(subStmt, keyValue, coefficient, 0, 0);
                        BREAK_IFERR(ret);
                    }
                    data->newVertexNum++;
                } else if (ret != GMERR_INVALID_PARAMETER_VALUE) {
                    TEST_ERROR("sub set fetch mode failed, ret = %d", ret);
                    break;
                }
            }

            // 处理key数据 (4: key)
            if ((info->msgType & 0x4) != 0 && (info->eventType != GMC_SUB_EVENT_DELETE) &&
                (info->eventType != GMC_SUB_EVENT_AGED)) {
                if (g_isSnVerifyVertexProperty) {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    BREAK_IFERR(ret);
                    bool isNull;
                    int32_t keyValue;
                    ret = GmcGetVertexPropertyByName(subStmt, "A0", &keyValue, sizeof(keyValue), &isNull);
                    BREAK_IFERR(ret);
                    int coefficient = data->newCoefficient;
                    ret = GtGetAndVerifyTypicalProperty(subStmt, keyValue, coefficient, 0, 0);
                    BREAK_IFERR(ret);
                }
                data->keyNum++;
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                data->updateNum++;
                break;
            }
            // case GMC_SUB_EVENT_REPLACE: {
            //     data->replaceNum++;
            //     break;
            // }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            default: {
                TEST_ERROR("invalid eventType = %d", info->eventType);
                abort();
            }
        }
    }
}

// 等待订阅推送消息
// 出于定制化考虑, 引用修改 testWaitSnRecv 函数

// 打印订阅推送统计数据
void GtPrintSnStatis(GtSnUserDataT snUserData, const char *flag = "")
{
    printf("sn statis %s:\n", flag);
    printf("insertNum = %d\n", snUserData.insertNum);
    printf("updateNum = %d\n", snUserData.updateNum);
    printf("deleteNum = %d\n", snUserData.deleteNum);
    printf("replaceInsertNum = %d\n", snUserData.replaceInsertNum);
    printf("replaceUpdateNum = %d\n", snUserData.replaceUpdateNum);
    printf("scanEofNum = %d\n", snUserData.scanEofNum);
    printf("scanNum = %d\n", snUserData.scanNum);
    printf("agedNum = %d\n", snUserData.agedNum);
    printf("kvSetNum = %d\n", snUserData.kvSetNum);
    printf("newVertexNum = %d\n", snUserData.newVertexNum);
    printf("oldVertexNum = %d\n", snUserData.oldVertexNum);
    printf("keyNum = %d\n", snUserData.keyNum);
}

bool g_isThreadTypicalRandomOperation = true;

void GtThreadSetTypicalOperationType(bool isRandomOper)
{
    g_isThreadTypicalRandomOperation = isRandomOper;
}

// 多线程并发场景获取错误码
int InnerVlComplexThreadGetExecuteStatus(int ret, GmcOperationTypeE operType = GMC_OPERATION_BUTT)
{
    // GMERR_PROGRAM_LIMIT_EXCEEDED
    int32_t ignoreStatus[] = {GMERR_DATA_EXCEPTION, GMERR_PRIMARY_KEY_VIOLATION, GMERR_INTERNAL_ERROR,
        GMERR_OUT_OF_MEMORY, GMERR_UNIQUE_VIOLATION};

    for (int32_t i = 0; i < sizeof(ignoreStatus) / sizeof(ignoreStatus[0]); i++) {
        if (ret == ignoreStatus[i]) {
            return GMERR_OK;
        }
    }
    return ret;
}

void *GtThreadInsertTypicalVertex(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_INSERT);
        BREAK_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        BREAK_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        BREAK_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        BREAK_IFERR(ret);

        ret = GmcExecute(stmt);
        ret = InnerVlComplexThreadGetExecuteStatus(ret);
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadReplaceTypicalVertex(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_REPLACE);
        BREAK_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, i, coefficient);
        BREAK_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        BREAK_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        BREAK_IFERR(ret);

        ret = GmcExecute(stmt);
        ret = InnerVlComplexThreadGetExecuteStatus(ret);
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadMergeTypicalVertex(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_MERGE);
        BREAK_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalVertexIndexKey(stmt, i, "PrimaryKey");
        BREAK_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        BREAK_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        BREAK_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                ret = FAILED;
                break;
        }
        BREAK_IFERR(ret);

        ret = GmcExecute(stmt);
        ret = InnerVlComplexThreadGetExecuteStatus(ret);
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadUpdateTypicalVertexByPk(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    VlComplexChildOpTypeE childOpType = vertexCfg.childOpType;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_UPDATE);
        BREAK_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, "PrimaryKey");
        BREAK_IFERR(ret);
        ret = GtSetTypicalUpdateRootProperty(stmt, i, coefficient);
        BREAK_IFERR(ret);
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        BREAK_IFERR(ret);
        switch (childOpType) {
            case VL_COMPLEX_CHILD_OP_APPEND:
                ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                break;
            case VL_COMPLEX_CHILD_OP_MODIFY:
                ret = GtModifyTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
                break;
            case VL_COMPLEX_CHILD_OP_DELETE:
                ret = GtRemoveTypicalArrayElement(childNode, startMkVal, childCount);
                break;
            case VL_COMPLEX_CHILD_OP_NONE:
                break;
            default:
                TEST_ERROR("invalid childOpType = %d", childOpType);
                ret = FAILED;
                break;
        }
        BREAK_IFERR(ret);

        ret = GmcExecute(stmt);
        ret = InnerVlComplexThreadGetExecuteStatus(ret);
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadDeleteTypicalVertexByPk(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_DELETE);
        BREAK_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, "PrimaryKey");
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        ret = InnerVlComplexThreadGetExecuteStatus(ret);
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadScanTypicalVertexByPrimary(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    int32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, "PrimaryKey");
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);

        bool isFinish = false;
        for (; !isFinish;) {
            ret = GmcFetch(stmt, &isFinish);
            BREAK_IFERR(ret);
            if (isFinish) {
                break;
            }
            ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadScanTypicalVertexByLocalHash(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    int32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, "LocalHashKey");
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);

        bool isFinish = false;
        for (; !isFinish;) {
            ret = GmcFetch(stmt, &isFinish);
            BREAK_IFERR(ret);
            if (isFinish) {
                break;
            }
            ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadScanTypicalVertexByHashCluster(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    int32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = opStart; i < opStart + opCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        ret = GtSetTypicalVertexIndexKey(stmt, i, "HashClusterKey");
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);

        bool isFinish = false;
        for (; !isFinish;) {
            ret = GmcFetch(stmt, &isFinish);
            BREAK_IFERR(ret);
            if (isFinish) {
                break;
            }
            ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadScanTypicalVertexByLocalIndex(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    uint32_t leftVal = vertexCfg.opStart;
    uint32_t rightVal = vertexCfg.opStart + vertexCfg.opCount;
    GmcOrderDirectionE order = GMC_ORDER_ASC;

    do {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);

        // 设置扫描条件
        ret = GmcSetIndexKeyName(stmt, "LocalKey");
        BREAK_IFERR(ret);
        GmcPropValueT left;
        left.type = GMC_DATATYPE_UINT32;
        left.size = sizeof(leftVal);
        left.value = &leftVal;

        GmcPropValueT right;
        right.type = GMC_DATATYPE_UINT32;
        right.size = sizeof(rightVal);
        right.value = &rightVal;

        GmcRangeItemT items[1];
        items[0].lValue = &left;
        items[0].rValue = &right;
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = order;
        ret = GmcSetKeyRange(stmt, items, 1);
        BREAK_IFERR(ret);

        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);
        bool isFinish = false;
        uint32_t i = (order == GMC_ORDER_ASC ? opStart : opStart + opCount - 1);
        for (; !isFinish; i = (order == GMC_ORDER_ASC ? i + 1 : i - 1)) {
            ret = GmcFetch(stmt, &isFinish);
            BREAK_IFERR(ret);
            if (isFinish) {
                break;
            }
            ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
    } while (0);

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

void *GtThreadInserTypicalVertexWithSamePk(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t startPkVal = vertexCfg.opStart;
    uint32_t vertexCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_INSERT);
        BREAK_IFERR(ret);

        // 设置所有根节点属性
        ret = GtSetTypicalAllRootProperty(stmt, startPkVal, coefficient);
        BREAK_IFERR(ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(stmt, "M0", &childNode);
        BREAK_IFERR(ret);
        ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        BREAK_IFERR(ret);

        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            ret = GMERR_OK;
            continue;
        }
        ret = GtCheckAffectRows(stmt, 1);
        BREAK_IFERR(ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

// local 索引扫描全表的线程函数
void *GtThreadScanTypicalVertexByLocalIndexDesc(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    uint32_t leftVal = vertexCfg.opStart;
    uint32_t rightVal = vertexCfg.opStart + vertexCfg.opCount;
    GmcOrderDirectionE order = GMC_ORDER_DESC;

    do {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);

        // 设置扫描条件
        GmcPropValueT left;
        left.type = GMC_DATATYPE_UINT32;
        left.size = sizeof(leftVal);
        left.value = &leftVal;

        GmcPropValueT right;
        right.type = GMC_DATATYPE_UINT32;
        right.size = sizeof(rightVal);
        right.value = &rightVal;

        GmcRangeItemT items[1];
        items[0].lValue = &left;
        items[0].rValue = &right;
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = order;
        ret = GmcSetKeyRange(stmt, items, 1);
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, "LocalKey");
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);
        bool isFinish = false;
        uint32_t i = (order == GMC_ORDER_ASC ? opStart : opStart + opCount - 1);
        for (; !isFinish; i = (order == GMC_ORDER_ASC ? i + 1 : i - 1)) {
            ret = GmcFetch(stmt, &isFinish);
            BREAK_IFERR(ret);
            if (isFinish) {
                break;
            }
            ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
    } while (0);

    ret = testGmcDisconnect(conn, stmt);
    conn = NULL;
    stmt = NULL;
    return (void *)(int64_t)ret;
}

// Filter 扫描全表的线程函数
void *GtThreadScanTypicalVertexByFilter(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    int32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;
    const char *filter = (char *)"A1>0";

    do {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        ret = GmcSetFilter(stmt, filter);
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);
    } while (0);
    if (ret != GMERR_OK) {
        testGmcDisconnect(conn, stmt);
        return (void *)(int64_t)ret;
    }

    bool isFinish = false;
    for (int32_t i = opStart; !isFinish; i++) {
        ret = GmcFetch(stmt, &isFinish);
        BREAK_IFERR(ret);
        if (isFinish) {
            break;
        }
        ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
        BREAK_IFERR(ret);
    }
    if (ret != GMERR_OK) {
        testGmcDisconnect(conn, stmt);
        return (void *)(int64_t)ret;
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

// fulltable 扫描全表的线程函数
void *GtThreadScanTypicalVertexFullTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GtConnectWithCsMode(&conn, &stmt);
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    VlComplexRecordCtxT vertexCfg = *(VlComplexRecordCtxT *)arg;
    int32_t opStart = vertexCfg.opStart;
    uint32_t opCount = vertexCfg.opCount;
    int32_t startMkVal = vertexCfg.startMkVal;
    int32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    do {
        ret = GmcPrepareStmtByLabelName(stmt, vertexCfg.labelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        ret = GmcExecute(stmt);
        BREAK_IFERR(ret);
    } while (0);
    if (ret != GMERR_OK) {
        testGmcDisconnect(conn, stmt);
        return (void *)(int64_t)ret;
    }

    bool isFinish = false;
    for (int32_t i = opStart; !isFinish; i++) {
        ret = GmcFetch(stmt, &isFinish);
        BREAK_IFERR(ret);
        if (isFinish) {
            break;
        }
        ret = GtGetAndVerifyTypicalProperty(stmt, i, coefficient, startMkVal, childCount);
        BREAK_IFERR(ret);
    }
    if (ret != GMERR_OK) {
        testGmcDisconnect(conn, stmt);
        return (void *)(int64_t)ret;
    }

    ret = testGmcDisconnect(conn, stmt);
    return (void *)(int64_t)ret;
}

#endif /* VL_COMPLEX_H */
