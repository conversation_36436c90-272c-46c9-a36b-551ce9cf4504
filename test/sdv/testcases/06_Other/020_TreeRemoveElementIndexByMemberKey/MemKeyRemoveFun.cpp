/*****************************************************************************
 Description  : 通过memberKeyValues索引集查找并删除子节点接口功能测试
 Notes        :
 History      :
 Author       : 覃乾宝 qwx995465
 Modification :
 Date         : 2021/06/15
*****************************************************************************/
#include "ArrayNestTest.h"

class MemKeyRemoveFun : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MemKeyRemoveFun::SetUpTestCase()
{
    printf("[INFO] MemKeyRemoveFun Start.\n");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void MemKeyRemoveFun::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] MemKeyRemoveFun End.\n");
}

void MemKeyRemoveFun::SetUp()
{
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, labelName1);
    readJanssonFile("schemaFile/NormalTreeModel.gmjson", &labelJson1);
    ASSERT_NE((void *)NULL, labelJson1);

    AW_CHECK_LOG_BEGIN();
}

void MemKeyRemoveFun::TearDown()
{
    AW_CHECK_LOG_END();
    free(labelJson1);
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : ector嵌套vector,创建label，插入数据，删除第二层vector某条记录，查询size，数据查询
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_001)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // uodate
    void *vertexLabel = NULL;
    bool new_bool_value = true;
    char *new_f14_value = (char *)"newstr";
    int new_value = 2;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcSetNodePropertyByName_P(T1, pk * index, bool_value, f14_value);
    // 插array节点
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(T2, j, &T2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "T4", &T4);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            ret = GmcNodeGetElementByIndex(T4, m, &T4);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A2(T4, m * index, bool_value, f14_value);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeAppendElement(T5, &T5);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V2(T5, m * new_value, new_bool_value, new_f14_value);
        }
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // T3.T5 is new value,other node is old value
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    uint32_t size = 0;
    bool eof;
    bool &isNull = eof;
    // uint32_t size = 0;
    // ret = GmcNodeGetElementCount(T3, &size);
    // EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(vector_num, size);
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);

    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 1; j++) {
        uint64_t mk3 = 7 * j;
        uint32_t vector_size = 3;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T3, T3_mk, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ;
        for (uint32_t m = 0; m < vector_num; m++) {
            uint64_t mk4 = 7 * m * 2;
            ret = GmcNodeGetElementCount(T5, &size);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(vector_size, size);
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T5, T5_mk);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t f5_value;
            ret = GmcNodeGetPropertyByName(T5, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ((unsigned int)0, isNull);
            }
            vector_size--;
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);
    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,删除第一层vector某条record，查询第一层vector record
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_002)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *vertexLabel = NULL;
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    uint32_t size = 0;
    bool eof;
    bool &isNull = eof;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f5_value;
        ret = GmcNodeGetPropertyByName(T3, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((unsigned int)0, isNull);
        }
    }
    GmcNodeFreeKey(T3_mk);
    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,删除第二层vector某条record，查询第二层vector record
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_003)
{
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    uint32_t size = 0;
    bool eof;
    bool &isNull = eof;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 1; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T3, T3_mk, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ;
        for (uint32_t m = 0; m < vector_num; m++) {
            uint64_t mk4 = 7 * m;
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T5, T5_mk);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t f5_value;
            ret = GmcNodeGetPropertyByName(T5, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
            if (ret != 0) {
                EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
                testGmcGetLastError(NULL);
            } else {
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ((unsigned int)0, isNull);
            }
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,删除第一层vector record(mk=0)，查询第二层vector对应(mk=0)记录
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_004)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    unsigned int size = 0;
    unsigned int isNull;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);

    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 3; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
        // TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);;
        for (uint32_t m = 0; m < vector_num; m++) {
            uint64_t mk4 = 7 * m;
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T5, T5_mk, &T5);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            EXPECT_EQ(GMERR_OK, ret);  //
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,删除第一层，紧接着删除第二层
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_005)
{
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    unsigned int size = 0;
    unsigned int isNull;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);

    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 3; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
        // TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);;
        for (uint32_t m = 0; m < vector_num; m++) {
            uint64_t mk4 = 7 * m;
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T5, T5_mk);
            if (ret != 0) {
                EXPECT_EQ(GMERR_NO_DATA, ret);
                testGmcGetLastError(NULL);
            } else {
                EXPECT_EQ(GMERR_OK, ret);  //
            }
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector, clearNode之后，再调用新增接口删除节点record
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_006)
{
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);
    // clear T3
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // ret = GmcNodeClear(T3);
    // ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int nodeSize;
    ret = GmcNodeGetElementCount(T3, &nodeSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3, nodeSize);
    // T3 is empty,Other node is old value
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 1; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : array嵌套array,vector嵌套vector,重复调用接口删除节点记录
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_007)
{
    int ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    unsigned int size = 0;
    unsigned int isNull;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 1; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (uint32_t j = 0; j < 1; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_NO_DATA, ret);
        testGmcGetLastError(NULL);
    }
    GmcNodeFreeKey(T3_mk);
    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套record节点再嵌套vector,写入数据，更新顶点，查询顶点, 删除顶点
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_008)
{
    char *labelJson26 = NULL;
    readJanssonFile("schemaFile/NormalTreeModel_026.gmjson", &labelJson26);
    ASSERT_NE((void *)NULL, labelJson26);
    int ret = GmcCreateVertexLabel(stmt, labelJson26, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson26);

    int64_t i = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex_026(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // update vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 2;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *T3;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
    // 插array节点
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(T2, j, &T2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "A_T3", &A_T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_T1_T2_T3_P(A_T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(A_T3, "T4", &T4);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            TestGmcSetNodePropertyByName_T1_T2_T3_T4_A2(T4, m * index, bool_value, f14_value);
            if (m < array_num - 1) {
                ret = GmcNodeGetNextElement(T4, &T4);
                ASSERT_EQ(GMERR_OK, ret);
            }
        }
        if (j < array_num - 1) {
            ret = GmcNodeGetNextElement(T2, &T2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "V_T4", &V_T4);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_T3_T4_P(V_T4, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(V_T4, "T5", &T5);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeAppendElement(T5, &T5);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_T3_T4_T5_V2(T5, m * index, bool_value, f14_value);
        }
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 备注：遗留replace后查询数据被清空 涉及用例008、009、010
    // TestVertexDirectFetch_026(stmt, i, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, i * index, bool_value, f14_value);

    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T3_mk1;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk1);
    ASSERT_EQ(GMERR_OK, ret);
    // 删除第一层vector节点记录
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T3_mk1, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套record节点和vector,插入顶点，更新顶点，查询顶点,删除顶点
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_009)
{
    char *labelJson27 = NULL;
    readJanssonFile("schemaFile/NormalTreeModel_027.gmjson", &labelJson27);
    ASSERT_NE((void *)NULL, labelJson27);
    int ret = GmcCreateVertexLabel(stmt, labelJson27, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson27);

    int64_t i = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // update vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 2;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *T3;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "A_T3", &A_T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_T1_T2_T3_P(A_T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            TestGmcSetNodePropertyByName_A2(T4, m * index, bool_value, f14_value);
            if (m < array_num - 1) {
                ret = GmcNodeGetNextElement(T4, &T4);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        if (j < array_num - 1) {
            ret = GmcNodeGetNextElement(T2, &T2);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "V_T4", &V_T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_T3_T4_P(V_T4, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeAppendElement(T5, &T5);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V2(T5, m * index, bool_value, f14_value);
        }
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // read vertex
    // TestVertexDirectFetch_027(stmt, i, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, i * index, bool_value, f14_value);

    // 删除第一层vector节点记录
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T3_mk1;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk1);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T3_mk1, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,memberKey都非唯一，插入顶点，更新顶点，查询顶点，删除顶点
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_010)
{
    char *labelJson = NULL;
    readJanssonFile("schemaFile/NormalTreeModelMkUniqueFalse.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);

    int64_t i = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // update vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 2;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_PK(root, i);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
    // 插array节点
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcSetNodePropertyByName_A1(T2, 1 * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            TestGmcSetNodePropertyByName_A2(T4, 1 * index, bool_value, f14_value);
            if (m < array_num - 1) {
                ret = GmcNodeGetNextElement(T4, &T4);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        if (j < array_num - 1) {
            ret = GmcNodeGetNextElement(T2, &T2);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V1(T3, 1 * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeAppendElement(T5, &T5);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V2(T5, 1 * index, bool_value, f14_value);
        }
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // read vertex
    // TestVertexDirectFetchMkUniqueFalse(stmt, i, index, bool_value, f14_value, array_num, vector_num, labelName1,
    // PKName1, 1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, i * index, bool_value, f14_value);

    // 删除第一层vector节点记录
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T3_mk1;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk1);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * index;
        ret = GmcNodeSetKeyValue(T3_mk1, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,memberKey仅第一层非唯一，插入顶点，更新vector，删除vector及顶点
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_011)
{
    char *labelJson = NULL;
    readJanssonFile("schemaFile/NormalTreeModelMkUniqueFalse1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);

    int64_t i = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    bool_value = true;
    f14_value = (char *)"newstr";
    index = 2;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_PK(root, i);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
    // 插array节点
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            TestGmcSetNodePropertyByName_A2(T4, m * index, bool_value, f14_value);
            if (m < array_num - 1) {
                ret = GmcNodeGetNextElement(T4, &T4);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        if (j < array_num - 1) {
            ret = GmcNodeGetNextElement(T2, &T2);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeAppendElement(T5, &T5);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V2(T5, m * index, bool_value, f14_value);
        }
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, i * index, bool_value, f14_value);

    // 删除第一层vector节点记录
    GmcIndexKeyT *T3_mk1;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk1);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T3_mk1, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套vector,memberKey仅第二层非唯一，插入顶点，更新vector,删除vector及顶点
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_012)
{
    char *labelJson = NULL;
    readJanssonFile("schemaFile/NormalTreeModelMkUniqueFalse2.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    int ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);

    int64_t i = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";

    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    bool_value = true;
    f14_value = (char *)"newstr";
    index = 2;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_PK(root, i);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
    // 插array节点
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            TestGmcSetNodePropertyByName_A2(T4, m * index, bool_value, f14_value);
            if (m < array_num - 1) {
                ret = GmcNodeGetNextElement(T4, &T4);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        if (j < array_num - 1) {
            ret = GmcNodeGetNextElement(T2, &T2);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeAppendElement(T5, &T5);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V2(T5, m * index, bool_value, f14_value);
        }
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, i * index, bool_value, f14_value);

    // 删除第一层vector节点记录
    GmcIndexKeyT *T3_mk1;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk1);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T3_mk1, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : vector嵌套record节点再嵌套vector,memberKey仅第二层非唯一，插入顶点，更新vector,删除vector及顶点
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_013)
{
    char *labelJson26 = NULL;
    readJanssonFile("schemaFile/NormalTreeModelMkUniqueFalse3.gmjson", &labelJson26);
    ASSERT_NE((void *)NULL, labelJson26);
    int ret = GmcCreateVertexLabel(stmt, labelJson26, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson26);

    int64_t i = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    // insert vertex
    TestInsert_vertex_026(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *T3;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *T5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, i * index, bool_value, f14_value);

    // 删除第一层vector节点记录
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V1_01(
    GmcNodeT *T5, int i, bool bool_value, char *f14_value, bool sp = false, bool BitMap = true)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    int8_t f0_value = value_8;
    uint8_t f1_value = value_u8;
    int16_t f2_value = value_16;
    uint16_t f3_value = value_u16;
    int32_t f4_value = 4 * i;
    if (sp) {
        char *spset = (char *)malloc(10);
        *(int8_t *)(spset) = f0_value;
        *(uint8_t *)(spset + 1) = f1_value;
        *(int16_t *)(spset + 2) = f2_value;
        *(uint16_t *)(spset + 4) = f3_value;
        *(int32_t *)(spset + 6) = f4_value;
        ret = GmcNodeGetSuperfieldByName(T5, (char *)"superfiled2", spset, 10);
        EXPECT_EQ(GMERR_OK, ret);
        free(spset);
    } else {
        ret = GmcNodeSetPropertyByName(T5, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T5, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T5, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T5, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T5, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(T5, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T5, (char *)"V20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (BitMap) {
        GmcBitMapT bitMap = {0, 127, NULL};
        uint8_t bits[128 / 8];
        memset(bits, 0xff, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcNodeSetPropertyByName(T5, (char *)"V21", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestInsert_vertex_027_01(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    // ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    // ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    GmcNodeT *root;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T5", &T5);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A2(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T5, &T5);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V1_01(T5, j * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/*******************************************************************************
Description : 不同vector节点memberKey和memberkey值相同
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_014)
{
    char *labelJson27 = NULL;
    readJanssonFile("schemaFile/NormalTreeModel_027_01.gmjson", &labelJson27);
    ASSERT_NE((void *)NULL, labelJson27);
    int ret = GmcCreateVertexLabel(stmt, labelJson27, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson27);

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex_027_01(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    GmcNodeT *root;

    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T5", &T5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T5", &T5);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    // 删除第一层vector节点记录
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T3, T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T3_mk);

    // 删除第一层vector节点记录
    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk3", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T5, T5_mk);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T5_mk);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V3_027(
    GmcNodeT *T6, int i, bool bool_value, char *f14_value, bool sp = false, bool BitMap = true)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    int8_t f0_value = value_8;
    uint8_t f1_value = value_u8;
    int16_t f2_value = value_16;
    uint16_t f3_value = value_u16;
    int32_t f4_value = 4 * i;
    if (sp) {
        char *spset = (char *)malloc(10);
        *(int8_t *)(spset) = f0_value;
        *(uint8_t *)(spset + 1) = f1_value;
        *(int16_t *)(spset + 2) = f2_value;
        *(uint16_t *)(spset + 4) = f3_value;
        *(int32_t *)(spset + 6) = f4_value;
        ret = GmcNodeGetSuperfieldByName(T6, (char *)"superfiled2", spset, 10);
        EXPECT_EQ(GMERR_OK, ret);
        free(spset);
    } else {
        ret = GmcNodeSetPropertyByName(T6, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T6, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T6, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T6, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(T6, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(T6, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(T6, (char *)"V20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (BitMap) {
        GmcBitMapT bitMap = {0, 127, NULL};
        uint8_t bits[128 / 8];
        memset(bits, 0xff, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcNodeSetPropertyByName(T6, (char *)"V21", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestInsert_vertex_027_02(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    // ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    GmcNodeT *T6;
    GmcNodeT *root;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T6", &T6);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A1(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A2(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V2(T5, m * index, bool_value, f14_value);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T6, &T6);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V1_01(T6, j * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*******************************************************************************
Description : vector节点与另一vector嵌套的vector子节点memberKey和memberkey值相同
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_015)
{
    char *labelJson27 = NULL;
    readJanssonFile("schemaFile/NormalTreeModel_027_02.gmjson", &labelJson27);
    ASSERT_NE((void *)NULL, labelJson27);
    int ret = GmcCreateVertexLabel(stmt, labelJson27, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson27);

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex_027_02(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    GmcNodeT *T6;
    GmcNodeT *root;

    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T6", &T6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T6", &T6);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    unsigned int size = 0;
    unsigned int isNull;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 1; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T3, T3_mk, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ;
        for (uint32_t m = 0; m < vector_num; m++) {
            uint64_t mk4 = 7 * m;
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T5, T5_mk);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);
    GmcIndexKeyT *T6_mk;
    ret = GmcNodeAllocKey(T6, "mk4", &T6_mk);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除第一层vector节点记录
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk4 = 7 * j * index;
        ret = GmcNodeSetKeyValue(T6_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(T6, T6_mk);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcNodeFreeKey(T6_mk);
    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : Member key支持定义在一个或多个属性上(不连续)，最大属性个数为8个，删除节点record
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_016)
{
    char *labelJson27 = NULL;
    readJanssonFile("schemaFile/NormalTreeModel_MemKey_08.gmjson", &labelJson27);
    ASSERT_NE((void *)NULL, labelJson27);
    int ret = GmcCreateVertexLabel(stmt, labelJson27, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson27);
    ;

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    GmcNodeT *root;

    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    unsigned int size = 0;
    unsigned int isNull;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T3, T3_mk, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            uint32_t mk4_V5 = 5 * m;
            int64_t mk4_V6 = 6 * m;
            uint64_t mk4_V7 = 7 * m;
            char mk4_V12 = m % 128;
            unsigned char mk4_V13 = m % 256;
            uint64_t mk4_V11 = 11 * m;
            char *f14_value = (char *)"string";
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT32, &mk4_V5, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 1, GMC_DATATYPE_INT64, &mk4_V6, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 2, GMC_DATATYPE_UINT64, &mk4_V7, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 3, GMC_DATATYPE_CHAR, &mk4_V12, sizeof(char));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 4, GMC_DATATYPE_UCHAR, &mk4_V13, sizeof(unsigned char));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 5, GMC_DATATYPE_TIME, &mk4_V11, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 6, GMC_DATATYPE_STRING, f14_value, strlen(f14_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 7, GMC_DATATYPE_BYTES, f14_value, 7);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T5, T5_mk);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : Member key支持定义在一个或多个属性上(连续)，最大属性个数为8个，删除节点record
*******************************************************************************/
TEST_F(MemKeyRemoveFun, Other_020_RemoveByMemKeyFuncTest_017)
{
    char *labelJson27 = NULL;
    readJanssonFile("schemaFile/NormalTreeModel_MemKey_08_con.gmjson", &labelJson27);
    ASSERT_NE((void *)NULL, labelJson27);
    int ret = GmcCreateVertexLabel(stmt, labelJson27, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson27);
    ;

    int64_t pk = 0;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *vertexLabel = NULL;
    // insert vertex
    TestInsert_vertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    GmcNodeT *root;

    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
    TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);

    unsigned int size = 0;
    unsigned int isNull;
    //读vector节点
    GmcIndexKeyT *T3_mk;
    ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T3, "T5", &T5);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *T5_mk;
    ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j;
        ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T3, T3_mk, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V1(T3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            uint32_t mk4_V5 = 5 * m;
            int64_t mk4_V6 = 6 * m;
            uint64_t mk4_V7 = 7 * m;
            int8_t mk4_V0 = m % 128;
            uint8_t mk4_V1 = m % 256;
            int16_t mk4_V2 = m % 32768;
            uint16_t mk4_V3 = m % 65566;
            int32_t mk4_V4 = 4 * m;
            ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT32, &mk4_V5, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 1, GMC_DATATYPE_INT64, &mk4_V6, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 2, GMC_DATATYPE_UINT64, &mk4_V7, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 3, GMC_DATATYPE_INT8, &mk4_V0, sizeof(int8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 4, GMC_DATATYPE_UINT8, &mk4_V1, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 5, GMC_DATATYPE_INT16, &mk4_V2, sizeof(int16_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 6, GMC_DATATYPE_UINT16, &mk4_V3, sizeof(uint16_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetKeyValue(T5_mk, 7, GMC_DATATYPE_INT32, &mk4_V4, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T5, T5_mk);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    GmcNodeFreeKey(T3_mk);
    GmcNodeFreeKey(T5_mk);

    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}
