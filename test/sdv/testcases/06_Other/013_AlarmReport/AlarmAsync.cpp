/* ****************************************************************************
 Description  : 告警上报 异步消息堆积(只能在hpe上测) 告警项测试
 Node      :
     001
服务拉起，创建异步连接，默认通道消息最大个数64，获取异步消息堆积告警数据；异步创建58个label且不接收消息，获取告警数据；消息堆积降至52个，获取告警数据；消息堆积降至46个，获取告警数据；异步删除14个label，消息堆积至60个，接收消息，消息堆积降至40个，获取告警数据；接收剩下的异步消息，获取告警数据。
     002 服务拉起，创建异步连接，默认通道消息最大个数64，正常通过异步接收消息300条，获取异步消息堆积告警数据
     003 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label
且不接收消息，获取异步消息堆积告警数据，接收剩下的异步消息，获取异步消息堆积告警数据。 004
服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label且不接收消息 ，获取异步消息堆积告警数据 005
服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label且不接收消息，然后接收剩下的异步消息，获取告警数据，获取异步消息堆积告警数据。
     006 服务拉起，创建异步连接，修改通道消息最大个数256，正常通过异步接收消息300条，获取异步消息堆积告警数据
     007 服务拉起，创建异步连接，修改通道消息最大个数256，异步创建231个label且不接收消息
，获取异步消息堆积告警数据，接收剩下的异步消息，获取异步消息堆积告警数据。 008
服务拉起，创建异步连接，修改通道消息最大个数256，异步创建231个label且不接收消息 ，获取异步消息堆积告警数据 009
服务拉起，创建异步连接，修改通道消息最大个数256，异步创建231个label且不接收消息，然后接收剩下的异步消息，获取异步消息堆积告警数据。

     010 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步创建1个label且不接收消息，获取异步消息堆积告警数据 011
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步创建vertex成功10次，消息堆积后，异步创建vertex失败10次，比对succ_times和fail_times
     012 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步删除1个label且不接收消息，获取异步消息堆积告警数据 013
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除vertex成功10次，消息堆积后，异步删除vertex失败10次，比对succ_times和fail_times
     014 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步打开1个label且不接收消息，获取异步消息堆积告警数据 015
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步打开vertex成功10次，消息堆积后，异步打开vertex失败10次，比对succ_times和fail_times

     016 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步插入1条记录且不接收消息，获取异步消息堆积告警数据 017
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步插入记录成功10次，消息堆积后，异步插入失败10次，比对succ_times和fail_times
     018 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步删除1条记录且不接收消息，获取异步消息堆积告警数据 019
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除记录成功10次，消息堆积后，异步删除记录失败10次，比对succ_times和fail_times
     020 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步merge
1条记录且不接收消息，获取异步消息堆积告警数据 021
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步merge记录成功10次，消息堆积后，异步merge记录失败10次，比对succ_times和fail_times
     022 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步replace
1条记录且不接收消息，获取异步消息堆积告警数据 023
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步replace成功10次，消息堆积后，异步replace失败10次，比对succ_times和fail_times
     024 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步执行一次batch操作且不接收消息，获取异步消息堆积告警数据 025
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步执行一次batch成功10次，消息堆积后，异步执行一次batch失败10次，比对succ_times和fail_times

     026 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步truncate一次label且不接收消息，获取异步消息堆积告警数据 027
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步异步truncate成功10次，消息堆积后，异步异步truncate失败10次，比对succ_times和fail_times
     028 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步创建namespace且不接收消息，获取异步消息堆积告警数据 029
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步创建namespace成功10次，消息堆积后，异步创建namespace失败10次，比对succ_times和fail_times
     030 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步删除namespace且不接收消息，获取异步消息堆积告警数据 031
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除namespace成功10次，消息堆积后，异步删除namespace失败10次，比对succ_times和fail_times
     032 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步切换namespace且不接收消息，获取异步消息堆积告警数据 033
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步切换namespace成功10次，消息堆积后，异步切换namespace失败10次，比对succ_times和fail_times

     034 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步创建kv表且不接收消息，获取异步消息堆积告警数据 035
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步创建kv表成功10次，消息堆积后，异步创建kv表失败10次，比对succ_times和fail_times
     036 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步删除kv表且不接收消息，获取异步消息堆积告警数据 037
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除kv表成功10次，消息堆积后，异步删除kv表失败10次，比对succ_times和fail_times
     038 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
，然后异步打开kv表且不接收消息，获取异步消息堆积告警数据 039
服务拉起，创建异步连接，默认通道消息最大个数64，正常异步打开kv表成功10次，消息堆积后，异步打开kv表失败10次，比对succ_times和fail_times
     040 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步set
kv表数据且不接收消息，获取异步消息堆积告警数据 041 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步set
kv表数据成功10次，消息堆积后，异步set kv表数据失败10次，比对succ_times和fail_times 042
服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步delete
kv表数据且不接收消息，获取异步消息堆积告警数据 043 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步delete
kv表数据成功10次，消息堆积后，异步delete kv表数据失败10次，比对succ_times和fail_times 044
服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步truncate
kv表且不接收消息，获取异步消息堆积告警数据 045 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步truncate
kv表成功10次，消息堆积后，异步truncate  kv表失败10次，比对succ_times和fail_times

     046 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label
且不接收消息，获取异步消息堆积告警数据，接收剩下的异步消息，获取异步消息堆积告警数据；循环10次，比对告警数据。

 Author       : 黄楚灿 hwx1007418
 Modification :
 Date         :
 node : 取消异步开表 014/015为无效用例 038/039为无效用例
        16004是发送失败，这是正常的，因为一直发送导致发送通道满(euler:DTS2021091712041)
**************************************************************************** */
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "test_delay_sn.h"
#include <iostream>
#include <fstream>
#define DEBUG_PRINT 1
#define BLOCK_DELAY_S 5 // 消息阻塞时延，和hpe性能相关

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
void infoInFile(char *msg)
{
    system("rm -rf check.txt");
    std::ofstream write;
    write.open("check.txt", std::ios::app);
    write << msg << std::endl;
    write.close();
}
double GetOneLineValue(const char *path, const char *cutter1, const char *cutter2)
{
    double res_f;
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "cat %s |awk -F '%s' '{print $2}'|awk -F '%s' '{print $1}'", path, cutter1, cutter2);
    printf("command is %s\n", command);
    system("cat check.txt");
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_INFO, "popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, 64, pf)) {
    };
    pclose(pf);
    AW_FUN_Log(LOG_INFO, "target value = %s", cmdOutput);
    res_f = atof(cmdOutput);
    return res_f;
}
char path[] = "./check.txt";
char actC[] = "value=";
char sucC[] = "succTimes=";
char faiC[] = "failTimes=";
char cutter[] = ",";
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

GmcAlarmIdE AlarmId = GMC_ALARM_ASYNC_CONN_RING;
GmcAlarmDataT test_AlarmData;

class AlarmReportAsync_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret = 0;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void AlarmReportAsync_test::SetUp()
{
    int ret = 0;
    //封装的创建异步连接
    // qemu环境默认值为16K，IOT默认值为64，用例按64进行测试
#if defined (ENV_RTOSV2X) || defined(ENV_RTOSV2)
    int chanRingLen = 64;
    ret = testGmcConnect_setRing(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
#endif

    //建立同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStopHeartbeat(g_conn_async);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "noExisTable", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel no exist------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //刷新告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    AW_CHECK_LOG_BEGIN();
}

void AlarmReportAsync_test::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;

    //断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}
class TestSuit2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

public:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
};
//不包含timeout的create and close epoll
int create_epoll_thread_myself(EpollThreadDataT *epollData = &g_epollData)
{
    if (epollData->isInited) {
 
        return 0;
    }
    sem_init(&epollData->sem, 0, 0);
 
    int ret = pthread_create(&epollData->epollThreadId, NULL, EpollThreadFunc, epollData);
 
    if (ret != 0) {
        const char *errInfo = strerror(errno);
        printf("create epoll thread failed, errmsg = %s\n", errInfo);
        return ret;
    }
    epollData->isInited = true;
    sem_wait(&epollData->sem);
    return 0;
}
int close_epoll_thread_myself()
{
    int ret = 0;
    ret = close_usr_epoll_thread(&g_epollData);
    if (ret != 0) {
        printf("close epoll thread failed, ret = %d\n", ret);
        return ret;
    }

    // ret = close_timeout_epoll_thread();
    // if (ret != 0) {
    //     printf("close_timeout_epoll_thread failed, ret = %d\n", ret);
    //     return ret;
    // }
    return ret;
}
int CheckAlarmData(GmcAlarmDataT *TestAlarmData, const char *detail = NULL)
{
    int ret = 0;
    if ((*TestAlarmData).alarmStatus == GMC_ALARM_STATUS) {
        // activeValue大于0，clearedValue为0
        if (!((*TestAlarmData).activeValue > 0))
            ret = -1;
        EXPECT_GT((*TestAlarmData).activeValue, 0);
    }
    if (detail) {
        if (strcmp((*TestAlarmData).detail, detail) != 0) {
            printf("get str: %s\ncheck str: %s\n", (*TestAlarmData).detail, detail);
            ret = -1;
        }
    }

#ifdef DEBUG_PRINT
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);
#endif
    return ret;
}

void set_VertexProperty_PK(GmcStmtT *stmt, uint64_t i)
{
    int ret = 0;
    uint64_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_localhash(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int8_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_hashcluster(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint8_t f3_value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty(GmcStmtT *stmt, int i, bool bool_value, char *f14_value, bool isupdate)
{
    int ret = 0;
    char f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f1_vaule = i + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f0_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f6_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f9_value = i + 3;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f10_value = i + 4;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float f11_value = i + 5;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f12_value = i + 6;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f13_value = i + 7;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = (i + 8) % 255;  // 2^8
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = (i + 9) % 4095;  // 2^12
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = (i + 10) % 268435455;  // 2^28
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i + 11;  // 2^58
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    // partition字段不允许修改
    if (isupdate == 0) {
        uint8_t f21_value = (12 + i) % 15;
        ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
}

#define TIME_OUT_60S 600
// 001
// 服务拉起，创建异步连接，默认通道消息最大个数64，获取异步消息堆积告警数据；异步创建58个label且不接收消息，获取告警数据；消息堆积降至52个，获取告警数据；消息堆积降至46个，获取告警数据；异步删除14个label，消息堆积至60个，接收消息，消息堆积降至40个，获取告警数据；接收剩下的异步消息，获取告警数据。
//默认最大64
TEST_F(AlarmReportAsync_test, Other_013_002_013)
{
    uint64_t t_recv_times = 0;
    uint64_t timeout = 1000;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }

    //阻塞异步epoll线程,堆积60条消息
    // epoll已接收一条消息，实际阻塞60条
    int data_num = 61;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);

        key_value++;
        value++;
        if ((i == 62) || (i == 68)) {
            memset(&test_AlarmData, 0, sizeof(test_AlarmData));
            ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
            EXPECT_EQ(GMERR_OK, ret);
            if (g_runMode == 1) {
                EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
                EXPECT_EQ(GMC_ALARM_ACTIVE, test_AlarmData.status);
                (void)snprintf(str, sizeof(str), "Alarm of async_msg_pool is active, from server, value=%.2f,"
                    " slient corruption statis succTimes=%d, failTimes=%d.",
                    test_AlarmData.activeValue, test_AlarmData.succTimes, test_AlarmData.failTimes);
                ret = CheckAlarmData(&test_AlarmData, str);
                EXPECT_EQ(0, ret);
            }
        }
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    //消费6条消息,堆积54条消息
    for (int i = 0; i < 7; i++) {
        ret = testWaitAsyncRecv(&data, 1, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT)
            continue;
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);
    //获取告警数据，预期无告警且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    //消费6条消息,堆积48条消息
    for (int i = 0; i < 7; i++) {
        ret = testWaitAsyncRecv(&data, 1, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT)
            continue;
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);
    //获取告警数据，预期告警消除且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_CLEARED){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, 0.8);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        } 
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    //堆积14条消息，阻塞异步epoll线程,堆积62条消息
    for (int i = 0; i < 14; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //消费16条消息,堆积46条消息
    for (int i = 0; i < 17; i++) {
        ret = testWaitAsyncRecv(&data, 1, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT)
            continue;
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);
    //获取告警数据，预期告警激活后消除且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE_CLEARED) {
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else if(test_AlarmData.status == GMC_ALARM_EMPTY){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
    }
        else {
            EXPECT_EQ(GMC_ALARM_CLEARED, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, 0.8);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费46条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 47, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);

    usleep(500000);
    //获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE_CLEARED) {
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else if(test_AlarmData.status == GMC_ALARM_EMPTY){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
    }
        else {
            EXPECT_EQ(GMC_ALARM_CLEARED, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, 0.8);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    printf("recv all message: %lld\n", g_recv_times);
    pthread_mutex_unlock(&g_delayLock);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 002 服务拉起，创建异步连接，默认通道消息最大个数64，正常通过异步接收消息300条，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_014)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //正常异步接收300条消息
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 300; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
        key_value++;
        value++;
    }

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 003 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label
// 且不接收消息，获取异步消息堆积告警数据，接收剩下的异步消息，获取异步消息堆积告警数据。
TEST_F(AlarmReportAsync_test, Other_013_002_015)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积60条消息
    // epoll已接收一条消息，实际阻塞60条
    int data_num = 61;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    //消费12条消息,堆积48条消息
    for (int i = 0; i < 13; i++) {
        ret = testWaitAsyncRecv(&data, 1, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT)
            continue;
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);
    //获取告警数据，预期告警消除且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_CLEARED)
            (void)snprintf(str, sizeof(str), "Alarm of async_msg_pool is cleared, from server, value=%.2f,"
            " slient corruption statis succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.clearedValue, test_AlarmData.succTimes, test_AlarmData.failTimes);
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            (void)snprintf(str, sizeof(str),
            "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        }
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费48条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 49, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 004 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label且不接收消息 ，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_016)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积60条消息
    // epoll已接收一条消息，实际阻塞60条
    int data_num = 61;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费60条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 005
// 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label且不接收消息，然后接收剩下的异步消息，获取告警数据，获取异步消息堆积告警数据。
TEST_F(AlarmReportAsync_test, Other_013_002_017)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char path[] = "./check.txt";
    char cutter1[] = "=";
    char cutter2[] = ",";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积60条消息
    // epoll已接收一条消息，实际阻塞60条
    int data_num = 61;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //消费12条消息,堆积48条消息
    for (int i = 0; i < 13; i++) {
        ret = testWaitAsyncRecv(&data, 1, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT)
            continue;
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);
    //获取告警数据，预期告警消除且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE_CLEARED) {
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else if(test_AlarmData.status == GMC_ALARM_EMPTY){
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_CLEARED, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, cutter1, cutter2);
            sucT = GetOneLineValue(path, cutter1, cutter2);
            faiT = GetOneLineValue(path, cutter1, cutter2);
            EXPECT_LE(actVal, 0.8);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费48条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 49, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}

// 006 服务拉起，创建异步连接，修改通道消息最大个数256，正常通过异步接收消息300条，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_018)
{
    GmcConnT *t_conn_async = NULL;
    GmcStmtT *t_stmt_async = NULL;
    int chanRingLen = 256;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    ret = testGmcConnect_setRing(&t_conn_async, &t_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcDropVertexLabelAsync(t_stmt_async, "noExisTable", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel no exist ring:256------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //刷新告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(t_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //正常异步接收300条消息
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(t_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 300; i++) {
        set_VertexProperty_PK(t_stmt_async, key_value);
        set_VertexProperty_localhash(t_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(t_stmt_async, hashcluster_value);
        set_VertexProperty(t_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback;
        ret = GmcExecuteAsync(t_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
        key_value++;
        value++;
    }

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }

    ret = GmcDropVertexLabelAsync(t_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    ret = testGmcDisconnect(t_conn_async, t_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}
// 007 服务拉起，创建异步连接，修改通道消息最大个数为256，异步创建231个label且不接收消息
// ，获取异步消息堆积告警数据，接收剩下的异步消息，获取异步消息堆积告警数据。
TEST_F(AlarmReportAsync_test, Other_013_002_019)
{
    GmcConnT *t_conn_async = NULL;
    GmcStmtT *t_stmt_async = NULL;
    int chanRingLen = 256;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    ret = testGmcConnect_setRing(&t_conn_async, &t_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcDropVertexLabelAsync(t_stmt_async, "noExisTable", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel no exist ring:256------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //刷新告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(t_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积60条消息
    // epoll已接收一条消息，实际阻塞60条
    int data_num = 251;
    int t_data_num = data_num;
    g_delay_count = BLOCK_DELAY_S;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(t_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(t_stmt_async, key_value);
        set_VertexProperty_localhash(t_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(t_stmt_async, hashcluster_value);
        set_VertexProperty(t_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(t_stmt_async, &insertRequestCtx);
        if ((g_runMode == 0) && (ret == GMERR_REQUEST_TIME_OUT)) {
            t_data_num = i;
            printf("t_data_num:%d\n", t_data_num);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }

        //消费堆积消息
        ret = testWaitAsyncRecv(&data, data_num, RECV_TIMEOUT, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status != GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, data.status);
            EXPECT_EQ(1, data.affectRows);
        }
    }

    //触发服务端更新数据
    ret = testGmcPrepareStmtByLabelName(t_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(t_stmt_async, key_value);
    set_VertexProperty_localhash(t_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(t_stmt_async, hashcluster_value);
    set_VertexProperty(t_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(t_stmt_async, &insertRequestCtx);
    if ((g_runMode == 0) && (ret != GMERR_OK))
        EXPECT_EQ(GMERR_REQUEST_TIME_OUT, ret);
    else
        EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，预期告警消除且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    infoInFile((char *)test_AlarmData.detail);
    if(test_AlarmData.status == GMC_ALARM_ACTIVE_CLEARED){
        actVal = GetOneLineValue(path, actC, cutter);
        sucT = GetOneLineValue(path, sucC, cutter);
        faiT = GetOneLineValue(path, faiC, cutter);
        EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
        EXPECT_LE(sucT, test_AlarmData.succTimes);
        EXPECT_LE(faiT, test_AlarmData.failTimes);
    }
    else if(test_AlarmData.status == GMC_ALARM_CLEARED){
        actVal = GetOneLineValue(path, actC, cutter);
        sucT = GetOneLineValue(path, sucC, cutter);
        faiT = GetOneLineValue(path, faiC, cutter);
        EXPECT_LE(actVal, 0.8);
        EXPECT_LE(sucT, test_AlarmData.succTimes);
        EXPECT_LE(faiT, test_AlarmData.failTimes);
    }
    else {
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        sucT = GetOneLineValue(path, sucC, cutter);
        faiT = GetOneLineValue(path, faiC, cutter);
        EXPECT_LE(sucT, test_AlarmData.succTimes);
        EXPECT_LE(faiT, test_AlarmData.failTimes);
    }
    } else if (g_runMode == 0) {
        ret = testWaitAsyncRecv(&data, t_data_num, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabelAsync(t_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    ret = testGmcDisconnect(t_conn_async, t_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}
// 008 服务拉起，创建异步连接，修改通道消息最大个数256，异步创建231个label且不接收消息 ，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_020)
{
    GmcConnT *t_conn_async = NULL;
    GmcStmtT *t_stmt_async = NULL;
    int chanRingLen = 256;
    int ret = 0;
    double actVal;
    int sucT;
    int faiT;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    ret = testGmcConnect_setRing(&t_conn_async, &t_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcDropVertexLabelAsync(t_stmt_async, "noExisTable", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel no exist ring:256------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //刷新告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(t_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积60条消息
    // epoll已接收一条消息，实际阻塞60条
    int data_num = 251;
    int t_data_num = data_num;
    g_delay_count = BLOCK_DELAY_S;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(t_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(t_stmt_async, key_value);
        set_VertexProperty_localhash(t_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(t_stmt_async, hashcluster_value);
        set_VertexProperty(t_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(t_stmt_async, &insertRequestCtx);
        if ((g_runMode == 0) && (ret == GMERR_REQUEST_TIME_OUT)) {
            t_data_num = i;
            printf("t_data_num:%d\n", t_data_num);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }

        //消费250条消息,堆积0条消息
        ret = testWaitAsyncRecv(&data, 251, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
    } else if (g_runMode == 0) {
        ret = testWaitAsyncRecv(&data, t_data_num, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabelAsync(t_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    ret = testGmcDisconnect(t_conn_async, t_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}
// 009
// 服务拉起，创建异步连接，修改通道消息最大个数256，异步创建231个label且不接收消息，然后接收剩下的异步消息，获取异步消息堆积告警数据。
TEST_F(AlarmReportAsync_test, Other_013_002_021)
{
    GmcConnT *t_conn_async = NULL;
    GmcStmtT *t_stmt_async = NULL;
    int chanRingLen = 256;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    ret = testGmcConnect_setRing(&t_conn_async, &t_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcDropVertexLabelAsync(t_stmt_async, "noExisTable", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel no exist ring:256------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //刷新告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(t_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积250条消息
    // epoll已接收一条消息，实际阻塞250条
    int data_num = 251;
    int t_data_num = data_num;
    g_delay_count = BLOCK_DELAY_S;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(t_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(t_stmt_async, key_value);
        set_VertexProperty_localhash(t_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(t_stmt_async, hashcluster_value);
        set_VertexProperty(t_stmt_async, value, 0, (char *)"string", 0);

        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(t_stmt_async, &insertRequestCtx);
        if ((g_runMode == 0) && (ret == GMERR_REQUEST_TIME_OUT)) {
            t_data_num = i;
            printf("t_data_num:%d\n", t_data_num);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);
    //消费堆积消息
    if (g_runMode == 1) {
        ret = testWaitAsyncRecv(&data, data_num, RECV_TIMEOUT, true);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status != GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, data.status);
            EXPECT_EQ(1, data.affectRows);
        }
    }

    //触发服务端更新数据
    set_VertexProperty_PK(t_stmt_async, key_value);
    set_VertexProperty_localhash(t_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(t_stmt_async, hashcluster_value);
    set_VertexProperty(t_stmt_async, value, 0, (char *)"string", 0);
    printf("------------insert start: update alarm data-------------------\n");
    insertRequestCtx.insertCb = insert_vertex_callback_delay;
    ret = GmcExecuteAsync(t_stmt_async, &insertRequestCtx);
    if ((g_runMode == 0) && (ret != GMERR_OK))
        EXPECT_EQ(GMERR_REQUEST_TIME_OUT, ret);
    else
        EXPECT_EQ(GMERR_OK, ret);
    printf("------------insert end: update alarm data-------------------\n");
    key_value++;
    value++;

    usleep(500000);
    //获取告警数据，预期告警消除且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        infoInFile((char *)test_AlarmData.detail);
        if(test_AlarmData.status == GMC_ALARM_ACTIVE_CLEARED){
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else if(test_AlarmData.status == GMC_ALARM_CLEARED){
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, 0.8);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
    } else if (g_runMode == 0) {
        ret = testWaitAsyncRecv(&data, t_data_num, -1, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabelAsync(t_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    ret = testGmcDisconnect(t_conn_async, t_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}
// 010 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步创建1个label且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_022)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    //异步创建表成功10次
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    for (int j = 0; j < 10; j++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            j, j);
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //同步删除表
    for (int j = 0; j < 20; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
}
// 011
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步创建vertex成功10次，消息堆积后，异步创建vertex失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_023)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    succTimes_1 = test_AlarmData.succTimes;
    //正常异步创建表10次成功
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    for (int j = 0; j < 10; j++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            j, j);
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步创建表10次
    for (int j = 10; j < 20; j++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            j, j);
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //同步删除表
    for (int j = 0; j < 20; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
}
// 012 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步删除1个label且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_024)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    //异步创建表
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    for (int j = 0; j < 10; j++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            j, j);
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    //异步删除表成功10次
    for (int j = 0; j < 10; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcDropVertexLabelAsync(g_stmt_async, vertexLabelName, drop_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //同步删除表
    for (int j = 0; j < 20; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
}
// 013
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除vertex成功10次，消息堆积后，异步删除vertex失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_025)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    //正常异步创建表10次成功
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    for (int j = 0; j < 20; j++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            j, j);
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //正常异步删除表10次成功
    for (int j = 0; j < 10; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcDropVertexLabelAsync(g_stmt_async, vertexLabelName, drop_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        EXPECT_EQ(0, ret);
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步删除表10次
    for (int j = 10; j < 20; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        ret = GmcDropVertexLabelAsync(g_stmt_async, vertexLabelName, drop_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //同步删除表
    for (int j = 0; j < 20; j++) {
        sprintf(vertexLabelName, "testT%d", j);
        GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
}
// 014 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步打开1个label且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_026)
{
#if 0
    int ret=0;
    char *schema = NULL;
    void *label = NULL;
    void *t_label[10] = {NULL};
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    uint64_t key_value;
    uint64_t locahash_value,hashcluster_value,value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData =  &data;

    //异步创建表
    char vertexLabelJson[1024];
    char vertexLabelName[32]="testT1";
    for(int j=0;j<10;j++){
        sprintf(vertexLabelJson,"[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",j,j);
        sprintf(vertexLabelName, "testT%d",j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    //epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s=1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i=0; i<data_num; i++){
        set_VertexProperty_PK(g_stmt_async,key_value);
        set_VertexProperty_localhash(g_stmt_async,locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async,hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string",0);
        insertRequestCtx.insertCb =  insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
/*  待对齐用例是否删除
    //异步打开表成功10次
    for(int j=0;j<10;j++){
        sprintf(vertexLabelName, "testT%d",j);
        ret = testOpenVertexLabelAsync_delay(g_stmt_async, vertexLabelName, &t_label[j], get_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        if(ret != GMERR_OK){
            ret=testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
*/
    sleep(1);//确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    if(g_runMode == 1){
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_ACTIVE, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }  

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    //EXPECT_EQ(GMERR_OK, ret);
    //printf("------------close vertexlabel------------\r\n");
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE);//error code
    if(false == (data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE)){
        printf("data.status: %d\n",data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //同步删除表
    for(int j=0;j<20;j++){
        sprintf(vertexLabelName, "testT%d",j);
        GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
#endif
}
// 015
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步打开vertex成功10次，消息堆积后，异步打开vertex失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_027)
{
#if 0
    unsigned int succTimes_1=0;
    unsigned int succTimes_2=0;
    unsigned int failTimes_1=0;
    unsigned int failTimes_2=0;
    int ret=0;
    char *schema = NULL;
    void *label = NULL;
    void *t_label[20] = {NULL};
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    uint64_t key_value;
    uint64_t locahash_value,hashcluster_value,value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData =  &data;
    

    //正常异步创建表10次成功
    char vertexLabelJson[1024];
    char vertexLabelName[32]="testT1";
    for(int j=0;j<20;j++){
        sprintf(vertexLabelJson,"[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",j,j);
        sprintf(vertexLabelName, "testT%d",j);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);//确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;
/*  待对齐用例是否删除
    //正常异步打开表10次成功
    for(int j=0;j<10;j++){
        sprintf(vertexLabelName, "testT%d",j);
        ret = testOpenVertexLabelAsync_delay(g_stmt_async, vertexLabelName, &t_label[j], get_vertex_label_callback_delay, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        if((ret != GMERR_OK)||(data.status != GMERR_OK)){
            ret=testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
*/
    sleep(1);//确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    //EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n",succTimes_2-succTimes_1);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积64条消息
    //epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s=1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i=0; i<data_num; i++){
        set_VertexProperty_PK(g_stmt_async,key_value);
        set_VertexProperty_localhash(g_stmt_async,locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async,hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string",0);
 
        insertRequestCtx.insertCb =  insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if(ret != GMERR_OK){
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);//rtos:GMERR_INTERNAL_ERROR
            ret=testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);//确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_ACTIVE, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData,);
        EXPECT_EQ(0, ret);
    }  
    failTimes_1 = test_AlarmData.failTimes;

/*  待对齐用例是否删除
    //消息阻塞时，异步打开表10次
    for(int j=10;j<20;j++){
        sprintf(vertexLabelName, "testT%d",j);
        ret = testOpenVertexLabelAsync_delay(g_stmt_async, vertexLabelName, &t_label[j], get_vertex_label_callback_delay, &data);
        EXPECT_EQ(GMERR_OK, ret);
        if(ret != GMERR_OK){
            ret=testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
*/
    sleep(1);//确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }
    failTimes_2 = test_AlarmData.failTimes;

    //EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n",failTimes_2-failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if(g_runMode == 1){     //euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }
    
    printf("receivMaxMessage:%d, send_fail:%d\n",receivMaxMessage,send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n",data.recvNum);

    //EXPECT_EQ(GMERR_OK, ret);
    //printf("------------close vertexlabel------------\r\n");
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE);//error code
    if(false == (data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE)){
        printf("data.status: %d\n",data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //同步删除表
    for(int j=0;j<20;j++){
        sprintf(vertexLabelName, "testT%d",j);
        //GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
#endif
}
// 016 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步插入1条记录且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_028)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 61;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 017
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步插入数据成功10次，消息堆积后，异步插入数据失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_029)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //正常插入10条数据
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步插入数据10次
    for (int i = 0; i < 10; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 018 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步删除1条记录且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_030)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);

        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //异步删除10条记录
    key_value = 1;
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 019
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除记录成功10次，消息堆积后，异步删除记录失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_031)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //正常插入10条数据
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //正常删除10条记录
    key_value = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        key_value++;
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步删除数据10次
    key_value = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 020 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步merge
// 1条记录且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_032)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char path[] = "./check.txt";
    char cutter1[] = "=";
    char cutter2[] = ",";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    //异步merge 10条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.mergeCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        EXPECT_EQ(0, ret);
    }
    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 021
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步merge记录成功10次，消息堆积后，异步merge记录失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_033)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //正常merge 10条数据
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.mergeCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步merge数据10次
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.mergeCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 022 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步replace
// 1条记录且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_034)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    //异步replace 10条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.replaceCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 023
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步replace成功10次，消息堆积后，异步replace失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_035)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //正常replace 10条数据
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.replaceCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步replace数据10次
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.replaceCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("faileTimes: %d\n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }
    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 024 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步执行一次batch操作且不接收消息，获取异步消息堆积告警数据
TEST_F(TestSuit2, Other_013_002_036)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // qemu环境默认值为16K，IOT默认值为64，用例按64进行测试
#if defined (ENV_RTOSV2X) || defined(ENV_RTOSV2)
    int chanRingLen = 64;
    ret = testGmcConnect_setRing(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    //建立同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStopHeartbeat(g_conn_async);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "noExisTable", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel no exist------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //刷新告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);  // batch需要在消息阻塞前申请，行为类似alloc stmt
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    //执行10次batch操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        for (uint32_t j = 0; j < 5; j++) {
            set_VertexProperty_PK(g_stmt_async, key_value);
            set_VertexProperty_localhash(g_stmt_async, locahash_value);
            set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
            set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt_async);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    testEnvClean();
}
// 025
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步执行一次batch成功10次，消息堆积后，异步执行一次batch失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_037)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    double actVal;
    int sucT;
    int faiT;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);  // batch需要在消息阻塞前申请
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次batch操作
    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        for (uint32_t j = 0; j < 5; j++) {
            set_VertexProperty_PK(g_stmt_async, key_value);
            set_VertexProperty_localhash(g_stmt_async, locahash_value);
            set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
            set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt_async);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(5, data.totalNum);
        EXPECT_EQ(5, data.succNum);
    }
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步batch数据10次
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        for (uint32_t j = 0; j < 5; j++) {
            set_VertexProperty_PK(g_stmt_async, key_value);
            set_VertexProperty_localhash(g_stmt_async, locahash_value);
            set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
            set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt_async);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 026 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步truncate一次label且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_038)
{
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次truncate操作
    for (int i = 0; i < 10; i++) {
        ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 027
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步异步truncate成功10次，消息堆积后，异步异步truncate失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_039)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次truncate操作
    for (int i = 0; i < 10; i++) {
        ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testWaitAsyncRecv(&data);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if(test_AlarmData.status = GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，执行10次truncate操作
    for (int i = 0; i < 10; i++) {
        ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 028 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步创建namespace且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_040)
{
    int ret = 0;
    char str[1024] = {};
    char const *usr_name = "userby025";
    char name_space[10][50] = {
        "class_0", "class_1", "class_2", "class_3", "class_4", "class_5", "class_6", "class_7", "class_8", "class_9"};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次create namespace操作
    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除namespace
    for (int i = 0; i < 10; i++) {
        GmcDropNamespace(g_stmt, name_space[i]);
    }
}
// 029
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步创建namespace成功10次，消息堆积后，异步创建namespace失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_041)
{
    char const *usr_name = "userby025";
    char name_space[10][50] = {
        "class_0", "class_1", "class_2", "class_3", "class_4", "class_5", "class_6", "class_7", "class_8", "class_9"};
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次creat namespace操作
    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    //删除namespace
    for (int i = 0; i < 10; i++) {
        GmcDropNamespace(g_stmt, name_space[i]);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步create namespace 10次
    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除namespace
    for (int i = 0; i < 10; i++) {
        GmcDropNamespace(g_stmt, name_space[i]);
    }
}
// 030 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步删除namespace且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_042)
{
    int ret = 0;
    char str[1024] = {};
    char const *usr_name = "userby025";
    char name_space[10][50] = {
        "class_0", "class_1", "class_2", "class_3", "class_4", "class_5", "class_6", "class_7", "class_8", "class_9"};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次drop namespace操作
    for (int i = 0; i < 5; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespaceAsync(g_stmt_async, name_space[i], drop_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 031
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除namespace成功10次，消息堆积后，异步删除namespace失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_043)
{
    char const *usr_name = "userby025";
    char name_space[10][50] = {
        "class_0", "class_1", "class_2", "class_3", "class_4", "class_5", "class_6", "class_7", "class_8", "class_9"};
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次drop namespace操作
    for (int i = 0; i < 5; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        ret = GmcDropNamespaceAsync(g_stmt_async, name_space[i], drop_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步create namespace 10次
    for (int i = 0; i < 5; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespaceAsync(g_stmt_async, name_space[i], drop_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 032 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步切换namespace且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_044)
{
    int ret = 0;
    char str[1024] = {};
    char const *usr_name = "userby025";
    char name_space[10][50] = {
        "class_0", "class_1", "class_2", "class_3", "class_4", "class_5", "class_6", "class_7", "class_8", "class_9"};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testWaitAsyncRecv(&data);
    // EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(true, data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE);//error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //执行10次usr namespace操作
    for (int i = 0; i < 10; i++) {
        ret = GmcUseNamespaceAsync(g_stmt_async, name_space[i], use_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if(test_AlarmData.status = GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息,51 insert +1 dropvertex +10 createnamespace
    ret = testWaitAsyncRecv(&data, 62, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    //删除namespace
    for (int i = 0; i < 10; i++) {
        GmcDropNamespace(g_stmt, name_space[i]);
    }
}
// 033
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步切换namespace成功10次，消息堆积后，异步切换namespace失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_045)
{
    char const *usr_name = "userby025";
    char name_space[10][50] = {
        "class_0", "class_1", "class_2", "class_3", "class_4", "class_5", "class_6", "class_7", "class_8", "class_9"};
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space[i], usr_name, create_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次usr namespace操作
    for (int i = 0; i < 10; i++) {
        ret = GmcUseNamespaceAsync(g_stmt_async, name_space[i], use_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    // usr public
    ret = GmcUseNamespaceAsync(g_stmt_async, g_testNameSpace, use_namespace_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testWaitAsyncRecv(&data);
    // EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(true, data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE);//error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //消息阻塞时，异步usr namespace 10次
    for (int i = 0; i < 10; i++) {
        ret = GmcUseNamespaceAsync(g_stmt_async, name_space[i], use_namespace_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10+1, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106 + 1;  //增加一条删表信息
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    //删除namespace
    for (int i = 0; i < 10; i++) {
        GmcDropNamespace(g_stmt, name_space[i]);
    }
}

// 034 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步创建kv表且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_046)
{
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次create KV table操作
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除 kv table
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvDropTable(g_stmt, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 035
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步创建kv表成功10次，消息堆积后，异步创建kv表失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_047)
{
    char kv_name[128] = "KVTest_0";
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    double actVal;
    int sucT;
    int faiT;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char configJson[128] = "{\"max_record_count\" : 1000}";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次creat kv table操作
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    usleep(500000);
    //删除 KV table
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvDropTable(g_stmt, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;
    //消息阻塞时，异步create kv table 10次
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除 kv table
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvDropTable(g_stmt, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 036 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步删除kv表且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_048)
{
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    // creat kv table
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次 delete KV table操作
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}
// 037
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步删除kv表成功10次，消息堆积后，异步删除kv表失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_049)
{
    char kv_name[128] = "KVTest_0";
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // creat kv table
    for (int i = 0; i < 20; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次 delete kv table操作
    for (int i = 0; i < 10; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < data_num; i++) {
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;
    //消息阻塞时，异步 delete kv table 10次
    for (int i = 10; i < 20; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除 kv table
    for (int i = 0; i < 20; i++) {
        sprintf(kv_name, "KVTest_%d", i);
        GmcKvDropTable(g_stmt, kv_name);
    }
}
// 038 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息
// ，然后异步打开kv表且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_050)
{
#if 0
    int ret=0;
    char kv_name[128] = "KVTest_0";
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char configJson[128] = "{\"max_record_count\" : 1000}";
    uint64_t key_value;
    uint64_t locahash_value,hashcluster_value,value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData =  &data;
    
    //creat kv table
    for(int i=0; i<10; i++){
        sprintf(kv_name, "KVTest_%d",i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    //epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s=1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i=0; i<data_num; i++){
        set_VertexProperty_PK(g_stmt_async,key_value);
        set_VertexProperty_localhash(g_stmt_async,locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async,hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string",0);
        insertRequestCtx.insertCb =  insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次 open KV table操作
    void *kvlable = NULL;
    for(int i=0; i<10; i++){
        sprintf(kv_name, "KVTest_%d",i);
        ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);//确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_ACTIVE, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }  

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE);//error code
    if(false == (data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE)){
        printf("data.status: %d\n",data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除 kv table
    for(int i=0; i<10; i++){
        sprintf(kv_name, "KVTest_%d",i);
        GmcKvDropTable(g_stmt,kv_name);
    }
#endif
}
// 039
// 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步打开kv表成功10次，消息堆积后，异步打开kv表失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_051)
{
#if 0
    char kv_name[128] = "KVTest_0";
    unsigned int succTimes_1=0;
    unsigned int succTimes_2=0;
    unsigned int failTimes_1=0;
    unsigned int failTimes_2=0;
    int ret=0;
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char configJson[128] = "{\"max_record_count\" : 1000}";
    uint64_t key_value;
    uint64_t locahash_value,hashcluster_value,value;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData =  &data;
    
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    //creat kv table
    for(int i=0; i<20; i++){
        sprintf(kv_name, "KVTest_%d",i);
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    usleep(500000);//等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    void *kvlable = NULL;
    //执行10次 open kv table操作
    for(int i=0; i<10; i++){
        sprintf(kv_name, "KVTest_%d",i);
        ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);  
    }

    sleep(1);//确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    //EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n",succTimes_2-succTimes_1);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //阻塞异步epoll线程,堆积64条消息
    //epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s=1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    printf("------------insert start-------------------\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i=0; i<data_num; i++){
        set_VertexProperty_PK(g_stmt_async,key_value);
        set_VertexProperty_localhash(g_stmt_async,locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async,hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string",0);
        insertRequestCtx.insertCb =  insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if(ret != GMERR_OK){
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret=testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        key_value++;
        value++;
    }
    printf("------------insert end-------------------\n");
    sleep(1);//确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_ACTIVE, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }  
    failTimes_1 = test_AlarmData.failTimes;
    //消息阻塞时，异步 open kv table 10次
    for(int i=10; i<20; i++){
        sprintf(kv_name, "KVTest_%d",i);
        ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);//确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData,0,sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMERR_OK, ret);
    if(g_runMode == 1){
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        ret = CheckAlarmData(&test_AlarmData);
        EXPECT_EQ(0, ret);
    }
    failTimes_2 = test_AlarmData.failTimes;

    //EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n",failTimes_2-failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if(g_runMode == 1){     //euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }
    
    printf("receivMaxMessage:%d, send_fail:%d\n",receivMaxMessage,send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n",data.recvNum);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE);//error code
    if(false == (data.status == GMERR_OK ||  data.status == GMERR_UNDEFINED_TABLE)){
        printf("data.status: %d\n",data.status);
    }
    printf("------------drop vertexlabel------------\r\n");

    //删除 kv table
    for(int i=0; i<20; i++){
        sprintf(kv_name, "KVTest_%d",i);
        GmcKvDropTable(g_stmt,kv_name);
    }
#endif
}
// 040 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步set
// kv表数据且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_052)
{
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    void *kvlable = NULL;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    char key[32] = "zhangsan";
    int32_t value = 1;
    double actVal;
    int sucT;
    int faiT;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);

    AsyncUserDataT data = {0};

    // creat kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create kv table------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------get and open kv table------------\r\n");

    printf("------------insert start-------------------\n");
    for (int i = 0; i < data_num; i++) {
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback_delay, &data);
        EXPECT_EQ(GMERR_OK, ret);
        value++;
    }
    printf("------------insert end-------------------\n");

    //执行10次 set KV 操作
    for (int i = 0; i < 10; i++) {
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        value++;
    }
    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    printf("------------close kv table------------\r\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        EXPECT_EQ(0, ret);
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------drop kv table------------\r\n");
}
// 041 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步set kv表数据成功10次，消息堆积后，异步set
// kv表数据失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_053)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    void *kvlable = NULL;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    char key[32] = "zhangsan";
    double actVal;
    int sucT;
    int faiT;
    int32_t value = 1;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);

    AsyncUserDataT data = {0};
    // creat kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create kv table------------\r\n");

    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------get and open kv table------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次 set kv  操作
    for (int i = 0; i < 10; i++) {
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        value++;
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    for (int i = 0; i < data_num; i++) {
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback_delay, &data);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
        value++;
    }
    printf("------------insert end-------------------\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步 set kv table 10次
    for (int i = 0; i < 10; i++) {
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        value++;
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    printf("------------close kv table------------\r\n");

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------drop kv table------------\r\n");
}
// 042 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步delete
// kv表数据且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_054)
{
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    void *kvlable = NULL;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    char key[32] = "object_0";
    int32_t value = 1;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    double actVal;
    int sucT;
    int faiT;
    AsyncUserDataT data = {0};

    // creat kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create kv table------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------get and open kv table------------\r\n");

    printf("------------insert start-------------------\n");
    for (int i = 0; i < data_num; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback_delay, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------insert end-------------------\n");

    //执行10次 delete KV 操作
    for (int i = 0; i < 10; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    printf("------------close kv table------------\r\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------drop kv table------------\r\n");
}
// 043 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步delete kv表数据成功10次，消息堆积后，异步delete
// kv表数据失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_055)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    void *kvlable = NULL;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    char key[32] = "zhangsan";
    double actVal;
    int sucT;
    int faiT;
    int32_t value = 1;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);

    AsyncUserDataT data = {0};
    // creat kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create kv table------------\r\n");

    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------get and open kv table------------\r\n");

    for (int i = 0; i < 10; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次 delete kv  操作
    for (int i = 0; i < 10; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    for (int i = 0; i < data_num; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback_delay, &data);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
    }
    printf("------------insert end-------------------\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步 delete kv table 10次
    for (int i = 0; i < 10; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    printf("------------close kv table------------\r\n");

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------drop kv table------------\r\n");
}
// 044 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建57个label且不接收消息 ，然后异步truncate
// kv表且不接收消息，获取异步消息堆积告警数据
TEST_F(AlarmReportAsync_test, Other_013_002_056)
{
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    void *kvlable = NULL;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    char key[32] = "zhangsan";
    double actVal;
    int sucT;
    int faiT;
    int32_t value = 1;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);

    AsyncUserDataT data = {0};

    // creat kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create kv table------------\r\n");

    //阻塞异步epoll线程,堆积50条消息
    // epoll已接收一条消息，实际阻塞50条
    int data_num = 51;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------get and open kv table------------\r\n");

    printf("------------insert start-------------------\n");
    for (int i = 0; i < data_num; i++) {
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback_delay, &data);
        EXPECT_EQ(GMERR_OK, ret);
        value++;
    }
    printf("------------insert end-------------------\n");

    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    printf("------------close kv table------------\r\n");

    //执行10次 truncate KV 操作
    for (int i = 0; i < 10; i++) {
        ret = GmcKvTruncateTableAsync(g_stmt_async, kv_name, truncate_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，预期告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费61条消息,堆积0条消息
    ret = testWaitAsyncRecv(&data, 61, -1, true);
    EXPECT_EQ(GMERR_OK, ret);

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------drop kv table------------\r\n");
}
// 045 服务拉起，创建异步连接，默认通道消息最大个数64，正常异步truncate  kv表成功10次，消息堆积后，异步truncate
// kv表失败10次，比对succTimes和failTimes
TEST_F(AlarmReportAsync_test, Other_013_002_057)
{
    unsigned int succTimes_1 = 0;
    unsigned int succTimes_2 = 0;
    unsigned int failTimes_1 = 0;
    unsigned int failTimes_2 = 0;
    double actVal;
    int sucT;
    int faiT;
    int ret = 0;
    char str[1024] = {};
    char kv_name[128] = "KVTest_0";
    void *kvlable = NULL;
    char configJson[128] = "{\"max_record_count\" : 1000}";
    char key[32] = "zhangsan";
    int32_t value = 1;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);

    AsyncUserDataT data = {0};

    // creat kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create kv table------------\r\n");

    usleep(500000);  //等待告警平台从服务端获取最新值
    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_1 = test_AlarmData.succTimes;

    //执行10次 truncate kv  操作
    for (int i = 0; i < 10; i++) {
        ret = GmcKvTruncateTableAsync(g_stmt_async, kv_name, truncate_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    sleep(1);  //确保全部消息已发送到服务端

    //获取告警数据，比对succTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
        EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
        (void)snprintf(str, sizeof(str), "Slient corruption statis of async_msg_pool succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            test_AlarmData.succTimes, test_AlarmData.failTimes);
        ret = CheckAlarmData(&test_AlarmData, str);
        EXPECT_EQ(0, ret);
    }
    succTimes_2 = test_AlarmData.succTimes;

    // EXPECT_EQ(10, succTimes_2 - succTimes_1);
    printf("succTimes: %lld \n", succTimes_2 - succTimes_1);

    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------get and open kv table------------\r\n");

    //阻塞异步epoll线程,堆积64条消息
    // epoll已接收一条消息，实际阻塞64条
    int data_num = 96;
    int send_fail = 0;
    g_delay_count = 2;
    g_delay_s = 1;

    pthread_mutex_lock(&g_delayLock);
    g_recv_times = 0;
    pthread_mutex_unlock(&g_delayLock);

    printf("------------insert start-------------------\n");
    for (int i = 0; i < data_num; i++) {
        sprintf(key, "object_%d", i);
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback_delay, &data);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            send_fail++;
        }
    }
    printf("------------insert end-------------------\n");

    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    printf("------------close kv table------------\r\n");

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_1 = test_AlarmData.failTimes;

    //消息阻塞时，异步 truncate kv table 10次
    for (int i = 0; i < 10; i++) {
        ret = GmcKvTruncateTableAsync(g_stmt_async, kv_name, truncate_kv_table_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(1);  //确保全部消息阻塞在服务端

    //获取告警数据，比对failTimes
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    if (g_runMode == 0) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else if (g_runMode == 1) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_runMode == 1) {
        EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
        if (test_AlarmData.status == GMC_ALARM_ACTIVE){
            infoInFile((char *)test_AlarmData.detail);
            actVal = GetOneLineValue(path, actC, cutter);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
        else {
            EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
            infoInFile((char *)test_AlarmData.detail);
            sucT = GetOneLineValue(path, sucC, cutter);
            faiT = GetOneLineValue(path, faiC, cutter);
            EXPECT_LE(sucT, test_AlarmData.succTimes);
            EXPECT_LE(faiT, test_AlarmData.failTimes);
        }
    }
    failTimes_2 = test_AlarmData.failTimes;

    // EXPECT_EQ(10, failTimes_2 - failTimes_1);
    printf("failTimes: %lld \n", failTimes_2 - failTimes_1);

    pthread_mutex_lock(&g_delayLock);
    g_delay_count = 0;
    pthread_mutex_unlock(&g_delayLock);

    //消费64条消息,堆积0条消息
    int receivMaxMessage = 106;
    if (g_runMode == 1) {  // euler上不会堆积消息，全部消息成功，iot/hpe环境消息堆积64条，剩余异步消息失败
        receivMaxMessage -= send_fail;
    }

    printf("receivMaxMessage:%d, send_fail:%d\n", receivMaxMessage, send_fail);
    ret = testWaitAsyncRecv(&data, receivMaxMessage, -1, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    printf("recvNum:%d\n", data.recvNum);

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------drop kv table------------\r\n");
}
// 046
// 服务拉起，创建异步连接，默认通道消息最大个数64，异步创建58个label且不接收消息，获取异步消息堆积告警数据，接收剩下的异步消息，获取异步消息堆积告警数据；循环10次，比对告警数据。
TEST_F(AlarmReportAsync_test, Other_013_002_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char str[1024] = {};
    char *schema = NULL;
    void *label = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    double actVal;
    int sucT;
    int faiT;
    uint64_t key_value;
    uint64_t locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, schema);
    int ret1 = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "losetup -a");
    ret = executeCommand(g_command, "AirEngineX773");
    if (ret == GMERR_OK){
        ret1 = 1;
        AW_FUN_Log(LOG_STEP, "ENV:AP1156");
    }
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(schema);
    printf("------------create vertexlabel------------\r\n");

    int data_num = 61;
    g_delay_count = 2;
    g_delay_s = 1;

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;

    for (int j = 0; j < 3; j++) {
        //阻塞异步epoll线程,堆积60条消息
        // epoll已接收一条消息，实际阻塞60条

        pthread_mutex_lock(&g_delayLock);
        g_recv_times = 0;
        g_delay_count = BLOCK_DELAY_S;
        pthread_mutex_unlock(&g_delayLock);

        printf("------------insert start-------------------\n");
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < data_num; i++) {
            set_VertexProperty_PK(g_stmt_async, key_value);
            set_VertexProperty_localhash(g_stmt_async, locahash_value);
            set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
            set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
            insertRequestCtx.insertCb = insert_vertex_callback_delay;
            ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            key_value++;
            value++;
        }
        printf("------------insert end-------------------\n");

        sleep(1);  //确保全部消息阻塞在服务端

        //获取告警数据，预期告警激活且为告警状态
        memset(&test_AlarmData, 0, sizeof(test_AlarmData));
        ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
#if defined (ENV_RTOSV2X) || defined(ENV_RTOSV2)
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
#endif
        if (g_runMode == 1) {
            AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
            if (test_AlarmData.status == GMC_ALARM_ACTIVE){
                infoInFile((char *)test_AlarmData.detail);
                actVal = GetOneLineValue(path, actC, cutter);
                sucT = GetOneLineValue(path, sucC, cutter);
                faiT = GetOneLineValue(path, faiC, cutter);
                EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
                EXPECT_LE(sucT, test_AlarmData.succTimes);
                EXPECT_LE(faiT, test_AlarmData.failTimes);
            }
            else {
                AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);
                infoInFile((char *)test_AlarmData.detail);
                sucT = GetOneLineValue(path, sucC, cutter);
                faiT = GetOneLineValue(path, faiC, cutter);
                EXPECT_LE(sucT, test_AlarmData.succTimes);
                EXPECT_LE(faiT, test_AlarmData.failTimes);
            }
        }

        pthread_mutex_lock(&g_delayLock);
        g_delay_count = 1;
        pthread_mutex_unlock(&g_delayLock);
        int checkVal = 1;
        //消费12条消息,堆积48条消息
        for (int i = 0; i < 13; i++) {
            ret = testWaitAsyncRecv(&data, 1, -1, true);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (data.status == GMERR_REQUEST_TIME_OUT)
                continue;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            AW_MACRO_EXPECT_EQ_INT(checkVal, data.affectRows);
            
        }

        pthread_mutex_lock(&g_delayLock);
        g_delay_count = BLOCK_DELAY_S;
        pthread_mutex_unlock(&g_delayLock);

        //触发服务端更新数据
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        printf("------------insert start: update alarm data-------------------\n");
        insertRequestCtx.insertCb = insert_vertex_callback_delay;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("------------insert end: update alarm data-------------------\n");
        key_value++;
        value++;
        
        //获取告警数据，预期告警消除且为非告警状态
        sleep(40);
        memset(&test_AlarmData, 0, sizeof(test_AlarmData));
        ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
#if defined (ENV_RTOSV2X) || defined(ENV_RTOSV2)
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
#endif

        pthread_mutex_lock(&g_delayLock);
        g_delay_count = 0;
        pthread_mutex_unlock(&g_delayLock);

        if (g_runMode == 1) {
            AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
            if (test_AlarmData.status == GMC_ALARM_CLEARED){
                infoInFile((char *)test_AlarmData.detail);
                actVal = GetOneLineValue(path, actC, cutter);
                sucT = GetOneLineValue(path, sucC, cutter);
                faiT = GetOneLineValue(path, faiC, cutter);
                EXPECT_LE(actVal, 0.8);
                EXPECT_LE(sucT, test_AlarmData.succTimes);
                EXPECT_LE(faiT, test_AlarmData.failTimes);
            }
            else {
                AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);
                infoInFile((char *)test_AlarmData.detail);
                actVal = GetOneLineValue(path, actC, cutter);
                sucT = GetOneLineValue(path, sucC, cutter);
                faiT = GetOneLineValue(path, faiC, cutter);
                EXPECT_LE(actVal, test_AlarmData.activeValue + 0.01);
                EXPECT_LE(sucT, test_AlarmData.succTimes);
                EXPECT_LE(faiT, test_AlarmData.failTimes);
            }
        }

        //消费48条消息,堆积0条消息
        ret = testWaitAsyncRecv(&data, 49, -1, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if(data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE){
        ret = 0;
    }
    else {
        ret = -1;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  // error code
    if (false == (data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE)) {
        printf("data.status: %d\n", data.status);
    }
    printf("------------drop vertexlabel------------\r\n");
}

