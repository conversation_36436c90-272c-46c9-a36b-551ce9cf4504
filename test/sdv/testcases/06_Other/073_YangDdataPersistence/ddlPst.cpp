/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"
#include "../053_OnDemandPersistence/common/test_shell.h"




class ddlPst : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ddlPst::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));

    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void ddlPst::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
}

void ddlPst::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    const char *namespace1 = "ddlPst";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void ddlPst::TearDown()
{
    const char *namespace1 = "ddlPst";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}


// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}

int flushAndRestartDb_1()
{
    // 落盘
    int ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alloc all stmt
    TestYangAllocAllstmt();

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int userTestNamespace()
{
    const char *namespace1 = "ddlPst";
    memset(&userData, 0, sizeof(AsyncUserDataT));
    int ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    return ret;
}

/*****************************************************************************
 * Description  : 001.contain和list建边，重启后，查询表和边，写入数据并查询
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 130; i < 135; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_001");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002. contain和leaf-list建边，重启后，查询表和边，写入数据并查询
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_002");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 003.list和leaf-list建边，重启后，查询表和边，写入数据并查询
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel3.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_003");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.yang模型包含contain和list和leaf-list建边，重启后，查询表和边，写入数据并查询
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }
    for (int i = 100; i < 105; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_004");
    TransCommit(g_conn_async);

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


const char *g_tmpLabelconfig = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1,
    "persistent":false
})";

/*****************************************************************************
 * Description  : 005.临时表，yang模型包含contain和list和leaf-list建边，重启后，查询表和边，写入数据并查询
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

int dropMyEdgeLable(const char * lableName)
{
    memset(&userData, 0, sizeof(AsyncUserDataT));
    int ret = GmcDropEdgeLabelAsync(g_stmt_async, lableName, drop_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    if (userData.status) {
        return userData.status;
    }

    memset(&userData, 0, sizeof(AsyncUserDataT));
    return ret;
}

int dropMyEdgeLableFailed(const char * lableName)
{
    memset(&userData, 0, sizeof(AsyncUserDataT));
    int ret = GmcDropEdgeLabelAsync(g_stmt_async, lableName, drop_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status) {
        return userData.status;
    }

    memset(&userData, 0, sizeof(AsyncUserDataT));
    return ret;
}

int dropMyVertexLable(const char *lableName)
{
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删点表
    int ret = GmcDropVertexLabelAsync(g_stmt_async, lableName, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    if (userData.status) {
        return userData.status;
    }

    memset(&userData, 0, sizeof(AsyncUserDataT));
    return ret;
}


int dropMyVertexLableFailed(const char *lableName)
{
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删点表
    int ret = GmcDropVertexLabelAsync(g_stmt_async, lableName, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status) {
        return userData.status;
    }

    memset(&userData, 0, sizeof(AsyncUserDataT));
    return ret;
}



/*****************************************************************************
 * Description  : 006.contain和list建边，drop后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删边表
    ret = dropMyEdgeLable("ContainerList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = dropMyEdgeLable("ContainerLeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = dropMyEdgeLable("ListListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 视图数据类型为string bool混合
    char str2[128] = {0};
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerLeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ListListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    // 重建边表
    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }
    for (int i = 100; i < 105; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_006");
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 007.contain和leaf-list建边，写入数据后持久化，drop后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 删边表
    ret = dropMyEdgeLable("ContainerList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyEdgeLable("ContainerLeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyEdgeLable("ListListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删点表
    ret = dropMyVertexLable("ContainerOne");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLable("ListOne");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLable("LeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLable("ListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 视图数据类型为string bool混合
    char str2[128] = {0};
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerLeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ListListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    // 检查点表，预期不存在
    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListOne'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'LeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);


    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.list和leaf-list建边，写入数据后持久化，drop后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel3.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 删边表
    ret = dropMyEdgeLable("ContainerList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyEdgeLable("ContainerListLeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyEdgeLable("ListListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删点表
    ret = dropMyVertexLable("ContainerOne");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLableFailed("ListOne");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLableFailed("LeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLableFailed("ListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 视图数据类型为string bool混合
    char str2[128] = {0};
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerListLeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ListListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    // 检查点表，预期不存在
    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListOne'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'LeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ContainerOne'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);


    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 009.yang模型包含contain和list和leaf-list建边，drop后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }
    for (int i = 100; i < 105; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删边表
    ret = dropMyEdgeLable("ContainerList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyEdgeLable("ContainerLeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyEdgeLable("ListListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删点表
    ret = dropMyVertexLable("ContainerOne");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLable("ListOne");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLable("LeafList");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dropMyVertexLable("ListTwo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 视图数据类型为string bool混合
    char str2[128] = {0};
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ContainerListLeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_EDGE_LABEL_INFO", "EDGE_LABEL_NAME = 'ListListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "EDGE_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    // 检查点表，预期不存在
    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListOne'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'LeafList'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);

    memset(str2, sizeof(str2), 0);
    ret = TestRDScanSysview(g_stmt_sync, "V$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_NAME = 'ListListTwo'");
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewData(g_stmt_sync, 1, "VERTEX_LABEL_NAME", &str2);
    EXPECT_EQ(FAILED, ret);


    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.contain和list建边，写入数据，truncate后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    for (int i = 100; i < 105; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 落盘 争锁10s，争不到
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    EXPECT_NE(GMERR_OK, ret);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_010");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_010");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 011.contain和leaf-list建边，写入数据后持久化，truncate后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // truncate
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_011");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.list和leaf-list建边，写入数据，truncate后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel12.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel12.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // truncate
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_012");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 013.yang模型包含contain和list和leaf-list建边，写入数据，truncate后持久化，重启后，查询表和边
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :
 * *****************************************************************************/
TEST_F(ddlPst, Other_073_ddlPst_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 10; i < 12; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }
    for (int i = 100; i < 105; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafList
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // truncate
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_ddlPst_013");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns ddlPst -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


