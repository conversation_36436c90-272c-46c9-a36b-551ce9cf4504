/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"


class pstBigOjTrans8 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void pstBigOjTrans8::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置pageSize = 8
    ret = ChangeGmserverCfg((char *)"pageSize", (char *)"8");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置hung挂死时间
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void pstBigOjTrans8::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
}

GmcConnT *g_conn_async2 = NULL;
GmcStmtT *g_stmt_async2 = NULL;
GmcStmtT *g_stmt_sync_T02 = NULL;
GmcStmtT *g_stmt_sync_T1List2 = NULL;
GmcStmtT *g_stmt_sync_T2List2 = NULL;
GmcStmtT *g_stmt_sync_LeafList2 = NULL;

int initConnect2()
{
    int ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async2, &g_stmt_sync_T02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async2, &g_stmt_sync_T1List2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async2, &g_stmt_sync_T2List2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async2, &g_stmt_sync_LeafList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

void pstBigOjTrans8::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "pstBigOjTrans8";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // conn2 stmt2
    ret = initConnect2();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void pstBigOjTrans8::TearDown()
{
    const char *namespace1 = "pstBigOjTrans8";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


int userTestNamespace(GmcStmtT *tStmt)
{
    const char *namespace1 = "pstBigOjTrans8";
    memset(&userData, 0, sizeof(AsyncUserDataT));
    int ret = GmcUseNamespaceAsync(tStmt, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    return ret;
}

int g_sumObjSize = 800 * 1024;
int g_fieldCount = 15;
int g_nStrFieldSize = 0;

int transInsert2Start()
{
    int ret = userTestNamespace(g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async2);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async2, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List2, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async2, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List2, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int i = 1; i < g_fieldCount + 1; i++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", i);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    return ret;
}

int trans2Commit()
{
    int ret = 0;

    // 提交事务2，预期失败
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 事务2回滚
    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans rollback error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    return ret;
}

/*****************************************************************************
 * Description  : 001.页大小设置8K，yang表，两个事务并发写入大对象数据 (1M 数据大小)
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize, i, 'd' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 事务2
    ret = transInsert2Start();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    TransCommit(g_conn_async);

    // 事务2提交，预期失败，回滚成功
    ret = trans2Commit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'd' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'd' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 2; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize, i, 'd' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 4; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'd' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}


int transDelete2Start()
{
    int ret = userTestNamespace(g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async2);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async2, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < g_fieldCount + 1; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REMOVE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List2, g_vertexLabelT1, GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async2, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List2, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List2, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List2, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    return ret;
}


/*****************************************************************************
 * Description  : 002.页大小设置8K，yang表，写入大对象数据后，两个事务并发删除
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < g_fieldCount + 1; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);


    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    ret = transDelete2Start();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 1; j < g_fieldCount + 1; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REMOVE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_002");
    TransCommit(g_conn_async);

    // 提交事务2
    ret = trans2Commit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_002");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstBigOjTrans8 -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}



int transDelete3Start()
{
    int ret = userTestNamespace(g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async2);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async2, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 2; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REMOVE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List2, g_vertexLabelT1, GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async2, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List2, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List2, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List2, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    return ret;
}

/*****************************************************************************
 * Description  : 003.页大小设置8K，yang表，写入大对象数据后，一个事务更新，一个事务并发删除
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int i = 1; i < 2; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    ret = transDelete3Start();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 2; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(
            g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'b');
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 提交事务2
    ret = trans2Commit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'c' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}


int transUpdate2Start()
{
    int ret = userTestNamespace(g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async2);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async2, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 2; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(
            g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c');
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List2, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async2, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List2, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List2, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List2, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int i = 1; i < g_fieldCount + 1; i++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", i);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c');
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    return ret;
}

/*****************************************************************************
 * Description  : 004.页大小设置8K，yang表，写入大对象数据后，两个事务并发更新
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    ret = transUpdate2Start();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 2; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(
            g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c');
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 提交事务2
    ret = trans2Commit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'c' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 005.页大小设置8K，yang表，写入大对象数据后，一个事务删除，一个事务并发更新
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    ret = transUpdate2Start();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 提交事务2
    ret = trans2Commit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_005");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_005");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstBigOjTrans8 -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 006.页大小设置8K，yang表，写入事务未提交，刷盘预期失败
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 开启事务2
    ret = transUpdate2Start();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 2; i++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", i);
        ret = TestYangSetNodeStrField(
            g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c');
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        uint32_t keyValue = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_MERGE, fieldName, g_nStrFieldSize, i, 'c' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'c' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

   // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    // 提交事务2
    ret = trans2Commit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'c' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}

const char *g_tmpLabelconfig = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1,
    "persistent":false
})";


/*****************************************************************************
 * Description  : 007.页大小设置8K，临时yang表，写入大对象数据后，重启
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

   // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    // 提交事务
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    TransStart(g_conn_async);

    // 设置批处理,开启diff
    nDmlCnt = 0;
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, g_nStrFieldSize, i, 'd' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObj
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'd' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.002.页大小设置8K，临时yang表，写入小对象数据后，重启
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, 10);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

   // 落盘
    ret = GmcFlushData(g_stmt_sync, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    // 提交事务
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    TransStart(g_conn_async);

    // 设置批处理,开启diff
    nDmlCnt = 0;
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_CREATE, fieldName, 10, 'd' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);


     // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_008");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstBigOjTrans8 -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 009.003.页大小设置8K，临时yang表，record1 1M record2 1M  多次发送2M大对象数据后，重启
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);


    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch1 = NULL;
    ret = testBatchPrepare(g_conn_async, &batch1, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch1, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nBatchCnt = 5;

    for (int seqBatch = 0; seqBatch < nBatchCnt; seqBatch++) {
        nDmlCnt = 0;
        TEST_INFO(">> batch seq: %d \n", seqBatch);

        // 设置批处理,开启diff
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        for (int i = seqBatch; i < seqBatch + 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListOne
            fieldValue = 100 + i;
            testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            for (int j = 1; j < g_fieldCount + 1; j++) {
                char fieldName[10] = {0};
                snprintf(fieldName, sizeof(fieldName), "F%d", j);
                ret = TestYangSetNodeStrField(
                    g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }

        // 提交批处理
        BatchExecute(batch, nDmlCnt, nDmlCnt);

        // 落盘
        ret = GmcFlushData(g_stmt_sync, NULL, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    }

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < nBatchCnt + 1; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch1 = NULL;
    ret = testBatchPrepare(g_conn_async, &batch1, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch1, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    for (int seqBatch = 0; seqBatch < nBatchCnt; seqBatch++) {
        nDmlCnt = 0;
        TEST_INFO(">> batch seq: %d \n", seqBatch);

        // 设置批处理,开启diff
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        for (int i = seqBatch; i < seqBatch + 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListOne
            fieldValue = 100 + i;
            testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            for (int j = 1; j < g_fieldCount + 1; j++) {
                char fieldName[10] = {0};
                snprintf(fieldName, sizeof(fieldName), "F%d", j);
                ret = TestYangSetNodeStrField(
                    g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }

        // 提交批处理
        BatchExecute(batch, nDmlCnt, nDmlCnt);

        // 落盘
        ret = GmcFlushData(g_stmt_sync, NULL, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    }

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < nBatchCnt + 1; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 010.004.页大小设置8K，临时yang表，reord写入小于1M， 发送超过2M大对象数据
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小 
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    for (int i = 0; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作，这里校验通道大小 2M，当前写入单条小于1M
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    TransCommit(g_conn_async);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_010");
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 011.005.页大小设置8K，临时yang表，reord写入超过1M
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小 超过1M
    g_sumObjSize = 1536 * 1024;
    g_fieldCount = 30;
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    for (int i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作，这里校验通道大小 2M，当前写入单条超过1M
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理 -- 单条超过1M，总共小于2M
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, userData.status);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    TransRollback(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_011");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_tmpLabelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_tmpLabelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_011");
    TransCommit(g_conn_async);
}


/*****************************************************************************
 * Description  : 012.027.页大小设置8K，持久化yang表，record1 1M record2 1M  多次发送2M大对象数据后，重启
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);


    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch1 = NULL;
    ret = testBatchPrepare(g_conn_async, &batch1, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch1, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nBatchCnt = 5;

    for (int seqBatch = 0; seqBatch < nBatchCnt; seqBatch++) {
        nDmlCnt = 0;
        TEST_INFO(">> batch seq: %d \n", seqBatch);

        // 设置批处理,开启diff
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        for (int i = seqBatch; i < seqBatch + 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListOne
            fieldValue = 100 + i;
            testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            for (int j = 1; j < g_fieldCount + 1; j++) {
                char fieldName[10] = {0};
                snprintf(fieldName, sizeof(fieldName), "F%d", j);
                ret = TestYangSetNodeStrField(
                    g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }

        // 提交批处理
        BatchExecute(batch, nDmlCnt, nDmlCnt);

        // 落盘
        ret = GmcFlushData(g_stmt_sync, NULL, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    }

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < nBatchCnt + 1; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < nBatchCnt + 1; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 013.028.页大小设置8K，持久化yang表，reord写入小于1M， 发送超过2M大对象数据ss
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小 
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    for (int i = 0; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作，这里校验通道大小 2M，当前写入单条小于1M
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    TransCommit(g_conn_async);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 bigObjls
    TransStart(g_conn_async);
    TestSubtreeFilterBigObjAll(g_stmt_async, "ContainerOne", NULL);
    TransCommit(g_conn_async);

    // 查询校验，临时内存申请偏大
    for (int i = 0; i < 2; i++) {
        if (g_envType != 0) {
            break;
        }

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = CheckReply(fieldName, g_nStrFieldSize, i,  'a' + i + j);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 014.028.页大小设置8K，持久化yang表，reord写入超过1M
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstBigOjTrans8, Other_073_pstBigOjTrans8_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBigObj/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBigObj/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 设置每个字段的size大小 超过1M
    g_sumObjSize = 1536 * 1024;
    g_fieldCount = 30;
    g_nStrFieldSize = g_sumObjSize / g_fieldCount;
    ASSERT_LE(g_nStrFieldSize, 65536);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    for (int j = 1; j < 2; j++) {
        char fieldName[10] = {0};
        snprintf(fieldName, sizeof(fieldName), "F%d", j);
        ret = TestYangSetNodeStrField(g_vertexLabelT0Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    for (int i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        for (int j = 1; j < g_fieldCount + 1; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, sizeof(fieldName), "F%d", j);
            ret = TestYangSetNodeStrField(
                g_vertexLabelT1Node, GMC_YANG_PROPERTY_OPERATION_REPLACE, fieldName, g_nStrFieldSize, i, 'a' + i + j);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加DML操作，这里校验通道大小 2M，当前写入单条超过1M
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理 -- 单条超过1M，总共小于2M，服务端返回报错
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, userData.status);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    TransRollback(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_014");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace(g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstBigOjTrans8_014");
    TransCommit(g_conn_async);
}

