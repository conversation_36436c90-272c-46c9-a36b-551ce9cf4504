/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"


class pstList : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void pstList::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void pstList::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
}

void pstList::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "pstList";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void pstList::TearDown()
{
    const char *namespace1 = "pstList";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


int userTestNamespace()
{
    const char *namespace1 = "pstList";
    memset(&userData, 0, sizeof(AsyncUserDataT));
    int ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    return ret;
}

/*****************************************************************************
 * Description  : 001.contain节点,create节点，create F1,replace F2,merge F3，create contain2,replace choice1后持久化，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_001");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_001");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.list节点,写入数据持久化重启后，replace节点，create F4,replace F2,merge F3，merge contain2,replace choice1持久化，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_002");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_002");
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 800 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F4");

        bool boolValue = true;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_MERGE, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 900;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 10000;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 10010;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_002_2");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_002_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 003.list节点,写入数据持久化重启后，merge节点，create F4,replace F2,merge F3，merge contain2,replace choice1持久化，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_003");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_003");
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 1800 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F4");

        bool boolValue = true;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_MERGE, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 1900;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 20000;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 10010;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_003_2");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_003_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.list节点,写入数据持久化重启后，delete部分record节点，持久化，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_004");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_004");
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_004_2");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_004_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 005.list节点,写入数据持久化重启后，remove部分record节点，持久化，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_005");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_005");
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 4; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_005_2");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_005_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 006.list节点,写入数据持久化重启后，部分record none节点操作，create F4,replace F2,merge F3，delete F5, remove F6， remove contain2, replace contain3，delete choice1，merge choice2持久化，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_006");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_006");
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 1800 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F4");

        // 对子节点字段做delete操作
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做remove操作
        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REMOVE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_MERGE, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 2900;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 20000;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 10010;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_006_2");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_006_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

void InitListProperty(GmcYangListLocatorT *listProperty, GmcYangListPositionE position, GmcPropValueT *referenceKey)
{
    if (listProperty == NULL) {
        return;
    }
    listProperty->position = position;
    if (referenceKey != NULL) {
        listProperty->refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *));
        listProperty->refKeyFields[0] = referenceKey;
        listProperty->refKeyFieldsCount = 1;
    } else {
        listProperty->refKeyFields = NULL;
        listProperty->refKeyFieldsCount = 0;
    }
}

void UninitListProperty(GmcYangListLocatorT *listProperty)
{
    if (listProperty->refKeyFields == NULL) {
        return;
    }
    free(listProperty->refKeyFields);
    listProperty->refKeyFields = NULL;
}

void InitRefKeys(GmcPropValueT *refKey, uint32_t propId, void *value, GmcDataTypeE type = GMC_DATATYPE_UINT32,
    uint32_t sizeValue = 4)
{
    refKey->propertyId = propId;
    refKey->propertyName[0] = '\0';
    refKey->type = type;
    refKey->size = sizeValue;
    refKey->value = value;
}

/*****************************************************************************
 * Description  : 0007.list节点,写入数据持久化重启后，交换节点顺序位置，重启后，subtree查询结果和重启前一致
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_007");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_007");
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = {0};
    snprintf(fieldStr, sizeof(fieldStr), "str00%d", 3);

    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &fieldStr, GMC_DATATYPE_STRING, (strlen(fieldStr)));
    GmcYangListLocatorT listProp;
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_sync_T1List, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    char fieldStr2[8] = {0};
    snprintf(fieldStr2, sizeof(fieldStr2), "str00%d", 6);
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr2, (strlen(fieldStr2)), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 2300;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;
    g_stmt_sync_T1List = NULL;


    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_007_2");
    TransCommit(g_conn_async);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_007_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.list节点,写入数据，不持久化，重启后，查询不到表
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(pstList, Other_073_pstList_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaList/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaList/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F3");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作 --ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_INSERT, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = 100;
        ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = 100;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段做create操作
        bool f4 = true;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_008");
    TransCommit(g_conn_async);


    // 重启
    ret = restartMyDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = userTestNamespace();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_073_pstList_008_2");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns pstList -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

