/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 073
 * Author: hanyang
 * Create: 2024-03-25
 */
#include "BigDataPer.h"

#define BIG_DATA_SIZE 158

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[100] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[100] = {0};

class YangModelPersist_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangModelPersist_test::SetUpTestCase()
{
}

void YangModelPersist_test::TearDownTestCase()
{
}

void YangModelPersist_test::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void YangModelPersist_test::TearDown()
{
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_INTERNAL_ERROR);
    AW_CHECK_LOG_END();
}

void TestReStartServer()
{
    int ret;
    AsyncUserDataT data = {0};

    // 持久化刷盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (uint32_t i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (uint32_t i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (uint32_t i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;

    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestConnectAndCreate()
{
    int ret;
    AsyncUserDataT data = {0};

    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;

    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);
}

void TestDisConnectAndDrop()
{
    int ret;
    AsyncUserDataT data = {0};

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (uint32_t i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (uint32_t i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (uint32_t i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
}

/*****************************************************************************
 Description  : 001.页大小设置8K，contain节点写入大对象数据后，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 002.页大小设置8K，contain节点写入大对象数据持久化重启后，
                replace节点，F1由小对象到大对象，F2由大对象到大对象，F3由大对象到小对象，
                持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置con_2
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置con_3
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

    // 设置con_2
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

    // 设置con_3
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 003.页大小设置8K，contain节点写入大对象数据持久化重启后，
                merge节点，F1由小对象到大对象，F2由大对象到大对象，F3由大对象到小对象，
                持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置con_2
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置con_3
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************MERGE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

    // 设置con_2
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

    // 设置con_3
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 先删除再添加
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE, cycleNum);
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 004.页大小设置8K，contain节点写入大对象数据持久化重启后，
                remove节点，持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 005.页大小设置8K，contain节点写入大对象数据持久化重启后，
                delete节点，持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 006.页大小设置8K，contain节点写入大对象数据持久化重启后，
                none节点操作，create F4,replace F2,merge F3，delete F5, remove F6，
                持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 65536;
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F161");
    fieldValue = 10;
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F002");
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F003");
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE, "F005");
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE, "F006");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 007.页大小设置8K，case节点写入大对象数据后，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 008.页大小设置8K，case节点写入大对象数据持久化重启后，
                replace节点，F1由小对象到大对象，F2由大对象到大对象，F3由大对象到小对象，
                持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置case_2_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置case_3_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_3", GMC_OPERATION_INSERT, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[5], "case_3_1", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

    // 设置case_2_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_2", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 2;
    TestYangSetNodePropertyNum(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

    // 设置case_3_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[5], "case_3_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 009.页大小设置8K，case节点写入大对象数据持久化重启后，
                merge节点，F1由小对象到大对象，F2由大对象到大对象，F3由大对象到小对象，
                持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置case_2_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 设置case_3_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_3", GMC_OPERATION_INSERT, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[5], "case_3_1", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************MERGE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

    // 设置case_2_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

    // 设置case_3_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_3", GMC_OPERATION_MERGE, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[5], "case_3_1", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE / 3;
    TestYangSetNodePropertyNum(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE, cycleNum);
    cycleNum = 1;
    TestYangSetNodePropertyNum(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 010.页大小设置8K，case节点写入大对象数据持久化重启后，
                remove节点，持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 011.页大小设置8K，case节点写入大对象数据持久化重启后，
                delete节点，持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 012.页大小设置8K，case节点写入大对象数据持久化重启后，
                none节点操作，create F4,replace F2,merge F3，delete F5, remove F6，
                持久化后重启，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 100;
    cycleNum = BIG_DATA_SIZE;
    TestYangSetNodePropertyNum(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_1_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    fieldValue = 65536;
    TestYangSetNodePropOneField(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F161");
    fieldValue = 10;
    TestYangSetNodePropOneField(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F002");
    TestYangSetNodePropOneField(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F003");
    TestYangSetNodePropOneField(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE, "F005");
    TestYangSetNodePropOneField(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE, "F006");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 013.页大小设置8K，list节点,create节点，create大对象数据后持久化，
                重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        cycleNum = BIG_DATA_SIZE;
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 014.页大小设置8K，list节点,写入大对象数据持久化重启后，
                replace节点 record1由小数据到大数据，record2由大数据到大数据，record3由大数据到小数据，
                重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i == 1) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        if (i == 3) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 015.页大小设置8K，list节点,写入大对象数据持久化重启后，
                merge节点 record1由小数据到大数据，record2由大数据到大数据，record3由大数据到小数据，
                重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i == 1) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);
        cycleNum = BIG_DATA_SIZE;
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE, cycleNum);
        if (i == 3) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 016.页大小设置8K，list节点,写入大对象数据持久化重启后，
                delete部分record节点，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i == 1) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 017.页大小设置8K，list节点,写入大对象数据持久化重启后，
                remove部分record节点，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i == 1) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 018.页大小设置8K，list节点,写入大对象数据持久化重启后，
                部分record none节点操作，create F4,replace F2,merge F3，delete F5, remove F6持久化，
                重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        cycleNum = BIG_DATA_SIZE;
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);
    fieldValue = 65536;
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F161");
    fieldValue = 10;
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F002");
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F003");
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE, "F005");
    TestYangSetNodePropOneField(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE, "F006");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 019.页大小设置8K，list节点,写入大对象数据持久化重启后，
                交换大对象节点顺序位置，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i == 1) {
            cycleNum = 1;
        } else {
            cycleNum = BIG_DATA_SIZE;
        }
        TestYangSetNodePropertyNum(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, cycleNum);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************merge***********************************/
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    uint32_t keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 020.页大小设置8K，leaf-list节点,create节点，create 大对象数据record1.F1,
                replace大对象数据 record2.F1,merge 大对象数据record3.F1后持久化，
                重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        if (i == 1) {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 523);
        } else if (i == 2) {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, 523);
        } else if (i == 3) {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, 523);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 021.页大小设置8K，leaf-list节点,写入大对象数据持久化重启后，
                replace节点，create record4.F1大对象,replace record1.F1由大对象到小对象,
                merge record2.F1由小对象到大对象，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        if (i == 1) {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 50);
        } else {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 500);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        if (i == 3) {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, 50);
        } else {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, 500);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 022.页大小设置8K，leaf-list节点,写入大对象数据持久化重启后，
                merge节点，create record4.F1大对象,replace record1.F1由大对象到小对象,
                merge record2.F1由大对象到大对象，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        if (i == 1) {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 50);
        } else {
            TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 500);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 3;
    uint32_t superSize = 50;
    char keyValue[10] = {0};
    (void)snprintf(keyValue, 10, "F%04d", fieldValue);
    char superValue[superSize] = {0};
    memcpy(superValue, keyValue, (strlen(keyValue)));
    memset((superValue + (strlen(keyValue))), 'A', (superSize - 1 - (strlen(keyValue))));
    superValue[superSize - 1] = '\0';
    ret = GmcSetIndexKeyValue(g_stmt_list[1], 1, GMC_DATATYPE_STRING, superValue, (superSize - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list[1], g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 023.页大小设置8K，leaf-list节点,写入大对象数据持久化重启后，
                delete部分record节点，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 500);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    uint32_t superSize = 500;
    char keyValue[10] = {0};
    (void)snprintf(keyValue, 10, "F%04d", fieldValue);
    char superValue[superSize] = {0};
    memcpy(superValue, keyValue, (strlen(keyValue)));
    memset((superValue + (strlen(keyValue))), 'A', (superSize - 1 - (strlen(keyValue))));
    superValue[superSize - 1] = '\0';
    ret = GmcSetIndexKeyValue(g_stmt_list[1], 1, GMC_DATATYPE_STRING, superValue, (superSize - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list[1], g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 024.页大小设置8K，leaf-list节点,写入大对象数据持久化重启后，
                remove部分record节点，持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 500);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    uint32_t superSize = 500;
    char keyValue[10] = {0};
    (void)snprintf(keyValue, 10, "F%04d", fieldValue);
    char superValue[superSize] = {0};
    memcpy(superValue, keyValue, (strlen(keyValue)));
    memset((superValue + (strlen(keyValue))), 'A', (superSize - 1 - (strlen(keyValue))));
    superValue[superSize - 1] = '\0';
    ret = GmcSetIndexKeyValue(g_stmt_list[1], 1, GMC_DATATYPE_STRING, superValue, (superSize - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list[1], g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 025.页大小设置8K，leaf-list节点,写入大对象数据持久化重启后，
                部分节点，none节点操作，create record4,replace record2,
                merge record3，delete record5, remove record6持久化，
                重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 500);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    uint32_t superSize = 500;
    char keyValue[10] = {0};
    (void)snprintf(keyValue, 10, "F%04d", fieldValue);
    char superValue[superSize] = {0};
    memcpy(superValue, keyValue, (strlen(keyValue)));
    memset((superValue + (strlen(keyValue))), 'A', (superSize - 1 - (strlen(keyValue))));
    superValue[superSize - 1] = '\0';
    ret = GmcSetIndexKeyValue(g_stmt_list[1], 1, GMC_DATATYPE_STRING, superValue, (superSize - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list[1], g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}

/*****************************************************************************
 Description  : 026.页大小设置8K，leaf-list节点,写入大对象数据持久化重启后，
                交换节点顺序位置持久化，重启后，subtree查询结果和重启前一致
 Author       : hanyang
*****************************************************************************/
TEST_F(YangModelPersist_test, Other_073_Size64_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t cycleNum;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动服务，初始化
    TestModifyCfgAndStartServer64();

    // 创建连接和表
    TestConnectAndCreate();

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PKStr(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 500);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    /***************************merge***********************************/
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置位置
    fieldValue = 3;
    uint32_t superSize = 500;
    char keyValue[10] = {0};
    (void)snprintf(keyValue, 10, "F%04d", fieldValue);
    char superValue[superSize] = {0};
    memcpy(superValue, keyValue, (strlen(keyValue)));
    memset((superValue + (strlen(keyValue))), 'A', (superSize - 1 - (strlen(keyValue))));
    superValue[superSize - 1] = '\0';

    refKey.propertyId = 1;
    refKey.propertyName[0] = '\0';
    refKey.type = GMC_DATATYPE_STRING;
    refKey.size = (superSize - 1);
    refKey.value = superValue;
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 1;
    uint32_t superSize1 = 500;
    char keyValue1[10] = {0};
    (void)snprintf(keyValue1, 10, "F%04d", fieldValue);
    char superValue1[superSize1] = {0};
    memcpy(superValue1, keyValue1, (strlen(keyValue1)));
    memset((superValue1 + (strlen(keyValue1))), 'A', (superSize1 - 1 - (strlen(keyValue1))));
    superValue1[superSize1 - 1] = '\0';
    ret = GmcSetIndexKeyValue(g_stmt_list[1], 1, GMC_DATATYPE_STRING, superValue1, (superSize1 - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list[1], g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务
    TestReStartServer();

    // 清理环境
    TestDisConnectAndDrop();
}
