/*
userPolicyMode = 0
107 全局上限，单个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group1再创建1张失败，删1张再创建1张成功，校验error日志
108 用户配置，单个用户组，cfg的元数据配置10，group1创建Yang表10张成功，group1再创建1张成功
109 全局上限，多个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group2再创建1张失败，group1删1张，group2创建1张成功，校验error日志
110 用户配置，多个用户组，cfg的元数据都配置10，group1创建Yang表10张成功，group2再创建11张成功
111 单用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group1创建10001张时失败，删1张再创1张成功
112 多用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group2创建10001张时失败，group1删1张，group2再创创1张成功

userPolicyMode = 1
113 全局上限，单个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group1再创建1张失败，删1张再创建1张成功，校验error日志
114 用户配置，单个用户组，cfg的元数据配置10，group1创建Yang表10张成功，group1再创建1张成功，校验warn日志
115 全局上限，多个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group2再创建1张失败，group1删1张，group2创建1张成功，校验error日志
116 用户配置，多个用户组，cfg的元数据都配置10，group1创建Yang表10张成功，group2再创建11张成功，校验warn日志
117 单用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group1创建10001张时失败，删1张再创1张成功
118 多用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group2创建10001张时失败，group1删1张，group2再创创1张成功

userPolicyMode = 2
119 全局上限，单个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group1再创建1张失败，删1张再创建1张成功，校验error日志
120 用户配置，单个用户组，cfg的元数据配置10，group1创建Yang表10张成功，group1再创建1张失败，删1张再创建1张成功
121 全局上限，多个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group2再创建1张失败，group1删1张，group2创建1张成功
122 用户配置，cfg的元数据都配置10，group1创建Yang表10张成功，group2再创建10张成功，group2删1张，group1创建1张失败、group2创建1张成功
123 单用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group1创建10001张时失败，删1张再创1张成功
124 多用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group2创建10001张时失败，group1删1张，group2再创创1张成功

*/

#include "GroupCreateMeta.h"

/************************************************ 用户白名单导入导出 ************************************************/
// 导入全局配置白名单group1
void testImportGroupAllA()
{
    int ret;
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 导入全局配置白名单group2
void testImportGroupAllB()
{
    int ret;
    const char *allowListFile = "./userFile/group_all_null_b.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 导入用户配置白名单group1
void testImportGroupTenA()
{
    int ret;
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 导入用户配置白名单group2
void testImportGroupTenB()
{
    int ret;
    const char *allowListFile = "./userFile/group_all_ten_b.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 删除全局配置白名单group1
void testRemoveGroupAllA()
{
    int ret;
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_null.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 删除全局配置白名单group2
void testRemoveGroupAllB()
{
    int ret;
    const char *allowListFile = "./userFile/group_all_null_b.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_all_null_b.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 删除用户配置白名单group1
void testRemoveGroupTenA()
{
    int ret;
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_ten.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

// 删除用户配置白名单group2
void testRemoveGroupTenB()
{
    int ret;
    const char *allowListFile = "./userFile/group_all_ten_b.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_all_ten_b.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
}

class GroupMetaYangModeZero : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GroupMetaYangModeZero::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=0\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GroupMetaYangModeZero::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f ");
}

void GroupMetaYangModeZero::SetUp()
{
    // 建连
    int ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void GroupMetaYangModeZero::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// userPolicyMode = 0
// 107 全局上限，单个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group1再创建1张失败，删1张再创建1张成功，校验error日志
TEST_F(GroupMetaYangModeZero, Other_070_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "删除1张表");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "再创建1张表");
    testCreateYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 5 删除白名单
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_null.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 108 用户配置，单个用户组，cfg的元数据配置10，group1创建Yang表11张成功
TEST_F(GroupMetaYangModeZero, Other_070_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 11);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_ten.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 109 全局上限，多个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group2再创建1张失败，group1删1张，group2创建1张成功，校验error日志
TEST_F(GroupMetaYangModeZero, Other_070_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "groupA建表成功");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "groupB建表失败");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "groupA删表1张");
    testRemoveGroupAllB();
    testImportGroupAllA();
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "groupB建表1张");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 1);

    // 6 删表、删除白名单
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllB();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 110 用户配置，多个用户组，cfg的元数据都配置10，group1创建Yang表10张成功，group2再创建1张成功
TEST_F(GroupMetaYangModeZero, Other_070_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "groupA建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 10);

    AW_FUN_Log(LOG_STEP, "groupB建表");
    testRemoveGroupTenA();
    testImportGroupTenB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 10, 11);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupTenB();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

class GroupMetaYangModeZeroB : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GroupMetaYangModeZeroB::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxYangTableNum=10000\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GroupMetaYangModeZeroB::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f ");
}

void GroupMetaYangModeZeroB::SetUp()
{
    // 建连
    int ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void GroupMetaYangModeZeroB::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 111 单用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group1创建10001张时失败，删1张再创1张成功
TEST_F(GroupMetaYangModeZeroB, Other_070_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 10001);

    AW_FUN_Log(LOG_STEP, "删除1张、再创建1张");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);
    testCreateYang(g_stmt, 0, 1);

    // 5 删表、删白名单
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllA();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 112 多用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group2创建10001张时失败，group1删1张，group2再创创1张成功
TEST_F(GroupMetaYangModeZeroB, Other_070_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "groupA建表成功");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateVertex(g_stmt, 0, 10000);

    AW_FUN_Log(LOG_STEP, "groupB建表失败");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateVertex(g_stmt, 10000, 10001);

    AW_FUN_Log(LOG_STEP, "groupA删表1张");
    testRemoveGroupAllB();
    testImportGroupAllA();
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "groupB建表1张成功");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateVertex(g_stmt, 0, 1);

    // 5 删表、删白名单
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllB();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

class GroupMetaYangModeOne : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GroupMetaYangModeOne::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GroupMetaYangModeOne::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f ");
}

void GroupMetaYangModeOne::SetUp()
{
    // 建连
    int ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void GroupMetaYangModeOne::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// userPolicyMode = 1
// 113 全局上限，单个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group1再创建1张失败，删1张再创建1张成功，校验error日志
TEST_F(GroupMetaYangModeOne, Other_070_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "删除1张表");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "再创建1张表");
    testCreateYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 5 删除白名单
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_null.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 114 用户配置，单个用户组，cfg的元数据配置10，group1创建Yang表10张成功，group1再创建1张成功，校验warn日志
TEST_F(GroupMetaYangModeOne, Other_070_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 11);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_ten.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 115 全局上限，多个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group2再创建1张失败，group1删1张，group2创建1张成功，校验error日志
TEST_F(GroupMetaYangModeOne, Other_070_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "groupA建表成功");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "groupB建表失败");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "groupA删表1张");
    testRemoveGroupAllB();
    testImportGroupAllA();
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "groupB建表1张");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 1);

    // 6 删表、删除白名单
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllB();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 116 用户配置，多个用户组，cfg的元数据都配置10，group1创建Yang表10张成功，group2再创建11张成功，校验warn日志
TEST_F(GroupMetaYangModeOne, Other_070_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "groupA建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 10);

    AW_FUN_Log(LOG_STEP, "groupB建表");
    testRemoveGroupTenA();
    testImportGroupTenB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 10, 11);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupTenB();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

class GroupMetaYangModeOneB : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GroupMetaYangModeOneB::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxYangTableNum=10000\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GroupMetaYangModeOneB::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f ");
}

void GroupMetaYangModeOneB::SetUp()
{
    // 建连
    int ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void GroupMetaYangModeOneB::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 117 单用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group1创建10001张时失败，删1张再创1张成功
TEST_F(GroupMetaYangModeOneB, Other_070_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 10001);

    AW_FUN_Log(LOG_STEP, "删除1张、再创建1张");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);
    testCreateYang(g_stmt, 0, 1);

    // 5 删表、删白名单
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllA();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 118 多用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group2创建10001张时失败，group1删1张，group2再创创1张成功
TEST_F(GroupMetaYangModeOneB, Other_070_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    AW_FUN_Log(LOG_STEP, "groupA建表成功");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    useNameSpace(g_stmt, g_namespace01);
    testCreateVertex(g_stmt, 0, 10000);

    AW_FUN_Log(LOG_STEP, "groupB建表失败");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateVertex(g_stmt, 10000, 10001);

    AW_FUN_Log(LOG_STEP, "groupA删表1张");
    testRemoveGroupAllB();
    testImportGroupAllA();
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "groupB建表1张成功");
    testRemoveGroupAllA();
    testImportGroupAllB();
    useNameSpace(g_stmt, g_namespace01);
    testCreateVertex(g_stmt, 0, 1);

    // 5 删表、删白名单
    testClearNsp(g_stmtAsync, g_namespace01);
    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllB();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

class GroupMetaYangModeTwo : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GroupMetaYangModeTwo::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GroupMetaYangModeTwo::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f ");
}

void GroupMetaYangModeTwo::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void GroupMetaYangModeTwo::TearDown()
{
    AW_CHECK_LOG_END();
}
// userPolicyMode = 2
// 119 全局上限，单个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group1再创建1张失败，删1张再创建1张成功，校验error日志
TEST_F(GroupMetaYangModeTwo, Other_070_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    // 建连
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    // 导入对象权限
    const char *obj_policy_file = "./policyFile/objClear.gmpolicy";
    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "删除1张表");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "再创建1张表");
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "删表");
    useNameSpaceAsync(g_stmtAsync, g_namespace01);
    testClearNsp(g_stmtAsync, g_namespace01);

    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "revoke policy. object privilege success: 1, warning: 0",
        "revoke policy. system privilege success: 0, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 5 删除白名单
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_null.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 120 用户配置，单个用户组，cfg的元数据配置10，group1创建Yang表10张成功，group1再创建1张失败，删1张再创建1张成功
TEST_F(GroupMetaYangModeTwo, Other_070_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_ten.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    // 建连
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    // 导入对象权限
    const char *obj_policy_file = "./policyFile/objClear.gmpolicy";
    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 11);

    AW_FUN_Log(LOG_STEP, "删表");
    useNameSpaceAsync(g_stmtAsync, g_namespace01);
    testClearNsp(g_stmtAsync, g_namespace01);

    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "revoke policy. object privilege success: 1, warning: 0",
        "revoke policy. system privilege success: 0, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_ten.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 121 全局上限，多个用户组，group配置文件元数据不配置，group1创建Yang表至全局上限成功，group2再创建1张失败，group1删1张，group2创建1张成功
TEST_F(GroupMetaYangModeTwo, Other_070_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null_b.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    // 建连
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    // 导入对象权限
    const char *obj_policy_file = "./policyFile/objClear.gmpolicy";
    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 2001);

    AW_FUN_Log(LOG_STEP, "groupA删表1张");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "groupB建表1张");
    ret = system("./modeTwoYang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    useNameSpaceAsync(g_stmtAsync, g_namespace01);
    testClearNsp(g_stmtAsync, g_namespace01);

    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "revoke policy. object privilege success: 1, warning: 0",
        "revoke policy. system privilege success: 0, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_null_b.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 122 用户配置，cfg的元数据都配置10，group1创建Yang表10张成功，group2再创建10张成功，group2删1张，group1创建1张失败、group2创建1张成功
TEST_F(GroupMetaYangModeTwo, Other_070_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_ten_b.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    // 建连
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    // 导入对象权限
    const char *obj_policy_file = "./policyFile/objClear.gmpolicy";
    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 1, 11);

    AW_FUN_Log(LOG_STEP, "groupB建表");
    ret = system("./modeTwoYang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    useNameSpaceAsync(g_stmtAsync, g_namespace01);
    testClearNsp(g_stmtAsync, g_namespace01);

    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "revoke policy. object privilege success: 1, warning: 0",
        "revoke policy. system privilege success: 0, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "userFile/group_yang_all_ten_b.gmuser successfully",
        "remove allowlist, remove db user. success: 0, warning: 0.",
        "remove allowlist, remove db group. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    AW_FUN_Log(LOG_STEP, "test stops.");
}

class GroupMetaYangModeTwoB : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GroupMetaYangModeTwoB::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxYangTableNum=10000\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GroupMetaYangModeTwoB::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f ");
}

void GroupMetaYangModeTwoB::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void GroupMetaYangModeTwoB::TearDown()
{
    AW_CHECK_LOG_END();
}
// 123 单用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group1创建10001张时失败，删1张再创1张成功
TEST_F(GroupMetaYangModeTwoB, Other_070_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;
    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    // 建连
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    // 导入对象权限
    const char *obj_policy_file = "./policyFile/objClear.gmpolicy";
    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 10001);

    AW_FUN_Log(LOG_STEP, "删除1张表");
    useNameSpace(g_stmt, g_namespace01);
    testDropYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "再创建1张表");
    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 0, 1);

    AW_FUN_Log(LOG_STEP, "删表");
    useNameSpaceAsync(g_stmtAsync, g_namespace01);
    testClearNsp(g_stmtAsync, g_namespace01);

    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "revoke policy. object privilege success: 1, warning: 0",
        "revoke policy. system privilege success: 0, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllA();
    AW_FUN_Log(LOG_STEP, "test stops.");
}

// 124 多用户组，修改maxYangTableNum为10000，group1创建Yang表10000张，group2创建10001张时失败，group1删1张，group2再创创1张成功校验error日志
TEST_F(GroupMetaYangModeTwoB, Other_070_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;

    // 1 导入白名单
    const char *allowListFile = "./userFile/group_yang_all_null.gmuser";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    system(g_cmd);
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    // 2 导入必备系统权限 create get drop
    const char *sysPolicyFile = "./policyFile/groupAllPolicy.gmpolicy";
    (void)snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));
    // 建连
    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptionsT;
    connOptionsT.timeoutMs = 1000000;
    ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    createNameSpaceAsync(g_stmtAsync, g_namespace01);
    // 导入对象权限
    const char *obj_policy_file = "./policyFile/objClear.gmpolicy";
    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    useNameSpace(g_stmt, g_namespace01);
    testCreateYang(g_stmt, 1, 10000);

    AW_FUN_Log(LOG_STEP, "groupB建表");
    ret = system("./modeTwoYang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    useNameSpaceAsync(g_stmtAsync, g_namespace01);
    testClearNsp(g_stmtAsync, g_namespace01);

    snprintf(
        g_cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer);
    ret = executeCommand(g_cmd, "revoke policy. object privilege success: 1, warning: 0",
        "revoke policy. system privilege success: 0, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_cmd, 0, sizeof(g_cmd));

    ret = GmcDropNamespace(g_stmt, g_namespace01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testRemoveGroupAllA();
    AW_FUN_Log(LOG_STEP, "test stops.");
}
