/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

/*
%function func1000(a:int4 -> b:int4)
*/

#pragma pack(1)

typedef struct Func {
    int32_t a;
    int32_t b;
} Func;

#pragma pack(0)

int32_t dtl_ext_func_func0000(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0001(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0002(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0003(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0004(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0005(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0006(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0007(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0008(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0009(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0010(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0011(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0012(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0013(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0014(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0015(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0016(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0017(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0018(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0019(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0020(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0021(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0022(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0023(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0024(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0025(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0026(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0027(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0028(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0029(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0030(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0031(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0032(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0033(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0034(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0035(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0036(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0037(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0038(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0039(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0040(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0041(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0042(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0043(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0044(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0045(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0046(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0047(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0048(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0049(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0050(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0051(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0052(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0053(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0054(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0055(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0056(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0057(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0058(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0059(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0060(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0061(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0062(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0063(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0064(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0065(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0066(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0067(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0068(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0069(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0070(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0071(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0072(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0073(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0074(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0075(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0076(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0077(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0078(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0079(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0080(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0081(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0082(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0083(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0084(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0085(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0086(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0087(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0088(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0089(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0090(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0091(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0092(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0093(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0094(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0095(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0096(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0097(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0098(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0099(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0100(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0101(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0102(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0103(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0104(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0105(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0106(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0107(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0108(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0109(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0110(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0111(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0112(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0113(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0114(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0115(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0116(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0117(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0118(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0119(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0120(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0121(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0122(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0123(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0124(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0125(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0126(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0127(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0128(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0129(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0130(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0131(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0132(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0133(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0134(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0135(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0136(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0137(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0138(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0139(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0140(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0141(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0142(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0143(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0144(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0145(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0146(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0147(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0148(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0149(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0150(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0151(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0152(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0153(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0154(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0155(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0156(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0157(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0158(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0159(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0160(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0161(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0162(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0163(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0164(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0165(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0166(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0167(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0168(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0169(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0170(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0171(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0172(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0173(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0174(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0175(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0176(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0177(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0178(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0179(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0180(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0181(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0182(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0183(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0184(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0185(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0186(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0187(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0188(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0189(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0190(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0191(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0192(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0193(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0194(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0195(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0196(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0197(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0198(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0199(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0200(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0201(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0202(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0203(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0204(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0205(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0206(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0207(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0208(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0209(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0210(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0211(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0212(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0213(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0214(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0215(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0216(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0217(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0218(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0219(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0220(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0221(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0222(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0223(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0224(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0225(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0226(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0227(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0228(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0229(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0230(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0231(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0232(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0233(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0234(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0235(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0236(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0237(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0238(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0239(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0240(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0241(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0242(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0243(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0244(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0245(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0246(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0247(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0248(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0249(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0250(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0251(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0252(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0253(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0254(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0255(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0256(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0257(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0258(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0259(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0260(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0261(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0262(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0263(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0264(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0265(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0266(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0267(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0268(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0269(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0270(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0271(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0272(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0273(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0274(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0275(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0276(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0277(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0278(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0279(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0280(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0281(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0282(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0283(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0284(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0285(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0286(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0287(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0288(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0289(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0290(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0291(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0292(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0293(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0294(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0295(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0296(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0297(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0298(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0299(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0300(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0301(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0302(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0303(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0304(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0305(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0306(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0307(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0308(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0309(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0310(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0311(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0312(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0313(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0314(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0315(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0316(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0317(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0318(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0319(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0320(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0321(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0322(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0323(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0324(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0325(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0326(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0327(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0328(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0329(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0330(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0331(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0332(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0333(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0334(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0335(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0336(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0337(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0338(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0339(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0340(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0341(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0342(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0343(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0344(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0345(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0346(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0347(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0348(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0349(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0350(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0351(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0352(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0353(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0354(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0355(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0356(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0357(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0358(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0359(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0360(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0361(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0362(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0363(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0364(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0365(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0366(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0367(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0368(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0369(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0370(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0371(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0372(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0373(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0374(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0375(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0376(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0377(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0378(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0379(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0380(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0381(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0382(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0383(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0384(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0385(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0386(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0387(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0388(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0389(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0390(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0391(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0392(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0393(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0394(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0395(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0396(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0397(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0398(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0399(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0400(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0401(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0402(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0403(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0404(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0405(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0406(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0407(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0408(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0409(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0410(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0411(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0412(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0413(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0414(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0415(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0416(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0417(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0418(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0419(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0420(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0421(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0422(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0423(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0424(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0425(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0426(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0427(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0428(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0429(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0430(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0431(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0432(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0433(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0434(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0435(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0436(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0437(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0438(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0439(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0440(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0441(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0442(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0443(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0444(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0445(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0446(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0447(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0448(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0449(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0450(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0451(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0452(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0453(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0454(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0455(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0456(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0457(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0458(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0459(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0460(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0461(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0462(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0463(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0464(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0465(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0466(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0467(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0468(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0469(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0470(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0471(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0472(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0473(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0474(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0475(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0476(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0477(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0478(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0479(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0480(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0481(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0482(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0483(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0484(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0485(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0486(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0487(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0488(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0489(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0490(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0491(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0492(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0493(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0494(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0495(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0496(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0497(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0498(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0499(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0500(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0501(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0502(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0503(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0504(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0505(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0506(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0507(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0508(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0509(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0510(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0511(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0512(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0513(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0514(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0515(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0516(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0517(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0518(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0519(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0520(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0521(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0522(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0523(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0524(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0525(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0526(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0527(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0528(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0529(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0530(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0531(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0532(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0533(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0534(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0535(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0536(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0537(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0538(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0539(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0540(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0541(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0542(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0543(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0544(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0545(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0546(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0547(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0548(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0549(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0550(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0551(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0552(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0553(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0554(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0555(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0556(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0557(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0558(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0559(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0560(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0561(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0562(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0563(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0564(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0565(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0566(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0567(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0568(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0569(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0570(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0571(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0572(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0573(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0574(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0575(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0576(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0577(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0578(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0579(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0580(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0581(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0582(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0583(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0584(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0585(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0586(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0587(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0588(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0589(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0590(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0591(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0592(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0593(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0594(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0595(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0596(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0597(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0598(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0599(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0600(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0601(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0602(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0603(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0604(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0605(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0606(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0607(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0608(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0609(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0610(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0611(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0612(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0613(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0614(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0615(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0616(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0617(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0618(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0619(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0620(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0621(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0622(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0623(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0624(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0625(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0626(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0627(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0628(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0629(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0630(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0631(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0632(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0633(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0634(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0635(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0636(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0637(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0638(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0639(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0640(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0641(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0642(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0643(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0644(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0645(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0646(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0647(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0648(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0649(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0650(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0651(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0652(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0653(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0654(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0655(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0656(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0657(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0658(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0659(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0660(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0661(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0662(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0663(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0664(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0665(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0666(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0667(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0668(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0669(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0670(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0671(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0672(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0673(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0674(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0675(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0676(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0677(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0678(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0679(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0680(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0681(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0682(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0683(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0684(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0685(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0686(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0687(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0688(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0689(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0690(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0691(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0692(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0693(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0694(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0695(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0696(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0697(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0698(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0699(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0700(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0701(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0702(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0703(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0704(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0705(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0706(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0707(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0708(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0709(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0710(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0711(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0712(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0713(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0714(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0715(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0716(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0717(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0718(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0719(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0720(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0721(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0722(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0723(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0724(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0725(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0726(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0727(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0728(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0729(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0730(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0731(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0732(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0733(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0734(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0735(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0736(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0737(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0738(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0739(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0740(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0741(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0742(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0743(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0744(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0745(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0746(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0747(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0748(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0749(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0750(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0751(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0752(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0753(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0754(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0755(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0756(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0757(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0758(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0759(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0760(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0761(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0762(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0763(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0764(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0765(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0766(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0767(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0768(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0769(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0770(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0771(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0772(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0773(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0774(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0775(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0776(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0777(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0778(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0779(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0780(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0781(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0782(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0783(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0784(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0785(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0786(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0787(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0788(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0789(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0790(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0791(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0792(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0793(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0794(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0795(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0796(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0797(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0798(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0799(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0800(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0801(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0802(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0803(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0804(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0805(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0806(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0807(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0808(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0809(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0810(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0811(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0812(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0813(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0814(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0815(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0816(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0817(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0818(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0819(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0820(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0821(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0822(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0823(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0824(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0825(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0826(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0827(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0828(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0829(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0830(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0831(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0832(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0833(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0834(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0835(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0836(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0837(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0838(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0839(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0840(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0841(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0842(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0843(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0844(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0845(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0846(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0847(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0848(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0849(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0850(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0851(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0852(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0853(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0854(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0855(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0856(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0857(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0858(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0859(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0860(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0861(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0862(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0863(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0864(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0865(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0866(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0867(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0868(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0869(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0870(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0871(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0872(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0873(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0874(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0875(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0876(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0877(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0878(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0879(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0880(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0881(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0882(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0883(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0884(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0885(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0886(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0887(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0888(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0889(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0890(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0891(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0892(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0893(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0894(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0895(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0896(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0897(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0898(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0899(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0900(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0901(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0902(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0903(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0904(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0905(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0906(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0907(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0908(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0909(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0910(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0911(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0912(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0913(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0914(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0915(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0916(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0917(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0918(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0919(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0920(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0921(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0922(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0923(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0924(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0925(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0926(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0927(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0928(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0929(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0930(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0931(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0932(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0933(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0934(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0935(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0936(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0937(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0938(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0939(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0940(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0941(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0942(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0943(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0944(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0945(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0946(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0947(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0948(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0949(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0950(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0951(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0952(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0953(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0954(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0955(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0956(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0957(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0958(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0959(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0960(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0961(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0962(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0963(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0964(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0965(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0966(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0967(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0968(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0969(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0970(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0971(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0972(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0973(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0974(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0975(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0976(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0977(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0978(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0979(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0980(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0981(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0982(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0983(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0984(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0985(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0986(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0987(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0988(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0989(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0990(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func0991(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0992(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0993(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0994(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0995(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0996(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0997(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0998(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func0999(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1000(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1001(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1002(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1003(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1004(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1005(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1006(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1007(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1008(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1009(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1010(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1011(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1012(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1013(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1014(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1015(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1016(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1017(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1018(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1019(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_func1020(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_func1021(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcT = (Func *)tuple;
    funcT->b = funcT->a;

    return GMERR_OK;
}
