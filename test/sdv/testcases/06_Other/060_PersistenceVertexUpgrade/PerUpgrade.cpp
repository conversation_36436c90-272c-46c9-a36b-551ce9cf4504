/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 060_PersistenceVertexUpgrade
 * Author: hanyang
 * Create: 2023-9-20
 */
#include "PerUpgrade.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn1 = NULL;
GmcStmtT *g_stmt1 = NULL;
GmcConnT *g_conn2 = NULL;
GmcStmtT *g_stmt2 = NULL;
GmcConnT *g_conn3 = NULL;
GmcStmtT *g_stmt3 = NULL;

class PerUpgrade : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void PerUpgrade::SetUpTestCase()
{
}

void PerUpgrade::TearDownTestCase()
{
}

void PerUpgrade::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void PerUpgrade::TearDown()
{
    AddWhiteList(GMERR_INTERNAL_ERROR);
    AW_CHECK_LOG_END();
}

void TestReStartServer()
{
    int ret;

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 001.模拟光启表升级基本流程，跨ns数据转化，单ns，单表
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 002.模拟光启表升级基本流程，跨ns数据转化，单ns，多表
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);
    TestCreateLabelTrx02(g_stmt);
    TestCreateLabelTrx03(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);
    TestCreateLabelTrxAfter02(g_stmt);
    TestCreateLabelTrxAfter03(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 003.模拟光启表升级基本流程，跨ns数据转化，多ns，多表
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);
    TestCreateLabelTrx02(g_stmt);
    TestCreateLabelTrx03(g_stmt);

    GmcDropNamespace(g_stmt2, g_namespace2);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt2, g_namespace2, g_namespaceUserName);
    TestUseNamespace(g_stmt2, g_namespace2);
    TestCreateLabelTrx01(g_stmt2);
    TestCreateLabelTrx02(g_stmt2);
    TestCreateLabelTrx03(g_stmt2);

    GmcDropNamespace(g_stmt3, g_namespace3);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt3, g_namespace3, g_namespaceUserName);
    TestUseNamespace(g_stmt3, g_namespace3);
    TestCreateLabelTrx01(g_stmt3);
    TestCreateLabelTrx02(g_stmt3);
    TestCreateLabelTrx03(g_stmt3);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_03");

    TestInsertVertexLabel(g_stmt3, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt3, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt3, times, initValue, "Vertex_03");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_03", expectNum);

    TestScanLabelCount(g_stmt3, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_03", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt, g_namespace1);
    TestUseNamespace(g_stmt2, g_namespace2);
    TestUseNamespace(g_stmt3, g_namespace3);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_03", expectNum);

    TestScanLabelCount(g_stmt3, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_03", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);
    TestCreateLabelTrxAfter02(g_stmt);
    TestCreateLabelTrxAfter03(g_stmt);

    TestCreateNamespace(g_stmt2, g_namespace5, g_namespaceUserName);
    TestUseNamespace(g_stmt2, g_namespace5);
    TestCreateLabelTrxAfter01(g_stmt2);
    TestCreateLabelTrxAfter02(g_stmt2);
    TestCreateLabelTrxAfter03(g_stmt2);

    TestCreateNamespace(g_stmt3, g_namespace6, g_namespaceUserName);
    TestUseNamespace(g_stmt3, g_namespace6);
    TestCreateLabelTrxAfter01(g_stmt3);
    TestCreateLabelTrxAfter02(g_stmt3);
    TestCreateLabelTrxAfter03(g_stmt3);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_03");

    TestInsertVertexLabel(g_stmt3, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt3, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt3, times, initValue, "Vertex_03");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    expectNum = times;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_03", expectNum);

    expectNum = times;
    TestScanLabelCount(g_stmt3, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_03", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace1);

    TestUseNamespace(g_stmt2, g_namespace2);
    TestDropLabel(g_stmt2, "Vertex_01");
    TestDropLabel(g_stmt2, "Vertex_02");
    TestDropLabel(g_stmt2, "Vertex_03");
    TestDropNamespace(g_stmt2, g_namespace2);

    TestUseNamespace(g_stmt3, g_namespace3);
    TestDropLabel(g_stmt3, "Vertex_01");
    TestDropLabel(g_stmt3, "Vertex_02");
    TestDropLabel(g_stmt3, "Vertex_03");
    TestDropNamespace(g_stmt3, g_namespace3);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt, g_namespace4);
    TestUseNamespace(g_stmt2, g_namespace5);
    TestUseNamespace(g_stmt3, g_namespace6);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    expectNum = times;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_03", expectNum);

    expectNum = times;
    TestScanLabelCount(g_stmt3, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt3, "Vertex_03", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace4);

    TestDropLabel(g_stmt2, "Vertex_01");
    TestDropLabel(g_stmt2, "Vertex_02");
    TestDropLabel(g_stmt2, "Vertex_03");
    TestDropNamespace(g_stmt2, g_namespace5);

    TestDropLabel(g_stmt3, "Vertex_01");
    TestDropLabel(g_stmt3, "Vertex_02");
    TestDropLabel(g_stmt3, "Vertex_03");
    TestDropNamespace(g_stmt3, g_namespace6);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 004.模拟光启表升级基本流程，跨ns数据转化，单ns，多表，部分表升级，部分表不变
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);
    TestCreateLabelTrx02(g_stmt);
    TestCreateLabelTrx03(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);
    TestCreateLabelTrx02(g_stmt);
    TestCreateLabelTrxAfter03(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 005.停止持久化后，表升级后，升级后的表写入数据，打开持久化，重启服务，查询数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // DML 新数据
    initValue = 1000;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times * 2;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times * 2;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 006.ns1中原本存在表1，关闭持久化后，新建表2，创建ns2，升级表2，表1不变，
                打开持久化，重启服务，查询数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ns1新建表2
    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrx01(g_stmt);
    TestCreateLabelTrxAfter02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 007.ns1中原本存在表1，关闭持久化后，新建表2，创建ns2，升级表1，表2不变，
                新增表3，打开持久化，新增表4，重启服务，查询数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ns1新建表2
    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);
    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 新增表3
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrx03(g_stmt);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 新增表4
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrx04(g_stmt);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 验证表3和表4是否存在
    uint64_t count;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_03", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_04", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropLabel(g_stmt, "Vertex_04");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 008.循环进行表升级业务，多次升级表，数据量大
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);

    for (uint32_t i = 0; i < 3; i++) {
        // 关闭持久化
        ret = GmcFlushEnable(g_conn, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (i == 0) {
            TestUseNamespace(g_stmt, g_namespace1);

            TestCreateLabelTrx01(g_stmt);
            TestInsertVertexLabelStr(g_stmt, times, initValue, "Vertex_01");
            expectNum = times;
            TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
        } else if (i == 1) {
            TestUseNamespace(g_stmt, g_namespace1);

            TestCreateLabelTrx02(g_stmt);
            TestInsertVertexLabelStr(g_stmt, times, initValue, "Vertex_02");
            expectNum = times;
            TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
        } else {
            TestUseNamespace(g_stmt, g_namespace1);

            TestCreateLabelTrx03(g_stmt);
            TestInsertVertexLabelStr(g_stmt, times, initValue, "Vertex_03");
            expectNum = times;
            TestScanLabelCount(g_stmt, "Vertex_03", expectNum);
        }

        // 打开持久化
        ret = GmcFlushEnable(g_conn, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 触发增量持久化
        ret = GmcFlushData(g_stmt, NULL, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 重启
        TestReStartServer();
    }

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    TestUseNamespace(g_stmt, g_namespace1);

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace1);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 009.配置多区持久化，表升级后，指定不同的分区进行恢复
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    system("mkdir -p $TEST_HOME/data/gmdbnew/; rm -rf $TEST_HOME/data/gmdbnew/*");
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_INFO, "get env PWD fail.\n");
    }

    // 触发增量持久化, 指定新的路径
    char newDbFilePath[1024] = {0};
    (void)sprintf(newDbFilePath, "%s/../../../data/gmdbnew", pwdDir);
    AW_FUN_Log(LOG_INFO, "===pwdDir=====%s\n", pwdDir);
    AW_FUN_Log(LOG_INFO, "====newDbFilePath====%s\n", newDbFilePath);

    ret = GmcFlushData(g_stmt, newDbFilePath, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 010.关闭持久化后，创建ns，进行建表及DML操作，重启服务，
                查询结果还是关闭持久化前的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 新的ns不存在
    ret = GmcUseNamespace(g_stmt, g_namespace4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 011.关闭持久化后，删除ns，进行删表及DML操作，重启服务，
                查询结果还是关闭持久化前的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 012.表升级过程中多次关闭持久化和打开持久化（多个连接），
                最后一次是打开持久化，重启服务后，表升级成功，数据正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 打开持久化
    ret = GmcFlushEnable(g_conn2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 关闭持久化
    ret = GmcFlushEnable(g_conn1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 打开持久化
    ret = GmcFlushEnable(g_conn2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 013.表升级过程中多次关闭持久化，每次关闭后都有DDL和DML操作，
                打开持久化，重启服务后，表升级成功，数据正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestCreateLabelTrx03(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 014.表升级过程中关闭持久化，进行DDL和DML操作，多次打开持久化，
                每次打开操作后都进行DDL和DML操作，重启服务后，表升级成功，数据正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestCreateLabelTrx03(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_03");

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_03", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropLabel(g_stmt, "Vertex_03");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 015.持久化开关打开，调用DB表升级工具返回错误码
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateUpgradLabelTrx(g_stmt);

    int32_t schemaVersion = 3;
    char *expectValue1 = (char *)"upgrade unsuccessfully";
    char *schemaUpFile1 = (char *)"./schema_file/Upgrad_01.gmjson";

    // 升级
    schemaVersion = 3;
    ret = TestUpdateVertexLabelSchema(schemaUpFile1, expectValue1, "Upgrad");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除namespace和表
    TestDropUpgradLabel(g_stmt);
    TestDropNamespace(g_stmt, g_namespace1);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 016.表升级后主键变更，重启后查询结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);

    // 创建表
    TestCreateLabelSchema(g_stmt, "./schema_file/Vertex_01_change_key.gmjson");

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 017.表升级后修改某些字段类型，重启后查询结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);

    // 创建表
    TestCreateLabelSchema(g_stmt, "./schema_file/Vertex_01_change_type.gmjson");

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 018.表升级后从中间删除某些字段类型，重启后查询结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);

    // 创建表
    TestCreateLabelSchema(g_stmt, "./schema_file/Vertex_01_delete.gmjson");

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 019.表升级后表名不同，字段和数据不变，重启后查询结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 020.同一ns下的两个表升级后表名互换，字段和数据也互换，重启后查询结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);
    TestCreateLabelTrx02(g_stmt);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    times = 500;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = 1000;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 500;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = 1000;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 500;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrx01(g_stmt);
    TestCreateLabelTrx02(g_stmt);

    // DML
    times = 500;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    times = 1000;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");

    // scan
    expectNum = 500;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1000;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = 500;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1000;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 021.不同ns下的两个表升级后表名互换，字段和数据也互换，重启后查询结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    GmcDropNamespace(g_stmt2, g_namespace2);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt2, g_namespace2, g_namespaceUserName);
    TestUseNamespace(g_stmt2, g_namespace2);
    TestCreateLabelTrx02(g_stmt2);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    times = 500;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_02");

    // scan
    expectNum = 1000;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    expectNum = 500;
    TestScanLabelCount(g_stmt2, "Vertex_02", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt, g_namespace1);
    TestUseNamespace(g_stmt2, g_namespace2);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = 1000;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    expectNum = 500;
    TestScanLabelCount(g_stmt2, "Vertex_02", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrx02(g_stmt);

    TestCreateNamespace(g_stmt2, g_namespace5, g_namespaceUserName);
    TestUseNamespace(g_stmt2, g_namespace5);
    TestCreateLabelTrx01(g_stmt2);

    // DML
    times = 500;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_02");
    times = 1000;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // scan
    expectNum = 500;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    expectNum = 1000;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    TestUseNamespace(g_stmt2, g_namespace2);
    TestDropLabel(g_stmt2, "Vertex_02");
    TestDropNamespace(g_stmt2, g_namespace2);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt, g_namespace4);
    TestUseNamespace(g_stmt2, g_namespace5);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = 500;
    TestScanLabelCount(g_stmt, "Vertex_02", expectNum);

    expectNum = 1000;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_02");
    TestDropNamespace(g_stmt, g_namespace4);

    TestDropLabel(g_stmt2, "Vertex_01");
    TestDropNamespace(g_stmt2, g_namespace5);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 022.使用悲观可重复读事务写入数据后表升级
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}

/*****************************************************************************
 Description  : 023.打开按需持久化后，重复开启悲观可重复读事务写入数据后表升级
 Author       : hanyang
*****************************************************************************/
TEST_F(PerUpgrade, Other_060_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 环境初始化
    TestModifyCfgAndStartServer();

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 创建namespace和表
    GmcDropNamespace(g_stmt, g_namespace1);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    TestCreateNamespace(g_stmt, g_namespace1, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace1);
    TestCreateLabelTrx01(g_stmt);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace1);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[1]======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 关闭持久化
    ret = GmcFlushEnable(g_conn, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的ns和表
    TestCreateNamespace(g_stmt, g_namespace4, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace4);
    TestCreateLabelTrxAfter01(g_stmt);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 删除旧的ns, 表和数据
    TestUseNamespace(g_stmt, g_namespace1);
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace1);

    // 打开持久化
    ret = GmcFlushEnable(g_conn, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发增量持久化
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启
    TestReStartServer();
    TestUseNamespace(g_stmt, g_namespace4);

    AW_FUN_Log(LOG_STEP, "=====================after server restart[2], vertex upgrade======================");

    // scan
    expectNum = times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 旧的ns已经不存在
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    // 删除namespace和表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropNamespace(g_stmt, g_namespace4);

    // 再次刷盘，避免影响下个用例执行
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 环境清理
    TestStopServerAndClean();
}
