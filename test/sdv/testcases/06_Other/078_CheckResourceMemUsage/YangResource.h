/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

#ifndef YANGRESOURCE_H
#define YANGRESOURCE_H

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcBatchT *batch = NULL;
char *g_schema = NULL;
const char *g_namespace = "namespaceA";
const char *g_namespaceUserName = "abc";
GmcNodeT *conRootNode = NULL;
GmcNodeT *ConConChildNode = NULL;
#define ABSOLUTEPATH "/node1"
GmcTxConfigT g_mSTrxConfig;
char g_lablenamePK[] = "table_pk";
const char *g_config = R"({"max_record_count":1000,"auto_increment":1,"isFastReadUncommitted":0,"yang_model":1})";
static vector<string> expectDiffTreeBase = {"root:create[(priKey(ID:1)),(NULL)]\n"
                                            "root.F0:create(100)\n"
                                            "root.F1:create(100)\n"
                                            "root.F2:create(string)\n"
                                            "root.con_2:create\n"
                                            "con_2.F0:create(1)\n"};
static vector<string> expectDiffTreeReplaceBase = {"root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                                   "root.con_2:update\n"
                                                   "con_2.F0:update(3,1)\n"};
static vector<string> expectDiffTreeReplaceBase1 = {"root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                                    "root.con_2:update\n"
                                                    "con_2.F0:update(2,3)\n"};
static vector<string> expectDiffTreeReplaceBase2 = {"root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                                    "root.con_2:update\n"
                                                    "con_2.F0:update(3,2)\n"};

void ReplaceStr(char *str, const char *find, const char *replace)
{
    char buffer[2048];
    char *insertpoint = &buffer[0];
    const char *tmp = str;
    size_t findlength = strlen(find);
    size_t replacelength = strlen(replace);
    while (1) {
        const char *p = strstr(tmp, find);
        if (p == NULL) {
            strcpy(insertpoint, tmp);
            break;
        }
        memcpy(insertpoint, tmp, p - tmp);
        insertpoint += p - tmp;
        memcpy(insertpoint, replace, replacelength);
        insertpoint += replacelength;

        tmp = p + findlength;
    }
    strcpy(str, buffer);
}
void errorpath_batch_execute_callback(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if (user_data->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
            // 结果检查
            EXPECT_EQ(user_data->expectedErrorCode, msg.errorCode);
            EXPECT_STREQ(user_data->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(user_data->expectedErrPath, msg.errorPath);
        }
    }
}
// 异步启动事务
int start_trans_async(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF,
    GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    AW_YANG_EXPECT_EQ_INT(ret, GMERR_OK, awYangBatchPrepare(conn, batch, batchType, diffType));
    return ret;
}
void TestBatchExecuteAsync(GmcBatchT *batch, AsyncUserDataT data, int num)
{
    int ret = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(num, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
// 异步提交事务
int testTransCommitAsync(GmcConnT *conn_async)
{
    AsyncUserDataT data = {0};
    int ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&data);
    RETURN_IFERR(ret);
    RETURN_IFERR(data.status);
    return ret;
}
int testYangSetNodeField(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}
void testYangSetNodeProperty(GmcNodeT *node, char *value, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    ret = testYangSetNodeField(node, GMC_DATATYPE_FIXED, value, (strlen(value)), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void addDmlOption(GmcStmtT *root, GmcBatchT *batch, GmcOperationTypeE vertexoptType, GmcYangPropOpTypeE fieldopType,
    char *value, bool expect = true)
{
    // 设置根节点
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(root, "node1", vertexoptType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conRootNode = NULL;
    ret = GmcGetRootNode(root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点
    ConConChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "node2", vertexoptType, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    if (expect) {
        testYangSetNodeProperty(ConConChildNode, value, fieldopType);
    } else {
        ret = testYangSetNodeField(ConConChildNode, GMC_DATATYPE_FIXED, value, (strlen(value)), "F1", fieldopType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    }
}
int queryMem(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcClientMemCtxStatInfoT memCtxInfo = {0};
    int ret = GmcGetClientMemCtxAllocSize(conn, stmt, &memCtxInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "memCtxInfo.connMemSize: %d", memCtxInfo.connMemSize);
    AW_FUN_Log(LOG_STEP, "memCtxInfo.stmtMemSize: %d", memCtxInfo.stmtMemSize);
    AW_FUN_Log(LOG_STEP, "memCtxInfo.clientTotalMemSize: %d", memCtxInfo.clientTotalMemSize);
    return memCtxInfo.clientTotalMemSize;
}

void testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, int expstatus = GMERR_OK)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(expstatus, ret);

    uint32_t valueF1 = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(expstatus, ret);

    char valueF2[8] = "string";
    ret = testYangSetNodeField(node, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(expstatus, ret);
}
string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const int *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTree(
    GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t index, uint32_t count, vector<string> &expectReply)
{
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        // cout << "actual diff:\n" << res; // 打印diff数据
        ASSERT_STREQ(StrCmp(expectReply[index + i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}
void TestCheckYangTree1(
    GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t index, uint32_t count, vector<string> &expectReply)
{
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        break;
    }
    return;
}
void TestCheckYangTree2(
    GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t index, uint32_t count, vector<string> &expectReply)
{
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        // cout << "actual diff:\n" << res; // 打印diff数据
        ASSERT_STREQ(StrCmp(expectReply[index + i], res).c_str(), "") << i;
    }
}

void FetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                userData1->recvNum++;
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL, FetchDiff_callback, userData1));
            return;
        }
        userData1->recvNum++;
    }
}
void FetchDiff_ecallback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                userData1->recvNum++;
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL, FetchDiff_callback, userData1));
            return;
        }
        userData1->recvNum++;
    }
}
void FetchDiff_ecallback1(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            int ret = GmcYangFetchRetDeparse(fetchRet, &isEnd, NULL, &count);
            AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            userData1->recvNum++;
            return;
        }
    }
}
void FetchDiff_ecallback2(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            return;
        }
    }
}
void FetchDiff_ecallback3(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
            userData1->recvNum++;
            return;
        }
    }
}
void FetchDiff_ecallback4(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
            TestCheckYangTree1(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                userData1->recvNum++;
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL, FetchDiff_callback, userData1));
            return;
        }
        userData1->recvNum++;
    }
}
void FetchDiff_ecallback5(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        uint32_t idx = userData1->lastExpectIdx;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
            TestCheckYangTree2(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                userData1->recvNum++;
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL, FetchDiff_callback, userData1));
            return;
        }
        userData1->recvNum++;
    }
}
void testFetchAndDeparseDiff(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data, bool FreeFetchRet = true)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = 0;

    if (FreeFetchRet) {
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    } else {
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_ecallback, &data);
    }
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "fetch diff error code:%d\n", ret);
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void testYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void updateDataAndGetDiff(GmcConnT *g_conn_async, GmcStmtT *stmt, GmcBatchT *batch, int value)
{
    AsyncUserDataT userData = {0};
    // 设置批处理并开启diff
    int ret = TestBatchPrepare(g_conn_async, &batch, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *g_root_node = NULL;

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置con_2节点的值
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_MERGE, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetNodeProperty_PK(ConConChildNode, value, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    TestBatchExecuteAsync(batch, userData, 1);
    GmcBatchDestroy(batch);
}
void ModelCheck(GmcStmtT *stmt, bool expect)
{
    int ret = 0;

    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecvOneThread(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    if (expect == true) {
        AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    }
    if (expect == false) {
        AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

int TestTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t i = 0;
        while ((data.status == GMERR_REQUEST_TIME_OUT) && (i < 60)) {
            ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            i++;
            AW_FUN_Log(LOG_DEBUG, "TransRollBack retry times is %d.\n", i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        return ret;
    }
}

#endif
