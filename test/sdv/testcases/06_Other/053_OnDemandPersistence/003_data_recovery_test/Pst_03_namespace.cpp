/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: 按需持久化--持久化恢复 复杂表DDL
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Create: 2023-08-16
 * History:
 */

#include "../Persistence_common.h"

class Pst_03_namespace : public testing::Test {
protected:
    static void SetUpTestCase()
    {  
    }

    static void TearDownTestCase()
    {  
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};
void Pst_03_namespace::SetUp()
{
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));

    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    AW_CHECK_LOG_BEGIN();
}

void Pst_03_namespace::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    system("sh $TEST_HOME/tools/stop.sh -f");
}

// 建namespace，调用GmcFlushData，重启，再次建namespace
TEST_F(Pst_03_namespace, Other_053_003_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新创建namespace
    ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);
    
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 适配光启重复建表报1005000错误
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 建表并删表，调用GmcFlushData，重启，再次建表删表
TEST_F(Pst_03_namespace, Other_053_003_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace，删除
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新创建、删除namespace
    ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 建namespace，并建表写数据，调用GmcFlushData，重启，再次建namespace并删除
TEST_F(Pst_03_namespace, Other_053_003_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    VlComplexRecordCtxT vertexCfg = {
        .opStart = 0,
        .opCount = 1000,
        .startMkVal = 0,
        .childCount = 10,
        .coefficient = 0,
    };
    int len = snprintf(vertexCfg.labelName, sizeof(vertexCfg.labelName), VL_GENERAL_COMPLEX_NAME);
    ASSERT_GT(len, 0);
    ret = VlComplexInsert(g_stmt, vertexCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新创建namespace
    ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}
