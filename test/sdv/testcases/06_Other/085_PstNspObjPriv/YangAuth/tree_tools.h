/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * Description: test
 * Author: pwx860460
 */

#ifndef TREE_TOOLS_H
#define TREE_TOOLS_H

extern "C" {}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
AsyncUserDataT data = {0};
Status ret = 0;
GmcConnT *conn;
GmcStmtT *stmt;
char g_labelName1[] = "OP_T0";
char g_labelName2[] = "DST_T0";
char g_lalableNamePK1[] = "OP_PK";
char g_labelConfigTest[] = "{\"max_record_num\":10000}";
char g_labelName5[] = "multi_process";
char g_tableName[128] = "KV41";
const char *g_configJson = R"({"max_record_count":10,"max_record_count_check":true})";

char *g_testSchema1 = NULL;
char *g_testSchema2 = NULL;
char *g_testSchema3 = NULL;
char *g_testSchema5 = NULL;
const char *g_edgeLabelName = "edgelabel_testEdgeBatchOp";


#define MAX_VERTEX_NUM 10000
char g_namespace1[128] = "nsp";

// 如果同时引用common.h和该头文件, 需要将该文件后置
// common.h里已经定义RETRUN_IFERR和BREAK_IFERR, 为保证用例兼容, 暂不从common.h中移除该定义，如果同时引用该头文件，以该文件为准
#ifdef RETURN_IFERR
#undef RETURN_IFERR
#endif
#ifdef BREAK_IFERR
#undef BREAK_IFERR
#endif

#define TEST_ERROR(log, args...)                                                                \
    do {                                                                                        \
        fprintf(stdout, "Error: pid-%d %s:%d " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)

#define RETURN_IFERR(ret)                                                                                              \
    do {                                                                                                               \
        if ((ret) != 0) {                                                                                              \
            TEST_ERROR(#ret " = %d.", ret);                                                                            \
            return ret;                                                                                                \
        }                                                                                                              \
    } while (0)

#define BREAK_IFERR(ret)                                                                                               \
    {                                                                                                                  \
        if ((ret) != 0) {                                                                                              \
            TEST_ERROR(#ret " = %d.", ret);                                                                            \
            break;                                                                                                     \
        }                                                                                                              \
    }

int TestUpdateVertexLabel(const char *filePath, char *expectValue, const char *labelName)
{
    char *schema = NULL;
    readJanssonFile(filePath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl 工具升级表操作
    char cmd[512] = {0};

    char *uWay = (char *)"online";  // 在线升级
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s  -u %s -ns %s", g_toolPath, labelName, filePath, uWay,
        g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13Value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14Value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13Value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14Value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13Value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14Value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13Value = valueu8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14Value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2Value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value16, f4Value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value_u16, f5_value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6Value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f6Value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(valueu8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9Value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10Value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12Value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13Value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(valueu8);
    AW_MACRO_EXPECT_EQ_INT(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"F15", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &stringValue, 10, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &stringValue, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);
}

void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0Value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2Value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2Value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4Value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value16, f4Value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value_u16, f5_value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6Value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f6Value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(valueu8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9Value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10Value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12Value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13Value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(valueu8);
    AW_MACRO_EXPECT_EQ_INT(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"P15", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &stringValue, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);
}

void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0Value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2Value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2Value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4Value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value16, f4Value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value_u16, f5_value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6Value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f6Value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(valueu8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9Value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10Value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12Value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13Value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(valueu8);
    AW_MACRO_EXPECT_EQ_INT(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"A15", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &stringValue, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);
}

void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{

    int8_t value8 = i % 128;
    uint8_t valueu8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0Value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2Value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2Value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4Value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value16, f4Value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value_u16, f5_value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6Value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f6Value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(valueu8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9Value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10Value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12Value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13Value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(valueu8);
    AW_MACRO_EXPECT_EQ_INT(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"V15", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &stringValue, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &stringValue, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum, int endNum,
    int arrayNum, int vectorNum, const char *labelName)
{
    

    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcInsertVertexBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14Value,
    int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(endNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, successNum);
}

void TestGmcInsertVertexAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum, int endNum,
    int arrayNum, int vectorNum, const char *labelName)
{
    

    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
}

void TestGmcInsertVertexAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14Value,
    int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ;
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.succNum);
}

void TestGmcInsertVertexSuperfiled(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // free
    free(sp1);
}

void TestGmcInsertVertexSuperfiledBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14Value,
    int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(endNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, successNum);
    // free
    free(sp1);
}

void TestGmcInsertVertexSuperfiledAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }

    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // free
    free(sp1);
}

void TestGmcInsertVertexSuperfiledAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14Value, int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ;
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.succNum);
    // free
    free(sp1);
}

void TestGmcUpdateVertexByIndexKey(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcUpdateVertexByIndexKeyBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14Value,
    int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(endNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, successNum);
}

void TestGmcUpdateVertexByIndexKeyAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
}

void TestGmcUpdateVertexByIndexKeyAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14Value, int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ;
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.succNum);
}

void TestGmcUpdateVertexByIndexKeySuperfiled(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // free
    free(sp1);
}

void TestGmcUpdateVertexByIndexKeySuperfiledBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14Value, int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(endNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, successNum);
    // free
    free(sp1);
}

void TestGmcUpdateVertexByIndexKeySuperfiledAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14Value,
    int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    // free
    free(sp1);
}

void TestGmcUpdateVertexByIndexKeySuperfiledAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14Value, int startNum, int endNum, int arrayNum, int vectorNum, const char *labelName, char *keyName)
{
    

    char *sp1 = (char *)malloc(26);
    if (sp1 == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        // set superfiled by name
        char *temp = sp1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp1, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ;
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(endNum, data.succNum);

    // free
    free(sp1);
}

void TestGmcDirectFetchVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum, int endNum,
    int arrayNum, int vectorNum, const char *labelName, const char *keyName, bool read_num)
{
    

    // 读取顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(isFinish, true);
        } else if (read_num == true) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            EXPECT_FALSE(isFinish);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_R(root, i * index, bool_value, f14Value);
            TestGmcNodeGetPropertyByName_p(t1, i * index, bool_value, f14Value);
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 0; j < arrayNum; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, i * index, bool_value, f14Value);
            }
            for (uint32_t j = 0; j < vectorNum; j++) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, i * index, bool_value, f14Value);
            }
        }
    }
}

void TestGmcDirectFetchVertexSuperfiled(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName, const char *keyName, bool read_num)
{
    

    char *sp1Get = (char *)malloc(26);

    // 读取顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(isFinish, true);
        } else if (read_num == true) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            EXPECT_FALSE(isFinish);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_R(root, i * index, bool_value, f14Value);

            // get superfiled by name
            uint32_t length;
            ret = GmcNodeGetSuperfieldSizeByName(t1, (char *)"superfiled0", &length);
            AW_MACRO_EXPECT_EQ_INT((unsigned int)26, length);
            ret = GmcNodeGetSuperfieldByName(t1, (char *)"superfiled0", sp1Get, 26);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char *temp = sp1Get;
            AW_MACRO_EXPECT_EQ_INT(1 * i * index, *(int64_t *)(temp));
            AW_MACRO_EXPECT_EQ_INT(1 * i * index, *(uint64_t *)(temp + 8));
            AW_MACRO_EXPECT_EQ_INT(2 * i * index, *(uint32_t *)(temp + 16));
            AW_MACRO_EXPECT_EQ_INT(3 * i * index, *(int32_t *)(temp + 20));
            AW_MACRO_EXPECT_EQ_INT(4 * i * index, *(int16_t *)(temp + 24));

            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 0; j < arrayNum; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                uint32_t length;
                ret = GmcNodeGetSuperfieldSizeByName(t2, (char *)"superfiled1", &length);
                AW_MACRO_EXPECT_EQ_INT((unsigned int)26, length);
                ret = GmcNodeGetSuperfieldByName(t1, (char *)"superfiled0", sp1Get, 26);
                char *temp = sp1Get;
                AW_MACRO_EXPECT_EQ_INT(1 * i * index, *(int64_t *)(temp));
                AW_MACRO_EXPECT_EQ_INT(1 * i * index, *(uint64_t *)(temp + 8));
                AW_MACRO_EXPECT_EQ_INT(2 * i * index, *(uint32_t *)(temp + 16));
                AW_MACRO_EXPECT_EQ_INT(3 * i * index, *(int32_t *)(temp + 20));
                AW_MACRO_EXPECT_EQ_INT(4 * i * index, *(int16_t *)(temp + 24));
            }

            for (uint32_t j = 0; j < vectorNum; j++) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                uint32_t length;
                ret = GmcNodeGetSuperfieldSizeByName(t3, (char *)"superfiled2", &length);
                AW_MACRO_EXPECT_EQ_INT((unsigned int)26, length);
                ret = GmcNodeGetSuperfieldByName(t3, (char *)"superfiled2", sp1Get, 26);
                char *temp = sp1Get;
                AW_MACRO_EXPECT_EQ_INT(1 * i * index, *(int64_t *)(temp));
                AW_MACRO_EXPECT_EQ_INT(1 * i * index, *(uint64_t *)(temp + 8));
                AW_MACRO_EXPECT_EQ_INT(2 * i * index, *(uint32_t *)(temp + 16));
                AW_MACRO_EXPECT_EQ_INT(3 * i * index, *(int32_t *)(temp + 20));
                AW_MACRO_EXPECT_EQ_INT(4 * i * index, *(int16_t *)(temp + 24));
            }
        }
    }

    free(sp1Get);
}


#endif /* TREE_TOOLS_H */
