/* ****************************************************************************
 Description  : CPU视图 基本功能与复杂场景测试
 Node      :
 01.基本功能
    Other_017_001 服务启动后，不做操作，查看视图信息
    Other_017_002 使用同步方式在MS循环创建和删除顶点表和边表100次，查看视图信息
    Other_017_003 使用同步方式在MS循环创建和删除顶点表和边表100次，再次循环创建和删除顶点表和边表100次，查看视图信息
    Other_017_004 使用同步方式在DS循环创建和删除顶点表和边表100次，查看视图信息
    Other_017_005 使用同步方式在DS的namespace内循环创建和删除DS顶点表和边表100次，查看视图信息
    Other_017_006 使用同步方式循环创建和删除DS 100次，查看视图信息
    Other_017_007 使用同步方式循环创建和销毁资源池100次，查看视图信息
    Other_017_008 使用同步方式循环创建和销毁资源池100次，再循环创建和销毁资源池100次，查看视图信息
    Other_017_009 使用同步方式循环创建、使用、删除namespace 100次，查看视图信息
    Other_017_010 使用同步方式循环创建和删除订阅关系 100次，查看视图信息
    Other_017_011 使用同步方式循环创建、获取、删除path路径 100次，查看视图信息
    Other_017_012 使用同步方式循环truncate表 100次，查看视图信息
    Other_017_013 使用同步方式循环绑定和解绑扩展资源池 100次，查看视图信息
    Other_017_014 使用同步方式循环绑定和解绑资源池到表 100次，查看视图信息
    Other_017_015 使用同步方式循环Get资源池定义100次，查看视图信息
    Other_017_016 使用同步方式开始事务，提交事务100次，查看视图信息
    Other_017_017 使用同步方式开始事务，回滚事务100次，查看视图信息
    Other_017_018 使用同步方式OPEN和CLOSE顶点表100次，查看视图信息
    Other_017_019 使用同步方式OPEN和CLOSE顶点表100次，再次使用同步方式OPEN和CLOSE顶点表100次，查看视图信息
    Other_017_020 使用同步方式OPEN和CLOSE边表100次，查看视图信息
    Other_017_021 使用同步方式，使用主键批量insert、update、delete、merge、replace数据，查看视图信息
    Other_017_022 使用同步方式，insert数据，查看视图信息
    Other_017_023 使用同步方式，主键update数据，查看视图信息
    Other_017_024 使用同步方式，二级索引update数据，查看视图信息
    Other_017_025 使用同步方式，按过滤条件update数据，查看视图信息
    Other_017_026 使用同步方式，主键delete数据，查看视图信息
    Other_017_027 使用同步方式，二级索引delete数据，查看视图信息
    Other_017_028 使用同步方式，按过滤条件delete数据，查看视图信息
    Other_017_029 使用同步方式，全表fetch数据，查看视图信息
    Other_017_030 使用同步方式，通过二级索引fetch数据，查看视图信息
    Other_017_031 使用同步方式，按条件过滤fetch数据，查看视图信息
    Other_017_032 使用同步方式，插入数据，手动建边，手动删边，循环100次，查看视图信息
    Other_017_033 使用同步方式，插入删除数据，查询变更记录数，查看视图信息
    Other_017_034 使用同步方式，插入数据，表关联查询，查看视图信息

 02.复杂场景测试
    Other_017_050 使用同步方式循环创建和删除KV表100次，查看视图信息
    Other_017_051 使用同步方式循环OPEN和CLOSE KV表100次，查看视图信息
    Other_017_052 使用同步方式循环truncate KV表 100次，查看视图信息
    Other_017_053 使用同步方式循环set、isKVexist、get、delete KV表数据100次，查看视图信息
    Other_017_054 使用同步方式循环获取KV count 100次，查看视图信息
    Other_017_055 32层表的插入、更新、删除，查看视图信息
    Other_017_056 数组嵌套表的插入、更新、删除，查看视图信息
    Other_017_057 record下的array数组为1024时，执行插入、更新、删除，查看视图信息
    Other_017_058 record下的vector数组为1024时，执行插入、更新、删除，查看视图信息
    Other_017_059 配置项时间阈值设为-1，创建表，删除表，查看视图信息
    Other_017_060 创建表，插入数据至内存满，查看视图信息
 Author       : 文思奇 wwx1060458
 Modification :
 Date         : 2021/06/23
**************************************************************************** */
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "TreeModel.h"
#include "../../../../../src/common/include/protocol/db_rpc_msg_op.h"  //包含op code的枚举值

#define DEBUG_PRINT 1

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char cond_OpCode[128] = {0};
char const *view_name = "V\\$DRT_LONG_OPERATION_STAT";
char const *view_name_1 = "V\\$DRT_CONN_STAT";

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
int ret = 0;

class CPU_TimeStatistics_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        system("ipcs");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");           //修改阈值
        system("sh $TEST_HOME/tools/start.sh -f ");
        system("ipcs");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CPU_TimeStatistics_test::SetUp()
{
    //建立同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void CPU_TimeStatistics_test::TearDown()
{
    AW_CHECK_LOG_END();
    //断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

int GetPrintBycmd(char *cmd, int *vaule)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
    char cmdOutput[64] = {0};
    while (NULL != fgets(cmdOutput, 64, pf))
        ;
    *vaule = atoi(cmdOutput);
    pclose(pf);
    return 0;
}
void set_VertexProperty_sourceVertex(
    GmcStmtT *stmt, uint32_t pk_value, uint8_t edge_vdata1, uint8_t edge_vdata2, uint8_t edge_vdata3)
{

    uint8_t temp = 1;
    uint32_t V1_f5_value = pk_value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f0_value = edge_vdata1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_BITFIELD8, &V1_f0_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f1_value = edge_vdata2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD8, &V1_f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f2_value = temp;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD8, &V1_f2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f3_value = edge_vdata3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &V1_f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f4_value = temp;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BITFIELD8, &V1_f4_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_dstVertex(
    GmcStmtT *stmt, uint32_t pk_value, uint8_t edge_vdata1, uint8_t edge_vdata2, uint8_t edge_vdata3)
{

    uint8_t temp = 2;
    uint32_t V2_f5_value = pk_value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f0_value = temp;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_BITFIELD8, &V2_f0_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f1_value = edge_vdata2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD8, &V2_f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f2_value = edge_vdata1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD8, &V2_f2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f3_value = temp;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &V2_f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f4_value = edge_vdata3;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BITFIELD8, &V2_f4_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f6_value = temp;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_BITFIELD8, &V2_f6_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_PK(GmcStmtT *stmt, uint64_t i)
{

    uint64_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_localhash(GmcStmtT *stmt, int i)
{

    int8_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_hashcluster(GmcStmtT *stmt, int i)
{

    uint8_t f3_value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty(GmcStmtT *stmt, int i, bool bool_value, char *f14_value, bool isupdate)
{

    char f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f1_vaule = i + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f0_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f6_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f9_value = i + 3;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f10_value = i + 4;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float f11_value = i + 5;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f12_value = i + 6;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f13_value = i + 7;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = (i + 8) % 255;  // 2^8
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = (i + 9) % 4095;  // 2^12
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = (i + 10) % 268435455;  // 2^28
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i + 11;  // 2^58
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    // partition字段不允许修改
    if (isupdate == 0) {
        uint8_t f21_value = (12 + i) % 15;
        ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 001 服务启动后，不做操作，查看视图信息
//执行gmsysview会调用一个open vertexlabel，视图打印后会调用一个scan vertex;
// start.sh 会调用一次gmsysview
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_001)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);

    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 002 使用同步方式在MS循环创建和删除顶点表和边表100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_002)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    for (int i = 0; i < 100; i++) {
        // creat vertexlabel edgelabel
        ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertexlabel edgelabel
        ret = GmcDropEdgeLabel(g_stmt, edge_labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName_2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    free(schema);
    free(schema_2);
    free(edge_schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL, MSG_OP_RPC_DROP_EDGE_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 003 CPU_TimeStatistics_test_003
// 使用同步方式在MS循环创建和删除顶点表和边表100次，再次循环创建和删除顶点表和边表100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_003)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char *delta_json = NULL;
    char *delat_config_json = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);
    readJanssonFile("./schema_file/deltaStore_schema.gmdstore", &delta_json);
    EXPECT_NE((void *)NULL, delta_json);
    readJanssonFile("./schema_file/deltaStore_config.gmjson", &delat_config_json);
    EXPECT_NE((void *)NULL, delat_config_json);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    for (int i = 0; i < 100; i++) {
        // creat vertexlabel edgelabel
        ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertexlabel edgelabel
        ret = GmcDropEdgeLabel(g_stmt, edge_labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName_2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 100; i < 200; i++) {
        // creat vertexlabel edgelabel
        ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertexlabel edgelabel
        ret = GmcDropEdgeLabel(g_stmt, edge_labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName_2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(schema);
    free(schema_2);
    free(edge_schema);
    free(delta_json);
    free(delat_config_json);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL, MSG_OP_RPC_DROP_EDGE_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 007 使用同步方式循环创建和销毁资源池100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_007)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *resPoolTestName = "resource_pool_test";

    const char *ResPoolTest =
        R"({
            "name" : "resource_pool_test",
            "pool_id" : 1,
            "start_id" : 1,
            "capacity" : 1200,
            "order" : 0,
            "alloc_type" : 0
        })";
    for (int i = 0; i < 100; i++) {
        ret = GmcCreateResPool(g_stmt, ResPoolTest);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDestroyResPool(g_stmt, resPoolTestName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_RES_POOL,
        MSG_OP_RPC_DROP_RES_POOL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_RES_POOL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_RES_POOL);
    printf("MSG_OP_RPC_DROP_RES_POOL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_RES_POOL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 008 使用同步方式循环创建和销毁资源池100次，再循环创建和销毁资源池100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_008)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *resPoolTestName = "resource_pool_test";

    const char *ResPoolTest =
        R"({
            "name" : "resource_pool_test",
            "pool_id" : 1,
            "start_id" : 1,
            "capacity" : 1200,
            "order" : 0,
            "alloc_type" : 0
        })";
    for (int i = 0; i < 100; i++) {
        ret = GmcCreateResPool(g_stmt, ResPoolTest);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDestroyResPool(g_stmt, resPoolTestName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 100; i < 200; i++) {
        ret = GmcCreateResPool(g_stmt, ResPoolTest);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDestroyResPool(g_stmt, resPoolTestName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_RES_POOL,
        MSG_OP_RPC_DROP_RES_POOL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_RES_POOL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_RES_POOL);
    printf("MSG_OP_RPC_DROP_RES_POOL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_RES_POOL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 009 使用同步方式循环创建、使用、删除namespace 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_009)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char const *nameSpace = "abc";
    char const *nameSpace_userName = "myname";

    for (int i = 0; i < 100; i++) {
        ret = GmcCreateNamespace(g_stmt, nameSpace, nameSpace_userName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, nameSpace);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, nameSpace);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_NAMESPACE,
        MSG_OP_RPC_DROP_NAMESPACE, MSG_OP_RPC_USE_NAMESPACE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_NAMESPACE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_NAMESPACE);
    printf("MSG_OP_RPC_DROP_NAMESPACE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_NAMESPACE);
    printf("MSG_OP_RPC_USE_NAMESPACE    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_USE_NAMESPACE);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

#define MAX_NAME_LENGTH 128
void sn_callback_checktimes(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
#if 0  //弱校验，只校验推送次数
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcGetSubPushVertexLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //默认推送new object和old object
            switch(info->eventType)
            {
                case GMC_SUB_EVENT_INSERT:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, false);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    //test_checkVertexProperty_sub(subStmt, index);

                    //读old
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, true);
                    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, false);
                    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);

                    //读old
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, true);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    //test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, false);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    //test_checkVertexProperty_sub(subStmt, index);

                    //读old
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, true);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    //test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE:
                {
                    //读new
                    ret = GmcIncSubSetFetchVertexLabel (subStmt, false);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    //test_checkVertexProperty_sub(subStmt, index);

                    //读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcIncSubSetFetchVertexLabel (subStmt, true);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t) , &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcIncSubSetFetchVertexLabel (subStmt, true);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[g_subIndex];
                        //test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                default:
                {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;

        }
        g_subIndex++;
#endif
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
// 010 使用同步方式循环创建和删除订阅关系 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_010)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T20";
    char *sub_info = NULL;
    GmcConnT *g_SubConn = NULL;
    GmcStmtT *g_SubStmt = NULL;
    const char *g_subName = "subVertexLabel";
    char subConnName[] = "subConnName";
    int chanRingLen = 256;

    SnUserDataT *user_data;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    free(user_data);

    //创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅通道
    ret = testSubConnect(&g_SubConn, &g_SubStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    GmcSubConfigT tmp_gSubInfoJson;
    readJanssonFile("schema_file/T20_subinfo_insert.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);

    for (int i = 0; i < 100; i++) {
        //创建订阅
        tmp_gSubInfoJson.subsName = g_subName;
        tmp_gSubInfoJson.configJson = sub_info;
        ret = GmcSubscribe(g_stmt, &tmp_gSubInfoJson, g_SubConn, sn_callback_checktimes, user_data);
        EXPECT_EQ(GMERR_OK, ret);
        //取消订阅
        ret = GmcUnSubscribe(g_stmt, g_subName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(sub_info);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_SubConn);
    EXPECT_EQ(GMERR_OK, ret);
    //删除epoll监听线程
    ret = close_epoll_thread();
    EXPECT_EQ(0, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_HEARTBEAT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_SUBSCRIPTION,
        MSG_OP_RPC_DROP_SUBSCRIPTION};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_HEARTBEAT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_HEARTBEAT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_SUBSCRIPTION : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_SUBSCRIPTION);
    printf("MSG_OP_RPC_DROP_SUBSCRIPTION   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_SUBSCRIPTION);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 011 使用同步方式循环创建、获取、删除path路径 100次，查看视图信息
//下个迭代会使用该接口，本轮迭代该用例暂不测试和上架
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_011)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    // creat vertexlabel edgelabel
    ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(schema);
    free(schema_2);
    free(edge_schema);

    // drop vertexlabel edgelabel
    ret = GmcDropEdgeLabel(g_stmt, edge_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName_2);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL, MSG_OP_RPC_DROP_EDGE_LABEL, MSG_OP_RPC_CREATE_PATH,
        MSG_OP_RPC_DROP_PATH, MSG_OP_RPC_GET_PATH};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("MSG_OP_RPC_CREATE_PATH : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_PATH);
    printf("MSG_OP_RPC_DROP_PATH   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_PATH);
    printf("MSG_OP_RPC_GET_PATH    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_PATH);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 012 使用同步方式循环truncate表 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_012)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T20";

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    for (int i = 0; i < 100; i++) {
        ret = GmcTruncateVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_TRUNCATE_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_TRUNCATE_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TRUNCATE_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 013 使用同步方式循环绑定和解绑扩展资源池 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_013)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *resPoolTestName = "resource_pool_test";
    const char *ExternalresPoolTestName = "external_resource_pool_test";
    const char *ResPoolTest =
        R"({
            "name" : "resource_pool_test",
            "pool_id" : 1,
            "start_id" : 1,
            "capacity" : 1200,
            "order" : 0,
            "alloc_type" : 0
        })";
    const char *ResPoolExternal =
        R"({
            "name" : "external_resource_pool_test",
            "pool_id" : 2,
            "start_id" : 1,
            "capacity" : 2000,
            "order" : 0,
            "alloc_type" : 0
        })";

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, ResPoolExternal);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        // bind respool
        ret = GmcBindExtResPool(g_stmt, resPoolTestName, ExternalresPoolTestName);
        ASSERT_EQ(GMERR_OK, ret);
        // unbind respool
        ret = GmcUnbindExtResPool(g_stmt, resPoolTestName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcDestroyResPool(g_stmt, ExternalresPoolTestName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    ASSERT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_RES_POOL,
        MSG_OP_RPC_DROP_RES_POOL, MSG_OP_RPC_BIND_EXTENDED_RES_POOL, MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_RES_POOL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_RES_POOL);
    printf("MSG_OP_RPC_DROP_RES_POOL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_RES_POOL);
    printf("MSG_OP_RPC_BIND_EXTENDED_RES_POOL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_BIND_EXTENDED_RES_POOL);
    printf("MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 014 使用同步方式循环绑定和解绑资源池到表 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_014)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T20_all_type";
    char label_config[] = "{\"max_record_num\":1000000,\"auto_increment\":1}";
    const char *resPoolTestName = "resource_pool_test";
    const char *ResPoolTest =
        R"({
            "name" : "resource_pool_test",
            "pool_id" : 1,
            "start_id" : 1,
            "capacity" : 1200,
            "order" : 0,
            "alloc_type" : 0
        })";

    readJanssonFile("schema_file/all_type_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        // bind table
        ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        // unbind table
        ret = GmcUnbindResPoolFromLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    EXPECT_EQ(GMERR_OK, ret);

    //删除vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_RES_POOL, MSG_OP_RPC_DROP_RES_POOL,
        MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL, MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_RES_POOL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_RES_POOL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_RES_POOL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_RES_POOL);
    printf("MSG_OP_RPC_DROP_RES_POOL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_RES_POOL);
    printf(
        "MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL);
    printf("MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL   : %d\n",
        (MsgOpcodeRpcE)MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 015 使用同步方式循环Get资源池定义100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_015)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *respool = NULL;
    const char *resPoolTestName = "resource_pool_test";
    const char *ResPoolTest =
        R"({
            "name" : "resource_pool_test",
            "pool_id" : 1,
            "start_id" : 1,
            "capacity" : 1200,
            "order" : 0,
            "alloc_type" : 0
        })";

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        ret = GmcGetResPool(g_stmt, resPoolTestName, &respool);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_RES_POOL,
        MSG_OP_RPC_DROP_RES_POOL, MSG_OP_RPC_GET_RES_POOL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_RES_POOL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_RES_POOL);
    printf("MSG_OP_RPC_DROP_RES_POOL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_RES_POOL);
    printf("MSG_OP_RPC_GET_RES_POOL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_RES_POOL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 016 使用同步方式开始事务，提交事务100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_016)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    for (int i = 0; i < 100; i++) {
        // commit
        ret = GmcTransStart(g_conn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcTransCommit(g_conn);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_TX_START,
        MSG_OP_RPC_TX_COMMIT, MSG_OP_RPC_TX_ROLLBACK};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_TX_START    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TX_START);
    printf("MSG_OP_RPC_TX_COMMIT   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TX_COMMIT);
    printf("MSG_OP_RPC_TX_ROLLBACK : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TX_ROLLBACK);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 017 使用同步方式开始事务，回滚事务100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_017)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    for (int i = 0; i < 100; i++) {
        // rollback
        ret = GmcTransStart(g_conn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcTransRollBack(g_conn);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_TX_START,
        MSG_OP_RPC_TX_COMMIT, MSG_OP_RPC_TX_ROLLBACK};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_TX_START    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TX_START);
    printf("MSG_OP_RPC_TX_COMMIT   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TX_COMMIT);
    printf("MSG_OP_RPC_TX_ROLLBACK : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TX_ROLLBACK);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

#define TIMES_SYSTEM_018 (2 + 1)
// 018 使用同步方式OPEN和CLOSE顶点表100次，查看视图信息error
//相同表open后客户端有缓存，不需要再从服务侧获取，后续就不会增加计数，需要用不同表测试
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_018)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    void *label = NULL;

    for (int i = 0; i < 100; i++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(vertexLabelName, "testT%d", i);
        // creat vertex
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // open vertexlabel
        ret = testGmcPrepareStmtByLabelName(g_stmt, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertex
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 019 使用同步方式OPEN和CLOSE顶点表100次，再次使用同步方式OPEN和CLOSE顶点表100次，查看视图信息
// vertex label和edge label都需要不同的
// MSG_OP_RPC_END_CHECK  GmcDirectFetchNeighborEnd  测试方案待补充
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_019)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    void *label = NULL;

    for (int i = 0; i < 100; i++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(vertexLabelName, "testT%d", i);
        // creat vertex
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // open vertexlabel
        ret = testGmcPrepareStmtByLabelName(g_stmt, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertex
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 100; i < 200; i++) {
        sprintf(vertexLabelJson, "[{\
    \"type\":\"record\",\
    \"name\":\"testT%d\",\
    \"fields\":\
        [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
        {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
        {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
        {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
    \"keys\":\
        [{\"node\":\"testT%d\",\
        \"name\":\"T10_PK\",\
        \"fields\":[\"F0\"],\
        \"index\":{\"type\":\"primary\"},\
        \"constraints\":{\"unique\":true}}]\
    }]",
            i, i);
        sprintf(vertexLabelName, "testT%d", i);
        // creat vertex
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // open vertexlabel
        ret = testGmcPrepareStmtByLabelName(g_stmt, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertex
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 020 使用同步方式OPEN和CLOSE边表100次，查看视图信息
// vertex label和edge label都需要不同的
// MSG_OP_RPC_END_CHECK  GmcDirectFetchNeighborEnd  测试方案待补充
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_020)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char edgeLabelName[32] = "testT1";
    char sourceVertexLabelName[32] = "sv_testT1";
    char dstVertexLabelName[32] = "dv_testT1";
    char edgeLabelJson[1024];
    char sourceVertexLabelJson[1024];
    char dstVertexLabelJson[1024];
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    void *label2 = NULL;
    uint8_t edge_value1 = 1;
    uint8_t edge_value2 = 2;
    uint8_t edge_value3 = 3;
    uint32_t key_value = 1;

    for (int i = 0; i < 100; i++) {
        sprintf(sourceVertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"sv_testT%d\",\
        \"fields\":\
            [{\"name\":\"F5\", \"type\":\"uint32\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"uint8: 3\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"uint8: 2\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"uint8: 4\", \"nullable\":false},\
            {\"name\":\"F4\", \"type\":\"uint8: 2\", \"nullable\":false},\
            {\"name\":\"F0\", \"type\":\"uint8: 3\", \"nullable\":false}],\
        \"keys\":\
            [{\"node\":\"sv_testT%d\",\
            \"name\":\"T20_PK\",\
            \"fields\":[\"F5\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(dstVertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"dv_testT%d\",\
        \"fields\":\
            [{\"name\":\"F5\", \"type\":\"uint32\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"uint8: 3\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"uint8: 2\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"uint8: 4\", \"nullable\":false},\
            {\"name\":\"F4\", \"type\":\"uint8: 2\", \"nullable\":false},\
            {\"name\":\"F0\", \"type\":\"uint8: 3\", \"nullable\":false},\
            {\"name\":\"F6\", \"type\":\"uint8: 2\", \"nullable\":false}],\
        \"keys\":\
            [{\"node\":\"dv_testT%d\",\
            \"name\":\"T20_PK\",\
            \"fields\":[\"F5\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(edgeLabelJson,
            "[{\"name\":\"testT%d\",\"source_vertex_label\":\"sv_testT%d\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"dv_testT%d\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F0\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F1\",\"dest_property\": \"F1\"},{\"source_property\": \"F3\",\"dest_property\": "
            "\"F4\"}]}}]",
            i, i, i);
        sprintf(edgeLabelName, "testT%d", i);
        sprintf(sourceVertexLabelName, "sv_testT%d", i);
        sprintf(dstVertexLabelName, "dv_testT%d", i);
        // creat vertexlabel edgelabel
        char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";
        ret = GmcCreateVertexLabel(g_stmt, sourceVertexLabelJson, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(g_stmt, dstVertexLabelJson, Label_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, Label_config);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, sourceVertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_sourceVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, dstVertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_dstVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &stmt);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t V1_f5_value = key_value;
        ret = testGmcPrepareStmtByLabelName(stmt, sourceVertexLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, edgeLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isEof);
        ret = GmcDirectFetchNeighborEnd(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeStmt(stmt);

        // drop vertexlabel edgelabel
        ret = GmcDropGraphLabel(g_stmt, sourceVertexLabelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL, MSG_OP_RPC_DROP_EDGE_LABEL,
        MSG_OP_RPC_GET_EDGE_LABEL, MSG_OP_RPC_INSERT_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("MSG_OP_RPC_GET_EDGE_LABEL    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_EDGE_LABEL);
    printf("MSG_OP_RPC_END_CHECK    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 021 使用同步方式，使用主键批量insert、update、delete、merge、replace数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_021)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 1000;
    int key_value, locahash_value, hashcluster_value, value;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    for (int i = 0; i < 10; i++) {
        //批量插入
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量更新
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 5;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t f7_value = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
            ASSERT_EQ(GMERR_OK, ret);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量删除
        key_value = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t f7_value = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量replace
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量merge
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 5;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t pk = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &pk, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            // set_VertexProperty_PK(g_stmt,key_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量删除
        key_value = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t f7_value = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        // GmcBatchReset(batch);
        GmcBatchDestroy(batch);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_BATCH};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_BATCH    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_BATCH);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 022 使用同步方式，insert数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_022)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 023 使用同步方式，主键update数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_023)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    key_value = 1;
    value = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        uint64_t f7_value = key_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
        ASSERT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
        ret = GmcSetIndexKeyName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_UPDATE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 024 使用同步方式，二级索引update数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_024)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        locahash_value++;
        value++;
    }

    key_value = 1;
    locahash_value = 2;
    value = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        int8_t f2_value = locahash_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(f2_value));
        ASSERT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
        ret = GmcSetIndexKeyName(g_stmt, locahash_PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
        value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_UPDATE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 025 使用同步方式，按过滤条件update数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_025)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    locahash_value = 3;
    hashcluster_value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        ret = GmcSetFilter(g_stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows = 0;

        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);

        ASSERT_EQ(end_num, affectRows);
        locahash_value++;
        hashcluster_value++;
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_UPDATE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 026 使用同步方式，主键delete数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_026)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    key_value = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        uint64_t f7_value = key_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 027 使用同步方式，二级索引delete数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_027)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        locahash_value++;
        value++;
    }

    locahash_value = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        int8_t f2_value = locahash_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(f2_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, locahash_PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 028 使用同步方式，按过滤条件delete数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_028)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    for (int i = 0; i < 100; i++) {
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows = 0;
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, affectRows);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

#define TIMES_SYSTEM_029 (1 + 1)
// 029 使用同步方式，全表fetch数据，查看视图信息
// MSG_OP_RPC_FETCH_CYPHER   GmcFetch表内数据量超过缓存区（这个用例end_num大概为100时）触发 测试方案补充说明
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_029)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 1000;
    uint64_t key_value;
    int locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    for (int j = 0; j < 100; j++) {
        ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }

        bool isNull;
        bool isFinish = 0;
        int cnt = 0;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num + 1; i++) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint64_t f7_value;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F7", &f7_value, sizeof(f7_value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((bool)0, isNull);
            if (f7_value > (uint64_t)(end_num)) {
                printf("error: the value is %lld \n", f7_value);
            }
            cnt++;
        }
        EXPECT_EQ(end_num, cnt);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_FETCH_CYPHER : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

#define TIMES_SYSTEM_030 (1 + 1)
// 030 使用同步方式，通过二级索引fetch数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_030)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 1000;
    uint64_t key_value;
    int locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    for (int j = 0; j < 100; j++) {
        ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }

        bool isNull;
        bool isFinish = 0;
        int cnt = 0;
        int8_t f2_value = (int8_t)locahash_value;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, locahash_PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num + 1; i++) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint64_t f7_value;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F7", &f7_value, sizeof(f7_value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((bool)0, isNull);
            if (f7_value > (uint64_t)end_num) {
                printf("error: the value is %lld \n", f7_value);
            }
            cnt++;
        }
        EXPECT_EQ(end_num, cnt);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

#define TIMES_SYSTEM_031 (1 + 1)
// 031 使用同步方式，按条件过滤fetch数据，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_031)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 1000;
    uint64_t key_value;
    int locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    for (int j = 0; j < 100; j++) {
        ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }

        bool isNull;
        bool isFinish = 0;
        int cnt = 0;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetOutputFormat(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num + 1; i++) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint64_t f7_value;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F7", &f7_value, sizeof(f7_value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((bool)0, isNull);
            if (f7_value > (uint64_t)end_num) {
                printf("error: the value is %d \n", f7_value);
            }
            cnt++;
        }
        EXPECT_EQ(end_num, cnt);

        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_FETCH_CYPHER : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 032 使用同步方式，插入数据，手动建边，手动删边，循环100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_032)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";
    char PKName[] = "T20_PK";
    void *label = NULL;
    void *label2 = NULL;
    void *edgelabel = NULL;
    GmcStmtT *stmt = NULL;

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    // creat vertexlabel edgelabel
    ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    free(schema_2);
    free(edge_schema);

    uint8_t edge_value1 = 1;
    uint8_t edge_value2 = 2;
    uint8_t edge_value3 = 3;
    uint8_t t_value = 0;
    uint32_t key_value = 1;
    // vertexlabel1 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V1_f5_value = key_value;
    set_VertexProperty_sourceVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // vertexlabel2 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V2_f5_value = key_value;
    set_VertexProperty_dstVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // query edge
    bool isEof;
    bool isNull;
    ret = GmcAllocStmt(g_conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, edge_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isEof);
    uint8_t V2_f0_Getvalue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &V2_f0_Getvalue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((bool)0, isNull);
    EXPECT_EQ(2, V2_f0_Getvalue);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isEof);
    ret = GmcDirectFetchNeighborEnd(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    for (int i = 0; i < 100; i++) {
        // delete edge
        ret = GmcOpenEdgeLabelByName(g_stmt, edge_labelName, &edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeSrcVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeDstVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCloseEdgeLabel(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        // query edge
        ret = GmcAllocStmt(g_conn, &stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, edge_labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, isEof);
        ret = GmcDirectFetchNeighborEnd(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeStmt(stmt);
        // insert edge
        ret = GmcOpenEdgeLabelByName(g_stmt, edge_labelName, &edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeSrcVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeDstVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcInsertEdge(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCloseEdgeLabel(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // drop vertexlabel edgelabel
    ret = GmcDropGraphLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL,
        MSG_OP_RPC_DROP_EDGE_LABEL, MSG_OP_RPC_INSERT_EDGE, MSG_OP_RPC_DELETE_EDGE, MSG_OP_RPC_INSERT_VERTEX,
        MSG_OP_RPC_GET_EDGE_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("MSG_OP_RPC_INSERT_EDGE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_EDGE);
    printf("MSG_OP_RPC_DELETE_EDGE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_EDGE);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_EDGE_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 033 使用同步方式，插入删除数据，查询变更记录数，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_033)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    int opteration_count = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
        opteration_count++;
        /*  待对齐，不涉及tree，暂不修改
                void *batch = NULL;
                unsigned int CountNum = 1;
                uint64_t Count[10];
                ret = GmcDynamicArrayAlloc(g_stmt, &batch, GMC_LABEL_LIST);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcDynamicArrayAppend(batch, GMC_DATATYPE_STRING, labelName, strlen(labelName));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, Count, CountNum);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ((uint64_t)opteration_count, Count[0]);
                GmcDynamicArrayDestroy(batch);
                memset(Count, 0, sizeof(uint64_t)*10);
                batch = NULL;*/
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_GET_STAT_COUNT};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_GET_STAT_COUNT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_STAT_COUNT);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 034 使用同步方式，插入数据，表关联查询，查看视图信息
// MSG_OP_RPC_FETCH_CYPHER
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_034)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";
    char PKName[] = "T20_PK";
    void *label = NULL;
    void *label2 = NULL;
    void *edgelabel = NULL;
    GmcStmtT *stmt = NULL;

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    // creat vertexlabel edgelabel
    ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    free(schema_2);
    free(edge_schema);

    uint8_t edge_value1 = 1;
    uint8_t edge_value2 = 2;
    uint8_t edge_value3 = 3;
    uint8_t t_value = 0;
    uint32_t key_value = 1;
    // vertexlabel1 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V1_f5_value = key_value;
    /*    ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f0_value = edge_value1;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_BITFIELD8, &V1_f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f1_value = edge_value2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITFIELD8, &V1_f1_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f2_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITFIELD8, &V1_f2_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f3_value = edge_value3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_BITFIELD8, &V1_f3_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f4_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_BITFIELD8, &V1_f4_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);*/
    set_VertexProperty_sourceVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    // vertexlabel2 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V2_f5_value = key_value;
    /*    ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f0_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_BITFIELD8, &V2_f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f1_value = edge_value2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITFIELD8, &V2_f1_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f2_value = edge_value1;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITFIELD8, &V2_f2_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f3_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_BITFIELD8, &V2_f3_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f4_value = edge_value3;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_BITFIELD8, &V2_f4_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f6_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F6", GMC_DATATYPE_BITFIELD8, &V2_f6_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);*/
    set_VertexProperty_dstVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // query edge
    bool isEof;
    bool isNull;
    ret = GmcAllocStmt(g_conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, edge_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isEof);
    uint8_t V2_f0_Getvalue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &V2_f0_Getvalue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((bool)0, isNull);
    EXPECT_EQ(2, V2_f0_Getvalue);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isEof);
    ret = GmcDirectFetchNeighborEnd(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetNeighbors(g_stmt, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isEof);
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, isEof);
    }

    // drop vertexlabel edgelabel
    ret = GmcDropGraphLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL, MSG_OP_RPC_DROP_EDGE_LABEL,
        MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_BEGIN_SCAN_PATH, MSG_OP_RPC_GET_EDGE_LABEL, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_BEGIN_SCAN_PATH : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_BEGIN_SCAN_PATH);
    printf("MSG_OP_RPC_GET_EDGE_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_EDGE_LABEL);
    printf("MSG_OP_RPC_FETCH_CYPHER : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 050 使用同步方式循环创建和删除KV表100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_050)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char tableName[] = "KV1";
    for (int i = 0; i < 100; i++) {
        ret = GmcKvCreateTable(g_stmt, tableName, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvDropTable(g_stmt, tableName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_KV_TABLE,
        MSG_OP_RPC_DROP_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 051 使用同步方式循环OPEN和CLOSE KV表100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_051)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char tableName[] = "KV2";
    void *kvlabel = NULL;

    for (int i = 0; i < 100; i++) {
        ret = GmcKvCreateTable(g_stmt, tableName, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvPrepareStmtByLabelName(g_stmt, tableName);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcCloseKvTable(g_stmt);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvDropTable(g_stmt, tableName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_KV_TABLE,
        MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_GET_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 052 使用同步方式循环truncate KV表 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_052)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char tableName[] = "KV3";
    void *kvlabel = NULL;

    ret = GmcKvCreateTable(g_stmt, tableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        ret = GmcKvTruncateTable(g_stmt, tableName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcKvDropTable(g_stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_KV_TABLE,
        MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_TRUNCATE_KV_TABLE, MSG_OP_RPC_GET_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_TRUNCATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TRUNCATE_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 053 使用同步方式循环set、isKVexist、get、delete KV表数据100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_053)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char tableName[] = "KV4";
    void *kvlabel = NULL;
    char key[32] = "zhangsan";
    int32_t value = 30;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    char output[128] = {0};
    uint32_t outputLen = 128;

    ret = GmcKvCreateTable(g_stmt, tableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        // set
        ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // get
        ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(30, *(uint32_t *)output);
        EXPECT_EQ(4, outputLen);
        // delete
        ret = GmcKvRemove(g_stmt, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
        // is exist
        uint32_t isExist = 0;
        bool isnull;
        ret = GmcKvIsExist(g_stmt, key, strlen(key), &isnull);
        EXPECT_EQ(0, isnull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    // ret = GmcCloseKvTable(g_stmt);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_KV_TABLE,
        MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_SET_KV, MSG_OP_RPC_DELETE_KV, MSG_OP_RPC_GET_KV_TABLE, MSG_OP_RPC_GET_KV,
        MSG_OP_RPC_IS_KV_EXIST};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_SET_KV    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SET_KV);
    printf("MSG_OP_RPC_DELETE_KV : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_KV);
    printf("MSG_OP_RPC_GET_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV);
    printf("MSG_OP_RPC_IS_KV_EXIST : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_IS_KV_EXIST);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 054 使用同步方式循环获取KV count 100次，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_054)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char tableName[] = "KV5";
    void *kvlabel = NULL;
    char key[32] = "zhangsan";
    int32_t value = 30;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    uint32_t t_count;

    ret = GmcKvCreateTable(g_stmt, tableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        ret = GmcKvTableRecordCount(g_stmt, &t_count);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // ret = GmcCloseKvTable(g_stmt);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_KV_TABLE,
        MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_SET_KV, MSG_OP_RPC_GET_KV_TABLE, MSG_OP_RPC_GET_KV_RECORD_COUNT};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_SET_KV    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SET_KV);
    printf("MSG_OP_RPC_GET_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV_RECORD_COUNT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_RECORD_COUNT);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 055 32层表的插入、更新、删除，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_055)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel = NULL;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    const char *cond1 = (const char *)"50 > "
                                      "all(test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/"
                                      "T19/T20/T21/T22/T23/T24/T25/T26/T27/T28/T29/T30/T31/A4) and "
                                      "test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/Q1 > "
                                      "all(test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/"
                                      "T19/T20/T21/T22/T23/T24/T25/T26/T27/T28/T29/T30/T31/A15)";
    const char *cond2 = (const char *)"test32_deeep.A1>=0";
    int affectRows = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    char *pk_name = (char *)"primary_key";
    int update_value = 100;
    char labelName_32Deep[64] = "test32_deeep";
    char *testSchema = NULL;
    int array_num = 3;

    ret = GmcDropVertexLabel(g_stmt, labelName_32Deep);
    readJanssonFile("./schema_file/thirty-two_depth_node.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(testSchema);
    //插入数据
    TestThirtyTwoDepthInsert(g_stmt, bool_value, test1, test2, 0, 100, 3, labelName_32Deep);
    //读
    TestThirtyTwoDepthGetPropertyAndCheck(
        g_stmt, bool_value, test1, test2, 0, 100, 3, vertexLabel, pk_name, true, labelName_32Deep);
    //更新
    GmcResetVertex(g_stmt, false);

    //获取node
    GmcNodeT *tNode[32];
    char nodeName[128];
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_32Deep, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &tNode[0]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= 31; i++) {
        sprintf(nodeName, "T%d", i);
        ret = GmcNodeGetChild(tNode[i - 1], nodeName, &tNode[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(tNode[31], j, &tNode[31]);
        ASSERT_EQ(GMERR_OK, ret);
        TestThirtyTwoDepthSetProperty_A(tNode[31], update_value, bool_value2, test2);
    }
    ret = GmcSetFilter(g_stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(46, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_32Deep, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 46; i++) {
        int32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != 0) {
            printf("i:%d\n\n", i);
        }
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        // get root and node
        ret = GmcGetRootNode(g_stmt, &tNode[0]);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 1; i <= 31; i++) {
            sprintf(nodeName, "T%d", i);
            ret = GmcNodeGetChild(tNode[i - 1], nodeName, &tNode[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
        TestThirtyTwoDepthGetProperty_P(tNode, i, bool_value, test1, test2);
        // 读取array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(tNode[31], j, &tNode[31]);
            ASSERT_EQ(GMERR_OK, ret);
            TestThirtyTwoDepthGetProperty_A(tNode[31], update_value, bool_value2, test2);
        }
        GmcFreeIndexKey(g_stmt);
    }
    TestThirtyTwoDepthGetPropertyAndCheck(
        g_stmt, bool_value, test1, test2, 46, 100, 3, vertexLabel, pk_name, true, labelName_32Deep);

    //删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_32Deep, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, affectRows);

    // check
    TestThirtyTwoDepthGetPropertyAndCheck(
        g_stmt, bool_value, test1, test2, 0, 100, 3, vertexLabel, pk_name, false, labelName_32Deep);
    ret = GmcDropVertexLabel(g_stmt, labelName_32Deep);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_UPDATE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 056 数组嵌套表的插入、更新、删除，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_056)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char *schema = NULL;
    char labelName[] = "array_nest";
    char labelName_PK[] = "OP_PK";
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    const char *cond1 = (const char *)"150>=all(array_nest.T1/T2/A1)";
    const char *cond2 = (const char *)"array_nest.F0>=0";
    int affectRows = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    int update_value = 100;
    int start_num = 0;
    int end_num = 255;
    int arrayNum = 3;
    int vectorNum = 3;
    GmcNodeT *root, *t1, *t2, *t3, *t4, *t5;

    GmcDropVertexLabel(g_stmt, labelName);
    //创建vertexlabel
    readJanssonFile("schema_file/NormalTreeModel.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    // printf("schema: %s\r\n", schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    uint64_t pk = 0;
    int index = 1;
    char *f14_value = (char *)"string";

    // insert vertex
    TestGmcInsertVertex_nest(g_stmt, index, bool_value, f14_value, start_num, end_num, arrayNum, vectorNum, labelName);

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        pk = i * index;
        TestGmcDirectFetchVertex_nest(
            g_stmt, pk, index, bool_value, f14_value, arrayNum, vectorNum, label, labelName_PK, 1, labelName);
    }

    // update T2 node
    void *vertexLabel = NULL;
    bool new_bool_value = true;
    char *new_f14_value = (char *)"newstr";
    int new_value = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_PK(root, pk);
    TestGmcNodeSetPropertyByName_R_nest(root, pk * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P_nest(t1, pk * index, bool_value, f14_value);
    // 插array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < arrayNum; j++) {
        ret = GmcNodeGetElementByIndex(t2, j, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_A1(t2, j * new_value, new_bool_value, new_f14_value);
        ret = GmcNodeGetChild(t2, "T4", &t4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < arrayNum; m++) {
            ret = GmcNodeGetElementByIndex(t4, m, &t4);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_A2(t4, m * index, bool_value, f14_value);
        }
    }
    // 插vector节点
    for (uint32_t j = 0; j < vectorNum; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V1(t3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t3, "T5", &t5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vectorNum; m++) {
            ret = GmcNodeAppendElement(t5, &t5);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V2(t5, m * index, bool_value, f14_value);
        }
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 2 is new value,other node is old value
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &pk, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, labelName_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, false);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeGetPropertyByName_R_nest(root, pk * index, bool_value, f14_value);
    TestGmcNodeGetPropertyByName_p_nest(t1, pk * index, bool_value, f14_value);
    ret = GmcNodeGetChild(t1, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < arrayNum; j++) {
        ret = GmcNodeGetElementByIndex(t2, j, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_A1(t2, j * new_value, new_bool_value, new_f14_value);
        ret = GmcNodeGetChild(t2, "T4", &t4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < arrayNum; m++) {
            ret = GmcNodeGetElementByIndex(t4, m, &t4);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_A2(t4, m * index, bool_value, f14_value);
        }
    }
    for (uint32_t j = 0; j < vectorNum; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V1(t3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t3, "T5", &t5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vectorNum; m++) {
            ret = GmcNodeGetElementByIndex(t5, m, &t5);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V2(t5, m * index, bool_value, f14_value);
        }
    }

    //过滤删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(255, affectRows);

    //过滤查询
    int fetchtimes = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // chek
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        if (isFinish == true) {
            break;
        }
        fetchtimes++;
    }
    EXPECT_EQ(0, fetchtimes);

    GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_REPLACE_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_REPLACE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_REPLACE_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 057 record下的array数组为1024时，执行插入、更新、删除，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_057)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel = NULL;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    const char *cond1 = (const char *)"150>=all(array1024.T1/T2/A1)";
    const char *cond2 = (const char *)"array1024.F0>=0";
    int affectRows = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    char *pk_name = (char *)"pk";
    int update_value = 100;
    char labelName[64] = "array1024";
    char *schema = NULL;
    int start_num = 0;
    int end_num = 255;
    int arrayNum = 1024;
    GmcNodeT *root, *t1, *t2, *t3;

    ret = GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/TreeModelSchemaArray1024.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_LocalhashUnique(root, i);
        TestGmcNodeSetPropertyByName_R_1024(root, i, 0, (char *)"testve");
        TestGmcNodeSetPropertyByName_P_1024(t1, i, 0, (char *)"testve");
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_Arr1024(t2, i);
            if (j == arrayNum - 1) {
                break;
            }
            ret = GmcNodeGetNextElement(t2, &t2);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //过滤更新
    GmcResetVertex(g_stmt, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_R_1024(root, update_value, bool_value2, test2);
    TestGmcNodeSetPropertyByName_P_1024(t1, update_value, bool_value2, test2);

    //更新array数组
    ret = GmcNodeGetChild(t1, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < arrayNum; j++) {
        ret = GmcNodeGetElementByIndex(t2, j, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Arr1024(t2, update_value);
    }
    ret = GmcSetFilter(g_stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(151, affectRows);
    // check
    for (int i = 1; i < 151; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);

        TestGetUniqueLocalHash(root, f0_value);
        TestGmcNodeGetPropertyByName_R_1024(root, update_value, bool_value2, test2);
        TestGmcNodeGetPropertyByName_P_1024(t1, update_value, bool_value2, test2);
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeGetElementByIndex(t2, j, &t2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Array1024(t2, update_value);
        }
        GmcFreeIndexKey(g_stmt);
    }
    for (int i = 151; i < 255; i++) {
        uint64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);

        TestGetUniqueLocalHash(root, f0_value);
        TestGmcNodeGetPropertyByName_R_1024(root, i, bool_value, test1);
        TestGmcNodeGetPropertyByName_P_1024(t1, i, bool_value, test1);
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeGetElementByIndex(t2, j, &t2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Array1024(t2, i);
        }
        GmcFreeIndexKey(g_stmt);
    }
    GmcResetVertex(g_stmt, false);
    //过滤删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(255, affectRows);

    //过滤查询
    bool isFinish = false;
    int fetchtimes = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // chek
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        if (isFinish == true) {
            break;
        }
        fetchtimes++;
    }
    EXPECT_EQ(0, fetchtimes);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_UPDATE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 058 record下的vector数组为1024时，执行插入、更新、删除，查看视图信息
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_058)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel = NULL;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    const char *cond1 = (const char *)"0<all(vector1024.T3/V1)";
    int affectRows = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    char *pk_name = (char *)"OP_PK";
    int update_value = 100;
    char labelName[64] = "vector1024";
    char *schema = NULL;
    int start_num = 0;
    int end_num = 255;
    int arrayNum = 3;
    int vectorNum = 1024;
    uint32_t timeOut = 60000;

    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, NULL, &timeOut);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC, 0, NULL, NULL, NULL, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    testFreeConnOptions(connOption);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/TreeModelSchemaVector1024.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    GmcNodeT *root, *t1, *t2, *t3;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_LocalhashUnique(root, i);
        TestGmcNodeSetPropertyByName_R_1024(root, i, bool_value, test1);
        TestGmcNodeSetPropertyByName_P_1024(t1, i, bool_value, test1);

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A_1024(t2, i, false, (char *)"testve");
            if (j == arrayNum - 1) {
                break;
            }
            ret = GmcNodeGetNextElement(t2, &t2);
            ASSERT_EQ(GMERR_OK, ret);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Vector1024(t3, i);
        }
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // check
    for (int i = 1; i < 255; i++) {
        uint64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGetUniqueLocalHash(root, f0_value);
        TestGmcNodeGetPropertyByName_R_1024(root, i, bool_value, test1);
        TestGmcNodeGetPropertyByName_P_1024(t1, i, bool_value, test1);

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeGetElementByIndex(t2, j, &t2);
            if (ret != 0)
                printf("times j: %d\n", j);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_A_1024(t2, i, bool_value, test1);
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Vector1024(t3, i);
        }
        GmcFreeIndexKey(g_stmt);
    }

    //更新，vector最多16个节点，不支持过滤更新
    GmcResetVertex(g_stmt, false);

    for (int i = 1; i < 255; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        uint64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 更新vector节点
        for (uint32_t j = 0; j < 16; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Vector1024(t3, update_value);
        }
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcResetVertex(g_stmt, false);
    // check
    for (int i = 1; i < 255; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGetUniqueLocalHash(root, f0_value);
        TestGmcNodeGetPropertyByName_R_1024(root, i, bool_value, test1);
        TestGmcNodeGetPropertyByName_P_1024(t1, i, bool_value, test1);
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeGetElementByIndex(t2, j, &t2);
            if (ret != 0)
                printf("times j: %d\n", j);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_A_1024(t2, i, bool_value, test1);
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            if (j < 16) {
                TestGmcNodeGetPropertyByName_Vector1024(t3, update_value);
            } else {
                TestGmcNodeGetPropertyByName_Vector1024(t3, i);
            }
        }
        GmcFreeIndexKey(g_stmt);
    }
    //过滤删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(253, affectRows);

    //过滤查询
    bool isFinish = false;
    int fetchtimes = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // check
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        if (isFinish == true) {
            break;
        }
        fetchtimes++;
    }
    EXPECT_EQ(0, fetchtimes);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_UPDATE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 059 配置项时间阈值设为0或者-1，创建表，删除表，查看视图信息
//需要修改modifyCfg.sh，手动测试与预期一致
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_059)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_string(GmcStmtT *stmt, char *f14_value)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}
#define HALF_MEMORY_RECORDNUM 65000
// 060 创建表，插入数据至内存满，查看视图信息
//需要修改modifyCfg.sh，手动测试与预期一致
TEST_F(CPU_TimeStatistics_test, CPU_TimeStatistics_test_060)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int maxInsertNum = 0;
    void *label = NULL;
    void *label2 = NULL;
    char *schema = NULL;
    char *schema2 = NULL;
    char labelName[] = "T20_001";
    char labelName2[] = "T20_002";
    char labelName_PK[] = "T20_PK";
    char TestValue[1024];
    char label_config[] = "{\"max_record_num\":1000000}";

    GmcDropVertexLabel(g_stmt, labelName);
    GmcDropVertexLabel(g_stmt, labelName2);
    readJanssonFile("schema_file/longstringfield.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/longstringfield_2.gmjson", &schema2);
    EXPECT_NE((void *)NULL, schema2);

    // create vertex label
    ret = GmcCreateVertexLabel(g_stmt, schema, label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema2, label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    free(schema2);

    // insert record vertex 1
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f0_value = 1;
    memset(TestValue, 'a', 1023);
    TestValue[1023] = '\0';
    for (int i = 0; i < HALF_MEMORY_RECORDNUM; i++) {
        uint64_t f7_value = f0_value;
        ret = GmcSetVertexProperty(g_stmt, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_string(g_stmt, TestValue);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f0_value++;
    }

    // insert record vertex 2
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t i_max = 1;
    f0_value = 1;
    memset(TestValue, 'a', 1023);
    TestValue[1023] = '\0';
    do {
        uint64_t f7_value = f0_value;
        ret = GmcSetVertexProperty(g_stmt, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_string(g_stmt, TestValue);
        ret = GmcExecute(g_stmt);
        if (i_max % 5000 == 0) {
            printf("%lld record insert success, No:%lld\n", i_max, (i_max / 5000));
        }
        if (i_max >= 1000000) {
            printf("%lld record insert success\n", i_max);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        i_max++;
        f0_value++;
    } while (ret == 0);
    if (ret != 0) {
        maxInsertNum = i_max;
        printf("insert %lld record success, memory is full\n", i_max);
        EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);
    
    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}
