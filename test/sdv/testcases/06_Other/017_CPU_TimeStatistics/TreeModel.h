#ifndef _TREEMODEL_H
#define _TREEMODEL_H

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <time.h>
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
int32_t TestSetVectorUpdateIndex(GmcStmtT *stmt, char *nodeName, uint32_t index)
{
    int32_t ret;
    void *batch = NULL;
    uint32_t indexTmp = index;
    ret = GmcDynamicArrayAlloc(stmt, &batch, GMC_INDEX_LIST);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDynamicArrayAppend(batch, GMC_DATATYPE_UINT32, (void *)&indexTmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDeltaVectorUpdate(stmt, nodeName, batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDynamicArrayDestroy(batch);
    batch = NULL;
    return ret;
}
*/

void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    uint64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_UINT64, &f0_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    uint64_t f1_value = i;

    uint64_t respoolId = 0xFFFF;
    uint64_t count = 2;
    uint64_t startIndex = 0xFFFFFFFF;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    EXPECT_EQ(GMERR_OK, ret);
    /*  当前导出导入工具暂未支持
        // bitmap值的设置
        GmcBitMapT bitMap = {0, 127, NULL};
        uint8_t bits[128/8];
        memset(bits, 0xffff, 128/8);
        bits[128/8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
    */
    uint8_t f19_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_PARTITION, &f19_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f1_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f0_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f0_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"A18", GMC_DATATYPE_BITFIELD16, &f0_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A17", GMC_DATATYPE_BITFIELD8, &f0_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"V18", GMC_DATATYPE_BITFIELD16, &f0_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V17", GMC_DATATYPE_BITFIELD8, &f0_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    /*  当前导出导入工具暂未支持
        uint32_t f20_propSize = 0;
        uint8_t bits[128/8];
        memset(bits, 0, 128/8);
        bits[128 / 8 - 1] = '\0';
        ret = GmcGetVertexPropertySizeByName(stmt, "F20", &f20_propSize);
        EXPECT_EQ(f20_propSize, 128);
        ret = GmcGetVertexPropertyByName(stmt, "F20", bits, f20_propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)0, isNull);
        for (int i = 0; i < 128 / 8 - 1; i++) {
            EXPECT_EQ(true, 0xFF == *(bits + i));
        }
    */
    uint8_t f19_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f19_value);

    unsigned int f18_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F18", &f18_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f18_propSize, sizeof(uint16_t));

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18_value, f18_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f18_value);

    unsigned int f17_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F17", &f17_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f17_propSize, sizeof(uint8_t));

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17_value, f17_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f17_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(5 * i, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(10 * i, f10_value);
    /*  hpe和rtos上系统时间期待不一样，无法用uint64来校验*/
    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    // ASSERT_EQ(11 * i, f11_value);
    if (g_runMode) {  //欧拉和hpe环境有8h时差
        EXPECT_EQ((11 * i + 28800), f11_value);
    } else {
        EXPECT_EQ(11 * i, f11_value);
    }

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(12 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;

    unsigned int f18_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P18", &f18_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f18_propSize, sizeof(uint16_t));

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P18", &f18_value, f18_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f18_value);

    unsigned int f17_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P17", &f17_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f17_propSize, sizeof(uint8_t));

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P17", &f17_value, f17_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f17_value);

    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(5 * i, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(10 * i, f10_value);
    /*  hpe和rtos上系统时间期待不一样，无法用uint64来校验*/
    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    // ASSERT_EQ(11 * i, f11_value);
    if (g_runMode) {  //欧拉和hpe环境有8h时差
        EXPECT_EQ((11 * i + 28800), f11_value);
    } else {
        EXPECT_EQ(11 * i, f11_value);
    }

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(12 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;

    unsigned int f18_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A18", &f18_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f18_propSize, sizeof(uint16_t));

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A18", &f18_value, f18_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f18_value);

    unsigned int f17_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A17", &f17_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f17_propSize, sizeof(uint8_t));

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A17", &f17_value, f17_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f17_value);

    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(5 * i, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(10 * i, f10_value);
    /*  hpe和rtos上系统时间期待不一样，无法用uint64来校验*/
    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    // ASSERT_EQ(11 * i, f11_value);
    if (g_runMode) {  //欧拉和hpe环境有8h时差
        EXPECT_EQ((11 * i + 28800), f11_value);
    } else {
        EXPECT_EQ(11 * i, f11_value);
    }

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(12 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;

    unsigned int f18_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V18", &f18_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f18_propSize, sizeof(uint16_t));

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V18", &f18_value, f18_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f18_value);

    unsigned int f17_propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V17", &f17_propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(f17_propSize, sizeof(uint8_t));

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V17", &f17_value, f17_propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f17_value);

    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(4 * i, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(5 * i, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(6 * i, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(10 * i, f10_value);
    /*  hpe和rtos上系统时间期待不一样，无法用uint64来校验*/
    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    // ASSERT_EQ(11 * i, f11_value);
    if (g_runMode) {  //欧拉和hpe环境有8h时差
        EXPECT_EQ((11 * i + 28800), f11_value);
    } else {
        EXPECT_EQ(11 * i, f11_value);
    }

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(12 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 * i);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        // TestGmcNodeSetPropertyByName_PK(stmt, i * index);
        TestGmcNodeSetPropertyByName_R(root, i + index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i + index, bool_value, f14_value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, j + i + index, bool_value, f14_value);
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                ASSERT_EQ(GMERR_OK, ret);
            } else {
                ASSERT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, j + i + index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcUpdateVertexByIndexKey(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i + index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i + index, bool_value, f14_value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i + index, bool_value, f14_value);
            ret = GmcNodeGetNextElement(t2, &t2);
            ASSERT_EQ(GMERR_OK, ret);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i + index, bool_value, f14_value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcDirectFetchVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num)
{
    int32_t ret = 0;
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        uint64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (read_num == false) {
            EXPECT_EQ(isFinish, true);
        } else if (read_num == true) {
            EXPECT_EQ(isFinish, false);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t count;
            ret = GmcNodeGetElementCount(t3, &count);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(vector_num, count);
            TestGmcNodeGetPropertyByName_R(root, i + index, bool_value, f14_value);
            TestGmcNodeGetPropertyByName_p(t1, i + index, bool_value, f14_value);
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(t2, &count);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, count);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, j + i + index, bool_value, f14_value);
            }
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, j + i + index, bool_value, f14_value);
            }
        }
    }
}

void TestThirtyTwoDepthSetPK(GmcNodeT *node, int i)
{
    int ret = 0;
    int32_t A1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_INT32, &A1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestThirtyTwoDepthSetProperty_P(GmcNodeT *node[], int i, bool bool_value, char *strtest, char *bytestest)
{
    int32_t A1_value = i;
    int32_t B1_value = i + 1;
    unsigned char s1_value = (i + 2) % 256;
    char u1_value = (i + 3) % 128;
    double v1_value = i + 4.121;
    float w1_value = i + 5.55;
    uint64_t x1_value = i + 6;
    uint8_t z1_value = (7 + i) % 256;
    int8_t aa1_value = (8 + i) % 128;
    uint16_t bb1_value = i + 9;
    int16_t cc1_value = i + 10;
    int64_t dd1_value = i + 11;
    uint64_t ee1_value = i + 12;
    uint32_t ff1_value = i + 13;
    int ret = 0;

    // ret = GmcNodeSetPropertyByName(node[0], (char *)"A1", GMC_DATATYPE_INT32, &A1_value, sizeof(int32_t));
    // ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[1], (char *)"B1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[2], (char *)"C1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[3], (char *)"D1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[4], (char *)"E1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[5], (char *)"F1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[6], (char *)"G1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[7], (char *)"H1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[8], (char *)"I1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[9], (char *)"J1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[10], (char *)"K1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[11], (char *)"L1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[12], (char *)"M1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[13], (char *)"N1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[14], (char *)"O1", GMC_DATATYPE_INT32, &B1_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[15], (char *)"P1", GMC_DATATYPE_STRING, strtest, strlen(strtest));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[16], (char *)"Q1", GMC_DATATYPE_BYTES, bytestest, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[17], (char *)"R1", GMC_DATATYPE_FIXED, bytestest, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[18], (char *)"S1", GMC_DATATYPE_UCHAR, &s1_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[19], (char *)"U1", GMC_DATATYPE_CHAR, &u1_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[20], (char *)"V1", GMC_DATATYPE_DOUBLE, &v1_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[21], (char *)"W1", GMC_DATATYPE_FLOAT, &w1_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[22], (char *)"X1", GMC_DATATYPE_TIME, &x1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[23], (char *)"Y1", GMC_DATATYPE_BOOL, &bool_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[24], (char *)"Z1", GMC_DATATYPE_UINT8, &z1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[25], (char *)"AA1", GMC_DATATYPE_INT8, &aa1_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[26], (char *)"BB1", GMC_DATATYPE_UINT16, &bb1_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[27], (char *)"CC1", GMC_DATATYPE_INT16, &cc1_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[28], (char *)"DD1", GMC_DATATYPE_INT64, &dd1_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[29], (char *)"EE1", GMC_DATATYPE_UINT64, &ee1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node[30], (char *)"FF1", GMC_DATATYPE_UINT32, &ff1_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestThirtyTwoDepthSetProperty_A(GmcNodeT *node, int i, bool bool_value, char *strtest)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, strtest, (strlen(strtest)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, strtest, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, strtest, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestThirtyTwoDepthGetProperty_A(GmcNodeT *node, int i, bool bool_value, char *strtest)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(1 + i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i % 128), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(strtest) + 1);

    char string_value[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, strtest), 0);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(strtest, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(strtest, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestThirtyTwoDepthGetProperty_P(GmcNodeT *node[], int i, bool bool_value, char *strtest, char *bytestest)
{
    int32_t A1_value = i;
    int32_t B1_value = i + 1;
    unsigned char s1_value = (i + 2) % 256;
    char u1_value = (i + 3) % 128;
    double v1_value = i + 4.121;
    float w1_value = i + 5.55;
    uint64_t x1_value = i + 6;
    uint8_t z1_value = (7 + i) % 256;
    int8_t aa1_value = (8 + i) % 128;
    uint16_t bb1_value = i + 9;
    int16_t cc1_value = i + 10;
    int64_t dd1_value = i + 11;
    uint64_t ee1_value = i + 12;
    uint32_t ff1_value = i + 13;

    int32_t a1_query = 0;
    int32_t b1_query = 0;
    unsigned char s1_query = 0;
    char u1_query = 0;
    double v1_query = 0;
    float w1_query = 0;
    uint64_t x1_query = 0;
    uint8_t z1_query = 0;
    int8_t aa1_query = 0;
    uint16_t bb1_query = 0;
    int16_t cc1_query = 0;
    int64_t dd1_query = 0;
    uint64_t ee1_query = 0;
    uint32_t ff1_query = 0;

    bool isNull = false;
    int ret = GmcNodeGetPropertyByName(node[0], (char *)"A1", &a1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(A1_value, a1_query);

    ret = GmcNodeGetPropertyByName(node[1], (char *)"B1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[2], (char *)"C1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[3], (char *)"D1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;
    ret = GmcNodeGetPropertyByName(node[4], (char *)"E1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[5], (char *)"F1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[6], (char *)"G1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[7], (char *)"H1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[8], (char *)"I1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[9], (char *)"J1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[10], (char *)"K1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[11], (char *)"L1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[12], (char *)"M1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[13], (char *)"N1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    ret = GmcNodeGetPropertyByName(node[14], (char *)"O1", &b1_query, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(B1_value, b1_query);
    b1_query = 0;

    char string_value[7] = {0};
    ret = GmcNodeGetPropertyByName(node[15], (char *)"P1", string_value, (strlen(strtest) + 1), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(strtest, string_value), 0);
    memset(string_value, 0, 7);

    ret = GmcNodeGetPropertyByName(node[16], (char *)"Q1", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = memcmp(bytestest, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node[17], (char *)"R1", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = memcmp(bytestest, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    memset(string_value, 0, 7);

    ret = GmcNodeGetPropertyByName(node[18], (char *)"S1", &s1_query, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(s1_value, s1_query);

    ret = GmcNodeGetPropertyByName(node[19], (char *)"U1", &u1_query, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(u1_value, u1_query);

    ret = GmcNodeGetPropertyByName(node[20], (char *)"V1", &v1_query, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(v1_value, v1_query);

    ret = GmcNodeGetPropertyByName(node[21], (char *)"W1", &w1_query, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(w1_value, w1_query);

    ret = GmcNodeGetPropertyByName(node[22], (char *)"X1", &x1_query, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(x1_value, x1_query);
    bool bool_query = 0;
    ret = GmcNodeGetPropertyByName(node[23], (char *)"Y1", &bool_query, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, bool_query);

    ret = GmcNodeGetPropertyByName(node[24], (char *)"Z1", &z1_query, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(z1_value, z1_query);

    ret = GmcNodeGetPropertyByName(node[25], (char *)"AA1", &aa1_query, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(aa1_value, aa1_query);

    ret = GmcNodeGetPropertyByName(node[26], (char *)"BB1", &bb1_query, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bb1_value, bb1_query);

    ret = GmcNodeGetPropertyByName(node[27], (char *)"CC1", &cc1_query, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(cc1_value, cc1_query);

    ret = GmcNodeGetPropertyByName(node[28], (char *)"DD1", &dd1_query, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(dd1_value, dd1_query);

    ret = GmcNodeGetPropertyByName(node[29], (char *)"EE1", &ee1_query, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(ee1_value, ee1_query);

    ret = GmcNodeGetPropertyByName(node[30], (char *)"FF1", &ff1_query, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(ff1_value, ff1_query);
}

void TestThirtyTwoDepthGetPropertyAndCheck(GmcStmtT *stmt, bool bool_value, char *strtest, char *bytestest,
    int start_num, int end_num, int array_num, void *label, const char *keyName, bool read_num, const char *labelName)
{
    int32_t ret = 0;
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int32_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (read_num == false) {
            EXPECT_EQ(isFinish, true);
        } else if (read_num == true) {
            EXPECT_EQ(isFinish, false);

            //获取node
            GmcNodeT *t_node[32];
            char nodeName[128];
            ret = GmcGetRootNode(stmt, &t_node[0]);
            EXPECT_EQ(GMERR_OK, ret);
            for (int i = 1; i <= 31; i++) {
                sprintf(nodeName, "T%d", i);
                ret = GmcNodeGetChild(t_node[i - 1], nodeName, &t_node[i]);
                EXPECT_EQ(GMERR_OK, ret);
            }
            TestThirtyTwoDepthSetProperty_P(t_node, i, bool_value, strtest, bytestest);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t_node[31], j, &t_node[31]);
                EXPECT_EQ(GMERR_OK, ret);
                TestThirtyTwoDepthGetProperty_A(t_node[31], i, bool_value, strtest);
            }
        }
        GmcFreeIndexKey(stmt);
    }
}

void TestThirtyTwoDepthInsert(GmcStmtT *stmt, bool bool_value, char *strtest, char *bytestest, int start_num,
    int end_num, int array_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        //获取node
        GmcNodeT *t_node[32];
        char nodeName[128];
        ret = GmcGetRootNode(stmt, &t_node[0]);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 1; i <= 31; i++) {
            sprintf(nodeName, "T%d", i);
            ret = GmcNodeGetChild(t_node[i - 1], nodeName, &t_node[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
        TestThirtyTwoDepthSetPK(t_node[0], i);
        TestThirtyTwoDepthSetProperty_P(t_node, i, bool_value, strtest, bytestest);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(t_node[31], j, &t_node[31]);
            ASSERT_EQ(GMERR_OK, ret);
            TestThirtyTwoDepthSetProperty_A(t_node[31], i, bool_value, strtest);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcNodeSetPropertyByName_LocalhashUnique(GmcNodeT *node, int i)
{
    int ret = 0;
    uint64_t f1_value = i + 1;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_R_1024(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    // uint64_t f1_value = 1+i;
    // ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    // ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_P_1024(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_A_1024(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_Arr1024(GmcNodeT *node, int i)
{
    int ret = 0;

    uint8_t f1_value = i % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
#if 0
    int8_t f2_value=i%128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
#endif
}
void TestGmcNodeSetPropertyByName_Vector1024(GmcNodeT *node, int i)
{
    int ret = 0;
    int8_t f2_value = i % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
#if 0
    uint8_t f1_value = i%256;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
#endif
}
void TestGetUniqueLocalHash(GmcNodeT *node, int i)
{
    int ret = 0;
    bool isNull = false;
    uint64_t f1_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(1 + i, f1_value);
}
void TestGmcNodeGetPropertyByName_P_1024(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull = false;
    int64_t f0_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((1 + i), f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i) % 128, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (strcmp(string_value, f14_value));
    if (ret != 0) {
        printf("string_value:%s f14_value:%s i:%d\n", string_value, f14_value, i);
    }
    ASSERT_EQ(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeGetPropertyByName_R_1024(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull = false;
    /*uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(1+i, f1_value);*/

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i) % 128, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeGetPropertyByName_A_1024(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(1 + i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i % 128), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeGetPropertyByName_Array1024(GmcNodeT *node, int i)
{
    uint8_t f1_value;
    bool isNull = false;
    int ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i) % 256, f1_value);
#if 0
    int8_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i)%128, f2_value);
#endif
}
void TestGmcNodeGetPropertyByName_Vector1024(GmcNodeT *node, int i)
{
    int ret = 0;
    bool isNull = false;
#if 0
    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f1_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i)%256, f1_value);
#endif
    int8_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f2_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ((i) % 128, f2_value);
}

void TestGmcNodeSetPropertyByName_R_nest(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint8_t f1_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f2_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f3_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    GmcBitMapT bitMap = {0, 127, NULL}; 
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
#endif

    uint8_t partition_id = 5;
    ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_PARTITION, &partition_id, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_P_nest(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int8_t f0_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f1_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f2_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f3_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    GmcBitMapT bitMap = {0, 127, NULL}; 
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(node, (char *)"P21", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
#endif
}
void TestGmcNodeSetPropertyByName_A1(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    int8_t f0_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f1_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f2_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f3_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    GmcBitMapT bitMap = {0, 127, NULL}; 
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(node, (char *)"A21", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
#endif
}
void TestGmcNodeSetPropertyByName_A2(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    int8_t f0_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f1_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f2_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f3_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    GmcBitMapT bitMap = {0, 127, NULL}; 
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(node, (char *)"A21", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
#endif
}
void TestGmcNodeSetPropertyByName_V1(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    int8_t f0_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f1_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f2_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f3_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    GmcBitMapT bitMap = {0, 127, NULL}; 
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(node, (char *)"V21", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
#endif
}
void TestGmcNodeSetPropertyByName_V2(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    int8_t f0_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f1_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f2_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f3_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V17", GMC_DATATYPE_BITFIELD8, &f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V18", GMC_DATATYPE_BITFIELD16, &f3_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V19", GMC_DATATYPE_BITFIELD32, &f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V20", GMC_DATATYPE_BITFIELD64, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    GmcBitMapT bitMap = {0, 127, NULL}; 
    uint8_t bits[128/8];
    memset(bits, 0xff, 128/8);
    bits[128/8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(node, (char *)"V21", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
#endif
}
void TestGmcNodeGetPropertyByName_R_nest(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    bool isNull;
    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);
    /*
        if(g_runMode){ //欧拉和hpe环境有8h时差
            EXPECT_EQ((11 * i+28800), f11_value);
        }else{
            EXPECT_EQ(11 * i, f11_value);
        }
    */
    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);
#if 0
    uint8_t bits[128/8];
    memset(bits, 0, 128/8);
    bits[128/8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F21", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for(int m = 0; m < 128/8 - 1; m++) {
        EXPECT_EQ(0xff, *(bits + m));
    }
#endif
    uint8_t partition_id = 5;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(partition_id, f1_value);
}
void TestGmcNodeGetPropertyByName_p_nest(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);
    /*
        if(g_runMode){ //欧拉和hpe环境有8h时差
            EXPECT_EQ((11 * i+28800), f11_value);
        }else{
            EXPECT_EQ(11 * i, f11_value);
        }
    */
    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P17", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"P18", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"P19", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"P20", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);
#if 0
    uint8_t bits[128/8];
    memset(bits, 0, 128/8);
    bits[128/8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P21", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(node, (char *)"P21", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for(int m = 0; m < 128/8 - 1; m++) {
        EXPECT_EQ(0xff, *(bits + m));
    }
#endif
}
void TestGmcNodeGetPropertyByName_A1(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);
    /*
        if(g_runMode){ //欧拉和hpe环境有8h时差
            EXPECT_EQ((11 * i+28800), f11_value);
        }else{
            EXPECT_EQ(11 * i, f11_value);
        }
    */
    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A17", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"A18", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"A19", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"A20", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);
#if 0
    uint8_t bits[128/8];
    memset(bits, 0, 128/8);
    bits[128/8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A21", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(node, (char *)"A21", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for(int m = 0; m < 128/8 - 1; m++) {
        EXPECT_EQ(0xff, *(bits + m));
    }
#endif
}
void TestGmcNodeGetPropertyByName_A2(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);
    /*
        if(g_runMode){ //欧拉和hpe环境有8h时差
            EXPECT_EQ((11 * i+28800), f11_value);
        }else{
            EXPECT_EQ(11 * i, f11_value);
        }
    */
    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A17", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"A18", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"A19", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"A20", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);
#if 0
    uint8_t bits[128/8];
    memset(bits, 0, 128/8);
    bits[128/8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A21", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(node, (char *)"A21", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for(int m = 0; m < 128/8 - 1; m++) {
        EXPECT_EQ(0xff, *(bits + m));
    }
#endif
}
void TestGmcNodeGetPropertyByName_V1(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);
    /*
        if(g_runMode){ //欧拉和hpe环境有8h时差
            EXPECT_EQ((11 * i+28800), f11_value);
        }else{
            EXPECT_EQ(11 * i, f11_value);
        }
    */

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V17", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"V18", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"V19", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"V20", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);
#if 0
    uint8_t bits[128/8];
    memset(bits, 0, 128/8);
    bits[128/8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V21", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(node, (char *)"V21", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for(int m = 0; m < 128/8 - 1; m++) {
        EXPECT_EQ(0xff, *(bits + m));
    }
#endif
}
void TestGmcNodeGetPropertyByName_V2(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_16, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(11 * i, f11_value);
    /*
        if(g_runMode){ //欧拉和hpe环境有8h时差
            EXPECT_EQ((11 * i+28800), f11_value);
        }else{
            EXPECT_EQ(11 * i, f11_value);
        }
    */

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    unsigned char k = (unsigned char)(value_u8);
    EXPECT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V17", &f1_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f1_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"V18", &f3_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u16, f3_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"V19", &f5_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    ret = GmcNodeGetPropertyByName(node, (char *)"V20", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);
#if 0
    uint8_t bits[128/8];
    memset(bits, 0, 128/8);
    bits[128/8 - 1] = '\0';
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V21", &propSize);
    EXPECT_EQ(propSize, 128);
    ret = GmcNodeGetPropertyByName(node, (char *)"V21", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)0, isNull);
    for(int m = 0; m < 128/8 - 1; m++) {
        EXPECT_EQ(0xff, *(bits + m));
    }
#endif
}

void TestGmcInsertVertex_nest(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3, *t4, *t5;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R_nest(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P_nest(t1, i * index, bool_value, f14_value);
        // 插array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A1(t2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(t2, "T4", &t4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcNodeSetPropertyByName_A2(t4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(t4, &t4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(t2, &t2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V1(t3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(t3, "T5", &t5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(t5, &t5);
                ASSERT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_V2(t5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestGmcDirectFetchVertex_nest(GmcStmtT *stmt, uint64_t pk, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, void *label, const char *keyName, bool read_num, const char *labelName)
{
    int32_t ret = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &pk, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, false);
    GmcNodeT *root, *t1, *t2, *t3, *t4, *t5;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeGetPropertyByName_R_nest(root, pk * index, bool_value, f14_value);
    TestGmcNodeGetPropertyByName_p_nest(t1, pk * index, bool_value, f14_value);
    // 读array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeGetPropertyByName_A1(t2, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t2, "T4", &t4);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < array_num; m++) {
            TestGmcNodeGetPropertyByName_A2(t4, m * index, bool_value, f14_value);
            if (m < array_num - 1) {
                ret = GmcNodeGetNextElement(t4, &t4);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        if (j < array_num - 1) {
            ret = GmcNodeGetNextElement(t2, &t2);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    //读vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        TestGmcNodeGetPropertyByName_V1(t3, j * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t3, "T5", &t5);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            TestGmcNodeGetPropertyByName_V2(t5, m * index, bool_value, f14_value);
            if (m < vector_num - 1) {
                ret = GmcNodeGetNextElement(t5, &t5);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        if (j < vector_num - 1) {
            ret = GmcNodeGetNextElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // read by GmcSetElementIndexByMemberKey
    GmcIndexKeyT *t2_mk;
    ret = GmcNodeAllocKey(t2, "mk1", &t2_mk);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *t4_mk;
    ret = GmcNodeAllocKey(t4, "mk2", &t4_mk);
    EXPECT_EQ(GMERR_OK, ret);
    // 读array节点
    for (uint32_t j = 0; j < array_num; j++) {
        uint64_t mk1 = 7 * j * index;
        ret = GmcNodeSetKeyValue(t2_mk, 0, GMC_DATATYPE_UINT64, &mk1, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(t2, t2_mk, &t2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_A1(t2, j * index, bool_value, f14_value);
        for (uint32_t m = 0; m < array_num; m++) {
            uint64_t mk2 = 7 * m * index;
            ret = GmcNodeSetKeyValue(t4_mk, 0, GMC_DATATYPE_UINT64, &mk2, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(t4, t4_mk, &t4);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_A2(t4, m * index, bool_value, f14_value);
        }
    }
    GmcNodeFreeKey(t2_mk);
    GmcNodeFreeKey(t4_mk);
    //读vector节点
    GmcIndexKeyT *t3_mk;
    ret = GmcNodeAllocKey(t3, "mk3", &t3_mk);
    EXPECT_EQ(GMERR_OK, ret);
    GmcIndexKeyT *t5_mk;
    ret = GmcNodeAllocKey(t5, "mk4", &t5_mk);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        uint64_t mk3 = 7 * j * index;
        ret = GmcNodeSetKeyValue(t3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(t3, t3_mk, &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V1(t3, j * index, bool_value, f14_value);
        for (uint32_t m = 0; m < vector_num; m++) {
            uint64_t mk4 = 7 * m * index;
            ret = GmcNodeSetKeyValue(t5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(t5, t5_mk, &t5);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V2(t5, m * index, bool_value, f14_value);
        }
    }
    GmcNodeFreeKey(t3_mk);
    GmcNodeFreeKey(t5_mk);
}

#ifdef __cplusplus
}
#endif

#endif
