/* ****************************************************************************
 Description  : CPU视图 异步操作测试
 Node      :
 01.基本功能
//Other_017_035 使用异步方式，使用主键批量insert、update、delete、merge、replace数据，查看视图信息
//Other_017_036 使用异步方式，insert数据，查看视图信息
//Other_017_037 使用异步方式，主键update数据，查看视图信息
//Other_017_038 使用异步方式，二级索引update数据，查看视图信息
//Other_017_039 使用异步方式，按过滤条件update数据，查看视图信息
//Other_017_040 使用异步方式，主键delete数据，查看视图信息
//Other_017_041 使用异步方式，二级索引delete数据，查看视图信息
//Other_017_042 使用异步方式，按过滤条件delete数据，查看视图信息
//Other_017_043 使用异步方式，全表fetch数据，查看视图信息
//Other_017_044 使用异步方式，通过二级索引fetch数据，查看视图信息
//Other_017_045 使用异步方式，按条件过滤fetch数据，查看视图信息
//Other_017_046 使用异步方式，插入数据自动建边，删除数据自动删边，循环100次，查看视图信息
//Other_017_047 使用异步方式，插入删除数据，查询变更记录数，查看视图信息
//Other_017_048 使用异步方式，插入数据，表关联查询，查看视图信息
Author       : 文思奇 wwx1060458
 Modification :
 Date         : 2021/06/23
**************************************************************************** */

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <semaphore.h>
#include <sys/shm.h>  //for shared memory
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "TreeModel.h"
#include "../../../../../src/common/include/protocol/db_rpc_msg_op.h"  //包含op code的枚举值

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char cond_OpCode[128] = {0};
char const *view_name = "V\\$DRT_LONG_OPERATION_STAT";
char const *view_name_1 = "V\\$DRT_CONN_STAT";

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
int ret = 0;

using namespace std;

class CPU_TimeStatistics_Asy_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
        system("ipcs");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
        system("sh $TEST_HOME/tools/start.sh -f ");
        system("ipcs");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        ret = testEnvClean();
        EXPECT_EQ(GMERR_OK, ret);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CPU_TimeStatistics_Asy_test::SetUp()
{
    printf("CPU_TimeStatistics_Asy_test Start.\n");
    AW_CHECK_LOG_BEGIN();
}
void CPU_TimeStatistics_Asy_test::TearDown()
{
    AW_CHECK_LOG_END();
    printf("CPU_TimeStatistics_Asy_test End.\n");
}

int GetPrintBycmd(char *cmd, int *vaule)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
    char cmdOutput[64] = {0};
    while (NULL != fgets(cmdOutput, 64, pf))
        ;
    *vaule = atoi(cmdOutput);
    pclose(pf);
    return 0;
}
void set_VertexProperty_sourceVertex(
    GmcStmtT *stmt, uint32_t pk_value, uint8_t edge_vdata1, uint8_t edge_vdata2, uint8_t edge_vdata3)
{

    uint8_t temp = 1;
    uint32_t V1_f5_value = pk_value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f0_value = edge_vdata1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_BITFIELD8, &V1_f0_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f1_value = edge_vdata2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD8, &V1_f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f2_value = temp;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD8, &V1_f2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f3_value = edge_vdata3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &V1_f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f4_value = temp;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BITFIELD8, &V1_f4_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_dstVertex(
    GmcStmtT *stmt, uint32_t pk_value, uint8_t edge_vdata1, uint8_t edge_vdata2, uint8_t edge_vdata3)
{

    uint8_t temp = 2;
    uint32_t V2_f5_value = pk_value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f0_value = temp;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_BITFIELD8, &V2_f0_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f1_value = edge_vdata2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD8, &V2_f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f2_value = edge_vdata1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD8, &V2_f2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f3_value = temp;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &V2_f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f4_value = edge_vdata3;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BITFIELD8, &V2_f4_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f6_value = temp;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_BITFIELD8, &V2_f6_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_PK(GmcStmtT *stmt, uint64_t i)
{

    uint64_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_localhash(GmcStmtT *stmt, int i)
{

    int8_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_hashcluster(GmcStmtT *stmt, int i)
{

    uint8_t f3_value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty(GmcStmtT *stmt, int i, bool bool_value, char *f14_value, bool isupdate)
{

    char f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f1_vaule = i + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f0_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f6_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f9_value = i + 3;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f10_value = i + 4;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float f11_value = i + 5;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f12_value = i + 6;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f13_value = i + 7;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = (i + 8) % 255;  // 2^8
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = (i + 9) % 4095;  // 2^12
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = (i + 10) % 268435455;  // 2^28
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i + 11;  // 2^58
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    // partition字段不允许修改
    if (isupdate == 0) {
        uint8_t f21_value = (12 + i) % 15;
        ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
}

// 035 使用异步方式，使用主键批量insert、update、delete、merge、replace数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_035)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 1000;
    int key_value, locahash_value, hashcluster_value, value;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    for (int i = 0; i < 10; i++) {
        //批量插入
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量更新
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 5;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t f7_value = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
            ASSERT_EQ(GMERR_OK, ret);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量删除
        key_value = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t f7_value = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量replace
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量merge
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 5;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t pk = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &pk, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            // set_VertexProperty_PK(g_stmt,key_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        GmcBatchReset(batch);
        //批量删除
        key_value = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchPrepare(g_conn, NULL, &batch);
        // EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            uint64_t f7_value = key_value;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, PKName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, totalNum);
        ASSERT_EQ(end_num, successNum);
        // GmcBatchReset(batch);
        GmcBatchDestroy(batch);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_BATCH};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_BATCH    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_BATCH);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 036 使用异步方式，insert数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_036)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 037 使用异步方式，主键update数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_037)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    key_value = 1;
    value = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        uint64_t f7_value = key_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
        ASSERT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
        ret = GmcSetIndexKeyName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_UPDATE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 038 使用异步方式，二级索引update数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_038)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        locahash_value++;
        value++;
    }

    key_value = 1;
    locahash_value = 2;
    value = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        int8_t f2_value = locahash_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(f2_value));
        ASSERT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 1);
        ret = GmcSetIndexKeyName(g_stmt, locahash_PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
        value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_UPDATE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 039 用异步方式，按过滤条件update数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_039)
{
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    locahash_value = 3;
    hashcluster_value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        ret = GmcSetFilter(g_stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows = 0;

        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);

        ASSERT_EQ(end_num, affectRows);
        locahash_value++;
        hashcluster_value++;
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_UPDATE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 040 使用异步方式，主键delete数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_040)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        // ret = GmcExecute(g_stmt);
        // EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
    }

    key_value = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        uint64_t f7_value = key_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(f7_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 041 使用异步方式，二级索引delete数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_041)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        locahash_value++;
        value++;
    }

    locahash_value = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        int8_t f2_value = locahash_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(f2_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, locahash_PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 042 使用异步方式，按过滤条件delete数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_042)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    for (int i = 0; i < 100; i++) {
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows = 0;
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(end_num, affectRows);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 043 使用异步方式，全表fetch数据，查看视图信息
// MSG_OP_RPC_FETCH_CYPHER   GmcFetch表内数据量超过缓存区（这个用例end_num大概为100时）触发 测试方案补充说明
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_043)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 1000;
    uint64_t key_value;
    int locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    for (int j = 0; j < 100; j++) {
        ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }

        bool isNull;
        bool isFinish = 0;
        int cnt = 0;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num + 1; i++) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint64_t f7_value;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F7", &f7_value, sizeof(f7_value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((bool)0, isNull);
            if (f7_value > (uint64_t)(end_num)) {
                printf("error: the value is %lld \n", f7_value);
            }
            cnt++;
        }
        EXPECT_EQ(end_num, cnt);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_FETCH_CYPHER : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 044 使用异步方式，通过二级索引fetch数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_044)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 1000;
    uint64_t key_value;
    int locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    for (int j = 0; j < 100; j++) {
        ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }

        bool isNull;
        bool isFinish = 0;
        int cnt = 0;
        int8_t f2_value = (int8_t)locahash_value;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, locahash_PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num + 1; i++) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint64_t f7_value;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F7", &f7_value, sizeof(f7_value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((bool)0, isNull);
            if (f7_value > (uint64_t)end_num) {
                printf("error: the value is %lld \n", f7_value);
            }
            cnt++;
        }
        EXPECT_EQ(end_num, cnt);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 045 使用异步方式，按条件过滤fetch数据，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_045)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    char locahash_PKName[] = "localhash_key1";
    void *label = NULL;
    int end_num = 1000;
    uint64_t key_value;
    int locahash_value, hashcluster_value, value;
    char const *cond = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    for (int j = 0; j < 100; j++) {
        ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num; i++) {
            set_VertexProperty_PK(g_stmt, key_value);
            set_VertexProperty_localhash(g_stmt, locahash_value);
            set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
            set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            key_value++;
            value++;
        }

        bool isNull;
        bool isFinish = 0;
        int cnt = 0;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < end_num + 1; i++) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint64_t f7_value;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F7", &f7_value, sizeof(f7_value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((bool)0, isNull);
            if (f7_value > (uint64_t)end_num) {
                printf("error: the value is %d \n", f7_value);
            }
            cnt++;
        }
        EXPECT_EQ(end_num, cnt);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_FETCH_CYPHER : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 046 使用异步方式，插入数据自动建边，删除数据自动删边，循环100次，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_046)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";
    char PKName[] = "T20_PK";
    void *label = NULL;
    void *label2 = NULL;
    void *edgelabel = NULL;
    GmcStmtT *stmt = NULL;

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);

    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    // creat vertexlabel edgelabel
    ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    free(schema_2);
    free(edge_schema);

    uint8_t edge_value1 = 1;
    uint8_t edge_value2 = 2;
    uint8_t edge_value3 = 3;
    uint8_t t_value = 0;
    uint32_t key_value = 1;
    // vertexlabel1 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V1_f5_value = key_value;
    set_VertexProperty_sourceVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // vertexlabel2 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V2_f5_value = key_value;
    set_VertexProperty_dstVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // query edge
    bool isEof;
    bool isNull;
    ret = GmcAllocStmt(g_conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, edge_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isEof);
    uint8_t V2_f0_Getvalue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &V2_f0_Getvalue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((bool)0, isNull);
    EXPECT_EQ(2, V2_f0_Getvalue);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isEof);
    ret = GmcDirectFetchNeighborEnd(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    for (int i = 0; i < 100; i++) {
        // delete edge
        ret = GmcOpenEdgeLabelByName(g_stmt, edge_labelName, &edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeSrcVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeDstVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCloseEdgeLabel(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        // query edge
        ret = GmcAllocStmt(g_conn, &stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, edge_labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, isEof);
        ret = GmcDirectFetchNeighborEnd(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeStmt(stmt);
        // insert edge
        ret = GmcOpenEdgeLabelByName(g_stmt, edge_labelName, &edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeSrcVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(g_stmt, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetEdgeDstVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(GMC_DATATYPE_UINT32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcInsertEdge(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCloseEdgeLabel(g_stmt, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // drop vertexlabel edgelabel
    ret = GmcDropGraphLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL,
        MSG_OP_RPC_DROP_EDGE_LABEL, MSG_OP_RPC_INSERT_EDGE, MSG_OP_RPC_DELETE_EDGE, MSG_OP_RPC_INSERT_VERTEX,
        MSG_OP_RPC_GET_EDGE_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("MSG_OP_RPC_INSERT_EDGE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_EDGE);
    printf("MSG_OP_RPC_DELETE_EDGE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_EDGE);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_EDGE_LABEL);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 047 使用异步方式，插入删除数据，查询变更记录数，查看视图信息
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_047)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    int end_num = 100;
    int key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    int opteration_count = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, key_value);
        set_VertexProperty_localhash(g_stmt, locahash_value);
        set_VertexProperty_hashcluster(g_stmt, hashcluster_value);
        set_VertexProperty(g_stmt, value, 0, (char *)"string", 0);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        key_value++;
        value++;
        opteration_count++;
        /*  待对齐，不涉及tree，暂不修改
                void *batch = NULL;
                unsigned int CountNum = 1;
                uint64_t Count[10];
                ret = GmcDynamicArrayAlloc(g_stmt, &batch, GMC_LABEL_LIST);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcDynamicArrayAppend(batch, GMC_DATATYPE_STRING, labelName, strlen(labelName));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_INSERT, Count, CountNum);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ((uint64_t)opteration_count, Count[0]);
                GmcDynamicArrayDestroy(batch);
                memset(Count, 0, sizeof(uint64_t)*10);
                batch = NULL;*/
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_GET_STAT_COUNT};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_GET_STAT_COUNT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_STAT_COUNT);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}

// 048 使用异步方式，插入数据，表关联查询，查看视图信息
// MSG_OP_RPC_FETCH_CYPHER
TEST_F(CPU_TimeStatistics_Asy_test, CPU_TimeStatistics_Asy_test_048)
{

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    char *schema_2 = NULL;
    char *edge_schema = NULL;
    char labelName[] = "T20";
    char labelName_2[] = "T21";
    char edge_labelName[] = "from_T20_to_T21";
    char PKName[] = "T20_PK";
    void *label = NULL;
    void *label2 = NULL;
    void *edgelabel = NULL;
    GmcStmtT *stmt = NULL;

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/byte_bitfield_type_discontinuous_schema.gmjson", &schema_2);
    EXPECT_NE((void *)NULL, schema_2);
    readJanssonFile("schema_file/edge_and_byte_bitfield.gmjson", &edge_schema);
    EXPECT_NE((void *)NULL, edge_schema);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    // creat vertexlabel edgelabel
    ret = GmcCreateVertexLabel(g_stmt, schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema_2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    free(schema_2);
    free(edge_schema);

    uint8_t edge_value1 = 1;
    uint8_t edge_value2 = 2;
    uint8_t edge_value3 = 3;
    uint8_t t_value = 0;
    uint32_t key_value = 1;
    // vertexlabel1 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V1_f5_value = key_value;
    /*    ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f0_value = edge_value1;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_BITFIELD8, &V1_f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f1_value = edge_value2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITFIELD8, &V1_f1_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f2_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITFIELD8, &V1_f2_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f3_value = edge_value3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_BITFIELD8, &V1_f3_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V1_f4_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_BITFIELD8, &V1_f4_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);*/
    set_VertexProperty_sourceVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    // vertexlabel2 insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t V2_f5_value = key_value;
    /*    ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f0_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_BITFIELD8, &V2_f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f1_value = edge_value2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITFIELD8, &V2_f1_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f2_value = edge_value1;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITFIELD8, &V2_f2_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f3_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_BITFIELD8, &V2_f3_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f4_value = edge_value3;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_BITFIELD8, &V2_f4_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t V2_f6_value = t_value;
        ret = GmcSetVertexProperty(g_stmt, "F6", GMC_DATATYPE_BITFIELD8, &V2_f6_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);*/
    set_VertexProperty_dstVertex(g_stmt, key_value, edge_value1, edge_value2, edge_value3);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // query edge
    bool isEof;
    bool isNull;
    ret = GmcAllocStmt(g_conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, edge_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isEof);
    uint8_t V2_f0_Getvalue;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &V2_f0_Getvalue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((bool)0, isNull);
    EXPECT_EQ(2, V2_f0_Getvalue);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isEof);
    ret = GmcDirectFetchNeighborEnd(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetNeighbors(g_stmt, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isEof);
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, isEof);
    }

    // drop vertexlabel edgelabel
    ret = GmcDropGraphLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT, MSG_OP_RPC_CREATE_VERTEX_LABEL,
        MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_CREATE_EDGE_LABEL, MSG_OP_RPC_DROP_EDGE_LABEL,
        MSG_OP_RPC_INSERT_VERTEX, MSG_OP_RPC_BEGIN_SCAN_PATH, MSG_OP_RPC_GET_EDGE_LABEL, MSG_OP_RPC_FETCH_CYPHER};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_CREATE_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_EDGE_LABEL);
    printf("MSG_OP_RPC_DROP_EDGE_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_EDGE_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX    : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_BEGIN_SCAN_PATH : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_BEGIN_SCAN_PATH);
    printf("MSG_OP_RPC_GET_EDGE_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_EDGE_LABEL);
    printf("MSG_OP_RPC_FETCH_CYPHER : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_FETCH_CYPHER);
    printf("*************test op code display end************\n");

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "OPCODE", "COUNT", "CPU_TIME_AVERAGE", "CPU_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name_1);
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_MSG_CPU_PROC_TIME", "AVG_MSG_CPU_PROC_TIME", "MAX_MSG_CPU_PROC_TIME");
    EXPECT_EQ(GMERR_OK, ret);
}
