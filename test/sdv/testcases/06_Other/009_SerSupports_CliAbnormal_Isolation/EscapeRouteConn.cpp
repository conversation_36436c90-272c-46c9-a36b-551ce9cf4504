extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "connect_test.h"
using namespace std;

int ret;
#define CON_SLEEP_TIME 2
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
uint32_t g_existConnNum = 0;

class escape_route_conn : public testing::Test {
protected:
    static void SetUpTestCase()
    {
#ifdef FEATURE_PERSISTENCE
        system("sh $TEST_HOME/tools/modifyCfg.sh \"redoPubBufSize=256\"");
#endif
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
#ifdef FEATURE_PERSISTENCE
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
#endif
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void escape_route_conn::SetUp()
{
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    AW_ADD_ERR_WHITE_LIST(5, "GMERR-1004005", "GMERR-1010000", "GMERR-1010002", "GMERR-1015004", "GMERR-1015002");
    printf("escape_route_conn Start.\n");
    AW_CHECK_LOG_BEGIN();
}
void escape_route_conn::TearDown()
{
    AW_CHECK_LOG_END();
    printf("escape_route_conn End.\n");
}

class escape_route_conn2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void *thread_client_connect_2(void *args)
{
    GmcConnT **conn_thr = (GmcConnT **)args;
    // usleep(100000);
    ret = testGmcConnect(conn_thr);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

/*****************************************************************************
 * Description  : 001 先建立1个连接，断开，再建立1024个连接
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_001)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // 减少打屏日志printf("escape_route_conn_002 connect success: %d\n", i);
    }

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // 减少打屏日志printf("escape_route_conn_002 disconnect success: %d\n", i);
    }
}

/*****************************************************************************
 * Description  : 002 建立1024个连接，再建立1个失败，再断开1024
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_002)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // uid_change();
        // 减少打屏日志printf("escape_route_conn_002 connect success: %d\n", i);
    }

    ret = testGmcConnect(&conn);
    EXPECT_NE(GMERR_OK, ret);

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // 减少打屏日志printf("escape_route_conn_002 disconnect success: %d\n", i);
    }
}

/*****************************************************************************
 * Description  : 003 建立1023个连接，断开1023个，再建立1个
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_003)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 1; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // 减少打屏日志printf("escape_route_conn_002 connect success: %d\n", i);
    }

    for (i = 1; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // 减少打屏日志printf("escape_route_conn_002 disconnect success: %d\n", i);
    }

    ret = testGmcConnect(&conn_t[0]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_t[0]);
    EXPECT_EQ(GMERR_OK, ret);

    memset(conn_t, 0, sizeof(conn_t));
}

/*****************************************************************************
 * Description  : 004 创建1024个连接，断开1024个连接，重复多次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_004)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    int i, j;

    for (j = 0; j < 10; j++) {
        for (i = 0; i < MAX_CONN - existConnNum; i++) {
            ret = testGmcConnect(&conn_t[i]);
            EXPECT_EQ(GMERR_OK, ret);
            printf("escape_route_conn_004 connect success: %d %d\n", j, i);
        }

        for (i = 0; i < MAX_CONN - existConnNum; i++) {
            ret = testGmcDisconnect(conn_t[i]);
            EXPECT_EQ(GMERR_OK, ret);
            printf("escape_route_conn_004 disconnect success: %d\n", i);
        }
        memset(conn_t, 0, sizeof(conn_t));
        sleep(CON_SLEEP_TIME);
        // conn_t[MAX_CONN - existConnNum] = {0};
    }
    // EXPECT_EQ(j, 10);
}

/*****************************************************************************
 * Description  : 005 创建1022个连接，再连接连接两个逃生通道
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_005)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 2; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_005 connect success: %d\n", i);
    }

    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_005 connect success: %d\n", i);
    }

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_005 disconnect success: %d\n", i);
    }
    memset(conn_t, 0, sizeof(conn_t));
}
/*****************************************************************************
 * Description  : 006 建立1个连接并断开，启动128个线程进行连接，完成后再建立1个连接
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/

TEST_F(escape_route_conn, Other_009_006)
{
#if defined(CPU_BIT_32) && defined(ENV_RTOSV2)
#define MAX_CONN1 128
#elif defined(ENV_SUSE)
#define MAX_CONN1 MAX_CONN_SIZE - 1
#else
#define MAX_CONN1 MAX_CONN_SIZE
#endif

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN1 - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN1 - existConnNum) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    pthread_t thr_arr[MAX_CONN1 - existConnNum];
    pthread_attr_t pThreadAttrs;
    pthread_attr_init(&pThreadAttrs);
    pthread_attr_setstacksize(&pThreadAttrs, 10240 * 3);
    void *thr_ret[MAX_CONN1 - existConnNum];
    int i;

    for (i = 0; i < MAX_CONN1 - existConnNum; i++) {
        pthread_create(&thr_arr[i], &pThreadAttrs, thread_client_connect_2, (void *)&conn_t[i]);
        printf("escape_route_conn_006 thread id: %d\n", i);
    }
    for (i = 0; i < MAX_CONN1 - existConnNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    for (i = 0; i < MAX_CONN1 - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(conn_t, 0, sizeof(conn_t));
}

/*****************************************************************************
 * Description  : 007 创建1024个连接，再次建连失败，采用无效的连接进行操作(这个无效的连接应该是个空指针）
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_007)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum], *conn = NULL;
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    void *vertexLabel = NULL;
    GmcStmtT *stmt_t[MAX_CONN - existConnNum], *stmt = NULL;
    memset(stmt_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcStmtT *));
    int i;

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_007 connect success: %d\n", i);
    }
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_NE(GMERR_OK, ret);
    //创建vertex
    ret = create_vertex(&stmt, &vertexLabel, 0);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_007 disconnect success: %d\n", i);
    }
    memset(conn_t, 0, sizeof(conn_t));
}

void *ThreadStartClient1(void *args)
 
{
    int ret = system("./EscapeRouteConn --gtest_also_run_disabled_tests "
                     "--gtest_filter=escape_route_conn2.DISABLED_Client1 >Client1.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端创建连接
TEST_F(escape_route_conn2, DISABLED_Client1)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client1 start.");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn1[10] = {0};
    GmcStmtT *stmt1[10] = {0};

    for (int i = 0; i < 10; i++) {
        ret = testGmcConnect(&conn1[i], &stmt1[i]);
        EXPECT_NE(GMERR_OK, ret);
    }
    system("echo Connecting when more than 1024 ...");
    system("sh connect_top.sh");

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    testEnvClean();
}

/*****************************************************************************
 * Description  : 008 建立1024个连接以后，持续建连，对现有连接无影响，观察top内存
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_008)
{
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum + 100];
    GmcStmtT *stmt_t[MAX_CONN - existConnNum + 100];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum + 100) * sizeof(GmcConnT *));
    memset(stmt_t, 0x00, (MAX_CONN - existConnNum + 100) * sizeof(GmcStmtT *));
    void *vertexLabel = NULL;
    void *vertex = NULL, *queryvertex = NULL;
    void *filter = NULL;
    char labelName[] = "T39_label_0";


    system("echo No connection ...");
    system("sh connect_top.sh");
    for (int i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_008 connect success: %d\n", i);
    }

    system("echo Connecting 1024 ...");
    system("sh connect_top.sh");

    pthread_t thrArr[1];
    ret = pthread_create(&thrArr[0], NULL, ThreadStartClient1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);

    // 创建vertex
    ret = create_vertex(&stmt_t[0], &vertexLabel, 0);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入记录
    ret = insert_vertex(&stmt_t[0]);
    EXPECT_EQ(GMERR_OK, ret);
    // 查询记录
    //  query_vertex(&stmt_t[0], &vertexLabel);

    // GmcFreeIndexKey(stmt_t[0]);

    ret = GmcDropVertexLabel(stmt_t[0], labelName);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("echo DisConnecting ...");
    system("sh connect_top.sh");

    memset(conn_t, 0, sizeof(conn_t));
}

void *ThreadStartClient2(void *args)
 
{
    int ret = system("./EscapeRouteConn --gtest_also_run_disabled_tests "
                     "--gtest_filter=escape_route_conn2.DISABLED_Client2 >Client2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartClient3(void *args)
 
{
    int ret = system("./EscapeRouteConn --gtest_also_run_disabled_tests "
                     "--gtest_filter=escape_route_conn2.DISABLED_Client3 >Client3.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端2创建连接
TEST_F(escape_route_conn2, DISABLED_Client2)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client2 start.");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < (MAX_CONN - g_existConnNum) / 2; i++) {
        // 记录当前子进程连接上锁情况
#ifdef ENV_SUSE
        AW_FUN_Log(LOG_DEBUG, "第%d次建连,pid：%u ,tid：%u. begin; __lock = %d, __owner = %d, __nusers = %d\n", i,
            getpid(), pthread_self(), g_connLock.__data.__lock, g_connLock.__data.__owner, g_connLock.__data.__nusers);
#endif

        ret = testGmcConnect(&g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
#ifdef ENV_SUSE
        AW_FUN_Log(LOG_DEBUG, "第%d次建连,pid:%u ,tid：%u, end; __lock = %d, __owner = %d, __nusers = %d\n", i,
            getpid(), pthread_self(), g_connLock.__data.__lock, g_connLock.__data.__owner, g_connLock.__data.__nusers);
#endif
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    testEnvClean();
}

// 客户端3创建连接
TEST_F(escape_route_conn2, DISABLED_Client3)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client3 start.");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 心跳连接
    int32_t cnt = 0;
#ifdef ENV_SUSE
    cnt = 1;
#endif
    for (int i = (MAX_CONN - g_existConnNum) / 2; i < (MAX_CONN - g_existConnNum) / 2 + 1024 - cnt; i++) {
        // 记录当前子进程连接上锁情况
#ifdef ENV_SUSE
        AW_FUN_Log(LOG_DEBUG, "第%d次建连,pid：%u ,tid：%u, begin __lock = %d, __owner = %d, __nusers = %d\n", i,
            getpid(), pthread_self(), g_connLock.__data.__lock, g_connLock.__data.__owner, g_connLock.__data.__nusers);
#endif

        ret = testGmcConnect(&g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
#ifdef ENV_SUSE
        AW_FUN_Log(LOG_DEBUG, "第%d次建连,pid：%u ,tid：%u, end __lock = %d, __owner = %d, __nusers = %d\n", i,
            getpid(), pthread_self(), g_connLock.__data.__lock, g_connLock.__data.__owner, g_connLock.__data.__nusers);
#endif
    }

    for (int i = (MAX_CONN - g_existConnNum) / 2; i < (MAX_CONN - g_existConnNum) / 2 + 1024 - cnt; i++) {
        ret = testGmcDisconnect(g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    testEnvClean();
}

/*****************************************************************************
 * Description  : 009 两个进程分别建立连接，第1个进程退出，第二个进程建立1024个连接，验证进程1资源是否释放
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * ****************************************************************************/
TEST_F(escape_route_conn, Other_009_009)
{
    char g_errorCode1[1024] = {0};
    char g_errorCode2[1024] = {0};
    char g_errorCode3[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode2, 1024, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode3, 1024, "GMERR-%d", GMERR_FILE_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode1, g_errorCode2, g_errorCode3);

    ret = testGetConnNum(&g_existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_conn, 0, sizeof(void *) * MAX_CONN * 2);

    pthread_t thrArr[2];
    int ret = pthread_create(&thrArr[0], NULL, ThreadStartClient2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);

    ret = pthread_create(&thrArr[1], NULL, ThreadStartClient3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[1], NULL);

    g_existConnNum = 0;
}

void *ThreadStartClient4(void *args)
 
{
    int ret = system("./EscapeRouteConn --gtest_also_run_disabled_tests "
                     "--gtest_filter=escape_route_conn2.DISABLED_Client4 >Client4.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartClient5(void *args)
 
{
    int ret = system("./EscapeRouteConn --gtest_also_run_disabled_tests "
                     "--gtest_filter=escape_route_conn2.DISABLED_Client5 >Client5.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端4创建连接
TEST_F(escape_route_conn2, DISABLED_Client4)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client4 start.");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < MAX_CONN - g_existConnNum / 2; i++) {
        ret = testGmcConnect(&g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    system("echo true >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = system(g_command);
    while (ret != GMERR_OK) {
        sleep(1);
        ret = system(g_command);
    }

    for (int i = 0; i < MAX_CONN - g_existConnNum / 2; i++) {
        ret = testGmcDisconnect(g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    testEnvClean();
}

// 客户端5创建连接
TEST_F(escape_route_conn2, DISABLED_Client5)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client5 start.");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = MAX_CONN - g_existConnNum / 2; i < MAX_CONN - g_existConnNum; i++) {
        ret = testGmcConnect(&g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    system("echo true >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = system(g_command);
    }

    for (int i = MAX_CONN - g_existConnNum / 2; i < MAX_CONN - g_existConnNum; i++) {
        ret = testGmcDisconnect(g_conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    testEnvClean();
}

/*****************************************************************************
 * Description  : 010 两个进程分别建立512个连接后，主进程再次连接失败
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_010)
{
    memset(g_conn, 0, sizeof(void *) * MAX_CONN * 2);
    
    ret = testGetConnNum(&g_existConnNum);
    EXPECT_EQ(0, ret);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, ThreadStartClient4, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_create(&thrArr[1], NULL, ThreadStartClient5, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log |grep true |wc -l");
    ret = executeCommand(g_command, "2");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "2");
    }

    ret = testGmcConnect(&g_conn[MAX_CONN - g_existConnNum]);
    EXPECT_NE(GMERR_OK, ret); // NE

    system("echo true >>b.log");

    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    system("rm -rf a.log");
    system("rm -rf b.log");
}

/*****************************************************************************
 * Description  : 011 创建1022个连接，再连接连接两个逃生通道,等待65秒，验证超时断链2个再连接成功
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_011)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_CONNECTION_FAILURE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 2; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }
    // 1022

    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }
    // +2
    printf("wait 65s for Escape route disconnected,connecte again success\n");
    sleep(65);

    // memset(conn_t,0,sizeof(conn_t));
    GmcDisconnect(conn_t[0]);
    GmcDisconnect(conn_t[1]);
    sleep(CON_SLEEP_TIME);
    // 0/1 free
    // reconn
    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);  // 17003
        printf("escape_route_conn_002 connect success: %d\n", i);
    }
    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_TRUE(conn_t[i]==NULL);
        // printf("escape_route_conn_002 disconnect success: %d\n", i);
    }
}

/*****************************************************************************
 * Description  : 012 创建1022个连接，再连接连接两个逃生通道,等待65秒，验证超时断链连接conn0
 * 断开，再连接conn1断开，再连接conn0,conn1. Input        : None Output       : None Return Value : Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_012)
{
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum + 10];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum + 10) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 2; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }

    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }

    printf("wait 65s for Escape route disconnected,connecte again success\n");
    sleep(65);

    GmcDisconnect(conn_t[0]);
    GmcDisconnect(conn_t[1]);

    sleep(CON_SLEEP_TIME);

    ret = testGmcConnect(&conn_t[0]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_t[0]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn_t[1]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_t[1]);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(CON_SLEEP_TIME);

    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }
    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_TRUE(conn_t[i]==NULL);
        // printf("escape_route_conn_002 disconnect success: %d\n", i);
    }
}

/*****************************************************************************
 * Description  : 013 创建1022个连接，再连接连接两个逃生通道,等待65秒，验证超时断链连接conn0 断开，再连接conn0,conn1.
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_013)
{
    uint32_t existConnNum = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_TIMED_OUT);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"connectTimeout=1\"");  // 开启连接超时检测机制
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum + 10];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum + 10) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        if ((i % 400 == 0) || (i > 1010)) {
            AW_FUN_Log(LOG_INFO, "escape_route_conn_002 connect success: %d\n", i);
        }
    }

    AW_FUN_Log(LOG_INFO, "wait 65s for Escape route disconnected,connecte again success\n");
    sleep(65);
#ifndef ENV_EULER
    GmcDisconnect(conn_t[0]);
    GmcDisconnect(conn_t[1]);
#endif
    sleep(CON_SLEEP_TIME);
    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        if ((i % 400 == 0) || (i > 1010)) {
        AW_FUN_Log(LOG_INFO, "escape_route_conn_002 connect success: %d\n", i);
        }
    }
    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        if ((i % 400 == 0) || (i > 1010)) {
        AW_FUN_Log(LOG_INFO, "escape_route_conn_002 disconnect success: %d\n", i);
        }
    }
    // 关闭连接超时检测机制
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

/*****************************************************************************
 * Description  : 014 创建1022个连接，再连接连接两个逃生通道,等待65秒，验证超时断链连接conn1 断开，再连接conn0,conn1.
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : jiangdingshan/jwx992802   2020.11.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(escape_route_conn, Other_009_014)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_CONNECTION_FAILURE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    GmcConnT *conn_t[MAX_CONN - existConnNum + 10];
    memset(conn_t, 0x00, (MAX_CONN - existConnNum + 10) * sizeof(GmcConnT *));
    GmcConnT *conn = NULL;
    int i;

    for (i = 2; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }

    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }

    printf("wait 65s for Escape route disconnected,connecte again success\n");
    sleep(65);

    GmcDisconnect(conn_t[0]);
    GmcDisconnect(conn_t[1]);
    sleep(CON_SLEEP_TIME);

    for (i = 0; i < 2; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("escape_route_conn_002 connect success: %d\n", i);
    }
    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_TRUE(conn_t[i]==NULL);
        // printf("escape_route_conn_002 disconnect success: %d\n", i);
    }
}
