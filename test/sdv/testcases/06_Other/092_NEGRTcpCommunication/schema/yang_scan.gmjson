[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "uint32", "nullable": true, "default": 1}, {"name": "root_F2", "type": "uint32", "nullable": true, "default": 2}, {"name": "root_F3", "type": "uint32", "nullable": true, "default": 3}, {"name": "root_F4", "type": "uint32", "nullable": true, "default": 4}, {"type": "container", "name": "P_container", "presence": true, "fields": [{"name": "P_F1", "type": "uint32", "nullable": true, "default": 4}, {"name": "P_F2", "type": "uint32", "nullable": true, "default": 5}, {"name": "P_F3", "type": "uint32", "nullable": true, "default": 6}, {"name": "P_F4", "type": "uint32", "nullable": true, "default": 7}]}, {"type": "container", "name": "NP_container", "fields": [{"name": "NP_F1", "type": "uint32", "nullable": true, "default": 7}, {"name": "NP_F2", "type": "uint32", "nullable": true, "default": 8}, {"name": "NP_F3", "type": "uint32", "nullable": true, "default": 9}, {"name": "NP_F4", "type": "uint32", "nullable": true, "default": 10}]}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1_1", "default": true, "fields": [{"name": "case_1_1_F1", "type": "uint32", "nullable": true, "default": 10}, {"name": "case_1_1_F2", "type": "uint32", "nullable": true, "default": 11}, {"name": "case_1_1_F3", "type": "uint32", "nullable": true, "default": 12}, {"name": "case_1_1_F4", "type": "uint32", "nullable": true, "default": 13}]}, {"type": "case", "name": "case_1_2", "fields": [{"name": "case_1_2_F1", "type": "uint32", "nullable": true, "default": 13}, {"name": "case_1_2_F2", "type": "uint32", "nullable": true, "default": 14}, {"name": "case_1_2_F3", "type": "uint32", "nullable": true, "default": 15}, {"name": "case_1_2_F4", "type": "uint32", "nullable": true, "default": 16}]}]}], "keys": [{"node": "main_label", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "main_label", "name": "lk", "fields": ["root_F1"], "index": {"type": "local"}, "constraints": {"unique": true}}, {"node": "main_label", "name": "lhk", "fields": ["root_F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "uint32", "nullable": false}, {"name": "root_F2", "type": "uint32", "nullable": true, "default": 2}, {"name": "root_F3", "type": "uint32", "nullable": true, "default": 3}, {"name": "root_F4", "type": "uint32", "nullable": true, "default": 4}, {"type": "container", "name": "P_container", "presence": true, "fields": [{"name": "P_F1", "type": "uint32", "nullable": true, "default": 4}, {"name": "P_F2", "type": "uint32", "nullable": true, "default": 5}, {"name": "P_F3", "type": "uint32", "nullable": true, "default": 6}, {"name": "P_F4", "type": "uint32", "nullable": true, "default": 7}]}, {"type": "container", "name": "NP_container", "fields": [{"name": "NP_F1", "type": "uint32", "nullable": true, "default": 7}, {"name": "NP_F2", "type": "uint32", "nullable": true, "default": 8}, {"name": "NP_F3", "type": "uint32", "nullable": true, "default": 9}, {"name": "NP_F4", "type": "uint32", "nullable": true, "default": 10}]}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1_1", "default": true, "fields": [{"name": "case_1_1_F1", "type": "uint32", "nullable": true, "default": 10}, {"name": "case_1_1_F2", "type": "uint32", "nullable": true, "default": 11}, {"name": "case_1_1_F3", "type": "uint32", "nullable": true, "default": 12}, {"name": "case_1_1_F4", "type": "uint32", "nullable": true, "default": 13}]}, {"type": "case", "name": "case_1_2", "fields": [{"name": "case_1_2_F1", "type": "uint32", "nullable": true, "default": 13}, {"name": "case_1_2_F2", "type": "uint32", "nullable": true, "default": 14}, {"name": "case_1_2_F3", "type": "uint32", "nullable": true, "default": 15}, {"name": "case_1_2_F4", "type": "uint32", "nullable": true, "default": 16}]}]}], "keys": [{"node": "list_label", "name": "pk", "fields": ["PID", "root_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "list_label", "name": "lk", "fields": ["root_F1"], "index": {"type": "local"}, "constraints": {"unique": true}}, {"node": "list_label", "name": "lhk", "fields": ["root_F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaf-list_label_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "uint32", "nullable": false, "default": [1, 2, 3]}], "keys": [{"node": "leaf-list_label_1", "name": "pk", "fields": ["PID", "root_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "leaf-list_label_1", "name": "lk", "fields": ["root_F1"], "index": {"type": "local"}, "constraints": {"unique": true}}, {"node": "leaf-list_label_1", "name": "lhk", "fields": ["root_F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}]