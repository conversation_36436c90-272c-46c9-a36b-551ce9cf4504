/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#ifndef YANG_SNAPSHOT_READ_TEST_H
#define YANG_SNAPSHOT_READ_TEST_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

const char *g_msConfigTrans =
    "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0, \"auto_increment\":1, \"yang_model\":1}";

// container--container类型的Vertex Name
const char *g_nameConRoot = "Con_root";
const char *g_nameConConChild = "Con_Con_Child";
const char *g_nameConListChild = "Con_List_Child";
const char *g_nameConChoiceChild = "Con_Choice_Child";
const char *g_nameConCaseChild = "Con_Case_Child";
const char *g_nameLevel3Con = "Level3Con";
const char *g_nameLevel3List = "Level3List";

const char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;

void testDropLabelAll(GmcStmtT *stmt)
{
    int ret = 0;

    sleep(1);
    // 删除点
    ret = GmcDropGraphLabel(stmt, g_nameConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testCreateLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;

    readJanssonFile("schema/Con_Con_List_Choice-case.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema/Con_Con_List_Choice-case_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void testCreateLabel2(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/YangV3trans.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_Con_List_Choice-case_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

int testTransStart(GmcConnT *conn, GmcTxConfigT Config, int32_t expret = GMERR_OK)
{
    int ret = 0;

    ret = GmcTransStart(conn, &Config);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(expret, ret);
        return ret;
    }
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int testTransCommit(GmcConnT *conn, int32_t expret = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommit(conn);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(expret, ret);
        return ret;
    }
}

int testTransRollBack(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBack(conn);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t valueSize,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int32_t ret;
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = valueSize;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcYangSetNodeProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

int testYangSetNodeProperty(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t valueSize,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int32_t ret;
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = valueSize;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "GmcYangSetNodeProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}


int testYangSetVertexProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = testYangSetNodeProperty(node, GMC_DATATYPE_UINT32, (void *)&pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangSetNodeProperty(node, GMC_DATATYPE_UINT32, (void *)&valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetNodeProperty(node, GMC_DATATYPE_STRING, (void *)valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

void testSetKeyNameAndValueSelect(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // 普通vertex查询时需要设置PID，Yang模型有专门的查询方式，业务不会这样使用
    if (numPID == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &numPID, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testSelectByKey(GmcStmtT *stmt, const char * vertexName, uint32_t expvalue, uint32_t keyvalue,
    uint32_t numPID = 0, int32_t expresult = GMERR_OK, bool isList = false)
{
    int ret;
    bool isNull;

    ret = testGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSetKeyNameAndValueSelect(stmt, keyvalue, numPID, isList);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验值
    uint32_t valueF1;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &valueF1, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(expresult, ret);
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(expvalue, valueF1);
    }
    AW_FUN_Log(LOG_DEBUG, "vertexName(%s), expvalue(%d), keyvalue(%d), PID(%d), getvalueF1(%d)\n",
        vertexName, expvalue, keyvalue, numPID, valueF1);
}

void testSelectScan(GmcStmtT *stmt, const char * vertexName, uint32_t expvalue, uint32_t keyvalue, uint32_t numPID = 0)
{
    int ret;
    bool isNull;

    ret = testGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        // 校验值
        uint32_t valueID;
        ret = GmcGetVertexPropertyByName(stmt, "ID", &valueID, sizeof(uint32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_FUN_Log(LOG_DEBUG, "ID = %d.\n\n", valueID);

        if (numPID != 0) {
            uint32_t valuePID;
            ret = GmcGetVertexPropertyByName(stmt, "PID", &valuePID, sizeof(uint32_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(false, isNull);
            AW_FUN_Log(LOG_DEBUG, "PID = %d.\n\n", valuePID);
        }

        uint32_t valueF0;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &valueF0, sizeof(uint32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_FUN_Log(LOG_DEBUG, "valueF0 = %d.\n\n", valueF0);

        uint32_t valueF1;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &valueF1, sizeof(uint32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_FUN_Log(LOG_DEBUG, "valueF1 = %d.\n\n", valueF1);
    }
}

int testYangSetVertexProperty_F0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t valueF0 = i;
    ret = testYangSetNodeProperty(node, GMC_DATATYPE_UINT32, (void *)&valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty_All(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool islist = false)
{
    int ret = 0;
    if (!islist) {
        uint32_t valueF0 = i;
        ret = testYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t valueF1 = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t valueF3 = static_cast<int8_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_INT8, &valueF3, sizeof(int8_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t valueF4 = static_cast<int16_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_INT16, &valueF4, sizeof(int16_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t valueF5 = static_cast<int32_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_INT32, &valueF5, sizeof(int32_t), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t valueF6 = static_cast<int64_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_INT64, &valueF6, sizeof(int64_t), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t valueF7 = static_cast<uint8_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_UINT8, &valueF7, sizeof(uint8_t), "F7", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t valueF8 = static_cast<uint16_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_UINT16, &valueF8, sizeof(uint16_t), "F8", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF9 = static_cast<uint32_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &valueF9, sizeof(uint32_t), "F9", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF10 = static_cast<uint64_t>(i);
    ret = testYangSetField(node, GMC_DATATYPE_UINT64, &valueF10, sizeof(uint64_t), "F10", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF11[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_FIXED, valueF11, 7, "F11", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF12[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_BYTES, valueF12, strlen(valueF12), "F12", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool valueF13 = true;
    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &valueF13, sizeof(bool), "F13", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float valueF14 = static_cast<float>(i);
    ret = testYangSetField(node, GMC_DATATYPE_FLOAT, &valueF14, sizeof(float), "F14", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double valueF15 = static_cast<double>(i);
    ret = testYangSetField(node, GMC_DATATYPE_DOUBLE, &valueF15, sizeof(double), "F15", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // 六原语操作时 不 需要设置PID
    if (numPID == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testGetCount(GmcStmtT *stmt, const char * vertexName, uint64_t expcount)
{
    int ret;
    uint64_t count = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, vertexName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expcount, count);
    AW_FUN_Log(LOG_DEBUG, "vertexName(%s), expcount(%d), getcount(%d)\n", vertexName, expcount, count);
}

// Con预先插入数据
void testInsertConRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(conRootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(conRootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void testInsertConConChild(GmcConnT *conn, uint32_t keyvalue, const char * childName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConRoot, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    GmcNodeT *Node = NULL;
    ret = GmcYangEditChildNode(conRootNode, childName, GMC_OPERATION_INSERT, &Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(Node, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(Node, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void testInsertConListChild(GmcConnT *conn, uint32_t keyvalue, const char * childName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConRoot, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key
    testSetKeyNameAndValue(stmt_root, keyvalue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    uint32_t setKeyValue = 0;
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, childName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *Node = NULL;
        ret = GmcGetRootNode(stmt_child, &Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        setKeyValue = i;
        testYangSetVertexProperty_PK(Node, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty(Node, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

void testInsertConChoiceCaseChild(GmcConnT *conn, uint32_t keyvalue, const char * choiceName, const char * caseName)
{
    int ret;
    uint32_t numPID = 0;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child_choice = NULL;
    GmcStmtT *stmt_child_case = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConRoot, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    GmcNodeT *choiceNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, choiceName, GMC_OPERATION_INSERT, &choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    GmcNodeT *caseNode = NULL;
    ret = GmcYangEditChildNode(choiceNode, caseName, GMC_OPERATION_INSERT, &caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(caseNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(caseNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}
bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}

void testYangSubtreeFilterExecute(GmcStmtT *stmt, GmcSubtreeFilterT *filters, const char *subtreeReturnJson)
{
    GmcFetchRetT *fetchRet = NULL;
    int32_t ret = GmcYangSubtreeFilterExecute(stmt, filters, &fetchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ(1, count);
    ASSERT_TRUE(jsonReply != NULL);
    ASSERT_TRUE(testYangJsonIsEqual((const char*)jsonReply[0], subtreeReturnJson));
}

#endif /* YANG_SNAPSHOT_READ_TEST_H */
