#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

// 公共变量
int affectRows = 0;
unsigned int len = 0;

GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

AsyncUserDataT asyncUserData = {0};

#define MAX_LABELNAME_LEN 128

#define MAX_CMD_SIZE 1024
#define MAX_PROPERTY_NAME_LEN 1024
// char g_command[MAX_CMD_SIZE];

// #define g_schUserConnSize (MAX_CONN_SIZE - 2)
// #define g_schUserConnSize 500
// #define g_schUserConnSize_SUB ((g_schUserConnSize - 4)/2)

// MS config
const char *MS_config = "{\"max_record_count\" : 1000000}";

// subName
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

// Vertex Name
const char *VertexName = "Vertex";
const char *KeyName = "Vertex_PK";
const char *HashName = "Vertex_hashcluster";

// using namespace std;

/**************************************Vertex sync*******************************************/
void testCreateMultiLabel(GmcStmtT *stmt)
{
    int ret = 0;

    char LabelName[1024];
    char schema_path[1024];
    for (uint32_t i = 0; i < 100; i++) {
        char *schema_json = NULL;
        sprintf(LabelName, "Vertex_%d", i);
        sprintf(schema_path, "./multi_vertexlabel/Vertex_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);

        ret = GmcCreateVertexLabel(stmt, schema_json, MS_config);
        ASSERT_EQ(GMERR_OK, ret);
        free(schema_json);
    }
}

void testDropMultiLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char LabelName[1024];

    for (uint32_t i = 0; i < 100; i++) {
        sprintf(LabelName, "Vertex_%d", i);
        ret = GmcDropVertexLabel(stmt, LabelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void testCreateMultiLabelNum(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;

    char LabelName[MAX_LABELNAME_LEN];
    char schema_path[1024];

    char *schema_json = NULL;
    sprintf(LabelName, "Vertex_%d", i);
    sprintf(schema_path, "./multi_vertexlabel/Vertex_%d.gmjson", i);
    readJanssonFile(schema_path, &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    ret = GmcCreateVertexLabel(stmt, schema_json, MS_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
}

void testDropMultiLabelNum(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    char LabelName[MAX_LABELNAME_LEN];

    sprintf(LabelName, "Vertex_%d", i);
    ret = GmcDropVertexLabel(stmt, LabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcGetStmtAttr(GmcConnT *conn, GmcStmtT *stmt, int expectAffectRows, uint32_t CycleNum)
{
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    if (ret == GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expectAffectRows, affectRows);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf("GmcGetStmtAttr Fail, CycleNum = %d.\n", CycleNum);
    }
}

void TestGmcSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t PK_value = i;
    ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetVertexProperty_Hash(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t Hash_value = i;
    ret = GmcSetVertexProperty(stmt, "Hash", GMC_DATATYPE_UINT32, &Hash_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetVertexProperty(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    char f8_value[8] = "string";

    int32_t f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f1_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f2_value = value_8;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f3_value = value_u8;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f4_value = true;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BOOL, &f4_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_FLOAT, &f5_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_DOUBLE, &f6_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_TIME, &f7_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, f8_value, (strlen(f8_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FIXED, f8_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcGetVertexPropertyByName(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    char f8_value[8] = "string";
    bool isNull;

    uint32_t f0_value;
    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(i, f0_value);

    int32_t f1_value;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1_value, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(2 * i, f1_value);

    int8_t f2_value;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2_value, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_8, f2_value);

    uint8_t f3_value;
    ret = GmcGetVertexPropertyByName(stmt, "F3", &f3_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(value_u8, f3_value);

    bool f4_value;
    ret = GmcGetVertexPropertyByName(stmt, "F4", &f4_value, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(true, f4_value);

    float f5_value;
    ret = GmcGetVertexPropertyByName(stmt, "F5", &f5_value, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(5 * i, f5_value);

    double f6_value;
    ret = GmcGetVertexPropertyByName(stmt, "F6", &f6_value, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcGetVertexPropertyByName(stmt, "F7", &f7_value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(7 * i, f7_value);

    unsigned int propSize;
    ret = GmcGetVertexPropertySizeByName(stmt, "F8", &propSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(propSize, strlen(f8_value) + 1);

    char string_value[strlen(f8_value)];
    ret = GmcGetVertexPropertyByName(stmt, "F8", &string_value, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(strcmp(string_value, f8_value), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F9", &string_value, 7, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strcmp(string_value, f8_value), 0);
}

void testInsertMultiLabel(GmcConnT *conn, GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;
    uint32_t HashValue = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert vertex
    for (i = 0; i < times; i++) {
        // ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
        // EXPECT_EQ(GMERR_OK, ret);

        // 写数据
        value = initValue + i;
        HashValue = value % 10;
        // printf("HashValue = %d.\n", HashValue);

        TestGmcSetVertexProperty_PK(stmt, value);
        TestGmcSetVertexProperty_Hash(stmt, HashValue);
        TestGmcSetVertexProperty(stmt, value);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcGetStmtAttr(conn, stmt, 1, i);
    }
}

// Update
void testUpdateMultiLabel(GmcConnT *conn, GmcStmtT *stmt, const char *KeyName, uint32_t times, uint32_t initValue,
    uint32_t newValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    for (i = 0; i < times; i++) {
        // ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_UPDATE);
        // EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i + newValue;
        TestGmcSetVertexProperty(stmt, value);

        // update
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcGetStmtAttr(conn, stmt, 1, i);
    }
}

// Update
void testUpdateMultiLabelHash(GmcConnT *conn, GmcStmtT *stmt, const char *KeyName, uint32_t times, uint32_t initValue,
    uint32_t newValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;
    uint32_t HashValue = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    for (i = 0; i < 10; i++) {
        // ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_UPDATE);
        // EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        HashValue = value % 10;
        // printf("HashValue = %d.\n", HashValue);
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i + newValue;
        TestGmcSetVertexProperty(stmt, value);

        // update
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcGetStmtAttr(conn, stmt, (g_schBigData / 10), i);
    }
}

// Delete
void testDeleteMultiLabel(
    GmcConnT *conn, GmcStmtT *stmt, const char *KeyName, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // delete vertex
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcGetStmtAttr(conn, stmt, 1, i);
    }
}

// scan
void testScanMultiLabel(GmcConnT *conn, GmcStmtT *stmt, const char *KeyName, uint32_t times, const char *labelname)
{
    int ret = 0;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish == true) {
            break;
        }
        cnt++;
    }

    if (times != 0) {
        EXPECT_EQ(times, cnt);
    }
}

// Select key
void testSelectMultiLabelKey(
    GmcConnT *conn, GmcStmtT *stmt, const char *KeyName, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // select vertex
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 校验值
        TestGmcGetVertexPropertyByName(stmt, i);
    }
}

// Select Hash
void testSelectMultiLabelHash(GmcConnT *conn, GmcStmtT *stmt, const char *KeyName, const char *labelname)
{
    int ret = 0;
    uint32_t value = 0;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, KeyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish == true) {
            break;
        }
        cnt++;
    }

    if (strncmp(labelname, "Vertex_0", sizeof("Vertex_0")) == 0) {
        printf("cnt = %d\n", cnt);
    }
}

/**************************************KV sync*******************************************/
void testCreateMultiKVTable(GmcStmtT *stmt)
{
    int ret = 0;
    char LabelName[1024];

    for (uint32_t i = 0; i < 100; i++) {
        sprintf(LabelName, "KVTable_%d", i);
        ret = GmcKvCreateTable(stmt, LabelName, MS_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void testDropMultiKVTable(GmcStmtT *stmt)
{
    int ret = 0;
    char LabelName[1024];

    for (uint32_t i = 0; i < 100; i++) {
        sprintf(LabelName, "KVTable_%d", i);
        ret = GmcKvDropTable(stmt, LabelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void testCreateMultiKVTableNum(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    char LabelName[MAX_LABELNAME_LEN];

    // 创建kv表
    sprintf(LabelName, "KVTable_%d", i);
    ret = GmcKvCreateTable(stmt, LabelName, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
}

void testDropMultiKVTableNum(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    char LabelName[MAX_LABELNAME_LEN];

    // 删除kv表
    sprintf(LabelName, "KVTable_%d", i);
    ret = GmcKvDropTable(stmt, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// set KV
void testInsertMultiKVTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // set KV
    for (i = 0; i < times; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, labelname);
        EXPECT_EQ(GMERR_OK, ret);
        GmcKvTupleT kvInfo = {0};
        value = initValue + i;
        char key[32];
        sprintf(key, "zhangsan_%d", i);
        // 设置k-v值
        // kvInfo.key = key;
        // kvInfo.keyLen = strlen(key);
        // kvInfo.value = &value;
        // kvInfo.valueLen = sizeof(uint32_t);
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// Remove
void testDeleteMultiKVTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // Remove KV
    for (i = 0; i < times; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, labelname);
        EXPECT_EQ(GMERR_OK, ret);
        GmcKvTupleT kvInfo = {0};
        value = initValue + i;
        char key[32];
        sprintf(key, "zhangsan_%d", i);
        // 设置k-v值
        // kvInfo.key = key;
        // kvInfo.keyLen = strlen(key);
        // kvInfo.value = &value;
        // kvInfo.valueLen = sizeof(uint32_t);
        ret = GmcKvRemove(stmt, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// get KV
void testSelectMultiKVTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // set KV
    for (i = 0; i < times; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, labelname);
        EXPECT_EQ(GMERR_OK, ret);
        value = initValue + i;
        char key[32];
        sprintf(key, "zhangsan_%d", i);
        // 设置k-v值
        char output[128] = {0};
        uint32_t outputLen = sizeof(uint32_t);
        ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(value, *(uint32_t *)output);
        EXPECT_EQ(sizeof(uint32_t), outputLen);
    }
}

/**************************************Vertex async*******************************************/
void testCreateMultiLabelAsync(GmcStmtT *stmt_async)
{
    int ret = 0;

    char LabelName[1024];
    char schema_path[1024];
    for (uint32_t i = 0; i < 100; i++) {
        AsyncUserDataT asyncUserData = {0};
        char *schema_json = NULL;
        sprintf(LabelName, "Vertex_%d", i);
        sprintf(schema_path, "./multi_vertexlabel/Vertex_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);

        ret = GmcCreateVertexLabelAsync(
            stmt_async, schema_json, MS_config, create_vertex_label_callback, &asyncUserData);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        free(schema_json);
    }
}

void testDropMultiLabelAsync(GmcStmtT *stmt_async)
{
    int ret = 0;
    char LabelName[1024];

    for (uint32_t i = 0; i < 100; i++) {
        AsyncUserDataT asyncUserData = {0};
        sprintf(LabelName, "Vertex_%d", i);
        ret = GmcDropVertexLabelAsync(stmt_async, LabelName, drop_vertex_label_callback, &asyncUserData);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }
}

void testCreateMultiLabelNumAsync(GmcStmtT *stmt_async, uint32_t i)
{
    int ret = 0;

    char LabelName[MAX_LABELNAME_LEN];
    char schema_path[1024];

    AsyncUserDataT asyncUserData = {0};
    char *schema_json = NULL;
    sprintf(LabelName, "Vertex_%d", i);
    sprintf(schema_path, "./multi_vertexlabel/Vertex_%d.gmjson", i);
    readJanssonFile(schema_path, &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    ret = GmcCreateVertexLabelAsync(stmt_async, schema_json, MS_config, create_vertex_label_callback, &asyncUserData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    free(schema_json);
}

void testDropMultiLabelNumAsync(GmcStmtT *stmt_async, uint32_t i)
{
    int ret = 0;
    char LabelName[MAX_LABELNAME_LEN];

    AsyncUserDataT asyncUserData = {0};
    sprintf(LabelName, "Vertex_%d", i);
    ret = GmcDropVertexLabelAsync(stmt_async, LabelName, drop_vertex_label_callback, &asyncUserData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
}

void testInsertMultiLabelAsync(
    GmcConnT *conn_async, GmcStmtT *stmt_async, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;
    uint32_t HashValue = 0;

    // insert vertex
    ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < times; i++) {
        AsyncUserDataT asyncUserData = {0};
        // ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_INSERT);
        // EXPECT_EQ(GMERR_OK, ret);

        // 写数据
        value = initValue + i;
        HashValue = value % 10;
        // printf("HashValue = %d.\n", HashValue);

        TestGmcSetVertexProperty_PK(stmt_async, value);
        TestGmcSetVertexProperty_Hash(stmt_async, HashValue);
        TestGmcSetVertexProperty(stmt_async, value);

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }
}

// Update
void testUpdateMultiLabelAsync(GmcConnT *conn_async, GmcStmtT *stmt_async, const char *KeyName, uint32_t times,
    uint32_t initValue, uint32_t newValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // update vertex
    ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < times; i++) {
        AsyncUserDataT asyncUserData = {0};
        // ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_UPDATE);
        // EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt_async, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i + newValue;
        TestGmcSetVertexProperty(stmt_async, value);

        // update
        ret = GmcSetIndexKeyName(stmt_async, KeyName);
        EXPECT_EQ(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }
}

// Update
void testUpdateMultiLabelHashAsync(GmcConnT *conn_async, GmcStmtT *stmt_async, const char *KeyName, uint32_t times,
    uint32_t initValue, uint32_t newValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;
    uint32_t HashValue = 0;

    // update vertex
    ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 10; i++) {
        AsyncUserDataT asyncUserData = {0};
        // ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_UPDATE);
        // EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        HashValue = value % 10;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt_async, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i + newValue;
        TestGmcSetVertexProperty(stmt_async, value);

        // update
        ret = GmcSetIndexKeyName(stmt_async, KeyName);
        EXPECT_EQ(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ((g_schBigData / 10), asyncUserData.affectRows);
    }
}

// Delete
void testDeleteMultiLabelAsync(GmcConnT *conn_async, GmcStmtT *stmt_async, const char *KeyName, uint32_t times,
    uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < times; i++) {
        AsyncUserDataT asyncUserData = {0};
        // ret = testGmcPrepareStmtByLabelName(stmt_async, labelname, GMC_OPERATION_DELETE);
        // EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt_async, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt_async, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }
}

/**************************************KV async*******************************************/
void testCreateMultiKVTableAsync(GmcStmtT *stmt_async)
{
    int ret = 0;
    char LabelName[1024];

    for (uint32_t i = 0; i < 100; i++) {
        AsyncUserDataT asyncUserData = {0};
        sprintf(LabelName, "KVTable_%d", i);
        ret = GmcKvCreateTableAsync(stmt_async, LabelName, MS_config, create_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }
}

void testDropMultiKVTableAsync(GmcStmtT *stmt_async)
{
    int ret = 0;
    char LabelName[1024];

    for (uint32_t i = 0; i < 100; i++) {
        AsyncUserDataT asyncUserData = {0};
        sprintf(LabelName, "KVTable_%d", i);

        ret = GmcKvDropTableAsync(stmt_async, LabelName, drop_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }
}

void testCreateMultiKVTableNumAsync(GmcStmtT *stmt_async, uint32_t i)
{
    int ret = 0;
    char LabelName[MAX_LABELNAME_LEN];
    AsyncUserDataT asyncUserData = {0};

    // 创建kv表
    sprintf(LabelName, "KVTable_%d", i);
    ret = GmcKvCreateTableAsync(stmt_async, LabelName, MS_config, create_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
}

void testDropMultiKVTableNumAsync(GmcStmtT *stmt_async, uint32_t i)
{
    int ret = 0;
    char LabelName[MAX_LABELNAME_LEN];
    AsyncUserDataT asyncUserData = {0};

    // 删除kv表
    sprintf(LabelName, "KVTable_%d", i);
    ret = GmcKvDropTableAsync(stmt_async, LabelName, drop_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
}

// set KV
void testInsertMultiKVTableAsync(
    GmcConnT *conn_async, GmcStmtT *stmt_async, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // set KV
    for (i = 0; i < times; i++) {
        AsyncUserDataT asyncUserData = {0};
        ret = GmcKvPrepareStmtByLabelName(stmt_async, labelname);
        EXPECT_EQ(GMERR_OK, ret);
        GmcKvTupleT kvInfo = {0};
        value = initValue + i;
        char key[32];
        sprintf(key, "zhangsan_%d", i);
        // 设置k-v值
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSetAsync(stmt_async, &kvInfo, set_kv_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }
}

// Remove
void testDeleteMultiKVTableAsync(
    GmcConnT *conn_async, GmcStmtT *stmt_async, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;

    // Remove KV
    for (i = 0; i < times; i++) {
        AsyncUserDataT asyncUserData = {0};
        ret = GmcKvPrepareStmtByLabelName(stmt_async, labelname);
        EXPECT_EQ(GMERR_OK, ret);
        GmcKvTupleT kvInfo = {0};
        value = initValue + i;
        char key[32];
        sprintf(key, "zhangsan_%d", i);
        // 设置k-v值
        // kvInfo.key = key;
        // kvInfo.keyLen = strlen(key);
        // kvInfo.value = &value;
        // kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvRemoveAsync(stmt_async, key, strlen(key), delete_kv_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }
}

/**************************************sub-func********************************************/
int g_data_num = g_schCycleNum;
int g_subIndex = 0;
bool gIsSnCallbackWait = false;

void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (gIsSnCallbackWait) {
        sleep(1);
    }

    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_LABELNAME_LEN] = {0};
    unsigned int labelNameLen = MAX_LABELNAME_LEN;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_fullsub_abnormal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    uint32_t PK = 0;  // F7是pk
    unsigned int sizeValue = 0;
    bool isNull;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    char labelName[MAX_LABELNAME_LEN] = {0};
    unsigned int labelNameLen = MAX_LABELNAME_LEN;

    // printf("[INFO] vrtxLabelNum is %d\r\n", vrtxLabelNum);

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                // EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);
                printf("[INFO] <---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                printf("[INFO] <---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_LABELNAME_LEN;
            GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            // printf("[INFO] vrtxLabelIdx : %d, labelName : %s\r\n", i, labelName);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // pk = ((int *)user_data->new_value)[g_subIndex];
                    // // printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", pk);
                    // test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    // pk = ((int *)user_data->old_value)[g_subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", pk);
                    // test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // pk = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", pk);
                    // test_checkVertexProperty_sub(subStmt, pk);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    // pk = ((int *)user_data->old_value)[g_subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", pk);
                    // test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // pk = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", pk);
                    // test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcGetVertexPropertySizeByName(subStmt, "F7", &sizeValue);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "F7", &PK, sizeValue, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    // test_checkVertexProperty_sub(subStmt, PK);
                    // printf("[INFO] test_checkVertexProperty_record PK ----> %d\r\n", PK);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
        }
    }
}

/**************************************gmsysview********************************************/
// 获取连接数的个数
int executeCommand_connect(char *cmd, const char *v1, int *connectNum)
{
    (*connectNum) = 0;
    char buffer[1024] = {0};
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(buffer, 500, pf)) {
        if (strstr(buffer, v1)) {
            (*connectNum)++;
        }
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

void getDRT_CONN_STAT(int ConnNum)
{
    int ret = 0;
    int connect_num = 0;
    char g_command[MAX_CMD_SIZE];

    char const *view_name = "V\\$DRT_CONN_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    printf("%s\n", g_command);
    // system(g_command);

    ret = executeCommand_connect(g_command, "index", &connect_num);
    EXPECT_EQ(GMERR_OK, ret);
    printf("connect_num: %d\n", connect_num);
    EXPECT_EQ(ConnNum, connect_num);

    memset(g_command, 0, sizeof(g_command));
}

void getSTORAGE_RES_SESSION_STAT(int Latch_count = 0)
{
    int ret = 0;
    int Latch_count_get = 0;
    char g_command[MAX_CMD_SIZE];

    char const *view_name = "V\\$STORAGE_RES_SESSION_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    printf("%s\n", g_command);
    system(g_command);

    FILE *pf = popen(g_command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", g_command);
    }
    char cmdOutput[64] = {0};
    while (NULL != fgets(cmdOutput, 64, pf)) {
        if (strstr(cmdOutput, "LATCH_COUNT")) {
            Latch_count_get = atoi(cmdOutput);
            printf("LATCH_COUNT = %d\n", Latch_count_get);
            EXPECT_EQ(Latch_count, Latch_count_get);
        }
    }

    pclose(pf);

    memset(g_command, 0, sizeof(g_command));
}

int executeCommand_value(char *cmd, const char *name, uint64_t *value)
{
    char cmdOutput[128] = {0};
    uint64_t getvalue = 0;
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(cmdOutput, 128, pf)) {
        // printf("[1]name:%s, getvalue: %d\n", name, getvalue);
        // printf("[1]cmdOutput:%s, cmdOutput: %d\n", cmdOutput, cmdOutput);
        // if (strstr(cmdOutput, name))
        // {
        getvalue = atoi(cmdOutput);
        // printf("[2]name:%s, getvalue: %lld\n", name, getvalue);
        // printf("[2]cmdOutput:%s, cmdOutput: %d\n", cmdOutput, cmdOutput);
        memcpy(value, &getvalue, sizeof(getvalue));
        // }
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

void getDRT_SND_QUEUE_STAT(const char *name = NULL, uint64_t *value = NULL)
{
    int ret = 0;
    char g_command[MAX_CMD_SIZE];

    if (name == NULL) {
        char const *view_name = "V\\$DRT_SND_QUEUE_STAT";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
        printf("%s\n", g_command);
        // system(g_command);
    } else {
        char const *view_name = "V\\$DRT_SND_QUEUE_STAT";
        snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s | grep %s |awk '{print $2}'", g_toolPath, view_name, name);
        printf("%s\n", g_command);
        system(g_command);

        if ((name != NULL) && (value != NULL)) {
            ret = executeCommand_value(g_command, name, value);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    memset(g_command, 0, sizeof(g_command));
}

void getDRT_SND_QUEUE_STAT_ConnNum(uint64_t ConnNum)
{
    int ret = 0;
    int connect_num = 0;
    char g_command[MAX_CMD_SIZE];

    char const *view_name = "V\\$DRT_SND_QUEUE_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    printf("%s\n", g_command);
    // system(g_command);

    ret = executeCommand_connect(g_command, "DRT_SEND_QUEUE_ID", &connect_num);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[DRT_SND_QUEUE_STAT]Sub connect_num: %d\n", connect_num);
    EXPECT_EQ(ConnNum, connect_num);

    memset(g_command, 0, sizeof(g_command));
}

int executeCommand_value_1(char *cmd, const char *filter, const char *name, uint64_t *value)
{
    char cmdOutput[128] = {0};
    uint64_t getvalue = 0;
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(cmdOutput, 128, pf)) {
        // printf("[1]name:%s, getvalue: %d\n", name, getvalue);
        // printf("[1]cmdOutput:%s, cmdOutput: %d\n", cmdOutput, cmdOutput);
        if (strstr(cmdOutput, filter)) {
            while (NULL != fgets(cmdOutput, 128, pf)) {
                if (strstr(cmdOutput, name)) {
                    getvalue = atoi(cmdOutput);
                    printf("[2]name:%s, getvalue: %lld\n", name, getvalue);
                    // printf("[2]cmdOutput:%s, cmdOutput: %d\n", cmdOutput, cmdOutput);
                    memcpy(value, &getvalue, sizeof(getvalue));
                }
            }
        }
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

void getDRT_SCHEDULE_STAT(const char *name = NULL, uint64_t *value = NULL)
{
    int ret = 0;
    char g_command[MAX_CMD_SIZE];

    if (name == NULL) {
        char const *view_name = "V\\$DRT_SCHEDULE_STAT";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
        printf("%s\n", g_command);
        // system(g_command);
    } else {
        char const *view_name = "V\\$DRT_SCHEDULE_STAT";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s | grep %s |awk '{print $2}'", g_toolPath, g_userName,
            g_connServer, g_passwd, view_name, name);
        printf("%s\n", g_command);
        system(g_command);

        if ((name != NULL) && (value != NULL)) {
            ret = executeCommand_value(g_command, name, value);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    memset(g_command, 0, sizeof(g_command));
}

void getDRT_SCHEDULE_STAT_ConnNum(uint64_t ConnNum)
{
    int ret = 0;
    int connect_num = 0;
    char g_command[MAX_CMD_SIZE];

    char const *view_name = "V\\$DRT_SCHEDULE_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    // printf("%s\n", g_command);
    // system(g_command);

    ret = executeCommand_connect(g_command, "CONN_ID", &connect_num);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[DRT_SCHEDULE_STAT]Connect_num: %d\n", connect_num);
    if (g_envType == 0) {
        EXPECT_EQ(ConnNum, connect_num);
        if (ConnNum != connect_num) {
            system(g_command);
        }
    }

    memset(g_command, 0, sizeof(g_command));
}

int testWaitSnRecvDW(void *userData, GmcSubEventTypeE eventType, int32_t expRecvNum, int timeout = RECV_TIMEOUT,
    bool isAutoReset = true)
{
    int waitCnt = 0, ret = 0, *recvNum = NULL;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char typeMsg[128] = {0};

    switch (eventType) {
        case GMC_SUB_EVENT_INSERT: {
            recvNum = &user_data->insertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INSERT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            recvNum = &user_data->deleteNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_DELETE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_UPDATE: {
            recvNum = &user_data->updateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_REPLACE: {
            recvNum = &user_data->replaceNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_REPLACE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_REPLACE_INSERT: {
            recvNum = &user_data->replaceInsertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_REPLACE_INSERT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_REPLACE_UPDATE: {
            recvNum = &user_data->replaceUpdateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_REPLACE_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MERGE: {
            recvNum = &user_data->mergeNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MERGE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MERGE_INSERT: {
            recvNum = &user_data->mergeInsertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MERGE_INSERT";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MERGE_UPDATE: {
            recvNum = &user_data->mergeUpdateNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MERGE_UPDATE";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_KV_SET: {
            recvNum = &user_data->kvSetNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_KV_SET";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            recvNum = &user_data->scanNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INITIAL_LOAD";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            recvNum = &user_data->scanEofNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_INITIAL_LOAD_EOF";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_AGED: {
            recvNum = &user_data->agedNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_AGED";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN: {
            recvNum = &user_data->triggerScanBeginNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN: {
            recvNum = &user_data->triggerScanNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN_END: {
            recvNum = &user_data->triggerScanEndNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_TRIGGER_SCAN_END";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        case GMC_SUB_EVENT_MODIFY: {
            // 由于存量用例的MODIFY事件推送已使用insertNum, 故不新增modifyNum
            recvNum = &user_data->insertNum;
            char tmpTypeMsg[128] = "GMC_SUB_EVENT_MODIFY";
            memcpy_s(typeMsg, sizeof(tmpTypeMsg), tmpTypeMsg, sizeof(tmpTypeMsg));
            break;
        }
        default: {
            printf("default: invalid eventType\r\n");
            assert(0);
            break;
        }
    }
    struct timeval start;
    struct timeval end;
    unsigned long duration;
    gettimeofday(&start, NULL);

    if (*recvNum != expRecvNum) {
        usleep(500);
        waitCnt++;
        AW_FUN_Log(LOG_INFO, "%s: Expected to receive %d times, actually received %d times",
            typeMsg, expRecvNum, *recvNum);
        ret = 0;
    }
    if (isAutoReset) {
        *recvNum = 0;
    }
    return ret;
}
