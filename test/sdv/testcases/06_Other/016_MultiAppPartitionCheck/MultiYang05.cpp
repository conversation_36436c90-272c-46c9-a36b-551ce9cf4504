#include "gm_multiappcheck_tools.h"

/* ****************************************************************************
 Description  : 分区对账的交互Yang模型用例
 Author       : pwx623912
 Create       : 2021.05.31
**************************************************************************** */

class MultiAppPartitionCheckYang : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int32_t ret = 0;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = 0;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MultiAppPartitionCheckYang::SetUp()
{
    int32_t ret = 0;
    (void)pthread_mutex_init(&LockSubChannel, NULL);
    printf("MultiAppPartitionCheckYang Start.\n");
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TablePartitionComplex_OP_T0.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);

    ASSERT_EQ(GMERR_OK, ret);
    GmcDropGraphLabel(stmt, GraphVertexLabelName1);

    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}
void MultiAppPartitionCheckYang::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    GmcDropVertexLabel(stmt, label_name01);
    GmcDropVertexLabel(stmt, label_name02);
    // 断连同步/异步连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("MultiAppPartitionCheckYang End.\n");
}

class MultiAppPartitionCheckYang1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MultiAppPartitionCheckYang1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}
void MultiAppPartitionCheckYang1::TearDown()
{
    AW_CHECK_LOG_END();
    printf("MultiAppPartitionCheckYang End.\n");
}
/* ****************************************************************************
 Description  : yang模型交互用例, 创建表, 写入数据, 主键查询数据  // 此处用例需要跟踪12000删边
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_131)
{
    int32_t ret = 0;
    uint8_t par_count = 16;
    unsigned int isNull;
    char *normal_graph_vertex_label_schema = NULL;
    char *normal_graph_edge_label_schema = NULL;
    GmcStmtT *stmtVsys = NULL;
    GmcStmtT *stmtRule = NULL;
    GmcStmtT *stmtS_ip = NULL;

    void *vsysLabel = NULL;
    void *ruleLabel = NULL;
    void *s_ipLabel = NULL;
    char GraphEdgeLabelName1[32] = "vsys_rule";
    char GraphEdgeLabelName2[32] = "ruleAndsource_ip";
    int scan_count = 0;

    readJanssonFile("schema_file/NormalGraphVertexLabel.gmjson", &normal_graph_vertex_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_vertex_label_schema);
    readJanssonFile("schema_file/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_edge_label_schema);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    // 创建批量vertexLabel
    ret = GmcCreateGraphVertexLabel(stmt, normal_graph_vertex_label_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建批量edgeLabel
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(normal_graph_vertex_label_schema);
    free(normal_graph_edge_label_schema);

    /* *构造数据v1 ~ v3* */
    ret = GmcAllocStmt(conn, &stmtVsys);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmtRule);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmtS_ip);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入yang模型的三表数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangVsys(stmtVsys, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangRule(stmtRule, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangS_ip(stmtS_ip, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }

    ret = testGmcPrepareStmtByLabelName(stmtVsys, GraphVertexLabelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRule, GraphVertexLabelName2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, GraphVertexLabelName3, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 主键读, 源顶点属性到目标点属性
    int32_t v1_id = 100;
    int64_t K1Value = 100;
    char K0Value = 100;
    uint8_t k2_value;
    uint8_t partition_value_expect = (v1_id / (RECORD_COUNT_2000));
    ret = testGmcPrepareStmtByLabelName(stmtVsys, GraphVertexLabelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmtVsys, GraphEdgeLabelName1);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmtVsys, &isFinish);
        if (isFinish) {
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
        char *v2nameExpectValue = (char *)"string";
        ret = queryPropertyAndCompare(stmtVsys, "name", GMC_DATATYPE_STRING, v2nameExpectValue);
        ASSERT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmtVsys, "K0", GMC_DATATYPE_CHAR, &K0Value);
        ASSERT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmtVsys, "K1", GMC_DATATYPE_INT64, &K1Value);
        ASSERT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmtVsys, "K2", GMC_DATATYPE_UINT8, &partition_value_expect);
        ASSERT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    ASSERT_EQ(1, scan_count);
    ret = GmcDirectFetchNeighborEnd(stmtVsys);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
    // for (int partition = 0; partition < par_count; partition++) {
    //     ret = GmcBeginCheck(stmt, GraphVertexLabelName1, partition);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    // for (int partition = 0; partition < par_count; partition++) {
    //     ret = GmcEndCheck(stmt, GraphVertexLabelName1, partition, false);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    ret = GmcDropGraphLabel(stmt, GraphVertexLabelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 主键查询期间进行对账,对账线程读写更新, 关联查询成功
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_132)
{
    int32_t ret = 0;
    uint8_t par_count = 16;
    unsigned int isNull;
    // 对账异常
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char *normal_graph_vertex_label_schema = NULL;
    char *normal_graph_edge_label_schema = NULL;
    GmcStmtT *stmtVsys = NULL;
    GmcStmtT *stmtRule = NULL;
    GmcStmtT *stmtS_ip = NULL;

    void *vsysLabel = NULL;
    void *ruleLabel = NULL;
    void *s_ipLabel = NULL;
    char GraphEdgeLabelName1[32] = "vsys_rule";
    char GraphEdgeLabelName2[32] = "ruleAndsource_ip";
    int scan_count = 0;

    readJanssonFile("schema_file/NormalGraphVertexLabel.gmjson", &normal_graph_vertex_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_vertex_label_schema);
    readJanssonFile("schema_file/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_edge_label_schema);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    // 创建批量vertexLabel
    ret = GmcCreateGraphVertexLabel(stmt, normal_graph_vertex_label_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建批量edgeLabel
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(normal_graph_vertex_label_schema);
    free(normal_graph_edge_label_schema);

    /* *构造数据v1 ~ v3* */
    ret = GmcAllocStmt(conn, &stmtVsys);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmtRule);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmtS_ip);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入yang模型的三表数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangVsys(stmtVsys, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName2, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangRule(stmtRule, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName3, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangS_ip(stmtS_ip, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, GraphVertexLabelName1, partition);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, GraphVertexLabelName1, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmtVsys, GraphVertexLabelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRule, GraphVertexLabelName2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, GraphVertexLabelName3, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
    ret = WaitCheckEnd(GraphVertexLabelName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropGraphLabel(stmt, GraphVertexLabelName1);
    while (ret == GMERR_LOCK_NOT_AVAILABLE) {
        ret = GmcDropGraphLabel(stmt, GraphVertexLabelName1);
        if (ret == GMERR_OK) {
            break;
        }
    }
}

/* ****************************************************************************
 Description  : 主键查询期间进行对账,对账线程读写更新, cypher查询成功
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_133)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    uint8_t par_count = 16;
    unsigned int isNull;
    char *normal_graph_vertex_label_schema = NULL;
    char *normal_graph_edge_label_schema = NULL;
    GmcStmtT *stmtVsys = NULL;
    GmcStmtT *stmtRule = NULL;
    GmcStmtT *stmtS_ip = NULL;

    void *vsysLabel = NULL;
    void *ruleLabel = NULL;
    void *s_ipLabel = NULL;
    char GraphEdgeLabelName1[32] = "vsys_rule";
    char GraphEdgeLabelName2[32] = "ruleAndsource_ip";
    int scan_count = 0;

    readJanssonFile("schema_file/NormalGraphVertexLabel.gmjson", &normal_graph_vertex_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_vertex_label_schema);
    readJanssonFile("schema_file/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_edge_label_schema);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    // 创建批量vertexLabel
    ret = GmcCreateGraphVertexLabel(stmt, normal_graph_vertex_label_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建批量edgeLabel
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(normal_graph_vertex_label_schema);
    free(normal_graph_edge_label_schema);

    /* *构造数据v1 ~ v3* */
    ret = GmcAllocStmt(conn, &stmtVsys);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmtRule);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmtS_ip);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入yang模型的三表数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangVsys(stmtVsys, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangRule(stmtRule, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", GraphVertexLabelName1, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexYangS_ip(stmtS_ip, partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000,
            partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, GraphVertexLabelName1, partition);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, GraphVertexLabelName1, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmtVsys, GraphVertexLabelName1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRule, GraphVertexLabelName2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, GraphVertexLabelName3, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
    ret = WaitCheckEnd(GraphVertexLabelName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropGraphLabel(stmt, GraphVertexLabelName1);
    while (ret == GMERR_LOCK_NOT_AVAILABLE) {
        ret = GmcDropGraphLabel(stmt, GraphVertexLabelName1);
        if (ret == GMERR_OK) {
            break;
        }
    }
}

/* ****************************************************************************
 Description  : 写入少量数据, 查询建边信息
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_134)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    char *test_schema3 = NULL;

    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TablePartitionComplex_OP_T0.gmjson", stmt, gConFigisFastReadUncommitted, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);

    const char *edgeLabelName = "edgelabel_testEdge";
    readJanssonFile("schema_file/TablePartitioning_edgelabel.gmjson", &test_schema3);
    ASSERT_NE((void *)NULL, test_schema3);
    ret = GmcCreateEdgeLabel(stmt, test_schema3, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema3);
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name01, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_100,
            (partition + 1) * RECORD_COUNT_100, array_num, vector_num, partition, GMC_CONN_TYPE_SYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_100;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_SYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropGraphLabel(stmt, label_name02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 对账前中后, 三次对数据进行二级索引更新(member key)
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_135)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    char *test_schema3 = NULL;
    const char *edgeLabelName = "edgelabel_testEdge";
    uint32_t scan_count = 0;

    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TablePartitionComplex_OP_T0.gmjson", stmt, gConFigisFastReadUncommitted, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/TablePartitioning_edgelabel.gmjson", &test_schema3);
    ASSERT_NE((void *)NULL, test_schema3);
    ret = GmcCreateEdgeLabel(stmt, test_schema3, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema3);
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name01, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_100,
            (partition + 1) * RECORD_COUNT_100, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_100;
    }
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_SYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    char const *view_name_1 = "V\\$STORAGE_EDGE_LABEL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name_1);
    printf("%s\n", g_command);
    GT_SYSTEM(g_command);
    printf("\n");
    ret = executeCommand(g_command, "edgelabel_testEdge", "FIXED_HEAP_ROW_NUM");
    ASSERT_EQ(GMERR_OK, ret);
    // GmcStmtT *stmt1;
    // ret = GmcAllocStmt(conn, &stmt1);
    // AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // bool isFinish = false;
    // // 主键读取数据
    // for (uint32_t loop = 0; loop < par_count*RECORD_COUNT_100; loop++) {
    //     uint32_t vr_id_value = loop;
    //     ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    //     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //     ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    //     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //     ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    //     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //     ret = GmcDirectFetchNeighborBegin(stmt, edgeLabelName);
    //     ASSERT_EQ(GMERR_OK, ret);

    //     while (isFinish == false) {
    //         ret = GmcFetch(stmt, &isFinish);
    //         if(isFinish == true || ret !=0){
    //             break;
    //         }
    //         ASSERT_EQ(GMERR_OK, ret);
    //         scan_count++;
    //     }
    //     ASSERT_EQ(100, scan_count);
    //     // ret = GmcSetIndexKeyName(stmt,  0);
    //     // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //     // TestGmcExecute(stmt);
    //     ret = GmcDirectFetchNeighborEnd(stmt);
    //     ASSERT_EQ(GMERR_OK, ret);

    // }
    ret = GmcDropGraphLabel(stmt, label_name02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 对账前中后, 交互path订阅
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_136)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 对账前中后交互条件删除
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_137)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 对账结束后老化结束前对老化数据进行覆盖写(replace)
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_138)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 对账结束后老化结束前对老化数据进行覆盖写(merge)
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_139)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}

/* ****************************************************************************
 Description  : 开启多分区, 部分分区同步/异步普通写, 部分分区同步/异步批写
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_140)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}

/* ****************************************************************************
 Description  : 多线程对不同分区开启核查，然后删除该分区全部数据，结束核查，预期成功
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_141)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}

/* ****************************************************************************
 Description  : 对账掉图数据多个顶点的数据, 核查边信息
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_142)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}

/* ****************************************************************************
 Description  : 对账掉图数据一个顶点的数据, 核查边信息
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_143)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}

/* ****************************************************************************
 Description  : 对账期间更新子节点 删除子子节点
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_144)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 工具看护对账后信息展现
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_145)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 工具看护对账中信息展现
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_146)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}

/* ****************************************************************************
 Description  : 写满内存后对账老化掉, 循环两次, 查看db内存暂用,无明显升高,数据量相差不大
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_147)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 使用全表对账接口开启核查, 使用全表对账接口结束核查, 预期失败

    // 检查数据量

    //
}
/* ****************************************************************************
 Description  : 工具看护对账前中后信息展现
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_148)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 建表并进行DMl操作
TEST_F(MultiAppPartitionCheckYang1, DISABLED_Client1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    AW_CHECK_LOG_BEGIN(0);
    g_needCheckWhenSucc = false;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    system("touch DISABLED_Client1.txt");
    // 创建同步连接
    ret = testGmcConnect(&conn1, &stmt1);
    ASSERT_EQ(GMERR_OK, ret);
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt1, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10000);
    system("rm -rf DISABLED_Client1.txt");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 建表并进行DMl操作
TEST_F(MultiAppPartitionCheckYang1, DISABLED_Client2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_CHECK_LOG_BEGIN(0);
    int32_t ret = system("kill -9 $(pidof ./MultiYang05_001)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *ThreadStartClient1(void *args)
{
    system("./MultiYang05_001 --gtest_also_run_disabled_tests "
                     "--gtest_filter=MultiAppPartitionCheckYang1.DISABLED_Client1 >Client1.txt");
    sleep(10);
    return NULL;
}
void *ThreadStartClient2(void *args)
{
    int ret = system("./MultiYang05 --gtest_also_run_disabled_tests "
                     "--gtest_filter=MultiAppPartitionCheckYang1.DISABLED_Client2 >Client2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
/* ****************************************************************************
 Description  : 多进程异常退出交互单表分区对账
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_149)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    system("cp MultiYang05 MultiYang05_001");
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
        g_par_record_cnt[partition] = RECORD_COUNT_2000;
    }

    // 断连同步/异步连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, ThreadStartClient1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, ThreadStartClient2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[1], NULL);
    pthread_join(thrArr[0], NULL);
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, label_name01, partition);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, label_name01, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name01, 0);
    ASSERT_EQ(GMERR_OK, ret);
    // 多进程用例屏蔽海量日志检测
    AW_CHECK_LOG_BEGIN(0);
}

void *OP_T0_table_append_vector_thread(void *arg)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *table_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint32_t expect_num = thr_arg->expect_num;
    GmcNodeT *root, *t3;

    TEST_INFO("the table_name is %s. the stard_id is %d, the end_id is %d.\n", table_name, start_id, end_id);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t loop = start_id; loop < end_id; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, table_name, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t f0_value = loop;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 追加 verctor 节点
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(t3, 2, 0, (char *)"cccccc");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TEST_INFO("Thread update by add one vector child opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 特性交互：多线程并发开启对账并发tree表追加更新节点 核查总分区的数据数据,错误场景用例,服务端正常
                prepare 在外面时追加, 同时表扫描数据量感觉异常, 需要支持对账后进行调整跟踪
**************************************************************************** */
TEST_F(MultiAppPartitionCheckYang, Other_016_MultiAppPartitionCheckYang_150)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TablePartitionComplex_OP_T0.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    int array_num = 3;
    int vector_num = 2;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name01, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_100,
            (partition + 1) * RECORD_COUNT_100, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, label_name01, partition);
        ASSERT_EQ(GMERR_OK, ret);
    }

    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name01;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, OP_T0_table_append_vector_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }

    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, label_name01, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name01, par_count * RECORD_COUNT_100);
    ASSERT_EQ(GMERR_OK, ret);
}
