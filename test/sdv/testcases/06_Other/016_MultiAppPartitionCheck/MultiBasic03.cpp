#include "gm_multiappcheck_tools.h"

/* ****************************************************************************
 Description  : 分区对账的基本功能用例
 Author       : pwx623912
 Create       : 2021.05.31
**************************************************************************** */

class MultiAppPartitionCheckOperaBasic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int32_t ret = 0;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = 0;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MultiAppPartitionCheckOperaBasic::SetUp()
{
    int32_t ret = 0;
    (void)pthread_mutex_init(&LockSubChannel, NULL);
    (void)pthread_mutex_init(&LockSubChannel2, NULL);
    printf("MultiAppPartitionCheckOperaBasic Start.\n");
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TablePartitionComplex_OP_T0.gmjson", stmt, g_configJson, label_name01);
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
}
void MultiAppPartitionCheckOperaBasic::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    // 断连同步/异步连接
    GmcDropVertexLabel(stmt, label_name01);
    GmcDropVertexLabel(stmt, label_name02);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MultiAppPartitionCheckOperaBasic End.\n");
}
void *ip4forward_all_table_scan_thread(void *arg)
{
    int ret = 0;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint16_t delta_value = thr_arg->delta_value;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    int8_t conn_type = thr_arg->conn_type;
    uint32_t expect_num = thr_arg->expect_num;
    uint8_t thread_id = thr_arg->thread_id;

    TEST_INFO("Thread table:%s scan opera  %d thread is start. \n", label_name, thread_id);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *lastErrorStr = NULL;
    void *label = NULL;
    bool isFinish = false;
    uint32_t scan_count = 0;
    int8_t loop = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (loop++ > 3 && thr_arg->running) {
        // 获取操作表
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (isFinish) {
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            scan_count++;
        }
        if (expect_num != UNEXPECT)
            EXPECT_EQ(expect_num, scan_count);
        // 关闭vertexLable
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread all table scan opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 简单表异步写数据每分区100条数据, 对账16个分区, 期间循环三次开启100个线程扫描全表, 结束对账
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_051)
{
    int32_t ret = 0;
    uint8_t par_count = 16;
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[THRAD_NUM_128];
    int index[THRAD_NUM_128];
    void *thr_ret[THRAD_NUM_128];
    thread_args thr_args_scan[THRAD_NUM_128] = {0};
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, label_name02, partition);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < THRAD_NUM_128; i++) {
        thr_args_scan[i].table_name = label_name02;
        // thr_args_scan[i].partition = i;
        // thr_args_scan[i].delta_value = i;
        // thr_args_scan[i].start_id = i;
        // thr_args_scan[i].end_id = i;
        // thr_args_scan[i].conn_type = GMC_CONN_TYPE_SYNC;
        thr_args_scan[i].expect_num = par_count * RECORD_COUNT_100;
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_all_table_scan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < THRAD_NUM_128; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = test_read_ip4forward_by_pk(stmt, "primary_key", partition * RECORD_COUNT_100,
            partition * RECORD_COUNT_100 + RECORD_COUNT_100, partition, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, label_name02, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

int fail_start_count = 0;
int fail_end_count = 0;
int32_t labelId = 0;
void *ip4forward_table_start_check_thread(void *arg)
{
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;

    TEST_INFO("Thread table:%s start check opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    EXPECT_EQ((ret == GMERR_OK || ret == GMERR_TABLE_IN_CHECKING), true);
    char exppectLastError[256] = {0};
    sprintf(exppectLastError, "Table is in checking. Vertex label id:%d, status:1", labelId);
    if (ret == GMERR_TABLE_IN_CHECKING) {
        lastErrorStr = GmcGetLastError();
        if (strstr(lastErrorStr, exppectLastError) == NULL) {
            AW_FUN_Log(LOG_STEP, "lastError is %s", lastErrorStr);
            AW_FUN_Log(LOG_STEP, "exppectLastError is %s", exppectLastError);
            EXPECT_EQ(GMERR_OK, 1);
        };
        pthread_mutex_lock(&LockSubChannel);
        fail_start_count++;
        pthread_mutex_unlock(&LockSubChannel);
    }
    sleep(1);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread all table scan opera  %d thread is end. \n", thread_id);
    return NULL;
}
void *ip4forward_table_end_check_thread(void *arg)
{
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    TEST_INFO("Thread table:%s start check opera  %d thread is start. \n", label_name, thread_id);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, label_name, partition, false);
    EXPECT_EQ((ret == GMERR_OK || ret == GMERR_TABLE_NOT_IN_CHECKING), true);
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        lastErrorStr = GmcGetLastError();
        // TEST_INFO("the lasterror is %s.\n", lastErrorStr);
        EXPECT_STREQ(lastErrorStr, "Catalog state mistake when end an account check.");
        pthread_mutex_lock(&LockSubChannel2);
        fail_end_count++;
        pthread_mutex_unlock(&LockSubChannel2);
    }
    sleep(1);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread all table scan opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 多个线程多次对多个分区进行对账开启/结束, 第二次开启/结束失败, 错误的数量为分区的数量(16)
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_052)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TABLE_IN_CHECKING);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_TABLE_NOT_IN_CHECKING);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    uint8_t par_count = 16;
    char cmd[256] = "00";
    (void)sprintf(cmd, "CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s", label_name02);
    labelId = GetViewValueByField(cmd, "VERTEX_LABEL_ID");
    AW_FUN_Log(LOG_STEP, "<labelId: is %d>", labelId);

    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[THRAD_NUM];
    int index[THRAD_NUM];
    void *thr_ret[THRAD_NUM];
    thread_args thr_args_scan[THRAD_NUM] = {0};

    for (int i = par_count * 4; i < THRAD_NUM; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].expect_num = UNEXPECT;  // 不预期值
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_all_table_scan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int partition = 0; partition < par_count; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].thread_id = partition;
        ret =
            pthread_create(&thread_op[partition], NULL, ip4forward_table_start_check_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        thr_args_scan[partition + par_count].table_name = label_name02;
        thr_args_scan[partition + par_count].partition = partition;
        thr_args_scan[partition + par_count].thread_id = partition;
        ret = pthread_create(&thread_op[partition + par_count], NULL, ip4forward_table_start_check_thread,
            &thr_args_scan[partition + par_count]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(5);
    for (int partition = 0; partition < par_count; partition++) {
        thr_args_scan[partition + par_count * 2].table_name = label_name02;
        thr_args_scan[partition + par_count * 2].partition = partition;
        thr_args_scan[partition + par_count * 2].thread_id = partition;
        ret = pthread_create(&thread_op[partition + par_count * 2], NULL, ip4forward_table_end_check_thread,
            &thr_args_scan[partition + par_count * 2]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        thr_args_scan[partition + par_count * 3].table_name = label_name02;
        thr_args_scan[partition + par_count * 3].partition = partition;
        thr_args_scan[partition + par_count * 3].thread_id = partition;
        ret = pthread_create(&thread_op[partition + par_count * 3], NULL, ip4forward_table_end_check_thread,
            &thr_args_scan[partition + par_count * 3]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < THRAD_NUM; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 0);
    ASSERT_LE(fail_start_count, 16);
    ASSERT_LE(fail_end_count, 16);
}
void *ip4forward_table_one_partition_up_version_thread(void *arg)
{
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;

    TEST_INFO("Thread table:%s start update check version opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    int wailtCount = 0;
    for (uint32_t loop = start_id; loop < end_id; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        while (ret == GMERR_LOCK_NOT_AVAILABLE && wailtCount < 10) {
            sleep(2);
            ret = GmcExecute(stmt);
            wailtCount++;
        }
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    ret = GmcEndCheck(stmt, label_name, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 并发16个分区, 分别更新16分区 各2000条数据 的对账版本号
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_053)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_table_one_partition_up_version_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    // 主键读取数据
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        ret = test_read_ip4forward_by_pk(stmt, "primary_key", partition * RECORD_COUNT_100,
            partition * RECORD_COUNT_100 + RECORD_COUNT_100, partition, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *ip4forward_table_one_partition_up_version_rollback_thread(void *arg)
{
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;

    TEST_INFO("Thread table:%s start update check version opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = start_id; loop < end_id; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    ret = GmcEndCheck(stmt, label_name, partition, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 并发16个分区, 分别更新16分区 各 100 条数据 的对账版本号,对账回滚
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_054)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, ip4forward_table_one_partition_up_version_rollback_thread,
            &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    // 主键读取数据
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        ret = test_read_ip4forward_by_pk(stmt, "primary_key", partition * RECORD_COUNT_100,
            partition * RECORD_COUNT_100 + RECORD_COUNT_100, partition, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *ip4forward_table_write_update_age_thread(void *arg)
{  // 假如分区0: 老数据主键0-100, 新数据20000-20100, 更新0-50,老化
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint16_t delta_value = thr_arg->delta_value;
    printf("the start_id is %d.\n", start_id);

    TEST_INFO("Thread table:%s start update check version opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 写入主键20000-20100新数据
    for (unsigned int loop = start_id + 20000; loop < end_id + 20000; loop++) {
        ret = vertex_label_ip4forward_set_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // 主键更新老0-50
    uint16_t value_u16;
    for (uint32_t loop = start_id; loop < start_id + (end_id - start_id) / 2; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        value_u16 = (loop + delta_value) & 0xffff;
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    ret = GmcEndCheck(stmt, label_name, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 16分区预制数据, 16分区开启对账, 写入部分新数据, 更新部分老数据, 部分数据老数据
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_055)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_table_write_update_age_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 2400);  // 每分区(100+50)*16
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 16分区预制数据, 16分区开启对账, 写入部分新数据, 更新部分老数据, 删除新写入的数据, 部分数据老数据
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_056)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_table_write_update_del_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 1600);  // 每分区(100+50-50新的)*16
    ASSERT_EQ(GMERR_OK, ret);
    char const *view_name_1 = "V\\$CATA_VERTEX_LABEL_CHECK_INFO";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name_1);
    do {
        ret = executeCommand(g_command, "PARTITION_ID: 14", "REAL_AGED_CNT: 50", "PARTITION_ID: 15", "CHECK_VERSION: 1",
            "SHOULD_AGED_CNT: 50");
    } while (ret != GMERR_OK);

    view_name_1 = "V\\$QRY_AGE_TASK";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name_1);
    printf("%s\n", g_command);
    GT_SYSTEM(g_command);
    printf("\n");
}
void *ip4forward_table_write_update_del_rollback_thread(void *arg)
{  // 假如分区0: 老数据主键0-100, 新数据20000-20100, 更新0-50,删除20000-20050
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint16_t delta_value = thr_arg->delta_value;
    printf("the start_id is %d.\n", start_id);

    TEST_INFO("Thread table:%s start update check version opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 写入主键20000-20100新数据
    for (unsigned int loop = start_id + 20000; loop < end_id + 20000; loop++) {
        ret = vertex_label_ip4forward_set_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // 主键更新老0-50
    uint16_t value_u16;
    for (uint32_t loop = start_id; loop < start_id + (end_id - start_id) / 2; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        value_u16 = (loop + delta_value) & 0xffff;
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除主键20000-20050新数据
    for (unsigned int loop = start_id + 20000; loop < start_id + (end_id - start_id) / 2 + 20000; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    ret = GmcEndCheck(stmt, label_name, partition, true);  // 回滚, 操作数据同056号线程函数用例
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 16分区预制数据, 16分区开启对账, 写入部分新数据, 更新部分老数据, 删除新写入的数据, 对账回滚数据
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_057)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_table_write_update_del_rollback_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 150 * 16);  // 每分区(100+100-50新的)*16
    ASSERT_EQ(GMERR_OK, ret);
}
void *ip4forward_trans_write_update_del_commit_thread(void *arg)
{  // 假如分区0: 老数据主键0-100, 新数据20000-20100, 更新0-50,删除20000-20050
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint16_t delta_value = thr_arg->delta_value;
    GmcTxConfigT config;
    int wailtCount = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    while (ret == GMERR_LOCK_NOT_AVAILABLE && wailtCount < 1) {
        ret = testGmcGetLastError();
        ret = GmcBeginCheck(stmt, label_name, partition);
        if(!ret){
            wailtCount++;
        }
        usleep(20000);
    }
    EXPECT_EQ(GMERR_OK, ret);
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    wailtCount = 0;
    while (wailtCount < 1) {
        wailtCount = 0;
        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // 写入主键20000-20100新数据
        for (unsigned int loop = start_id + 20000; loop < end_id + 20000; loop++) {
            ret = vertex_label_ip4forward_set_field(stmt, loop);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret) {
                ret = GmcTransRollBack(conn);
                EXPECT_EQ(GMERR_OK, ret);
                wailtCount--;
                usleep(20000);
                break;
            }
        }
        if (wailtCount < 0) {
            usleep(20000);
            continue;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // 主键更新老0-50
        uint16_t value_u16;
        for (uint32_t loop = start_id; loop < start_id + (end_id - start_id) / 2; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            value_u16 = (loop + delta_value) & 0xffff;
            ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret) {
                ret = GmcTransRollBack(conn);
                EXPECT_EQ(GMERR_OK, ret);
                wailtCount--;
                usleep(20000);
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            if (ret) {
                testGmcGetLastError();
            }
        }
        if (wailtCount < 0) {
            usleep(20000);
            continue;
        }

        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除主键20000-20050新数据
        for (unsigned int loop = start_id + 20000; loop < start_id + (end_id - start_id) / 2 + 20000; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcTransCommit(conn);
        if (!ret) {
            usleep(20000);
            wailtCount++;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcEndCheck(stmt, label_name, partition, false);
    while (ret == GMERR_LOCK_NOT_AVAILABLE && wailtCount < 100) {
        usleep(20000);
        wailtCount++;
        ret = GmcEndCheck(stmt, label_name, partition, false);
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
/* ****************************************************************************
 Description  : 16分区预制数据, 16分区开启对账, 开启事务, 写入部分新数据, 更新部分老数据, 删除新写入的数据, 事务commit,
正常老化
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_058)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 100 * 16);  // 每分区(100+100-50新的-50老化)*16
    ASSERT_EQ(GMERR_OK, ret);
}
void *ip4forward_trans_write_update_del_rollback_thread(void *arg)
{  // 假如分区0: 老数据主键0-100, 新数据20000-20100, 更新0-50,删除20000-20050
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint16_t delta_value = thr_arg->delta_value;
    GmcTxConfigT config;
    int wailtCount = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, label_name, partition);
    while ((ret == GMERR_LOCK_NOT_AVAILABLE) && (wailtCount < 10)) {
        usleep(100000);
        ret = GmcBeginCheck(stmt, label_name, partition);
        wailtCount++;
    }
    EXPECT_EQ(GMERR_OK, ret);
    wailtCount = 0;

    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;

    while (wailtCount < 1) {
        wailtCount = 0;
        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // 写入主键20000-20100新数据
        for (unsigned int loop = start_id + 20000; loop < end_id + 20000; loop++) {
            ret = vertex_label_ip4forward_set_field(stmt, loop);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // 主键更新老0-50
        uint16_t value_u16;
        for (uint32_t loop = start_id; loop < start_id + (end_id - start_id) / 2; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            value_u16 = (loop + delta_value) & 0xffff;
            ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret) {
                ret = GmcTransRollBack(conn);
                EXPECT_EQ(GMERR_OK, ret);
                wailtCount--;
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (wailtCount < 0) {

            continue;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除主键20000-20050新数据
        for (unsigned int loop = start_id + 20000; loop < start_id + (end_id - start_id) / 2 + 20000; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret) {
                ret = GmcTransRollBack(conn);
                EXPECT_EQ(GMERR_OK, ret);
                wailtCount--;
                break;
            }
            EXPECT_EQ(GMERR_OK, ret);
            if (ret) {
                testGmcGetLastError();
            }
        }
        ret = GmcTransRollBack(conn);
        if (!ret) {
            wailtCount++;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcEndCheck(stmt, label_name, partition, false);
    while (ret == GMERR_LOCK_NOT_AVAILABLE && wailtCount < 10) {
        ret = GmcEndCheck(stmt, label_name, partition, false);
        sleep(2);
        wailtCount++;
    }

    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 满16分区, 多线程交互事务回滚, 对账流程正常, 期间获取各分区的对账状态, 预制数据被老化, 新数据被回滚
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_059)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_trans_write_update_del_rollback_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 0 * 16);
    ASSERT_EQ(GMERR_OK, ret);
}

void *ip4forward_trans_write_update_del_commit_dml_thread(void *arg)
{  // 假如分区0: 老数据主键0-100, 新数据20000-20100, 更新0-50,删除20000-20050
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint16_t delta_value = thr_arg->delta_value;
    GmcTxConfigT config;

    TEST_INFO("Thread table:%s start update check version opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < 10; loop++) {
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_PESSIMISITIC_TRX;
        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // 写入主键20000-20100新数据
        for (unsigned int loop = start_id + 20000; loop < end_id + 20000; loop++) {
            ret = vertex_label_ip4forward_set_field(stmt, loop);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret != GMERR_OK) {
                goto thread_end;
            }
        }
        // merge不支持设置主键字段的值
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        // merge写入主键0-100老数据
        for (unsigned int loop = start_id; loop < end_id; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = vertex_label_ip4forward_set_field(stmt, loop, 0, false);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret != GMERR_OK) {
                goto thread_end;
            }
        }
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // 主键更新老0-50
        uint16_t value_u16;
        for (uint32_t loop = start_id; loop < start_id + (end_id - start_id) / 2; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            value_u16 = (loop + delta_value) & 0xffff;
            ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            GmcFreeIndexKey(stmt);
            if (ret != GMERR_OK) {
                goto thread_end;
            }
        }
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除主键20000-20050新数据
        for (unsigned int loop = start_id + 20000; loop < end_id + 20000 - 50; loop++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            GmcFreeIndexKey(stmt);
            if (ret != GMERR_OK) {
                goto thread_end;
            }
        }
        if (thread_id % 2) {
            ret = GmcTransCommit(conn);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcTransRollBack(conn);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
thread_end:
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}

/* ****************************************************************************
 Description  : 单独对一个交互单独分区事务操作,未加对账流程, 执行正常, 单独线程单独连接,事务并发, 为062号用例铺垫
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_060)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    for (int partition = 0; partition < 1; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[THRAD_NUM_20 + 1];
    int index[THRAD_NUM_20 + 1];
    void *thr_ret[THRAD_NUM_20 + 1];
    thread_args thr_args_scan[THRAD_NUM_20 + 1] = {0};

    for (int partition = 0; partition < THRAD_NUM_20; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_dml_thread,
            &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = THRAD_NUM_20; i < THRAD_NUM_20 + 1; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].expect_num = UNEXPECT;  // 不预期值
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_all_table_scan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < THRAD_NUM_20 + 1; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    // ret = TestGmcGetVertexInterCount(stmt, label_name02, 150);
    // ASSERT_EQ(GMERR_OK, ret);
}
void *OP_T0_all_partition_write_thread(void *arg)
{
    int ret = 0;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    GmcConnTypeE conn_type = thr_arg->conn_type;
    uint8_t partition = thr_arg->partition;
    uint32_t expect_num = thr_arg->expect_num;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    TEST_INFO("Thread table:%s write opera  %d thread is start. \n", label_name, thread_id);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *lastErrorStr = NULL;
    void *label = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestInsertVertexOP_T0(1, 0, (char *)"string", start_id, end_id, 3, 3, partition, conn_type, GMERR_OK);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入的结果
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Array subscript unsucc. Set eleIndex in node T2, Index (3) exceeds real eleNum (3).");

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread all partition write opera  %d thread is end. \n", thread_id);
    return NULL;
}

void *OP_T0_all_partition_del_thread(void *arg)
{
    int ret = 0;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    GmcConnTypeE conn_type = thr_arg->conn_type;
    uint8_t partition = thr_arg->partition;
    uint32_t expect_num = thr_arg->expect_num;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    TEST_INFO("Thread table:%s write opera  %d thread is start. \n", label_name, thread_id);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *lastErrorStr = NULL;
    void *label = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = FunPkGmcDelVertexOP_T0(stmt, start_id, end_id, label_name01, lalable_name_PK1, GMERR_OK);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入的结果
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "");

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread all partition write opera  %d thread is end. \n", thread_id);
    return NULL;
}
void *all_table_recycle_start_check_end_thread(void *arg)
{
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    int8_t loop = 0;

    TEST_INFO("Thread table:%s start check end opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (loop++ > 3 && thr_arg->running) {
        ret = GmcBeginCheck(stmt, label_name, partition);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(1);
        ret = GmcEndCheck(stmt, label_name, partition, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread start check end opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 多线程对不同分区写入数据, 开启核查，然后核查掉该分区全部数据，结束核查，预期成功
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_061)
{
    int32_t ret = 0;
    pthread_t thread_op[OPERA_PARTITION_16];
    int index[OPERA_PARTITION_16];
    void *thr_ret[OPERA_PARTITION_16];
    thread_args thr_args_scan[OPERA_PARTITION_16] = {0};
    for (int32_t i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].table_name = label_name01;
        thr_args_scan[i].partition = i & 0xff;
        thr_args_scan[i].start_id = i * RECORD_COUNT_2000;
        thr_args_scan[i].end_id = (i + 1) * RECORD_COUNT_2000;
        thr_args_scan[i].conn_type = GMC_CONN_TYPE_SYNC;
        thr_args_scan[i].expect_num = RECORD_COUNT_2000;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, OP_T0_all_partition_write_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name01;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, all_table_recycle_start_check_end_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    for (int32_t i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].table_name = label_name01;
        thr_args_scan[i].partition = i & 0xff;
        thr_args_scan[i].start_id = i * RECORD_COUNT_2000;
        thr_args_scan[i].end_id = (i + 1) * RECORD_COUNT_2000;
        thr_args_scan[i].conn_type = GMC_CONN_TYPE_SYNC;
        thr_args_scan[i].expect_num = RECORD_COUNT_2000;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, OP_T0_all_partition_del_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
}

/* ****************************************************************************
 Description  : 单线程操作单个分区dml, 单线程开启结束对账, 单线程扫描表
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_062)  // DTS2021052906F93AP1O00
{
#if 0
    int32_t ret = 0;
    for (int partition = 0; partition < 1; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", 
                    label_name02, partition, partition*RECORD_COUNT_100, (partition+1)*RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(partition*RECORD_COUNT_100, (partition+1)*RECORD_COUNT_100, 
                                            partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[3];
    int index[3];
    void *thr_ret[3];
    thread_args thr_args_scan[3] = {0};

    for (int partition = 0; partition < 1; partition++){
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_dml_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 1; i < 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].expect_num = UNEXPECT; // 不预期值
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_all_table_scan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 2; partition < 3; partition++){
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, all_table_recycle_start_check_end_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    // ret = TestGmcGetVertexInterCount(stmt, label_name02, 150);
    // ASSERT_EQ(GMERR_OK, ret);
#endif
}

/* ****************************************************************************
 Description  : 多个分区 执行dml, 多分区随机开启结束对账
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_063)  // DTS2021052906F93AP1O00
{
#if 0
    int32_t ret = 0;
    for (int partition = 0; partition < 1; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", 
                    label_name02, partition, partition*RECORD_COUNT_100, (partition+1)*RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(partition*RECORD_COUNT_100, (partition+1)*RECORD_COUNT_100, 
                                            partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[3];
    int index[3];
    void *thr_ret[3];
    thread_args thr_args_scan[3] = {0};

    for (int partition = 0; partition < 1; partition++){
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_dml_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 1; i < 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].expect_num = UNEXPECT; // 不预期值
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_all_table_scan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 2; partition < 3; partition++){
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, all_table_recycle_start_check_end_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    // ret = TestGmcGetVertexInterCount(stmt, label_name02, 150);
    // ASSERT_EQ(GMERR_OK, ret);
#endif
}

void *ip4forward_trans_write_update_truncate_commit_dml_thread(void *arg)
{  // 假如分区0: 老数据主键0-100, 新数据20000-20100, 更新0-50,删除20000-20050
    int ret = 0;
    const char *lastErrorStr = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *label_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    uint16_t delta_value = thr_arg->delta_value;
    GmcTxConfigT config;

    TEST_INFO("Thread table:%s start update check version opera  %d thread is start. \n", label_name, thread_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < 10; loop++) {
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_PESSIMISITIC_TRX;
        ret = GmcTransStart(conn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        // EXPECT_EQ(GMERR_OK, ret);  此处多出来的
        ret = GmcTruncateVertexLabel(stmt, label_name);
        EXPECT_EQ(true, ret == GMERR_OK);
        // EXPECT_EQ(true, ret==GMERR_OK||ret==OBJECT_IN_USE);//OBJECT_IN_USE以删除
        TEST_INFO("The ret is %d. \n", ret);
        if (thread_id % 2) {
            ret = GmcTransCommit(conn);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcTransRollBack(conn);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update check version opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 单个分区 执行dml, truncate 表, 单个分区随机开启结束对账, 主要是测试单分区对账和truncate接口交互,
不能并发 Info:
启动对账和truncate不能并发，因为启动对账的时候会修改元数据上的hasTruncate标记位，truncate也会修改元数据上的hasTruncate标记位
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_064)
{
    int32_t ret = 0;
    for (int partition = 0; partition < 1; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[3];
    int index[3];
    void *thr_ret[3];
    thread_args thr_args_scan[3] = {0};

    for (int partition = 0; partition < 1; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(&thread_op[partition], NULL, ip4forward_trans_write_update_truncate_commit_dml_thread,
            &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 1; i < 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].expect_num = UNEXPECT;  // 不预期值
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_all_table_scan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 2; partition < 3; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = 0;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = 0;
        thr_args_scan[partition].end_id = RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, all_table_recycle_start_check_end_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
}

/* ****************************************************************************
 Description  : 各分区对账过程中, 进行local索引扫描, 由于并发线程扫描和local索引并发扫描, 数据量是不确定的,
但总数确定在线程完毕后 再次对表进行全表扫描
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_065)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16 * 2];
    void *thr_ret[OPERA_PARTITION_16 * 2];
    thread_args thr_args_scan[OPERA_PARTITION_16 * 2] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = OPERA_PARTITION_16; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].partition = i - OPERA_PARTITION_16;
        thr_args_scan[i].start_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100;
        thr_args_scan[i].end_id = (i - OPERA_PARTITION_16 + 1) * RECORD_COUNT_100;
        thr_args_scan[i].expect_num = UNEXPECT;
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_table_localscan_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(5);
    for (int i = 0; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 100 * 16);  // 每分区(100+100-50新的)*16
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name_1 = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name_1);
    do {
        ret = executeCommand(g_command, "ip4forward", "HEAP_ROW_NUM: 1600", "HEAP_PAGE_NUM", "FSM_TOTAL_PAGE_SIZE");
    } while (ret != GMERR_OK);
}

/* ****************************************************************************
 Description  : 各分区对账过程中, 进行local索引删除
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_066)
{
    int32_t ret = 0;
    int array_num = 3;
    int vector_num = 3;
    uint8_t par_count = 16;
    // 同步插入数据
    for (int partition = 0; partition < par_count; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name01, partition,
            partition * RECORD_COUNT_2000, (partition + 1) * RECORD_COUNT_2000);
        ret = TestInsertVertexOP_T0(1, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, partition, GMC_CONN_TYPE_ASYNC, GMERR_OK);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 主键读取数据
    for (int partition = 0; partition < par_count; partition++) {
        ret = FunReadGmcInsertVertexOP_T0(stmt, partition, 0, (char *)"string", partition * RECORD_COUNT_2000,
            (partition + 1) * RECORD_COUNT_2000, array_num, vector_num, label_name01, lalable_name_PK1, true);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcBeginCheck(stmt, label_name01, partition);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 索引更新数据
    ret = FunUpdateGmcVertexOP_T0(stmt, 1, (char *)"string2", 0, par_count * RECORD_COUNT_2000, array_num, vector_num,
        label_name01, lalable_name_PK1);
    ret = FunLocalGmcDelVertexOP_T0(stmt, 0, par_count * RECORD_COUNT_2000 / 2,
        (par_count * RECORD_COUNT_2000 / 2) + 1);  // 闭区间需要加边缘1
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < par_count; partition++) {
        ret = GmcEndCheck(stmt, label_name01, partition, false);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name01, (par_count * RECORD_COUNT_2000 / 2) - 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FunLocalGmcDelVertexOP_T0(stmt, 0,
        par_count * RECORD_COUNT_2000 / 2);  // 此处需要定位, local范围删除, 错误码返回4000,预期成功
    ASSERT_EQ(GMERR_OK, ret);
}
class MultiAppPartitionCheckOperaBasic1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int32_t ret = 0;
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = 0;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MultiAppPartitionCheckOperaBasic1::SetUp()
{
    int32_t ret = 0;
    (void)pthread_mutex_init(&LockSubChannel, NULL);
    (void)pthread_mutex_init(&LockSubChannel2, NULL);
    printf("MultiAppPartitionCheckOperaBasic Start.\n");
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TablePartitionComplex_OP_T0.gmjson", stmt, g_configJson, label_name01);
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
}
void MultiAppPartitionCheckOperaBasic1::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    // 断连同步/异步连接
    GmcDropVertexLabel(stmt, label_name01);
    GmcDropVertexLabel(stmt, label_name02);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MultiAppPartitionCheckOperaBasic End.\n");
}

/* ****************************************************************************
 Description  : 各分区对账过程中, 进行并发线程local索引删除
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic1, Other_016_MultiAppPartitionCheckOperaBasic_067)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16 * 2];
    int index[OPERA_PARTITION_16 * 2];
    void *thr_ret[OPERA_PARTITION_16 * 2];
    thread_args thr_args_scan[OPERA_PARTITION_16 * 2] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(3);
    for (int i = OPERA_PARTITION_16; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].partition = i - OPERA_PARTITION_16;
        thr_args_scan[i].start_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100;
        thr_args_scan[i].end_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100 + RECORD_COUNT_100;
        thr_args_scan[i].expect_num = UNEXPECT;
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_table_localdel_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(5);
    for (int i = 0; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 100 * OPERA_PARTITION_16);  // 每分区(100+100-50新的)*16
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name_1 = "V\\$QRY_DML_OPER_STATIS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name_1);
    ret = executeCommand(g_command, "LABEL_NAME: ip4forward", "SUCCESS_COUNT");
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 交互GmcGetVertexCount接口, 查看是否准确, 此接口受对账影响,数据不可信
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_068)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, gConFigisFastReadUncommitted, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16 * 2];
    int index[OPERA_PARTITION_16 * 2];
    void *thr_ret[OPERA_PARTITION_16 * 2];
    thread_args thr_args_scan[OPERA_PARTITION_16 * 2] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].delta_value = 20000;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_trans_write_update_del_commit_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(3);
    for (int i = OPERA_PARTITION_16; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].partition = i - OPERA_PARTITION_16;
        thr_args_scan[i].start_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100;
        thr_args_scan[i].end_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100 + RECORD_COUNT_100;
        thr_args_scan[i].expect_num = UNEXPECT;
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_table_localdel_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 100 * OPERA_PARTITION_16);  // 每分区(100+100-50新的)*16
    ASSERT_EQ(GMERR_OK, ret);

    ret = FuncGmcGetVertexCount(label_name02, NULL, NULL);  // 此接口调用未走服务端不能保证数据的准确性,目前是2350
    ASSERT_EQ(GMERR_OK, ret);
}

void *ip4forward_table_pk_del_thread(void *arg)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *label = NULL;
    thread_args *thr_arg = (thread_args *)arg;
    char *table_name = thr_arg->table_name;
    uint8_t partition = thr_arg->partition;
    uint8_t thread_id = thr_arg->thread_id;
    uint32_t start_id = thr_arg->start_id;
    uint32_t end_id = thr_arg->end_id;
    TEST_INFO("the table_name is %s. the stard_id is %d, the end_id is %d.\n", table_name, start_id, end_id);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, table_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = start_id; loop < end_id; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread del pk  opera  %d thread is end. \n", thread_id);
    return NULL;
}
/* ****************************************************************************
 Description  : 对账期间更新版本号, 交互主键删除线程
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_069)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16 * 2];
    int index[OPERA_PARTITION_16 * 2];
    void *thr_ret[OPERA_PARTITION_16 * 2];
    thread_args thr_args_scan[OPERA_PARTITION_16 * 2] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_table_one_partition_up_version_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(2);
    for (int i = OPERA_PARTITION_16; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].table_name = label_name02;
        thr_args_scan[i].partition = i - OPERA_PARTITION_16;
        thr_args_scan[i].start_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100;
        thr_args_scan[i].end_id = (i - OPERA_PARTITION_16) * RECORD_COUNT_100 + RECORD_COUNT_100;
        thr_args_scan[i].expect_num = UNEXPECT;
        thr_args_scan[i].running = 1;
        thr_args_scan[i].thread_id = i;
        ret = pthread_create(&thread_op[i], NULL, ip4forward_table_pk_del_thread, &thr_args_scan[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < OPERA_PARTITION_16 * 2; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }

    ret = TestGmcGetVertexInterCount(stmt, label_name02, 0 * 16);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 对账期间更新版本号, 交互主键删除单独线程执行
**************************************************************************** */
TEST_F(MultiAppPartitionCheckOperaBasic, Other_016_MultiAppPartitionCheckOperaBasic_070)
{
    int32_t ret = 0;
    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        TEST_INFO("table %s prepare data, write with partition:%d, index:[%d , %d)", label_name02, partition,
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100);
        ret = test_insert_vertex_ip4forward(
            partition * RECORD_COUNT_100, (partition + 1) * RECORD_COUNT_100, partition, GMERR_OK, GMC_CONN_TYPE_ASYNC);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t thread_op[OPERA_PARTITION_16 + 1];
    int index[OPERA_PARTITION_16 + 1];
    void *thr_ret[OPERA_PARTITION_16 + 1];
    thread_args thr_args_scan[OPERA_PARTITION_16 + 1] = {0};

    for (int partition = 0; partition < OPERA_PARTITION_16; partition++) {
        thr_args_scan[partition].table_name = label_name02;
        thr_args_scan[partition].partition = partition;
        thr_args_scan[partition].thread_id = partition;
        thr_args_scan[partition].start_id = partition * RECORD_COUNT_100;
        thr_args_scan[partition].end_id = (partition + 1) * RECORD_COUNT_100;
        ret = pthread_create(
            &thread_op[partition], NULL, ip4forward_table_one_partition_up_version_thread, &thr_args_scan[partition]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    thr_args_scan[OPERA_PARTITION_16].table_name = label_name02;
    thr_args_scan[OPERA_PARTITION_16].partition = 0;
    thr_args_scan[OPERA_PARTITION_16].thread_id = 17;
    thr_args_scan[OPERA_PARTITION_16].start_id = 0;
    thr_args_scan[OPERA_PARTITION_16].end_id = OPERA_PARTITION_16 * RECORD_COUNT_100;
    ret = pthread_create(
        &thread_op[OPERA_PARTITION_16], NULL, ip4forward_table_pk_del_thread, &thr_args_scan[OPERA_PARTITION_16]);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < OPERA_PARTITION_16 + 1; i++) {
        thr_args_scan[i].running = 0;
        pthread_join(thread_op[i], &thr_ret[i]);
    }
    ret = TestGmcGetVertexInterCount(stmt, label_name02, 0 * 16);
    ASSERT_EQ(GMERR_OK, ret);
}
