/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
#ifndef PERSIST_DEMAND_PRODUCT_H
#define PERSIST_DEMAND_PRODUCT_H
 
#ifdef FEATURE_PERSISTENCE
 
#include <sys/ipc.h>
#include <sys/shm.h>
#include <zlib.h>
#include "t_light.h"
#include "t_datacom_lite.h"
#include "gms.h"
 
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
 
int32_t GmdbPersistCompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    std::vector<uint8_t> buf(compressBound(srcSize), 0);
    unsigned long outLen = buf.size();
    auto ret = compress2(buf.data(), &outLen, src, srcSize, Z_BEST_SPEED);
    if (outLen > *destSize) {
        return -1;
    }
 
    (void)memcpy_s(dest, *destSize, buf.data(), outLen);
    *destSize = outLen;
    return ret;
}
 
int32_t GmdbPersistDecompressCallBack(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    unsigned long outLen = *destSize;
    auto ret = uncompress(dest, &outLen, src, srcSize);
    *destSize = outLen;
    return ret;
}
 
int32_t GmdbPersistFileNameFilterCallBack(const char *src, char *dest, uint32_t destSize)
{
    uint32_t i = 0;
    for (i = 0; src[i] != '\0'; i++) {
        dest[i] = tolower(src[i]);
    }
    dest[i] = '\0';
    return GMERR_OK;
}
 
int32_t GmsRegAdaptFuncsProc()
{
    GmsAdptFuncsT adpt = {0};
    adpt.persistCompressFunc = (GmsPersistCompressFuncT)GmdbPersistCompressCallBack;
    adpt.persistDecompressFunc = (GmsPersistDecompressFuncT)GmdbPersistDecompressCallBack;
    adpt.persistFileNameFilterFunc = (GmsPersistFileNameFilterFuncT)GmdbPersistFileNameFilterCallBack;
    return GmsRegAdaptFuncs(&adpt);
}
 
void getTimeString(char *strBuff, uint32_t size)
{
    time_t t;
    struct tm *lt;
    (void)time(&t);
    lt = localtime(&t);
    (void)sprintf_s(strBuff, size, "%4u%02u%02u %02u:%02u:%02u", lt->tm_year+1900, lt->tm_mon + 1,
        lt->tm_mday, lt->tm_hour, lt->tm_min, lt->tm_sec);
}
 
typedef enum {
    LONG_STABILITY_LOG_INFO = 0,
    LONG_STABILITY_LOG_ERR
} LOG_LEVEL_E;
const char *g_logLevelStr[2] = { "INFO ", "ERROR" };
FILE *g_logFp = NULL;
 
#define BOOL_BY_UINT32(uint32_val) ((uint32_val) % 2)
#define UINT16_BY_UINT32(uint32_val) ((uint32_val) % 65535)
 
int32_t debugModelLogInit()
{
    system("rm -f persistDemandProduct_run.log");
    g_logFp = fopen("persistDemandProduct_run.log", "ab+");
    if (NULL == g_logFp) {
        return -1;
    }
    return 0;
}
 
void debugModelLogClose()
{
    (void)fclose(g_logFp);
}
 
// 长稳信息打印，需要记录在某个文件里，不在屏幕上输出，用于调试长稳
void __attribute__((format(printf, 3, 4))) longStabilityLog(FILE *logFp, LOG_LEVEL_E logLevel,
    const char *format, ...)
{
    char printfBuf[2048] = { 0 };
 
    // 获取格式化参数
    va_list args;
    va_start(args, format);
    (void)vsprintf(printfBuf, format, args);
    va_end(args);
 
    // 获取时间
    char timeStr[100] = { 0 };
    getTimeString(timeStr, sizeof(timeStr));
 
    // 日志输出到指定位置，不初始化日志文件的情况下输出到屏幕
    if (logFp != NULL) {
        fprintf(logFp, "[ %s ][ %s ] %s\n", timeStr, g_logLevelStr[logLevel], printfBuf);
        fflush(logFp);
    } else {
        printf("[ %s ][ %s ] %s\n", timeStr, g_logLevelStr[logLevel], printfBuf);
    }
}
 
// 不符合预期就退出进程
#define EXIT_WHEN_NOT_EQ(a, b)    \
do {    \
    if (((a) != (b))) { \
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s:%d, Expected: %d, Actual: %d, proc exit",    \
            __FILE__, __LINE__, (a), (b));  \
        exit(-1);   \
    }   \
} while (0)
 
// 用例是共进程的，报错之后不能退出，退出会导致服务退出，无法定位问题
#define WHILE_1_WHEN_NOT_EQ(a, b)    \
do {    \
    if (((a) != (b))) { \
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s:%d, Expected: %d, Actual: %d, pthread while loop",    \
            __FILE__, __LINE__, (a), (b));  \
        while (1) { \
            sleep(600); \
        }   \
    }   \
} while (0)
 
#define RETURN_WHEN_NOT_EQ(a, b) \
do {    \
    int32_t tmpExpect = (a); \
    int32_t tmpActual = (b); \
    if (tmpExpect != tmpActual) { \
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s:%d, Expected: %d, Actual: %d",    \
            __FILE__, __LINE__, (a), (b));  \
        return tmpActual;  \
    }   \
} while (0)
 
#define ERRLOG_WHEN_NOT_EQ(a, b) \
do {    \
    int32_t tmpExpect = (a); \
    int32_t tmpActual = (b); \
    if (tmpExpect != tmpActual) { \
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s:%d, Expected: %d, Actual: %d",    \
            __FILE__, __LINE__, (a), (b));  \
    }   \
} while (0)
 
#define ERRLOG_WHEN_PTR_NULL(ptr) \
do {    \
    if (ptr == NULL) { \
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s:%d, ptr is NULL", __FILE__, __LINE__);  \
    }   \
} while (0)
 
int32_t testGetCmdResult(const char *cmd, char *result, uint32_t len)
{
    if (result == NULL) {
        return -1;
    }
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
 
    char *cmdOutput = result;
    while (fgets(cmdOutput, len, pf) != NULL) {
    }
    for (uint32_t i = 0; i < len; i++) {
        if (cmdOutput[i] == '\n') {
            cmdOutput[i] = '\0';
        }
        if (cmdOutput[i] == '\0') {
            break;
        }
    }
    pclose(pf);
 
    return 0;
}
 
int32_t checkPersistenceFile(char *persistDir, uint32_t persistFileCnt)
{
    int32_t ret;
    char cmd[200] = { 0 };
    char result[10] = { 0 };
 
    // 判断持久化目录是否存在，不存在的情况下进行报错，强制需要持久化目录
    if (access(persistDir, F_OK) != 0) {
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s failed, persistence dir(%s) not exist",
            __func__, persistDir);
        return -1;
    }
 
    (void)sprintf_s(cmd, sizeof(cmd), "ls %s | wc -l", persistDir);
    ret = testGetCmdResult(cmd, result, sizeof(result));
    RETURN_WHEN_NOT_EQ(GMERR_OK, ret);
 
    if (atoi(result) != persistFileCnt) {
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "%s failed, expect persistFileCnt(%u), actual(%u)",
            __func__, persistFileCnt, atoi(result));
        return -1;
    }
 
    return 0;
}
 
int32_t replaceCfgFile(char *destCfgFile, char *srcCfgFile)
{
    if (access(destCfgFile, F_OK) != 0) {
        longStabilityLog(g_logFp, LONG_STABILITY_LOG_ERR, "destCfgFile(%s) not exist.", destCfgFile);
        return -1;
    }
    char cmd[512] = { 0 };
    (void)sprintf_s(cmd, sizeof(cmd), "cp -f %s %s.bak; rm -f %s", srcCfgFile, srcCfgFile, srcCfgFile);
    system(cmd);
    longStabilityLog(g_logFp, LONG_STABILITY_LOG_INFO, "srcCfgFile(%s) copy to %s.bak", srcCfgFile, srcCfgFile);
    (void)sprintf_s(cmd, sizeof(cmd), "cp -f %s %s", destCfgFile, srcCfgFile);
    system(cmd);
 
    return 0;
}
 
long readFileContent(char const *path, char **buf, bool add_nul)
{
    FILE* fp;
    size_t fsz;
    long off_end;
    int rc;
    char *pBuffer = NULL;
    /* Open the file */
    fp = fopen(path, "rb");
    if (NULL == fp) {
        return -1L;
    }
    /* Seek to the end of the file */
    rc = fseek(fp, 0L, SEEK_END);
    if (0 != rc) {
        fclose(fp);
        return -1L;
    }
    /* Byte offset to the end of the file (size) */
    if (0 > (off_end = ftell(fp))) {
        fclose(fp);
        return -1L;
    }
    fsz = (size_t)off_end;
    /* Allocate a buffer to hold the whole file */
    pBuffer = (char*)malloc(fsz + (int)add_nul);
    if (NULL == pBuffer) {
        fclose(fp);
        return -1L;
    }
    /* Rewind file pointer to start of file */
    rewind(fp);
    /* Slurp file into buffer */
    if (fsz != fread(pBuffer, 1, fsz, fp)) {
        free(pBuffer);
        fclose(fp);
        return -1L;
    }
    /* Close the file */
    if (EOF == fclose(fp)) {
        free(pBuffer);
        return -1L;
    }
    if (add_nul) {
        /* Make sure the buffer is NUL-terminated, just in case */
        pBuffer[fsz] = '\0';
    }
    *buf = pBuffer;
    /* Return the file size */
    return (long)fsz;
}
 
bool isHasStrOnFile(const char *file, char *checkStr, uint32_t *findCnt)
{
    char *buff = NULL;
    readFileContent(file, &buff, true);
    bool isHasStr = false;
    char *p1 = buff;
    char *p2 = NULL;
    uint32_t cnt = 0;
    do {
        p2 = strstr(p1, checkStr);
        if (p2 != NULL) {
            isHasStr = true;
            cnt++;
            p1 = p2 + strlen(checkStr);
        }
    } while (p2 != NULL);
    free(buff);
    *findCnt = cnt;
 
    return isHasStr;
}
 
int32_t getVertexCnt()
{
    char cmd[512] = { 0 };
    char result[20] = { 0 };
    (void)sprintf_s(cmd, sizeof(cmd), "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep 'index =' | wc -l");
    int32_t ret = testGetCmdResult(cmd, result, sizeof(result));
    if (ret != 0) {
        return ret;
    }
 
    return atoi(result);
}
 
#endif
 
#endif
