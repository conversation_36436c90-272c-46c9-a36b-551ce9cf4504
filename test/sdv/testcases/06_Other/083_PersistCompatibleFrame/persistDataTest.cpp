/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 8k典配业务持久化数据恢复后操作，共进程用例
 * Author: zu<PERSON>hao
 * Create: 2024-08-22
 */
#include "tools.h"
#include <fstream>

class persistDataTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void persistDataTest::SetUpTestCase()
{
    RebuildPstFolder();
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb8k", pwdDir);
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);    
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh gmdb8k");
    system(g_command);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"workerHungThreshold", "20,299,300"));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"trxMonitorThreshold", "999,1000"));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"localLocatorListened", "usocket:/run/verona/unix_emserver"));

    int ret = 0;
    g_isDone = false;
    ret = pthread_create(&g_thrDoClock, NULL, Clock, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注册持久化文件的解压缩和修改文件名的函数，文件是压缩，名字小写的，需要共进程注册回调函数处理
    ret = GmsRegAdaptFuncsProc();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = CheckImSameProcess();
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void persistDataTest::TearDownTestCase()
{
    int ret = 0;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(g_stmt_root);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command); 
    CleanPstFolder();
}
void persistDataTest::SetUp()
{
    system("stop.sh -f");
    system("echo 'This is checkfile' > ./replyCheck/perDbPath");
    system("rm -rf ./replyCheck/*.txt");
    AW_CHECK_LOG_BEGIN();
    g_writeTimes = 0;
}
void persistDataTest::TearDown()
{
    g_isDone = true;
    pthread_join(g_thrDoClock, NULL);
    system("echo 'This is checkfile' > ./replyCheck/perDbPath");
    system("rm -rf ./replyCheck/*.txt");
    system("rm -rf ./reply/*.txt");
    AW_CHECK_LOG_END();
}

// 048.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定ont数据，靠前的记录
TEST_F(persistDataTest, Other_083_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *filedname = "v-ani.0.1.0.0";
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, (void *)filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOnt";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 049.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定业务流数据，靠前的记录
TEST_F(persistDataTest, Other_083_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *filedname = "vlan-sub-interface.0.1.0.3.12";
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, (void *)filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *checkFileName = "GetOneSubInterface";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 050.恢复业务典配8k持久化数据后，hardware模型，subtree查询指定ont端口，靠前的记录
TEST_F(persistDataTest, Other_083_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "hardware";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "hw:component.1";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *filedname = "port.0.1.0.0.eth.1";
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, (void *)filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOntPort";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 051.恢复业务典配8k持久化数据后，bbf-mgmd：multicast模型，subtree查询指定组播用户，靠前的记录
TEST_F(persistDataTest, Other_083_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-mgmd";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "multicast";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "mgmd";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-mgmd:multicast-vpn.1";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *T3filedname = "mvlan100";
    ret = testsubtreeSetvalue(T3List, GMC_DATATYPE_STRING, (void *)T3filedname, strlen(T3filedname), "name",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T4List = NULL;
    const char *SubT4List = "bbf-mgmd:multicast-interface-to-host.1";
    ret = GmcYangEditChildNode(T3List, SubT4List, GMC_OPERATION_SUBTREE_FILTER, &T4List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *filedname = "vlan-sub-interface.*******.36";
    ret = testsubtreeSetvalue(
        T4List, GMC_DATATYPE_STRING, (void *)filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneMulticast";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 052.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定ont数据，靠后的记录
TEST_F(persistDataTest, Other_083_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *filedname = "v-ani.0.8.9.62";
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, (void *)filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOntLast";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 053.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定业务流数据，靠后的记录
TEST_F(persistDataTest, Other_083_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[35] = "";
    strcpy(filedname, "vlan-sub-interface.0.8.15.56.30691");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneSubInterfaceLast";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 054.恢复业务典配8k持久化数据后，hardware模型，subtree查询指定ont端口，靠后的记录
TEST_F(persistDataTest, Other_083_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "hardware";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "hw:component.1";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[21] = "";
    strcpy(filedname, "port.0.8.15.51.eth.4");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOntPortLast";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 055.恢复业务典配8k持久化数据后，bbf-mgmd：multicast模型，subtree查询指定组播用户，靠后的记录
TEST_F(persistDataTest, Other_083_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-mgmd";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "multicast";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "mgmd";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-mgmd:multicast-vpn.1";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char T3filedname[9] = "";
    strcpy(T3filedname, "mvlan100");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &T3filedname, strlen(T3filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T4List = NULL;
    const char *SubT4List = "bbf-mgmd:multicast-interface-to-host.1";
    ret = GmcYangEditChildNode(T3List, SubT4List, GMC_OPERATION_SUBTREE_FILTER, &T4List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[35] = "";
    strcpy(filedname, "vlan-sub-interface.*********.30616");
    ret = testsubtreeSetvalue(
        T4List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneMulticastLast";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 056.恢复业务典配8k持久化数据后，hardware模型，subtree查询指定单板
TEST_F(persistDataTest, Other_083_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "hardware";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "hw:component.1";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[35] = "";
    strcpy(filedname, "board.0.4");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneBoard";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 057.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定ani
TEST_F(persistDataTest, Other_083_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[15] = "";
    strcpy(filedname, "ani.0.6.5.0");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneAni";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 058.恢复业务典配8k持久化数据后，bbf-link-table模型，subtree查询指定link-table
TEST_F(persistDataTest, Other_083_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-link-table";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "link-table";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "bbf-lt:link-table.2";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[15] = "";
    strcpy(filedname, "ani.0.8.3.4");
    ret = testsubtreeSetvalue(T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "from-interface",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneLinkTable";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 059.恢复业务典配8k持久化数据后，bbf-xpongemtcont模型，subtree查询指定tcont
TEST_F(persistDataTest, Other_083_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "xpongemtcont";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "tconts";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-xpongemtcont:tcont.2";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char T3filedname[18] = "";
    strcpy(T3filedname, "tcont.0.8.15.1.2");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &T3filedname, strlen(T3filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneTcont";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 060.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定olt-v-enet
TEST_F(persistDataTest, Other_083_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[22] = "";
    strcpy(filedname, "olt-v-enet.0.8.12.1.3");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOltVenet";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 061.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定onu-v-enet
TEST_F(persistDataTest, Other_083_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[22] = "";
    strcpy(filedname, "onu-v-enet.0.6.15.1.1");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOnuVenet";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 062.恢复业务典配8k持久化数据后，bbf-link-table模型，subtree查询指定onu和olt的link-table
TEST_F(persistDataTest, Other_083_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-link-table";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "link-table";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "bbf-lt:link-table.2";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[22] = "";
    strcpy(filedname, "onu-v-enet.0.7.1.2.1");
    ret = testsubtreeSetvalue(T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "from-interface",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOnuLinkTable";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 063.恢复业务典配8k持久化数据后，bbf-xpongemtcont模型，subtree查询指定gemport
TEST_F(persistDataTest, Other_083_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "xpongemtcont";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "gemports";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-xpongemtcont:gemport.2";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char T3filedname[22] = "";
    strcpy(T3filedname, "gemport.0.6.9.1.2");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &T3filedname, strlen(T3filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneGemport";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 064.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定ontgem
TEST_F(persistDataTest, Other_083_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[39] = "";
    strcpy(filedname, "vlan-sub-interface.0.5.4.33.ontgem.1.0");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOntgem";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 065.恢复业务典配8k持久化数据后，bbf-l2-forwarding模型，subtree查询指定forwarder ontgem
TEST_F(persistDataTest, Other_083_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-l2-forwarding";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "forwarding";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "forwarders";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-l2-fwd:forwarder.1";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char filedname[39] = "";
    strcpy(filedname, "forwarder.0.6.8.0.ontgem.1.0");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneForwarderOntgem";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 066.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定ethernetCsmacd
TEST_F(persistDataTest, Other_083_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[39] = "";
    strcpy(filedname, "ethernetCsmacd.0.7.1.53.4");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneEthernetCsmacd";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 067.恢复业务典配8k持久化数据后，interfaces模型，subtree查询指定onteth
TEST_F(persistDataTest, Other_083_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[39] = "";
    strcpy(filedname, "vlan-sub-interface.0.7.14.31.onteth.1");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneOnteth";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 068.恢复业务典配8k持久化数据后，bbf-l2-forwarding模型，subtree查询指定forwarder onteth
TEST_F(persistDataTest, Other_083_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-l2-forwarding";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "forwarding";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "forwarders";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-l2-fwd:forwarder.1";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char filedname[39] = "";
    strcpy(filedname, "forwarder.0.7.6.38.onteth.3");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneForwarderOnteth";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 069.恢复业务典配8k持久化数据后，bbf-l2-forwarding模型，subtree查询指定forwarder
TEST_F(persistDataTest, Other_083_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-l2-forwarding";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "forwarding";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "forwarders";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-l2-fwd:forwarder.1";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char filedname[39] = "";
    strcpy(filedname, "forwarder.0.6.12.35.103");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneForwarder";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 070.恢复业务典配8k持久化数据后，bbf-mgmd：multicast模型，subtree查询指定组播VLAN
TEST_F(persistDataTest, Other_083_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-mgmd";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "multicast";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "mgmd";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-mgmd:multicast-vpn.1";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char T3filedname[9] = "";
    strcpy(T3filedname, "mvlan100");
    ret = testsubtreeSetvalue(
        T3List, GMC_DATATYPE_STRING, &T3filedname, strlen(T3filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetOneVLAN";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 071.恢复业务典配8k持久化数据后，ietf-hardware模型，subtree批量查询主控板
TEST_F(persistDataTest, Other_083_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "hardware";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "hw:component.1";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[21] = "";
    strcpy(filedname, "board.0.9");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_STRING, &filedname, strlen(filedname), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetActiveBoard";
    snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s",checkFileName);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "diff replyCheck/%s.txt %s", checkFileName, "reply/1.txt");
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 072.恢复业务典配8k持久化数据后，ietf-interfaces模型，subtree批量查询ont v-ani
TEST_F(persistDataTest, Other_083_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[23] = "";
    strcpy(filedname, "bbf-xpon-if-type:v-ani");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetAllOnt";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 073.恢复业务典配8k持久化数据后，ietf-interfaces模型，subtree批量查询ont ani
TEST_F(persistDataTest, Other_083_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[23] = "";
    strcpy(filedname, "bbf-xpon-if-type:ani");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetAllOntAni";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 074.恢复业务典配8k持久化数据后，xpongemtcont模型，subtree批量查询ont ani
TEST_F(persistDataTest, Other_083_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "xpongemtcont";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "tconts";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-xpongemtcont:tcont.2";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char T3filedname[17] = "";
    strcpy(T3filedname, "dba-profile_3");
    ret = testsubtreeSetvalue(T3List, GMC_DATATYPE_STRING, &T3filedname, strlen(T3filedname),
        "traffic-descriptor-profile-ref", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetAllTcont";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 075.恢复业务典配8k持久化数据后，ietf-interfaces模型，subtree批量查询olt-v-enet
TEST_F(persistDataTest, Other_083_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[28] = "";
    strcpy(filedname, "bbf-xpon-if-type:olt-v-enet");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetAllOlt";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 076.恢复业务典配8k持久化数据后，ietf-interfaces模型，subtree批量查询onu-v-enet
TEST_F(persistDataTest, Other_083_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[28] = "";
    strcpy(filedname, "bbf-xpon-if-type:onu-v-enet");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetAllOnu";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 077.恢复业务典配8k持久化数据后，xpongemtcont模型，subtree批量查询gemport
TEST_F(persistDataTest, Other_083_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "xpongemtcont";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2Node = NULL;
    const char *SubT2Node = "gemports";
    ret = GmcYangEditChildNode(T1Node, SubT2Node, GMC_OPERATION_SUBTREE_FILTER, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    const char *SubT3List = "bbf-xpongemtcont:gemport.2";
    ret = GmcYangEditChildNode(T2Node, SubT3List, GMC_OPERATION_SUBTREE_FILTER, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t T3filedname = 0;
    ret = testsubtreeSetvalue(T3List, GMC_DATATYPE_UINT8, &T3filedname, sizeof(T3filedname), "traffic-class",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "GetAllGemport";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 078.恢复业务典配8k持久化数据后，interfaces模型，subtree批量查询ontgem
TEST_F(persistDataTest, Other_083_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[39] = "";
    strcpy(filedname, "bbf-if-type:vlan-sub-interface");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "GetAllOntgem";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);    
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);
}

// 079.恢复业务典配8k持久化数据后，hardware模型，subtree批量查询port
TEST_F(persistDataTest, Other_083_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "hardware";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "hw:component.1";
    ret = GmcYangEditChildNode(T1Node, SubT2ListNode, GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[26] = "";
    strcpy(filedname, "iana-hardware:port");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "class", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "GetAllOntPort";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);  
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);
}

// 080.恢复业务典配8k持久化数据后，interfaces模型，subtree批量查询ethernetCsmacd
TEST_F(persistDataTest, Other_083_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    const char *SubT1Node = "interfaces";
    ret = GmcYangEditChildNode(root, SubT1Node, GMC_OPERATION_SUBTREE_FILTER, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T2List = NULL;
    const char *SubT2ListNode = "if:interface.1";
    ret = GmcYangEditChildNode(T1Node, "if:interface.1", GMC_OPERATION_SUBTREE_FILTER, &T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char filedname[39] = "";
    strcpy(filedname, "iana-if-type:ethernetCsmacd");
    ret = testsubtreeSetvalue(
        T2List, GMC_DATATYPE_IDENTITY, &filedname, strlen(filedname), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "GetAllEthernetCsmacd";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);    
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);
}

// 081.恢复业务典配8k持久化数据后，ietf-interfaces表全量subtree查询
TEST_F(persistDataTest, Other_083_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "Interfaces";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);    
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);	
}

// 081.恢复业务典配8k持久化数据后，ietf-hardware表全量subtree查询
TEST_F(persistDataTest, Other_083_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "Hardware";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);     
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);	
}

// 081.恢复业务典配8k持久化数据后，bbf-mgmd表全量subtree查询
TEST_F(persistDataTest, Other_083_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-mgmd";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "BbfMgmd";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);    
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);	
}

// 081.恢复业务典配8k持久化数据后，bbf-link-table表全量subtree查询
TEST_F(persistDataTest, Other_083_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-link-table";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "BbfLinkTable";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);    
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);	
}

// 081.恢复业务典配8k持久化数据后，bbf-xpongemtcont表全量subtree查询
TEST_F(persistDataTest, Other_083_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "BbfXpongemtcont";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);       
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);
}

// 081.恢复业务典配8k持久化数据后，bbf-l2-forwarding表全量subtree查询
TEST_F(persistDataTest, Other_083_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-l2-forwarding";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "BbfL2Forwarding";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);         
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);
}

// 082.恢复业务典配8k持久化数据后，新增1个单板数据
TEST_F(persistDataTest, Other_083_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "hardware", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    GmcStmtT *g_T0T2_stmt = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "hw:component.1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *listroot = NULL;
    ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    char valueName[11] = "";
    strcpy(valueName, "board.0.19");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char name[25] = "";
    strcpy(name, "bbf-hardware-types:board");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "class", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff006, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增1个ONT数据
TEST_F(persistDataTest, Other_083_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    GmcStmtT *g_T0T2_stmt = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *listroot = NULL;
    ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    char valueName[15] = "";
    strcpy(valueName, "v-ani.0.19.0.0");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char name[23] = "";
    strcpy(name, "bbf-xpon-if-type:v-ani");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T3List = NULL;
    ret = GmcYangEditChildNode(listroot, "v-ani", GMC_OPERATION_INSERT, &T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char valueNameT3[15] = "";
    strcpy(valueNameT3, "HWTC01000000");
    ret = TestYangSetField(T3List, GMC_DATATYPE_STRING, valueNameT3, (strlen(valueNameT3)), "expected-serial-number",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff001, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增1个bbf-xponani数据
TEST_F(persistDataTest, Other_083_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    GmcStmtT *g_T0T2_stmt = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *listroot = NULL;
    ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    char valueName[15] = "";
    strcpy(valueName, "ani.0.19.0.0");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char name[21] = "";
    strcpy(name, "bbf-xpon-if-type:ani");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff007, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增1个bbf-link-table数据
TEST_F(persistDataTest, Other_083_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    GmcStmtT *g_T0T2_stmt = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *listroot = NULL;
    ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    char valueName[15] = "";
    strcpy(valueName, "ani.0.19.0.0");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char name[21] = "";
    strcpy(name, "bbf-xpon-if-type:ani");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 新增1个bbf-link-table数据
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    const char *SubT0ConNode1 = "bbf-link-table";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node1 = NULL;
    ret = GmcYangEditChildNode(root, "link-table", GMC_OPERATION_NONE, &T1Node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    GmcStmtT *g_T0T2_stmt2 = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt2, "bbf-lt:link-table.2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *listroot1 = NULL;
    ret = GmcGetRootNode(g_T0T2_stmt2, &listroot1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    char valueName1[15] = "ani.0.19.0.0";
    ret = ListtestYangSetField(g_T0T2_stmt2, GMC_DATATYPE_STRING, valueName1, strlen(valueName1), "from-interface",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_T0T2_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff008, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增1个bbf-xpongemtcont数据
TEST_F(persistDataTest, Other_083_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "xpongemtcont", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    ret = GmcYangEditChildNode(T1Node, "tconts", GMC_OPERATION_NONE, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    GmcStmtT *g_T0T2_stmt = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "bbf-xpongemtcont:tcont.2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *listroot = NULL;
    ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    char valueName[17] = "";
    strcpy(valueName, "tcont.0.19.0.0.1");
    ret = ListtestYangSetField(
        g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff009, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个bbf-xponvani数据
TEST_F(persistDataTest, Other_083_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[25] = {0};
        sprintf(valueName, "olt-v-enet.0.19.0.0.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char name[28] = "";
        strcpy(name, "bbf-xpon-if-type:olt-v-enet");
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T3Node = NULL;
        ret = GmcYangEditChildNode(listroot, "olt-v-enet", GMC_OPERATION_INSERT, &T3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char oltvalueName[25] = {0};
        sprintf(oltvalueName, "v-ani.0.8.9.%d", i);
        ret = TestYangSetField(T3Node, GMC_DATATYPE_STRING, oltvalueName, (strlen(oltvalueName)),
            "lower-layer-interface", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff010, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个onu-v-enet数据
TEST_F(persistDataTest, Other_083_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[25] = {0};
        sprintf(valueName, "onu-v-enet.0.19.0.0.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char name[28] = "";
        strcpy(name, "bbf-xpon-if-type:onu-v-enet");
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T3Node = NULL;
        ret = GmcYangEditChildNode(listroot, "onu-v-enet", GMC_OPERATION_INSERT, &T3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char oltvalueName[25] = {0};
        sprintf(oltvalueName, "ani.0.6.12.%d", i);
        ret = TestYangSetField(T3Node, GMC_DATATYPE_STRING, oltvalueName, (strlen(oltvalueName)), "ani",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff011, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个gemport数据
TEST_F(persistDataTest, Other_083_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-xpongemtcont";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "xpongemtcont", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    ret = GmcYangEditChildNode(T1Node, "gemports", GMC_OPERATION_NONE, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "bbf-xpongemtcont:gemport.2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[25] = {0};
        sprintf(valueName, "gemport.0.19.0.0.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char intervalueName[25] = {0};
        sprintf(intervalueName, "olt-v-enet.0.8.0.0.%d", 1);
        ret = ListtestYangSetField(g_T0T2_stmt, GMC_DATATYPE_STRING, intervalueName, strlen(intervalueName),
            "interface", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char tcontvalueName[25] = {0};
        sprintf(tcontvalueName, "tcont.0.8.0.0.%d", 1);
        ret = ListtestYangSetField(g_T0T2_stmt, GMC_DATATYPE_STRING, tcontvalueName, strlen(tcontvalueName),
            "tcont-ref", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff012, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个ontgem数据
TEST_F(persistDataTest, Other_083_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[40] = {0};
        sprintf(valueName, "vlan-sub-interface.0.19.0.0.ontgem.1.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char name[31] = "";
        strcpy(name, "bbf-if-type:vlan-sub-interface");
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T3Node = NULL;
        ret = GmcYangEditChildNode(listroot, "subif-lower-layer", GMC_OPERATION_INSERT, &T3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char oltvalueName[25] = {0};
        sprintf(oltvalueName, "onu-v-enet.0.8.0.0.%d", 1);
        ret = TestYangSetField(T3Node, GMC_DATATYPE_STRING, oltvalueName, (strlen(oltvalueName)), "interface",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff013, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个ontgem数据
TEST_F(persistDataTest, Other_083_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-l2-forwarding";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "forwarding", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    ret = GmcYangEditChildNode(T1Node, "forwarders", GMC_OPERATION_NONE, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "bbf-l2-fwd:forwarder.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[40] = {0};
        sprintf(valueName, "forwarder.0.19.0.0.ontgem.1.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T3Node = NULL;
        ret = GmcYangEditChildNode(listroot, "ports", GMC_OPERATION_INSERT, &T3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff014, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个ont por数据
TEST_F(persistDataTest, Other_083_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-hardware";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "hardware", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "hw:component.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[40] = {0};
        sprintf(valueName, "port.0.19.0.0.eth.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char name[28] = "iana-hardware:port";
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "class", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff015, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 082.恢复业务典配8k持久化数据后，新增10个ethernetCsmacd数据
TEST_F(persistDataTest, Other_083_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "interfaces", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "if:interface.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[40] = {0};
        sprintf(valueName, "ethernetCsmacd.0.19.0.0.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char name[28] = "";
        strcpy(name, "iana-if-type:ethernetCsmacd");
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_IDENTITY, name, strlen(name), "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcStmtT *g_T0T3_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T3_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T3_stmt, "bbf-if-port-ref:port-layer-if.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_T0T2_stmt, g_T0T3_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot1 = NULL;
        ret = GmcGetRootNode(g_T0T3_stmt, &listroot1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName1[40] = {0};
        sprintf(valueName1, "port.0.8.0.0.eth.%d", 1);
        ret = ListtestYangSetField(g_T0T3_stmt, GMC_DATATYPE_STRING, valueName1, strlen(valueName1), "port-layer-if",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T3_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(21, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(21, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff017, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

// 081.恢复业务典配8k持久化数据后，全量subtree查询
TEST_F(persistDataTest, Other_083_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "ietf-interfaces";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_FULL_OBJ,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(4);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCbNS, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *expect = NULL;
    char *actual = NULL;
    char *checkFileName = "AllNameSpace";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);           
        readJanssonFile(replyCheckFile, &expect);
        EXPECT_NE((void *)NULL, expect);
        readJanssonFile(replyFile, &actual);
        EXPECT_NE((void *)NULL, actual);
        EXPECT_TRUE(testYangJsonIsEqual(actual, expect));
    }
    free(expect);
    free(actual);
}


// 081.恢复业务典配8k持久化数据后，进行全量validate校验
TEST_F(persistDataTest, Other_083_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};
    // 启动事务
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据校验
    clock_t start_time = clock(); 
    // 预期报超时错误
    WhenDataCheck(g_stmt_root, true,GMERR_REQUEST_TIME_OUT,0,GMC_YANG_VALIDATION_ALL_FORCE);
    clock_t end_time = clock(); 
    double elapsed_time = (double)(end_time - start_time) / CLOCKS_PER_SEC * 1000;
    TEST_INFO("[Create]Ont Validate Timeout Cost Time=%lf ms",elapsed_time);

    // 超时后可以继续查询
    GmcNodeT *root = NULL;
    const char *SubT0ConNode = "bbf-l2-forwarding";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = root;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    system("mkdir -p reply;rm -rf reply/*");
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 比较预期与实际是否一致
    char *checkFileName = "BbfL2Forwarding";
    int resultFileCount = 0;
    ret = GetPrintBycmd("ls -l reply/*.txt|wc -l", &resultFileCount, NULL, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(0, resultFileCount);
    char replyCheckFile[128];
    char replyFile[128];
    for (int i = 1; i < resultFileCount + 1; i++) {
        (void)snprintf(replyFile, 128, "./reply/%d.txt", i);
        snprintf(g_command, MAX_CMD_SIZE, "sh get_persistentdata.sh %s%d",checkFileName,i);
        system(g_command);
        (void)snprintf(replyCheckFile, 128, "./replyCheck/%s%d.txt", checkFileName,i);
        snprintf(g_command, MAX_CMD_SIZE, "diff %s %s", replyCheckFile, replyFile);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 再次开启事务进行更新数据
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, SubT0ConNode, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T1Node = NULL;
    ret = GmcYangEditChildNode(root, "forwarding", GMC_OPERATION_NONE, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T2Node = NULL;
    ret = GmcYangEditChildNode(T1Node, "forwarders", GMC_OPERATION_NONE, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 申请句柄
    for (int i = 0; i < 10; i++) {
        GmcStmtT *g_T0T2_stmt = NULL;
        ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "bbf-l2-fwd:forwarder.1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *listroot = NULL;
        ret = GmcGetRootNode(g_T0T2_stmt, &listroot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        char valueName[40] = {0};
        sprintf(valueName, "forwarder.0.19.0.0.ontgem.1.%d", i);
        ret = ListtestYangSetField(
            g_T0T2_stmt, GMC_DATATYPE_STRING, valueName, strlen(valueName), "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *T3Node = NULL;
        ret = GmcYangEditChildNode(listroot, "ports", GMC_OPERATION_INSERT, &T3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, 0, 0, GMC_YANG_VALIDATION_ALL);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff014, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}
