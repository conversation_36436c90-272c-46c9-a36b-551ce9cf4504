
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tPoolTest.h"
#include "tPoolThread.h"
 
#define MAX_VERTEX_NUM 10000
GmcConnT *g_conn = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt = NULL, *g_stmt_async = NULL;

 
class tPoolVertex : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tPoolVertex::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxWorkerNum=10\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"permanentWorkerNum=4\"");

    // 设置为：3,4,5 并发场景下争不到锁，会导致服务hung掉，属于正常现象；
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");

    system("sh $TEST_HOME/tools/start.sh");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tPoolVertex::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void tPoolVertex::SetUp()
{
    int ret = 0;

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_stmt, g_labelName);

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    system("rm -rf \"../../../log/run/rgmserver/rgmserver.log\"");

    AW_CHECK_LOG_BEGIN();
}

void tPoolVertex::TearDown()
{
    // 并发replace merage可能会报主键冲突错误；并发批量会报1010003错误
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    // 并发可能报锁不可用错误
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);

    AW_CHECK_LOG_END();

    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}


// 100个并发耗费 tPoolVertex.Other_047_002_001 (45048 ms)
// 250个并发耗费 tPoolVertex.Other_047_002_001 (264985 ms)
// 300个并发耗费 tPoolVertex.Other_047_002_001 (378404 ms)
// 001.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发单步dml操作
TEST_F(tPoolVertex, Other_047_002_001)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < g_threadNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDml, (void *)&index[i]);
    }
 
    for (int i = 0; i < g_threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


// 100个线程并发 tPoolVertex.Other_047_002_002 (3313 ms)
// 300个线程并发 tPoolVertex.Other_047_002_002 (10722 ms)
// 500个线程并发 tPoolVertex.Other_047_002_002 (17007 ms)
// 800个线程并发  tPoolVertex.Other_047_002_002 (27258 ms)
// 002.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发批量dml操作
TEST_F(tPoolVertex, Other_047_002_002)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < g_threadNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexBatchDml, (void *)&index[i]);
    }
 
    for (int i = 0; i < g_threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


// 003.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发开启悲观读已提交事务，单步dml操作
TEST_F(tPoolVertex, Other_047_002_003)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 10; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDmlTrans, (void *)&index[i]);
    }
 
    for (int i = 0; i < 10; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    AW_FUN_Log(LOG_INFO, ">>> trans commit succ count: %d\n", g_dmlTransSuccCount);
    EXPECT_GE(g_dmlTransSuccCount, 1);

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}


// 127个并发 tPoolVertex.Other_047_002_004 (5952 ms)
// 004.vertex表，maxWorkerNum设置10,异步连接，20个多线程并发单步dml操作
TEST_F(tPoolVertex, Other_047_002_004)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    g_threadNum = 127;
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    // 单客户端异步连接最多128个；
    for (int i = 0; i < 127; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDmlAsync, (void *)&index[i]);

    }
 
    for (int i = 0; i < 127; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


// 005.vertex表，maxWorkerNum设置10,异步连接，20个多线程并发批量dml操作
TEST_F(tPoolVertex, Other_047_002_005)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    g_threadNum = 127;
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 127; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDmlAsyncBatch, (void *)&index[i]);
    }
 
    for (int i = 0; i < 127; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);
    AW_FUN_Log(LOG_INFO, ">>> succ cnt: %d %d %d %d %d\n",
        g_succNum1, g_succNum2, g_succNum3, g_succNum4, g_succNum5);
    EXPECT_GE(g_succNum1, 1);
    EXPECT_GE(g_succNum2, 1);
    EXPECT_GE(g_succNum3, 1);
    EXPECT_GE(g_succNum4, 1);
    EXPECT_GE(g_succNum5, 1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);

    // 光启就是有单独的心跳连接，发统计包的时候连接被断联了，就会出现这个；是正常的，加白名单。-- 沈俊辉
    #ifdef ENV_SUSE
        AddWhiteList(GMERR_NO_DATA);
        AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    #endif
}


 // 127个并发 tPoolVertex.Other_047_002_006 (6264 ms)
// 50个并发 tPoolVertex.Other_047_002_006 (3351 ms) succ 50
// 006.vertex表，maxWorkerNum设置10,异步连接，20个多线程并发开启悲观读已提交事务，单步dml操作
TEST_F(tPoolVertex, Other_047_002_006)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    g_dmlTransSuccCount = 0;

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    g_threadNum = 127;
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 127; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDmlAsyncBatchTrans, (void *)&index[i]);
    }
 
    for (int i = 0; i < 127; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);
    AW_FUN_Log(LOG_INFO, ">>> trans commit succ count: %d\n", g_dmlTransSuccCount);
    EXPECT_GE(g_dmlTransSuccCount, 1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);

    // 光启就是有单独的心跳连接，发统计包的时候连接被断联了，就会出现这个；是正常的，加白名单。-- 沈俊辉
    #ifdef ENV_SUSE
        AddWhiteList(GMERR_NO_DATA);
        AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    #endif
}


// 超过10个并发，乐观事务，资源不足
// 007.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发开启乐观可重复读事务，单步dml操作
TEST_F(tPoolVertex, Other_047_002_007)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    g_dmlTransSuccCount = 0;

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    // create namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建vertexlabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00001.gmjson", &schema_json);

    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 10; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDmlOtimicTrans, (void *)&index[i]);
    }

    for (int i = 0; i < 10; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);
    AW_FUN_Log(LOG_INFO, ">>> trans commit succ count: %d\n", g_dmlTransSuccCount);
    EXPECT_GE(g_dmlTransSuccCount, 1);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
}

// 008.vertex表，maxWorkerNum设置10，15个多线程并发单步dml操作，15个多线程并发订阅操作；
TEST_F(tPoolVertex, Other_047_002_008)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 15; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexSub, (void *)&index[i]);
    }

    sleep(3);
    for (int i = 15; i < 30; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDml, (void *)&index[i]);
    }

    for (int i = 0; i < 30; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 009.vertex表，maxWorkerNum设置10,表里写入大量数据，表结构升级；
TEST_F(tPoolVertex, Other_047_002_009)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    char *schemaUpdateParth = (char *)"./schema_file/ip4forward00000_update1.gmjson";
    char *schemaUpdateParth2 = (char *)"./schema_file/ip4forward00000_update2.gmjson";


    // 写入大量数据
    unsigned int totalNum;
    unsigned int successNum;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
 
    uint64_t i = 0;
    uint64_t tStart = 0;
    uint64_t tEnd = 10000;
    for (i = tStart; i < tEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(ret, GMERR_OK);

        if (i % 200 == 0 || i == (tEnd -1)) {
            GmcBatchRetT batchRet;
            ret = GmcBatchExecute(batch, &batchRet);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    GmcBatchDestroy(batch);
    batch = NULL;
    PrintTableCount(g_stmt, g_labelName);

    sleep(1);

    // 升级表
    char *expectValue = (char *)"upgrade successfully";
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t schemaVersion = 2;
    for (i = 0; i < 10000; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tValue32 = 0;
            uint64_t tValue64 = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"flags", &tValue32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tValue32, i);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"app_version_1", &tValue32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_version_1.");
            AW_MACRO_EXPECT_EQ_INT(tValue32, 0);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"trace_1", &tValue64, sizeof(uint64_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName trace_1.");
            AW_MACRO_EXPECT_EQ_INT(tValue64, 0);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"app_version_2", &tValue32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_version_2.");
            AW_MACRO_EXPECT_EQ_INT(tValue32, 0);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"trace_2", &tValue64, sizeof(uint64_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName trace_2.");
            AW_MACRO_EXPECT_EQ_INT(tValue64, 0);
        }
    }

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 010.vertex表，maxWorkerNum设置10,表里写入大量数据，表结构降级；
TEST_F(tPoolVertex, Other_047_002_010)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    char *schemaUpdateParth = (char *)"./schema_file/ip4forward00000_update1.gmjson";
    char *schemaUpdateParth2 = (char *)"./schema_file/ip4forward00000_update2.gmjson";

    // 升级表
    char *expectValue = (char *)"upgrade successfully";
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入大量数据
    unsigned int totalNum;
    unsigned int successNum;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
 
    uint64_t i = 0;
    uint64_t tStart = 0;
    uint64_t tEnd = 10000;
    for (i = tStart; i < tEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(ret, GMERR_OK);

        if (i % 200 == 0 || i == (tEnd -1)) {
            GmcBatchRetT batchRet;
            ret = GmcBatchExecute(batch, &batchRet);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    GmcBatchDestroy(batch);
    batch = NULL;
    PrintTableCount(g_stmt, g_labelName);
    sleep(1);

    char *expectValue2 = (char *)"degrade successfully";
    uint32_t schemaVersion = 1;
    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    schemaVersion = 1;
    for (i = 0; i < 1; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tValue32 = 0;
            uint64_t tValue64 = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"flags", &tValue32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tValue32, i);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"app_version_1", &tValue32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_version_1.");
            AW_MACRO_EXPECT_EQ_INT(tValue32, 0);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"trace_1", &tValue64, sizeof(uint64_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName trace_1.");
            AW_MACRO_EXPECT_EQ_INT(tValue64, 0);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"app_version_2", &tValue32, sizeof(uint32_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_INVALID_PROPERTY);

            ret = GmcGetVertexPropertyByName(g_stmt, (char *)"trace_2", &tValue64, sizeof(uint64_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_INVALID_PROPERTY);
        }
    }

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}
