/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: V3规格基线用例转化--schema规格
 * Author: guopanpan
 * Create: 2021-05-10
 * History:
 */
#include "SpecificationBaseline.h"

class SpecificationBaseline002Schema : public testing::Test {
public:
    static void SetUpTestCase()
    {
        int ret;
        GtExecSystemCmd("start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&gConn, &gStmt);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    static void TearDownTestCase()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(gConn, gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        gConn = NULL;
        gStmt = NULL;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }
};

// schema定义的字符串长度等于1024KB
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_001)
{
    int ret;
    int jsonLen = 1024 * 1024;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    // 使用空格填充字符串, 以满足测试长度需求
    memset(schemaJson, ' ', jsonLen);
    schemaJson[jsonLen - 1] = '\0';
    ret = snprintf(schemaJson, jsonLen, "%s", gLabelSchemaJson);
    EXPECT_GT(ret, 0);
    schemaJson[strlen(schemaJson)] = ' ';
    EXPECT_EQ(jsonLen, strlen(schemaJson) + 1);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// schema定义的字符串长度大于1024KB
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_002)
{
    int ret;
    int jsonLen = 1024 * 1024 + 2;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    // 使用空格填充字符串, 以满足测试长度需求
    memset(schemaJson, ' ', jsonLen);
    schemaJson[jsonLen - 1] = '\0';
    ret = snprintf(schemaJson, jsonLen, "%s", gLabelSchemaJson);
    EXPECT_GT(ret, 0);
    schemaJson[strlen(schemaJson)] = ' ';
    EXPECT_EQ(jsonLen, strlen(schemaJson) + 1);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schemaJson);
    schemaJson = NULL;

    // 异常资源清理
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// schema总节点数等于1024个
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_003)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"SecondNode", "type":"record", "fields": [
                    {"name":"ThirdNode", "type":"record", "fields": [
                        {"name":"H0", "type":"int32", "nullable":false}
                    ]}
                ]},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 154922 字节
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定节点数量的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    // 已经预置了3个节点 因此再组装1021个节点即可
    int32_t nodeCnt = 1021;
    for (int32_t i = 0; i < nodeCnt - 1; i++) {
        ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"N%d\", \"type\":\"record\", \"fields\": [", 16, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
        ret = GtStrcat(
            schemaJson, jsonLen, "\n%*s{\"name\":\"G%d\", \"type\":\"int32\", \"nullable\":false}", 20, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
        ret = GtStrcat(schemaJson, jsonLen, "\n%*s]},", 16, " ");
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"N%d\", \"type\":\"record\", \"fields\": [", 16, " ", nodeCnt - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"G%d\", \"type\":\"int32\", \"nullable\":false}", 20, " ", nodeCnt - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s]}", 16, " ");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    TEST_INFO("strlen(schemaJson) + 1 = %d", strlen(schemaJson) + 1);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// schema总节点数等于1025个
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_004)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"SecondNode", "type":"record", "fields": [
                    {"name":"ThirdNode", "type":"record", "fields": [
                        {"name":"H0", "type":"int32", "nullable":false}
                    ]}
                ]},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 154922 字节
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定节点数量的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    // 已经预置了3个节点 因此再组装1022个节点即可
    int32_t nodeCnt = 1022;
    for (int32_t i = 0; i < nodeCnt - 1; i++) {
        ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"N%d\", \"type\":\"record\", \"fields\": [", 16, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
        ret = GtStrcat(
            schemaJson, jsonLen, "\n%*s{\"name\":\"G%d\", \"type\":\"int32\", \"nullable\":false}", 20, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
        ret = GtStrcat(schemaJson, jsonLen, "\n%*s]},", 16, " ");
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"N%d\", \"type\":\"record\", \"fields\": [", 16, " ", nodeCnt - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"G%d\", \"type\":\"int32\", \"nullable\":false}", 20, " ", nodeCnt - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s]}", 16, " ");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    TEST_INFO("strlen(schemaJson) + 1 = %d", strlen(schemaJson) + 1);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// 一个节点下的字段（普通字段或节点）等于1024个
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_005)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定字段数量的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t fieldCnt = 1024;
    for (int32_t i = 0; i < fieldCnt - 1; i++) {
        ret = GtStrcat(
            schemaJson, jsonLen, "\n%*s{\"name\":\"F%d\", \"type\":\"int32\", \"nullable\":false},", 16, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"F%d\", \"type\":\"int32\", \"nullable\":false}", 16, " ", fieldCnt - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 一个节点下的字段（普通字段或节点）等于1025个
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_006)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定字段数量的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t fieldCnt = 1024 + 1;
    for (int32_t i = 0; i < fieldCnt - 1; i++) {
        ret = GtStrcat(
            schemaJson, jsonLen, "\n%*s{\"name\":\"F%d\", \"type\":\"int32\", \"nullable\":false},", 16, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"F%d\", \"type\":\"int32\", \"nullable\":false}", 16, " ", fieldCnt - 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 节点名长度等于128字节
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_007)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
                    [
                        {"name":"G0", "type":"int32", "nullable":false}
                    ]
                }
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char nodeName[128] = {0};
    int32_t nodeNameLen = sizeof(nodeName);
    memset(nodeName, 't', nodeNameLen - 1);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"record\", \"fields\": ", 16, " ", nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 节点名长度等于129字节
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_008)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
                    [
                        {"name":"G0", "type":"int32", "nullable":false}
                    ]
                }
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char nodeName[129] = {0};
    int32_t nodeNameLen = sizeof(nodeName);
    memset(nodeName, 't', nodeNameLen - 1);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"record\", \"fields\": ", 16, " ", nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 属性名长度等于128字节
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_009)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char propertyName[128] = {0};
    int32_t propertyNameLen = sizeof(propertyName);
    memset(propertyName, 'f', propertyNameLen - 1);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"int32\", \"nullable\":false}", 16, " ", propertyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 属性名长度等于129字节
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_010)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char propertyName[129] = {0};
    int32_t propertyNameLen = sizeof(propertyName);
    memset(propertyName, 'f', propertyNameLen - 1);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"int32\", \"nullable\":false}", 16, " ", propertyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// schema的深度等于32层
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_011)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定深度的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t nodeDepth = 32;
    // 根节点是第一层节点，因此循环中下标从1开始
    for (int32_t i = 1; i < nodeDepth; i++) {
        ret = GtStrcat(
            schemaJson, jsonLen, "\n%*s{\"name\":\"Node%d\", \"type\":\"record\", \"fields\":[", (i + 1) * 8, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"P0\", \"type\":\"int32\", \"nullable\":false}",
        (nodeDepth + 1) * 8, " ");
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = nodeDepth - 1; i >= 1; i--) {
        ret = GtStrcat(schemaJson, jsonLen, "\n%*s]}", (i + 1) * 8, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// schema的深度等于33层
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_012)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定深度的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t nodeDepth = 33;
    // 根节点是第一层节点，因此循环中下标从1开始
    for (int32_t i = 1; i < nodeDepth; i++) {
        ret = GtStrcat(
            schemaJson, jsonLen, "\n%*s{\"name\":\"Node%d\", \"type\":\"record\", \"fields\":[", (i + 1) * 8, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"P0\", \"type\":\"int32\", \"nullable\":false}",
        (nodeDepth + 1) * 8, " ");
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = nodeDepth - 1; i >= 1; i--) {
        ret = GtStrcat(schemaJson, jsonLen, "\n%*s]}", (i + 1) * 8, " ", i);
        CHECK_AND_BREAK(ret, "set schema node");
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// schema中对象命名（node、field、key）包含字母, 数字, 下划线
// NOTICE 2021-05-11 V5规格基线中无该约束
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_013)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
                    [
                        {"name":"G0", "type":"int32", "nullable":false}
                    ]
                }
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char propertyName[128] = {0};
    int32_t propertyNameLen = sizeof(propertyName);
    ret = snprintf(propertyName, propertyNameLen, "_1Az");
    EXPECT_GT(ret, 0);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"int32\", \"nullable\":false},", 16, " ", propertyName);
    EXPECT_EQ(GMERR_OK, ret);
    char nodeName[128] = {0};
    int32_t nodeNameLen = sizeof(nodeName);
    ret = snprintf(nodeName, nodeNameLen, "1_zA");
    EXPECT_GT(ret, 0);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"record\", \"fields\": ", 16, " ", nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// schema中对象命名（node、field、key）包含特殊字符$@
// NOTICE 2021-05-11 V5规格基线中无该约束!!!
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_014)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
                    [
                        {"name":"G0", "type":"int32", "nullable":false}
                    ]
                }
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char propertyName[128] = {0};
    int32_t propertyNameLen = sizeof(propertyName);
    ret = snprintf(propertyName, propertyNameLen, "$@*");
    EXPECT_GT(ret, 0);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"int32\", \"nullable\":false},", 16, " ", propertyName);
    EXPECT_EQ(GMERR_OK, ret);
    char nodeName[128] = {0};
    int32_t nodeNameLen = sizeof(nodeName);
    ret = snprintf(nodeName, nodeNameLen, "V$");
    EXPECT_GT(ret, 0);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"record\", \"fields\": ", 16, " ", nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// 同一个record下属性名及节点名保持唯一, 不同节点下属性名相同
// HISTORY 2021-09-22 要支持node name的唯一性， 不同节点下的子节点也不支持同名 （马晓迪）
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_015)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"T0", "type":"record", "fields": [
                    {"name":"G1", "type":"int32", "nullable":false},
                    {"name":"N1", "type":"record", "fields": [
                        {"name":"G0", "type":"int32", "nullable":false}
                    ]}
                ]},
                {"name":"T1", "type":"record", "fields": [
                    {"name":"G1", "type":"int32", "nullable":false},
                    {"name":"N2", "type":"record", "fields": [
                        {"name":"G0", "type":"int32", "nullable":false}
                    ]}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 同一个record下属性名相同
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_016)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"SameName", "type":"int32", "nullable":false},
                {"name":"SameName", "type":"int32", "nullable":false},
                {"name":"T0", "type":"record", "fields": [
                    {"name":"G0", "type":"int32", "nullable":false}
                ]},
                {"name":"T1", "type":"record", "fields": [
                    {"name":"G1", "type":"int32", "nullable":false}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 同一个record下节点名相同
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_017)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"SameName", "type":"record", "fields": [
                    {"name":"G0", "type":"int32", "nullable":false}
                ]},
                {"name":"SameName", "type":"record", "fields": [
                    {"name":"G1", "type":"int32", "nullable":false}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_COLUMN);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 同一个record下节点名和属性名相同
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_018)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"SameName", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"SameName", "type":"record", "fields": [
                    {"name":"G0", "type":"int32", "nullable":false}
                ]},
                {"name":"T1", "type":"record", "fields": [
                    {"name":"G1", "type":"int32", "nullable":false}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 简单表schema单层节点size等于16KB
// NOTICE 2021-05-17 V5规格基线中无该约束!!!
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_019)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":10240, "nullable":true},
                {"name":"F2", "type":"fixed", "size":6140, "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 简单表schema单层节点size大于16KB
// NOTICE 2021-05-17 V5规格基线中无该约束
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_020)
{
    const char *labelName = "VertexLabel";
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":4294967295, "nullable":true},
                {"name":"F2", "type":"fixed", "size":4294967295, "nullable":true},
                {"name":"F3", "type":"fixed", "size":4294967295, "nullable":true},
                {"name":"F4", "type":"fixed", "size":4294967295, "nullable":true},
                {"name":"F5", "type":"fixed", "size":4294967295, "nullable":true},
                {"name":"F6", "type":"fixed", "size":4294967295, "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// Schema中索引名长度等于 33*129 字节
// HISTORY 2021-06-24 规格由 33 * 129 修改为 33 * 128 (DTS202105310QHH74P1F00)
// HISTORY 2021-09-07 规格由 33 * 128 修改为 33 * 129 (兼容V3 -- 朱丽霞 邮件)
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_021)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",)";

    const char *schemaJsonTail =
        R"(
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    // 33 * 129 = 4257
    char indexName[4257] = {0};
    int32_t indexNameLen = sizeof(indexName);
    memset(indexName, 'a', indexNameLen - 1);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s\"name\":\"%s\",", 20, " ", indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);

    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// Schema中索引名长度大于 33*129 字节
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_022)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",)";

    const char *schemaJsonTail =
        R"(
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    int ret;

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    // 33 * 129 + 1 = 4258
    char indexName[4258] = {0};
    int32_t indexNameLen = sizeof(indexName);
    memset(indexName, 'a', indexNameLen - 1);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s\"name\":\"%s\",", 20, " ", indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// record类型为array或vector
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_023)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"T0", "type":"record", "fixed_array":true, "fields": [
                    {"name":"G0", "type":"int32", "nullable":false}
                ]},
                {"name":"T1", "type":"record", "vector":true, "fields": [
                    {"name":"G1", "type":"int32", "nullable":false}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// record类型同时为vector和array
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_024)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"T0", "type":"record", "fixed_array":true, "vector":true, "fields": [
                    {"name":"G0", "type":"int32", "nullable":false}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// record类型为非法类型
// NOTICE 2021-05-18 V5只解析当前需要解析的字段,如果配置项符合json格式,对非法的配置项不解析不处理 -- 徐鑫鑫
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_025)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"T0", "type":"record", "xxxx":true, "fields": [
                    {"name":"G0", "type":"int32", "nullable":false}
                ]}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// NOTICE 2021-05-11 V5 规格基线中无以下规格约束(用户手册中有)
// bitmap属性不设置size
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_026)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"bitmap", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// bitmap长度等于8*4*1024个比特
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_027)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"bitmap", "size":32768, "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// bitmap长度大于8*4*1024个比特
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_028)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"bitmap", "size":32769, "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// bitmap字段含支持default属性
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_029)
{
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"bitmap", "size":8, "nullable":false, "default":true}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// key中field含有bitmap类型
TEST_F(SpecificationBaseline002Schema, BasicSpecifications_007_002_030)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *schemaJson =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"bitmap", "size":8, "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0", "F1"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret;
    ret = GmcCreateVertexLabel(gStmt, schemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// NOTICE 2021-05-11 V5现有头文件中未找到表升级相关接口
// 一张表同时存在版本数等于8个, 即升级7次表
// 一张表同时存在版本数等于9个, 即升级8次表
