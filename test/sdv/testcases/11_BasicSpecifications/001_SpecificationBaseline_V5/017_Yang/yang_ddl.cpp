#include "ddl_tools.h"

const char *namespace1 = "NamespaceA";
const char *namespaceUserName = "abc";

class yang_ddl : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void yang_ddl::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yang_ddl::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void yang_ddl::SetUp()
{
    // 同步建连
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步建连
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void yang_ddl::TearDown()
{
    const char *namespace1 = "NamespaceA";
    AsyncUserDataT userData = {0};
    // 异步删除namespace
    int ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 1.表名长度规格512字节,(512成功,513失败)
TEST_F(yang_ddl, BasicSpecifitions_001_017_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // name_size:512, container
    string mark = "name_size_512_container..";
    string label_name_512_container = WriteStrBySize(mark, 512);
    const char *label_name_512_container_ch = label_name_512_container.c_str();
    string label_json_512_container = model_1_part_1 + "container" + model_1_part_2 + label_name_512_container +
                                      model_1_part_3 + label_name_512_container + model_1_part_4;
    const char *label_json_512_container_ch = label_json_512_container.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_512_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // name_size:512, list
    mark = "name_size_512_list..";
    string label_name_512_list = WriteStrBySize(mark, 512);
    const char *label_name_512_list_ch = label_name_512_list.c_str();
    string label_json_512_list = model_1_part_1 + "list" + model_1_part_2 + label_name_512_list + model_1_part_3_list +
                                 label_name_512_list + model_1_part_4_list;
    const char *label_json_512_list_ch = label_json_512_list.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_512_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // name_size:512, edge, from label_name_512_container to label_name_512_list
    mark = "name_size_512_edge_from_label_name_512_container_to_label_name_512_list..";
    string label_name_512_edge = WriteStrBySize(mark, 512);
    const char *label_name_512_edge_ch = label_name_512_edge.c_str();
    string label_json_512_edge = model_2_part_1 + label_name_512_edge + model_2_part_2 + label_name_512_container +
                                 model_2_part_3 + label_name_512_list + model_2_part_4;
    const char *label_json_512_edge_ch = label_json_512_edge.c_str();
    ret = GmcCreateEdgeLabelAsync(
        g_stmt_async, label_json_512_edge_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, label_name_512_edge_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_512_container_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_512_list_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // name_size:513, container
    mark = "name_size_513_container..";
    string label_name_513_container = WriteStrBySize(mark, 513);
    const char *label_name_513_container_ch = label_name_513_container.c_str();
    string label_json_513_container = model_1_part_1 + "container" + model_1_part_2 + label_name_513_container +
                                      model_1_part_3 + label_name_513_container + model_1_part_4;
    const char *label_json_513_container_ch = label_json_513_container.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_513_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);

    // name_size:513, list
    mark = "name_size_513_list..";
    string label_name_513_list = WriteStrBySize(mark, 513);
    const char *label_name_513_list_ch = label_name_513_list.c_str();
    string label_json_513_list = model_1_part_1 + "list" + model_1_part_2 + label_name_513_list + model_1_part_3_list +
                                 label_name_513_list + model_1_part_4_list;
    const char *label_json_513_list_ch = label_json_513_list.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_513_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);

    // name_size:513, choice
    mark = "name_size_513_choice..";
    string label_name_513_choice = WriteStrBySize(mark, 513);
    const char *label_name_513_choice_ch = label_name_513_choice.c_str();
    string label_json_513_choice = model_1_part_1 + "choice" + model_1_part_2 + label_name_513_choice + model_1_part_3 +
                                   label_name_513_choice + model_1_part_4;
    const char *label_json_513_choice_ch = label_json_513_choice.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_513_choice_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);

    // name_size:513, case
    mark = "name_size_513_case..";
    string label_name_513_case = WriteStrBySize(mark, 513);
    const char *label_name_513_case_ch = label_name_513_case.c_str();
    string label_json_513_case = model_1_part_1 + "case" + model_1_part_2 + label_name_513_case + model_1_part_3 +
                                 label_name_513_case + model_1_part_4;
    const char *label_json_513_case_ch = label_json_513_case.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_513_case_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);

    // name_size:513, edge, from label_name_513_container to label_name_513_list
    mark = "name_size_513_edge_from_label_name_513_container_to_label_name_513_list..";
    string label_name_513_edge = WriteStrBySize(mark, 513);
    const char *label_name_513_edge_ch = label_name_513_edge.c_str();
    string label_json_513_edge = model_2_part_1 + label_name_513_edge + model_2_part_2 + label_name_512_container +
                                 model_2_part_3 + label_name_512_list + model_2_part_4;
    const char *label_json_513_edge_ch = label_json_513_edge.c_str();
    ret = GmcCreateEdgeLabelAsync(
        g_stmt_async, label_json_513_edge_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
}

// 2.建表schmea json大小规格,1MB成功,1MB+1失败
TEST_F(yang_ddl, BasicSpecifitions_001_017_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // size:1024*1024, container
    int length = 1024 * 1024 -
                 (model_3_part_1 + "container" + model_3_part_2 + "label_1M_container" + model_3_part_3 +
                     model_3_part_4 + "label_1M_container" + model_3_part_5)
                     .length();
    string mark_1M_container = "";
    string label_json_1M_container = model_3_part_1 + "container" + model_3_part_2 + "label_1M_container" +
                                     model_3_part_3 + WriteStrBySize(mark_1M_container, length, " ") + model_3_part_4 +
                                     "label_1M_container" + model_3_part_5;
    const char *label_json_1M_container_ch = label_json_1M_container.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_1M_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // size:1024*1024, list
    length = 1024 * 1024 -
             (model_3_part_1 + "list" + model_3_part_2 + "label_1M_list" + model_3_part_3_list + model_3_part_4 +
                 "label_1M_list" + model_3_part_5_list)
                 .length();
    string mark_1M_list = "";
    string label_json_1M_list = model_3_part_1 + "list" + model_3_part_2 + "label_1M_list" + model_3_part_3_list +
                                WriteStrBySize(mark_1M_list, length, " ") + model_3_part_4 + "label_1M_list" +
                                model_3_part_5_list;
    const char *label_json_1M_list_ch = label_json_1M_list.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_1M_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // size:1024*1024, edge
    length = 1024 * 1024 -
             (model_4_part_1 + "label_1M_edge_from_label_1M_container_to_label_1M_list" + model_4_part_2 +
                 "label_1M_edge_from_label_1M_container_to_label_1M_list" + model_4_part_3 +
                 "label_1M_edge_from_label_1M_container_to_label_1M_list" + model_4_part_4 + model_4_part_5)
                 .length();
    string mark_1M_edge = "";
    string label_json_1M_edge = model_4_part_1 + "label_1M_edge_from_label_1M_container_to_label_1M_list" +
                                model_4_part_2 + "label_1M_container" + model_4_part_3 + "label_1M_list" +
                                model_4_part_4 + WriteStrBySize(mark_1M_edge, length, " ") + model_4_part_5;
    const char *label_json_1M_edge_ch = label_json_1M_edge.c_str();
    ret = GmcCreateEdgeLabelAsync(
        g_stmt_async, label_json_1M_edge_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, "label_1M_edge_from_label_1M_container_to_label_1M_list");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label_1M_container");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label_1M_list");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // size:1024*1024+1, container
    string label_name_1M_plus_1_container = "label_1M_container";
    const char *label_name_1M_plus_1_container_ch = label_name_1M_plus_1_container.c_str();
    length = 1024 * 1024 -
             (model_3_part_1 + "container" + model_3_part_2 + label_name_1M_plus_1_container + model_3_part_3 +
                 model_3_part_4 + label_name_1M_plus_1_container + model_3_part_5)
                 .length() +
             1;
    string mark_1M_plus_1_container = "";
    string label_json_1M_plus_1_container = model_3_part_1 + "container" + model_3_part_2 +
                                            label_name_1M_plus_1_container + model_3_part_3 +
                                            WriteStrBySize(mark_1M_plus_1_container, length, " ") + model_3_part_4 +
                                            label_name_1M_plus_1_container + model_3_part_5;
    const char *label_json_1M_plus_1_container_ch = label_json_1M_plus_1_container.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_1M_plus_1_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);

    // size:1024*1024+1, list
    string label_name_1M_plus_1_list = "label_1M_list";
    const char *label_name_1M_plus_1_list_ch = label_name_1M_plus_1_list.c_str();
    length = 1024 * 1024 -
             (model_3_part_1 + "list" + model_3_part_2 + label_name_1M_plus_1_list + model_3_part_3_list +
                 model_3_part_4 + label_name_1M_plus_1_list + model_3_part_5_list)
                 .length() +
             1;
    string mark_1M_plus_1_list = "";
    string label_json_1M_plus_1_list = model_3_part_1 + "list" + model_3_part_2 + label_name_1M_plus_1_list +
                                       model_3_part_3_list + WriteStrBySize(mark_1M_plus_1_list, length, " ") +
                                       model_3_part_4 + label_name_1M_plus_1_list + model_3_part_5_list;
    const char *label_json_1M_plus_1_list_ch = label_json_1M_plus_1_list.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_1M_plus_1_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);

    // size:1024*1024+1, choice
    string label_name_1M_plus_1_choice = "label_1M_choice";
    const char *label_name_1M_plus_1_choice_ch = label_name_1M_plus_1_choice.c_str();
    length = 1024 * 1024 -
             (model_3_part_1 + "choice" + model_3_part_2 + label_name_1M_plus_1_choice + model_3_part_3 +
                 model_3_part_4 + label_name_1M_plus_1_choice + model_3_part_5)
                 .length() +
             1;
    string mark_1M_plus_1_choice = "";
    string label_json_1M_plus_1_choice = model_3_part_1 + "choice" + model_3_part_2 + label_name_1M_plus_1_choice +
                                         model_3_part_3 + WriteStrBySize(mark_1M_plus_1_choice, length, " ") +
                                         model_3_part_4 + label_name_1M_plus_1_choice + model_3_part_5;
    const char *label_json_1M_plus_1_choice_ch = label_json_1M_plus_1_choice.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_1M_plus_1_choice_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);

    // size:1024*1024+1, case
    string label_name_1M_plus_1_case = "label_1M_case";
    const char *label_name_1M_plus_1_case_ch = label_name_1M_plus_1_case.c_str();
    length = 1024 * 1024 -
             (model_3_part_1 + "case" + model_3_part_2 + label_name_1M_plus_1_case + model_3_part_3 + model_3_part_4 +
                 label_name_1M_plus_1_case + model_3_part_5)
                 .length() +
             1;
    string mark_1M_plus_1_case = "";
    string label_json_1M_plus_1_case = model_3_part_1 + "case" + model_3_part_2 + label_name_1M_plus_1_case +
                                       model_3_part_3 + WriteStrBySize(mark_1M_plus_1_case, length, " ") +
                                       model_3_part_4 + label_name_1M_plus_1_case + model_3_part_5;
    const char *label_json_1M_plus_1_case_ch = label_json_1M_plus_1_case.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_1M_plus_1_case_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);

    // size:1024*1024+1, edge
    string label_name_1M_plus_1_edge = "label_1M_edge_from_label_1M_container_to_label_1M_list";
    const char *label_name_1M_plus_1_edge_ch = label_name_1M_plus_1_edge.c_str();
    length = 1024 * 1024 -
             (model_4_part_1 + label_name_1M_plus_1_edge + model_4_part_2 + "label_1M_container" + model_4_part_3 +
                 "label_1M_list" + model_4_part_4 + model_4_part_5)
                 .length() +
             1;
    string mark_1M_plus_1_edge = "";
    string label_json_1M_plus_edge = model_4_part_1 + label_name_1M_plus_1_edge + model_4_part_2 +
                                     "label_1M_container" + model_4_part_3 + "label_1M_list" + model_4_part_4 +
                                     WriteStrBySize(mark_1M_plus_1_edge, length, " ") + model_4_part_5;
    const char *label_json_1M_plus_edge_ch = label_json_1M_plus_edge.c_str();
    ret = GmcCreateEdgeLabelAsync(
        g_stmt_async, label_json_1M_plus_edge_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
}

// 3.VertexLabel的Keys仅支持“primary”和“list_localhash”,且“list_localhash”只能用于list节点的唯一性，不能用于索引功能
TEST_F(yang_ddl, BasicSpecifitions_001_017_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container localhash
    string label_json_container_localhash =
        model_5_part_1 + "container" + model_5_part_2 + "localhash" + model_5_part_3;
    const char *label_json_container_localhash_ch = label_json_container_localhash.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_localhash_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async, "label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // container list_localhash
    string label_json_container_list_localhash =
        model_5_part_1 + "container" + model_5_part_2 + "list_localhash" + model_5_part_3;
    const char *label_json_container_list_localhash_ch = label_json_container_list_localhash.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_list_localhash_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // list localhash
    string label_json_list_localhash = model_5_part_1 + "list" + model_5_part_2_list + "localhash" + model_5_part_3;
    const char *label_json_list_localhash_ch = label_json_list_localhash.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_localhash_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async, "label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // list list_localhash
    string label_json_list_list_localhash =
        model_5_part_1 + "list" + model_5_part_2_list + "list_localhash" + model_5_part_3_null_check;
    const char *label_json_list_list_localhash_ch = label_json_list_list_localhash.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_list_localhash_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 4.container、choice、case节点主键只能有一个字段且字段类型必须为uint32
TEST_F(yang_ddl, BasicSpecifitions_001_017_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container pk index:1个字段
    string str_1 = "";
    string str_2 = "";
    string json_container_pk_1 = model_6_part_1 + "container" + model_6_part_2 + WriteFieldByNum(str_1, 5) +
                                 model_6_part_3 + WriteIndexFieldByNum(str_2, 1) + model_6_part_4;
    const char *json_container_pk_1_ch = json_container_pk_1.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, json_container_pk_1_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // container pk index:2个字段, 报错
    string str_3 = "";
    string str_4 = "";
    string json_container_pk_2 = model_6_part_1 + "container" + model_6_part_2 + WriteFieldByNum(str_3, 5) +
                                 model_6_part_3 + WriteIndexFieldByNum(str_4, 2) + model_6_part_4;
    const char *json_container_pk_2_ch = json_container_pk_2.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, json_container_pk_2_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
}

// list的主键和list_localhash支持最多32个字段
TEST_F(yang_ddl, BasicSpecifitions_001_017_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmt_async, "schema/32_list_F.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmt_async, "schema/33_list_F.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = CreateVertexLabelAsync(g_stmt_async, "schema/32_list_localhash_F.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmt_async, "schema/33_list_localhash_F.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
}

// list_localhash最大长度规格536(DDL不报错，DML报错，改规格列表)
TEST_F(yang_ddl, BasicSpecifitions_001_017_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // pk index_account_size:536
    string label_name_index_size_536 = "label_name_index_account_size_536";
    const char *label_name_index_size_536_ch = label_name_index_size_536.c_str();
    string label_json_index_size_536 = model_7_part_1 + label_name_index_size_536 + model_7_part_2 +
                                       to_string(536 - 32 * 2 + 100) + model_7_part_3 + label_name_index_size_536 +
                                       model_7_part_4 + "primary" + model_7_part_5;
    const char *label_json_index_size_536_ch = label_json_index_size_536.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_index_size_536_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_index_size_536_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// VertexLabel中list_localhash最大个数规格16
TEST_F(yang_ddl, BasicSpecifitions_001_017_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // list_localhash_num:16
    string label_name_list_localhash_16 = "label_name_list_localhash_num_16";
    const char *label_name_list_localhash_16_ch = label_name_list_localhash_16.c_str();
    string list_localhash_keys_16 = "";
    string label_json_list_localhash_16 =
        model_8_part_1 + label_name_list_localhash_16 + model_8_part_2 + label_name_list_localhash_16 + model_8_part_3 +
        WriteListKeyByNum(list_localhash_keys_16, 16, label_name_list_localhash_16_ch);
    const char *label_json_list_localhash_16_ch = label_json_list_localhash_16.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_localhash_16_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_list_localhash_16_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_localhash_num:17
    string label_name_list_localhash_17 = "label_name_list_localhash_num_17";
    const char *label_name_list_localhash_17_ch = label_name_list_localhash_17.c_str();
    string list_localhash_keys_17 = "";
    string label_json_list_localhash_17 =
        model_8_part_1 + label_name_list_localhash_17 + model_8_part_2 + label_name_list_localhash_17 + model_8_part_3 +
        WriteListKeyByNum(list_localhash_keys_17, 17, label_name_list_localhash_17_ch);
    const char *label_json_list_localhash_17_ch = label_json_list_localhash_17.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_localhash_17_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
}

// 支持建VertexLabel个数5000
TEST_F(yang_ddl, BasicSpecifitions_001_017_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    for (int i = 0; i < 1000; i++) {
        // 500 个 container
        string label_name_container = "label_name_container_" + to_string(i + 1);
        string label_json_container = model_1_part_1 + "container" + model_1_part_2 + label_name_container +
                                      model_1_part_3 + label_name_container + model_1_part_4;
        const char *label_json_container_ch = label_json_container.c_str();
        ret = GmcCreateVertexLabelAsync(
            g_stmt_async, label_json_container_ch, g_labelConfig, create_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        // 500 个 list
        string label_name_list = "label_name_list_" + to_string(i + 1);
        string label_json_list = model_1_part_1 + "list" + model_1_part_2 + label_name_list + model_1_part_3_list +
                                 label_name_list + model_1_part_4_list;
        const char *label_json_list_ch = label_json_list.c_str();
        ret = GmcCreateVertexLabelAsync(
            g_stmt_async, label_json_list_ch, g_labelConfig, create_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    // 超出数量限制的container
    string label_name_container = "label_name_container_over_limit";
    string label_json_container = model_1_part_1 + "container" + model_1_part_2 + label_name_container +
                                  model_1_part_3 + label_name_container + model_1_part_4;
    const char *label_json_container_ch = label_json_container.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);

    // 超出数量限制的list
    string label_name_list = "label_name_list_over_limit";
    string label_json_list = model_1_part_1 + "list" + model_1_part_2 + label_name_list + model_1_part_3_list +
                             label_name_list + model_1_part_4_list;
    const char *label_json_list_ch = label_json_list.c_str();
    ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);

    // 其他类型的表（edge）不受影响
    string label_name_edge = "label_name_edge";
    const char *label_name_edge_ch = label_name_edge.c_str();
    string label_json_513_edge = model_2_part_1 + label_name_edge + model_2_part_2 + "label_name_container_1" +
                                 model_2_part_3 + "label_name_list_1" + model_2_part_4;
    const char *label_json_513_edge_ch = label_json_513_edge.c_str();
    ret = GmcCreateEdgeLabelAsync(
        g_stmt_async, label_json_513_edge_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, "label_name_edge");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 500; i++) {
        string label_name_container = "label_name_container_" + to_string(i + 1);
        const char *label_name_container_ch = label_name_container.c_str();
        ret = GmcDropVertexLabel(g_stmt, label_name_container_ch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        string label_name_list = "label_name_list_" + to_string(i + 1);
        const char *label_name_list_ch = label_name_list.c_str();
        ret = GmcDropVertexLabel(g_stmt, label_name_list_ch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 表名、节点名、属性名支持下划线“_”开头命名
TEST_F(yang_ddl, BasicSpecifitions_001_017_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container
    string label_name_container = "_label_name_container";
    const char *label_name_container_ch = label_name_container.c_str();
    string label_json_container = model_9_part_1 + "container" + model_9_part_2 + label_name_container +
                                  model_9_part_3 + label_name_container + model_9_part_4;
    const char *label_json_container_ch = label_json_container.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // list
    string label_name_list = "label_name_list";
    const char *label_name_list_ch = label_name_list.c_str();
    string label_json_list = model_9_part_1 + "list" + model_9_part_2 + label_name_list + model_9_part_3_list +
                             label_name_list + model_9_part_4_list;
    const char *label_json_list_ch = label_json_list.c_str();
    ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // edge
    string label_name_edge = "_label_name_edge";
    const char *label_name_edge_ch = label_name_edge.c_str();
    string label_json_edge = model_10_part_1 + label_name_edge + model_10_part_2 + label_name_container +
                             model_10_part_3 + label_name_list + model_10_part_4;
    const char *label_json_edge_ch = label_json_edge.c_str();
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, label_json_edge_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_container_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_list_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 必须指定 "auto_increment"为一个非0值（仅会在 list 中报错）
TEST_F(yang_ddl, BasicSpecifitions_001_017_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // wrong config:yang_model=0, auto_increment=0
    const char *wrong_config = R"({"auto_increment": 0, "isFastReadUncommitted": 0, "yang_model": 1})";
    // wrong config:yang_model!=0, auto_increment=0
    const char *right_config = R"({"auto_increment": 1, "isFastReadUncommitted": 0, "yang_model": 1})";

    string label_name_list = "label_name_list";
    const char *label_name_list_ch = label_name_list.c_str();
    string label_json_list = model_1_part_1 + "list" + model_1_part_2 + label_name_list + model_1_part_3_list +
                             label_name_list + model_1_part_4_list;
    const char *label_json_list_ch = label_json_list.c_str();

    // 自增ID字段固定值为1，不受用户输入的值控制
    int ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_list_ch, wrong_config, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, label_name_list_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_list_ch, right_config, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabel(g_stmt, label_name_list_ch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// presence属性定义仅能用于container类型节点(不能与默认值不同)
TEST_F(yang_ddl, BasicSpecifitions_001_017_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container 有 presence
    string label_json_container_with_presence =
        model_11_part_1 + "container" + model_11_part_2 + R"("presence":true)" + model_11_part_3;
    const char *label_json_container_with_presence_ch = label_json_container_with_presence.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_with_presence_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list 有 presence, 报错
    string label_json_list_with_presence =
        model_11_part_1 + "list" + model_11_part_2 + R"("presence":true)" + model_11_part_3_list;
    const char *label_json_list_with_presence_ch = label_json_list_with_presence.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_with_presence_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // choice 有 presence, 报错
    string label_json_choice_with_presence =
        model_11_part_1 + "choice" + model_11_part_2 + R"("presence":true)" + model_11_part_3;
    const char *label_json_choice_with_presence_ch = label_json_choice_with_presence.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_choice_with_presence_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // case 有 presence, 报错
    string label_json_case_with_presence =
        model_11_part_1 + "case" + model_11_part_2 + R"("presence":true)" + model_11_part_3;
    const char *label_json_case_with_presence_ch = label_json_case_with_presence.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_case_with_presence_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
}

// nullable属性定义仅能用于choice类型节点和property字段(不能与默认值不同)
TEST_F(yang_ddl, BasicSpecifitions_001_017_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container 有 nullable, 报错
    string label_json_container_with_nullable =
        model_11_part_1 + "container" + model_11_part_2 + R"("nullable":false)" + model_11_part_3;
    const char *label_json_container_with_nullable_ch = label_json_container_with_nullable.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_with_nullable_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // list 有 nullable, 报错
    string label_json_list_with_nullable =
        model_11_part_1 + "list" + model_11_part_2 + R"("nullable":false)" + model_11_part_3_list;
    const char *label_json_list_with_nullable_ch = label_json_list_with_nullable.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_with_nullable_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // choice 有 nullable
    const char *label_json_choice_with_nullable_ch = con_choice_label.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_choice_with_nullable_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // case 有 nullable, 报错
    string label_json_case_with_nullable =
        model_11_part_1 + "case" + model_11_part_2 + R"("nullable":false)" + model_11_part_3;
    const char *label_json_case_with_nullable_ch = label_json_case_with_nullable.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_case_with_nullable_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
}

// 仅case节点有default属性(不能与默认值不同)
TEST_F(yang_ddl, BasicSpecifitions_001_017_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container 有 nullable, 报错
    string label_json_container_with_default =
        model_11_part_1 + "container" + model_11_part_2 + R"("default":true)" + model_11_part_3;
    const char *label_json_container_with_default_ch = label_json_container_with_default.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_with_default_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // list 有 nullable, 报错
    string label_json_list_with_default =
        model_11_part_1 + "list" + model_11_part_2 + R"("default":true)" + model_11_part_3_list;
    const char *label_json_list_with_default_ch = label_json_list_with_default.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_with_default_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // choice 有 nullable, 报错
    string label_json_choice_with_default =
        model_11_part_1 + "choice" + model_11_part_2 + R"("default":true)" + model_11_part_3;
    const char *label_json_choice_with_default_ch = label_json_choice_with_default.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_choice_with_default_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // case 有 nullable
    const char *label_json_case_with_default_ch = con_choice_case_label.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_case_with_default_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// choice的dest只能是case，case的src只能是choice(不能与默认值不同)
TEST_F(yang_ddl, BasicSpecifitions_001_017_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container
    string label_json_container = model_1_part_1 + "container" + model_1_part_2 + "label_container" + model_1_part_3 +
                                  "label_container" + model_1_part_4;
    const char *label_json_container_ch = label_json_container.c_str();
    string label_json_list = model_1_part_1 + "list" + model_1_part_2 + "label_list" + model_1_part_3_list +
                             "label_list" + model_1_part_4_list;
    const char *label_json_list_ch = label_json_list.c_str();
    string label_json_choice =
        model_1_part_1 + "choice" + model_1_part_2 + "label_choice" + model_1_part_3 + "label_choice" + model_1_part_4;
    const char *label_json_choice_ch = label_json_choice.c_str();
    string label_json_case =
        model_1_part_1 + "case" + model_1_part_2 + "label_case" + model_1_part_3 + "label_case" + model_1_part_4;
    const char *label_json_case_ch = label_json_case.c_str();

    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_list_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_choice_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_case_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    const char *label_json_edge_choice_to_case_ch = con_choice_case_label.c_str();
    ret =
        GmcCreateVertexLabelAsync(g_stmt_async, label_json_edge_choice_to_case_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    const char *label_json_edge_container_to_case_ch = con_case_label.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_edge_container_to_case_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    const char *label_json_edge_list_to_case_ch = list_case_label.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_edge_list_to_case_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label_container");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label_list");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// default = true的case不能和nullable = false的choice建边
TEST_F(yang_ddl, BasicSpecifitions_001_017_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    const char *label_choice_false_case_true = R"(
{
    "type":"container",
    "name":"label",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true},
        {
            "type":"choice",
            "name":"label2",
            "nullable":false,
            "fields":[
                {
                    "type":"case",
                    "name":"label3",
                    "default":true,
                    "fields":[
                        {"name":"F1", "type":"uint32", "nullable":true}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"label",
            "name":"pk",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_choice_false_case_true, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// container"ID"不为uint32, 报错
TEST_F(yang_ddl, BasicSpecifitions_001_017_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // container "ID"不为 uint32, 报错
    string label_json_container_ID_int32 = model_12_part_1 + "container" + model_12_part_2 + "int32" + model_12_part_3;
    const char *label_json_container_ID_int32_ch = label_json_container_ID_int32.c_str();
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_container_ID_int32_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // choice "ID"不为 uint32, 报错
    string label_json_choice_ID_int32 = model_12_part_1 + "choice" + model_12_part_2 + "int32" + model_12_part_3;
    const char *label_json_choice_ID_int32_ch = label_json_choice_ID_int32.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_choice_ID_int32_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // case "ID"不为 uint32, 报错
    string label_json_case_ID_int32 = model_12_part_1 + "case" + model_12_part_2 + "int32" + model_12_part_3;
    const char *label_json_case_ID_int32_ch = label_json_case_ID_int32.c_str();
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_case_ID_int32_ch, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    // list "ID" 或 "PID" 不为 uint32, 报错
    const char *label_json_list_ID_int32 = R"(
{
    "type":"list",
    "name":"label",
    "fields":[
        {"name":"ID", "type":"int32", "nullable":false},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true}
    ],
    "keys":[
        {
            "node":"label",
            "name":"pk",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
    )";
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_ID_int32, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    const char *label_json_list_PID_int32 = R"(
{
    "type":"list",
    "name":"label",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {"name":"PID", "type":"int32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true}
    ],
    "keys":[
        {
            "node":"label",
            "name":"pk",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
    )";
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, label_json_list_PID_int32, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
}

// 边表建边条件必须指定为源顶点的“ID”和目的顶点的“PID”字段
TEST_F(yang_ddl, BasicSpecifitions_001_017_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    // 边表建边条件必须指定为源顶点的“ID”和目的顶点的“PID”字段
    const char *source_json = R"(
{
    "type":"container",
    "name":"label_source",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true},
        {"name":"F2", "type":"uint32", "nullable":true},
        {"name":"F3", "type":"uint32", "nullable":true}
    ],
    "keys":[
        {
            "node":"label_source",
            "name":"pk",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
    )";

    const char *dest_json = R"(
{
    "type":"container",
    "name":"label_dest",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"G1", "type":"uint32", "nullable":true},
        {"name":"G2", "type":"uint32", "nullable":true},
        {"name":"G3", "type":"uint32", "nullable":true}
    ],
    "keys":[
        {
            "node":"label_dest",
            "name":"pk",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
    )";

    const char *edge_json = R"(
[
    {
        "name":"edge_label",
        "source_vertex_label":"label_source",
        "dest_vertex_label":"label_dest",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "F1",
                    "dest_property": "G1"
                }
            ]
        }
    }
]
    )";

    int ret = GmcCreateVertexLabelAsync(g_stmt_async, source_json, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, dest_json, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edge_json, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label_source");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "label_dest");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
