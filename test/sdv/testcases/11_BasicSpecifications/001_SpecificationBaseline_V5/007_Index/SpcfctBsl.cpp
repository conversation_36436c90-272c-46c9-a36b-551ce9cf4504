/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "ctime"
using namespace std;

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 128
char command[MAX_CMD_SIZE];
int ret = 0;
GmcConnT *conn;
GmcStmtT *stmt;

class SpecificationBaseline_001_007 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SpecificationBaseline_001_007::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    // 建连
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void SpecificationBaseline_001_007::TearDownTestCase()
{
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
}

void SpecificationBaseline_001_007::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0},
        errorMsg4[errCodeLen] = {0}, errorMsg5[errCodeLen] = {0}, errorMsg6[errCodeLen] = {0},
        errorMsg7[errCodeLen] = {0}, errorMsg8[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    (void)snprintf(errorMsg6, errCodeLen, "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    (void)snprintf(errorMsg7, errCodeLen, "GMERR-%d", GMERR_INVALID_VALUE);
    (void)snprintf(errorMsg8, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(8, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6,
        errorMsg7, errorMsg8);
}
void SpecificationBaseline_001_007::TearDown()
{
    AW_CHECK_LOG_END();
}

string key_type_json(string type, string key)
{
    string p_1 = "[{\"type\":\"record\",\"name\":\"label\",\"fields\":[{\"name\":\"A\",\"type\":\"";
    string p_2 = "\"}],\"keys\":[{\"node\":\"label\",\"name\":\"pk\",\"fields\":[\"A\"],\"index\":{\"type\":\"";
    string p_3 = "\"},\"constraints\":{\"unique\":true}}]}]";
    string json = p_1 + type + p_2 + key + p_3;
    return json;
}
// 1.索引字段类型为double、float、boolean建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_001)
{
    // 各类索引指定double字段
    string json = key_type_json("double", "primary");
    const char *json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("double", "localhash");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("double", "local");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("double", "hashcluster");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 各类索引指定float字段
    json = key_type_json("float", "primary");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("float", "localhash");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("float", "local");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("float", "hashcluster");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 各类索引指定boolean字段
    json = key_type_json("boolean", "primary");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("boolean", "localhash");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("boolean", "local");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = key_type_json("boolean", "hashcluster");
    json_ch = json.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_NE(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 2.没有索引建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_002)
{
    const char *json = R"(
        [ {"type" : "record", "name" : "label", "fields" : [ {"name" : "A", "type" : "fixed", "size" : 1} ]} ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

string adjust_member_key_num(int key_num)
{
    const char *p_1 = "[{\"type\":\"record\",\"name\":\"label\",\"fields\":[{\"name\":\"A\",\"type\": \"record\", "
                      "\"vector\": true,\"fields\": [";
    const char *p_2 = "]}],\"keys\":[";
    const char *p_3 = "]}]";
    string str_fields = "";
    string str_keys = "";
    string field[key_num];
    string key[key_num];
    for (int i = 0; i < key_num; i++) {
        field[i] = "{\"name\":\"a" + to_string(i) + "\", \"type\":\"int32\"}";
        key[i] = "{\"node\":\"A\",\"name\":\"key_" + to_string(i) + "\",\"fields\":[\"a" + to_string(i) +
                 "\"],\"index\":{\"type\":\"none\"},\"constraints\":{\"unique\":true}}";
        str_fields += field[i];
        str_keys += key[i];
        if (i < key_num - 1) {
            str_fields += ",";
            str_keys += ",";
        }
    }
    string str = p_1 + str_fields + p_2 + str_keys + p_3;
    return str;
}

// 3.64个member_key建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_003)
{
    string str = adjust_member_key_num(64);
    const char *json = str.c_str();
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 4.65个member_key建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_004)
{
    string str = adjust_member_key_num(65);
    const char *json = str.c_str();
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 5.primary key索引字段个数为1建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_005)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [ {"name" : "A", "type" : "fixed", "size" : 1} ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 6.primary key索引字段个数0建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_006)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [ {"name" : "A", "type" : "fixed", "size" : 1} ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : [],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 7.primary key索引字段个数为33建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_007)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1},
            {"name" : "Q", "type" : "fixed", "size" : 1},
            {"name" : "R", "type" : "fixed", "size" : 1},
            {"name" : "S", "type" : "fixed", "size" : 1},
            {"name" : "T", "type" : "fixed", "size" : 1},
            {"name" : "U", "type" : "fixed", "size" : 1},
            {"name" : "V", "type" : "fixed", "size" : 1},
            {"name" : "W", "type" : "fixed", "size" : 1},
            {"name" : "X", "type" : "fixed", "size" : 1},
            {"name" : "Y", "type" : "fixed", "size" : 1},
            {"name" : "Z", "type" : "fixed", "size" : 1},
            {"name" : "A1", "type" : "fixed", "size" : 1},
            {"name" : "B1", "type" : "fixed", "size" : 1},
            {"name" : "C1", "type" : "fixed", "size" : 1},
            {"name" : "D1", "type" : "fixed", "size" : 1},
            {"name" : "E1", "type" : "fixed", "size" : 1},
            {"name" : "F1", "type" : "fixed", "size" : 1},
            {"name" : "G1", "type" : "fixed", "size" : 1}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : [ "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
                "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z", "A1", "B1", "C1", "D1",
                "E1", "F1", "G1"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 8.17个localhash_key建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_008)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1},
            {"name" : "Q", "type" : "fixed", "size" : 1}
        ],
        "keys" : [
            {
                "node" : "label",
                "name" : "localhash_key_1",
                "fields" : ["A"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_2",
                "fields" : ["B"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_3",
                "fields" : ["C"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_4",
                "fields" : ["D"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_5",
                "fields" : ["E"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_6",
                "fields" : ["F"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_7",
                "fields" : ["G"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_8",
                "fields" : ["H"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_9",
                "fields" : ["I"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_10",
                "fields" : ["J"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_11",
                "fields" : ["K"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_12",
                "fields" : ["L"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_13",
                "fields" : ["M"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_14",
                "fields" : ["N"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_15",
                "fields" : ["O"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_16",
                "fields" : ["P"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_17",
                "fields" : ["Q"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 9.local_key、localhash_key、hashcluster_key总共16个建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_009)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1}
        ],
        "keys" : [
            {
                "node" : "label",
                "name" : "localhash_key_1",
                "fields" : ["A"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_2",
                "fields" : ["B"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_3",
                "fields" : ["C"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_4",
                "fields" : ["D"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_5",
                "fields" : ["E"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_1",
                "fields" : ["F"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_2",
                "fields" : ["G"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_3",
                "fields" : ["H"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_4",
                "fields" : ["I"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_5",
                "fields" : ["J"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_1",
                "fields" : ["K"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_2",
                "fields" : ["L"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_3",
                "fields" : ["M"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_4",
                "fields" : ["N"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_5",
                "fields" : ["O"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_6",
                "fields" : ["P"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK)
        ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 10.local_key、localhash_key、hashcluster_key总共17个建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_010)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1},
            {"name" : "Q", "type" : "fixed", "size" : 1}
        ],
        "keys" : [
            {
                "node" : "label",
                "name" : "localhash_key_1",
                "fields" : ["A"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_2",
                "fields" : ["B"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_3",
                "fields" : ["C"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_4",
                "fields" : ["D"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key_5",
                "fields" : ["E"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_1",
                "fields" : ["F"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_2",
                "fields" : ["G"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_3",
                "fields" : ["H"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_4",
                "fields" : ["I"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key_5",
                "fields" : ["J"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_1",
                "fields" : ["K"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_2",
                "fields" : ["L"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_3",
                "fields" : ["M"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_4",
                "fields" : ["N"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_5",
                "fields" : ["O"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_6",
                "fields" : ["P"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key_7",
                "fields" : ["Q"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 11.Local Hash Key索引字段个数为9建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_011)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1},
            {"name" : "Q", "type" : "fixed", "size" : 1},
            {"name" : "R", "type" : "fixed", "size" : 1},
            {"name" : "S", "type" : "fixed", "size" : 1},
            {"name" : "T", "type" : "fixed", "size" : 1},
            {"name" : "U", "type" : "fixed", "size" : 1},
            {"name" : "V", "type" : "fixed", "size" : 1},
            {"name" : "W", "type" : "fixed", "size" : 1},
            {"name" : "X", "type" : "fixed", "size" : 1},
            {"name" : "Y", "type" : "fixed", "size" : 1},
            {"name" : "Z", "type" : "fixed", "size" : 1},
            {"name" : "A1", "type" : "fixed", "size" : 1},
            {"name" : "B1", "type" : "fixed", "size" : 1},
            {"name" : "C1", "type" : "fixed", "size" : 1},
            {"name" : "D1", "type" : "fixed", "size" : 1},
            {"name" : "E1", "type" : "fixed", "size" : 1},
            {"name" : "F1", "type" : "fixed", "size" : 1},
            {"name" : "G1", "type" : "fixed", "size" : 1}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "localhash_key_1",
            "fields" : [ "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
                "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z", "A1", "B1", "C1", "D1",
                "E1", "F1", "G1"],
            "index" : {"type" : "localhash"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 12.Local Key索引字段个数为9建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_012)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1},
            {"name" : "Q", "type" : "fixed", "size" : 1},
            {"name" : "R", "type" : "fixed", "size" : 1},
            {"name" : "S", "type" : "fixed", "size" : 1},
            {"name" : "T", "type" : "fixed", "size" : 1},
            {"name" : "U", "type" : "fixed", "size" : 1},
            {"name" : "V", "type" : "fixed", "size" : 1},
            {"name" : "W", "type" : "fixed", "size" : 1},
            {"name" : "X", "type" : "fixed", "size" : 1},
            {"name" : "Y", "type" : "fixed", "size" : 1},
            {"name" : "Z", "type" : "fixed", "size" : 1},
            {"name" : "A1", "type" : "fixed", "size" : 1},
            {"name" : "B1", "type" : "fixed", "size" : 1},
            {"name" : "C1", "type" : "fixed", "size" : 1},
            {"name" : "D1", "type" : "fixed", "size" : 1},
            {"name" : "E1", "type" : "fixed", "size" : 1},
            {"name" : "F1", "type" : "fixed", "size" : 1},
            {"name" : "G1", "type" : "fixed", "size" : 1}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "local_key_1",
            "fields" : [ "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
                "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z", "A1", "B1", "C1", "D1",
                "E1", "F1", "G1"],
            "index" : {"type" : "local"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 13.hashcluster索引字段个数为9建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_013)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 1},
            {"name" : "B", "type" : "fixed", "size" : 1},
            {"name" : "C", "type" : "fixed", "size" : 1},
            {"name" : "D", "type" : "fixed", "size" : 1},
            {"name" : "E", "type" : "fixed", "size" : 1},
            {"name" : "F", "type" : "fixed", "size" : 1},
            {"name" : "G", "type" : "fixed", "size" : 1},
            {"name" : "H", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "I", "type" : "fixed", "size" : 1},
            {"name" : "J", "type" : "fixed", "size" : 1},
            {"name" : "K", "type" : "fixed", "size" : 1},
            {"name" : "L", "type" : "fixed", "size" : 1},
            {"name" : "M", "type" : "fixed", "size" : 1},
            {"name" : "N", "type" : "fixed", "size" : 1},
            {"name" : "O", "type" : "fixed", "size" : 1},
            {"name" : "P", "type" : "fixed", "size" : 1},
            {"name" : "Q", "type" : "fixed", "size" : 1},
            {"name" : "R", "type" : "fixed", "size" : 1},
            {"name" : "S", "type" : "fixed", "size" : 1},
            {"name" : "T", "type" : "fixed", "size" : 1},
            {"name" : "U", "type" : "fixed", "size" : 1},
            {"name" : "V", "type" : "fixed", "size" : 1},
            {"name" : "W", "type" : "fixed", "size" : 1},
            {"name" : "X", "type" : "fixed", "size" : 1},
            {"name" : "Y", "type" : "fixed", "size" : 1},
            {"name" : "Z", "type" : "fixed", "size" : 1},
            {"name" : "A1", "type" : "fixed", "size" : 1},
            {"name" : "B1", "type" : "fixed", "size" : 1},
            {"name" : "C1", "type" : "fixed", "size" : 1},
            {"name" : "D1", "type" : "fixed", "size" : 1},
            {"name" : "E1", "type" : "fixed", "size" : 1},
            {"name" : "F1", "type" : "fixed", "size" : 1},
            {"name" : "G1", "type" : "fixed", "size" : 1}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "hashcluster_key_1",
            "fields" : [ "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
                "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z", "A1", "B1", "C1", "D1",
                "E1", "F1", "G1" ],
            "index" : {"type" : "hashcluster"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 14.同一节点下,member_key索引字段个数为9建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_014)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [ {
            "name" : "A",
            "type" : "record",
            "vector" : true,
            "fields" : [
                {"name" : "A", "type" : "fixed", "size" : 1},
                {"name" : "B", "type" : "fixed", "size" : 1},
                {"name" : "C", "type" : "fixed", "size" : 1},
                {"name" : "D", "type" : "fixed", "size" : 1},
                {"name" : "E", "type" : "fixed", "size" : 1},
                {"name" : "F", "type" : "fixed", "size" : 1},
                {"name" : "G", "type" : "fixed", "size" : 1},
                {"name" : "H", "type" : "fixed", "size" : 1},
                {"name" : "I", "type" : "fixed", "size" : 1},
                {"name" : "I", "type" : "fixed", "size" : 1},
                {"name" : "I", "type" : "fixed", "size" : 1},
                {"name" : "J", "type" : "fixed", "size" : 1},
                {"name" : "K", "type" : "fixed", "size" : 1},
                {"name" : "L", "type" : "fixed", "size" : 1},
                {"name" : "M", "type" : "fixed", "size" : 1},
                {"name" : "N", "type" : "fixed", "size" : 1},
                {"name" : "O", "type" : "fixed", "size" : 1},
                {"name" : "P", "type" : "fixed", "size" : 1},
                {"name" : "Q", "type" : "fixed", "size" : 1},
                {"name" : "R", "type" : "fixed", "size" : 1},
                {"name" : "S", "type" : "fixed", "size" : 1},
                {"name" : "T", "type" : "fixed", "size" : 1},
                {"name" : "U", "type" : "fixed", "size" : 1},
                {"name" : "V", "type" : "fixed", "size" : 1},
                {"name" : "W", "type" : "fixed", "size" : 1},
                {"name" : "X", "type" : "fixed", "size" : 1},
                {"name" : "Y", "type" : "fixed", "size" : 1},
                {"name" : "Z", "type" : "fixed", "size" : 1},
                {"name" : "A1", "type" : "fixed", "size" : 1},
                {"name" : "B1", "type" : "fixed", "size" : 1},
                {"name" : "C1", "type" : "fixed", "size" : 1},
                {"name" : "D1", "type" : "fixed", "size" : 1},
                {"name" : "E1", "type" : "fixed", "size" : 1},
                {"name" : "F1", "type" : "fixed", "size" : 1},
                {"name" : "G1", "type" : "fixed", "size" : 1}
            ]
        } ],
        "keys" : [ {
            "node" : "A",
            "name" : "member_key_1",
            "fields" : [ "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
                "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z", "A1", "B1", "C1", "D1",
                "E1", "F1", "G1" ],
            "index" : {"type" : "none"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    if (ret == GMERR_OK) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

GmcNodeT *node_root, *node_T1, *node_T2;

// 15.delta对象中一个node的更新操作1024次
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_015)
{
    char *test_schema = NULL;
    readJanssonFile("schema_file/normal_structure_all_node.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t pk_value = 1;
    ret = GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1024; i++) {
        ret = GmcNodeGetElementByIndex(node_T1, i, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(node_T2, i, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(stmt, "mylabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 16.delta对象中一个node的更新操作1025次
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_016)
{
    char *test_schema = NULL;
    readJanssonFile("schema_file/normal_structure_all_node.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t pk_value = 1;
    ret = GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1024; i++) {
        ret = GmcNodeGetElementByIndex(node_T1, i, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(node_T2, i, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t index = 0;
    ret = GmcNodeGetElementByIndex(node_T1, index, &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(node_T2, index, &node_T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &index, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &index, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(stmt, "mylabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 17.delta对象中一个node的append操作1024次
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_017)
{
    char *test_schema = NULL;
    readJanssonFile("schema_file/normal_structure_all_node.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value = 1;
    ret = GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(stmt, "mylabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 18.delta对象中一个node的append500次,再remove500,append524次
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_018)
{
    char *test_schema = NULL;
    readJanssonFile("schema_file/normal_structure_all_node.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value = 1;
    ret = GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < 1024; i++) {
        ret = GmcNodeRemoveElementByIndex(node_T1, i);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(node_T2, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(stmt, "mylabel");
    EXPECT_EQ(GMERR_OK, ret);
}
// 树模型增量更新操作码上限调整，由1024增加至2048：2023-11-10
// 19.delta对象中一个node的append500次,再remove500,append525次  --> 一个node的append1024次,再remove 1024,append 1024次
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_019)
{
    char *test_schema = NULL;
    readJanssonFile("schema_file/normal_structure_all_node.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value = 1;
    ret = GmcSetVertexProperty(stmt, "id", GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "mylabel", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &node_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node_root, (char *)"T2", &node_T2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &pk_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < 1024; i++) {
        ret = GmcNodeRemoveElementByIndex(node_T1, i);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(node_T2, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < 1024; i++) {
        ret = GmcNodeAppendElement(node_T1, &node_T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T2, &node_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T1, "t1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node_T2, "t2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcNodeAppendElement(node_T1, &node_T1);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcNodeAppendElement(node_T2, &node_T2);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(stmt, "mylabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 20.bitmap size为8建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_020)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 8, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 21.bitmap size为32768建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_021)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 32768, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 22.bitmap size为0或7建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_022)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 0, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 7, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 23.bitmap size为32769或32776建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_023)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 32769, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 32776, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 24.bitmap size为100建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_024)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 100, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 25.bitmap 不设置size建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_025)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["A"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 26.bitmap 为索引字段建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_026)
{
    const char *json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 128, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "pk",
            "fields" : ["B"],
            "index" : {"type" : "primary"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 128, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "localhash_key",
            "fields" : ["B"],
            "index" : {"type" : "localhash"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 128, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "local_key",
            "fields" : ["B"],
            "index" : {"type" : "local"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }

    json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 128, "nullable" : false},
            {"name" : "B", "type" : "bitmap", "size" : 128, "nullable" : true}
        ],
        "keys" : [ {
            "node" : "label",
            "name" : "hashcluster_key",
            "fields" : ["B"],
            "index" : {"type" : "hashcluster"},
            "constraints" : {"unique" : true}
        } ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, json, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 27.表名中带有特殊字段建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_027)
{
    string p_1 = "[{\"type\":\"record\",\"name\":\"a";
    string p_2 =
        "\",\"fields\":[{\"name\":\"A\",\"type\":\"fixed\",\"size\":128,\"nullable\":false}],\"keys\":[{\"node\":\"a";
    string p_3 = "\",\"name\":\"primary_key\",\"fields\":[\"A\"],\"index\":{\"type\":\"primary\"},\"constraints\":{"
                 "\"unique\":true}}]}]";
    string special_mark[27] = {"~",
        "!",
        "@",
        "#",
        "$",
        "￥",
        "%",
        "^",
        "&",
        "*",
        "(",
        ")",
        "+",
        "=",
        "[",
        "]",
        "{",
        "}",
        "\\",
        " ",
        "|",
        "<",
        ">",
        "?",
        "/",
        "☆",
        "α"};
    for (int i = 0; i < 27; i++) {
        string str = p_1 + special_mark[i] + p_2 + special_mark[i] + p_3;
        const char *json = str.c_str();
        string label_name = "a" + special_mark[i];
        const char *lable_name_ch = label_name.c_str();
        ret = GmcCreateVertexLabel(stmt, json, NULL);
        EXPECT_NE(GMERR_OK, ret);
        if (ret == 0) {
            ret = GmcDropVertexLabel(stmt, lable_name_ch);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

// 28.建1024张kv表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_028)
{
    const char *kv_config = R"({"max_record_num" : 10000, "max_record_num_check" : true})";
    string kv_name[1024];
    uint32_t tableCnt = 0;
    uint32_t tableNum = 1024;

    ret = TestGetKvTableNum(&tableCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tableNum = tableNum - tableCnt;
    for (int i = 0; i < tableNum; i++) {
        kv_name[i] = "a" + to_string(i);
        const char *kv_name_ch = kv_name[i].c_str();
        ret = GmcKvCreateTable(stmt, kv_name_ch, kv_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tableNum; i++) {
        kv_name[i] = "a" + to_string(i);
        const char *kv_name_ch = kv_name[i].c_str();
        ret = GmcKvDropTable(stmt, kv_name_ch);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 29.建1025张kv表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_029)
{
    const char *kv_config = R"({"max_record_num" : 10000, "max_record_num_check" : true})";
    string kv_name[1024];
    uint32_t tableCnt = 0;
    uint32_t tableNum = 1024;

    ret = TestGetKvTableNum(&tableCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tableNum = tableNum - tableCnt;
    for (int i = 0; i < tableNum; i++) {
        kv_name[i] = "a" + to_string(i);
        const char *kv_name_ch = kv_name[i].c_str();
        ret = GmcKvCreateTable(stmt, kv_name_ch, kv_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
    const char *kv_name_1025 = "a_1025";
    ret = GmcKvCreateTable(stmt, kv_name_1025, kv_config);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    for (int i = 0; i < tableNum; i++) {
        kv_name[i] = "a" + to_string(i);
        const char *kv_name_ch = kv_name[i].c_str();
        ret = GmcKvDropTable(stmt, kv_name_ch);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 功能同strcat, 增加支持格式化入参
int GtStrcat(char *dest, size_t dest_max, const char *src, ...)
{
    errno = 0;
    char *tmpSrc = (char *)malloc(dest_max);

    va_list args;
    va_start(args, src);
    ret = vsnprintf(tmpSrc, dest_max, src, args);
    if (ret <= 0) {
        printf("call vsnprintf failed, ret = %d, errno = %d, %s\n", ret, errno, strerror(errno));
        va_end(args);
        free(tmpSrc);
        return ret;
    }
    va_end(args);

    strncat(dest, tmpSrc, dest_max);
    if (errno != GMERR_OK) {
        printf("call strncat failed, errno = %d, %s", errno, strerror(errno));
        free(tmpSrc);
        return errno;
    }
    free(tmpSrc);
    return GMERR_OK;
}

// 30.节点名长度1字节（包括'\0'）建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_030)
{
    const char *schemaJsonHead = R"(
        [{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail = R"(
        [ {"name" : "G0", "type" : "int32", "nullable" : false} ]
}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
            }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char nodeName[1] = {0};
    int32_t nodeNameLen = sizeof(nodeName);
    memset(nodeName, 't', nodeNameLen - 1);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"record\", \"fields\": ", 16, " ", nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    free(schemaJson);
    schemaJson = NULL;
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "VertexLabel");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 31.字段名长度2字节(包括'\0')建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_031)
{
    const char *schemaJsonHead = R"(
        [{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char propertyName[2] = {0};
    int32_t propertyNameLen = sizeof(propertyName);
    memset(propertyName, 'f', propertyNameLen - 1);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"int32\", \"nullable\":false}", 16, " ", propertyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(stmt, "VertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 32.schema深度1层建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_032)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
}])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);

    // 组装指定深度的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t nodeDepth = 1;
    // 根节点是第一层节点，因此循环中下标从1开始

    ret = GtStrcat(schemaJson,
        jsonLen,
        "\n%*s{\"name\":\"P0\", \"type\":\"int32\", \"nullable\":false}",
        (nodeDepth + 1) * 8,
        " ");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(stmt, "VertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 33.索引名1byte建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_033)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",)";

    const char *schemaJsonTail =
        R"(
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    // 索引名设为最短1字节
    char indexName[2] = {0};
    int32_t indexNameLen = sizeof(indexName);
    memset(indexName, 'a', indexNameLen - 1);
    ret = GtStrcat(schemaJson, jsonLen, "\n%*s\"name\":\"%s\",", 20, " ", indexName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(stmt, "VertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 34.表名字符串长度512建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_034)
{
    const char *vertexLabel = R"(
       [{
    "name" : "labelllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(llllllllllllllllllllllllllllllm",
    "type" : "record",
    "version" : "2.0",
    "fields" : [
       {"name" : "F0", "type" : "int32"},
       {"name" : "F1", "type" : "int32"}
    ],
    "keys" : [
        {
            "fields" : [ "F0" ],
            "index" : {
            "type" : "primary"
            },
            "name" : "pk",
            "node" : "labelllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(llllllllllllllllllllllllllllllm",
            "constraints" : {"unique" : true}
        }]
    }]
    )";
    ret = GmcCreateVertexLabel(stmt, vertexLabel, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,
        "labelllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "llllllllllllllllllllllllllllllm");
    EXPECT_EQ(GMERR_OK, ret);
}

// 35.表名字符串长度513建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_035)
{
    AW_ADD_TRUNCATION_WHITE_LIST(1, "name len exceeds limit");
    const char *vertexLabel = R"(
        [{
    "name" : "labelllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
        R"(llllllllllllllllllllllllllllllml",
    "type" : "record",
    "version" : "2.0",
    "fields" : [
        {"name" : "F0", "type" : "int32"},
        {"name" : "F1", "type" : "int32"}
    ],
    "keys" : [
        {
            "fields" : [ "F0" ],
            "index" : {
                "type" : "primary"
            },
            "name" : "pk",
            "node" : "labelllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm)"
                R"(llllllllllllllllllllllllllllllml",
            "constraints" : {"unique" : true}
        }]
    }]
    )";
    ret = GmcCreateVertexLabel(stmt, vertexLabel, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcDropVertexLabel(stmt,
        "labelllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllmlllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "lllllllllllllllllllllllllllllllllllllllllllllllllmlllllllllllllllllllllllllllllllllllllllllllllllllm"
        "llllllllllllllllllllllllllllllml");
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
}

// 36.字段名长度1字节建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_036)
{
    const char *schemaJsonHead =
        R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},)";

    const char *schemaJsonTail =
        R"(
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    // json实际长度超度超过设定值后将导致未定义错误, 当前1024个节点占用内存 68927 字节
    int32_t jsonLen = 1024 * 70;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);

    // 组装指定属性名称和节点名称的schema
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonHead);
    EXPECT_EQ(GMERR_OK, ret);
    char propertyName[2] = {0};
    int32_t propertyNameLen = sizeof(propertyName);
    memset(propertyName, 'f', propertyNameLen - 1);
    ret = GtStrcat(
        schemaJson, jsonLen, "\n%*s{\"name\":\"%s\", \"type\":\"int32\", \"nullable\":false}", 16, " ", propertyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaJsonTail);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;
    ret = GmcDropVertexLabel(stmt, "VertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
}

// 037.kv表key 长度1建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_037)
{
    ret = GmcKvPrepareStmtByLabelName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t keyLen = 1;
    char key[1];
    key[keyLen - 1] = '\0';
    printf("key:%s", key);

    int32_t val = 10;
    ret = GmcKvSet(stmt, key, strlen(key) + 1, &val, sizeof(val));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t outputVal;
    uint32_t outputValLen = sizeof(outputVal);
    ret = GmcKvGet(stmt, key, strlen(key) + 1, &outputVal, &outputValLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(val, outputVal);
    EXPECT_EQ(sizeof(val), outputValLen);

    ret = GmcKvRemove(stmt, key, strlen(key) + 1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 038.kv表key 长度2建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_038)
{
    ret = GmcKvPrepareStmtByLabelName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t keyLen = 2;
    char key[2];
    key[1] = 'a';
    key[2] = '\0';
    printf("key:%s", key);

    int32_t val = 10;
    ret = GmcKvSet(stmt, key, strlen(key) + 1, &val, sizeof(val));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t outputVal;
    uint32_t outputValLen = sizeof(outputVal);
    ret = GmcKvGet(stmt, key, strlen(key) + 1, &outputVal, &outputValLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(val, outputVal);
    EXPECT_EQ(sizeof(val), outputValLen);

    ret = GmcKvRemove(stmt, key, strlen(key) + 1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 039.索引字段长度为1建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_039)
{
    const char *label_json = R"(
        [
        {
            "type":"record",
            "name":"label",
            "fields":[
                {"name":"A", "type":"fixed", "size":1},
                {"name":"B", "type":"record", "vector":true,
                "fields": [
                            {"name":"b", "type":"fixed", "size":1}]}
            ],
            "keys":[
                {
                    "node":"label",
                    "name":"pk",
                    "fields":["A"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"label",
                    "name":"localhash_key",
                    "fields":["A"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"B",
                    "name":"member_key",
                    "fields":["b"],
                    "index":{"type":"none"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"label",
                    "name":"local_key",
                    "fields":["A"],
                    "index":{"type":"local"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"label",
                    "name":"hashcluster_key",
                    "fields":["A"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":true}
                }
            ]
        }
    ])";
    ret = GmcCreateVertexLabel(stmt, label_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 040.索引字段长度为532建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_040)
{
    const char *label_json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 532},
            {
                "name" : "B",
                "type" : "record",
                "vector" : true,
                "fields" : [ {"name" : "b", "type" : "fixed", "size" : 532} ]
            }
        ],
        "keys" : [
            {
                "node" : "label",
                "name" : "pk",
                "fields" : ["A"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key",
                "fields" : ["A"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "B",
                "name" : "member_key",
                "fields" : ["b"],
                "index" : {"type" : "none"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key",
                "fields" : ["A"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key",
                "fields" : ["A"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, label_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 041.索引字段长度为533建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_041)
{
    const char *label_json = R"([ {
        "type" : "record",
        "name" : "label",
        "fields" : [
            {"name" : "A", "type" : "fixed", "size" : 533},
            {
                "name" : "B",
                "type" : "record",
                "vector" : true,
                "fields" : [ {"name" : "b", "type" : "fixed", "size" : 533} ]
            }
        ],
        "keys" : [
            {
                "node" : "label",
                "name" : "pk",
                "fields" : ["A"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "localhash_key",
                "fields" : ["A"],
                "index" : {"type" : "localhash"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "B",
                "name" : "member_key",
                "fields" : ["b"],
                "index" : {"type" : "none"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "local_key",
                "fields" : ["A"],
                "index" : {"type" : "local"},
                "constraints" : {"unique" : true}
            },
            {
                "node" : "label",
                "name" : "hashcluster_key",
                "fields" : ["A"],
                "index" : {"type" : "hashcluster"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";
    ret = GmcCreateVertexLabel(stmt, label_json, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    if (ret == 0) {
        ret = GmcDropVertexLabel(stmt, "label");
        EXPECT_EQ(GMERR_OK, ret);
    }
}

string p_44_1 = "[{\"type\":\"record\",\"name\":\"label\",\"fields\":[{\"name\":\"A\",\"type\":\"uint8\",\"nullable\":"
                "false},{\"name\":\"B\",\"type\": \"record\", \"vector\": true,\"fields\": [{\"name\":\"b\", "
                "\"type\":\"fixed\",\"size\":1}]},{\"name\":\"C\",\"type\":\"uint32\",\"nullable\":false},{\"name\":"
                "\"D\",\"type\":\"uint32\",\"nullable\":false},{\"name\":\"E\",\"type\":\"uint32\",\"nullable\":false}]"
                ",\"keys\":[{\"node\":\"label\",\"name\":\"";
string p_44_2 = "\",\"fields\":[\"A\"],\"index\":{\"type\":\"primary\"},\"constraints\":{\"unique\":true}},{\"node\":"
                "\"label\",\"name\":\"";
string p_44_3 = "\",\"fields\":[\"A\"],\"index\":{\"type\":\"localhash\"},\"constraints\":{\"unique\":true}},{\"node\":"
                "\"B\",\"name\":\"";
string p_44_4 = "\",\"fields\":[\"b\"],\"index\":{\"type\":\"none\"},\"constraints\":{\"unique\":true}},{\"node\":"
                "\"label\",\"name\":\"";
string p_44_5 = "\",\"fields\":[\"D\"],\"index\":{\"type\":\"local\"},\"constraints\":{\"unique\":true}},{\"node\":"
                "\"label\",\"name\":\"";
string p_44_6 = "\",\"fields\":[\"E\"],\"index\":{\"type\":\"hashcluster\"},\"constraints\":{\"unique\":true}},{"
                "\"node\":\"label\",\"name\":\"";
string p_44_7 = "\",\"fields\":[\"C\",\"D\",\"E\",\"A\"],\"index\":{\"type\":\"lpm4_tree_bitmap\"},\"constraints\":{"
                "\"unique\":true}}]}]";

string adjust_key_word_len_json(int key_name_len)
{
    key_name_len++;
    char pk_name[key_name_len];
    char localhash_key_name[key_name_len];
    char member_key_name[key_name_len];
    char local_key_name[key_name_len];
    char hashcluster_key_name[key_name_len];
    char lpm4_key_name[key_name_len];
    for (int i = 0; i < key_name_len; i++) {
        if (i < key_name_len - 1) {
            pk_name[i] = 'A';
            localhash_key_name[i] = 'B';
            member_key_name[i] = 'C';
            local_key_name[i] = 'D';
            hashcluster_key_name[i] = 'E';
            lpm4_key_name[i] = 'F';
        } else {
            pk_name[i] = '\0';
            localhash_key_name[i] = '\0';
            member_key_name[i] = '\0';
            local_key_name[i] = '\0';
            hashcluster_key_name[i] = '\0';
            lpm4_key_name[i] = '\0';
        }
    }
    string pk_str(pk_name), localhash_str(localhash_key_name), member_key_str(member_key_name),
        local_key_str(local_key_name), hashcluster_key_str(hashcluster_key_name), lpm4_key(lpm4_key_name);
    string json_str = p_44_1 + pk_str + p_44_2 + localhash_str + p_44_3 + member_key_str + p_44_4 + local_key_str +
                      p_44_5 + hashcluster_key_str + p_44_6 + lpm4_key + p_44_7;
    return json_str;
}

// 042.索引名长度为1（不包括\0）建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_042)
{
    string json_str = adjust_key_word_len_json(1);
    const char *json_ch = json_str.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 043.索引名长度为33*129（包括\0）建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_043)
{
    string json_str = adjust_key_word_len_json(33 * 129 - 1);
    const char *json_ch = json_str.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "label");
    EXPECT_EQ(GMERR_OK, ret);
}

// 044.索引名长度为33*129+1（包括\0）建表
TEST_F(SpecificationBaseline_001_007, SpecificationBaseline_001_007_test_044)
{
    AW_ADD_TRUNCATION_WHITE_LIST(1, "name len exceeds limit");
    string json_str = adjust_key_word_len_json(33 * 129);
    const char *json_ch = json_str.c_str();
    ret = GmcCreateVertexLabel(stmt, json_ch, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
}
