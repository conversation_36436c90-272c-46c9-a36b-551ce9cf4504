/*****************************************************************************
 Description  : 资源池规格基线
 Notes        :
 History      :
 Author       :
 Modification :
 Date         :
*****************************************************************************/

extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

int ret = 0;
class BaseLine_ResourcePool_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
};

void BaseLine_ResourcePool_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void BaseLine_ResourcePool_test::TearDownTestCase()
{
    close_epoll_thread();
    testEnvClean();
}

void BaseLine_ResourcePool_test::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_CHECK_LOG_BEGIN();
}

void BaseLine_ResourcePool_test::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 起始索引和申请数量之和要小于资源池的范围上限
static const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"ResourceLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceLable",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
static const char *gLabelConfig = R"({"max_record_num":1000})";
static const char *gLabelName = "ResourceLable";
static const char *gResPoolConfigJson =
    R"({
        "name" : "ResourcePool",
        "pool_id" : 65535,
        "start_id" : 200,
        "capacity" : 2000,
        "order" : 0,
        "alloc_type" : 0
    })";
static const char *gResPoolName = "ResourcePool";
static const uint32_t gResPoolId = 2;
static const uint32_t gResPoolStartId = 200;
static const uint32_t gResPoolCapacity = 2000;
#define MAX_RECORD_NUM 1000

#define LOG_IFERR(ret, log, args...)                                                                     \
    do {                                                                                                 \
        if ((ret) != GMERR_OK) {                                                                         \
            fprintf(stdout, "Error: %s:%d " log ", " #ret " = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        }                                                                                                \
    } while (0)
int GtCheckAffectRows(GmcStmtT *g_stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    EXPECT_EQ(GMERR_OK, ret);
    if (affect != expect) {
        printf("checke effect rows failed, expect is %d, actual is %d", expect, affect);
        return FAILED;
    }
    return GMERR_OK;
}
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_001)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcCreateVertexLabel(g_stmt, gLabelSchemaJson, gLabelConfig);
    ret = GmcCreateResPool(g_stmt, gResPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, gLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(gResPoolId, &tmpResIdx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(gResPoolCapacity, &tmpResIdx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(gResPoolStartId + 1, &tmpResIdx);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入数据
    int32_t F0Value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtCheckAffectRows(g_stmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < MAX_RECORD_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        BREAK_IFERR(ret)
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        BREAK_IFERR(ret);
        ret = GmcExecute(g_stmt);
        BREAK_IFERR(ret);
    }
    ret = GmcUnbindResPoolFromLabel(g_stmt, gLabelName);
    LOG_IFERR(ret, "unbind res pool from label");
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    LOG_IFERR(ret, "destroy res pool");
    ret = GmcDropVertexLabel(g_stmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 每张表允许创建的resource类型字段数量上限[0,4]
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_002)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *labelSchema_5 =
        R"([{
            "type":"record",
            "name":"ResourceLableMultiRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false},
                    {"name":"F3", "type":"resource", "nullable":false},
                    {"name":"F4", "type":"resource", "nullable":false}，
                    {"name":"F5", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableMultiRes",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    const char *labelSchema_4 =
        R"([{
            "type":"record",
            "name":"ResourceLableMultiRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false},
                    {"name":"F3", "type":"resource", "nullable":false},
                    {"name":"F4", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableMultiRes",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = GmcCreateVertexLabel(g_stmt, labelSchema_5, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema_4, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "ResourceLableMultiRes");
    EXPECT_EQ(GMERR_OK, ret);
}

// 资源池名称长度上限[1,64]
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_003)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *resPoolConfigJson =
        R"({
            "name" : "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaab",
            "pool_id" : 65535,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaab");
    EXPECT_EQ(GMERR_OK, ret);
    const char *resPoolConfigJson_error =
        R"({
            "name" : "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaabc",
            "pool_id" : 65535,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson_error);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    // expect = "The length of name should be in the range : (0, 64].";
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// resource字段只能位于根节点
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_004)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableIllegalRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableIllegalRes",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ret = GmcCreateVertexLabel(g_stmt, labelSchema, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "ResourceLableIllegalRes");
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelSchema_error =
        R"([{
            "type":"record",
            "name":"ResourceLableInvalidRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"G1", "type:"record", "fixed_array":true, "size": 1024, "fields": [
                        {"name":"H0", "type":"int32", "nullable":false},
                        {"name":"H1", "type":"resource", "nullable":false}
                    ]}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableInvalidRes",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ret = GmcCreateVertexLabel(g_stmt, labelSchema_error, gLabelConfig);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
}

// resource字段auto_increment不能为true
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_005)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableutoIncrementTrue",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "auto_increment":true}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLable",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(g_stmt, labelSchema, gLabelConfig);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    // expect = "The type of auto increment F1 is not match.";
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// resource字段nullable为false
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_006)
{
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableNullableFalse",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableNullableFalse",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(g_stmt, labelSchema, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "ResourceLableNullableFalse");
    EXPECT_EQ(GMERR_OK, ret);
}

// resource字段nullable为true
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_007)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableNullableTrue1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":true}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableNullableTrue1",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(g_stmt, labelSchema, gLabelConfig);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // expect = "Property F1 should not be null because its type.";
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 不存在资源字段的表不允许绑定资源池
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_008)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableNullableTrue2",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":true}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableNullableTrue2",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(g_stmt, gResPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, "ResourceLableNullableTrue2");
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "ResourceLableNullableTrue2");
    EXPECT_EQ(GMERR_OK, ret);
}

// 销毁资源池之前，需要先解除表的绑定
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_009)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableNullableTrue3",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource"}
                ],
            "keys":
                [
                    {
                        "node":"ResourceLableNullableTrue3",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, gResPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, "ResourceLableNullableTrue3");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    ret = GmcUnbindResPoolFromLabel(g_stmt, "ResourceLableNullableTrue3");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "ResourceLableNullableTrue3");
    EXPECT_EQ(GMERR_OK, ret);
}

// 只能设置为主键索引,不能在其他索引
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_010)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char *test_schema1 = NULL;
    readJanssonFile("schemaFile/resource_hashindex_op240_local.gmjson", &test_schema1);
    ret = GmcCreateVertexLabel(g_stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(test_schema1);
    test_schema1 = NULL;

    readJanssonFile("schemaFile/resource_hashindex_op240_hashcluster.gmjson", &test_schema1);
    ret = GmcCreateVertexLabel(g_stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);

    free(test_schema1);
    test_schema1 = NULL;

    readJanssonFile("schemaFile/resource_hashindex_op240.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "OP_240");

    readJanssonFile("schemaFile/resource_mainkey_op239.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "OP_239");
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema1);
    test_schema1 = NULL;
}

// alloc type是1，起始id必须为偶数
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_011)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *gResPoolConfigJson_error =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 65535,
        "start_id" : 199,
        "capacity" : 2000,
        "order" : 0,
        "alloc_type" : 1
    })";
    ret = GmcCreateResPool(g_stmt, gResPoolConfigJson_error);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
}

// 资源池无法自己绑定自己
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_012)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建、绑定资源池
    ret = GmcCreateVertexLabel(g_stmt, gLabelSchemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, gResPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindExtResPool(g_stmt, gResPoolName, gResPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(g_stmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 资源池无法循环绑定
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_013)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *resPoolName1 = "ResourcePool1";
    const char *resPoolConfigJson1 =
        R"({
        "name" : "ResourcePool1",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName2 = "ResourcePool2";
    const char *resPoolConfigJson2 =
        R"({
        "name" : "ResourcePool2",
        "pool_id" : 10002,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName3 = "ResourcePool3";
    const char *resPoolConfigJson3 =
        R"({
        "name" : "ResourcePool3",
        "pool_id" : 10003,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    ret = GmcCreateResPool(g_stmt, gResPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindExtResPool(g_stmt, gResPoolName, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindExtResPool(g_stmt, resPoolName1, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindExtResPool(g_stmt, resPoolName2, resPoolName3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindExtResPool(g_stmt, resPoolName3, gResPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(g_stmt, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(g_stmt, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, resPoolName3);
    EXPECT_EQ(GMERR_OK, ret);
}

// 资源池pool id等于2^16-1 (uint16_t的最大值: 65535 )
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_014)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65535,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 资源池pool id大于2^16-1 (uint16_t的最大值: 65535 )
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_015)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65536,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
}

//  资源池 start_id + capacity == 2^32-1 (uint32_t的最大值: 4294967295 )
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_016)
{
#if defined ENV_RTOSV2X
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65535,
            "start_id" : 335544320,
            "capacity" : 1,
            "order" : 0,
			"alloc_type" : 0
        })";
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
#else
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65535,
            "start_id" : 4294967294,
            "capacity" : 1,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 资源池 start_id + capacity > 2^32-1 (uint32_t的最大值: 4294967295 )
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_017)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
#if defined ENV_RTOSV2X
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 65535,
        "start_id" : 335544320,
        "capacity" : 2,
        "order" : 0,
		"alloc_type" : 0
    })";

    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
#else
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 65535,
        "start_id" : 4294967294,
        "capacity" : 2,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // 2021-05-13 数据翻转问题补充验证 (DTS202105130DTHC6P1H00)
#if defined ENV_RTOSV2X
    resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 65535,
        "start_id" : 335544320,
        "capacity" : 10,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
#else
    resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 65535,
        "start_id" : 4294967294,
        "capacity" : 10,
        "order" : 0,
        "alloc_type" : 0
        })";
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 对资源字段进行修改update
#define CHECK_AND_BREAK(ret, log, args...)                                                             \
    if ((ret) != GMERR_OK) {                                                                           \
        fprintf(stdout, "Error: %s:%d " log " failed, ret = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                         \
    }
TEST_F(BaseLine_ResourcePool_test, BasicSpecifications_V5_ResourcePool_018)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret;
    const char *gResPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 1000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *gExtendResPoolName = "ExtendResourcePool";
    const char *gExtendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 1001,
        "start_id" : 10,
        "capacity" : 1000,
        "order" : 1,
        "alloc_type" : 0
    })";
    const char *gLabelSchemaJson =
        R"([{
        "type":"record",
        "name":"ResourceLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"resource", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
    const char *gLabelName = "ResourceLabel";
    static const uint64_t gResPoolId = 1000;
    ret = GmcCreateVertexLabel(g_stmt, gLabelSchemaJson, gLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建、绑定资源池
    do {
        ret = GmcCreateResPool(g_stmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(g_stmt, gExtendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(g_stmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 10;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(g_stmt);
        CHECK_AND_BREAK(ret, "insert vertex");
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool eof = false;
        ret = GmcFetch(g_stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            printf("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            printf("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gResPoolId) {
            printf("checek res pool id failed, expect is %llu, actual is %llu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            printf("checek res count failed, expect is %llu, actual is %llu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            printf("checek res start index failed, expect is %d, actual is %llu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 更新资源字段 （不支持, 预期失败）
    do {
        ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int32_t F0Value = 0;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set index key value");
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set index key value");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(2, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        // expect = "Feature is not supported. Resource property can not modify.";
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);

    } while (0);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(g_stmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(g_stmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gExtendResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}
