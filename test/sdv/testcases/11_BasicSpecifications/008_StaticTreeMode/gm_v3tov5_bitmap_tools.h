#ifndef _GM_V5_COMMON_H_
#define _GM_V5_COMMON_H_

#include "gm_v3tov5_bitmap_util.h"

#ifdef __cplusplus
extern "C" {
#endif

// 同步创建VertexLabel, 需注意label_name 必须是schema中的name一致
int func_create_vertex_label_sync(char *file_path, GmcStmtT *stmt, const char *g_configJson, char *label_name)
{
    int32_t ret = 0;
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 异步创建VertexLabel
int func_create_vertex_label_async(
    char *file_path, GmcStmtT *stmt, char *g_configJson, char *label_name, int32_t expect = 0)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect, data.status);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}
void *thread_create_label(void *arg)
{
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    AsyncUserDataT data = {0};
    const char *lastErrorStr = NULL;
    int i = *(int *)arg;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(stmt_async, kv_table_name_sync, g_configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread %d is start \n", i);
    usleep(i);
    if (data.status != GMERR_OK) {
        EXPECT_EQ(GMERR_DUPLICATE_TABLE, data.status);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ(lastErrorStr, "Duplicate table. Unable to create label because label name already exists.");
        TEST_INFO("Failed: lastErrorStr: %s \n", lastErrorStr);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        lastErrorStr = GmcGetLastError();
        TEST_INFO(" Success: lastErrorStr: %s \n", lastErrorStr);
    }
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 获取表中记录数
int TestGmcGetVertexInterCount(GmcStmtT *stmt, char *label_name, int32_t expect)
{
    int ret = 0;
    void *label = NULL;
    bool isFinish = false;
    uint32_t scan_count = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect, scan_count);
    TEST_INFO("Scan_count is %d.\n", scan_count);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}
int TestInsertVertexByJson(
    GmcStmtT *stmt, const char *jsonFile, int expect_count = 1, const char *labelName = label_name03)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json = NULL;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    COMPARE_NE(NULL, data_json);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expect_count);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expect_count);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
    return ret;
}

// localhash scan
int func_T39_all_type_superfield_read(GmcStmtT *stmt, int32_t field_value, int expect_count)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &field_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(2);
    ret = GmcGetSuperfieldById(stmt, 0, sp_1_get, 2);
    EXPECT_EQ(expect_count, *(char *)sp_1_get);
    EXPECT_EQ(expect_count, *(unsigned char *)(sp_1_get + 1));
    free(sp_1_get);
    GmcFreeIndexKey(stmt);
    return ret;
}

int func_insert_ip4forward_field(GmcStmtT *stmt, unsigned int loop, bool need_pk = true)
{
    int ret = 0;
    if (need_pk) {
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    char wr_fixed[34] = {0};
    (void)memset(wr_fixed, 0, sizeof(wr_fixed));
    (void)memset(wr_fixed, 'a', sizeof(wr_fixed));
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

// 同步写vertexLabel ip4forward, bitmap字段不赋值
int test_insert_vertex_ip4forward_without_bitmap(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = func_insert_ip4forward_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}
// 读 ip4forward表, bitmap不赋值的函数
int test_read_ip4forward_by_pk_without_bitmap(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;

            unsigned char rd_mask_len;
            ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, valueSize);
            EXPECT_EQ(wr_uint8, rd_mask_len);
            EXPECT_EQ(0, isNull);

            char rd_svc_ctx_high_prio[34] = {0};
            (void)memset(rd_svc_ctx_high_prio, 0, sizeof(rd_svc_ctx_high_prio));
            char svc_ctx_high_prio[34] = {0};
            (void)memset(svc_ctx_high_prio, 0, sizeof(svc_ctx_high_prio));
            (void)memset(svc_ctx_high_prio, 'a', sizeof(svc_ctx_high_prio));
            ret = GmcGetVertexPropertySizeByName(stmt, "svc_ctx_high_prio", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "svc_ctx_high_prio", &rd_svc_ctx_high_prio, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(34, valueSize);
            for (int i = 0; i < valueSize; i++) {
                if (rd_svc_ctx_high_prio[i] != svc_ctx_high_prio[i])
                    return -1;
            }
            EXPECT_EQ(0, isNull);

            char rd_test_str[50] = {0};
            ret = GmcGetVertexPropertySizeByName(stmt, "test_str", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_str", &rd_test_str, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(string_tmp) + 1, valueSize);
            EXPECT_STREQ(string_tmp, rd_test_str);
            EXPECT_EQ(0, isNull);
            // 未写值的时候, 读取bitmap的值, 都为0000 0000的八位
            uint8_t rd_bits[1];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(8, valueSize);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 1; i++) {
                EXPECT_EQ(0x00, *(rd_bits + i));
            }
        }
    }
    return ret;
}

// 同步写vertexLabel ip4forward
int test_insert_vertex_ip4forward(GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = func_insert_ip4forward_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBitMapT bitMap = {0, 7, NULL};
        uint8_t bits[1];
        memset(bits, 0xff, sizeof(bits));
        bitMap.bits = bits;
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

// 同步写vertexLabel ip4forward
int test_insert_vertex_ip4forward_128(GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = func_insert_ip4forward_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);

        GmcBitMapT bitMap = {0, 127, NULL};
        uint8_t bits[] = {
            0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55};
        bitMap.bits = bits;
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

int test_read_ip4forward_by_pk_128(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;
            uint8_t rd_bits[16];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(128, valueSize);
            EXPECT_EQ(0, isNull);
            uint8_t bits[] = {
                0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55};
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bits[i], *(rd_bits + i));
            }
        }
    }
    return ret;
}

// 同步写vertexLabel ip4forward
int test_insert_vertex_ip4forward_size_start8(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = func_insert_ip4forward_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);

        GmcBitMapT bitMap = {8, 135, NULL};
        uint8_t bits[] = {
            0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66};
        bitMap.bits = bits;
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

int test_read_ip4forward_by_pk_start8(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;

            // bitmap不写值的情况下, 读取的结果为0000 0000
            uint8_t rd_bits[16];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(128, valueSize);
            EXPECT_EQ(0, isNull);
            uint8_t bits[] = {
                0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66};
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bits[i], *(rd_bits + i));
            }
        }
    }
    return ret;
}

// 同步写vertexLabel ip4forward 128 不进行bitmap字段赋值
int test_insert_vertex_ip4forward_128_without_bitmap(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[userDataIdx] = loop;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        ret = func_insert_ip4forward_field(stmt, loop);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        userDataIdx++;
    }
    return ret;
}

int test_update_ip4forward_by_pk_bitmap(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        GmcBitMapT updateBitMap = {0};
        updateBitMap.beginPos = 1;
        updateBitMap.endPos = 7;
        uint8_t bits[] = {0x22};
        updateBitMap.bits = bits;
        ret =
            GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&updateBitMap, sizeof(updateBitMap));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int test_update_ip4forward_read_by_pk_bitmap(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;
            uint8_t rd_bits[16];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(128, valueSize);
            EXPECT_EQ(0, isNull);
            uint8_t bits[] = {
                0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bits[i], *(rd_bits + i));
            }
        }
    }
    return ret;
}

int test_read_update_ip4forward_by_pk_128(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;
            uint8_t rd_bits[16];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(128, valueSize);
            EXPECT_EQ(0, isNull);
            uint8_t bits[] = {
                0x44, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55};
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bits[i], *(rd_bits + i));
            }
        }
    }
    return ret;
}

// 同步写vertexLabel ip4forward_bitmap128
int test_insert_vertex_ip4forward_bitmap_size128(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        if (Opera_type == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
        }
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

// 同步批量写vertexLabel ip4forward_bitmap128
int test_batch_vertex_ip4forward_bitmap_size128_sync(
    GmcConnT *conn, GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
        EXPECT_EQ(GMERR_OK, ret);
        if (Opera_type == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
        }
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(oper_end, totalNum);
    EXPECT_EQ(oper_end, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 同步批量写vertexLabel ip4forward_bitmap32768
int test_batch_vertex_ip4forward_bitmap_size32768_sync(
    GmcConnT *conn, GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name04, Opera_type);
        EXPECT_EQ(GMERR_OK, ret);
        if (Opera_type == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
        }
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(oper_end, totalNum);
    EXPECT_EQ(oper_end, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 同步批量写vertexLabel ip4forward_bitmap32768
int test_vertex_ip4forward_bitmap_size32768_sync(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        if (Opera_type == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
        }
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

// 同步批量更新vertexLabel ip4forward_bitmap128
int test_batch_update_ip4forward_bitmap_size128_sync(
    GmcConnT *conn, GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(oper_end, totalNum);
    EXPECT_EQ(oper_end, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 同步批量更新vertexLabel ip4forward_bitmap32768
int test_batch_update_ip4forward_bitmap_size32768_sync(
    GmcConnT *conn, GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(oper_end, totalNum);
    EXPECT_EQ(oper_end, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 异步批量写vertexLabel ip4forward_bitmap128
int test_batch_vertex_ip4forward_bitmap_size128_async(
    GmcConnT *conn, GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        if (Opera_type == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
        }
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 异步写vertexLabel ip4forward_bitmap128
int test_insert_vertex_ip4forward_bitmap_size128_async(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, Opera_type);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        if (Opera_type == GMC_OPERATION_MERGE) {
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
            EXPECT_EQ(GMERR_OK, ret);
        }
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    return ret;
}

// 异步更新vertexLabel ip4forward_bitmap128
int test_update_vertex_ip4forward_bitmap_size128_unexist_async(GmcStmtT *stmt, int oper_begin, int oper_end,
    const char *keyName, GmcOperationTypeE Opera_type, GmcBitMapT bitMap, int except = 1)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[userDataIdx] = loop;
        ((bool *)(user_data->old_value))[userDataIdx] = loop;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(except, data.affectRows);
    }
    return ret;
}

// 同步更新vertexLabel ip4forward_bitmap128
int test_update_vertex_ip4forward_bitmap_size128_unexist_sync(
    GmcStmtT *stmt, int oper_begin, int oper_end, GmcOperationTypeE Opera_type, GmcBitMapT bitMap)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        bool isNeedPk = true;
        if (Opera_type == GMC_OPERATION_MERGE || Opera_type == GMC_OPERATION_UPDATE) {
            isNeedPk = false;
        }
        ret = func_insert_ip4forward_field(stmt, loop, isNeedPk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int test_read_ip4forward_bitmap_128(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype, GmcBitMapT bitMap)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;
            // bitmap不写值的情况下, 读取的结果为0000 0000
            uint8_t rd_bits[16];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(128, valueSize);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bitMap.bits[i], *(rd_bits + i));
            }
        }
    }
    return ret;
}

int test_update_ip4forward_bitmap_128(GmcStmtT *stmt, const char *keyName, int read_begin, int read_end,
    GmcDataTypeE datatype, GmcBitMapT bitMap, int expect = 0)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "test_bitmap", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(expect, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int test_read_ip4forward_by_pk(GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;

            // bitmap不写值的情况下, 读取的结果为0000 0000
            uint8_t rd_bits[1];
            memset(rd_bits, 0, sizeof(rd_bits));
            ret = GmcGetVertexPropertySizeByName(stmt, "test_bitmap", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_bitmap", rd_bits, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(8, valueSize);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 1; i++) {
                EXPECT_EQ(0xff, *(rd_bits + i));
            }
        }
    }
    return ret;
}

// 同步主键删除ipforward_128表
int test_del_ip4forward_bitmap_128_by_pk(GmcStmtT *stmt, const char *keyName, int read_begin, int read_end)
{
    int ret = 0;
    void *label = NULL;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    return ret;
}

// 异步主键删除ipforward_128表
int test_del_ip4forward_bitmap_128_async_by_pk(GmcStmtT *stmt, const char *keyName, int read_begin, int read_end)
{
    int ret = 0;
    void *label = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = read_begin; loop < read_end; loop++) {
        ((int *)(user_data->old_value))[userDataIdx] = loop;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    return ret;
}

void TestGmcSetNodePropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 65;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 65;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 65;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 65;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
}
// 同步/异步 写数据 OP_T0
int TestInsertVertexOP_T0(GmcStmtT *stmt, bool bool_value, char *f14_value, int64_t start_num, int64_t end_num,
    int array_num, int vector_num, GmcConnTypeE conn_type, GmcBitMapT bitMap, int32_t expect)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};
    GmcNodeT *root, *t1, *t2, *t3;

    if (conn_type) {
        // 插入顶点   数组成员分别传 1 ，0, 2
        for (int64_t i = start_num; i < end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name01, GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_async, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_PK(root, i);
            TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);

            ret = GmcNodeSetPropertyByName(root, (char *)"F17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcSetNodePropertyByName_P(t1, i, bool_value, f14_value);
            ret = GmcNodeSetPropertyByName(t1, (char *)"P17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
            EXPECT_EQ(GMERR_OK, ret);
            // 插入array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                TestGmcSetNodePropertyByName_A(t2, j, bool_value, string_array[j]);
                ret = GmcNodeSetPropertyByName(t2, (char *)"A17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
                EXPECT_EQ(GMERR_OK, ret);
                GmcNodeGetNextElement(t2, &t2);
            }
            // 插入 vector 节点
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeAppendElement(t3, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(t3, j, bool_value, string_array[j]);
                ret = GmcNodeSetPropertyByName(t3, (char *)"V17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
                EXPECT_EQ(GMERR_OK, ret);
            }
            GmcAsyncRequestDoneContextT insertRequestCtx;
            insertRequestCtx.insertCb = insert_vertex_callback;
            insertRequestCtx.userData = &data;
            ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(expect, data.status);
            EXPECT_EQ(1, data.affectRows);
        }
    } else {
        // 插入顶点   数组成员分别传 1 ，0, 2
        for (int64_t i = start_num; i < end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_PK(root, i);
            TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);
            TestGmcSetNodePropertyByName_P(t1, i, bool_value, f14_value);
            // 插入array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                TestGmcSetNodePropertyByName_A(t2, j, bool_value, string_array[j]);
                GmcNodeGetNextElement(t2, &t2);
            }
            // 插入 vector 节点
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeAppendElement(t3, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(t3, j, bool_value, string_array[j]);
                ret = GmcNodeSetPropertyByName(t3, (char *)"V17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
                EXPECT_EQ(GMERR_OK, ret);
            }
            ret = GmcExecute(stmt);
            EXPECT_EQ(expect, ret);
        }
    }
    return 0;
}

// 同步/异步 写数据 OP_T0, 不设置bitmap的值
int TestInsertVertexOP_T0_WithoutBitmap(GmcStmtT *stmt, bool bool_value, char *f14_value, int64_t start_num,
    int64_t end_num, int array_num, int vector_num, GmcConnTypeE conn_type, int32_t expect)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};
    GmcNodeT *root, *t1, *t2, *t3;

    if (conn_type) {
        // 插入顶点   数组成员分别传 1 ，0, 2
        for (int64_t i = start_num; i < end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name01, GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_async, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_PK(root, i);
            TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);
            TestGmcSetNodePropertyByName_P(t1, i, bool_value, f14_value);
            // 插入array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                TestGmcSetNodePropertyByName_A(t2, j, bool_value, string_array[j]);
                GmcNodeGetNextElement(t2, &t2);
            }
            // 插入 vector 节点
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeAppendElement(t3, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(t3, j, bool_value, string_array[j]);
            }
            GmcAsyncRequestDoneContextT insertRequestCtx;
            insertRequestCtx.insertCb = insert_vertex_callback;
            insertRequestCtx.userData = &data;
            ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(expect, data.status);
            EXPECT_EQ(1, data.affectRows);
        }
    } else {
        // 插入顶点   数组成员分别传 1 ，0, 2
        for (int64_t i = start_num; i < end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_PK(root, i);
            TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);
            TestGmcSetNodePropertyByName_P(t1, i, bool_value, f14_value);
            // 插入array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                TestGmcSetNodePropertyByName_A(t2, j, bool_value, string_array[j]);
                GmcNodeGetNextElement(t2, &t2);
            }
            // 插入 vector 节点
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeAppendElement(t3, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(t3, j, bool_value, string_array[j]);
            }
            ret = GmcExecute(stmt);
            EXPECT_EQ(expect, ret);
        }
    }
    return 0;
}

// 同步主键扫描数据和节点数据
int FunScanGmcInsertVertexOP_T0(GmcStmtT *stmt, int64_t field_value, bool bool_value, char *f14_value,
    uint8_t array_num, uint8_t vector_num, GmcBitMapT bitMap)
{
    int32_t ret = 0;
    void *label = NULL;
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"string"};
    bool isNull;
    bool isFinish = false;
    GmcNodeT *root, *t1, *t2, *t3;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &field_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "OP_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t rd_bits_F17[16];
        memset(rd_bits_F17, 0, sizeof(rd_bits_F17));
        ret = GmcNodeGetPropertyByName(root, "F17", rd_bits_F17, 128, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(bitMap.bits[i], *(rd_bits_F17 + i));
        }
        ret = GmcNodeGetPropertyByName(t1, "P17", rd_bits_F17, 128, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(bitMap.bits[i], *(rd_bits_F17 + i));
        }
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(t2, j, &t2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetPropertyByName(t2, "A17", rd_bits_F17, 128, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bitMap.bits[i], *(rd_bits_F17 + i));
            }
        }
        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetPropertyByName(t3, "V17", rd_bits_F17, 128, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(bitMap.bits[i], *(rd_bits_F17 + i));
            }
        }
        GmcFreeIndexKey(stmt);
    }
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 同步更新数据 OP_T0
int FunUpdateGmcVertexOP_T0(GmcStmtT *stmt, int64_t start_num, int64_t end_num, int array_num, int vector_num,
    const char *labelName, const char *keyName, GmcBitMapT bitMap)
{
    int32_t ret = 0;
    char *string_array_update[] = {(char *)"string", (char *)"string", (char *)"string"};
    bool isNull;

    GmcNodeT *root, *t1, *t2, *t3;

    // 更新顶点下面的属性节点信息
    for (int64_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(root, (char *)"F17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t1, (char *)"P17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetPropertyByName(t2, (char *)"A17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
            EXPECT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(t3, (char *)"V17", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
    }
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

// create sub relation func
int func_create_sub_relation(GmcStmtT *stmt, GmcConnT *sn_conn, char *file_path, GmcSubCallbackT callback,
    SnUserDataT *user_data, const char *sub_name)
{
    int ret = 0;
    pthread_mutex_lock(&LockSubChannel);
    char *sub_info = NULL;
    readJanssonFile(file_path, &sub_info);
    COMPARE_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = sub_name;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, sn_conn, callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// void sn_callback_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
// {
//     int index, i, ret = 0;
//     char labelName[MAX_NAME_LENGTH] = {0};
//     unsigned int labelNameLen = MAX_NAME_LENGTH;
//     SnUserDataT *user_data = (SnUserDataT *)userData;
//     char f14_value[256] = {0};
//     bool bool_value;
//     char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"string"};
//     bool isNull;

//     bool eof = false;
//     while (!eof) {
//         ret = GmcFetch(subStmt, &eof);
//         if (ret != GMERR_OK || eof == true) {
//             if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_SEQ_SCAN_EOF) {
//                 printf("[info] GMC_SUB_EVENT_SEQ_SCAN_EOF\n");
//                 user_data->scanEofNum++;
//             }
//             break;
//         }
//         for (i = 0; i < info->labelCount; i++) {
//             memset(labelName, 0, sizeof(labelName));
//             labelNameLen = MAX_NAME_LENGTH;
//             // 获取订阅推送顶点的标签名称
//             ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
//             EXPECT_EQ(GMERR_OK, ret);
//             EXPECT_EQ(strlen(labelName), labelNameLen);
//             // 默认推送new object和old object
//             switch (info->eventType) {
//                 case GMC_SUB_EVENT_INSERT: {
//                     // 读 new object
//                     bool_value = 0;
//                     strcpy(f14_value, "string");
//                     // 在增量订阅中，指定获取旧顶点或新顶点, true表示获取旧顶点, false表示获取新顶点
//                     ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
//                     EXPECT_EQ(GMERR_OK, ret);
//                     index = ((int *)user_data->new_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_INSERT new_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     break;
//                 }
//                 case GMC_SUB_EVENT_DELETE: {
//                     //读old
//                     bool_value = 0;
//                     strcpy(f14_value, "string");
//                     ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
//                     EXPECT_EQ(GMERR_OK, ret);
//                     index = ((int *)user_data->old_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_DELETE old_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     break;
//                 }
//                 case GMC_SUB_EVENT_AGED: {
//                     //读old
//                     bool_value = 0;
//                     strcpy(f14_value, "string");
//                     ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
//                     EXPECT_EQ(GMERR_OK, ret);
//                     index = ((int *)user_data->old_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_AGED old_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     break;
//                 }
//                 case GMC_SUB_EVENT_UPDATE: {
//                     //读new
//                     bool_value = 1;
//                     strcpy(f14_value, "string2");
//                     ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
//                     EXPECT_EQ(GMERR_OK, ret);
//                     index = ((int *)user_data->new_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_UPDATE new_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     //读old
//                     bool_value = 0;
//                     strcpy(f14_value, "string");
//                     ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
//                     EXPECT_EQ(GMERR_OK, ret);
//                     index = ((int *)user_data->old_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_UPDATE old_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     break;
//                 }
//                 case GMC_SUB_EVENT_REPLACE: {
//                     //读new
//                     ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
//                     EXPECT_EQ(GMERR_OK, ret);
//                     index = ((int *)user_data->new_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_REPLACE new_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     // 读old
//                     // bool_value = 0;
//                     // strcpy(f14_value, "string");
//                     // ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // index = ((int *)user_data->old_value)[user_data->subIndex];
//                     // printf("[INFO] %d GMC_SUB_EVENT_UPDATE old_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     break;
//                 }
//                 case GMC_SUB_EVENT_KV_SET: {
//                     //读new
//                     index = ((int *)user_data->new_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_REPLACE new_value is %d\r\n", user_data->subIndex, index);
//                     break;
//                 }
//                 case GMC_SUB_EVENT_SEQ_SCAN: {
//                     //读old
//                     bool_value = 0;
//                     strcpy(f14_value, "string");
//                     index = ((int *)user_data->new_value)[user_data->subIndex];
//                     printf("[INFO] %d GMC_SUB_EVENT_SEQ_SCAN new_value is %d\r\n", user_data->subIndex, index);
//                     // ret = GmcGetNodePropertyByName(subStmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
//                     // EXPECT_EQ(GMERR_OK, ret);
//                     // EXPECT_EQ((unsigned int)0, isNull);
//                     // EXPECT_EQ(index/RECORD_COUNT_100, f7_value);
//                     break;
//                 }
//                 default: {
//                     printf("default: invalid eventType\r\n");
//                     break;
//                 }
//             }
//         }
//         user_data->subIndex++;
//         switch (info->eventType) {
//             case GMC_SUB_EVENT_INSERT: {
//                 user_data->insertNum++;
//                 break;
//             }
//             case GMC_SUB_EVENT_DELETE: {
//                 user_data->deleteNum++;
//                 break;
//             }
//             case GMC_SUB_EVENT_UPDATE: {
//                 user_data->updateNum++;
//                 break;
//             }
//             case GMC_SUB_EVENT_REPLACE: {
//                 user_data->replaceNum++;
//                 break;
//             }
//             case GMC_SUB_EVENT_KV_SET: {
//                 user_data->kvSetNum++;
//                 break;
//             }
//             case GMC_SUB_EVENT_SEQ_SCAN: {
//                 user_data->scanNum++;
//                 break;
//             }
//             case GMC_SUB_EVENT_AGED: {
//                 user_data->agedNum++;
//                 break;
//             }
//         }
//     }
// }

void sn_callback_key_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i, ret = 0;
    bool isNull;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const void *key;
    uint32_t size;
    unsigned int valueSize;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            // if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_SEQ_SCAN_EOF) {
            //     printf("[info] GMC_SUB_EVENT_SEQ_SCAN_EOF\n");
            //     user_data->scanEofNum++;
            // }
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            // 获取订阅推送顶点的标签名称
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 0: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                // 推送key msgType, 获取key 及其 其余字段的值
                case 4: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType,
                                info->eventType, index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint32_t rd_vr_id;
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);
                            EXPECT_EQ('1', rd_nhp_group_flag);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0xff, *(rd_bits + i));
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0xff, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            break;
                        }
                        default: {
                            TEST_INFO("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                default: {
                    TEST_INFO("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            // case GMC_SUB_EVENT_SEQ_SCAN: {
            //     user_data->scanNum++;
            //     break;
            // }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_key_age_del(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i, ret = 0;
    bool isNull;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const void *key;
    uint32_t size;
    unsigned int valueSize;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            // if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_SEQ_SCAN_EOF) {
            //     printf("[info] GMC_SUB_EVENT_SEQ_SCAN_EOF\n");
            //     user_data->scanEofNum++;
            // }
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            TEST_INFO("[sn_callback] msgType:%d eventType:%d\n", info->msgType, info->eventType);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    TEST_INFO("[sn_callback] msgType:%d eventType:%d\n", info->msgType, info->eventType);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    EXPECT_EQ(GMERR_OK, ret);

                    uint8_t rd_nhp_group_flag;
                    ret = GmcGetVertexPropertyByName(
                        subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    EXPECT_EQ((unsigned int)0, isNull);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO(
                        "[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType, info->eventType, index);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    EXPECT_EQ(GMERR_OK, ret);

                    uint32_t rd_vr_id;
                    ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                    EXPECT_EQ(0, isNull);

                    uint8_t rd_nhp_group_flag;
                    ret = GmcGetVertexPropertyByName(
                        subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    EXPECT_EQ((unsigned int)0, isNull);

                    uint8_t rd_bits[16];
                    memset(rd_bits, 0x00, sizeof(rd_bits));
                    ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    EXPECT_EQ(128, valueSize);
                    EXPECT_EQ(0, isNull);
                    for (int i = 0; i < 16; i++) {
                        EXPECT_EQ(0xfe, *(rd_bits + i));
                    }
                    break;
                }  // 此处及以下6处断言各字段的值是不能放开的isnull, 因为数据被删了
                case GMC_SUB_EVENT_UPDATE: {
                    TEST_INFO("[sn_callback] msgType:%d eventType:%d\n", info->msgType, info->eventType);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    EXPECT_EQ(GMERR_OK, ret);

                    uint8_t rd_nhp_group_flag;
                    ret = GmcGetVertexPropertyByName(
                        subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(0, isNull);

                    uint8_t rd_bits[16];
                    memset(rd_bits, 0xff, sizeof(rd_bits));
                    ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                    // EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(128, valueSize);
                    // EXPECT_EQ(0, isNull);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    EXPECT_EQ(GMERR_OK, ret);

                    // uint32_t rd_vr_id;
                    // ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                    // EXPECT_EQ(0, isNull);

                    uint8_t rd_nhp_group_flag;
                    ret = GmcGetVertexPropertyByName(
                        subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(0, isNull);

                    uint8_t rd_bits[16];
                    memset(rd_bits, 0xff, sizeof(rd_bits));
                    ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                    // EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(0, isNull);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 如果需要获取其余字段, 可在del/age这里添加主键字段的获取
                    // uint32_t rd_vr_id;
                    // ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                    // EXPECT_EQ(0, isNull);

                    uint8_t rd_nhp_group_flag;
                    ret = GmcGetVertexPropertyByName(
                        subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(0, isNull);

                    uint8_t rd_bits[16];
                    memset(rd_bits, 0xff, sizeof(rd_bits));
                    ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                    // EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // EXPECT_EQ(0, isNull);
                    break;
                }
                default: {
                    TEST_INFO("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            // case GMC_SUB_EVENT_SEQ_SCAN: {
            //     user_data->scanNum++;
            //     break;
            // }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_key_simple_count(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i, ret = 0;
    bool isNull;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const void *key;
    uint32_t size;
    unsigned int valueSize;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            // if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_SEQ_SCAN_EOF) {
            //     printf("[info] GMC_SUB_EVENT_SEQ_SCAN_EOF\n");
            //     user_data->scanEofNum++;
            // }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            // case GMC_SUB_EVENT_SEQ_SCAN: {
            //     user_data->scanNum++;
            //     break;
            // }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_data_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i, ret = 0;
    bool isNull;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const void *key;
    uint32_t size;
    unsigned int valueSize;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            // if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_SEQ_SCAN_EOF) {
            //     printf("[info] GMC_SUB_EVENT_SEQ_SCAN_EOF\n");
            //     user_data->scanEofNum++;
            // }
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            // 获取订阅推送顶点的标签名称
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            TEST_INFO("[sn_callback] msgType:%d eventType:%d\n", info->msgType, info->eventType);
            switch (info->msgType) {
                case 0: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                // 推送key msgType, 获取key 及其 其余字段的值
                case 4: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType,
                                info->eventType, index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint32_t rd_vr_id;
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);
                            EXPECT_EQ('1', rd_nhp_group_flag);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0xff, *(rd_bits + i));
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0xff, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            break;
                        }
                        default: {
                            TEST_INFO("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_REPLACE: {
                            // 测试推key场景
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType,
                                info->eventType, index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint32_t rd_vr_id;
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);
                            EXPECT_EQ('1', rd_nhp_group_flag);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            // 测试推data场景
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            // 写入新数据, 增加标记位, 老数据读不到isNull 会返回true, 在069号用例中
                            // 覆盖写会推送老数据
                            if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                                EXPECT_EQ(index, rd_vr_id);
                                EXPECT_EQ(0, isNull);
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            // 测试推key场景
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType,
                                info->eventType, index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint32_t rd_vr_id;
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);
                            EXPECT_EQ('1', rd_nhp_group_flag);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            // 测试推data场景
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            // 写入新数据, 增加标记位, 老数据读不到isNull 会返回true, 在069号用例中
                            // 覆盖写会推送老数据
                            if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                                EXPECT_EQ(index, rd_vr_id);
                                EXPECT_EQ(0, isNull);
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_AGED: {
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d\n", info->msgType, info->eventType);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0xff, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);

                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            // for(int i = 0; i < 16; i++) {
                            //     EXPECT_EQ(0x00, *(rd_bits+i));
                            // }
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d\n", info->msgType, info->eventType);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0xff, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);

                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0xaa, *(rd_bits + i));
                            }
                            break;
                        }
                        default: {
                            TEST_INFO("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            // 测试推key场景
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType,
                                info->eventType, index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint32_t rd_vr_id;
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            // 测试推data场景
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0xaa, *(rd_bits + i));
                            }
                            // 写入新数据, 增加标记位, 老数据读不到isNull 会返回true, 在069号用例中
                            // 覆盖写会推送老数据
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);

                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            // for(int i = 0; i < 16; i++) {
                            //     EXPECT_EQ(0x00, *(rd_bits+i));
                            // }
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 测试推key场景
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            TEST_INFO("[sn_callback] msgType:%d eventType:%d index: %d\n", info->msgType,
                                info->eventType, index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint32_t rd_vr_id;
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ((unsigned int)0, isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            // EXPECT_EQ(128, valueSize);
                            // EXPECT_EQ(0, isNull);
                            // for(int i = 0; i < 16; i++) {
                            //     EXPECT_EQ(0, *(rd_bits+i));
                            // }
                            // 测试推data场景
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                            // EXPECT_EQ(index, rd_vr_id);
                            EXPECT_EQ(0, isNull);
                            memset(rd_bits, 0x00, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            // for(int i = 0; i < 16; i++) {
                            //     EXPECT_EQ(0x00, *(rd_bits+i));
                            // }
                            // 写入新数据, 增加标记位, 老数据读不到isNull 会返回true, 在069号用例中
                            // 覆盖写会推送老数据
                            if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertySizeByName(subStmt, "vr_id", &valueSize);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertyByName(subStmt, "vr_id", &rd_vr_id, valueSize, &isNull);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(sizeof(rd_vr_id), valueSize);
                                EXPECT_EQ(index, rd_vr_id);
                                EXPECT_EQ(0, isNull);
                                printf("the replace old data\n");
                                memset(rd_bits, 0x00, sizeof(rd_bits));
                                ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(128, valueSize);
                                EXPECT_EQ(0, isNull);
                                for (int i = 0; i < 16; i++) {
                                    EXPECT_EQ(0x00, *(rd_bits + i));
                                }
                                unsigned char rd_mask_len;
                                ret = GmcGetVertexPropertySizeByName(subStmt, "mask_len", &valueSize);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcGetVertexPropertyByName(subStmt, "mask_len", &rd_mask_len, valueSize, &isNull);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(sizeof(rd_mask_len), valueSize);
                                EXPECT_EQ(49, rd_mask_len);
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_AGED: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            // EXPECT_EQ((unsigned int)0, isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0xff, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0x00, *(rd_bits + i));
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                            EXPECT_EQ(GMERR_OK, ret);

                            uint8_t rd_nhp_group_flag;
                            ret = GmcGetVertexPropertyByName(
                                subStmt, (char *)"nhp_group_flag", &rd_nhp_group_flag, sizeof(uint8_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            // EXPECT_EQ((unsigned int)0, isNull);

                            uint8_t rd_bits[16];
                            memset(rd_bits, 0xff, sizeof(rd_bits));
                            ret = GmcGetVertexPropertySizeByName(subStmt, "test_bitmap", &valueSize);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, "test_bitmap", rd_bits, valueSize, &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(128, valueSize);
                            EXPECT_EQ(0, isNull);
                            for (int i = 0; i < 16; i++) {
                                EXPECT_EQ(0xaa, *(rd_bits + i));
                            }
                            break;
                        }
                        default: {
                            TEST_INFO("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                default: {
                    TEST_INFO("default: invalid msgType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            // case GMC_SUB_EVENT_SEQ_SCAN: {
            //     user_data->scanNum++;
            //     break;
            // }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

// 通过索引获取记录数目
int FuncGmcGetVertexCount(char *label_name, char *keyName)
{
    int ret = 0;
    void *label = NULL;
    bool isFinish = false;
    uint64_t result;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, label_name, keyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    return result;
}

#ifdef __cplusplus
}
#endif
#endif
