#include "gm_basic_008_tools.h"

/*****************************************************************************
 Description  : 静态编译接口V3 to V5基线转化
 Author       : pwx623912
 Create       : 2021.05.14
 Info         : 静态编译接口V3 to V5基线转化,树形表整理
*****************************************************************************/

class BasicSpecifications_008 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int32_t ret = 0;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = 0;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BasicSpecifications_008::SetUp()
{
    int32_t ret = 0;
    (void)pthread_mutex_init(&LockSubChannel, NULL);
    printf("BasicSpecifications_008 Start.\n");
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 默认建顶一个顶点
#ifdef FEATURE_PERSISTENCE
    // 不带member key
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test_op1.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
#else
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test_op.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
#endif


    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}
void BasicSpecifications_008::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    // 断连同步/异步连接
    GmcDropVertexLabel(stmt, label_name01);
    GmcDropVertexLabel(stmt, label_name02);
    GmcDropVertexLabel(stmt, label_name03);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
    printf("BasicSpecifications_008 End.\n");
}

/* ****************************************************************************
 Description  : v3 db_child_count 接口参数校验, 类似 V5 测试接口转换, 获取array节点的个数
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_001)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2;
    bool isFinish = true;

    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    // 主键读数据
    ret = FunReadGmcInsertVertexOP_T0(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01, lalable_name_PK1);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);

        // 获取 ArrayNode 的大小
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T2, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(nodeSize, 3);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : v3 db_child_count 接口参数校验, 类似 V5 测试接口转换, 获取vector节点的个数
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_002)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T3;
    bool isFinish = true;

    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    // 主键读数据
    ret = FunReadGmcInsertVertexOP_T0(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01, lalable_name_PK1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 获取 VectorNode 的大小
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(nodeSize, 3);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : v3 db_child_count 接口参数校验, 类似 V5 测试接口转换, 清空子节点,再读取NULL
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_003)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;

    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T2);  // 清数据, 结构不变
        ASSERT_EQ(GMERR_OK, ret);
        // 获取 ArrayNode 的大小
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T2, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(3, nodeSize);

        TestGmcGetNodePropertyByName_A_NULL(
            T2, 0, 0, (char *)"bbbbbb");  // 读取结点的值都为NULL
                                          // 此处怀疑有问题,字段是不能为NULL, 数据其实都是NULL
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(nodeSize, 3);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : v3 db_child_count 接口参数校验, 类似 V5 测试接口转换
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_004)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(T3, 0);
        ASSERT_EQ(GMERR_OK, ret);
        // 获取 ArrayNode 的大小
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T2, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(3, nodeSize);

        ret = GmcNodeGetElementCount(T3, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(nodeSize, 2);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : v3 db_child_count 接口参数校验, 类似 V5 测试接口转换
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_005)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeRemoveElementByIndex(root, 0);  // 不存在的vector 节点名称
        ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // 获取 ArrayNode 的大小
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T2, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(nodeSize, 3);

        ret = GmcNodeGetElementCount(T3, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(nodeSize, 3);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : tree 节点操作, 追加vector节点,(6/2 现修改了size约束后 size<=schema中的size,
因此写入改小一个,后续再去append)
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_006)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    GmcNodeT *root, *T3;
    // 同步插入数据
    ret = FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, 2, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        // 追加 verctor 节点
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(T3, 3, 0, (char *)"string");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : tree 节点操作, 追加vector节点, 主键读全量数据
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_007)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T3;
    // 同步插入数据
    ret = FunGmcInsertVertexOP_T0(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num - 1, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        // 追加 verctor 节点
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(T3, 2, 0, (char *)"cccccc");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
    printf("Update the vertex is over\n");
    // 主键读数据
    ret = FunReadGmcInsertVertexOP_T0(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01, lalable_name_PK1);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 追加vector节点, 主键读老数据
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_008)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    // 同步插入数据
    ret = FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, 2, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 追加 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(T3, 2, 0, (char *)"cccccc");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 主键读数据
    ret = FunReadGmcInsertVertexOP_T0(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01, lalable_name_PK1);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_009)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(T3, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读 vector 子节点
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        // 读取vector节点, 此处删除了索引为0的vector, 1和2会往前移动, 所以j+1, 如果j = 3,会报相应的错误码
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num - 1; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            if (ret == GMERR_ARRAY_SUBSCRIPT_ERROR)
                break;
            TestGmcGetNodePropertyByName_V(T3, j + 1, 0, string_array[j + 1]);
        }
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点再增加, 主键读全量数据
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_010)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(T3, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(T3, 3, 0, string_array[3]);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读 vector 子节点
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        // 读取vector节点, 此处删除了索引为0的vector, 1和2会往前移动, 所以j+1, 如果j = 3,会报相应的错误码
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            if (ret == GMERR_ARRAY_SUBSCRIPT_ERROR)
                break;
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j + 1, 0, string_array[j + 1]);
        }
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 根据member key操作读
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_011)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取 array 节点 member key
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T2_member_key0;
        ret = GmcNodeAllocKey(T2, "member_key0", &T2_member_key0);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T2_member_key0, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T2, T2_member_key0, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        // 读取 vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
        GmcNodeFreeKey(T2_member_key0);
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 根据member key(string类型)操作读array节点
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_012)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取 array 节点 member key 建立在string节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T2_member_key14;
        ret = GmcNodeAllocKey(T2, "member_key14", &T2_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetKeyValue(T2_member_key14, 0, GMC_DATATYPE_STRING, string_array[j], strlen(string_array[j]));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T2, T2_member_key14, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
            GmcFreeIndexKey(stmt);
        }
        GmcNodeFreeKey(T2_member_key14);
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 根据member key(string类型)操作读array节点和vector节点
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_013)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取 array 节点 member key 建立在string节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T2_member_key14;
        ret = GmcNodeAllocKey(T2, "member_key14", &T2_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetKeyValue(T2_member_key14, 0, GMC_DATATYPE_STRING, string_array[j], strlen(string_array[j]));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T2, T2_member_key14, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
            GmcFreeIndexKey(stmt);
        }
        // 读取 vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
        GmcNodeFreeKey(T2_member_key14);
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 根据member key(string类型)操作读 vector 节点
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_014)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 100;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取 vector 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key14;
        ret = GmcNodeAllocKey(T3, "member_key14", &T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeSetKeyValue(T3_member_key14, 0, GMC_DATATYPE_STRING, string_array[j], strlen(string_array[j]));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T3, T3_member_key14, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
        GmcNodeFreeKey(T3_member_key14);
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 根据member key(int64类型)操作读 vector 节点
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_015)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 100;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取 vector 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key0;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key0);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key0, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T3, T3_member_key0, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
        GmcNodeFreeKey(T3_member_key0);
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 根据member key(string类型)操作读 vector 节点
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_016)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 100;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc", (char *)"dddddd"};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取 vector 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key0;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key0);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key0, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T3, T3_member_key0, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
        GmcNodeFreeKey(T3_member_key0);
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 清空子节点及array节点, 读下面的array/vector节点, 子节点的嵌套节点也会清空
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_017)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 4;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};
    // 同步插入数据
    const int errCodeLen = 1024;
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 清空 T1 节点
        ret = GmcNodeClear(T1);
        ASSERT_EQ(GMERR_OK, ret);
        // 清空之后再次设置也不会生效
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * 1, 0, string_array[j % 3]);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读 vector 子节点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t a0_value;
        ret = GmcNodeGetPropertyByName(T2, (char *)"A0", &a0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isNull);
        // 读取vector节点, 此处删除了索引为0的vector, 1和2会往前移动, 所以j+1, 如果j = 3,会报相应的错误码
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            if (ret == GMERR_ARRAY_SUBSCRIPT_ERROR) {
                AW_FUN_Log(LOG_STEP,"the vector index is %d\n", j);
                break;
            }
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
    }
    GmcFreeIndexKey(stmt);
    // merge更新时clear后再次设置属性无效
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_MERGE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 清空 T1 节点
        ret = GmcNodeClear(T1);
        ASSERT_EQ(GMERR_OK, ret);
        // 清空之后再次设置也不会生效
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * 1, 0, string_array[j % 3]);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读 vector 子节点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t a0_value;
        ret = GmcNodeGetPropertyByName(T2, (char *)"A0", &a0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isNull);
        // 读取vector节点, 此处删除了索引为0的vector, 1和2会往前移动, 所以j+1, 如果j = 3,会报相应的错误码
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            if (ret == GMERR_ARRAY_SUBSCRIPT_ERROR) {
                printf("the vector index is %d\n", j);
                break;
            }
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 清空vector子节点, 读下面的vector节点, vector节点就会删除
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_018)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 4;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcNodeClear(T3);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读 vector 子节点
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
        }
        // 读取vector节点
        // 2023.11.23 此处设计和老业务存在冲突，适配代码回退(DTS2023112216804)
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        unsigned int nodeSize = 0;
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO,"the nodeSize is %d\n", nodeSize);
    }
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : tree 节点操作, 更新子树 array/vector 节点
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_019)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    unsigned int nodeSize;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    char *string_array_update[] = {(char *)"dddddd", (char *)"eeeeee", (char *)"ffffff"};
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        // 更新节点数据
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"peiyan");
        // 更新 array 节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, j + 3, 0, string_array_update[j]);
        }
        // 更新 vector 节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j + 3, 0, string_array_update[j]);
        }
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读 vector 子节点
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"peiyan");
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j + 3, 0, string_array_update[j]);
        }
        // 读取vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(3, nodeSize);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j + 3, 0, string_array_update[j]);
        }
    }
    GmcFreeIndexKey(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_opertor_vertex_label_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *stmt;
    GmcConnT *conn;
    void *label = NULL;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *string_array_update[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    for (int i = 0; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 更新节点数据
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 更新 array 节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, j, 0, string_array_update[j]);
        }
        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j, 0, string_array_update[j]);
        }
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}

void *thread_opertor_read_vertex_label_thread(void *arg)
{
    TEST_INFO("Thread read opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *stmt;
    GmcConnT *conn;
    void *label = NULL;
    int array_num = 3;
    int vector_num = 3;
    unsigned int nodeSize;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *string_array_update[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    for (int i = 0; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j, 0, string_array_update[j]);
        }
        // 读取vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(3, nodeSize);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array_update[j]);
        }
    }
    GmcFreeIndexKey(stmt);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;

    TEST_INFO("Thread read opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : tree 节点操作, 并发隐式事务用例测试
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_020)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2000;
    int array_num = 3;
    int vector_num = 3;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    for (int i = 0; i < CONN_THREAD_100 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_vertex_label_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = CONN_THREAD_100 / 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(vectorTabel[i], &thr_ret[i]);
    }
}

/* ****************************************************************************
 Description  : tree 节点操作, 并发隐式事务用例测试 ,连接数1000
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_021)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2000;
    int array_num = 3;
    int vector_num = 3;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    for (int i = 0; i < CONN_THREAD_100 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_vertex_label_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = CONN_THREAD_100 / 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(vectorTabel[i], &thr_ret[i]);
    }
}

void *thread_transaction_commit_opertor_vertex_label_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *stmt;
    GmcConnT *conn;
    void *label = NULL;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;

    GmcTxConfigT MSTrxConfig;
    //定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    char *string_array_update[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    for (int i = 0; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 更新节点数据
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 插入 array 节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, j, 0, string_array_update[j]);
        }
        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j, 0, string_array_update[j]);
        }
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // MS事务commit
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : tree 节点操作, 并发显示式事务用例测试 ,连接数100
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_022)
{
    int32_t ret = 0;

    // 删除重建：区分轻量化表与非轻量化表(事务&建边)
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":1}";

    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test_op.gmjson", stmt, Label_config, label_name01);

    int start_num = 0;
    int end_num = 2000;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    GmcNodeT *root, *T1, *T2, *T3;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    // pthread_t vectorTabel[CONN_THREAD_100];
    // void *thr_ret[CONN_THREAD_100];
    // int index[CONN_THREAD_100];
    // for (int i = 0; i < CONN_THREAD_100/2; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_transaction_commit_opertor_vertex_label_thread,
    //     &index[i]); ASSERT_EQ(GMERR_OK, ret);
    // }
    // for (int i = CONN_THREAD_100/2; i < CONN_THREAD_100; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_thread, &index[i]);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    // for (int i = 0; i < CONN_THREAD_100; i++) {
    //     pthread_join(vectorTabel[i], &thr_ret[i]);
    // }
}

void *thread_transaction_rollback_opertor_vertex_label_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *stmt;
    GmcConnT *conn;
    void *label = NULL;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;

    GmcTxConfigT MSTrxConfig;
    //定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    char *string_array_update[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    for (int i = 0; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 更新节点数据
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 插入 array 节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, j, 0, string_array_update[j]);
        }
        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j, 0, string_array_update[j]);
        }
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // MS事务commit
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : tree 节点操作, 并发显示式事务rollback用例测试 ,连接数100
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_023)
{
    int32_t ret = 0;

    // 删除重建：区分轻量化表与非轻量化表(事务&建边)
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":1}";

    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test_op.gmjson", stmt, Label_config, label_name01);
    int start_num = 0;
    int end_num = 2000;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    GmcNodeT *root, *T1, *T2, *T3;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    // for (int i = 0; i < CONN_THREAD_100/2; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_transaction_rollback_opertor_vertex_label_thread,
    //     &index[i]); ASSERT_EQ(GMERR_OK, ret);
    // }
    // for (int i = CONN_THREAD_100/2; i < CONN_THREAD_100; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_thread, &index[i]);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    // for (int i = 0; i < CONN_THREAD_100; i++) {
    //     pthread_join(vectorTabel[i], &thr_ret[i]);
    // }
}

/* ****************************************************************************
 Description  : tree 节点操作, 并发显示式事务commit/rollback混合用例测试 ,连接数100
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_024)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2000;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    GmcNodeT *root, *T1, *T2, *T3;
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    // for (int i = 0; i < CONN_THREAD_100/2; i++) {
    //     index[i] = i;
    //     if(i % 2){
    //         ret = pthread_create(&vectorTabel[i], NULL, thread_transaction_rollback_opertor_vertex_label_thread,
    //         &index[i]); ASSERT_EQ(GMERR_OK, ret);
    //     }
    //     else{
    //         ret = pthread_create(&vectorTabel[i], NULL, thread_transaction_commit_opertor_vertex_label_thread,
    //         &index[i]); ASSERT_EQ(GMERR_OK, ret);
    //     }
    // }
    // for (int i = CONN_THREAD_100/2; i < CONN_THREAD_100; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_thread, &index[i]);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    // for (int i = 0; i < CONN_THREAD_100; i++) {
    //     pthread_join(vectorTabel[i], &thr_ret[i]);
    // }
}


void *thread_opertor_vertex_label_ip4forward_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *stmt;
    GmcConnT *conn;
    void *label = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_LOCK_NOT_AVAILABLE);
        if (ret == GMERR_LOCK_NOT_AVAILABLE)
            AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
            break;
    }
    GmcFreeIndexKey(stmt);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}

void *thread_opertor_read_vertex_label_ip4forward_thread(void *arg)
{
    TEST_INFO("Thread read opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *stmt;
    GmcConnT *conn;
    void *label = NULL;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        unsigned int rd_vr_id;
        ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i, rd_vr_id);
        EXPECT_EQ(0, isNull);

        unsigned int rd_dest_ip_addr;
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i, rd_dest_ip_addr);
        EXPECT_EQ(0, isNull);

        unsigned char rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ('1', rd_mask_len);
        EXPECT_EQ(0, isNull);

        unsigned short rd_qos_profile_id;
        ret = GmcGetVertexPropertyByName(stmt, "qos_profile_id", &rd_qos_profile_id, sizeof(uint16_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1111, rd_qos_profile_id);
        EXPECT_EQ(0, isNull);

        unsigned int rd_primary_label;
        ret = GmcGetVertexPropertyByName(stmt, "primary_label", &rd_primary_label, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(i, rd_primary_label);
        EXPECT_EQ(0, isNull);

        char rd_svc_ctx_high_prio[35] = {0};
        ret = GmcGetVertexPropertyByName(stmt, "svc_ctx_high_prio", &rd_svc_ctx_high_prio, 34, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);

        unsigned long long rd_app_obj_id;
        ret = GmcGetVertexPropertyByName(stmt, "app_obj_id", &rd_app_obj_id, sizeof(uint64_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(11111111, rd_app_obj_id);
        EXPECT_EQ(0, isNull);

        char rd_test_str[50] = {0};
        ret = GmcGetVertexPropertyByName(stmt, "test_str", &rd_test_str, 100, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(string_tmp, rd_test_str);
        EXPECT_EQ(0, isNull);
    }
    GmcFreeIndexKey(stmt);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;

    TEST_INFO("Thread read opera thread is end. \n");
    return NULL;
}

/* ****************************************************************************
 Description  : 简单表节点操作, 并发隐式事务用例测试 ,连接数1000   // 此用例需要对齐, gmserver coredump
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_028)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[THRAD_NUM - 2];
    void *thr_ret[THRAD_NUM - 2];
    int index[THRAD_NUM - 2];
    for (int i = 0; i < THRAD_NUM - 2; i++) {
        index[i] = RECORD_COUNT;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_vertex_label_ip4forward_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // for (int i = 126/2; i < THRAD_NUM-2; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_ip4forward_thread, &index[i]);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    for (int i = 0; i < THRAD_NUM - 2; i++) {
        pthread_join(vectorTabel[i], &thr_ret[i]);
    }
}

/* ****************************************************************************
 Description  : 简单表节点操作, 并发隐式事务用例测试 ,连接数2正常
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_029)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 2000;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[CONN_COUNT_1000];
    void *thr_ret[CONN_COUNT_1000];
    int index[CONN_COUNT_1000];
    for (int i = 0; i < 2; i++) {
        index[i] = RECORD_COUNT;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_vertex_label_ip4forward_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // for (int i = CONN_COUNT_1000/2; i < CONN_COUNT_1000; i++) {
    //     index[i] = i;
    //     ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_ip4forward_thread, &index[i]);
    //     ASSERT_EQ(GMERR_OK, ret);
    // }
    for (int i = 0; i < 2; i++) {
        pthread_join(vectorTabel[i], &thr_ret[i]);
    }
}

/* ****************************************************************************
 Description  : 简单表节点操作, 并发隐式事务用例测试 ,连接数4正常
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_030)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = test_insert_vertex_ip4forward(stmt, 0, 2);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t vectorTabel[20];
    void *thr_ret[20];
    int index[20];
    for (int i = 0; i < 10; i++) {
        index[i] = 2;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_vertex_label_ip4forward_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 10; i < 20; i++) {
        index[i] = i;
        ret = pthread_create(&vectorTabel[i], NULL, thread_opertor_read_vertex_label_ip4forward_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 20; i++) {
        pthread_join(vectorTabel[i], &thr_ret[i]);
    }
}

/* ****************************************************************************
 Description  : tree 节点操作, 清空子节点及array节点, 读下面的array/vector节点,
子节点的嵌套节点也会清空,但节点中含有nullable属性为false
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_031)
{
    const int errCodeLen = 1024;
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 4;
    int array_num = 3;
    int vector_num = 3;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 读 vector 子节点
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test_op_nullable.gmjson", stmt, g_configJson, label_name03);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret =
        FunGmcInsertVertexOP_T0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name03);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1);
        ASSERT_EQ(GMERR_OK, ret);
        // 清空之后再次设置也不会生效
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * 1, 0, string_array[j % 3]);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }

        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A_NULL(T2, j, 0, string_array[j]);
        }
        // 读取vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
    }
    GmcFreeIndexKey(stmt);

    //  使用merge更新同样无效
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_MERGE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1);
        ASSERT_EQ(GMERR_OK, ret);
        // 清空之后再次设置也不会生效
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * 1, 0, string_array[j % 3]);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }

        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A_NULL(T2, j, 0, string_array[j]);
        }
        // 读取vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j, 0, string_array[j]);
        }
    }
    GmcFreeIndexKey(stmt);

    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, DTS2021112919401 （非问题） DTS2021112921177（非问题）
db_create_child_node（第三层子节点T2） 在写操作时没有创建上层父节点（根节点下的T1，非数组节点），预期创建失败，实际成功
                v5中会创建一个record， 不需要去创建迭代器， 需要get， 如果get失败会返回错误码
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_032)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 10;
    int array_num = 3;
    int vector_num = 3;
    bool isNull;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 同步插入数据
    // void *label = NULL;
    char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};
    char *f14_value = (char *)"string";
    bool bool_value = 0;

    // 插入顶点   数组成员分别传 1 ，0, 2
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i, bool_value, f14_value);
        // 插入array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j, bool_value, string_array[j]);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        ret = GmcNodeGetChild(NULL, "T3", &T3);
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetChild(T1, "T3", &T3);
        ASSERT_EQ(GMERR_INVALID_NAME, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据 DTS2021120412868
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_033)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 10;
    int vector_num = 10;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
#ifdef FEATURE_PERSISTENCE
// 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test1.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
#else
    // 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
#endif
    // 同步插入数据
    ret = FunGmcInsertVertexOpt0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(T3, 3);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // // 读 vector 子节点
    // char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    // // 读取顶点
    // for (int i = start_num; i < end_num; i++) {
    //     ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    //     ASSERT_EQ(GMERR_OK, ret);
    //     int64_t f0_value = i;
    //     ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcExecute(stmt);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcFetch(stmt, &isFinish);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcGetRootNode(stmt, &root);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcNodeGetChild(root, "T1", &T1);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
    //     TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
    //     // 读取array节点
    //     ret = GmcNodeGetChild(T1, "T2", &T2);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     for (uint32_t j = 0; j < array_num; j++) {
    //         ret = GmcNodeGetElementByIndex(T2, j, &T2);
    //         EXPECT_EQ(GMERR_OK, ret);
    //         TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
    //     }
    //     // 读取vector节点, 此处删除了索引为0的vector, 1和2会往前移动, 所以j+1, 如果j = 3,会报相应的错误码
    //     ret = GmcNodeGetChild(root, "T3", &T3);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     for (uint32_t j = 0; j < vector_num-1; j++) {
    //         ret = GmcNodeGetElementByIndex(T3, j, &T3);
    //         if(ret == GMERR_ARRAY_SUBSCRIPT_ERROR)break;
    //         TestGmcGetNodePropertyByName_V(T3, j+1, 0, string_array[j+1]);
    //     }
    // }
    // GmcFreeIndexKey(stmt);
    // EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据 DTS2021120412868
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_034)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 10;
    int vector_num = 10;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = FunGmcInsertVertexOpt0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key14 = NULL;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 2; j < 5; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key14, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T3, T3_member_key14);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // // 读 vector 子节点
    // char *string_array[] = {(char *)"aaaaaa", (char *)"bbbbbb", (char *)"cccccc"};

    // // 读取顶点
    // for (int i = start_num; i < end_num; i++) {
    //     ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_SCAN);
    //     ASSERT_EQ(GMERR_OK, ret);
    //     int64_t f0_value = i;
    //     ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcExecute(stmt);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcFetch(stmt, &isFinish);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcGetRootNode(stmt, &root);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = GmcNodeGetChild(root, "T1", &T1);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     TestGmcGetNodePropertyByName_R(root, i, 0, (char *)"string");
    //     TestGmcGetNodePropertyByName_p(T1, i, 0, (char *)"string");
    //     // 读取array节点
    //     ret = GmcNodeGetChild(T1, "T2", &T2);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     for (uint32_t j = 0; j < array_num; j++) {
    //         ret = GmcNodeGetElementByIndex(T2, j, &T2);
    //         EXPECT_EQ(GMERR_OK, ret);
    //         TestGmcGetNodePropertyByName_A(T2, j, 0, string_array[j]);
    //     }
    //     // 读取vector节点, 此处删除了索引为0的vector, 1和2会往前移动, 所以j+1, 如果j = 3,会报相应的错误码
    //     ret = GmcNodeGetChild(root, "T3", &T3);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     for (uint32_t j = 0; j < vector_num-1; j++) {
    //         ret = GmcNodeGetElementByIndex(T3, j, &T3);
    //         if(ret == GMERR_ARRAY_SUBSCRIPT_ERROR)break;
    //         TestGmcGetNodePropertyByName_V(T3, j+1, 0, string_array[j+1]);
    //     }
    // }
    // GmcFreeIndexKey(stmt);
    // EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据 DTS2021120413015
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_035)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 10;
    int vector_num = 10;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = FunGmcInsertVertexOpt0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key14 = NULL;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 2; j < 5; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key14, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T3, T3_member_key14);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        // gmsysview record OP_T0 -view_fmt json
    }
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据 DTS2021120415384
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_036)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 10;
    int vector_num = 10;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = FunGmcInsertVertexOpt0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key14 = NULL;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 2; j < 5; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key14, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T3, T3_member_key14);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        // gmsysview record OP_T0 -view_fmt json
    }
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据 DTS2021120415384
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_037)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 10;
    int vector_num = 10;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = FunGmcInsertVertexOpt0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key14 = NULL;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 2; j < 5; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key14, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T3, T3_member_key14);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        // gmsysview record OP_T0 -view_fmt json
    }
}

/* ****************************************************************************
 Description  : tree 节点操作, 删除vector节点, 主键读全量数据 DTS2021120720612
**************************************************************************** */
TEST_F(BasicSpecifications_008, BasicSpecifications_008_038)
{
    int32_t ret = 0;
    int start_num = 0;
    int end_num = 1;
    int array_num = 10;
    int vector_num = 10;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    // 默认建顶一个顶点
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/TreeModelArrayNode_test.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    // 同步插入数据
    ret = FunGmcInsertVertexOpt0(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name01);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name01, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // 删除 verctor 节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T3_member_key14 = NULL;
        ret = GmcNodeAllocKey(T3, "member_key0", &T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 2; j < 5; j++) {
            int64_t indexKeyValue1 = j;
            ret = GmcNodeSetKeyValue(T3_member_key14, 0, GMC_DATATYPE_INT64, &indexKeyValue1, sizeof(int64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByKey(T3, T3_member_key14);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T3_member_key14);
        EXPECT_EQ(GMERR_OK, ret);
        // gmsysview record OP_T0 -view_fmt json
    }
}
