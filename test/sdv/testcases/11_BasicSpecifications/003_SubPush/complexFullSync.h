#ifndef _TREEFULLSYNC_H
#define _TREEFULLSYNC_H

#include "t_datacom_lite.h"
#include "gtest/gtest.h"
#include "syCommon.h"

#ifdef __cplusplus
extern "C" {
#endif

void tree_sn_null_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData);

int g_chanRingLen = 256;
SnUserDataT *g_userData, *g_userDataWrite, *g_userDataUpdate, *g_userDataDelete, *g_userDataReplace;
int *g_newValue, *g_newValueWrite, *g_newValueUpdate, *g_newValueDelete, *g_newValueReplace;
int *g_oldValue, *g_oldValueWrier, *g_oldValueUpdate, *g_oldValueDelete, *g_oldValueReplace;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sub = NULL;
GmcConnT *g_conn = NULL;
GmcConnT *g_conn_sub = NULL;
void *g_vertexLabel = NULL;
char *g_labelName = (char *)"schema_datatype1";
char *g_subConnName = (char *)"subConnName";

int createSubConn(char *file, GmcStmtT *stmt, GmcConnT *conn, char *subName)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = schema;
    ret = GmcSubscribe(stmt, &tmp_schema, conn, tree_sn_null_callback, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    return ret;
}

// 释放malloc申请的内存
void freeMallocSqace(SnUserDataT *userData, int *newValue, int *oldValue)
{
    free(userData);
    free(newValue);
    free(oldValue);
}

void TestGmcGetNodePropertyByName_R(GmcNodeT *stmt, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    bool isNull;
    uint64_t f1_value;

    ret = GmcNodeGetPropertyByName(stmt, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(stmt, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"F15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"F16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F17", &f17_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f17_value);

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F18", &f18_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f18_value);

    uint32_t f19_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F19", &f19_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFF, f19_value);

    uint64_t f20_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"F20", &f20_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f20_value);
}

void TestGmcGetNodePropertyByName_p(GmcNodeT *stmt, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(stmt, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"P15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"P16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P17", &f17_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f17_value);

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P18", &f18_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f18_value);

    uint32_t f19_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P19", &f19_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFF, f19_value);

    uint64_t f20_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"P20", &f20_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f20_value);
}

void TestGmcGetNodePropertyByName_A(GmcNodeT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(stmt, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(stmt, (char *)"A14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"A15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"A16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_V(GmcNodeT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;

    ret = GmcNodeGetPropertyByName(stmt, (char *)"V0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(stmt, (char *)"V14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(stmt, (char *)"V14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"V15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(stmt, (char *)"V16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcDirectFetchVertex(GmcStmtT *stmt, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num, bool isBitmap = true)
{
    int32_t ret = 0;
    bool isFinish = false;
    void *label = NULL;
    GmcNodeT *root, *T1, *T2, *T3;

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            ASSERT_EQ(40005, ret);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish == true || ret != GMERR_OK) {
                    break;
                }
                ret = GmcGetRootNode(stmt, &root);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(root, "T1", &T1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(T1, "T2", &T2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(root, "T3", &T3);
                EXPECT_EQ(GMERR_OK, ret);

                TestGmcGetNodePropertyByName_R(root, i, bool_value, f14_value, isBitmap);
                TestGmcGetNodePropertyByName_p(T1, i, bool_value, f14_value, isBitmap);
                // 读取array节点
                for (uint32_t j = 0; j < array_num; j++) {
                    ret = GmcNodeGetElementByIndex(T2, j, &T2);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcGetNodePropertyByName_A(T2, i, bool_value, f14_value);
                }

                for (uint32_t j = 0; j < vector_num; j++) {
                    ret = GmcNodeGetElementByIndex(T3, j, &T3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcGetNodePropertyByName_V(T3, i, bool_value, f14_value);
                }
            }
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
}

void tree_sn_null_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{}

void TestGmcSetNodePropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(root, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(root, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(root, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_P(GmcNodeT *stmt, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

// tree表写数据
void subTreeWrite(GmcStmtT *stmt, char *labelName, int recordCount, bool bool_value, char *f14_value, int array_num,
    int vector_num, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    GmcNodeT *root, *T1, *T2, *T3;

    for (int i = 0; i < recordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入顶点
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value, isBitmap);

        uint8_t f21_value = (i)&0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_P(T1, i, bool_value, f14_value, isBitmap);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                ASSERT_EQ(GMERR_OK, ret);  // 当array不为1时这里会报错4001，待确认
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
    }
}

void subTreeUpdate(GmcStmtT *stmt, char *labelName, int recordCount, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *primaryKey)
{
    int32_t ret = 0;
    int affectRows;
    GmcNodeT *root, *T1, *T2, *T3;

    for (int i = 0; i < recordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        // 更新顶点
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i, bool_value, f14_value);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                ASSERT_EQ(GMERR_OK, ret);  // 当array不为1时这里会报错4001，待确认
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        //插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i, bool_value, f14_value);
        }
        ret = GmcSetIndexKeyName(stmt, primaryKey);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
    }
}

void subTreeRemove(int recordCount, GmcStmtT *stmt, char *labelName, char *primaryKey)
{
    int ret = 0;
    int affectRows;

    for (uint32_t i = 0; i < recordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, primaryKey);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void complexWriteAsync(GmcStmtT *g_stmt_async, char *labelName, int recordCount, bool bool_value, char *f14_value,
    int array_num, int vector_num, bool isBitmap = true)
{
    int32_t ret = 0;
    GmcNodeT *root, *T1, *T2, *T3;

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < recordCount; i++) {
        // 插入顶点
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入顶点
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value, isBitmap);

        uint8_t f21_value = (i)&0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_P(T1, i, bool_value, f14_value, isBitmap);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                ASSERT_EQ(GMERR_OK, ret);  // 当array不为1时这里会报错4001，待确认
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i, bool_value, f14_value);
        }
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &g_data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&g_data);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcGetStmtAttr(g_stmt_async, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        // ASSERT_EQ(GMERR_OK, ret); // 异步接口未报错获取affectRows失败
        if (g_data.status == GMERR_PRIMARY_KEY_VIOLATION) {
        } else {
            EXPECT_EQ(GMERR_OK, g_data.status);
        }
        /* EXPECT_EQ(1, g_data.affectRows);
        EXPECT_EQ(1, affectRows); // 获取到的结果均异常 */
    }
}

#ifdef __cplusplus
}
#endif
#endif
