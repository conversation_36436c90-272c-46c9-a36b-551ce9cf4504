/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.1 迭代四 ART聚簇容器
 History      :
 Author       : linyuan/l30046718
 Create       : [2024.09.21]
*****************************************************************************/
#include "tools.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
using namespace std;

class ARTClusterTest_001 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcSignalRegisterNotify();
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ARTClusterTest_001::SetUp()
{
    int ret = 0;
    g_conn = NULL;
    g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
void ARTClusterTest_001::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

const char *labelName = "art_cluster";
const char *isRealCluster = "true";
const char *configJson = NULL;

// 001.ART真聚簇容器不支持string
TEST_F(ARTClusterTest_001, DS_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"string", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.ART真聚簇容器不支持bytes
TEST_F(ARTClusterTest_001, DS_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"bytes", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.ART真聚簇容器不支持资源表
TEST_F(ARTClusterTest_001, DS_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.ART真聚簇容器不支持大对象
TEST_F(ARTClusterTest_001, DS_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":15320, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":15320, "nullable":false},
                    {"name":"F4", "type":"fixed", "size":15320, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.ART真聚簇容器不支持无主键表
TEST_F(ARTClusterTest_001, DS_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.ART真聚簇容器不支持二级索引
TEST_F(ARTClusterTest_001, DS_005_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"art_cluster",
                        "name":"TK",
                        "fields":["F0", "F1"],   
                        "index":{"type":"local"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.ART真聚簇容器不支持显式事务，开事务会报错
TEST_F(ARTClusterTest_001, DS_005_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;

    // 起事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交报错
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.ART真聚簇容器支持结构化读写
TEST_F(ARTClusterTest_001, DS_005_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    Test_struct_t obj = (Test_struct_t){0};
    char tableName[] = "art_cluster";
    TestLabelInfoT labelInfo = {tableName, 0, g_testNameSpace};

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    obj.F0 = F0;
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, obj.F1);
    uint8_t *ptr = obj.F2;
    *(uint64_t *)ptr = F0;
    ret = testStructSetVertexWithBuf(g_stmt, &obj, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    Test_struct_t obj2 = (Test_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(g_stmt, &obj2, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    obj2.F0 = F0;
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, obj2.F1);

    ret = testStructSetIndexKeyWithBuf(g_stmt, &obj2, 0, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_FIXED, uuid, sizeof(uuid));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, cnt);
    deSeriFreeDynMem(&deseriCtx, true);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.ART真聚簇容器不支持对账老化
TEST_F(ARTClusterTest_001, DS_005_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBeginCheck(g_stmt, labelName, 0xff);
    GmcEndCheck(g_stmt, labelName, 0xff, false);

    // 老化不会报错，但老不掉数据
    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    AW_FUN_Log(LOG_STEP, "start to delete data.");
    sleep(10);
    AW_FUN_Log(LOG_STEP, "delete completed.");
}

// 019.ART真聚簇容器不支持订阅
TEST_F(ARTClusterTest_001, DS_005_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    const char *g_subName = "subVertexLabel", *g_subConnName = "subConnName";
    char *g_sub_info = NULL;

    SnUserDataT *user_data;
    int g_data_num = 10;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    // 创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema_file/subinfo_001.gmjson", &g_sub_info);
    ASSERT_NE((void *)NULL, g_sub_info);

    // 订阅结构体
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;

    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
    free(g_sub_info);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.ART真聚簇容器不支持批操作
TEST_F(ARTClusterTest_001, DS_005_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    // 起批操作
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchRetT batchRet;
    // 提交失败
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.ART真聚簇容器不支持表升降级
TEST_F(ARTClusterTest_001, DS_005_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    // 表升级
    const char *vertex_update_path = "schema_file/real_cluster_plus.gmjson";
    char *expectValue = (char *) "unsuccessfully";
    ret = TestUpdateVertexLabel(vertex_update_path, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.ART真聚簇容器不支持DLR
TEST_F(ARTClusterTest_001, DS_005_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    char *configJson2 = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不能全表扫
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.ART真聚簇容器与Datalog无交互
TEST_F(ARTClusterTest_001, DS_005_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libName[] = "d_file/testing.so";
    char nsName[] = "testing";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t data1 = 10000;
    int32_t data[3] = {1,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.关闭ART，设置为真聚簇容器无影响
TEST_F(ARTClusterTest_001, DS_005_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":false,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label，报参数无效
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char filter[64] = {0};
    char cmdOutput[64] = {0};
    (void)memset_s(filter, sizeof(filter), 0, sizeof(filter));
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    sprintf_s(filter, sizeof(filter), "LABEL_NAME=\'%s\'", labelName);
    GetViewFieldResultFilter("V\\$STORAGE_ART_INDEX_STAT", "IS_CONTAINER_ART", filter, cmdOutput, 64);
    uint32_t clusterFlag = atoi(cmdOutput);
    EXPECT_EQ(0, clusterFlag);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.ART真聚簇容器，单字段fixed做索引，总长度超过532
TEST_F(ARTClusterTest_001, DS_005_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"fixed", "size":532, "nullable":false},
                    {"name":"F1", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label成功
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.ART真聚簇容器，单字段fixed做索引，总长度等于532
TEST_F(ARTClusterTest_001, DS_005_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"fixed", "size":533, "nullable":false},
                    {"name":"F1", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label失败
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.ART真聚簇容器，多字段做索引，只有一个fixed，总长度超过532
TEST_F(ARTClusterTest_001, DS_005_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":524, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label成功
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.ART真聚簇容器，多字段做索引，只有一个fixed，总长度等于532
TEST_F(ARTClusterTest_001, DS_005_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":525, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label失败
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.ART真聚簇容器，字段满多个fixed，总长度超过532
TEST_F(ARTClusterTest_001, DS_005_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_localJson_2[4096] = {0};
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F1", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F4", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F5", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F6", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F7", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F8", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F9", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F10", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F11", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F12", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F13", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F14", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F15", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F16", "type":"fixed", "size":524, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "F13", "F14", "F15"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson_2, sizeof(g_localJson_2), localJson, isRealCluster);
    // 创建vertex label成功
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson_2, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.ART真聚簇容器，字段满多个fixed，总长度等于532
TEST_F(ARTClusterTest_001, DS_005_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_localJson_2[4096] = {0};
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F1", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F4", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F5", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F6", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F7", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F8", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F9", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F10", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F11", "type":"fixed", "size":30, "nullable":false},
                    {"name":"F12", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F13", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F14", "type":"fixed", "size":43, "nullable":false},
                    {"name":"F15", "type":"fixed", "size":44, "nullable":false},
                    {"name":"F16", "type":"fixed", "size":524, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "F13", "F14", "F15"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson_2, sizeof(g_localJson_2), localJson, isRealCluster);
    // 创建vertex label失败
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson_2, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
}

// 045.ART真聚簇容器指定表名建表
TEST_F(ARTClusterTest_001, DS_005_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label
    Status ret = GmcCreateVertexLabelWithName(g_stmt, g_localJson, configJson, "T1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered("T1");

    ret = GmcDropVertexLabel(g_stmt, "T1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.gmimport导入ART真聚簇容器表
TEST_F(ARTClusterTest_001, DS_005_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[1024];
    char const *g_vertexPath = "./schema_file/real_cluster_default.gmjson";
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    uint32_t ret = executeCommand(command, "successfully");

    JudgeWhetherClustered(labelName);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.ART真聚簇容器单列索引主键在第一个字段
TEST_F(ARTClusterTest_001, DS_005_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.ART真聚簇容器多列索引，主键在123字段
TEST_F(ARTClusterTest_001, DS_005_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);
    // 创建vertex label
    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067.ART真聚簇容器改最大记录数
TEST_F(ARTClusterTest_001, DS_005_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};
    for(int i = 0; i < 20000; i ++)
    {
        ret = TestARTReplace(g_stmt, labelName, dataRec);
        if(ret != GMERR_OK)
        {
            break;
        }
        dataRec[0] ++;
        dataRec[1] ++;
        dataRec[2] ++;
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(20000, dataRec[0]);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(20000, ret);

    const char *configJson2 = R"({"max_record_count":100000})";
    ret = GmcAlterVertexLabelConfig(g_stmt, labelName, configJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069.ART真聚簇容器Truncate清空表数据
TEST_F(ARTClusterTest_001, DS_005_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071.ART真聚簇容器不支持insert
TEST_F(ARTClusterTest_001, DS_005_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073.ART真聚簇容器不支持merge
TEST_F(ARTClusterTest_001, DS_005_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F0 = 0;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);

    // F1 -> uint8_t [38]
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_FIXED, uuid, uuidSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value[230] = {0};
    uint8_t *ptr = value;
    *(uint64_t *)ptr = F0;
    // F2 -> uint8_t [230]
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 075.ART真聚簇容器支持delete
TEST_F(ARTClusterTest_001, DS_005_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTDelete(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 079.ART真聚簇容器支持update
TEST_F(ARTClusterTest_001, DS_005_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataRec[2]++;
    ret = TestARTUpdate(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 083.ART真聚簇容器replace无冲突数据正常执行
TEST_F(ARTClusterTest_001, DS_005_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataRec[0]++;
    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 087.ART真聚簇容器replace有冲突数据报错
TEST_F(ARTClusterTest_001, DS_005_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0}; 

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataRec[2]++;
    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 089.ART真聚簇容器，范围查询查大数据量
TEST_F(ARTClusterTest_001, DS_005_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    for(int i = 0; i < 100; i ++)
    {
        dataRec[0] = i;
        for(int j = 0; j < 100; j ++){
            dataRec[1] = j;
            ret = TestARTReplace(g_stmt, labelName, dataRec);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            dataRec[2] ++;
        }
    }

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 091.ART真聚簇容器，范围匹配，匹配列不在索引里
TEST_F(ARTClusterTest_001, DS_005_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 4;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    StSetPropValueUInt64(&leftVals[1], &lepoch);
    StSetPropValueUInt64(&rightVals[1], &repoch);

    StSetPropValueUInt64(&leftVals[2], &lepoch);
    StSetPropValueUInt64(&rightVals[2], &repoch);

    StSetPropValueUInt64(&leftVals[3], &lepoch);
    StSetPropValueUInt64(&rightVals[3], &repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 093.ART真聚簇容器，范围匹配，结构体不一致
TEST_F(ARTClusterTest_001, DS_005_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);
    items[0].rFlag = GMC_COMPARE_RANGE_OPEN;

    uint64_t lepoch[3] = {0x0000000000000000, 0x0000000000000000, 0x0000000000000000};
    uint64_t repoch[3] = {0xffffffffffffffff, 0xffffffffffffffff, 0xffffffffffffffff};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 095.ART真聚簇容器，在新namespace里，范围匹配，第一个左字段小于右字段，后面的左字段等于右字段，正常查询
TEST_F(ARTClusterTest_001, DS_005_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *nameSpace = (const char *)"USE9";
    const char *userName = (const char *)"BCD9";
    GmcDropNamespace(g_stmt, nameSpace);
    Status ret = GmcCreateNamespace(g_stmt, nameSpace, userName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {0x0000000000000000, 0, 0};
    uint64_t repoch[3] = {0xffffffffffffffff, 0, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 097.ART真聚簇容器，范围匹配，第一个左字段小于右字段，后面的左字段存在大于右字段，正常
TEST_F(ARTClusterTest_001, DS_005_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {0x0000000000000000, 1, 0};
    uint64_t repoch[3] = {0xffffffffffffffff, 0, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 099.ART真聚簇容器，范围匹配，第一个左字段小于右字段，后面的左字段等于右字段，正常查询
TEST_F(ARTClusterTest_001, DS_005_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {0x0000000000000000, 0, 0};
    uint64_t repoch[3] = {0xffffffffffffffff, 0, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 101.ART真聚簇容器，范围匹配，第一个左字段小于右字段，后面的左字段存在小于右字段，正常查询
TEST_F(ARTClusterTest_001, DS_005_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {0x0000000000000000, 0, 0};
    uint64_t repoch[3] = {0xffffffffffffffff, 1, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 103.ART真聚簇容器，范围匹配，第一个左字段等于右字段，第二个左字段小于右字段，正常查询
TEST_F(ARTClusterTest_001, DS_005_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {1, 2, 0};
    uint64_t repoch[3] = {1, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 105.ART真聚簇容器，范围匹配，左字段均等于右字段，报错
TEST_F(ARTClusterTest_001, DS_005_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {0, 0, 0};
    uint64_t repoch[3] = {0, 0, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 107.ART真聚簇容器，范围匹配，第一个左字段大于右字段，报错
TEST_F(ARTClusterTest_001, DS_005_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {1, 0, 0};
    uint64_t repoch[3] = {0, 0, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 109.ART真聚簇容器，范围匹配， 匹配第一个字段，开区间查询
TEST_F(ARTClusterTest_001, DS_005_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {2, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_OPEN,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {1, 0, 0};
    uint64_t repoch[3] = {3, 0, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 111.ART真聚簇容器，范围匹配， 匹配第一个字段，逆序查询
TEST_F(ARTClusterTest_001, DS_005_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {2, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_DESC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {0, 2, 0};
    uint64_t repoch[3] = {4, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 113.ART真聚簇容器，范围匹配， 匹配第二个字段，开区间逆序查询
TEST_F(ARTClusterTest_001, DS_005_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {1, 1, 1, 1};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_OPEN,
        .order = GMC_ORDER_DESC,
        .scanType = GMC_RANGE_SCAN};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {1, 0, 0};
    uint64_t repoch[3] = {1, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 115.ART真聚簇容器，前缀查询查大数据量
TEST_F(ARTClusterTest_001, DS_005_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    for(int i = 0; i < 100; i ++)
    {
        dataRec[0] = i;
        for(int j = 0; j < 100; j ++){
            dataRec[1] = j;
            ret = TestARTReplace(g_stmt, labelName, dataRec);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            dataRec[2] ++;
        }
    }

    ret = SelectAllDatasPrefix(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 117.ART真聚簇容器，前缀匹配列不在索引里，报错
TEST_F(ARTClusterTest_001, DS_005_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 4,
        .matchBytesNum = 8};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 16, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 119.ART真聚簇容器，前缀匹配，结构体不一致
TEST_F(ARTClusterTest_001, DS_005_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 2,
        .matchBytesNum = 8};
    StSetFlagAndOrder(items, &scanPara, arrLen);
    items[0].rFlag = GMC_COMPARE_RANGE_OPEN;

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 0, uuid);
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 0xff, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 123.ART真聚簇容器，指定字段1范围对字段1匹配报错
TEST_F(ARTClusterTest_001, DS_005_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 8};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &lepoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 0, uuid);
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 0xff, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 125.ART真聚簇容器，int8不支持前缀匹配
TEST_F(ARTClusterTest_001, DS_005_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"uint8", "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    uint8_t lF1 = 0;
    uint8_t rF1 = 0;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &lepoch);

    leftVals[1].size = sizeof(uint8_t);
    leftVals[1].type = GMC_DATATYPE_UINT8;
    leftVals[1].value = &lF1;

    rightVals[1].size = sizeof(uint8_t);
    rightVals[1].type = GMC_DATATYPE_UINT8;
    rightVals[1].value = &rF1;

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 127.ART真聚簇容器，int16不支持前缀匹配
TEST_F(ARTClusterTest_001, DS_005_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"uint16", "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    uint16_t lF1 = 0;
    uint16_t rF1 = 0;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &lepoch);

    leftVals[1].size = sizeof(uint16_t);
    leftVals[1].type = GMC_DATATYPE_UINT16;
    leftVals[1].value = &lF1;

    rightVals[1].size = sizeof(uint16_t);
    rightVals[1].type = GMC_DATATYPE_UINT16;
    rightVals[1].value = &rF1;

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 129.ART真聚簇容器，int32不支持前缀匹配 
TEST_F(ARTClusterTest_001, DS_005_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    uint32_t lF1 = 0;
    uint32_t rF1 = 0;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &lepoch);

    leftVals[1].size = sizeof(uint32_t);
    leftVals[1].type = GMC_DATATYPE_UINT32;
    leftVals[1].value = &lF1;

    rightVals[1].size = sizeof(uint32_t);
    rightVals[1].type = GMC_DATATYPE_UINT32;
    rightVals[1].value = &rF1;

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 131.ART真聚簇容器，int64不支持前缀匹配
TEST_F(ARTClusterTest_001, DS_005_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    uint64_t lF1 = 0;
    uint64_t rF1 = 0;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &lepoch);

    leftVals[1].size = sizeof(uint64_t);
    leftVals[1].type = GMC_DATATYPE_UINT64;
    leftVals[1].value = &lF1;

    rightVals[1].size = sizeof(uint64_t);
    rightVals[1].type = GMC_DATATYPE_UINT64;
    rightVals[1].value = &rF1;

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 133.ART真聚簇容器，time不支持前缀匹配
TEST_F(ARTClusterTest_001, DS_005_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"time", "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    uint64_t lF1 = 0;
    uint64_t rF1 = 0;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &lepoch);

    leftVals[1].size = sizeof(uint64_t);
    leftVals[1].type = GMC_DATATYPE_TIME;
    leftVals[1].value = &lF1;

    rightVals[1].size = sizeof(uint64_t);
    rightVals[1].type = GMC_DATATYPE_TIME;
    rightVals[1].value = &rF1;

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 135.ART真聚簇容器，指定字段1范围，前缀匹配字段2，索引顺序为F1，F2，F3
TEST_F(ARTClusterTest_001, DS_005_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {2, 2, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 8};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 16, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    uint8_t val1[230] = {0};
    StSetPropValueString(&leftVals[2], (char *)val1, 230);
    StSetPropValueString(&rightVals[2], (char *)val1, 230);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull;
    uint64_t f0;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0, sizeof(f0), &isNull);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(dataRec[0], f0);

    uint8_t f1[uuidSize];
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", f1, sizeof(f1), &isNull);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(dataRec[1], *(uint64_t *)(f1 + 16));

    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 141.ART真聚簇容器，不指定范围，前缀匹配字段2，索引顺序为F1，F2，F3
TEST_F(ARTClusterTest_001, DS_005_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {2, 2, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 8};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0x0000000000000000;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 16, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    uint8_t val1[230] = {0};
    StSetPropValueString(&leftVals[2], (char *)val1, 230);
    StSetPropValueString(&rightVals[2], (char *)val1, 230);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 143.ART真聚簇容器，指定字段1范围，前缀匹配字段2，匹配长度为0，返回范围匹配查出的所有值
TEST_F(ARTClusterTest_001, DS_005_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {2, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 0};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 16, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    uint8_t val1[230] = {0};
    StSetPropValueString(&leftVals[2], (char *)val1, 230);
    StSetPropValueString(&rightVals[2], (char *)val1, 230);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull;
    uint64_t f0;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0, sizeof(f0), &isNull);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(dataRec[0], f0);

    uint8_t f1[uuidSize];
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", f1, sizeof(f1), &isNull);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(dataRec[1], *(uint64_t *)(f1 + 16));

    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 145.ART真聚簇容器，指定字段1范围，前缀匹配字段2，匹配长度为F2长度，返回范围匹配查出的所有值
TEST_F(ARTClusterTest_001, DS_005_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {2, 2, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 38};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 2, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    uint8_t val1[230] = {0};
    StSetPropValueString(&leftVals[2], (char *)val1, 230);
    StSetPropValueString(&rightVals[2], (char *)val1, 230);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull;
    uint64_t f0;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0, sizeof(f0), &isNull);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(dataRec[0], f0);

    uint8_t f1[uuidSize];
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", f1, sizeof(f1), &isNull);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(dataRec[1], *(uint64_t *)(f1 + 16));

    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 147.ART真聚簇容器，指定字段1范围，前缀匹配字段2，匹配长度超过F2长度，返回范围匹配查出的所有值
TEST_F(ARTClusterTest_001, DS_005_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *localJson = (char *)
        R"([{
            "type":"record",
            "name":"art_cluster",
            "config": {
                "art_container":true,
                "direct_write": true,
                "real_cluster":%s
            },
            "fields":
                [
                    {"name":"F0", "type":"uint64", "nullable":false},
                    {"name":"F1", "type":"fixed", "size":38, "nullable":false},
                    {"name":"F2", "type":"fixed", "size":230, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"art_cluster",
                        "name":"PK",
                        "fields":["F0", "F1", "F2"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    sprintf_s(g_localJson, sizeof(g_localJson), localJson, isRealCluster);

    Status ret = GmcCreateVertexLabel(g_stmt, g_localJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {2, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_PREFIX_SCAN,
        .condIdx = 1,
        .matchBytesNum = 39};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch = 0x0000000000000000;
    uint64_t repoch = 0xffffffffffffffff;
    StSetPropValueUInt64(&leftVals[0], &lepoch);
    StSetPropValueUInt64(&rightVals[0], &repoch);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 16, uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    uint8_t val1[230] = {0};
    StSetPropValueString(&leftVals[2], (char *)val1, 230);
    StSetPropValueString(&rightVals[2], (char *)val1, 230);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 149.ART真聚簇容器，临近匹配，字段不在索引里，报错
TEST_F(ARTClusterTest_001, DS_005_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 2};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    StSetPropValueUInt64(&leftVals[0], &dataRec[0]);
    StSetPropValueUInt64(&rightVals[0], &dataRec[0]);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, dataRec[1], uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);
 
    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 151.ART真聚簇容器，临近匹配，匹配列不在索引里，报错
TEST_F(ARTClusterTest_001, DS_005_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 2,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    StSetPropValueUInt64(&leftVals[0], &dataRec[0]);
    StSetPropValueUInt64(&rightVals[0], &dataRec[0]);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, dataRec[1], uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);
 
    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 153.ART真聚簇容器，临近匹配，匹配字段不一致
TEST_F(ARTClusterTest_001, DS_005_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    StSetPropValueUInt64(&leftVals[0], &dataRec[0]);
    StSetPropValueUInt64(&rightVals[0], &dataRec[0]);

    uint8_t uuid[uuidSize] = {0};
    uint8_t uuid2[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, dataRec[1], uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, 1, uuid2);
    StSetPropValueString(&rightVals[1], (char *)uuid2, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);
 
    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 155.ART真聚簇容器，临近匹配，匹配列不一致
TEST_F(ARTClusterTest_001, DS_005_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t f0 = 1;
    StSetPropValueUInt64(&leftVals[0], &dataRec[0]);
    StSetPropValueUInt64(&rightVals[0], &f0);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, dataRec[1], uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 157.ART真聚簇容器，临近匹配，结构体不一致
TEST_F(ARTClusterTest_001, DS_005_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[3] = {0, 0, 0};

    ret = TestARTReplace(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 2;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 2,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);
    items[0].rFlag = GMC_COMPARE_RANGE_OPEN;

    StSetPropValueUInt64(&leftVals[0], &dataRec[0]);
    StSetPropValueUInt64(&rightVals[0], &dataRec[0]);

    uint8_t uuid[uuidSize] = {0};
    WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, dataRec[1], uuid);
    StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
    StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

    StSetValueRange(items, leftVals, rightVals, arrLen);
 
    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 159.ART真聚簇容器，在新namespace里指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，正序匹配多值
TEST_F(ARTClusterTest_001, DS_005_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *nameSpace = (const char *)"USE9";
    const char *userName = (const char *)"BCD9";
    GmcDropNamespace(g_stmt, nameSpace);
    Status ret = GmcCreateNamespace(g_stmt, nameSpace, userName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {3, 4, 0};
    uint64_t repoch[3] = {3, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec3);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 161.ART真聚簇容器，指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，值在表中不存在
TEST_F(ARTClusterTest_001, DS_005_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {3, 5, 0};
    uint64_t repoch[3] = {3, 5, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 163.ART真聚簇容器，指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，正序不存在>=F1l的值
TEST_F(ARTClusterTest_001, DS_005_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {5, 4, 0};
    uint64_t repoch[3] = {5, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 165.ART真聚簇容器，指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，逆序不存在<=F1l的值
TEST_F(ARTClusterTest_001, DS_005_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label      
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_DESC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {3, 4, 0};
    uint64_t repoch[3] = {3, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 167.ART真聚簇容器，指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，逆序最近的值有两条
TEST_F(ARTClusterTest_001, DS_005_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_DESC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {5, 4, 0};
    uint64_t repoch[3] = {5, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec3);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 169.ART真聚簇容器，指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，正序开区间存在相等值
TEST_F(ARTClusterTest_001, DS_005_169)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_OPEN,
        .rFlag = GMC_COMPARE_RANGE_OPEN,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {4, 4, 0};
    uint64_t repoch[3] = {4, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec3);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 171.ART真聚簇容器，指定字段2值V2，对字段1的值V1进行临近匹配，索引顺序为F1,F2,F3，F2l等于F2r，逆序闭区间存在相等值
TEST_F(ARTClusterTest_001, DS_005_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 5, 0};
    uint64_t dataRec3[4] = {4, 4, 6, 0};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestARTReplace4int(g_stmt, labelName, dataRec3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_DESC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {4, 4, 0};
    uint64_t repoch[3] = {4, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec3);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec2);
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 179.ART真聚簇容器，临近查询查大数据量
TEST_F(ARTClusterTest_001, DS_005_179)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {4, 4, 0, 0};
    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 0; i < 9999; i++)
    {
        ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dataRec2[2] ++;
    }

    uint64_t arrLen = 3;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    GmcRangeItemT items[arrLen];

    StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
        .rFlag = GMC_COMPARE_RANGE_CLOSED,
        .order = GMC_ORDER_ASC,
        .scanType = GMC_NEAR_SCAN,
        .condIdx = 1,
        .matchBytesNum = 30,
        .nearAIdx = 0,
        .nearBIdx = 1};
    StSetFlagAndOrder(items, &scanPara, arrLen);

    uint64_t lepoch[3] = {3, 4, 0};
    uint64_t repoch[3] = {3, 4, 0};

    StSetIndexKey(leftVals, lepoch);
    StSetIndexKey(rightVals, repoch);

    StSetValueRange(items, leftVals, rightVals, arrLen);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetKeyRange(g_stmt, items, arrLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    int res = 0;
    while(!eof)
    {
        ret = GmcFetch(g_stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        res ++;
    }
    AW_MACRO_EXPECT_EQ_INT(10000, res);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 181.ART真聚簇容器，全匹配，匹配列不都在索引里
TEST_F(ARTClusterTest_001, DS_005_181)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &dataRec[0], sizeof(dataRec[0]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT64, &dataRec[1], sizeof(dataRec[2]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_UINT64, &dataRec[3], sizeof(dataRec[3]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 183.ART真聚簇容器，全匹配，在新namespace里，存在1条与之匹配的数据
TEST_F(ARTClusterTest_001, DS_005_183)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *nameSpace = (const char *)"USE9";
    const char *userName = (const char *)"BCD9";
    GmcDropNamespace(g_stmt, nameSpace);
    Status ret = GmcCreateNamespace(g_stmt, nameSpace, userName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &dataRec[0], sizeof(dataRec[0]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT64, &dataRec[1], sizeof(dataRec[2]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_UINT64, &dataRec[2], sizeof(dataRec[2]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);

    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 185.ART真聚簇容器，全匹配，不存在与之匹配的数据
TEST_F(ARTClusterTest_001, DS_005_185)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &dataRec[0], sizeof(dataRec[0]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT64, &dataRec[1], sizeof(dataRec[2]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_UINT64, &dataRec[3], sizeof(dataRec[3]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;

    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 187.ART真聚簇容器，全匹配，存在1条与之匹配的数据
TEST_F(ARTClusterTest_001, DS_005_187)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_4int.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    uint64_t dataRec[4] = {2, 3, 4, 0};
    uint64_t dataRec2[4] = {1, 2, 3, 4};

    ret = TestARTReplace4int(g_stmt, labelName, dataRec);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestARTReplace4int(g_stmt, labelName, dataRec2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &dataRec[0], sizeof(dataRec[0]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT64, &dataRec[1], sizeof(dataRec[2]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_UINT64, &dataRec[2], sizeof(dataRec[2]));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    StCompareResults(g_stmt, dataRec);

    ret = GmcFetch(g_stmt, &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_TRUE(eof);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *ReplaceThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t F0 = val * 1000 + i;
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
        uint8_t uuid[uuidSize] = {0};
        WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);
        GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, uuid, uuidSize);
        uint8_t value[230] = {0};
        uint8_t *ptr = value;
        *(uint64_t *)ptr = F0;
        GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *DeleteThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t F0 = val * 1000;
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        GmcSetIndexKeyName(g_stmt, "PK");
        GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
        uint8_t uuid[uuidSize] = {0};
        WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);
        GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, uuid, uuidSize);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *UpdateThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t F0 = val * 1000 + i;
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        GmcSetIndexKeyName(g_stmt, "PK");
        GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
        uint8_t uuid[uuidSize] = {0};
        WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);
        GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, uuid, uuidSize);
        uint64_t F2 = F0 + i;
        uint8_t value[230] = {0};
        uint8_t *ptr = value;
        *(uint64_t *)ptr = F2;
        GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_FIXED, value, 230);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *SelectThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t F0 = val * 1000;
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        GmcSetIndexKeyName(g_stmt, "PK");
        GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F0, sizeof(uint64_t));
        uint8_t uuid[uuidSize] = {0};
        WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F0, uuid);
        GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, uuid, uuidSize);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *RangeThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t arrLen = 2;
        GmcPropValueT leftVals[arrLen];
        GmcPropValueT rightVals[arrLen];
        GmcRangeItemT items[arrLen];

        uint64_t F0 = val * 1000;
        uint64_t F1 = val * 1000 + 1000;
        StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
            .rFlag = GMC_COMPARE_RANGE_CLOSED,
            .order = GMC_ORDER_ASC,
            .scanType = GMC_RANGE_SCAN};
        StSetFlagAndOrder(items, &scanPara, arrLen);
        StSetPropValueUInt64(&leftVals[0], &F0);
        StSetPropValueUInt64(&rightVals[0], &F1);
        uint8_t uuid[uuidSize] = {0};
        StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
        StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

        StSetValueRange(items, leftVals, rightVals, arrLen);
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        GmcSetIndexKeyName(stmt, "PK");
        GmcSetKeyRange(stmt, items, arrLen);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *PrefixThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t arrLen = 2;
        GmcPropValueT leftVals[arrLen];
        GmcPropValueT rightVals[arrLen];
        GmcRangeItemT items[arrLen];

        uint64_t F0 = val * 1000;
        uint64_t F1 = val * 1000 + 1000;
        StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
            .rFlag = GMC_COMPARE_RANGE_CLOSED,
            .order = GMC_ORDER_ASC,
            .scanType = GMC_PREFIX_SCAN,
            .condIdx = 1,
            .matchBytesNum = 8};
        StSetFlagAndOrder(items, &scanPara, arrLen);
        StSetPropValueUInt64(&leftVals[0], &F0);
        StSetPropValueUInt64(&rightVals[0], &F1);
        uint8_t uuid[uuidSize] = {0};
        StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
        StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

        StSetValueRange(items, leftVals, rightVals, arrLen);
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        GmcSetIndexKeyName(stmt, "PK");
        GmcSetKeyRange(stmt, items, arrLen);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *NearThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    Status ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t val = *(uint64_t *)args;
    for(int i = 0; i < 1000; i ++)
    {
        uint64_t arrLen = 2;
        GmcPropValueT leftVals[arrLen];
        GmcPropValueT rightVals[arrLen];
        GmcRangeItemT items[arrLen];

        uint64_t F1 = val * 1000;
        uint64_t F0 = val * 1000 + 1000;
        StCondScanParaT scanPara = {.lFlag = GMC_COMPARE_RANGE_CLOSED,
            .rFlag = GMC_COMPARE_RANGE_CLOSED,
            .order = GMC_ORDER_DESC,
            .scanType = GMC_NEAR_SCAN,
            .condIdx = 1,
            .matchBytesNum = 30,
            .nearAIdx = 0,
            .nearBIdx = 1};
        StSetFlagAndOrder(items, &scanPara, arrLen);
        StSetPropValueUInt64(&leftVals[0], &F0);
        StSetPropValueUInt64(&rightVals[0], &F0);
        uint8_t uuid[uuidSize] = {0};
        WbcCaheTestBuildUuid(defaultFsId, defaultDtreeId, defaultPfId, F1, uuid);
        StSetPropValueString(&leftVals[1], (char *)uuid, uuidSize);
        StSetPropValueString(&rightVals[1], (char *)uuid, uuidSize);

        StSetValueRange(items, leftVals, rightVals, arrLen);
        GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        GmcSetIndexKeyName(stmt, "PK");
        GmcSetKeyRange(stmt, items, arrLen);
        GmcExecute(stmt);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 189.ART真聚簇容器，大并发写
TEST_F(ARTClusterTest_001, DS_005_189)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, ReplaceThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(11000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 191.ART真聚簇容器，大并发写并发改
TEST_F(ARTClusterTest_001, DS_005_191)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, UpdateThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 193.ART真聚簇容器，大并发写并发删
TEST_F(ARTClusterTest_001, DS_005_193)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, DeleteThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 195.ART真聚簇容器，大并发写并发全匹配
TEST_F(ARTClusterTest_001, DS_005_195)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, SelectThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 197.ART真聚簇容器，大并发写并发范围读
TEST_F(ARTClusterTest_001, DS_005_197)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, RangeThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 199.ART真聚簇容器，大并发写并发前缀读
TEST_F(ARTClusterTest_001, DS_005_199)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, PrefixThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 201.ART真聚簇容器，大并发写并发临近读
TEST_F(ARTClusterTest_001, DS_005_201)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, ReplaceThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, NearThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 203.ART真聚簇容器，大并发改并发范围读
TEST_F(ARTClusterTest_001, DS_005_203)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
        ReplaceThread((void *)&nums[i]);
    }
    
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, UpdateThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, RangeThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 205.ART真聚簇容器，大并发改并发前缀读
TEST_F(ARTClusterTest_001, DS_005_205)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 10] = i + 1;
        ReplaceThread((void *)&nums[i]);
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, UpdateThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, PrefixThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 207.ART真聚簇容器，大并发改并发临近读
TEST_F(ARTClusterTest_001, DS_005_207)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建vertex label
    char *labelJson = NULL;
    readJanssonFile("./schema_file/real_cluster_default.gmjson", &labelJson);
    Status ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    JudgeWhetherClustered(labelName);

    pthread_t tid[20];
    uint64_t nums[20];
    for(int i = 0; i < 10; i ++)
    {
        nums[i] = i;
        nums[i + 1] = i + 1;
        ReplaceThread((void *)&nums[i]);
    }
    for(int i = 0; i < 10; i++)
    {
        pthread_create(&tid[i], NULL, UpdateThread, (void *)&nums[i]);
        pthread_create(&tid[i+10], NULL, NearThread, (void *)&nums[i+10]);
    }

    for(int i = 0; i < 20; i++)
    {
        pthread_join(tid[i], NULL);
    }

    ret = SelectAllDatas(g_stmt, labelName);
    EXPECT_GE(10000, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

