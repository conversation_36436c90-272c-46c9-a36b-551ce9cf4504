/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :长稳问题单转换SDV头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/07/07
**************************************************************************** */

#ifndef QUESTION_VARIATION_SDV_H
#define QUESTION_VARIATION_SDV_H

#include "t_datacom_lite.h"

char *g_labelName7 = (char *)"ip4forward";
char *g_labelName8 = (char *)"nhp_group";
char *g_labelName9 = (char *)"nhp_group_node";
char *g_labelName10 = (char *)"nhp";

char g_lable7PkName[] = "primary_key";

char g_testLabelconfig[] = "{\"defragmentation\":false,\"isFastReadUncommitted\":0}";
const char *g_labelNameIp4foward8k = "ip4forward_8k";
const char *g_ip4foward8kPkName = "primary_key";
const char *g_ip4foward8kLocalKey = "local_key";
char g_if32kName[64] = "if_32k";
char g_if32kPk[64] = "if_pk";

const char *g_resPoolName = "resource_pool_test";
const char *g_resPool =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 1,
        "start_id" : 0,
        "capacity" : 65535,
        "order" : 0,
        "alloc_type" : 0
    })";

const char *g_v2MemberKeyName = (const char *)"member_keyV2_unique";
const char *g_v3MemberKeyName = (const char *)"member_keyV3";
const char *g_v5MemberKeyName = (const char *)"member_keyV5_unique";
char *g_resLabel = (char *)"multilayer_test";
char *g_resPk = (char *)"primary_key";
char *g_resLpm4 = (char *)"lpm4_index";
const char *g_namespaceUserName = (const char *)"abc";
const char *g_nameSpace = (const char *)"user001";

char g_ifStabilityName[128] = "if";
char g_ifStabilityPkName[128] = "if_pk";
char g_ifLocalhashKey[128] = "ifname_pk";
const char *g_labelNameIp4fowardMiniLpm = "ip4foward_mini_lpm";
const char *g_ip4fowardMiniLpmPkName = "if_vlan_pk";
const char *g_ip4fowardMiniLpmLocalhashName = "k_vlan_id";
char g_customerName[128] = "customer";
char g_customerPk[128] = "pk";
char g_subip4fowardName[128] = "sub_ip4forward";
char g_subIp4fowardPk[128] = "primary_key";

#if defined ENV_RTOSV2X              // IoT设备
#define RES_WRITE_COUNT 100            // 资源表写的数据量
#define OP_NUM  50                     // 表数据单次操作量
#define IP4FORWARD_8K_NUM 2000
#define BATCH_MAX_COUNT 30             // 一次批量最大点个数
#define RES_WRITE_COUNT 100            // 资源表写的数据量
#define RES_TRANS_WRITE_COUNT 500
#define IFGENERAL_32K_NUM  10
#define GRAPH_WRITE_NUM 2000           // 表预置数据量
#else
#define RES_WRITE_COUNT 1000           // 资源表写的数据量
#define OP_NUM  2000                   // 表数据单次操作量
#define IP4FORWARD_8K_NUM 2000
#define BATCH_MAX_COUNT 300
#define RES_WRITE_COUNT 1000           // 资源表写的数据量
#define RES_TRANS_WRITE_COUNT 5000
#define GRAPH_WRITE_NUM 20000          // 表预置数据量
#define IFGENERAL_32K_NUM  100         // GRAPH_WRITE_NUM/200
#endif

#define ST 1
#define THREADS_NUM_MAX      100       // 1个场景最大线程数，暂定100
#define FIB_THREAD1_DATA_START 0
#define FIB_THREAD1_DATA_END 10
#define FIB_THREAD2_DATA_END 20
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define HASH_CONFILICT_SIZE (15625)
#define GMERR_LOCK_NOT_AVAILABLE_CIRCLE_TIME 100
#define IP4FORWARD_BITMAP_LEN 8
#define IP4FORWARD_8K_BITMAP_LEN 64
#define IP4FORWARD_8K_F1_LEN 5
#define IP4FORWARD_8K_F7_LEN 5
#define IP4FORWARD_8K_F15_LEN 4038
#define IP4FORWARD_8K_F16_LEN 4038
#define MAX_NAME_LENGTH 128
#define RES_FIXED_PROPERTY_SIZE 7
#define RES_BYTES_PROPERTY_SIZE 7
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define IF_TYPE_FIXED_LEN 6
#define MINI_LABEL_PREPARE_DATA 2000
#define CUSTOMER_FIXED_LEN 11
#define FIXED_F2_SIZE_32K 6
#define FIXED_F6_SIZE_32K 2048
#define FIXED_F7_SIZE_32K 10240
#define BYTES_F9_SIZE_32K 10240

/**-------------------DTS2022050518371-----------------------**/
void CreateFibLabel()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *testSchema = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName7);
    ret = GmcDropVertexLabel(stmt, g_labelName8);
    ret = GmcDropVertexLabel(stmt, g_labelName9);
    ret = GmcDropVertexLabel(stmt, g_labelName10);
    // 创建7#
    readJanssonFile("../../../schema_file/r21_ndb/gmjson/fib/ip4forward.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(stmt, testSchema, g_testLabelconfig);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    free(testSchema);
    // 创建#8
    readJanssonFile("../../../schema_file/r21_ndb/gmjson/fib/nhp_group.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(stmt, testSchema, g_testLabelconfig);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    free(testSchema);
    // 创建#9
    readJanssonFile("../../../schema_file/r21_ndb/gmjson/fib/nhp_group_node.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(stmt, testSchema, g_testLabelconfig);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    free(testSchema);
    // 创建#10
    readJanssonFile("../../../schema_file/r21_ndb/gmjson/fib/nhp.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(stmt, testSchema, g_testLabelconfig);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    EXPECT_EQ(GMERR_OK, ret);
    free(testSchema);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestWholdLabelScan(GmcStmtT *stmt, char *labelName)
{
    int ret = 0;
    bool isFinish = true;
    uint32_t fetchNum = 0;
    // 全表扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        while (1) {
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            fetchNum++;
        }
    } else if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        testGmcGetLastError(NULL);
        printf("TestWholdLabelScan:lalbelName: %s\n", labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

int TestExcuteTime(GmcStmtT *stmt, int32_t returnV)
{
    int32_t circleTime = 0;
    int32_t ret = returnV;
    while (ret == GMERR_LOCK_NOT_AVAILABLE) {
        circleTime++;
        usleep(10);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            break;
        }
        if (circleTime == GMERR_LOCK_NOT_AVAILABLE_CIRCLE_TIME) {
            break;
        }
    }
    return ret;
}

int TestGetAffactRows(GmcStmtT *stmt, int32_t expect_value)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect_value, affectRows);
    return expect_value == affectRows ? GMERR_OK : 1;
}

// 线程里设置数据量
void TestSetDataVolume(void *args, uint32_t *DataVolume, uint32_t defaultValue)
{
    if (args) {
        (*DataVolume) = *(uint32_t *)args;
    } else {
        (*DataVolume) = defaultValue;
    }
}

void TestSetVertexProperty7(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t valueUint8 = id;
    uint16_t valueUint16 = id;
    uint32_t valueUint32 = id;
    uint64_t valueUint64 = id;
    uint8_t fixed[17] = {0};
    for (i = 0; i < 16; i++) {
        fixed[i] = '0' + id;
    }
    uint32_t vrid = 0;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t vrfIndex = 0;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (id <= MAX_MASK_LEN_16) {
        destIpAddr = ((id + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (id > MAX_MASK_LEN_16 && id <= MAX_MASK_LEN_24) {
        destIpAddr = ((id + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((id + 2));
        maskLen = ((32) & 0xff);
    }

    // 主键字段
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &valueUint16, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, fixed, 16);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, fixed, 16);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &valueUint16, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &valueUint16, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "time_stamp_create", GMC_DATATYPE_TIME, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "time_stamp_smooth", GMC_DATATYPE_TIME, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

// 8表
void TestSetVertexProperty8(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t valueUint8 = id;
    uint16_t valueUint16 = id;
    uint32_t valueUint32 = id;
    uint64_t valueUint64 = id;
    uint8_t fixed[16] = {0};
    for (i = 0; i < 16; i++) {
        fixed[i] = id;
    }

    // 主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_number", GMC_DATATYPE_UINT16, &valueUint16, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ref_count", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "new_vrf", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_type_high_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_type_normal_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attr_flag", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "time_stamp_create", GMC_DATATYPE_TIME, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "time_stamp_smooth", GMC_DATATYPE_TIME, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetVertexProperty9(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t valueUint8 = id;
    uint16_t valueUint16 = id;
    uint32_t valueUint32 = id;
    uint64_t valueUint64 = id;
    int32_t fixed[37] = {0};
    for (i = 0; i < 36; i++) {
        fixed[i] = '0' + id;
    }

    // 主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 主键字段
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "group_smooth_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetVertexProperty10(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t valueUint8 = id;
    uint16_t valueUint16 = id;
    uint32_t valueUint32 = id;
    uint64_t valueUint64 = id;
    int32_t fixed[37] = {0};
    for (i = 0; i < 36; i++) {
        fixed[i] = '0' + id;
    }

    // 主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "origin_nhp", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_flag", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_num", GMC_DATATYPE_UINT8, &valueUint8, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ref_cnt", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "iid_flags", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &valueUint64, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestInsert7(GmcStmtT *stmt, int32_t startN, int32_t endN)
{
    int ret = 0;
    uint32_t affectRows = 0;
    // 写#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName7, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = startN; i < endN; i++) {
        TestSetVertexProperty7(stmt, i);
        ret = GmcExecute(stmt);
        while (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = TestExcuteTime(stmt, ret);
        }
        if (ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
            continue;
        }
    }
}

void TestDelete7(GmcStmtT *stmt, int32_t startN, int32_t endN)
{
    int ret = 0;
    uint32_t affectRows = 0;
    int vir = 0;
    // 删除#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName7, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = startN; i < endN; i++) {
        uint32_t destIpAddr = 0;
        uint8_t maskLen = 0;
        if (i <= MAX_MASK_LEN_16) {
            destIpAddr = ((i + 2) << 16);
            maskLen = ((16) & 0xff);
        } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
            destIpAddr = ((i + 2) << 8);
            maskLen = ((24) & 0xff);
        } else {
            destIpAddr = ((i + 2));
            maskLen = ((32) & 0xff);
        }
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vir, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vir, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, g_lable7PkName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_OUT_OF_MEMORY &&
            ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
            continue;
        }
    }
}

void TestInsert8(GmcStmtT *stmt, int32_t startN, int32_t endN)
{
    int ret = 0;
    uint32_t affectRows = 0;
    // 写#8的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName8, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = startN; i < endN; i++) {
        TestSetVertexProperty8(stmt, i);
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_PRIMARY_KEY_VIOLATION &&
            ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
            continue;
        }
        if (ret == GMERR_OK) {
            ret = TestGetAffactRows(stmt, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

int TestInsert9(GmcStmtT *stmt, int32_t startN, int32_t endN)
{
    int ret = 0;
    uint32_t affectRows = 0;
    // 写#9的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = startN; i < endN; i++) {
        TestSetVertexProperty9(stmt, i);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_TRANSACTION_ROLLBACK ||
            ret == GMERR_PRIMARY_KEY_VIOLATION) {
            return ret;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = TestGetAffactRows(stmt, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return GMERR_OK;
}

void TestInsert10(GmcStmtT *stmt, int32_t startN, int32_t endN)
{
    int ret = 0;
    uint32_t affectRows = 0;
    // 写#10的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName10, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = startN; i < endN; i++) {
        TestSetVertexProperty10(stmt, i);
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_PRIMARY_KEY_VIOLATION &&
            ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
            continue;
        }
        if (ret == GMERR_OK) {
            ret = TestGetAffactRows(stmt, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

void *TestFibThread1(void *args)
{
    int ret = 0;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    uint32_t countN = 0;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_DEFAULT_TRX;
    config.readOnly = false;
    TestSetDataVolume(args, &countN, FIB_THREAD1_DATA_END);
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 写10/9/8
    for (int i = 0; i < 5; i++) {
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        TestInsert7(syncStmt, FIB_THREAD1_DATA_START, countN);

        TestDelete7(syncStmt, FIB_THREAD1_DATA_START, countN);

        TestInsert8(syncStmt, FIB_THREAD1_DATA_START, countN);

        TestInsert9(syncStmt, FIB_THREAD1_DATA_START, countN);
       
        TestInsert10(syncStmt, FIB_THREAD1_DATA_START, countN);
        ret = GmcTransRollBack(syncConn);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(ST);
    }
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *TestFibThread2(void *args)
{
    int ret = 0;
    int startT = 0;
    int endT = 100;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_DEFAULT_TRX;
    config.readOnly = false;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    uint32_t countN = 0;
    TestSetDataVolume(args, &countN, FIB_THREAD1_DATA_END);
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        // 写9号表
        ret = TestInsert9(syncStmt, countN, countN * 2);
        if (ret != GMERR_OK) {
            ret = GmcTransRollBack(syncConn);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            // 全表扫描
            TestWholdLabelScan(syncStmt, g_labelName9);
            // 回滚
            ret = GmcTransRollBack(syncConn);
            EXPECT_EQ(GMERR_OK, ret);
        }
        sleep(ST);
    }
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/**-------------------DTS2022042811478-----------------------**/
// 创表
int CreateLabelIp4foward8k()
{
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    char *testSchema = NULL;
    readJanssonFile("schemaFile/ip4foward_8k.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = testGmcConnect(&connSync, &stmtSync);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmtSync, g_labelNameIp4foward8k);
    ret = GmcCreateVertexLabel(stmtSync, testSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create label ip4foward_8k success \n");
    } else {
        testGmcGetLastError(NULL);
    }
    free(testSchema);
    ret = testGmcDisconnect(connSync, stmtSync);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

void TestDropVertexLabel(const char *vertexLabelName)
{
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmtSync, g_subip4fowardName);
    GmcDropVertexLabel(stmtSync, "tools_test");
    ret = GmcDropVertexLabel(stmtSync, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connSync, stmtSync);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexPropertyByName8k(GmcStmtT *stmt, int i, void *fixedValue, void *fixedValue15)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_FIXED, fixedValue, IP4FORWARD_8K_F1_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f2Value = i % 5000;
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3Value = (uint32_t)i;
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f4Value = (uint32_t)i;
    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f5Value = i % 256;
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT8, &f5Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap = {0, 63, NULL};
    uint8_t f6_bits[IP4FORWARD_8K_BITMAP_LEN / 8] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07};
    bitMap.bits = f6_bits;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_BITMAP, &bitMap, sizeof(bitMap));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_FIXED, fixedValue, IP4FORWARD_8K_F7_LEN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_UINT32, &f4Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_FIXED, fixedValue15, IP4FORWARD_8K_F15_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_FIXED, fixedValue15, IP4FORWARD_8K_F16_LEN);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetPkIndex8k(GmcStmtT *stmt, int i, void *fixedValue)
{
    int ret = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, fixedValue, IP4FORWARD_8K_F1_LEN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f2Value = i % 5000;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f3Value = i;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F4Value = i;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT32, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ip4foward8kPkName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 随机产生一个【0,5000】数值a，对 【a，a+500]】间的数据进行：写--主键更新--主键删除操作
void *RecycleWriteRemoveSync8k(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = 0;
    TestSetDataVolume(args, &countN, IP4FORWARD_8K_NUM * 2);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *f1Value = (char *)"fixed";
    char *fixedValue15 = (char *)malloc(IP4FORWARD_8K_F15_LEN * sizeof(char));
    if (fixedValue15 == NULL) {
        EXPECT_NE((void *)NULL, fixedValue15);
    }
    memset(fixedValue15, 'a', IP4FORWARD_8K_F15_LEN - 1);
    fixedValue15[IP4FORWARD_8K_F15_LEN - 1] = '\0';
    char *fixedValue15New = (char *)malloc(IP4FORWARD_8K_F15_LEN * sizeof(char));
    if (fixedValue15New == NULL) {
        EXPECT_NE((void *)NULL, fixedValue15New);
    }
    memset(fixedValue15New, 'b', IP4FORWARD_8K_F15_LEN - 1);
    fixedValue15New[IP4FORWARD_8K_F15_LEN - 1] = '\0';

    // write
    int index = rand() % (countN / 8);
    for (int loop = index; loop < (index + OP_NUM / 4); loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4foward8k, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        TestSetVertexPropertyByName8k(stmt, loop, f1Value, fixedValue15);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_UNIQUE_VIOLATION ||
            ret == GMERR_OUT_OF_MEMORY || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, 0);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // update
    for (int loop = index; loop < (index + OP_NUM / 4); loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4foward8k, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TestSetPkIndex8k(stmt, loop, f1Value);
        GmcBitMapT bitMap1 = {0, 63, NULL};
        uint8_t f6_bits1[IP4FORWARD_8K_BITMAP_LEN] = {0x08, 0x09, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15};
        bitMap1.bits = f6_bits1;
        ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_BITMAP, &bitMap1, sizeof(bitMap1));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F8value_new = (loop + 66) % countN;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT32, &F8value_new, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F9value_new = (loop + 66) % 5000;
        ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_UINT32, &F9value_new, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_FIXED, fixedValue15New, IP4FORWARD_8K_F15_LEN);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_FIXED, fixedValue15New, IP4FORWARD_8K_F16_LEN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // delete
    for (int loop = index; loop < (index + OP_NUM / 4); loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4foward8k, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        TestSetPkIndex8k(stmt, loop, f1Value);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    free(fixedValue15);
    free(fixedValue15New);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/**-------------------DTS2021092723076-----------------------**/
void ResGmcSetNodePropertyByNamePk(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void ResGmcNodeSetPropertyByNameRootNode(GmcNodeT *node, int i, bool boolValue, char *f14Value, void *fixedValue)
{
    int ret = 0;

    uint64_t f1Value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(0xFFFF, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(1, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(0xFFFFFFFF, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "res", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = boolValue;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11Value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, fixedValue, RES_BYTES_PROPERTY_SIZE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, fixedValue, RES_FIXED_PROPERTY_SIZE);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f17Value = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_UINT32, &f17Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f18Value = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_UINT32, &f18Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void ResGmcNodeUpdate_Root(GmcNodeT *node, int i, bool boolValue, char *f14Value)
{
    int ret = 0;

    uint64_t f1Value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = boolValue;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11Value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);
}

void ResGmcNodeSetPropertyByNameNode(GmcNodeT *node, int i, char *f14Value, void *fixedValue)
{
    int ret = 0;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = (i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = (i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7Value = (i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, fixedValue, RES_FIXED_PROPERTY_SIZE);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestCreateResourcePoolAndBind(GmcStmtT *stmt, const char *pool, const char *poolName)
{
    // 创建资源池
    int ret = GmcCreateResPool(stmt, pool);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(stmt, poolName, g_resLabel);
    ASSERT_EQ(GMERR_OK, ret);
}

void testUnbindResourcePoolAndDestrop(GmcStmtT *stmt, char *labelName, const char *poolName)
{
    int ret = GmcUseNamespace(stmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // 解绑资源池到表，第一要归还所有资源池，第二要解绑表，第三要解绑所有资源池
    ret = GmcUnbindResPoolFromLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    // 销毁资源池
    ret = GmcDestroyResPool(stmt, poolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 获取根节点以及非根节点下非嵌套的vector节点
void TestResGetRootAndChildVectorNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **V2)
{
    GmcNodeT *Root = NULL, *node[2] = {NULL};

    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "V2", &node[0]);
    ASSERT_EQ(GMERR_OK, ret);
    *root = Root;
    *V2 = node[0];
}

void ResReplaceOrInsertVertex(GmcStmtT *stmt, const char *labelName, bool boolValue, char *f14Value,
    char *fixedValue, int startNum, int endNum, int vectorNum, int writeFlag = 0)
{
    int ret = 0;
    int flagW = 0;
    int circleTime = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;
    if (writeFlag == 0) {
        flagW = GMC_OPERATION_REPLACE;
    } else {
        flagW = GMC_OPERATION_INSERT;
    }
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        circleTime = 0;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, (GmcOperationTypeE)flagW);
        ASSERT_EQ(GMERR_OK, ret);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
        ResGmcSetNodePropertyByNamePk(rootNode, i);
        ResGmcNodeSetPropertyByNameRootNode(rootNode, i, boolValue, f14Value, fixedValue);

        // 插入vector节点 V2和V2.V3和V2.V3.V5
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(v2Node, &v2Node);
            ASSERT_EQ(GMERR_OK, ret);
            ResGmcNodeSetPropertyByNameNode(v2Node, j, f14Value, fixedValue);
            // 获取V3节点
            ret = GmcNodeGetChild(v2Node, "V3", &v3Node);
            ASSERT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vectorNum; k++) {
                ret = GmcNodeAppendElement(v3Node, &v3Node);
                ASSERT_EQ(GMERR_OK, ret);
                ResGmcNodeSetPropertyByNameNode(v3Node, k, f14Value, fixedValue);
                // 获取V5节点
                ret = GmcNodeGetChild(v3Node, "V5", &v5Node);
                ASSERT_EQ(GMERR_OK, ret);
                for (uint32_t m = 0; m < vectorNum; m++) {
                    ret = GmcNodeAppendElement(v5Node, &v5Node);
                    ASSERT_EQ(GMERR_OK, ret);
                    ResGmcNodeSetPropertyByNameNode(v5Node, m, f14Value, fixedValue);
                }
            }
        }
        ret = GmcExecute(stmt);
        while (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = TestExcuteTime(stmt, ret);
        }
        if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_PRIMARY_KEY_VIOLATION &&
            ret != GMERR_OUT_OF_MEMORY && ret != GMERR_LOCK_NOT_AVAILABLE) {
            printf("[ResReplaceOrInsertVertex]i:%d line:%d\n", i, __LINE__);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

void ResSetPkIndexUpdate(GmcStmtT *stmt, int64_t pkValue)
{
    int ret = 0;
    int64_t keyValue = pkValue;
    ret = GmcSetIndexKeyName(stmt, g_resPk);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void ResUpdateVertex(
    GmcStmtT *stmt, const char *labelName, bool boolValue, char *f14Value, int startNum, int endNum)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;
    int circleTime = 0;
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        circleTime = 0;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
        ResGmcNodeUpdate_Root(rootNode, (i + 1), boolValue, f14Value);
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TRANSACTION_ROLLBACK) {
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

void ResDeleteVertex(GmcStmtT *stmt, const char *labelName, int startNum, int endNum)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;

    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_TRANSACTION_ROLLBACK) {
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

int ResBatchInsert(GmcConnT *conn, GmcStmtT *stmt, int startNum, int endNum, int vectorNum)
{
    int ret = 0;
    int retN = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;
    uint32_t totalNum = 0, successNum = 0;
    char *f14Value = (char *)"1vertex";
    char *fixedValue = (char *)"test111";
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_resLabel, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
        ResGmcSetNodePropertyByNamePk(rootNode, i);
        ResGmcNodeSetPropertyByNameRootNode(rootNode, i, false, f14Value, fixedValue);

        // 插入vector节点 V2和V2.V3和V2.V3.V5
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(v2Node, &v2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ResGmcNodeSetPropertyByNameNode(v2Node, j, f14Value, fixedValue);
            // 获取V3节点
            ret = GmcNodeGetChild(v2Node, "V3", &v3Node);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vectorNum; k++) {
                ret = GmcNodeAppendElement(v3Node, &v3Node);
                EXPECT_EQ(GMERR_OK, ret);
                ResGmcNodeSetPropertyByNameNode(v3Node, k, f14Value, fixedValue);
                // 获取V5节点
                ret = GmcNodeGetChild(v3Node, "V5", &v5Node);
                EXPECT_EQ(GMERR_OK, ret);
                for (uint32_t m = 0; m < vectorNum; m++) {
                    ret = GmcNodeAppendElement(v5Node, &v5Node);
                    EXPECT_EQ(GMERR_OK, ret);
                    ResGmcNodeSetPropertyByNameNode(v5Node, m, f14Value, fixedValue);
                }
            }
        }
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_LOCK_NOT_AVAILABLE &&
        ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_OUT_OF_MEMORY) {
        if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    retN = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, retN);
    GmcBatchDestroy(batch);
    return ret;
}

int ResBatchDelete(GmcConnT *conn, GmcStmtT *stmt, int startNum, int endNum)
{
    int ret = 0;
    int retN = 0;
    uint32_t totalNum = 0, successNum = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_resLabel, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ResSetPkIndexUpdate(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK && ret != GMERR_TRANSACTION_ROLLBACK && ret != GMERR_OUT_OF_MEMORY &&
        ret != GMERR_LOCK_NOT_AVAILABLE) {
        if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    retN = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, retN);
    GmcBatchDestroy(batch);
    return ret;
}

void *ResLabelWriteThread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = *(uint32_t *)args;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    int32_t vectorNum = 3;
    uint32_t startNum = 0;
    uint32_t endNum = RES_WRITE_COUNT;
    int32_t customer_batch_num = RES_WRITE_COUNT / BATCH_MAX_COUNT;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入
    ResReplaceOrInsertVertex(syncStmt, g_resLabel, false, (char *)"vertex", (char *)"test111", startC,
        (startC + RES_WRITE_COUNT), vectorNum);
    sleep(ST);
    // 更新
    ResUpdateVertex(syncStmt, g_resLabel, true, (char *)"111111", startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    // delete
    ResDeleteVertex(syncStmt, g_resLabel, startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    // 批量写,数据量为BATCH_MAX_COUNT
    for (int i = 0; i < customer_batch_num; i++) {
        startNum = startC + i * BATCH_MAX_COUNT;
        endNum = startNum + BATCH_MAX_COUNT;
        ResBatchInsert(syncConn, syncStmt, startNum, endNum, vectorNum);
    }
    if ((RES_WRITE_COUNT + startC - endNum) > 0) {
        ResBatchInsert(syncConn, syncStmt, endNum, (RES_WRITE_COUNT + startC), vectorNum);
    }
    // 批量删
    for (int i = 0; i < customer_batch_num; i++) {
        startNum = startC + i * BATCH_MAX_COUNT;
        endNum = startNum + BATCH_MAX_COUNT;
        ResBatchDelete(syncConn, syncStmt, startNum, endNum);
    }
    if ((RES_WRITE_COUNT + startC - endNum) > 0) {
        ResBatchDelete(syncConn, syncStmt, endNum, (RES_WRITE_COUNT + startC));
    }
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void ResReConnServer(GmcConnT **conn, GmcStmtT **stmt, const char *nameSpaceT = NULL)
{
    int ret = 0;
    GmcConnT *testConn = NULL;
    GmcStmtT *testStmt = NULL;
    ret = testGmcConnect(&testConn, &testStmt);
    EXPECT_EQ(GMERR_OK, ret);
    if (nameSpaceT) {
        ret = GmcUseNamespace(testStmt, nameSpaceT);
        EXPECT_EQ(GMERR_OK, ret);
    }

    *conn = testConn;
    *stmt = testStmt;
}

void *ResLabelTransWriteThread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = *(uint32_t *)args;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcConnT *batchConn = NULL;
    GmcStmtT *batchStmt = NULL;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_DEFAULT_TRX;
    config.readOnly = false;
    int32_t vectorNum = 3;
    uint32_t startNum = 0;
    uint32_t endNum = RES_TRANS_WRITE_COUNT;
    int32_t customer_batch_num = RES_TRANS_WRITE_COUNT / BATCH_MAX_COUNT;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&batchConn, &batchStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(batchStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入
    do {
        sleep(ST);
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ResReplaceOrInsertVertex(syncStmt, g_resLabel, false, (char *)"vertex", (char *)"test111", startC,
            (startC + RES_TRANS_WRITE_COUNT), vectorNum);
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            EXPECT_EQ(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新
    do {
        sleep(ST);
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ResUpdateVertex(syncStmt, g_resLabel, true, (char *)"111111", startC, (startC + RES_TRANS_WRITE_COUNT));
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            EXPECT_EQ(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    EXPECT_EQ(GMERR_OK, ret);

    // delete
    do {
        sleep(ST);
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ResDeleteVertex(syncStmt, g_resLabel, startC, (startC + RES_TRANS_WRITE_COUNT));
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            EXPECT_EQ(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量写,数据量为BATCH_MAX_COUNT
    do {
        sleep(ST);
        ret = GmcTransStart(batchConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < customer_batch_num; i++) {
            startNum = startC + i * BATCH_MAX_COUNT;
            endNum = startNum + BATCH_MAX_COUNT;
            ret = ResBatchInsert(batchConn, batchStmt, startNum, endNum, vectorNum);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                break;
            }
        }
        if ((RES_TRANS_WRITE_COUNT + startC - endNum) > 0) {
            ret = ResBatchInsert(batchConn, batchStmt, endNum, (RES_TRANS_WRITE_COUNT + startC), vectorNum);
        }
        ret = GmcTransCommit(batchConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(batchConn);
            EXPECT_EQ(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
            testGmcDisconnect(batchConn, batchStmt);
            ResReConnServer(&batchConn, &batchStmt, g_nameSpace);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量删
    do {
        sleep(ST);
        ret = GmcTransStart(batchConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < customer_batch_num; i++) {
            startNum = startC + i * BATCH_MAX_COUNT;
            endNum = startNum + BATCH_MAX_COUNT;
            ret = ResBatchDelete(batchConn, batchStmt, startNum, endNum);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                break;
            }
        }
        if ((RES_TRANS_WRITE_COUNT + startC - endNum) > 0) {
            ret = ResBatchDelete(batchConn, batchStmt, endNum, (RES_TRANS_WRITE_COUNT + startC));
        }
        ret = GmcTransCommit(batchConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(batchConn);
            EXPECT_EQ(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
            testGmcDisconnect(batchConn, batchStmt);
            ResReConnServer(&batchConn, &batchStmt, g_nameSpace);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(batchConn, batchStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void ResSetIndexKeyValueLpm4(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint32_t ipaddr, uint8_t maskLen)
{
    int ret = 0;
    uint32_t t_vrid = vrid;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &t_vrid, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t t_vrfid = vrfid;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &t_vrfid, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t t_ipaddr = ipaddr;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &t_ipaddr, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t t_mask_len = maskLen;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &t_mask_len, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_resLpm4);
    ASSERT_EQ(GMERR_OK, ret);
}

void ResGmcGetNodePropertyByNameRoot(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    int16_t f4Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    uint16_t f5Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    int8_t f6Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6Value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    bool f8Value = false;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8Value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9Value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    double f10Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10Value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    uint64_t f11Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11Value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    char f12Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12Value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    unsigned char f13Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13Value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    unsigned int propSize = 0;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);

    char stringValue[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    char bytesValueQuery[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", bytesValueQuery, RES_FIXED_PROPERTY_SIZE, &isNull);
    ASSERT_EQ(GMERR_OK, ret);

    char fixedValueQuery[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", fixedValueQuery, RES_FIXED_PROPERTY_SIZE, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
}

void ResMemberKeySetKeyValueV2V3(GmcIndexKeyT *key, int32_t i, void *fixedValue)
{
    int64_t key1Value = i;
    uint64_t key2Value = i;
    int32_t key3Value = i;
    int16_t key4Value = i;
    uint16_t key5Value = i % 32768;
    int8_t key6Value = i % 128;
    uint64_t key8Value = i;
    // "F0","F1","F2","F4","F5","F6","F16","F11"
    int ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_INT64, &key1Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_UINT64, &key2Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 2, GMC_DATATYPE_INT32, &key3Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 3, GMC_DATATYPE_INT16, &key4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 4, GMC_DATATYPE_UINT16, &key5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 5, GMC_DATATYPE_INT8, &key6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 6, GMC_DATATYPE_FIXED, fixedValue, RES_FIXED_PROPERTY_SIZE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 7, GMC_DATATYPE_TIME, &key8Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void ResMemberKeySetKeyValueV2Node(GmcIndexKeyT *key, uint64_t v2Key1, int32_t v2Key2, uint32_t v2Key3)
{
    int ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT64, &v2Key1, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_INT32, &v2Key2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 2, GMC_DATATYPE_UINT32, &v2Key3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void ResMemberKeySetKeyValueV5(GmcIndexKeyT *key, int64_t key1, int32_t key2, int16_t key3)
{
    int64_t v5Key1 = key1;
    int32_t v5Key2 = key2;
    int16_t v5Key3 = key3 % 32768;
    int ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_INT64, &v5Key1, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_INT32, &v5Key2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 2, GMC_DATATYPE_INT16, &v5Key3, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void ResMemberkeyUpdateRemoveAppend(GmcStmtT *stmt, int64_t pkvalue)
{
    bool boolValue = false;
    int ret = 0;
    int startNum = 0;
    int endNum = 10;
    char *f14Value = (char *)"vertexTest";
    char *fixedValue = (char *)"vertex1";
    int arrayNum = 3;
    int vectorNum = 3;
    int32_t circleTime = 0;
    int64_t primaryIndexValue = pkvalue;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v2V3Node = NULL, *v2V3V5Node = NULL;

    ret = testGmcPrepareStmtByLabelName(stmt, g_resLabel, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ResSetPkIndexUpdate(stmt, primaryIndexValue);
    // 获取根节点与非嵌套的vector节点和array节点
    TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
    GmcIndexKeyT *v2Key = NULL, *v3Key = NULL, *v5Key = NULL, *v3Key2 = NULL;
    uint64_t v2Key1 = 0;
    int32_t v2Key2 = 0;
    uint32_t v2Key3 = 0;
    GmcNodeT *v3Value1 = NULL, *v2Value1 = NULL, *v5Value1 = NULL;
    uint32_t updateValue = 100;
    ret = GmcNodeAllocKey(v2Node, g_v2MemberKeyName, &v2Key);
    EXPECT_EQ(GMERR_OK, ret);
    ResMemberKeySetKeyValueV2Node(v2Key, v2Key1, v2Key2, v2Key3);
    // update 1次
    ret = GmcNodeGetElementByKey(v2Node, v2Key, &v2Value1);
    ResGmcNodeSetPropertyByNameNode(v2Value1, updateValue, f14Value, fixedValue);
    // append 13次
    for (uint32_t m = 3; m < 16; m++) {
        ret = GmcNodeAppendElement(v2Node, &v2Node);
        EXPECT_EQ(GMERR_OK, ret);
        ResGmcNodeSetPropertyByNameNode(v2Node, m, f14Value, fixedValue);
    }
    // remove 2次
    for (int i = 1; i < vectorNum; i++) {
        v2Key1 = i;
        v2Key2 = i;
        v2Key3 = i;
        ResMemberKeySetKeyValueV2Node(v2Key, v2Key1, v2Key2, v2Key3);
        ret = GmcNodeRemoveElementByKey(v2Node, v2Key);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcNodeGetChild(v2Value1, "V3", &v2V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(v2V3Node, g_v3MemberKeyName, &v3Key);
    EXPECT_EQ(GMERR_OK, ret);
    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        ResMemberKeySetKeyValueV2V3(v3Key, i, fixedValue);
        ret = GmcNodeRemoveElementByKey(v2V3Node, v3Key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // append 13次
    for (uint32_t m = 3; m < 16; m++) {
        ret = GmcNodeAppendElement(v2V3Node, &v2V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        ResGmcNodeSetPropertyByNameNode(v2V3Node, m, f14Value, fixedValue);
    }
    // update 1次
    ResMemberKeySetKeyValueV2V3(v3Key, 1, fixedValue);
    ret = GmcNodeGetElementByKey(v2V3Node, v3Key, &v3Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ResGmcNodeSetPropertyByNameNode(v3Value1, updateValue, f14Value, fixedValue);

    // 再对V3下面的V5进行操作
    ret = GmcNodeGetChild(v2Value1, "V3", &v2V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(v2V3Node, g_v3MemberKeyName, &v3Key2);
    EXPECT_EQ(GMERR_OK, ret);
    ResMemberKeySetKeyValueV2V3(v3Key2, 2, fixedValue);
    ret = GmcNodeGetElementByKey(v2V3Node, v3Key2, &v3Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(v3Value1, "V5", &v2V3V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(v2V3V5Node, g_v5MemberKeyName, &v5Key);
    EXPECT_EQ(GMERR_OK, ret);
    // "F0","F2","F4"
    // remove 2次
    ResMemberKeySetKeyValueV5(v5Key, 0, 0, 0);
    ret = GmcNodeRemoveElementByKey(v2V3V5Node, v5Key);
    EXPECT_EQ(GMERR_OK, ret);
    ResMemberKeySetKeyValueV5(v5Key, 1, 1, 1);
    ret = GmcNodeRemoveElementByKey(v2V3V5Node, v5Key);
    EXPECT_EQ(GMERR_OK, ret);
    // append 13次
    for (uint32_t m = 3; m < 16; m++) {
        ret = GmcNodeAppendElement(v2V3V5Node, &v2V3V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        ResGmcNodeSetPropertyByNameNode(v2V3V5Node, m, f14Value, fixedValue);
    }
    // update 1次
    ResMemberKeySetKeyValueV5(v5Key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(v2V3V5Node, v5Key, &v5Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ResGmcNodeSetPropertyByNameNode(v5Value1, updateValue, f14Value, fixedValue);
    ret = GmcExecute(stmt);
    while (ret == GMERR_LOCK_NOT_AVAILABLE) {
        ret = TestExcuteTime(stmt, ret);
    }
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNIQUE_VIOLATION) {
        EXPECT_EQ(GMERR_OK, ret);
    } else if (ret == GMERR_OK) {
        ret = TestGetAffactRows(stmt, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcNodeFreeKey(v2Key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(v3Key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(v5Key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(v3Key2);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByNameNode(GmcNodeT *node, int i, char *f14Value, void *fixedValue)
{
    int ret = 0;
    bool isNull = 0;

    int64_t f0Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0Value);

    uint64_t f1Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1Value);

    int32_t f2Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2Value);

    uint32_t f3Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3Value);

    int16_t f4Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((i) % 32768, f4Value);

    uint16_t f5Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((i) % 65536, f5Value);

    int8_t f6Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6Value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((i) % 128, f6Value);

    uint8_t f7Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7Value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((i) % 256, f7Value);

    uint64_t f11Value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11Value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11Value);

    unsigned int propSize = 0;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value) + 1);

    char stringValue[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    char fixedValueQuery[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", fixedValueQuery, RES_FIXED_PROPERTY_SIZE, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueQuery, RES_FIXED_PROPERTY_SIZE);
    ASSERT_EQ(GMERR_OK, ret);
}

void resMemberKeyCheckData(GmcStmtT *stmt, int64_t pkvalue)
{
    bool boolValue = false;
    int ret = 0;
    int startNum = 0;
    int endNum = 10;
    char *f14Value = (char *)"vertexTest";
    char *fixedValue = (char *)"vertex1";
    int arrayNum = 3;
    int vectorNum = 3;
    bool isFinish = true;
    int64_t primaryIndexValue = pkvalue;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v2V3Node = NULL, *v2V3V5Node = NULL;
    uint32_t updateValue = 100;
    // 获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primaryIndexValue;
    ret = testGmcPrepareStmtByLabelName(stmt, g_resLabel, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ResSetPkIndexUpdate(stmt, primaryIndexValue);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    if (isFinish) {
        printf("pkvalue=%d\n", primaryIndexValue);
        ASSERT_EQ(false, isFinish);
    }
    TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
    ResGmcGetNodePropertyByNameRoot(rootNode);

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    uint32_t count = 0;
    ret = GmcNodeGetElementCount(v2Node, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(14, count);

    ret = GmcNodeGetElementByIndex(v2Node, 0, &v2Node);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByNameNode(v2Node, updateValue, f14Value, fixedValue);
    ret = GmcNodeGetChild(v2Node, "V3", &v2V3Node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(v2V3Node, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(15, count);
    GmcNodeT *V3NodeTest = NULL;
    ret = GmcNodeGetElementByIndex(v2V3Node, 0, &v2V3Node);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByNameNode(v2V3Node, updateValue, f14Value, fixedValue);
    // 获取V5节点，该节点未变
    ret = GmcNodeGetChild(v2V3Node, "V5", &v2V3V5Node);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t m = 0; m < vectorNum; m++) {
        ret = GmcNodeGetElementByIndex(v2V3V5Node, m, &v2V3V5Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByNameNode(v2V3V5Node, m, f14Value, fixedValue);
    }

    ret = GmcNodeGetElementByIndex(v2V3Node, 1, &v2V3Node);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByNameNode(v2V3Node, 2, f14Value, fixedValue);
    ret = GmcNodeGetChild(v2V3Node, "V5", &v2V3V5Node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(v2V3V5Node, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(14, count);
    for (uint32_t m = 1; m < 14; m++) {
        int i = m + 2;
        ret = GmcNodeGetElementByIndex(v2V3V5Node, m, &v2V3V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByNameNode(v2V3V5Node, i, f14Value, fixedValue);
    }
    ret = GmcNodeGetElementByIndex(v2V3V5Node, 0, &v2V3V5Node);
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByNameNode(v2V3V5Node, updateValue, f14Value, fixedValue);
    ret = GmcNodeGetElementByIndex(v2V3V5Node, 14, &v2V3V5Node);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    for (int32_t i = 1; i < 14; i++) {
        int m = i + 2;
        ret = GmcNodeGetElementByIndex(v2Node, i, &v2Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByNameNode(v2Node, m, f14Value, fixedValue);
        ret = GmcNodeGetChild(v2Node, "V3", &v2V3Node);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(v2V3Node, 0, &v2V3Node);
        ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
}

// member key
void *ResMemberkeyIncThread(void *args)
{
    int32_t ret = 0;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    int32_t vectorNum = 3;
    uint32_t startNum = *(int32_t *)args;
    uint32_t endNum = 50;

    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ResReplaceOrInsertVertex(syncStmt, g_resLabel, false, (char *)"vertexTest", (char *)"vertex1", startNum,
        (startNum + endNum), vectorNum, 1);
    sleep(ST);
    // member key增量更新
    ResMemberkeyUpdateRemoveAppend(syncStmt, startNum);
    sleep(ST);
    resMemberKeyCheckData(syncStmt, startNum);
    sleep(ST);
    // delete
    ResDeleteVertex(syncStmt, g_resLabel, startNum, (startNum + endNum));
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 全表扫描线程
void *ResWholeLabelScan(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&conn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // 全表扫描 --同步
    TestWholdLabelScan(syncStmt, g_resLabel);
    ret = testGmcDisconnect(conn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void ResourceAsyncReplaceCb(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg)
{
    int ret = 0;
    for (uint32_t i = 0; i < resInfo->resIdNum; i++) {
        uint16_t poolId = 0;
        uint16_t count = 0;
        uint32_t startIndex = 0;
        ret = GmcGetPoolIdResource(resInfo->resIdBuf[i], &poolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resInfo->resIdBuf[i], &count);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resInfo->resIdBuf[i], &startIndex);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 原有的回调
    if (userData) {
        AsyncUserDataT *userData = (AsyncUserDataT *)userData;
        userData->status = status;
        userData->affectRows = affectedRows;
        userData->historyRecvNum++;
        if (userData->lastError != NULL) {
            ret = strcmp(userData->lastError, errMsg);
            EXPECT_EQ(GMERR_OK, ret);
        }
        userData->recvNum++;
    }
}
void ResAsyncReplaceOrInsertVertex(GmcStmtT *stmt, const char *labelName, bool boolValue, char *f14Value,
    char *fixedValue, int startNum, int endNum, int vectorNum, int writeFlag = 0)
{
    int ret = 0;
    int flagW = 0;
    uint64_t resId;
    uint32_t resCnt = 1;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;
    if (writeFlag == 0) {
        flagW = GMC_OPERATION_REPLACE_WITH_RESOURCE;
    } else {
        flagW = GMC_OPERATION_INSERT_WITH_RESOURCE;
    }
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, (GmcOperationTypeE)flagW);
        ASSERT_EQ(GMERR_OK, ret);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
        ResGmcSetNodePropertyByNamePk(rootNode, i);
        ResGmcNodeSetPropertyByNameRootNode(rootNode, i, boolValue, f14Value, fixedValue);

        // 插入vector节点 V2和V2.V3和V2.V3.V5
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(v2Node, &v2Node);
            ASSERT_EQ(GMERR_OK, ret);
            ResGmcNodeSetPropertyByNameNode(v2Node, j, f14Value, fixedValue);
            // 获取V3节点
            ret = GmcNodeGetChild(v2Node, "V3", &v3Node);
            ASSERT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vectorNum; k++) {
                ret = GmcNodeAppendElement(v3Node, &v3Node);
                ASSERT_EQ(GMERR_OK, ret);
                ResGmcNodeSetPropertyByNameNode(v3Node, k, f14Value, fixedValue);
                // 获取V5节点
                ret = GmcNodeGetChild(v3Node, "V5", &v5Node);
                ASSERT_EQ(GMERR_OK, ret);
                for (uint32_t m = 0; m < vectorNum; m++) {
                    ret = GmcNodeAppendElement(v5Node, &v5Node);
                    ASSERT_EQ(GMERR_OK, ret);
                    ResGmcNodeSetPropertyByNameNode(v5Node, m, f14Value, fixedValue);
                }
            }
        }
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceWithResourceCb = ResourceAsyncReplaceCb;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &replaceRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        ASSERT_EQ(GMERR_OK, ret);
        if (data.status != GMERR_CONFIGURATION_LIMIT_EXCEEDED && data.status != GMERR_LOCK_NOT_AVAILABLE &&
            data.status != GMERR_PRIMARY_KEY_VIOLATION && data.status != GMERR_UNIQUE_VIOLATION &&
            data.status != GMERR_FEATURE_NOT_SUPPORTED) {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }
}

void ResAsyncUpdateVertex(
    GmcStmtT *stmt, const char *labelName, bool boolValue, char *f14Value, int startNum, int endNum)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        // 获取根节点与非嵌套的vector节点和array节点
        TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
        ResGmcNodeUpdate_Root(rootNode, (i + 1), boolValue, f14Value);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }
}

void ResAsyncDeleteVertex(GmcStmtT *stmt, const char *labelName, int startNum, int endNum)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;

    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ResSetPkIndexUpdate(stmt, i);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }
}

void ResLpm4Scan(GmcStmtT *stmt, int startNum, int endNum)
{
    int ret = 0;
    GmcNodeT *rootNode = NULL, *v2Node = NULL, *v3Node = NULL, *v5Node = NULL;
    bool isFinish = false;
    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_resLabel, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t destIpAddr = 0;
        uint8_t maskLen = 0;
        if (i <= MAX_MASK_LEN_16) {
            destIpAddr = ((i + 2) << 16);
            maskLen = ((16) & 0xff);
        } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
            destIpAddr = ((i + 2) << 8);
            maskLen = ((24) & 0xff);
        } else {
            destIpAddr = ((i + 2));
            maskLen = ((32) & 0xff);
        }
        ResSetIndexKeyValueLpm4(stmt, 0, 0, destIpAddr, maskLen);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            if (!isFinish) {
                // 获取根节点与非嵌套的vector节点和array节点
                TestResGetRootAndChildVectorNode(stmt, &rootNode, &v2Node);
                ResGmcGetNodePropertyByNameRoot(rootNode);
            }
        } else if (ret != GMERR_NO_DATA && ret != GMERR_LOCK_NOT_AVAILABLE) {
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

// lpm4扫描线程
void *ResLpm4ScanThread(void *args)
{
    int32_t ret = 0;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    int32_t vectorNum = 3;
    uint32_t startNum = 0;
    uint32_t endNum = 500;

    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret=GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    int index = rand() % (RES_WRITE_COUNT + RES_TRANS_WRITE_COUNT);
    ResLpm4Scan(syncStmt, index, (index + endNum));
    testGmcDisconnect(syncConn, syncStmt);
    return NULL;
}

// 资源表异步DML操作，写--主键更新--主键删除操作
void *ResLabelAsyncOperationThread(void *args)
{
    int32_t ret = 0;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;
    uint32_t startC = *(uint32_t *)args;
    int32_t vectorNum = 3;
    AsyncUserDataT tdata = {0};
    ret = testGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(asyncStmt, g_nameSpace, use_namespace_callback, &tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata.status);
    // 插入, 资源表异步insert、replace的GmcOperationTypeE和同步不一样
    ResAsyncReplaceOrInsertVertex(asyncStmt, g_resLabel, false, (char *)"vertex", (char *)"test111", startC,
        (startC + RES_WRITE_COUNT), vectorNum);
    sleep(ST);
    // 更新
    ResAsyncUpdateVertex(asyncStmt, g_resLabel, true, (char *)"111111", startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    // 删除
    ResAsyncDeleteVertex(asyncStmt, g_resLabel, startC, (startC + RES_WRITE_COUNT));
    sleep(ST);
    ret = testGmcDisconnect(asyncConn, asyncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}


/**-------------------DTS2022050509690-----------------------**/
int TestCreateIfLabel()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *testSchema = NULL;

    readJanssonFile("./schemaFile/if_general_complex.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_ifStabilityName);
    ret = GmcCreateVertexLabel(stmt, testSchema, g_testLabelconfig);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create label if_general_complex success \n");
    } else {
        testGmcGetLastError(NULL);
    }
    free(testSchema);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

void TestIfStabilitySetNodeRoot(GmcNodeT *node, uint32_t index, void *fixedValue)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"name", GMC_DATATYPE_FIXED, fixedValue, IF_TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t vrid = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t if_type = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"if_type", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"shutdown", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"linkup", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tbtp", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tb", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tp", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"port_switch", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"forwardType", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"macAddress", GMC_DATATYPE_FIXED, fixedValue, IF_TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t ipv4_mtu = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"ipv4_mtu", GMC_DATATYPE_UINT16, &ipv4_mtu, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"ipv6_mtu", GMC_DATATYPE_UINT16, &ipv4_mtu, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"on_board", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"lagid", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void IfStabilitySetNodeIfm(GmcNodeT *node, uint32_t index, void *fixedValue)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    uint32_t value6 = 7 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"simple_name", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"description", GMC_DATATYPE_FIXED, fixedValue, IF_TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"is_configure", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"main_ifindex", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"sub_max_num", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"sub_curr_num", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"error_down", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"statistic", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vsys_id", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"zone_id", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"last_up_time", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"last_down_time", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void IfStabilitySetNodeDev(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"dev_id", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"chassis_id", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"slot_id", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"card_id", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"unit_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"port_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void IfStabilitySetNodeL2(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"trunk_id", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vlan_id", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"l2_portindex", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vsi", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tunnel_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void IfStabilitySetNodePort(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"speed", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"duplex", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"flow_control", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"jumbo", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"baud", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"rmon", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"phy_link", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"if_mib", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"on_board", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void IfStabilitySetNodeT1V(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestIfStabilityGetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **ifmN, GmcNodeT **devN, GmcNodeT **l2N,
    GmcNodeT **portN, GmcNodeT **T1_VN)
{
    GmcNodeT *Root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL, *node4 = NULL, *node5 = NULL;
    *root = NULL;
    *ifmN = NULL;
    *devN = NULL;
    *l2N = NULL;
    *portN = NULL;
    *T1_VN = NULL;
    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "ifm", &node1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "dev", &node2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "l2", &node3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "port", &node4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1_V", &node5);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *ifmN = node1;
    *devN = node2;
    *l2N = node3;
    *portN = node4;
    *T1_VN = node5;
}
void TestIfStabilitySetPk(GmcNodeT *node, uint32_t pkValue)
{
    int ret = 0;
    uint32_t ifindex = pkValue;
    ret = GmcNodeSetPropertyByName(node, (char *)"ifindex", GMC_DATATYPE_UINT32, &ifindex, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestIfStabilityInsert(GmcStmtT *stmt, int32_t startPk, int32_t endPk)
{
    int32_t ret = 0;
    uint32_t vectorNum = 3;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    for (int32_t i = startPk; i < endPk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_ifStabilityName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        TestIfStabilityGetNode(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        TestIfStabilitySetPk(root, i);
        if (i % 2 == 0) {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"xxxxx");
        } else {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"yyyyy");
        }
        IfStabilitySetNodeIfm(ifmN, i, (uint8_t *)"fixed");
        IfStabilitySetNodeDev(devN, i);
        IfStabilitySetNodeL2(l2N, i);
        IfStabilitySetNodePort(portN, i);
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(T1_VN, &T1_VN);
            EXPECT_EQ(GMERR_OK, ret);
            IfStabilitySetNodeT1V(T1_VN, j);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

int PrepareWriteIfGeneralComplex(int32_t startPk, int32_t endPk)
{
    int32_t ret = 0;
    uint32_t vectorNum = 3;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = startPk; i < endPk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_ifStabilityName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestIfStabilityGetNode(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        TestIfStabilitySetPk(root, i);
        if (i % 5 == 0) {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"fixed");
        } else if (i % 5 == 1) {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"verte");
        } else if (i % 5 == 2) {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"56789");
        } else if (i % 5 == 3) {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"12345");
        } else {
            TestIfStabilitySetNodeRoot(root, i, (uint8_t *)"test1");
        }
        IfStabilitySetNodeIfm(ifmN, i, (uint8_t *)"fixed");
        IfStabilitySetNodeDev(devN, i);
        IfStabilitySetNodeL2(l2N, i);
        IfStabilitySetNodePort(portN, i);
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(T1_VN, &T1_VN);
            EXPECT_EQ(GMERR_OK, ret);
            IfStabilitySetNodeT1V(T1_VN, j);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

void IfStabilitySetPkIndex(GmcStmtT *stmt, uint32_t pkValue)
{
    int ret = 0;
    uint32_t pk_Value = pkValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk_Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ifStabilityPkName);
    EXPECT_EQ(GMERR_OK, ret);
}

void IfStabilityPkDelete(GmcStmtT *stmt, int32_t startPk, int32_t endPk)
{
    int32_t ret = 0;
    uint32_t vectorNum = 3;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    for (int32_t i = startPk; i < endPk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_ifStabilityName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        IfStabilitySetPkIndex(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void IfStabilityUpdateSetProperty(GmcNodeT *node, uint32_t index, void *fixedValue)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"name", GMC_DATATYPE_FIXED, fixedValue, IF_TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
}

void IfStabilityPkUpdate(GmcStmtT *stmt, int32_t startPk, int32_t endPk)
{
    int32_t ret = 0;
    uint32_t vectorNum = 3;
    uint8_t *fixedUpdateValue = (uint8_t *)"field";
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    for (int32_t i = startPk; i < endPk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_ifStabilityName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        IfStabilitySetPkIndex(stmt, i);
        TestIfStabilityGetNode(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        IfStabilityUpdateSetProperty(root, i, fixedUpdateValue);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
void IfGeneralComplexUpdate(int32_t startPk, int32_t endPk)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IfStabilityPkUpdate(stmt, startPk, endPk);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestIfGeneralComplexDelete(int32_t startPk, int32_t endPk)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IfStabilityPkDelete(stmt, startPk, endPk);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// truncate操作
void TestIfGeneralComplexTruncate()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = 0;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, g_ifStabilityName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/**-------------------DTS2022042610806-----------------------**/
void *ResLabelInsertRollbackThread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = *(uint32_t *)args;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcConnT *batchConn = NULL;
    GmcStmtT *batchStmt = NULL;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_DEFAULT_TRX;
    config.readOnly = false;
    int vectorNum = 3;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入
    for (int i = 0; i < 5; i++) {
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ResReplaceOrInsertVertex(syncStmt, g_resLabel, false, (char *)"vertex", (char *)"test111", startC,
            (startC + 50), vectorNum);
        ret = GmcTransRollBack(syncConn);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *ResLabelDeleteCommitThread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = *(uint32_t *)args;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcConnT *batchConn = NULL;
    GmcStmtT *batchStmt = NULL;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_DEFAULT_TRX;
    config.readOnly = false;

    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // delete
    do {
        ret = GmcTransStart(syncConn, &config);
        EXPECT_EQ(GMERR_OK, ret);
        ResDeleteVertex(syncStmt, g_resLabel, startC, (startC + 50));
        ret = GmcTransCommit(syncConn);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            int retN = GmcTransRollBack(syncConn);
            EXPECT_EQ(GMERR_OK, retN);
            continue;
        } else if (ret == GMERR_OK) {
            break;
        }
    } while (ret == GMERR_TRANSACTION_ROLLBACK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/**-------------------DTS2022042513337-----------------------**/
#pragma pack(1)
typedef struct miniforward_struct_t {
    uint32_t ifindex;
    uint32_t vr_id;
    uint32_t vrf_index;
    int8_t destIpAddr[16];
    uint8_t maskLen;
    uint16_t vlanid;
    uint32_t vrid;
    uint16_t F1 : 16;
    uint16_t F2 : 16;
    uint8_t F3 : 8;
} MINI_FORWARD_TSTRUCT;
#pragma pack()

int CreateLabelMiniLpm()
{
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    char *testSchema = NULL;
    readJanssonFile("schemaFile/ip4foward_mini_lpm.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = testGmcConnect(&connSync, &stmtSync);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmtSync, g_labelNameIp4fowardMiniLpm);
    ret = GmcCreateVertexLabel(stmtSync, testSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create label ip4foward_mini_lpm success \n");
    } else {
        testGmcGetLastError(NULL);
    }
    free(testSchema);
    ret = testGmcDisconnect(connSync, stmtSync);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// mini table设置vertex属性
void TestMiniStructSetVertex(MINI_FORWARD_TSTRUCT *vertex, int32_t valueT)
{
    uint8_t wrFixed6[16] = {0};
    wrFixed6[0] = 0xcd;
    wrFixed6[1] = 0xcd;
    wrFixed6[2] = 0x91;
    wrFixed6[3] = 0x0a;
    wrFixed6[4] = 0x22;
    wrFixed6[5] = 0x22;
    wrFixed6[6] = 0x54;
    wrFixed6[7] = 0x98;
    wrFixed6[8] = 0x84;
    wrFixed6[9] = 0x75;
    wrFixed6[10] = 0x11;
    wrFixed6[11] = valueT % 256;
    wrFixed6[12] = valueT % 255;
    wrFixed6[13] = valueT % 254;
    wrFixed6[14] = valueT % 253;
    wrFixed6[15] = valueT % 252;

    vertex->ifindex = valueT;
    vertex->vr_id = 0;
    vertex->vrf_index = 0;
    memcpy(vertex->destIpAddr, wrFixed6, 16);
    vertex->maskLen = 128;
    vertex->vlanid = valueT;
    vertex->vrid = valueT % 5000;
    vertex->F1 = (valueT % 3000) + 3;
    vertex->F2 = (valueT % 3000) + 1;
    vertex->F3 = ((valueT % 3000) + 2) % 256;
}

void TestMiniSetPrimaryKey(MINI_FORWARD_TSTRUCT *d, int intV)
{
    d->ifindex = (uint32_t)intV;  // uint32  4
}

void TestMiniStructReadProperty(MINI_FORWARD_TSTRUCT *obj, int i)
{
    int ret = 0;
    uint8_t wrFixed6[16];
    wrFixed6[0] = 0xcd;
    wrFixed6[1] = 0xcd;
    wrFixed6[2] = 0x91;
    wrFixed6[3] = 0x0a;
    wrFixed6[4] = 0x22;
    wrFixed6[5] = 0x22;
    wrFixed6[6] = 0x54;
    wrFixed6[7] = 0x98;
    wrFixed6[8] = 0x84;
    wrFixed6[9] = 0x75;
    wrFixed6[10] = 0x11;
    wrFixed6[11] = i % 256;
    wrFixed6[12] = i % 255;
    wrFixed6[13] = i % 254;
    wrFixed6[14] = i % 253;
    wrFixed6[15] = i % 252;

    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t maskLen = 128;
    uint32_t ifindex = i;
    uint16_t vlanid = i;
    uint32_t vrid = i % 5000;
    uint16_t f1Value = ((i % 3000) + 3) & 0xFFFF;
    uint16_t f2Value = ((i % 3000) + 1) & 0xFFFF;
    uint8_t f3Value = (((i % 3000) + 2) % 256) & 0xFF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &vr_id, &obj->vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &vrf_index, &obj->vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, wrFixed6, obj->destIpAddr, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &obj->maskLen, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &ifindex, &obj->ifindex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

// 结构化读
void TestMiniStructRead(GmcStmtT *stmt, int32_t startVal, uint32_t count)
{
    int ret = 0;
    int32_t startPkVal = startVal;
    uint32_t vertexCount = count;
    bool isFinish = false;
    MINI_FORWARD_TSTRUCT obj;
    memset(&obj, 0, sizeof(obj));
    TestLabelInfoT labelInfo = {(char *)g_labelNameIp4fowardMiniLpm, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4fowardMiniLpm, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        TestMiniSetPrimaryKey(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            TestMiniStructReadProperty(&obj, i);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 结构化+非结构化
void *ReadThreadIp4fowardMiniLpm(void *args)
{
    int ret = 0;
    GmcConnT *conn_read;
    GmcStmtT *stmt_read;
    uint32_t countN = MINI_LABEL_PREPARE_DATA;
    ret = testGmcConnect(&conn_read, &stmt_read);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < countN; i++) {
        uint32_t pk = i;
        ret = testGmcPrepareStmtByLabelName(stmt_read, g_labelNameIp4fowardMiniLpm, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // 结构化读
        TestMiniStructRead(stmt_read, pk, 1);
        // 非结构化读
        ret = GmcSetIndexKeyValue(stmt_read, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_read, g_ip4fowardMiniLpmPkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_read);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = testGmcDisconnect(conn_read, stmt_read);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// localhash非唯一索引获取表记录数
void *GetRecordLocalhashMiniLpm(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = MINI_LABEL_PREPARE_DATA;
    // 开启缩容，设置内存上限为4k
    int packShrinkThresholdSize = 4 * 1024;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 0, NULL, NULL, NULL, NULL,
        &packShrinkThresholdSize);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t result = 0;
    for (int i = 0; i < countN; i++) {
        int loop = i;
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4fowardMiniLpm, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        uint16_t local_vlanidValue = loop;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &local_vlanidValue, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t local_vridValue = loop % 5000;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &local_vridValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, g_labelNameIp4fowardMiniLpm, g_ip4fowardMiniLpmLocalhashName, &result);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void LpmSetVertexPropertyPK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t ifindexValue = i;
    ret = GmcSetVertexProperty(stmt, "ifindex", GMC_DATATYPE_UINT32, &ifindexValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void LpmSetVertexPropertySkVlanid(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint16_t vlanidValue = i;
    ret = GmcSetVertexProperty(stmt, "vlanid", GMC_DATATYPE_UINT16, &vlanidValue, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void LpmSetVertexPropertySkVrid(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t vridValue = i;
    ret = GmcSetVertexProperty(stmt, "vrid", GMC_DATATYPE_UINT32, &vridValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void LpmSetVertexPropertyBitfield(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint16_t f1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD16, &f1Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t f2Value = (i + 1) % 1024;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD16, &f2Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f3Value = (i + 2) % 128;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &f3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void LpmSetVertexPropertyLpm6(GmcStmtT *stmt, int i)
{
    int ret = 0;
    unsigned int wrUnit32 = 0;
    uint8_t wrFixed6[16] = {0};
    wrFixed6[0] = 0xcd;
    wrFixed6[1] = 0xcd;
    wrFixed6[2] = 0x91;
    wrFixed6[3] = 0x0a;
    wrFixed6[4] = 0x22;
    wrFixed6[5] = 0x22;
    wrFixed6[6] = 0x54;
    wrFixed6[7] = 0x98;
    wrFixed6[8] = 0x84;
    wrFixed6[9] = 0x75;
    wrFixed6[10] = 0x11;
    wrFixed6[11] = i % 256;
    wrFixed6[12] = i % 255;
    wrFixed6[13] = i % 254;
    wrFixed6[14] = i % 253;
    wrFixed6[15] = i % 252;
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wrUnit32, sizeof(wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wrUnit32, sizeof(wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wrFixed6, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    uint8_t wrUnit8 = 128;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wrUnit8, sizeof(wrUnit8));
    EXPECT_EQ(GMERR_OK, ret);
}

void ReadIp4fowardLpmByGet(GmcStmtT *stmt)
{
    int ret = 0;
    bool isNull = false;
    uint32_t ifindex_value = 0;
    uint32_t vr_id_value = 0;
    uint32_t vrf_index_value = 0;
    uint8_t mask_len_value = 0;
    uint16_t vlanid_value = 0;
    uint32_t vrid_value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "ifindex", &ifindex_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &vr_id_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &vrf_index_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &mask_len_value, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vlanid", &vlanid_value, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrid", &vrid_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestLpmWriteData(GmcStmtT *stmt, int startPk, int endPk)
{
    int ret = 0;
    for (int loop = startPk; loop < endPk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4fowardMiniLpm, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        LpmSetVertexPropertyPK(stmt, loop);
        LpmSetVertexPropertySkVlanid(stmt, loop);
        LpmSetVertexPropertySkVrid(stmt, loop % 5000);
        LpmSetVertexPropertyBitfield(stmt, loop % 3000);
        LpmSetVertexPropertyLpm6(stmt, loop);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
int TestLpmPrepareData(int startPk, int endPk)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpmWriteData(stmt, startPk, endPk);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

// 随机产生一个【0,4w】数值a，对 【a，a+OP_NUM】间的数进行主键索引更新
void *UpdateThreadIp4fowardMiniLpm(void *args)
{
    int ret = 0;
    GmcConnT *conn_update = NULL;
    GmcStmtT *stmt_update = NULL;
    uint32_t countN = MINI_LABEL_PREPARE_DATA;
    // 开启缩容，设置内存上限为4k
    int packShrinkThresholdSize = 4 * 1024;
    ret = testGmcConnect(&conn_update, &stmt_update, GMC_CONN_TYPE_SYNC, 0, NULL, NULL, NULL, NULL,
        &packShrinkThresholdSize);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 500; i < countN; i++) {
        int loop = i;
        uint32_t pk = i;
        ret = testGmcPrepareStmtByLabelName(stmt_update, g_labelNameIp4fowardMiniLpm, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_update, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        LpmSetVertexPropertySkVlanid(stmt_update, (666 + loop) % countN);
        LpmSetVertexPropertySkVrid(stmt_update, (666 + loop) % 5000);
        ret = GmcSetIndexKeyName(stmt_update, g_ip4fowardMiniLpmPkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_update);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = testGmcDisconnect(conn_update, stmt_update);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// localhash非唯一索引扫描
void *LocalhashScanMiniLpm(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = MINI_LABEL_PREPARE_DATA;
    // 开启缩容，设置内存上限为4k
    int packShrinkThresholdSize = 4 * 1024;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 0, NULL, NULL, NULL, NULL,
        &packShrinkThresholdSize);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t result = 0;
    for (int i = 0; i < countN; i++) {
        int loop = i;
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4fowardMiniLpm, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_ip4fowardMiniLpmLocalhashName);
        EXPECT_EQ(GMERR_OK, ret);
        uint16_t local_vlanidValue = loop;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &local_vlanidValue, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t local_vridValue = loop % 5000;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &local_vridValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        bool isFinish = false;
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish || ret != GMERR_OK) {
                    break;
                }
                ReadIp4fowardLpmByGet(stmt);
            }
        } else {
            EXPECT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/**-------------------DTS2022042901661-----------------------**/
void ReadMemory(char *fileName, uint32_t *memory1, uint32_t *memory2, uint32_t *memory3)
{
    FILE *fp = NULL;
    char buff[512] = {0};
    char buf[512] = {0};
    const char delims[32] = ":";
    const char delims2[32] = "] MB";
    const char delims3[32] = "] KB";
    const char delims4[32] = "] Byte";
    char *pSave = NULL;
    char *temp = NULL;

    int32_t memoryN[3] = {0};
    if (fileName) {
        fp = fopen(fileName, "r");
        if (fp) {
            while (fgets(buff, 512, fp)) {
                temp = strtok_r(buff, delims, &pSave);
                if (strcmp(temp, "  TOTAL_PHY_SIZE") == 0) {
                    for (int i = 0; i < 3; i++) {
                        if (temp != NULL) {
                            if (i == 0) {
                                temp = strtok_r(NULL, "[", &pSave);
                                temp = strtok_r(NULL, delims2, &pSave);
                                memoryN[i] = atoi(temp);
                            } else if (i == 1) {
                                temp = strtok_r(NULL, "[", &pSave);
                                temp = strtok_r(NULL, delims3, &pSave);
                                memoryN[i] = atoi(temp);
                            } else if (i == 2) {
                                temp = strtok_r(NULL, "[", &pSave);
                                temp = strtok_r(NULL, delims4, &pSave);
                                memoryN[i] = atoi(temp);
                            }
                        }
                    }
                    break;
                }
            }
            fclose(fp);
        }
    }
    (*memory1) = memoryN[0];
    (*memory2) = memoryN[1];
    (*memory3) = memoryN[2];
    printf("memory:%d MB %d KB %d Bytes\n", memoryN[0], memoryN[1], memoryN[2]);
}

// 使用命令读取CTX_NAME='SeChainedHashShmMemCtx'的占用内存
void GmsysviewReadMemory(char *fileName, uint32_t *memory1, uint32_t *memory2, uint32_t *memory3)
{
    char command[256];
    char const *viewname = "V\\$COM_SHMEM_CTX";
    (void)snprintf(command, 256, "%s/gmsysview -q %s -f CTX_NAME='DbTopSharedMemoryContext' >%s",
        g_toolPath, viewname, fileName);
    int ret = system(command);
    EXPECT_EQ(GMERR_OK, ret);
    ReadMemory(fileName, memory1, memory2, memory3);
}

/**-------------------DTS2022042516099-----------------------**/
// gmimport：导入系统中没有的一张表再删掉
#define CYCLE_TIME 100
char *g_configLabel = (char *)"{\"auto_increment\":10}";
void *TestGmimportVertexLabel(void *arg)
{
    int ret = 0;
    char command1[1024] = {0};
    for (int i = 0; i < CYCLE_TIME; i++) {
        char const *toolsTestLabelName = "tools_test";
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, toolsTestLabelName);
        // 使用工具导表一张
        const char filePath[256] = "./schemaFile/tools_test.gmjson";
        (void)snprintf(command1, 256, "%s/gmimport -c vschema -f %s ", g_toolPath, filePath);
        ret = executeCommand(command1, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/12_Compatibility/002_LSQuestionVariationSDV/schemaFile/"
        "tools_test.gmjson\" successfully");
        EXPECT_EQ(GMERR_OK, ret);
        testGmcDisconnect(conn, stmt);
    }
    return NULL;
}

int TestCreateSubLabel()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *testSchema = NULL;
    char *testschema2 = NULL;
    readJanssonFile("./schemaFile/sub_ip4forward.gmjson", &testschema2);
    EXPECT_NE((void *)NULL, testschema2);
    readJanssonFile("./schemaFile/customer.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_subip4fowardName);
    ret = GmcDropVertexLabel(stmt, g_customerName);
    ret = GmcCreateVertexLabel(stmt, testschema2, g_testLabelconfig);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create %s success \n", g_subip4fowardName);
    } else {
        testGmcGetLastError(NULL);
    }
    ret = GmcCreateVertexLabel(stmt, testSchema, g_configLabel);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create %s success \n", g_customerName);
    } else {
        testGmcGetLastError(NULL);
    }
    free(testSchema);
    free(testschema2);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

void TestCustomerGetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **array, GmcNodeT **vector)
{
    GmcNodeT *Root = NULL, *node1 = NULL, *node2 = NULL;
    *root = NULL;
    *array = NULL;
    *vector = NULL;
    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "order", &node1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "job", &node2);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *array = node1;
    *vector = node2;
}

void ReadSubCustomerByGet(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = false;
    uint64_t value = 0;
    unsigned int propSize = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"cid", &value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"cname", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    char *cname = (char *)malloc(propSize);
    if (cname == NULL) {
        ASSERT_NE((void *)NULL, cname);
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"cname", cname, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    free(cname);
}

void SubCallbackWithCustomerKeyold(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *testUserData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    uint32_t size;
    const void *keyValue = NULL;
    char keyName[128] = {0};
    GmcNodeT *root = NULL, *nodeA = NULL, *nodeV = NULL;
    SnUserDataT *userData = (SnUserDataT *)testUserData;
    bool eof = false;
    bool isNull = false;
    uint64_t cid_value = 0;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 1: { // 推送old
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_AGED: {
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            TestCustomerGetNode(subStmt, &root, &nodeA, &nodeV);
                            ReadSubCustomerByGet(root);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("[%s]default: invalid eventType:%d  %d\r\n", __FUNCTION__, info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 4: { // 推送key
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            EXPECT_EQ(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, "cid", &cid_value, sizeof(uint64_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            break;
                        }
                        default: {
                            printf("[%s]default: invalid eventType:%d  %d\r\n", __FUNCTION__, info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 5: { // 推送key+old
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // key 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            EXPECT_EQ(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, "cid", &cid_value, sizeof(uint64_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            TestCustomerGetNode(subStmt, &root, &nodeA, &nodeV);
                            ReadSubCustomerByGet(root);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            EXPECT_EQ(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, "cid", &cid_value, sizeof(uint64_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            TestCustomerGetNode(subStmt, &root, &nodeA, &nodeV);
                            ReadSubCustomerByGet(root);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            EXPECT_EQ(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, "cid", &cid_value, sizeof(uint64_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            if (ret == GMERR_OK) {
                                TestCustomerGetNode(subStmt, &root, &nodeA, &nodeV);
                                ReadSubCustomerByGet(root);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_AGED: {
                            // key 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            EXPECT_EQ(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, "cid", &cid_value, sizeof(uint64_t), &isNull);
                            EXPECT_EQ(GMERR_OK, ret);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            TestCustomerGetNode(subStmt, &root, &nodeA, &nodeV);
                            ReadSubCustomerByGet(root);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("[%s]default: invalid eventType:%d  %d\r\n", __FUNCTION__, info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("[%s]default: invalid msgType: %d eventType: %d %d\r\n", __FUNCTION__, info->msgType,
                        info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                userData->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void ReadSubIp4fowardByGet(GmcStmtT *stmt)
{
    int ret = 0;
    bool isNull = false;
    uint32_t f2Value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &f2Value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &f2Value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
}

void SubCallbackWithSubIp4fowardNew(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *testUserData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    SnUserDataT *userData = (SnUserDataT *)testUserData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->msgType) {
                case 1: { // 推送old
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            ReadSubIp4fowardByGet(subStmt);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_AGED: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            ReadSubIp4fowardByGet(subStmt);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("[%s]default: invalid eventType:%d  %d\r\n", __FUNCTION__, info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }

                case 2: { // 推送new object
                    // 默认推送new object和old object
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ReadSubIp4fowardByGet(subStmt);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ReadSubIp4fowardByGet(subStmt);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            if (ret == GMERR_OK) {
                                ReadSubIp4fowardByGet(subStmt);
                            }

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("[%s]default: invalid eventType:%d  %d\r\n", __FUNCTION__, info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("[%s]default: invalid msgType: %d %d\r\n", __FUNCTION__, info->msgType, __LINE__);
                    break;
                }
            }
        }
        userData->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                userData->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void *TestSubThread(void *args)
{
    int ret = 0;
    char *subInfo = NULL;
    char *subInfo2 = NULL;
    int chanRingLen = 64;
    const char *subConnName = (const char*)"subKeyOld010";
    GmcStmtT *stmt_sub = NULL;
    GmcConnT *testSubConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *subName = (const char *)"subip4forwardnew010";
    const char *subName1 = (const char *)"customerkeyold010";
    SnUserDataT *userData1 = NULL;
    userData1 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (userData1 == NULL) {
        EXPECT_NE((void *)NULL, userData1);
    }
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    if (userData == NULL) {
        EXPECT_NE((void *)NULL, userData);
    }
    readJanssonFile("schemaFile/sub_customer_keyoldsub.gmjson", &subInfo);
    EXPECT_NE((void *)NULL, subInfo);
    readJanssonFile("schemaFile/sub_ip4forward_subnew.gmjson", &subInfo2);
    EXPECT_NE((void *)NULL, subInfo2);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2 * CYCLE_TIME; i++) {
        // 创建订阅连接
        ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
        EXPECT_EQ(GMERR_OK, ret);
        memset(userData1, 0, sizeof(SnUserDataT));
        memset(userData, 0, sizeof(SnUserDataT));

        // 订阅sub_ip4foward事件
        GmcSubConfigT tmpSubInfo;
        tmpSubInfo.subsName = subName;
        tmpSubInfo.configJson = subInfo2;
        ret = GmcSubscribe(stmt, &tmpSubInfo, testSubConn, SubCallbackWithSubIp4fowardNew, userData);
        EXPECT_EQ(GMERR_OK, ret);

        // 订阅customer
        tmpSubInfo.subsName = subName1;
        tmpSubInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmpSubInfo, testSubConn, SubCallbackWithCustomerKeyold, userData1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subName1);
        EXPECT_EQ(GMERR_OK, ret);
        // 释放订阅连接
        ret = testSubDisConnect(testSubConn, stmt_sub);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(userData1);
    free(userData);
    free(subInfo);
    free(subInfo2);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void SubWriteCustomerSetPropertyRoot(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint64_t cid = index;
    char *cname = (char *)"testxxx";
    bool married = index % 2;
    char *mail = (char *)"<EMAIL>";
    char *phone = (char *)"15837627600";
    uint64_t birthday = 54578749 + index % 2;
    uint8_t part = (index % 15);

    ret = GmcNodeSetPropertyByName(node, (char *)"cid", GMC_DATATYPE_UINT64, &cid, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"cname", GMC_DATATYPE_STRING, cname, strlen(cname));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"married", GMC_DATATYPE_BOOL, &married, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"mail", GMC_DATATYPE_STRING, mail, strlen(mail));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"birthday", GMC_DATATYPE_TIME, &birthday, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"phone", GMC_DATATYPE_FIXED, phone, CUSTOMER_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"part", GMC_DATATYPE_PARTITION, &part, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void SubWriteCustomerSetPropertyArray(GmcNodeT *node, uint32_t index, char *stringV)
{
    int ret = 0;
    int32_t oid = index;

    ret = GmcNodeSetPropertyByName(node, (char *)"oid", GMC_DATATYPE_INT32, &oid, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"oname", GMC_DATATYPE_STRING, stringV, strlen(stringV));
    ASSERT_EQ(GMERR_OK, ret);
}

void SubWriteCustomerSetPropertyVector(GmcNodeT *node, uint32_t index, char *stringV, char *bytesV)
{
    int ret = 0;
    int32_t oid = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"oid", GMC_DATATYPE_INT32, &oid, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"oname", GMC_DATATYPE_STRING, stringV, strlen(stringV));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"adress", GMC_DATATYPE_BYTES, bytesV, strlen(bytesV));
    ASSERT_EQ(GMERR_OK, ret);
}

void SubWriteCustomerInsert(
    GmcStmtT *stmt, uint32_t startPk, uint32_t endPk, int32_t arrayNum, int32_t vectorNum)
{
    int ret = 0;
    GmcNodeT *root = NULL, *nodeA = NULL, *nodeV = NULL;
    for (uint32_t count = startPk; count < endPk; count++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_customerName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestCustomerGetNode(stmt, &root, &nodeA, &nodeV);
        SubWriteCustomerSetPropertyRoot(root, count);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            ret = GmcNodeAppendElement(nodeA, &nodeA);
            EXPECT_EQ(GMERR_OK, ret);
            SubWriteCustomerSetPropertyArray(nodeA, j + count, (char *)"tx");
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(nodeV, &nodeV);
            EXPECT_EQ(GMERR_OK, ret);
            SubWriteCustomerSetPropertyVector(
                nodeV, j + count, (char *)"tx", (char *)"guangdongshenzhenlonggangbantain");
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_UNIQUE_VIOLATION &&
            ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

void SubWriteCustomerSetPkIndex(GmcStmtT *stmt, uint32_t pkVal)
{
    int ret = 0;
    uint64_t cid = pkVal;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &cid, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_customerPk);
    EXPECT_EQ(GMERR_OK, ret);
}
void SubWriteCustomerUpdateSetProperty(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    char *cname = (char *)"update_testxxx";
    bool married = (index + 1) % 2;

    ret = GmcNodeSetPropertyByName(node, (char *)"cname", GMC_DATATYPE_STRING, cname, strlen(cname));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"married", GMC_DATATYPE_BOOL, &married, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
}

void SubWriteCustomerUpdate(GmcStmtT *stmt, uint32_t startPk, uint32_t endPk)
{
    int ret = 0;
    GmcNodeT *root = NULL, *nodeA = NULL, *nodeV = NULL;
    for (uint32_t count = startPk; count < endPk; count++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_customerName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        SubWriteCustomerSetPkIndex(stmt, count);
        TestCustomerGetNode(stmt, &root, &nodeA, &nodeV);
        SubWriteCustomerUpdateSetProperty(root, count);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_INSUFFICIENT_RESOURCES &&
            ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

void *TestCustomerThread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = 0;
    uint32_t endC = 100;
    if (args) {
        endC = *(uint32_t *)args;
    }
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    int32_t arrayNum = 3;
    int32_t vectorNum = 3;
    uint32_t startNum = 0;
    uint32_t endNum = 0;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int j = 0; j < CYCLE_TIME; j++) {
        // 插入
        SubWriteCustomerInsert(syncStmt, startC, (endC + startC), arrayNum, vectorNum);
        // 更新
        SubWriteCustomerUpdate(syncStmt, startC, (endC / 2 + startC));
        // 对账老化
        bool isAbnormal = false;
        for (int32_t i = 0; i < 16; i++) {
            uint8_t id = i;
            ret = GmcBeginCheck(syncStmt, g_customerName, id);
            if (ret == GMERR_OK) {
                do {
                    ret = GmcEndCheck(syncStmt, g_customerName, id, isAbnormal);
                    if (ret == GMERR_OK) {
                        break;
                    } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                        usleep(10);
                    } else {
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                } while (ret == GMERR_LOCK_NOT_AVAILABLE);
            } else if (ret != GMERR_LOCK_NOT_AVAILABLE) {
                printf("labelName:%s GmcBeginCheck failed line:%d\n", g_customerName, __LINE__);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void SubWriteIp4forwardSetPorperty(GmcStmtT *stmt, uint32_t index)
{
    int32_t ret = 0;
    uint32_t vr_id = index % 16;
    uint32_t vrf_index = 0;
    uint32_t destIpAddr = (index + 2) << 8;
    uint8_t maskLen = (28) & 0xff;
    uint8_t nhp_group_flag = index % 2;
    uint16_t qos_profile_id = index + 10;
    uint32_t primary_label = index + 100;
    uint32_t attribute_id = index + 666;
    uint32_t nhp_group_id = index % 8;
    uint32_t route_flags = index + 888;
    uint32_t flags = index + 10010;

    ret = GmcSetVertexProperty(stmt, (char *)"vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"dest_ip_addr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"nhp_group_flag", GMC_DATATYPE_UINT8, &nhp_group_flag, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"qos_profile_id", GMC_DATATYPE_UINT16, &qos_profile_id, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"primary_label", GMC_DATATYPE_UINT32, &primary_label, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"attribute_id", GMC_DATATYPE_UINT32, &attribute_id, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"nhp_group_id", GMC_DATATYPE_UINT32, &nhp_group_id, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"route_flags", GMC_DATATYPE_UINT32, &route_flags, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"flags", GMC_DATATYPE_UINT32, &flags, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void SubWriteIp4forwardInsert(GmcStmtT *stmt, uint32_t startPk, uint32_t endPk)
{
    int32_t ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_subip4fowardName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = startPk; i < endPk; i++) {
        SubWriteIp4forwardSetPorperty(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_SUB_PUSH_QUEUE_FULL && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_UNIQUE_VIOLATION &&
            ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_INSUFFICIENT_RESOURCES) {
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

void SubWriteIp4forwardSetPkIndext(GmcStmtT *stmt, uint32_t pkVal)
{
    int ret = 0;
    uint32_t vr_id = pkVal % 16;
    uint32_t vrf_index = 0;
    uint32_t destIpAddr = (pkVal + 2) << 8;
    uint8_t maskLen = (28) & 0xff;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_subIp4fowardPk);
    EXPECT_EQ(GMERR_OK, ret);
}

void SubWriteIp4forwardUpdateSetProperty(GmcStmtT *stmt, uint32_t value)
{
    int ret = 0;
    uint32_t primary_label = value + 666;
    uint32_t attribute_id = value + 888;

    ret = GmcSetVertexProperty(stmt, (char *)"primary_label", GMC_DATATYPE_UINT32, &primary_label, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"attribute_id", GMC_DATATYPE_UINT32, &attribute_id, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

// 更新
void SubWriteIp4forwardUpdate(GmcStmtT *stmt, uint32_t startPk, uint32_t endPk)
{
    int32_t ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_subip4fowardName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startPk; i < endPk; i++) {
        SubWriteIp4forwardSetPkIndext(stmt, i);
        SubWriteIp4forwardUpdateSetProperty(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_UNIQUE_VIOLATION &&
            ret != GMERR_INSUFFICIENT_RESOURCES && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

int CheckAccountStatus(GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    return ret;
}

void TestAgeCheck(GmcStmtT *stmt, const char *labelName, uint8_t partitionId, bool isAbnormal)
{
    int ret = 0;
    ret = GmcBeginCheck(stmt, labelName, partitionId);
    if (ret == GMERR_OK) {
        do {
            ret = GmcEndCheck(stmt, labelName, partitionId, isAbnormal);
            if (ret == GMERR_OK) {
                break;
            } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                usleep(100);
            } else {
                EXPECT_EQ(GMERR_OK, ret);
            }
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        if (ret == GMERR_OK) {
            ret = CheckAccountStatus(stmt, labelName);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (ret != GMERR_LOCK_NOT_AVAILABLE) {
        testGmcGetLastError(NULL);
        printf("labelName: %s GmcBeginCheck faile\n", labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *TestIp4forwardThread(void *args)
{
    int32_t ret = 0;
    uint32_t startC = 0;
    uint32_t endC = 100;
    if (args) {
        endC = *(uint32_t *)args;
    }
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < CYCLE_TIME; i++) {
        // 写
        SubWriteIp4forwardInsert(syncStmt, startC, (endC + startC));
        // update
        SubWriteIp4forwardUpdate(syncStmt, startC, (endC / 2 + startC));

        // 对账老化
        bool isAbnormal = false;
        TestAgeCheck(syncStmt, g_subip4fowardName, 0xff, isAbnormal);
    }
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/**-------------------DTS2022050901939-----------------------**/
void TestCreateLabelIf32k()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *testSchema = NULL;

    readJanssonFile("./schemaFile/if_general_complex_32k.gmjson", &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 先删掉表
    ret=GmcDropVertexLabel(stmt, g_if32kName);
    ret = GmcCreateVertexLabel(stmt, testSchema, g_testLabelconfig);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create label %s success \n", g_if32kName);
    } else {
        testGmcGetLastError(NULL);
    }
    free(testSchema);
    testGmcDisconnect(conn, stmt);
}

// 获取根节点以及非根节点下非嵌套的vector节点
void TestGetRootAndChild32k(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT**T1, GmcNodeT**T2)
{
    GmcNodeT *Root = NULL, *t1 = NULL, *t2 = NULL;

    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *T1 = t1;
    *T2 = t2;
}

void TestSetPrimaryIf32k(GmcNodeT *node, int index)
{
    int ret = 0;
    uint32_t f1Value = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_FIXED, (int8_t*)"fixeds", FIXED_F2_SIZE_32K);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetPropertyIf32k(GmcNodeT *node, int index, int8_t* fixedValue, int8_t* fixedValue1,
                          char* strF8, int8_t* bytesF9)
{
    int ret = 0;
    uint32_t uint32Value = index;
    uint32_t uint32Value2 = index % 5000;
    uint64_t timeValue = 12345678;

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT32, &uint32Value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_FIXED, fixedValue, FIXED_F6_SIZE_32K);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_FIXED, fixedValue1, FIXED_F7_SIZE_32K);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_STRING, strF8, strlen(strF8));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BYTES, bytesF9, BYTES_F9_SIZE_32K);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_TIME, &timeValue, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetRecordPropertyIf32k(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t uint32Value = index;

    ret = GmcNodeSetPropertyByName(node, (char *)"R1", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"R2", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"R3", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"R4", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void Test32KSetVectorProperty(GmcNodeT *node, int t2ItemStart, int t2ItemEnd)
{
    int ret = 0;
    for (int i = t2ItemStart; i < t2ItemEnd; i++) {
        uint32_t uint32Value = i;
        ret = GmcNodeAppendElement(node, &node);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void Test32KWriteDate(GmcStmtT *stmt, int startPk, int endPk, int t2ItemStart, int t2ItemEnd)
{
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL;

    char *fixedValue = (char*)malloc(2048 * sizeof(char));
    if (fixedValue == NULL) {
        EXPECT_NE((void *)NULL, fixedValue);
    }
    memset(fixedValue, 'a', 2047);
    fixedValue[2047] = '\0';

    char *fixedValue1 = (char*)malloc(10240 * sizeof(char));
    if (fixedValue1 == NULL) {
        EXPECT_NE((void *)NULL, fixedValue1);
    }
    memset(fixedValue1, 'a', 10239);
    fixedValue1[10239] = '\0';

    char *strF8 = (char*)malloc(10240 * sizeof(char));
    if (strF8 == NULL) {
        EXPECT_NE((void *)NULL, strF8);
    }
    memset(strF8, 'a', sizeof(char) * (10240 - 1));
    strF8[10239] = '\0';

    char *bytesF9 = (char*)malloc(10240 * sizeof(char));
    if (bytesF9 == NULL) {
        EXPECT_NE((void *)NULL, bytesF9);
    }
    memset(bytesF9, 'a', sizeof(char) * (10240 - 1));
    bytesF9[10239] = '\0';

    for (int loop = startPk; loop < endPk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_if32kName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        TestGetRootAndChild32k(stmt, &root, &T1, &T2);
        TestSetPrimaryIf32k(root, loop);
        TestSetPropertyIf32k(root, loop, (int8_t*)fixedValue, (int8_t*)fixedValue1, strF8, (int8_t*)bytesF9);
        TestSetRecordPropertyIf32k(T1, loop);
        Test32KSetVectorProperty(T2, t2ItemStart, t2ItemEnd);
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        }
    }
    free(fixedValue);
    free(fixedValue1);
    free(strF8);
    free(bytesF9);
}

void Test32KPrepareWrite(int startPk, int endPk)
{
    int ret = 0;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    Test32KWriteDate(syncStmt, startPk, endPk, 0, 3);
    testGmcDisconnect(syncConn, syncStmt);
}

void IfGeneralComplex32KSetPkIndex(GmcStmtT *stmt, int32_t pkValue, int8_t *fixedValue)
{
    int ret = 0;
    uint32_t keyValue = pkValue;
    ret = GmcSetIndexKeyName(stmt, g_if32kPk);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, fixedValue, FIXED_F2_SIZE_32K);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestIfGeneralComplex32KUpdateProperty(GmcNodeT *node, int32_t value)
{
    int ret = 0;
    uint32_t uint32Value = (value + 66) % 5000;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void Test32KPkUpdate(GmcStmtT *stmt, int startPk, int endPk)
{
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL;
    for (int loop = startPk; loop < endPk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_if32kName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TestGetRootAndChild32k(stmt, &root, &T1, &T2);
        TestIfGeneralComplex32KUpdateProperty(root, loop);
        IfGeneralComplex32KSetPkIndex(stmt, loop, (int8_t*)"fixeds");
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        }
    }
}

void Test32KPkDelete(GmcStmtT *stmt, int startPk, int endPk)
{
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL;

    for (int loop = startPk; loop < endPk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_if32kName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        IfGeneralComplex32KSetPkIndex(stmt, loop, (int8_t*)"fixeds");
        ret = GmcExecute(stmt);
        ret = TestExcuteTime(stmt, ret);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            usleep(10);
        }
    }
}

// 随机产生一个【0,200】数值a，对 【a，a+10]】间的数据进行：主键写--主键更新--主键删除操作
// 同步写
void* RecycleWriteRemoveIfGeneralComplex32k(void* args)
{
    int ret = 0;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    int index = rand() % (GRAPH_WRITE_NUM / 100);  // 随机产生一个10的区间进行写，更新，删除
    // write
    Test32KWriteDate(syncStmt, index, index + 10, 0, 3);
    // update
    Test32KPkUpdate(syncStmt, index, index + 10);
    // remove
    Test32KPkDelete(syncStmt, index, index + 10);
    testGmcDisconnect(syncConn, syncStmt);
    return NULL;
}

// 使用命令读取STORAGE_HEAP_STAT的PAGE_COUNT页
void GmsysviewReadHeapPageCount(int *pageCount, int *curItemNum)
{
    char command[256] = {0};
    uint64_t length = 0;
    char tmpBuff[512] = {0};
    char str[2][512];
    int ret = 0;
    memset(command, 0, sizeof(command));
    memset(tmpBuff, 0, sizeof(tmpBuff));
    memset(str, 0, sizeof(str));
    char const *viewname = "V\\$STORAGE_HEAP_STAT";
    (void)snprintf(command, 256, "%s/gmsysview -q %s -f LABEL_NAME=%s", g_toolPath, viewname, g_if32kName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        ASSERT_EQ(GMERR_OK, 1);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "PAGE_COUNT:") == 0) {
            *pageCount = atoi(str[1]);
        } else if (strcmp(str[0], "CUR_ITEM_NUM:") == 0) {
            *curItemNum = atoi(str[1]);
            break;
        }
    }
    ret = pclose(pf);
    if (ret != 0) {
        printf("pclose(%d) error.\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/**-------------------DTS2022042815636-----------------------**/
void TestUpdateSetProperty8k(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t f9Value = (i + 66) % 5000;
    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_UINT32, &f9Value, sizeof(uint32_t));
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char *fixedValue15Update = (char *)malloc(IP4FORWARD_8K_F15_LEN * sizeof(char));
    if (fixedValue15Update == NULL) {
        EXPECT_NE((void *)NULL, fixedValue15Update);
    }
    memset(fixedValue15Update, 'b', IP4FORWARD_8K_F15_LEN - 1);
    fixedValue15Update[IP4FORWARD_8K_F15_LEN - 1] = '\0';
    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_FIXED, fixedValue15Update, IP4FORWARD_8K_F15_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_FIXED, fixedValue15Update, IP4FORWARD_8K_F16_LEN);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(fixedValue15Update);
}

// 随机产生一个【0,5k】的数a，【a,a+20】的区间进行localkey等值的批量更新--等值批量删除
void *LocalkeyBatchUpdateDelete8k(void *args)
{
    int ret = 0;
    bool isFinish = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t countN = IP4FORWARD_8K_NUM * 2;
    char *f1Value = (char *)"fixed";
    uint32_t successNum = 0, totalNum = 0;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        char *fixedValue15 = (char *)malloc(IP4FORWARD_8K_F15_LEN * sizeof(char));
        if (fixedValue15 == NULL) {
            EXPECT_NE((void *)NULL, fixedValue15);
        }
        memset(fixedValue15, 'a', IP4FORWARD_8K_F15_LEN - 1);
        fixedValue15[IP4FORWARD_8K_F15_LEN - 1] = '\0';
        int start = rand() % (countN / 8);
        int end = 20;
        // 批量写
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4foward8k, GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        for (int loop = start; loop <= (start + end); loop++) {
            TestSetVertexPropertyByName8k(stmt, loop, f1Value, fixedValue15);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcResetStmt(stmt);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret == GMERR_OK || ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_UNIQUE_VIOLATION ||
            ret == GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, 0);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchReset(batch);
        // 批量更新
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4foward8k, GMC_OPERATION_UPDATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int loop = start; loop <= (start + end); loop++) {
            uint32_t f8Value = loop;
            ret = GmcSetIndexKeyName(stmt, g_ip4foward8kLocalKey);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, f1Value, IP4FORWARD_8K_F7_LEN);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f8Value, sizeof(uint32_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TestUpdateSetProperty8k(stmt, loop + 100);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcResetStmt(stmt);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcBatchReset(batch);
        // 批量删除
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameIp4foward8k, GMC_OPERATION_DELETE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        for (int loop = start; loop <= (start + end); loop++) {
            uint32_t f8Value = loop;
            ret = GmcSetIndexKeyName(stmt, g_ip4foward8kLocalKey);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, f1Value, IP4FORWARD_8K_F7_LEN);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f8Value, sizeof(uint32_t));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            GmcResetStmt(stmt);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(fixedValue15);
        GmcBatchReset(batch);
    }
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return NULL;
}

// 对表数据进行老化
void *AgingThreadIp4foward8k(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        TestAgeCheck(stmt, g_labelNameIp4foward8k, 0xff, isAbnormal);
        sleep(1);
    }
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return NULL;
}
/**-------------------DTS2022042703706-----------------------**/

void IfStabilitySetLocalhashIndex(GmcStmtT *stmt, void *fixedValue)
{
    int ret = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, fixedValue, IF_TYPE_FIXED_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_ifLocalhashKey);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void TestReadifStabilityByGetRoot(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = false;
    char fixedValue[IF_TYPE_FIXED_LEN + 1] = {0};
    uint32_t f2Value = 0;
    ret = GmcNodeGetPropertyByName(node, "vrid", &f2Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "name", fixedValue, IF_TYPE_FIXED_LEN, &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void TestReadifStabilityByGetIfm(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = false;
    char fixedValue[IF_TYPE_FIXED_LEN + 1] = {0};
    uint32_t f2Value = 0;
    uint32_t f3Value = 0;
    uint32_t f4Value = 0;
    uint32_t f8Value = 0;
    uint32_t f9Value = 0;
    ret = GmcNodeGetPropertyByName(node, "simple_name", &f2Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "description", fixedValue, IF_TYPE_FIXED_LEN, &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "is_configure", &f3Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "main_ifindex", &f4Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "sub_max_num", &f9Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "sub_curr_num", &f8Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "error_down", &f8Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "statistic", &f8Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "vsys_id", &f9Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "zone_id", &f8Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "last_up_time", &f8Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "last_down_time", &f8Value, sizeof(uint32_t), &isNull);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void *LocalhashScanThreadIfGeneralComplex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    uint32_t countN = 0;

    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        int loop = i;
        ret = testGmcPrepareStmtByLabelName(stmt, g_ifStabilityName, GMC_OPERATION_SCAN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        IfStabilitySetLocalhashIndex(stmt, (int8_t *)"field");
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            ret = GmcFetch(stmt, &isFinish);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            while (!isFinish) {
                TestIfStabilityGetNode(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
                TestReadifStabilityByGetRoot(root);
                TestReadifStabilityByGetIfm(ifmN);
                ret = GmcFetch(stmt, &isFinish);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    return NULL;
}

void SysviewReadComDynCtx()
{
    char command[256] = {0};
    uint64_t length = 0;
    char tmpBuff[512] = {0};
    char str[2][512];
    int ret = 0;
    memset(command, 0, sizeof(command));
    memset(tmpBuff, 0, sizeof(tmpBuff));
    memset(str, 0, sizeof(str));
    char const *viewname = "V\\$COM_DYN_CTX";
    (void)snprintf(command, 256, "%s/gmsysview -q %s -l 1", g_toolPath, viewname);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        ASSERT_EQ(GMERR_OK, 1);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "CTX_LEVEL:") == 0) {
            if (strcmp(str[1], "null") == 0) {
                printf("[error], gmsysview COM_DYN_CTX Failed\n");
                ASSERT_EQ(GMERR_OK, 1);
            }
        }
    }
    ret = pclose(pf);
    if (ret != 0) {
        printf("pclose(%d) error.\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *SysviewReadComDynCtxThread(void *args)
{
    for (int i = 0; i < 20; i++) {
        SysviewReadComDynCtx();
    }
    return NULL;
}


#endif  // _QUESTION_VARIATION_SDV_H_
