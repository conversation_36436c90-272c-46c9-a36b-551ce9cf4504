//
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

GmcConnT *g_connSync = NULL, *g_conn = NULL, *g_conn_sub = NULL;  // conn 句柄
GmcStmtT *g_stmtSync = NULL, *g_stmt = NULL, *g_stmt_sub = NULL;  // stmt 句柄
#define SYS_VIEW 1
#define SYS_CHECK 0
#define DELAY_S 5
int ret = 0;
const char *Tree_Label_Name = "Tree_fileds_time";
const char *Normal_Label_Name = "normal_fileds_bitmap";
const char *Normal_sub_Name = "normal_sub";
const char *MS_config = "{\"max_record_num\" : 5000000000,\"defragmentation\":true }";
//["disable_sub_back_pressure": (true|false),]
const char *MS_config2 = "{\"max_record_num\" : 5000000000,\"disable_sub_back_pressure\":true }";
int g_start_num = 0;
int g_end_num = 1000;
int g_array_num = 3;
int g_vector_num = 3;

char *g_sub_info = NULL;
int g_subIndex = 0;
const char *g_subConnName = "subConnName";
const char *g_subName = "normal_sub_info";

#define MAX_NAME_LENGTH 128
typedef enum tagRunMode { MODE_EULER = 0, MODE_DAP = 1, MODE_HONGMENG = 2 } GtRunModeE;

#define COMPARE_NE(expectValue, actual_value)                                \
    do {                                                                      \
        if ((expectValue) == (actual_value)) {                               \
            printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);       \
            printf("Value of: " #actual_value " = %p\n", (actual_value));     \
            printf("Not Expected: " #expectValue " = %p\n", (expectValue)); \
            return -1;                                                        \
        };                                                                    \
    } while (0)

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    ret = 0;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *keyValue = 0, *connSync = 0, *stmtSync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            // ret = GmcGetSubPushVertexLabelName(subStmt, i, labelName, &labelNameLen);
            // EXPECT_EQ(GMERR_OK, ret);
            // EXPECT_EQ(strlen(labelName), labelNameLen);

            //Ĭ������new object��old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //��new
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);

                    //��old
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                    // EXPECT_EQ(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //��new
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, false);
                    // // EXPECT_EQ(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    //��old
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //��new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);

                    //��old
                    // ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //��new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);

                    //��old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        // ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        // ret = GmcIncSubSetFetchVertexLabel(subStmt, true);
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[g_subIndex];
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: invalid GMC_SUB_TYPE \r\n");
                break;
            }
        }
    }
}

void testCreateLabelMS(GmcStmtT *stmt)
{
    char *Tree_schema = NULL;
    char *Normal_schema = NULL;
    readJanssonFile("schema_file/tree_fileds_time.gmjson", &Tree_schema);
    ASSERT_NE((void *)NULL, Tree_schema);
    ret = GmcCreateVertexLabel(stmt, Tree_schema, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/normal_fileds_bitmap.gmjson", &Normal_schema);
    ASSERT_NE((void *)NULL, Normal_schema);
    ret = GmcCreateVertexLabel(stmt, Normal_schema, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(Normal_schema);
    free(Tree_schema);
}

void testDropLabelMS(GmcStmtT *stmt)
{
    ret = GmcDropVertexLabel(stmt, Normal_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, Tree_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
}

int findLog(char *ecpectLog)
{
    ret = 0;
    char string[300];
    FILE *fp;
    char *actual = (char *)"temp.log";
    char *expect = (char *)"expect.log";

    memset(string, 0, 100);
    if (g_runMode == MODE_EULER) {
        sprintf(string, "cat ${TEST_HOME}/log/run/rgmserver/* |grep \"%s\" > temp.log", ecpectLog);
        ret = system(string);
        EXPECT_EQ(GMERR_OK, ret);
        char *actual_value = NULL;
        ret = readJanssonFile(actual, &actual_value);
        COMPARE_NE((char *)NULL, actual_value);
        char *ter;
        ter = strstr(actual_value, ecpectLog);
        free(actual_value);
        if (ter != NULL) {
            printf("find %s keywords:%d\n", ecpectLog, ret);
            return 0;
        } else {
            return 666;  //表示没有找到日志内容
        }
        system("cat /dev/null > ${TEST_HOME}/log/run/rgmserver/rgmserver.log");
    } else if (g_runMode == MODE_DAP) {
        sprintf(string, "cat /var/log/hlog/err/hloglog_err.csv |grep \"%s\" > temp.log", ecpectLog);
        ret = system(string);
        EXPECT_EQ(GMERR_OK, ret);
        char *actual_value = NULL;
        ret = readJanssonFile(actual, &actual_value);
        COMPARE_NE((char *)NULL, actual_value);
        char *ter;
        ter = strstr(actual_value, ecpectLog);
        free(actual_value);
        if (ter != NULL) {
            printf("find %s keywords:%d\n", ecpectLog, ret);
            return 0;
        } else {
            return 666;  //表示失败
        }
    }
    return GMERR_OK;
}
void clearlog()
{
    if (g_runMode == MODE_EULER) {
        system("cat /dev/null > log/run/rgmserver/rgmserver.log");
    } else {
        system("rm -rf /var/log/hlog/info/hloglog.csv");
    }
}

int executeCmd(char *cmd, char *v1 = NULL)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("[executeCmd] popen(%s) error.\n", cmd);
        return -1;
    }
    int len = 0;
    char cmdOutput[4096] = {0};
    while (NULL != fgets(cmdOutput + len, 4000 - len, pf)) {
        len = strlen(cmdOutput);
    }
    pclose(pf);
    if (v1) {
        snprintf(v1, strlen(cmdOutput), "%s", cmdOutput);
    }
    return 0;
}

int CheckAlarmNum(char *fileName)
{
    char alarmNum[64];
    char cmd[80];
    int length = strlen(fileName);
    int res;
    res = snprintf(cmd, 33 + length, "cat ./%s |grep 'Alarm Type' |wc -l", fileName);
    if (res != 0) {
        printf("snprintf error \n");
        return -1;
    }
    printf("check alarm num cmd is:%s\n", cmd);
    executeCmd(cmd, (char *)alarmNum);
    int num = atoi(alarmNum);
    return num;
}

int check_alarm_sysview_local_2(bool none_status = true, bool middlle_status = false, bool cleared_value = false,
    bool alarm_activeThreshold = false, bool alarm_clearedThreshold = false, bool alarm_successTimes = false,
    bool alarm_failTimes = false, bool alarm_detail_info = false, char *crn_check_value = (char *)"EMPTY",
    char *src_check_value = (char *)"SERVER", char *act_check_value = (char *)"0.00",
    char *middle_check_value = (char *)"EMPTY", char *cleared_check_value = (char *)"0.00",
    char *act_Check_Threshold = (char *)"0.90", char *cle_check_threshold = (char *)"0.90",
    char *suc_check_value = (char *)"0", char *fai_check_value = (char *)"0", float check_error_float = 0.03,
    int check_error_int = 20)
{
    int ret = 0;
    system("wc -c ./check.txt >size.txt");
    system("grep -F '0 ./check.txt' ./size.txt >size_check.txt");
    char *expectValue = NULL;
    ret = readJanssonFile((char *)"size_check.txt", &expectValue);
    int length2 = strlen(expectValue);
    char aa[80] = "0 ./check.txt";
    ret = strncmp(expectValue, (char *)aa, length2 - 1);
    printf("actual size is:%s\n", expectValue);
    system("rm -r -f size_check.txt");
    memset(aa, 0, 80);
    if (!ret) {  // 检验文件是否为空，如果为空，且none_status为1则不报错
        if (none_status) {
            printf("EXPECT NO ALARM INFO \n");
        } else if (none_status) {
            EXPECT_EQ(1, 0);
            printf("NO ALARM INFO BY GMSYSVIEW\n");
        }
    }
    FILE *pf = fopen("./check.txt", "r");
    if (pf == NULL) {
        return -1;
        EXPECT_EQ(-1, 0);
        printf("OPEN FILED \n");
    }
    free(expectValue);
    int length;
    char source[512];
    char middleStatus[512];
    char currentStatus[512];
    float activeValue = 0;
    float checkClearedValue = 0;
    float activeThreshold = 0;
    float clearedThreshold = 0;
    int successTimes = 0;
    int failTimes = 0;
    int size = 0;
    char tmpBuff[512];
    char str[6][512];
    int res;
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        if (tmpBuff[0] == '#') {
            continue;
        }
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        res = sscanf(tmpBuff, "%s %s", str[3], str[5]);
        if (res != 0) {
            printf("sscanf error \n");
            return -1;
        }
        if (str[3][0] == ' ' || str[5][0] == '\0') {
            continue;
        }
        if (strcmp(str[3], "alarm_source:") == 0) {
            memset(aa, 0, 80);
            length = strlen(src_check_value);
            strcpy(source, str[5]);
            printf("actual alarm_source is :%s\n", source);
            printf("expect alarm_source is :%s\n", src_check_value);
            ret = strncmp(source, src_check_value, length - 1);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (strcmp(str[3], "alarm_middleStatus:") == 0) {
            memset(aa, 0, 80);
            length = strlen(middleStatus);
            strcpy(middleStatus, str[5]);
            printf("actual alarm_middleStatus is :%s\n", middleStatus);
            printf("expect alarm_middleStatus is :%s\n", middle_check_value);
            if (middlle_status) {
                ret = strncmp(middleStatus, middle_check_value, length - 1);
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (strcmp(str[3], "alarm_currentStatus:") == 0) {
            memset(aa, 0, 80);
            length = strlen(crn_check_value);
            strcpy(currentStatus, str[5]);
            printf("actual alarm_currentStatus is :%s\n", currentStatus);
            printf("expect alarm_currentStatus is :%s\n", crn_check_value);
            ret = strncmp(currentStatus, crn_check_value, length - 1);
            EXPECT_EQ(GMERR_OK, ret);
        }

        res = sscanf(tmpBuff, "%s %s %s", str[0], str[1], str[2]);
        if (res != 0) {
            printf("sscanf error \n");
            return -1;
        }
        if (str[2][0] == ' ' || str[2][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "alarm_activeValue") == 0) {
            memset(aa, 0, 80);
            length = strlen(act_check_value);
            activeValue = atof(str[2]);
            float actCheckValue2 = atof(act_check_value);
            printf("actual alarm_activeValue is :%.2f\n", activeValue);
            printf("expect alarm_activeValue is :%.2f\n", actCheckValue2);
            if (activeValue > actCheckValue2 - check_error_float &&
                activeValue < actCheckValue2 + check_error_float) {
                EXPECT_EQ(0, 0);
            } else {
                EXPECT_EQ(0, 1);
            }
        } else if (strcmp(str[0], "alarm_cleared_value") == 0) {
            memset(aa, 0, 80);
            length = strlen(cleared_check_value);
            checkClearedValue = atof(str[2]);
            float clearedCheckValue2 = atof(cleared_check_value);
            printf("actual alarm_cleared_value is :%.2f\n", checkClearedValue);
            printf("expect alarm_cleared_value is :%.2f\n", clearedCheckValue2);
            if (cleared_value) {
                if (checkClearedValue > clearedCheckValue2 - check_error_float &&
                    checkClearedValue < clearedCheckValue2 + check_error_float) {
                    EXPECT_EQ(0, 0);
                } else {
                    EXPECT_EQ(0, 1);
                }
            }
        } else if (strcmp(str[0], "alarm_activeThreshold") == 0) {
            memset(aa, 0, 80);
            length = strlen(act_Check_Threshold);
            activeThreshold = atof(str[2]);
            float actCheckThreshold2 = atof(act_Check_Threshold);
            printf("actual alarm_activeThreshold is :%.2f\n", activeThreshold);
            printf("expect alarm_activeThreshold is :%.2f\n", actCheckThreshold2);
            if (alarm_activeThreshold) {
                if (activeThreshold > actCheckThreshold2 - check_error_float &&
                    activeThreshold < actCheckThreshold2 + check_error_float) {
                    EXPECT_EQ(0, 0);
                } else {
                    EXPECT_EQ(0, 1);
                }
            }
        } else if (strcmp(str[0], "alarm_clearedThreshold") == 0) {
            memset(aa, 0, 80);
            length = strlen(cle_check_threshold);
            clearedThreshold = atof(str[2]);
            float cleCheckThreshold2 = atof(cle_check_threshold);
            printf("actual alarm_activeThreshold is :%.2f\n", clearedThreshold);
            printf("expect alarm_activeThreshold is :%.2f\n", cleCheckThreshold2);
            if (alarm_clearedThreshold) {
                if (clearedThreshold > cleCheckThreshold2 - check_error_float &&
                    clearedThreshold < cleCheckThreshold2 + check_error_float) {
                    EXPECT_EQ(0, 0);
                } else {
                    EXPECT_EQ(0, 1);
                }
            }
        } else if (strcmp(str[0], "alarm_successTimes") == 0) {
            memset(aa, 0, 80);
            length = strlen(suc_check_value);
            successTimes = atoi(str[2]);
            int sucCheckValue2 = atoi(suc_check_value);
            printf("actual alarm_successTimes is :%d\n", successTimes);
            printf("expect alarm_successTimes is :%d\n", sucCheckValue2);
            if (alarm_successTimes) {
                if (successTimes >= sucCheckValue2 - check_error_int &&
                    successTimes <= sucCheckValue2 + check_error_int) {
                    EXPECT_EQ(0, 0);
                } else {
                    EXPECT_EQ(0, 1);
                }
            }
        } else if (strcmp(str[0], "alarm_failTimes") == 0) {
            memset(aa, 0, 80);
            length = strlen(fai_check_value);
            failTimes = atoi(str[2]);
            int faiCheckValue2 = atoi(fai_check_value);
            printf("actual alarm_failTimes is :%d\n", failTimes);
            printf("expect alarm_failTimes is :%d\n", faiCheckValue2);
            if (alarm_failTimes) {
                if (failTimes >= faiCheckValue2 - check_error_int &&
                    failTimes <= faiCheckValue2 + check_error_int) {
                    EXPECT_EQ(0, 0);
                } else {
                    EXPECT_EQ(0, 1);
                }
            }
        } else {
            printf("[getSysConfig] unknow parameter: %s.\n", tmpBuff);
        }
    }
    res = fclose(pf);
    if (res != 0) {
        printf("fclose error \n");
        return -1;
    }
    return GMERR_OK;
}

void test_setVertexPK(GmcStmtT *stmt, int index)
{
    ret = 0;
    uint32_t value7 = index;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_setVertexProperty(GmcStmtT *stmt, int index)
{
    ret = 0;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = index;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = index;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = index;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = index;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)index;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + index;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = index;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = index;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = index;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = index;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = index;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void test_checkVertexProperty_sub(GmcStmtT *stmt, int index)
{
    ret = 0;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = index;
    uint8_t value3 = index;
    int16_t value4 = index;
    uint16_t value5 = index;
    int32_t value6 = index;
    uint32_t value7 = index;  // F7是pk
    bool value8 = false;
    int64_t value9 = index;
    uint64_t value10 = index;
    float value11 = (float)1.2 + (float)index;
    double value12 = 10.86 + index;
    uint64_t value13 = index;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    uint32_t value17 = index;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback_simple_sleep(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    ret = 0;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *connSync = 0, *stmtSync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    sleep(DELAY_S);
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[g_subIndex];
                        test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: invalid GMC_SUB_TYPE \r\n");
                break;
            }
        }
    }
}

void sn_callback_simple_sleep2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *connSync = 0, *stmtSync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    sleep(DELAY_S);
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[g_subIndex];
                        test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
