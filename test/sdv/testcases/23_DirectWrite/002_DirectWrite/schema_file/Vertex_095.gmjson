[{"version": "2.0", "schema_version": 1, "type": "record", "name": "Vertex_095", "comment": "vertexlabel_1", "config": {"check_validity": true}, "max_record_count": 100000, "fields": [{"name": "Vertex_095_0", "type": "char", "nullable": true}, {"name": "Vertex_095_1", "type": "uchar", "nullable": true}, {"name": "Vertex_095_2", "type": "int8", "nullable": true}, {"name": "Vertex_095_3", "type": "uint8", "default": 100}, {"name": "Vertex_095_4", "type": "int16", "default": 10}, {"name": "Vertex_095_5", "type": "uint16", "default": 100}, {"name": "Vertex_095_6", "type": "int32", "default": 10}, {"name": "Vertex_095_7", "type": "uint32", "default": 100}, {"name": "Vertex_095_8", "type": "int64", "default": 10}, {"name": "Vertex_095_9", "type": "uint64", "default": 100}, {"name": "Vertex_095_10", "type": "time", "nullable": true}, {"name": "Vertex_095_11", "type": "float", "default": 500.1}, {"name": "Vertex_095_12", "type": "long", "default": 10}, {"name": "Vertex_095_13", "type": "double", "nullable": true}, {"name": "Vertex_095_14", "type": "boolean", "nullable": true}, {"name": "Vertex_095_15", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_16", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_17", "type": "partition", "nullable": false}, {"name": "Vertex_095_18", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_19", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_20", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_21", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_22", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_23", "type": "bytes", "size": 12}, {"name": "S200", "type": "record", "fields": [{"name": "Vertex_095_2000", "type": "char", "nullable": true}, {"name": "Vertex_095_2001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_2002", "type": "int8", "nullable": true}, {"name": "Vertex_095_2003", "type": "uint8", "default": 100}, {"name": "Vertex_095_2004", "type": "int16", "default": 10}, {"name": "Vertex_095_2005", "type": "uint16", "default": 100}, {"name": "Vertex_095_2006", "type": "int32", "default": 10}, {"name": "Vertex_095_2007", "type": "uint32", "default": 100}, {"name": "Vertex_095_2008", "type": "int64", "default": 10}, {"name": "Vertex_095_2009", "type": "uint64", "default": 100}, {"name": "Vertex_095_20010", "type": "time", "nullable": true}, {"name": "Vertex_095_20011", "type": "float", "default": 500.1}, {"name": "Vertex_095_20012", "type": "long", "default": 10}, {"name": "Vertex_095_20013", "type": "double", "nullable": true}, {"name": "Vertex_095_20014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_20015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_20016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_20018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_20019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_20020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_20021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_20022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_20023", "type": "bytes", "size": 12}, {"name": "S300", "type": "record", "fields": [{"name": "Vertex_095_3000", "type": "char", "nullable": true}, {"name": "Vertex_095_3001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_3002", "type": "int8", "nullable": true}, {"name": "Vertex_095_3003", "type": "uint8", "default": 100}, {"name": "Vertex_095_3004", "type": "int16", "default": 10}, {"name": "Vertex_095_3005", "type": "uint16", "default": 100}, {"name": "Vertex_095_3006", "type": "int32", "default": 10}, {"name": "Vertex_095_3007", "type": "uint32", "default": 100}, {"name": "Vertex_095_3008", "type": "int64", "default": 10}, {"name": "Vertex_095_3009", "type": "uint64", "default": 100}, {"name": "Vertex_095_30010", "type": "time", "nullable": true}, {"name": "Vertex_095_30011", "type": "float", "default": 500.1}, {"name": "Vertex_095_30012", "type": "long", "default": 10}, {"name": "Vertex_095_30013", "type": "double", "nullable": true}, {"name": "Vertex_095_30014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_30015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_30016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_30018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_30019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_30020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_30021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_30022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_30023", "type": "bytes", "size": 12}, {"name": "S400", "type": "record", "fields": [{"name": "Vertex_095_4000", "type": "char", "nullable": true}, {"name": "Vertex_095_4001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_4002", "type": "int8", "nullable": true}, {"name": "Vertex_095_4003", "type": "uint8", "default": 100}, {"name": "Vertex_095_4004", "type": "int16", "default": 10}, {"name": "Vertex_095_4005", "type": "uint16", "default": 100}, {"name": "Vertex_095_4006", "type": "int32", "default": 10}, {"name": "Vertex_095_4007", "type": "uint32", "default": 100}, {"name": "Vertex_095_4008", "type": "int64", "default": 10}, {"name": "Vertex_095_4009", "type": "uint64", "default": 100}, {"name": "Vertex_095_40010", "type": "time", "nullable": true}, {"name": "Vertex_095_40011", "type": "float", "default": 500.1}, {"name": "Vertex_095_40012", "type": "long", "default": 10}, {"name": "Vertex_095_40013", "type": "double", "nullable": true}, {"name": "Vertex_095_40014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_40015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_40016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_40018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_40019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_40020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_40021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_40022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_40023", "type": "bytes", "size": 12}, {"name": "S500", "type": "record", "fields": [{"name": "Vertex_095_5000", "type": "char", "nullable": true}, {"name": "Vertex_095_5001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_5002", "type": "int8", "nullable": true}, {"name": "Vertex_095_5003", "type": "uint8", "default": 100}, {"name": "Vertex_095_5004", "type": "int16", "default": 10}, {"name": "Vertex_095_5005", "type": "uint16", "default": 100}, {"name": "Vertex_095_5006", "type": "int32", "default": 10}, {"name": "Vertex_095_5007", "type": "uint32", "default": 100}, {"name": "Vertex_095_5008", "type": "int64", "default": 10}, {"name": "Vertex_095_5009", "type": "uint64", "default": 100}, {"name": "Vertex_095_50010", "type": "time", "nullable": true}, {"name": "Vertex_095_50011", "type": "float", "default": 500.1}, {"name": "Vertex_095_50012", "type": "long", "default": 10}, {"name": "Vertex_095_50013", "type": "double", "nullable": true}, {"name": "Vertex_095_50014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_50015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_50016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_50018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_50019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_50020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_50021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_50022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_50023", "type": "bytes", "size": 12}, {"name": "S600", "type": "record", "fields": [{"name": "Vertex_095_6000", "type": "char", "nullable": true}, {"name": "Vertex_095_6001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_6002", "type": "int8", "nullable": true}, {"name": "Vertex_095_6003", "type": "uint8", "default": 100}, {"name": "Vertex_095_6004", "type": "int16", "default": 10}, {"name": "Vertex_095_6005", "type": "uint16", "default": 100}, {"name": "Vertex_095_6006", "type": "int32", "default": 10}, {"name": "Vertex_095_6007", "type": "uint32", "default": 100}, {"name": "Vertex_095_6008", "type": "int64", "default": 10}, {"name": "Vertex_095_6009", "type": "uint64", "default": 100}, {"name": "Vertex_095_60010", "type": "time", "nullable": true}, {"name": "Vertex_095_60011", "type": "float", "default": 500.1}, {"name": "Vertex_095_60012", "type": "long", "default": 10}, {"name": "Vertex_095_60013", "type": "double", "nullable": true}, {"name": "Vertex_095_60014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_60015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_60016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_60018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_60019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_60020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_60021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_60022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_60023", "type": "bytes", "size": 12}, {"name": "S700", "type": "record", "fields": [{"name": "Vertex_095_7000", "type": "char", "nullable": true}, {"name": "Vertex_095_7001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_7002", "type": "int8", "nullable": true}, {"name": "Vertex_095_7003", "type": "uint8", "default": 100}, {"name": "Vertex_095_7004", "type": "int16", "default": 10}, {"name": "Vertex_095_7005", "type": "uint16", "default": 100}, {"name": "Vertex_095_7006", "type": "int32", "default": 10}, {"name": "Vertex_095_7007", "type": "uint32", "default": 100}, {"name": "Vertex_095_7008", "type": "int64", "default": 10}, {"name": "Vertex_095_7009", "type": "uint64", "default": 100}, {"name": "Vertex_095_70010", "type": "time", "nullable": true}, {"name": "Vertex_095_70011", "type": "float", "default": 500.1}, {"name": "Vertex_095_70012", "type": "long", "default": 10}, {"name": "Vertex_095_70013", "type": "double", "nullable": true}, {"name": "Vertex_095_70014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_70015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_70016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_70018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_70019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_70020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_70021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_70022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_70023", "type": "bytes", "size": 12}, {"name": "S800", "type": "record", "fields": [{"name": "Vertex_095_8000", "type": "char", "nullable": true}, {"name": "Vertex_095_8001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_8002", "type": "int8", "nullable": true}, {"name": "Vertex_095_8003", "type": "uint8", "default": 100}, {"name": "Vertex_095_8004", "type": "int16", "default": 10}, {"name": "Vertex_095_8005", "type": "uint16", "default": 100}, {"name": "Vertex_095_8006", "type": "int32", "default": 10}, {"name": "Vertex_095_8007", "type": "uint32", "default": 100}, {"name": "Vertex_095_8008", "type": "int64", "default": 10}, {"name": "Vertex_095_8009", "type": "uint64", "default": 100}, {"name": "Vertex_095_80010", "type": "time", "nullable": true}, {"name": "Vertex_095_80011", "type": "float", "default": 500.1}, {"name": "Vertex_095_80012", "type": "long", "default": 10}, {"name": "Vertex_095_80013", "type": "double", "nullable": true}, {"name": "Vertex_095_80014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_80015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_80016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_80018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_80019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_80020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_80021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_80022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_80023", "type": "bytes", "size": 12}, {"name": "S900", "type": "record", "fields": [{"name": "Vertex_095_9000", "type": "char", "nullable": true}, {"name": "Vertex_095_9001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_9002", "type": "int8", "nullable": true}, {"name": "Vertex_095_9003", "type": "uint8", "default": 100}, {"name": "Vertex_095_9004", "type": "int16", "default": 10}, {"name": "Vertex_095_9005", "type": "uint16", "default": 100}, {"name": "Vertex_095_9006", "type": "int32", "default": 10}, {"name": "Vertex_095_9007", "type": "uint32", "default": 100}, {"name": "Vertex_095_9008", "type": "int64", "default": 10}, {"name": "Vertex_095_9009", "type": "uint64", "default": 100}, {"name": "Vertex_095_90010", "type": "time", "nullable": true}, {"name": "Vertex_095_90011", "type": "float", "default": 500.1}, {"name": "Vertex_095_90012", "type": "long", "default": 10}, {"name": "Vertex_095_90013", "type": "double", "nullable": true}, {"name": "Vertex_095_90014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_90015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_90016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_90018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_90019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_90020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_90021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_90022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_90023", "type": "bytes", "size": 12}, {"name": "S1000", "type": "record", "fields": [{"name": "Vertex_095_10000", "type": "char", "nullable": true}, {"name": "Vertex_095_10001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_10002", "type": "int8", "nullable": true}, {"name": "Vertex_095_10003", "type": "uint8", "default": 100}, {"name": "Vertex_095_10004", "type": "int16", "default": 10}, {"name": "Vertex_095_10005", "type": "uint16", "default": 100}, {"name": "Vertex_095_10006", "type": "int32", "default": 10}, {"name": "Vertex_095_10007", "type": "uint32", "default": 100}, {"name": "Vertex_095_10008", "type": "int64", "default": 10}, {"name": "Vertex_095_10009", "type": "uint64", "default": 100}, {"name": "Vertex_095_100010", "type": "time", "nullable": true}, {"name": "Vertex_095_100011", "type": "float", "default": 500.1}, {"name": "Vertex_095_100012", "type": "long", "default": 10}, {"name": "Vertex_095_100013", "type": "double", "nullable": true}, {"name": "Vertex_095_100014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_100015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_100016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_100018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_100019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_100020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_100021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_100022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_100023", "type": "bytes", "size": 12}, {"name": "S1100", "type": "record", "fields": [{"name": "Vertex_095_11000", "type": "char", "nullable": true}, {"name": "Vertex_095_11001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_11002", "type": "int8", "nullable": true}, {"name": "Vertex_095_11003", "type": "uint8", "default": 100}, {"name": "Vertex_095_11004", "type": "int16", "default": 10}, {"name": "Vertex_095_11005", "type": "uint16", "default": 100}, {"name": "Vertex_095_11006", "type": "int32", "default": 10}, {"name": "Vertex_095_11007", "type": "uint32", "default": 100}, {"name": "Vertex_095_11008", "type": "int64", "default": 10}, {"name": "Vertex_095_11009", "type": "uint64", "default": 100}, {"name": "Vertex_095_110010", "type": "time", "nullable": true}, {"name": "Vertex_095_110011", "type": "float", "default": 500.1}, {"name": "Vertex_095_110012", "type": "long", "default": 10}, {"name": "Vertex_095_110013", "type": "double", "nullable": true}, {"name": "Vertex_095_110014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_110015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_110016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_110018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_110019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_110020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_110021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_110022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_110023", "type": "bytes", "size": 12}, {"name": "S1200", "type": "record", "fields": [{"name": "Vertex_095_12000", "type": "char", "nullable": true}, {"name": "Vertex_095_12001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_12002", "type": "int8", "nullable": true}, {"name": "Vertex_095_12003", "type": "uint8", "default": 100}, {"name": "Vertex_095_12004", "type": "int16", "default": 10}, {"name": "Vertex_095_12005", "type": "uint16", "default": 100}, {"name": "Vertex_095_12006", "type": "int32", "default": 10}, {"name": "Vertex_095_12007", "type": "uint32", "default": 100}, {"name": "Vertex_095_12008", "type": "int64", "default": 10}, {"name": "Vertex_095_12009", "type": "uint64", "default": 100}, {"name": "Vertex_095_120010", "type": "time", "nullable": true}, {"name": "Vertex_095_120011", "type": "float", "default": 500.1}, {"name": "Vertex_095_120012", "type": "long", "default": 10}, {"name": "Vertex_095_120013", "type": "double", "nullable": true}, {"name": "Vertex_095_120014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_120015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_120016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_120018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_120019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_120020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_120021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_120022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_120023", "type": "bytes", "size": 12}, {"name": "S1300", "type": "record", "fields": [{"name": "Vertex_095_13000", "type": "char", "nullable": true}, {"name": "Vertex_095_13001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_13002", "type": "int8", "nullable": true}, {"name": "Vertex_095_13003", "type": "uint8", "default": 100}, {"name": "Vertex_095_13004", "type": "int16", "default": 10}, {"name": "Vertex_095_13005", "type": "uint16", "default": 100}, {"name": "Vertex_095_13006", "type": "int32", "default": 10}, {"name": "Vertex_095_13007", "type": "uint32", "default": 100}, {"name": "Vertex_095_13008", "type": "int64", "default": 10}, {"name": "Vertex_095_13009", "type": "uint64", "default": 100}, {"name": "Vertex_095_130010", "type": "time", "nullable": true}, {"name": "Vertex_095_130011", "type": "float", "default": 500.1}, {"name": "Vertex_095_130012", "type": "long", "default": 10}, {"name": "Vertex_095_130013", "type": "double", "nullable": true}, {"name": "Vertex_095_130014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_130015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_130016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_130018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_130019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_130020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_130021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_130022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_130023", "type": "bytes", "size": 12}, {"name": "S1400", "type": "record", "fields": [{"name": "Vertex_095_14000", "type": "char", "nullable": true}, {"name": "Vertex_095_14001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_14002", "type": "int8", "nullable": true}, {"name": "Vertex_095_14003", "type": "uint8", "default": 100}, {"name": "Vertex_095_14004", "type": "int16", "default": 10}, {"name": "Vertex_095_14005", "type": "uint16", "default": 100}, {"name": "Vertex_095_14006", "type": "int32", "default": 10}, {"name": "Vertex_095_14007", "type": "uint32", "default": 100}, {"name": "Vertex_095_14008", "type": "int64", "default": 10}, {"name": "Vertex_095_14009", "type": "uint64", "default": 100}, {"name": "Vertex_095_140010", "type": "time", "nullable": true}, {"name": "Vertex_095_140011", "type": "float", "default": 500.1}, {"name": "Vertex_095_140012", "type": "long", "default": 10}, {"name": "Vertex_095_140013", "type": "double", "nullable": true}, {"name": "Vertex_095_140014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_140015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_140016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_140018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_140019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_140020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_140021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_140022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_140023", "type": "bytes", "size": 12}, {"name": "S1500", "type": "record", "fields": [{"name": "Vertex_095_15000", "type": "char", "nullable": true}, {"name": "Vertex_095_15001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_15002", "type": "int8", "nullable": true}, {"name": "Vertex_095_15003", "type": "uint8", "default": 100}, {"name": "Vertex_095_15004", "type": "int16", "default": 10}, {"name": "Vertex_095_15005", "type": "uint16", "default": 100}, {"name": "Vertex_095_15006", "type": "int32", "default": 10}, {"name": "Vertex_095_15007", "type": "uint32", "default": 100}, {"name": "Vertex_095_15008", "type": "int64", "default": 10}, {"name": "Vertex_095_15009", "type": "uint64", "default": 100}, {"name": "Vertex_095_150010", "type": "time", "nullable": true}, {"name": "Vertex_095_150011", "type": "float", "default": 500.1}, {"name": "Vertex_095_150012", "type": "long", "default": 10}, {"name": "Vertex_095_150013", "type": "double", "nullable": true}, {"name": "Vertex_095_150014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_150015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_150016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_150018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_150019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_150020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_150021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_150022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_150023", "type": "bytes", "size": 12}, {"name": "S1600", "type": "record", "fields": [{"name": "Vertex_095_16000", "type": "char", "nullable": true}, {"name": "Vertex_095_16001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_16002", "type": "int8", "nullable": true}, {"name": "Vertex_095_16003", "type": "uint8", "default": 100}, {"name": "Vertex_095_16004", "type": "int16", "default": 10}, {"name": "Vertex_095_16005", "type": "uint16", "default": 100}, {"name": "Vertex_095_16006", "type": "int32", "default": 10}, {"name": "Vertex_095_16007", "type": "uint32", "default": 100}, {"name": "Vertex_095_16008", "type": "int64", "default": 10}, {"name": "Vertex_095_16009", "type": "uint64", "default": 100}, {"name": "Vertex_095_160010", "type": "time", "nullable": true}, {"name": "Vertex_095_160011", "type": "float", "default": 500.1}, {"name": "Vertex_095_160012", "type": "long", "default": 10}, {"name": "Vertex_095_160013", "type": "double", "nullable": true}, {"name": "Vertex_095_160014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_160015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_160016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_160018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_160019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_160020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_160021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_160022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_160023", "type": "bytes", "size": 12}, {"name": "S1700", "type": "record", "fields": [{"name": "Vertex_095_17000", "type": "char", "nullable": true}, {"name": "Vertex_095_17001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_17002", "type": "int8", "nullable": true}, {"name": "Vertex_095_17003", "type": "uint8", "default": 100}, {"name": "Vertex_095_17004", "type": "int16", "default": 10}, {"name": "Vertex_095_17005", "type": "uint16", "default": 100}, {"name": "Vertex_095_17006", "type": "int32", "default": 10}, {"name": "Vertex_095_17007", "type": "uint32", "default": 100}, {"name": "Vertex_095_17008", "type": "int64", "default": 10}, {"name": "Vertex_095_17009", "type": "uint64", "default": 100}, {"name": "Vertex_095_170010", "type": "time", "nullable": true}, {"name": "Vertex_095_170011", "type": "float", "default": 500.1}, {"name": "Vertex_095_170012", "type": "long", "default": 10}, {"name": "Vertex_095_170013", "type": "double", "nullable": true}, {"name": "Vertex_095_170014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_170015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_170016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_170018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_170019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_170020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_170021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_170022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_170023", "type": "bytes", "size": 12}, {"name": "S1800", "type": "record", "fields": [{"name": "Vertex_095_18000", "type": "char", "nullable": true}, {"name": "Vertex_095_18001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_18002", "type": "int8", "nullable": true}, {"name": "Vertex_095_18003", "type": "uint8", "default": 100}, {"name": "Vertex_095_18004", "type": "int16", "default": 10}, {"name": "Vertex_095_18005", "type": "uint16", "default": 100}, {"name": "Vertex_095_18006", "type": "int32", "default": 10}, {"name": "Vertex_095_18007", "type": "uint32", "default": 100}, {"name": "Vertex_095_18008", "type": "int64", "default": 10}, {"name": "Vertex_095_18009", "type": "uint64", "default": 100}, {"name": "Vertex_095_180010", "type": "time", "nullable": true}, {"name": "Vertex_095_180011", "type": "float", "default": 500.1}, {"name": "Vertex_095_180012", "type": "long", "default": 10}, {"name": "Vertex_095_180013", "type": "double", "nullable": true}, {"name": "Vertex_095_180014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_180015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_180016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_180018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_180019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_180020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_180021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_180022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_180023", "type": "bytes", "size": 12}, {"name": "S1900", "type": "record", "fields": [{"name": "Vertex_095_19000", "type": "char", "nullable": true}, {"name": "Vertex_095_19001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_19002", "type": "int8", "nullable": true}, {"name": "Vertex_095_19003", "type": "uint8", "default": 100}, {"name": "Vertex_095_19004", "type": "int16", "default": 10}, {"name": "Vertex_095_19005", "type": "uint16", "default": 100}, {"name": "Vertex_095_19006", "type": "int32", "default": 10}, {"name": "Vertex_095_19007", "type": "uint32", "default": 100}, {"name": "Vertex_095_19008", "type": "int64", "default": 10}, {"name": "Vertex_095_19009", "type": "uint64", "default": 100}, {"name": "Vertex_095_190010", "type": "time", "nullable": true}, {"name": "Vertex_095_190011", "type": "float", "default": 500.1}, {"name": "Vertex_095_190012", "type": "long", "default": 10}, {"name": "Vertex_095_190013", "type": "double", "nullable": true}, {"name": "Vertex_095_190014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_190015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_190016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_190018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_190019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_190020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_190021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_190022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_190023", "type": "bytes", "size": 12}, {"name": "S2000", "type": "record", "fields": [{"name": "Vertex_095_20000", "type": "char", "nullable": true}, {"name": "Vertex_095_20001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_20002", "type": "int8", "nullable": true}, {"name": "Vertex_095_20003", "type": "uint8", "default": 100}, {"name": "Vertex_095_20004", "type": "int16", "default": 10}, {"name": "Vertex_095_20005", "type": "uint16", "default": 100}, {"name": "Vertex_095_20006", "type": "int32", "default": 10}, {"name": "Vertex_095_20007", "type": "uint32", "default": 100}, {"name": "Vertex_095_20008", "type": "int64", "default": 10}, {"name": "Vertex_095_20009", "type": "uint64", "default": 100}, {"name": "Vertex_095_200010", "type": "time", "nullable": true}, {"name": "Vertex_095_200011", "type": "float", "default": 500.1}, {"name": "Vertex_095_200012", "type": "long", "default": 10}, {"name": "Vertex_095_200013", "type": "double", "nullable": true}, {"name": "Vertex_095_200014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_200015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_200016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_200018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_200019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_200020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_200021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_200022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_200023", "type": "bytes", "size": 12}, {"name": "S2100", "type": "record", "fields": [{"name": "Vertex_095_21000", "type": "char", "nullable": true}, {"name": "Vertex_095_21001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_21002", "type": "int8", "nullable": true}, {"name": "Vertex_095_21003", "type": "uint8", "default": 100}, {"name": "Vertex_095_21004", "type": "int16", "default": 10}, {"name": "Vertex_095_21005", "type": "uint16", "default": 100}, {"name": "Vertex_095_21006", "type": "int32", "default": 10}, {"name": "Vertex_095_21007", "type": "uint32", "default": 100}, {"name": "Vertex_095_21008", "type": "int64", "default": 10}, {"name": "Vertex_095_21009", "type": "uint64", "default": 100}, {"name": "Vertex_095_210010", "type": "time", "nullable": true}, {"name": "Vertex_095_210011", "type": "float", "default": 500.1}, {"name": "Vertex_095_210012", "type": "long", "default": 10}, {"name": "Vertex_095_210013", "type": "double", "nullable": true}, {"name": "Vertex_095_210014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_210015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_210016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_210018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_210019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_210020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_210021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_210022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_210023", "type": "bytes", "size": 12}, {"name": "S2200", "type": "record", "fields": [{"name": "Vertex_095_22000", "type": "char", "nullable": true}, {"name": "Vertex_095_22001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_22002", "type": "int8", "nullable": true}, {"name": "Vertex_095_22003", "type": "uint8", "default": 100}, {"name": "Vertex_095_22004", "type": "int16", "default": 10}, {"name": "Vertex_095_22005", "type": "uint16", "default": 100}, {"name": "Vertex_095_22006", "type": "int32", "default": 10}, {"name": "Vertex_095_22007", "type": "uint32", "default": 100}, {"name": "Vertex_095_22008", "type": "int64", "default": 10}, {"name": "Vertex_095_22009", "type": "uint64", "default": 100}, {"name": "Vertex_095_220010", "type": "time", "nullable": true}, {"name": "Vertex_095_220011", "type": "float", "default": 500.1}, {"name": "Vertex_095_220012", "type": "long", "default": 10}, {"name": "Vertex_095_220013", "type": "double", "nullable": true}, {"name": "Vertex_095_220014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_220015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_220016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_220018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_220019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_220020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_220021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_220022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_220023", "type": "bytes", "size": 12}, {"name": "S2300", "type": "record", "fields": [{"name": "Vertex_095_23000", "type": "char", "nullable": true}, {"name": "Vertex_095_23001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_23002", "type": "int8", "nullable": true}, {"name": "Vertex_095_23003", "type": "uint8", "default": 100}, {"name": "Vertex_095_23004", "type": "int16", "default": 10}, {"name": "Vertex_095_23005", "type": "uint16", "default": 100}, {"name": "Vertex_095_23006", "type": "int32", "default": 10}, {"name": "Vertex_095_23007", "type": "uint32", "default": 100}, {"name": "Vertex_095_23008", "type": "int64", "default": 10}, {"name": "Vertex_095_23009", "type": "uint64", "default": 100}, {"name": "Vertex_095_230010", "type": "time", "nullable": true}, {"name": "Vertex_095_230011", "type": "float", "default": 500.1}, {"name": "Vertex_095_230012", "type": "long", "default": 10}, {"name": "Vertex_095_230013", "type": "double", "nullable": true}, {"name": "Vertex_095_230014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_230015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_230016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_230018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_230019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_230020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_230021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_230022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_230023", "type": "bytes", "size": 12}, {"name": "S2400", "type": "record", "fields": [{"name": "Vertex_095_24000", "type": "char", "nullable": true}, {"name": "Vertex_095_24001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_24002", "type": "int8", "nullable": true}, {"name": "Vertex_095_24003", "type": "uint8", "default": 100}, {"name": "Vertex_095_24004", "type": "int16", "default": 10}, {"name": "Vertex_095_24005", "type": "uint16", "default": 100}, {"name": "Vertex_095_24006", "type": "int32", "default": 10}, {"name": "Vertex_095_24007", "type": "uint32", "default": 100}, {"name": "Vertex_095_24008", "type": "int64", "default": 10}, {"name": "Vertex_095_24009", "type": "uint64", "default": 100}, {"name": "Vertex_095_240010", "type": "time", "nullable": true}, {"name": "Vertex_095_240011", "type": "float", "default": 500.1}, {"name": "Vertex_095_240012", "type": "long", "default": 10}, {"name": "Vertex_095_240013", "type": "double", "nullable": true}, {"name": "Vertex_095_240014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_240015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_240016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_240018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_240019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_240020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_240021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_240022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_240023", "type": "bytes", "size": 12}, {"name": "S2500", "type": "record", "fields": [{"name": "Vertex_095_25000", "type": "char", "nullable": true}, {"name": "Vertex_095_25001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_25002", "type": "int8", "nullable": true}, {"name": "Vertex_095_25003", "type": "uint8", "default": 100}, {"name": "Vertex_095_25004", "type": "int16", "default": 10}, {"name": "Vertex_095_25005", "type": "uint16", "default": 100}, {"name": "Vertex_095_25006", "type": "int32", "default": 10}, {"name": "Vertex_095_25007", "type": "uint32", "default": 100}, {"name": "Vertex_095_25008", "type": "int64", "default": 10}, {"name": "Vertex_095_25009", "type": "uint64", "default": 100}, {"name": "Vertex_095_250010", "type": "time", "nullable": true}, {"name": "Vertex_095_250011", "type": "float", "default": 500.1}, {"name": "Vertex_095_250012", "type": "long", "default": 10}, {"name": "Vertex_095_250013", "type": "double", "nullable": true}, {"name": "Vertex_095_250014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_250015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_250016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_250018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_250019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_250020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_250021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_250022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_250023", "type": "bytes", "size": 12}, {"name": "S2600", "type": "record", "fields": [{"name": "Vertex_095_26000", "type": "char", "nullable": true}, {"name": "Vertex_095_26001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_26002", "type": "int8", "nullable": true}, {"name": "Vertex_095_26003", "type": "uint8", "default": 100}, {"name": "Vertex_095_26004", "type": "int16", "default": 10}, {"name": "Vertex_095_26005", "type": "uint16", "default": 100}, {"name": "Vertex_095_26006", "type": "int32", "default": 10}, {"name": "Vertex_095_26007", "type": "uint32", "default": 100}, {"name": "Vertex_095_26008", "type": "int64", "default": 10}, {"name": "Vertex_095_26009", "type": "uint64", "default": 100}, {"name": "Vertex_095_260010", "type": "time", "nullable": true}, {"name": "Vertex_095_260011", "type": "float", "default": 500.1}, {"name": "Vertex_095_260012", "type": "long", "default": 10}, {"name": "Vertex_095_260013", "type": "double", "nullable": true}, {"name": "Vertex_095_260014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_260015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_260016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_260018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_260019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_260020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_260021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_260022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_260023", "type": "bytes", "size": 12}, {"name": "S2700", "type": "record", "fields": [{"name": "Vertex_095_27000", "type": "char", "nullable": true}, {"name": "Vertex_095_27001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_27002", "type": "int8", "nullable": true}, {"name": "Vertex_095_27003", "type": "uint8", "default": 100}, {"name": "Vertex_095_27004", "type": "int16", "default": 10}, {"name": "Vertex_095_27005", "type": "uint16", "default": 100}, {"name": "Vertex_095_27006", "type": "int32", "default": 10}, {"name": "Vertex_095_27007", "type": "uint32", "default": 100}, {"name": "Vertex_095_27008", "type": "int64", "default": 10}, {"name": "Vertex_095_27009", "type": "uint64", "default": 100}, {"name": "Vertex_095_270010", "type": "time", "nullable": true}, {"name": "Vertex_095_270011", "type": "float", "default": 500.1}, {"name": "Vertex_095_270012", "type": "long", "default": 10}, {"name": "Vertex_095_270013", "type": "double", "nullable": true}, {"name": "Vertex_095_270014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_270015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_270016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_270018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_270019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_270020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_270021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_270022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_270023", "type": "bytes", "size": 12}, {"name": "S2800", "type": "record", "fields": [{"name": "Vertex_095_28000", "type": "char", "nullable": true}, {"name": "Vertex_095_28001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_28002", "type": "int8", "nullable": true}, {"name": "Vertex_095_28003", "type": "uint8", "default": 100}, {"name": "Vertex_095_28004", "type": "int16", "default": 10}, {"name": "Vertex_095_28005", "type": "uint16", "default": 100}, {"name": "Vertex_095_28006", "type": "int32", "default": 10}, {"name": "Vertex_095_28007", "type": "uint32", "default": 100}, {"name": "Vertex_095_28008", "type": "int64", "default": 10}, {"name": "Vertex_095_28009", "type": "uint64", "default": 100}, {"name": "Vertex_095_280010", "type": "time", "nullable": true}, {"name": "Vertex_095_280011", "type": "float", "default": 500.1}, {"name": "Vertex_095_280012", "type": "long", "default": 10}, {"name": "Vertex_095_280013", "type": "double", "nullable": true}, {"name": "Vertex_095_280014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_280015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_280016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_280018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_280019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_280020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_280021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_280022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_280023", "type": "bytes", "size": 12}, {"name": "S2900", "type": "record", "fields": [{"name": "Vertex_095_29000", "type": "char", "nullable": true}, {"name": "Vertex_095_29001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_29002", "type": "int8", "nullable": true}, {"name": "Vertex_095_29003", "type": "uint8", "default": 100}, {"name": "Vertex_095_29004", "type": "int16", "default": 10}, {"name": "Vertex_095_29005", "type": "uint16", "default": 100}, {"name": "Vertex_095_29006", "type": "int32", "default": 10}, {"name": "Vertex_095_29007", "type": "uint32", "default": 100}, {"name": "Vertex_095_29008", "type": "int64", "default": 10}, {"name": "Vertex_095_29009", "type": "uint64", "default": 100}, {"name": "Vertex_095_290010", "type": "time", "nullable": true}, {"name": "Vertex_095_290011", "type": "float", "default": 500.1}, {"name": "Vertex_095_290012", "type": "long", "default": 10}, {"name": "Vertex_095_290013", "type": "double", "nullable": true}, {"name": "Vertex_095_290014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_290015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_290016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_290018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_290019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_290020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_290021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_290022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_290023", "type": "bytes", "size": 12}, {"name": "S3000", "type": "record", "fields": [{"name": "Vertex_095_30000", "type": "char", "nullable": true}, {"name": "Vertex_095_30001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_30002", "type": "int8", "nullable": true}, {"name": "Vertex_095_30003", "type": "uint8", "default": 100}, {"name": "Vertex_095_30004", "type": "int16", "default": 10}, {"name": "Vertex_095_30005", "type": "uint16", "default": 100}, {"name": "Vertex_095_30006", "type": "int32", "default": 10}, {"name": "Vertex_095_30007", "type": "uint32", "default": 100}, {"name": "Vertex_095_30008", "type": "int64", "default": 10}, {"name": "Vertex_095_30009", "type": "uint64", "default": 100}, {"name": "Vertex_095_300010", "type": "time", "nullable": true}, {"name": "Vertex_095_300011", "type": "float", "default": 500.1}, {"name": "Vertex_095_300012", "type": "long", "default": 10}, {"name": "Vertex_095_300013", "type": "double", "nullable": true}, {"name": "Vertex_095_300014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_300015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_300016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_300018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_300019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_300020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_300021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_300022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_300023", "type": "bytes", "size": 12}, {"name": "S3100", "type": "record", "fields": [{"name": "Vertex_095_31000", "type": "char", "nullable": true}, {"name": "Vertex_095_31001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_31002", "type": "int8", "nullable": true}, {"name": "Vertex_095_31003", "type": "uint8", "default": 100}, {"name": "Vertex_095_31004", "type": "int16", "default": 10}, {"name": "Vertex_095_31005", "type": "uint16", "default": 100}, {"name": "Vertex_095_31006", "type": "int32", "default": 10}, {"name": "Vertex_095_31007", "type": "uint32", "default": 100}, {"name": "Vertex_095_31008", "type": "int64", "default": 10}, {"name": "Vertex_095_31009", "type": "uint64", "default": 100}, {"name": "Vertex_095_310010", "type": "time", "nullable": true}, {"name": "Vertex_095_310011", "type": "float", "default": 500.1}, {"name": "Vertex_095_310012", "type": "long", "default": 10}, {"name": "Vertex_095_310013", "type": "double", "nullable": true}, {"name": "Vertex_095_310014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_310015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_310016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_310018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_310019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_310020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_310021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_310022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_310023", "type": "bytes", "size": 12}, {"name": "S3200", "type": "record", "fields": [{"name": "Vertex_095_32000", "type": "char", "nullable": true}, {"name": "Vertex_095_32001", "type": "uchar", "nullable": true}, {"name": "Vertex_095_32002", "type": "int8", "nullable": true}, {"name": "Vertex_095_32003", "type": "uint8", "default": 100}, {"name": "Vertex_095_32004", "type": "int16", "default": 10}, {"name": "Vertex_095_32005", "type": "uint16", "default": 100}, {"name": "Vertex_095_32006", "type": "int32", "default": 10}, {"name": "Vertex_095_32007", "type": "uint32", "default": 100}, {"name": "Vertex_095_32008", "type": "int64", "default": 10}, {"name": "Vertex_095_32009", "type": "uint64", "default": 100}, {"name": "Vertex_095_320010", "type": "time", "nullable": true}, {"name": "Vertex_095_320011", "type": "float", "default": 500.1}, {"name": "Vertex_095_320012", "type": "long", "default": 10}, {"name": "Vertex_095_320013", "type": "double", "nullable": true}, {"name": "Vertex_095_320014", "type": "boolean", "nullable": true}, {"name": "Vertex_095_320015", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "Vertex_095_320016", "type": "fixed", "size": 3, "default": "fff"}, {"name": "Vertex_095_320018", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "Vertex_095_320019", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "Vertex_095_320020", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "Vertex_095_320021", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "Vertex_095_320022", "type": "string", "size": 100, "nullable": true}, {"name": "Vertex_095_320023", "type": "bytes", "size": 12}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"name": "c11", "type": "record", "fixed_array": true, "size": 16, "fields": [{"name": "r_095_0", "type": "char", "nullable": true}, {"name": "r_095_1", "type": "uchar", "nullable": true}, {"name": "r_095_2", "type": "int8", "nullable": true}, {"name": "r_095_3", "type": "uint8", "default": 100}, {"name": "r_095_4", "type": "int16", "default": 10}, {"name": "r_095_5", "type": "uint16", "default": 100}, {"name": "r_095_6", "type": "int32", "default": 10}, {"name": "r_095_7", "type": "uint32", "default": 100}, {"name": "r_095_8", "type": "int64", "default": 10}, {"name": "r_095_9", "type": "uint64", "default": 100}, {"name": "r_095_10", "type": "time", "nullable": true}, {"name": "r_095_11", "type": "float", "default": 500.1}, {"name": "r_095_12", "type": "long", "default": 10}, {"name": "r_095_13", "type": "double", "nullable": true}, {"name": "r_095_14", "type": "boolean", "nullable": true}, {"name": "r_095_15", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "r_095_16", "type": "fixed", "size": 3, "default": "fff"}, {"name": "r_095_18", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "r_095_19", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "r_095_20", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "r_095_21", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "r_095_22", "type": "string", "size": 100, "nullable": true}, {"name": "r_095_23", "type": "bytes", "size": 12}]}, {"name": "c12", "type": "record", "fixed_array": true, "size": 16, "comment": "nodeSchema 1", "fields": [{"name": "f_095_0", "type": "char", "nullable": true}, {"name": "f_095_1", "type": "uchar", "nullable": true}, {"name": "f_095_2", "type": "int8", "nullable": true}, {"name": "f_095_3", "type": "uint8", "default": 100}, {"name": "f_095_4", "type": "int16", "default": 10}, {"name": "f_095_5", "type": "uint16", "default": 100}, {"name": "f_095_6", "type": "int32", "default": 10}, {"name": "f_095_7", "type": "uint32", "default": 100}, {"name": "f_095_8", "type": "int64", "default": 10}, {"name": "f_095_9", "type": "uint64", "default": 100}, {"name": "f_095_10", "type": "time", "nullable": true}, {"name": "f_095_11", "type": "float", "default": 500.1}, {"name": "f_095_12", "type": "long", "default": 10}, {"name": "f_095_13", "type": "double", "nullable": true}, {"name": "f_095_14", "type": "boolean", "nullable": true}, {"name": "f_095_15", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "f_095_16", "type": "fixed", "size": 3, "default": "fff"}, {"name": "f_095_18", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "f_095_19", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "f_095_20", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "f_095_21", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "f_095_22", "type": "string", "size": 100, "nullable": true}, {"name": "f_095_23", "type": "bytes", "size": 12}]}, {"name": "c13", "type": "record", "vector": true, "size": 1024, "comment": "nodeSchema 2", "fields": [{"name": "v_095_0", "type": "char", "nullable": true}, {"name": "v_095_1", "type": "uchar", "nullable": true}, {"name": "v_095_2", "type": "int8", "nullable": true}, {"name": "v_095_3", "type": "uint8", "default": 100}, {"name": "v_095_4", "type": "int16", "default": 10}, {"name": "v_095_5", "type": "uint16", "default": 100}, {"name": "v_095_6", "type": "int32", "default": 10}, {"name": "v_095_7", "type": "uint32", "default": 100}, {"name": "v_095_8", "type": "int64", "default": 10}, {"name": "v_095_9", "type": "uint64", "default": 100}, {"name": "v_095_10", "type": "time", "nullable": true}, {"name": "v_095_11", "type": "float", "default": 500.1}, {"name": "v_095_12", "type": "long", "default": 10}, {"name": "v_095_13", "type": "double", "nullable": true}, {"name": "v_095_14", "type": "boolean", "nullable": true}, {"name": "v_095_15", "type": "bitmap", "size": 1024, "nullable": true}, {"name": "v_095_16", "type": "fixed", "size": 3, "default": "fff"}, {"name": "v_095_18", "type": "uint8:5", "nullable": false, "default": "0x1f"}, {"name": "v_095_19", "type": "uint16:10", "nullable": false, "default": "0x3ff"}, {"name": "v_095_20", "type": "uint32:17", "nullable": false, "default": "0x3f"}, {"name": "v_095_21", "type": "uint64:35", "nullable": false, "default": "0x7ffffffff"}, {"name": "v_095_22", "type": "string", "size": 100, "nullable": true}, {"name": "v_095_23", "type": "bytes", "size": 12}]}], "super_fields": [{"name": "superfield0", "comment": "test1", "fields": {"begin": "Vertex_095_6", "end": "Vertex_095_6"}}, {"name": "superfield1", "comment": "test2", "fields": {"begin": "Vertex_095_6", "end": "Vertex_095_7"}}], "keys": [{"node": "vertexlabel_123", "name": "index_vertex_01", "fields": ["Vertex_095_6"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "vertexlabel_123", "name": "index_vertex_02", "fields": ["Vertex_095_0"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_03", "fields": ["Vertex_095_1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_04", "fields": ["Vertex_095_2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_05", "fields": ["Vertex_095_3"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_06", "fields": ["Vertex_095_4"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_07", "fields": ["Vertex_095_5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_08", "fields": ["Vertex_095_7"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_09", "fields": ["Vertex_095_8"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_10", "fields": ["Vertex_095_9"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_11", "fields": ["Vertex_095_10"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_12", "fields": ["Vertex_095_10", "Vertex_095_9"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_13", "fields": ["Vertex_095_10", "Vertex_095_1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_14", "fields": ["Vertex_095_10", "Vertex_095_2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_15", "fields": ["Vertex_095_10", "Vertex_095_3"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_16", "fields": ["Vertex_095_10", "Vertex_095_4"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "vertexlabel_123", "name": "index_vertex_17", "fields": ["Vertex_095_10", "Vertex_095_5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_02", "fields": ["v_095_0"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_03", "fields": ["v_095_1"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_04", "fields": ["v_095_2"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_05", "fields": ["v_095_3"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_06", "fields": ["v_095_4"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_07", "fields": ["v_095_5"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_08", "fields": ["v_095_7"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_09", "fields": ["v_095_8"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_10", "fields": ["v_095_9"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_11", "fields": ["v_095_10"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_12", "fields": ["v_095_10", "v_095_9"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_13", "fields": ["v_095_10", "v_095_1"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_14", "fields": ["v_095_10", "v_095_2"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_15", "fields": ["v_095_10", "v_095_3"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_16", "fields": ["v_095_10", "v_095_4"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c13", "name": "index_v_17", "fields": ["v_095_10", "v_095_5"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_02", "fields": ["f_095_0"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_03", "fields": ["f_095_1"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_04", "fields": ["f_095_2"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_05", "fields": ["f_095_3"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_06", "fields": ["f_095_4"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_07", "fields": ["f_095_5"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_08", "fields": ["f_095_7"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_09", "fields": ["f_095_8"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_10", "fields": ["f_095_9"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_11", "fields": ["f_095_10"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_12", "fields": ["f_095_10", "f_095_9"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_13", "fields": ["f_095_10", "f_095_1"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_14", "fields": ["f_095_10", "f_095_2"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_15", "fields": ["f_095_10", "f_095_3"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_16", "fields": ["f_095_10", "f_095_4"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c12", "name": "index_f_17", "fields": ["f_095_10", "f_095_5"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_02", "fields": ["r_095_0"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_03", "fields": ["r_095_1"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_04", "fields": ["r_095_2"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_05", "fields": ["r_095_3"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_06", "fields": ["r_095_4"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_07", "fields": ["r_095_5"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_08", "fields": ["r_095_7"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_09", "fields": ["r_095_8"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_10", "fields": ["r_095_9"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_11", "fields": ["r_095_10"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_12", "fields": ["r_095_10", "r_095_9"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_13", "fields": ["r_095_10", "r_095_1"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_14", "fields": ["r_095_10", "r_095_2"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_15", "fields": ["r_095_10", "r_095_3"], "index": {"type": "none"}, "constraints": {"unique": false}}, {"node": "c11", "name": "index_r_16", "fields": ["r_095_10", "r_095_4"], "index": {"type": "none"}, "constraints": {"unique": false}}]}]