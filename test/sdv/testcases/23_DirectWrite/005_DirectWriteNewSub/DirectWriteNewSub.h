/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :直连写适配新订阅推送头文件
 Author       : lwx1036939
 Modification :
 Date         : 2023/03/07
**************************************************************************** */
#ifndef DIRECTWRITENEWSUB_H
#define DIRECTWRITENEWSUB_H

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 128
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL, *g_connSub = NULL, *g_connSub1 = NULL;
GmcStmtT *g_stmt = NULL, *g_stmtSub = NULL, *g_stmtSub1 = NULL;

const char *g_vertexLabelName = "OP_T0", *g_vertexLabelName1 = "OP_T1", *g_treeTableName = "Tree_T0",
           *g_treeTableName2 = "Tree_T1";
const char *g_vertexPkName = "pk", *g_treePkName = "Tree_PK";
const char *g_vertexSubName = "subVertexLabel", *g_vertexSubName1 = "subVertexLabel1";
const char *g_vertexSubConnName = "subVertexConnName", *g_vertexSubConnName1 = "subVertexConnName1";

const char *g_simpileVertexFilePath1 = "schemafile/SimpleTable_001.gmjson";
const char *g_simpileVertexFilePath2 = "schemafile/SimpleTable_002.gmjson";
const char *g_simpileVertexFilePath3 = "schemafile/SimpleTable_003.gmjson";
const char *g_nonSimpileVertexFilePath1 = "schemafile/NonSimpleTable_001.gmjson";
const char *g_nonSimpileVertexFilePath2 = "schemafile/NonSimpleTable_002.gmjson";
const char *g_treeFilePath = "schemafile/TreeModel_001.gmjson";
const char *g_treeFilePath2 = "schemafile/TreeModel_002.gmjson";

const char *g_vertexIncSubPath = "schemafile/IncSubInfo.gmjson";
const char *g_vertexIncSubPath005 = "schemafile/IncSubInfo005.gmjson";
const char *g_vertexIncSubPath2 = "schemafile/IncSubInfo2.gmjson";
const char *g_vertexConAndSubPath = "schemafile/CondAndSubInfo.gmjson";
const char *g_vertexConAndSubPath2 = "schemafile/CondAndSubInfo2.gmjson";
const char *g_vertexConAndSubPath3 = "schemafile/CondAndSubInfo3.gmjson";
const char *g_vertexConOrSubPath3 = "schemafile/CondOrSubInfo3.gmjson";
const char *g_vertexConOrSubPath = "schemafile/CondOrSubInfo.gmjson";
const char *g_vertexConOrSubPath2 = "schemafile/CondOrSubInfo2.gmjson";
const char *g_vertexFullSubPath = "schemafile/FullSubInfo.gmjson";
const char *g_vertexReliableSubPath = "schemafile/ReliableSubInfo.gmjson";
const char *g_vertexRetryIsTrueSubPath = "schemafile/RetryIsTrueSubInfo.gmjson";
const char *g_treeIncSubPath = "schemafile/TreeIncSubInfo.gmjson";
const char *g_treeIncSubPath2 = "schemafile/TreeIncSubInfo2.gmjson";

const char *g_labelConfig = "{\"max_recordNum\":10000, \"status_merge_sub\":true}";

int g_startNum = 0, g_endNum = 10, g_updateValue = 1000, g_arrayNum = 0, g_vectorNum = 3, g_threadWait1 = 0,
    g_threadWait2 = 0, g_chanRingLen = 256;

pthread_mutex_t g_subLock1;
pthread_mutex_t g_subLock2;

int TestGetAffectRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return ret;
}

int TestCreateLabel(
    GmcStmtT *stmt, const char *filePath, const char *labelName, const char *labelConfig = g_labelConfig)
{
    int ret = 0;
    char *schema = NULL;

    if (filePath) {
        readJanssonFile(filePath, &schema);
        AW_MACRO_EXPECT_NE_INT((void *)NULL, schema);
    }

    // create table
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, labelConfig);
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }

    free(schema);
    schema = NULL;

    return ret;
}

int TestUpdateVertexLabel(const char *filePath, char *expectValue, const char *labelName)
{
    char *schema = NULL;
    readJanssonFile(filePath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl 工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    char *uWay = (char *)"online";  // 在线升级
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s  -u %s -ns %s", g_toolPath, labelName, filePath, uWay,
        g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int TestDownGradeVertexLabel(const char *labelName, int32_t schemaVersion, char *expectValue)
{
    // gmddl 工具降级表操作
    char cmd[512] = {0};
    int ret = 0;
    char *dWay = (char *)"sync";  // 同步降级
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath, labelName, schemaVersion, dWay,
        g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

/****************** 简单表 *******************/
void TestSetVertexPropertyPK(GmcStmtT *stmt, uint32_t i)
{
    uint32_t f7Value = i;
    int ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetVertexProperty(GmcStmtT *stmt, int32_t i, bool isSimTable = false)
{
    uint32_t f0Value = i;
    int ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isSimTable) {
        uint32_t f1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f8Value = i;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT32, &f8Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        unsigned char f1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value, sizeof(unsigned char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool f8Value = false;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSetT1VertexProperty(GmcStmtT *stmt, int64_t value, bool t1NewFileIsFull = true)
{
    int ret = 0;
    int64_t f9Value = value;

    if (t1NewFileIsFull) {
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    } else {
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestSetT2VertexProperty(GmcStmtT *stmt, int64_t value, bool t2NewFileIsFull = true)
{
    int64_t f9Value = value;
    int64_t f10Value = value;
    int ret = 0;

    if (t2NewFileIsFull) {
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &f9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    } else {
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &f10Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestSetIndexValue(GmcStmtT *stmt, int32_t i)
{
    int ret = 0;
    uint32_t f7Value = i;

    ret = GmcSetIndexKeyName(stmt, "pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestQueryProperty(GmcStmtT *stmt, int32_t i, bool isSimTable)
{
    int ret = 0;

    uint32_t f0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &f2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &f6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isSimTable) {
        uint32_t f1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f8Value = i;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_UINT32, &f8Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        unsigned char f1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool f8Value = false;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestQueryT1NewFileValue(GmcStmtT *stmt, int64_t value, bool t1NewFileIsExist = true, bool t1NewFileIsFull = true)
{
    int ret = 0;
    int64_t f9Value = value;
    bool isNull = false;
    if (t1NewFileIsExist) {
        if (t1NewFileIsFull) {
            ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &f9Value);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9Value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    }
}

void TestQueryT2NewFileValue(GmcStmtT *stmt, int64_t value, bool t2NewFileIsExist = true, bool t2NewFileIsFull = true)
{
    int ret = 0;
    int64_t f9Value = value;
    int64_t f10Value = value;
    bool isNull = false;

    if (t2NewFileIsExist) {
        if (t2NewFileIsFull) {
            ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_INT64, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_INT64, &f9Value);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10Value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    }
}

void TestWriteVertex(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum,
    GmcOperationTypeE operationType, bool isSimTable, bool check = true, int32_t schemaVersion = DB_MAX_UINT32)
{
    int ret = 0;

    for (int i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (operationType == GMC_OPERATION_MERGE) {
            TestSetIndexValue(stmt, i);
        } else {
            TestSetVertexPropertyPK(stmt, i);
        }
        // 设置低版本字段值
        TestSetVertexProperty(stmt, i, isSimTable);

        // 设置新增字段
        if (schemaVersion == 0) {
            TestSetT1VertexProperty(stmt, i, true);
            TestSetT2VertexProperty(stmt, i, true);
        } else if (schemaVersion == 1) {
            TestSetT1VertexProperty(stmt, i, false);
        } else if (schemaVersion == 2) {
            TestSetT2VertexProperty(stmt, i, false);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (check) {
            ret = TestGetAffectRows(stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestUpdateVertex(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum,
    GmcOperationTypeE operationType, bool isSimTable, int expNum = 1, int32_t schemaVersion = DB_MAX_UINT32)
{
    int ret = 0;

    for (int i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (operationType == GMC_OPERATION_REPLACE) {
            // 设置低版本字段值
            TestSetVertexPropertyPK(stmt, i);
            TestSetVertexProperty(stmt, i + g_updateValue, isSimTable);
        } else {
            // 设置低版本字段值
            TestSetIndexValue(stmt, i);
            TestSetVertexProperty(stmt, i + g_updateValue, isSimTable);
        }

        // 设置新增字段
        if (schemaVersion == 0) {
            TestSetT1VertexProperty(stmt, i + g_updateValue, true);
            TestSetT2VertexProperty(stmt, i + g_updateValue, true);
        } else if (schemaVersion == 1) {
            TestSetT1VertexProperty(stmt, i + g_updateValue, false);
        } else if (schemaVersion == 2) {
            TestSetT2VertexProperty(stmt, i + g_updateValue, false);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (operationType == GMC_OPERATION_UPDATE) {
            // 设置低版本字段值
            ret = TestGetAffectRows(stmt, expNum);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = TestGetAffectRows(stmt, 2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestDeleteVertex(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum,
    GmcOperationTypeE operationType, int expNum = 1, int32_t schemaVersion = DB_MAX_UINT32)
{
    int ret = 0;
    for (int i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestSetIndexValue(stmt, i);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffectRows(stmt, expNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestPkScan(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum, bool isSimTable = false,
    int32_t schemaVersion = DB_MAX_UINT32)
{
    int ret = 0;

    for (uint32_t i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestSetIndexValue(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            // 读低版本字段值
            TestQueryProperty(stmt, i, isSimTable);

            // 读新增字段值
            if (schemaVersion == 0) {
                TestQueryT1NewFileValue(stmt, i, false, true);
                TestQueryT2NewFileValue(stmt, i, false, true);
            } else if (schemaVersion == 1) {
                TestQueryT1NewFileValue(stmt, i, true, false);
            } else if (schemaVersion == 2) {
                TestQueryT2NewFileValue(stmt, i, true, false);
            }
        }
    }
}

/******************  新订阅 *************************/
void NewSubCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t f7Value = 0;

    int updateValue = 1000;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_DELETE f7Value = %u\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_AGED f7Value = %u\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    if (f7Value < 30) {
                        AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_MODIFY f7Value = %u\n", f7Value);
                    }
                    if (f7Value % 10000 == 0 && f7Value != 0) {
                        AW_FUN_Log(LOG_INFO, "FullMem NewSub GMC_SUB_EVENT_MODIFY f7Value = %u\n", f7Value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD f7Value = %u\n", f7Value);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n", info->msgType,
                        info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void NewSubCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    int updateValue = 1000, index = 0, count = 30;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;
    bool newFieldIsNull[2] = {true};
    bool newFieldIsExist[2] = {true};
    uint32_t f7Value = 0;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                userData1->scanEofNum++;
                AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_DELETE f7Value = %u\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_MODIFY f7Value = %u\n", f7Value);

                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD f7Value = %u\n", f7Value);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n", info->msgType,
                        info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestGmcSubscribe(GmcStmtT *stmt, GmcConnT *connSub, const char *filepath, const char *subName,
    SnUserDataT *userdata, bool oldVersion)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    // 创建订阅关系
    if (oldVersion == true) {
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, NewSubCallBackWithOldVersion, userdata);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (oldVersion == false) {
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, NewSubCallBackWithNewVersion, userdata);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(subInfo);
}

void NewSubCallBackWithOldVersion2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t f7Value = 0;

    int updateValue = 1000;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n", info->msgType,
                        info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestGmcSubscribe2(
    GmcStmtT *stmt, GmcConnT *connSub, const char *filepath, const char *subName, SnUserDataT *userdata)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    // 创建订阅关系
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = subInfo;
    ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, NewSubCallBackWithOldVersion2, userdata);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subInfo);
}

/****************** tree表 *******************/

void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool boolValue, char *f14Value, bool isSimTable = false)
{
    int ret = 0;
    uint64_t f1Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5Value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6Value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7Value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isSimTable == false) {
        bool f8Value = boolValue;
        ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        float f9Value = i;
        ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        double f10Value = i;
        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint64_t f11Value = i;
        ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char f12Value = 'a' + (i & 0x1A);
        ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        unsigned char f13Value = 'A' + (i & 0x1A);
        ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14Value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14Value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f17Value = i & 0xF;
        ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint16_t f18Value = i & 0xF;
        ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f18Value, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f19Value = i & 0xFF;
        ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f19Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint64_t f20Value = i & 0xFFFF;
        ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f20Value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool boolValue, char *f14Value, bool isSimTable = false)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5Value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6Value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7Value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isSimTable == false) {
        bool f8Value = boolValue;
        ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        float f9Value = i;
        ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        double f10Value = i;
        ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint64_t f11Value = i;
        ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char f12Value = 'a' + (i & 0x1A);
        ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        unsigned char f13Value = 'A' + (i & 0x1A);
        ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14Value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14Value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f17Value = i & 0xF;
        ret = GmcNodeSetPropertyByName(node, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint16_t f18Value = i & 0xF;
        ret = GmcNodeSetPropertyByName(node, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18Value, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f19Value = i & 0xFF;
        ret = GmcNodeSetPropertyByName(node, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint64_t f20Value = i & 0xFFFF;
        ret = GmcNodeSetPropertyByName(node, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20Value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcNodeSetPropertyByName_NewFile(GmcNodeT *node, int i, bool newFileIsFull = true)
{
    int ret = 0;
    int64_t f21Value = i;

    if (newFileIsFull) {
        ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_INT64, &f21Value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    } else if (newFileIsFull == false) {
        ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_INT64, &f21Value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool boolValue, char *f14Value, bool isSimTable = false)
{
    int ret = 0;
    bool isNull;
    uint64_t f1Value;

    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1Value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f2Value);

    uint32_t f3Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f3Value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0x7FFF, f4Value);

    uint16_t f5Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f5Value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6Value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((int8_t)(i & 0x7F), f6Value);

    uint8_t f7Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7Value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((uint8_t)(i & 0xFF), f7Value);

    if (isSimTable == false) {
        bool f8Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8Value, sizeof(bool), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(boolValue, f8Value);

        float f9Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9Value, sizeof(float), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i, f9Value);

        double f10Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10Value, sizeof(double), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i, f10Value);

        uint64_t f11Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11Value, sizeof(uint64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i, f11Value);

        char f12Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12Value, sizeof(char), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT((char)('a' + (i & 0x1A)), f12Value);

        unsigned char f13Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13Value, sizeof(unsigned char), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        unsigned char k = (unsigned char)('A' + (i & 0x1A));
        AW_MACRO_EXPECT_EQ_INT(k, f13Value);

        unsigned int propSize;
        ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value) + 1);

        char stringValue[100] = {0};
        ret = GmcNodeGetPropertyByName(node, (char *)"F14", stringValue, propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

        ret = GmcNodeGetPropertyByName(node, (char *)"F15", stringValue, 7, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

        ret = GmcNodeGetPropertyByName(node, (char *)"F16", stringValue, 7, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

        uint8_t f17Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value, sizeof(uint8_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xF, f17Value);

        uint16_t f18Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value, sizeof(uint16_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xF, f18Value);

        uint32_t f19Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value, sizeof(uint32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xFF, f19Value);

        uint64_t f20Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value, sizeof(uint64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f20Value);
    }
}

void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool boolValue, char *f14Value, bool isSimTable = false)
{
    int ret = 0;
    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0Value);

    uint64_t f1Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1Value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1Value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2Value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f2Value);

    uint32_t f3Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3Value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f3Value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4Value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0x7FFF, f4Value);

    uint16_t f5Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5Value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f5Value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6Value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((int8_t)(i & 0x7F), f6Value);

    uint8_t f7Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7Value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((uint8_t)(i & 0xFF), f7Value);

    if (isSimTable == false) {
        bool f8Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8Value, sizeof(bool), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(boolValue, f8Value);

        float f9Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9Value, sizeof(float), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i, f9Value);

        double f10Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10Value, sizeof(double), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i, f10Value);

        uint64_t f11Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11Value, sizeof(uint64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i, f11Value);

        char f12Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12Value, sizeof(char), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT((char)('a' + (i & 0x1A)), f12Value);

        unsigned char f13Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13Value, sizeof(unsigned char), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        unsigned char k = (unsigned char)('A' + (i & 0x1A));
        AW_MACRO_EXPECT_EQ_INT(k, f13Value);

        unsigned int propSize;
        ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14Value) + 1);

        char stringValue[100] = {0};
        ret = GmcNodeGetPropertyByName(node, (char *)"P14", stringValue, propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

        ret = GmcNodeGetPropertyByName(node, (char *)"P15", stringValue, 7, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

        ret = GmcNodeGetPropertyByName(node, (char *)"P16", stringValue, 7, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strcmp(stringValue, f14Value), 0);

        uint8_t f17Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P17", &f17Value, sizeof(uint8_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xF, f17Value);

        uint16_t f18Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P18", &f18Value, sizeof(uint16_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xF, f18Value);

        uint32_t f19Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P19", &f19Value, sizeof(uint32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xFF, f19Value);

        uint64_t f20Value;
        ret = GmcNodeGetPropertyByName(node, (char *)"P20", &f20Value, sizeof(uint64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
        AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f20Value);
    }
}

void TestGmcNodeGetPropertyByName_NewFile(GmcNodeT *node, int i, bool newFileIsExist = true, bool newFileIsFull = true)
{
    int ret = 0;
    int64_t f21Value = i;
    bool isNull;

    if (newFileIsExist) {
        if (newFileIsFull) {
            ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value, sizeof(int64_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(true, isNull);
            AW_MACRO_EXPECT_EQ_INT(i, f21Value);
        } else {
            ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value, sizeof(int64_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
            AW_MACRO_EXPECT_EQ_INT(i, f21Value);
        }
    } else {
        ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    }
}

void TestQueryOldVersion(GmcStmtT *stmt, bool boolValue, char *f14Value, int startNum, int endNum, int arrayNum,
    int vectorNum, bool isSimTable = false, int32_t schemaVersion = DB_MAX_UINT32)
{
    int32_t ret = 0;
    void *label = NULL;

    // 读取顶点
    for (int i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "Tree_T0", schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "Tree_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *t1;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 查询根节点公共部分
        TestGmcNodeGetPropertyByName_R(root, i, boolValue, f14Value, isSimTable);

        // 查询升级字段属性失败
        TestGmcNodeGetPropertyByName_NewFile(root, i, false);

        // 查询 record T1
        ret = GmcNodeGetChild(root, "T1", &t1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_p(t1, i, boolValue, f14Value, isSimTable);

        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcInsertTree(GmcStmtT *stmt, int i, bool boolValue, char *f14Value, int arrayNum, int vectorNum,
    const char *labelName, GmcOperationTypeE operationType, bool isSimTable = false,
    int32_t schemaVersion = DB_MAX_UINT32)
{
    int32_t ret = 0;

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root, *t1;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键
    TestGmcNodeSetPropertyByName_PK(root, i);
    // 设置根节点公共属性
    TestGmcNodeSetPropertyByName_R(root, i, boolValue, f14Value, isSimTable);

    // InsertOrReplace设置新增字段属性
    if (schemaVersion == 0) {
        TestGmcNodeSetPropertyByName_NewFile(root, i);
    } else if (schemaVersion == 1) {
        TestGmcNodeSetPropertyByName_NewFile(root, i, false);
    }

    // 插入 record T1
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_P(t1, i, boolValue, f14Value, isSimTable);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断影响了多少行数据
    ret = TestGetAffectRows(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void SubCallBackWithTreeOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, arrayNum = 3, vectorNum = 3, startNum = 0, endNum = 10;
    bool boolValue;
    char f14Value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    bool isSimTable;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = 128;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    // 读new
                    boolValue = 0;
                    strcpy(f14Value, "1234567");

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_MODIFY index is %d\r\n", index);

                    if (index >= 0 && index < 10) {
                        isSimTable = true;
                    } else if (index >= 10 && index < 20) {
                        isSimTable = false;
                    }

                    GmcNodeT *root, *t1;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    // 查询根节点公共部分
                    TestGmcNodeGetPropertyByName_R(root, index, boolValue, f14Value, isSimTable);

                    // 查询升级字段属性失败
                    TestGmcNodeGetPropertyByName_NewFile(root, index, false);

                    // 查询 record T1
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_p(t1, index, boolValue, f14Value, isSimTable);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    boolValue = 0;
                    strcpy(f14Value, "1234567");
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INITIAL_LOAD index is %d\r\n", index);

                    if (index >= 0 && index < 10) {
                        isSimTable = true;
                    } else if (index >= 10 && index < 20) {
                        isSimTable = false;
                    }

                    GmcNodeT *root, *t1;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    // 查询根节点公共部分
                    TestGmcNodeGetPropertyByName_R(root, index, boolValue, f14Value, isSimTable);

                    // 查询升级字段属性失败
                    TestGmcNodeGetPropertyByName_NewFile(root, index, false);

                    // 查询 record T1
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_p(t1, index, boolValue, f14Value, isSimTable);

                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestTreeGmcSubscribe(
    GmcStmtT *stmt, GmcConnT *connSub, const char *filepath, const char *subName, SnUserDataT *userdata)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    // 创建订阅关系
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = subInfo;
    ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, SubCallBackWithTreeOldVersion, userdata);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
}

void *TestCreateSub(void *args)
{
    int ret = 0;
    GmcConnT *conn3 = 0, *connSub3 = 0;
    GmcStmtT *stmt3 = 0, *stmtSub3 = 0;
    SnUserDataT *user_data1;
    const char *vertexSubConnName3 = "oldSubVertexConnName1";

    ret = testSnMallocUserData(&user_data1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn3, &stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&connSub3, &stmtSub3, 1, g_epoll_reg_info, vertexSubConnName3, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发订阅
    TestGmcSubscribe(stmt3, connSub3, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 1) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait dml");
    }

    // 校验推送次数
    sleep(3);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, 0, g_endNum + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(stmt3, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn3, stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    testSnFreeUserData(user_data1);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestSubDML(void *args)
{
    int ret = 0;
    GmcConnT *conn4 = 0;
    GmcStmtT *stmt4 = 0;

    // 创建同步连接
    ret = testGmcConnect(&conn4, &stmt4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 1) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait sub scribe");
    }

    // 执行 insert 操作
    sleep(1);
    TestWriteVertex(stmt4, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(stmt4, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(stmt4, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestDeleteVertex(stmt4, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 断连
    ret = testGmcDisconnect(conn4, stmt4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestSubDML2(void *args)
{
    int ret = 0;
    GmcConnT *conn5 = 0;
    GmcStmtT *stmt5 = 0;

    // 创建同步连接
    ret = testGmcConnect(&conn5, &stmt5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 2) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait sub scribe");
    }

    // 执行 insert 操作
    TestWriteVertex(stmt5, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_INSERT, true);
    sleep(6);
    // 执行 insert 操作
    TestWriteVertex(stmt5, g_vertexLabelName, g_endNum / 2, g_endNum, GMC_OPERATION_INSERT, true);

    // 断连
    ret = testGmcDisconnect(conn5, stmt5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestCreateSubAndUnSub(void *args)
{
    int ret = 0;
    GmcConnT *conn6 = 0, *connSub6 = 0;
    GmcStmtT *stmt6 = 0, *stmtSub6 = 0;
    SnUserDataT *user_data1;
    const char *vertexSubConnName6 = "oldSubVertexConnName1";

    ret = testSnMallocUserData(&user_data1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn6, &stmt6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&connSub6, &stmtSub6, 1, g_epoll_reg_info, vertexSubConnName6, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发订阅
    TestGmcSubscribe(stmt6, connSub6, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 2) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait dml");
    }

    sleep(3);
    // 删除订阅关系
    ret = GmcUnSubscribe(stmt6, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新下发订阅
    TestGmcSubscribe(stmt6, connSub6, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(stmt6, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(connSub6, stmtSub6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn6, stmt6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    testSnFreeUserData(user_data1);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestCreateSubAndDisConn(void *args)
{
    int ret = 0;
    GmcConnT *conn7 = 0, *connSub7 = 0;
    GmcStmtT *stmt7 = 0, *stmtSub7 = 0;
    SnUserDataT *user_data1;
    const char *vertexSubConnName7 = "oldSubVertexConnName1";

    ret = testSnMallocUserData(&user_data1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn7, &stmt7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&connSub7, &stmtSub7, 1, g_epoll_reg_info, vertexSubConnName7, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发订阅
    TestGmcSubscribe(stmt7, connSub7, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 2) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait dml");
    }

    sleep(3);
    // 断连
    ret = testGmcDisconnect(conn7, stmt7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新创建同步连接
    ret = testGmcConnect(&conn7, &stmt7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(stmt7, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(connSub7, stmtSub7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn7, stmt7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    testSnFreeUserData(user_data1);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestCreateSubAndDisSubConn(void *args)
{
    int ret = 0;
    GmcConnT *conn8 = 0, *connSub8 = 0;
    GmcStmtT *stmt8 = 0, *stmtSub8 = 0;
    SnUserDataT *user_data1;
    const char *vertexSubConnName8 = "oldSubVertexConnName1";

    ret = testSnMallocUserData(&user_data1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn8, &stmt8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&connSub8, &stmtSub8, 1, g_epoll_reg_info, vertexSubConnName8, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发订阅
    TestGmcSubscribe(stmt8, connSub8, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 2) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait dml");
    }

    sleep(3);
    // 删除订阅关系
    ret = GmcUnSubscribe(stmt8, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新下发订阅
    TestGmcSubscribe(stmt8, connSub8, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(stmt8, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(connSub8, stmtSub8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn8, stmt8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    testSnFreeUserData(user_data1);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestIncSub(void *args)
{
    int ret = 0;
    GmcConnT *conn9 = 0, *connSub9 = 0;
    GmcStmtT *stmt9 = 0, *stmtSub9 = 0;
    SnUserDataT *user_data1;
    const char *vertexSubConnName9 = "oldSubVertexConnName9";

    ret = testSnMallocUserData(&user_data1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn9, &stmt9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&connSub9, &stmtSub9, 1, g_epoll_reg_info, vertexSubConnName9, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发订阅
    TestGmcSubscribe(stmt9, connSub9, g_vertexIncSubPath, g_vertexSubName, user_data1, true);

    // 执行 insert 操作
    sleep(1);
    TestWriteVertex(stmt9, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_INSERT, true);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 1) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait condSub");
    }

    // 校验推送次数
    sleep(1);
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(stmt9, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(connSub9, stmtSub9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn9, stmt9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    testSnFreeUserData(user_data1);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

void *TestCondSub(void *args)
{
    int ret = 0;
    GmcConnT *conn10 = 0, *connSub10 = 0;
    GmcStmtT *stmt10 = 0, *stmtSub10 = 0;
    SnUserDataT *user_data1;
    const char *vertexSubConnName10 = "oldSubVertexConnName10";

    ret = testSnMallocUserData(&user_data1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn10, &stmt10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&connSub10, &stmtSub10, 1, g_epoll_reg_info, vertexSubConnName10, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发订阅
    TestGmcSubscribe(stmt10, connSub10, g_vertexConOrSubPath, g_vertexSubName1, user_data1, true);

    // 执行 insert 操作
    sleep(1);
    TestWriteVertex(stmt10, g_vertexLabelName, g_endNum / 2, g_endNum, GMC_OPERATION_INSERT, true);

    // 加锁避免线程冲突
    pthread_mutex_lock(&g_subLock2);
    g_threadWait2++;
    pthread_mutex_unlock(&g_subLock2);

    while (g_threadWait2 < 1) {
        sleep(1);
        AW_FUN_Log(LOG_INFO, "wait incSub");
    }

    // 校验推送次数
    sleep(1);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(stmt10, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(connSub10, stmtSub10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn10, stmt10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    testSnFreeUserData(user_data1);

    pthread_mutex_lock(&g_subLock2);
    g_threadWait2--;
    pthread_mutex_unlock(&g_subLock2);
    return NULL;
}

// 根据进程名字，获取进程id
int GetProcessIdByName(const char *proName)
{
    int pid = 0;
    char cmd[MAX_CMD_SIZE] = {0};
#if defined ENV_RTOSV2X
    (void)snprintf(cmd, MAX_CMD_SIZE, "ps -l | grep %s | grep -v defunct | grep -v grep | awk '{print $3}'", proName);
#else
    (void)snprintf(cmd, MAX_CMD_SIZE, "ps -ef | grep %s | grep -v defunct | grep -v grep | awk '{print $2}'", proName);
#endif
    (void)TestGetResultCommand(cmd, &pid);
    return pid;
}

void TestWaitProExit(const char *processName, uint32_t waitTime = 40)
{
    if (processName == NULL) {
        return;
    }

    int pid;
    uint32_t cycleTimes = 0;
    while (true) {
        if (cycleTimes > waitTime) {
            AW_FUN_Log(LOG_ERROR, "wait %s is exit timeout %d.", processName, cycleTimes * 3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }

        pid = GetProcessIdByName(processName);
        if (pid > 0) {
            AW_FUN_Log(LOG_INFO, "%s is exist pid %d, wait time %d", processName, pid, cycleTimes * 3);
        } else {
            AW_FUN_Log(LOG_INFO, "%s is exit successfully, time %d", processName, cycleTimes * 3);
            break;
        }

        sleep(3);
        cycleTimes++;
    }
}

void CheckSystemPrcLog(const char *logPath)
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "cat %s | grep ': Failure' | wc -l", logPath);
    int failCount = 0;
    int ret = TestGetResultCommand(cmd, &failCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (failCount != 0) {
        (void)snprintf(cmd, MAX_CMD_SIZE, "cat %s | grep ': Failure'", logPath);
        system(cmd);
        AW_FUN_Log(LOG_ERROR, "system execute proc failed, failCount %u", failCount);
        AW_MACRO_EXPECT_EQ_INT(0, failCount);
    } else {
        (void)snprintf(cmd, MAX_CMD_SIZE, "rm -rf %s", logPath);
        system(cmd);
    }
}

int TestCheckSimplePropertyNonPk(GmcStmtT *subStmt, int32_t i, int32_t addVal)
{
    int ret = 0;

    uint32_t f0Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F0", GMC_DATATYPE_UINT32, &f0Value);
    RETURN_IFERR(ret);
    uint32_t f1Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F1", GMC_DATATYPE_UINT32, &f1Value);
    RETURN_IFERR(ret);
    int8_t f2Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F2", GMC_DATATYPE_INT8, &f2Value);
    RETURN_IFERR(ret);
    uint8_t f3Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F3", GMC_DATATYPE_UINT8, &f3Value);
    RETURN_IFERR(ret);
    int16_t f4Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F4", GMC_DATATYPE_INT16, &f4Value);
    RETURN_IFERR(ret);
    uint16_t f5Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F5", GMC_DATATYPE_UINT16, &f5Value);
    RETURN_IFERR(ret);
    int32_t f6Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F6", GMC_DATATYPE_INT32, &f6Value);
    RETURN_IFERR(ret);
    uint32_t f8Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F8", GMC_DATATYPE_UINT32, &f8Value);
    RETURN_IFERR(ret);
    return 0;
}

int g_pushList[100] = {0};
int g_pushList2[100] = {0};
void TestInitPushList(int count, int initVal, int start = 0, bool isInitList2 = false)
{
    for (int i = 0 + start; i < count + start; i++) {
        g_pushList[i] = initVal;
    }

    if (isInitList2) {
        for (int i = 0 + start; i < count + start; i++) {
            g_pushList2[i] = initVal;
        }
    }
}

void TestCheckSimpleSubStmt(GmcStmtT *subStmt, int diffPk, int addVal, bool isSetPushList = true)
{
    bool isNull = false;
    int ret = 0;
    uint32_t f7Val = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "F7", &f7Val, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    AW_FUN_Log(LOG_INFO, "F7 %d", f7Val);
    ret = TestCheckSimplePropertyNonPk(subStmt, f7Val, addVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isSetPushList) {
        g_pushList[f7Val - diffPk] = f7Val;
    } else {
        g_pushList2[f7Val - diffPk] = f7Val;
    }
}

void TestCheckPushList(int count, int diffPK, int start = 0, bool isCheckList2 = false)
{
    for (int i = 0 + start; i < count + start; i++) {
        if (g_pushList[i] != i + diffPK) {
            AW_FUN_Log(LOG_ERROR, "pushList[%d] is %d, not match pk index %d", i, g_pushList[i], i + diffPK);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, -1);
        }
    }

    if (isCheckList2) {
        for (int i = 0 + start; i < count + start; i++) {
            if (g_pushList2[i] != i + diffPK) {
                AW_FUN_Log(LOG_ERROR, "pushList2[%d] is %d, not match pk index %d", i, g_pushList2[i], i + diffPK);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, -1);
            }
        }
    }
}

uint32_t g_testcaseId = 0;
void TestCaseCheckSimpleSubStmt(GmcStmtT *subStmt, uint32_t testcaseId, const GmcSubMsgInfoT *info)
{
    switch (testcaseId) {
        case 1: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 2: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            } else if (info->eventType == GMC_SUB_EVENT_AGED) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 3: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            } else if (info->eventType == GMC_SUB_EVENT_MODIFY) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 4: {
            int addVal = 0;
            int diffPk = 0;
            TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            break;
        }
        case 5: {
            if (info->eventType == GMC_SUB_EVENT_MODIFY) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 6: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 7: {
            if (info->eventType == GMC_SUB_EVENT_AGED) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            } else if (info->eventType == GMC_SUB_EVENT_MODIFY) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        default: {
            AW_FUN_Log(LOG_INFO, "default: testcase id %d, substmt is mid status, no need to check.", g_testcaseId);
        }
    }
}

void NewSnSimpleCallBackWithCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void TestCaseCheckOldVersionSimpleSubStmt(GmcStmtT *subStmt, uint32_t testcaseId, const GmcSubMsgInfoT *info)
{
    switch (testcaseId) {
        case 1: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckSimpleSubStmt(subStmt, diffPk, addVal, false);
            }
            break;
        }
        default: {
            AW_FUN_Log(LOG_INFO, "default: testcase id %d, substmt is mid status, no need to check.", g_testcaseId);
        }
    }
}

void NewSnOldVersionSimpleCallBackWithCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckOldVersionSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void TestGmcSubscribeCheckPush(GmcStmtT *stmt, GmcConnT *connSub, const char *filepath, const char *subName,
    SnUserDataT *userdata, GmcSubCallbackT userCb)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    // 创建订阅关系
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = subInfo;
    ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, userCb, userdata);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
}

int TestCheckNonSimplePropertyNonPk(GmcStmtT *subStmt, int32_t i, int32_t addVal)
{
    int ret = 0;

    uint32_t f0Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F0", GMC_DATATYPE_UINT32, &f0Value);
    RETURN_IFERR(ret);
    unsigned char f1Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F1", GMC_DATATYPE_UCHAR, &f1Value);
    RETURN_IFERR(ret);
    int8_t f2Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F2", GMC_DATATYPE_INT8, &f2Value);
    RETURN_IFERR(ret);
    uint8_t f3Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F3", GMC_DATATYPE_UINT8, &f3Value);
    RETURN_IFERR(ret);
    int16_t f4Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F4", GMC_DATATYPE_INT16, &f4Value);
    RETURN_IFERR(ret);
    uint16_t f5Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F5", GMC_DATATYPE_UINT16, &f5Value);
    RETURN_IFERR(ret);
    int32_t f6Value = i + addVal;
    ret = queryPropertyAndCompare(subStmt, "F6", GMC_DATATYPE_INT32, &f6Value);
    RETURN_IFERR(ret);
    bool f8Value = false;
    ret = queryPropertyAndCompare(subStmt, "F8", GMC_DATATYPE_BOOL, &f8Value);
    RETURN_IFERR(ret);
    return 0;
}

void TestCheckNonSimpleSubStmt(GmcStmtT *subStmt, int diffPk, int addVal, bool isSetPushList = true)
{
    bool isNull = false;
    int ret = 0;
    uint32_t f7Val = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "F7", &f7Val, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    AW_FUN_Log(LOG_INFO, "F7 %d", f7Val);
    ret = TestCheckNonSimplePropertyNonPk(subStmt, f7Val, addVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isSetPushList) {
        g_pushList[f7Val - diffPk] = f7Val;
    } else {
        g_pushList2[f7Val - diffPk] = f7Val;
    }
}

void TestCaseCheckNonSimpleSubStmt(GmcStmtT *subStmt, uint32_t testcaseId, const GmcSubMsgInfoT *info)
{
    switch (testcaseId) {
        case 1: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 2: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            } else if (info->eventType == GMC_SUB_EVENT_AGED) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 3: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            } else if (info->eventType == GMC_SUB_EVENT_MODIFY) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 4: {
            int addVal = 0;
            int diffPk = 0;
            TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            break;
        }
        case 5: {
            if (info->eventType == GMC_SUB_EVENT_MODIFY) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        case 6: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = 0;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal);
            }
            break;
        }
        default: {
            AW_FUN_Log(LOG_INFO, "default: testcase id %d, substmt is mid status, no need to check.", g_testcaseId);
        }
    }
}

void NewSnNonSimpleCbWithCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckNonSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckNonSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckNonSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckNonSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void TestCaseCheckOldVersionNonSimpleSubStmt(GmcStmtT *subStmt, uint32_t testcaseId, const GmcSubMsgInfoT *info)
{
    switch (testcaseId) {
        case 1: {
            if (info->eventType == GMC_SUB_EVENT_DELETE) {
                int addVal = g_updateValue;
                int diffPk = 0;
                TestCheckNonSimpleSubStmt(subStmt, diffPk, addVal, false);
            }
            break;
        }
        default: {
            AW_FUN_Log(LOG_INFO, "default: testcase id %d, substmt is mid status, not need check.", g_testcaseId);
        }
    }
}

void NewSnOldVersionNonSimpleCbWithCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCaseCheckOldVersionNonSimpleSubStmt(subStmt, g_testcaseId, info);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

#endif
