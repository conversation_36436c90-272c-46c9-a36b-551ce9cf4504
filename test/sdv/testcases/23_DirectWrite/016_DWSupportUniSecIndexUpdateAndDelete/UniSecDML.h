/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 23_DirectWrite/016_DWSupportUniSecIndexUpdateAndDelete
 * Theme: 直连写支持唯一二级索引更新与删除
 * Function: Heap表和聚簇容器表中唯一二级索引的更新和删除
 * Designer: 张景龙 00800427
 * Author: tangguagnming/twx1148729
 * Create: 2024-01-17
 */

#ifndef UNI_SEC_DML_H
#define UNI_SEC_DML_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <string.h>
#include <sys/timerfd.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

#define FULLTABLE 0xff
#define MAX_CMD_SIZE 1024
char g_cmd[MAX_CMD_SIZE];
SnUserDataT *userData;

GmcConnT *g_conn = NULL, *g_connSub = NULL;
GmcStmtT *g_stmt = NULL, *g_stmtSub = NULL;

const char *g_labelConfig = R"({"max_record_count":2000000})";
char *g_vertexLabelJson = NULL;
const char *g_labelName = "T39";
const char *g_labelName2 = "Vertex_check_01";

uint8_t g_wrFixed[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98,
    0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
// 建表
void testCreateTable(GmcStmtT *stmt, bool isTrue = true)
{
    int ret;
    if (isTrue) {
        readJanssonFile("./schemaFile/vertex.gmjson", &g_vertexLabelJson);
    } else {
        readJanssonFile("./schemaFile/Vertex_check_01.gmjson", &g_vertexLabelJson);
    }
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;
}
void testCreateHeap(GmcStmtT *stmt)
{
    int ret;
    readJanssonFile("./schemaFile/HeapVertex.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;
}

// 删表
void testDropTable(GmcStmtT *g_stmt, bool isTrue = true)
{
    int ret;
    if (isTrue) {
        ret = GmcDropVertexLabel(g_stmt, g_labelName);
    } else {
        ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置表'T39'各个字段的值
void SetVertexProperty(GmcStmtT *stmt, uint32_t val, bool ip4 = true)
{
    int ret = 0;
    int32_t f0 = val;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = val;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = val;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    int32_t f3 = val;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置lpm索引相关字段
    uint32_t vrID = val % 16;  // vr_id字段的取值范围是[0,15]，这里注意一下
    ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrID, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vrfIndex = val % 1024;  // vrf_index字段的取值范围是[0,1023]，这里注意一下
    ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (ip4) {
        uint32_t ipAddr = val;
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &ipAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_FIXED, g_wrFixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint8_t maskLen = val % 33;  // 掩码长度范围[0,32]
    ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void SetVertexProperty2(GmcStmtT *stmt, uint32_t val)
{
    int ret = 0;
    int32_t f0 = val;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = val;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = val;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    int32_t f3 = val;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
// 写数据
void testInsertData(GmcStmtT *g_stmt, uint64_t startNum, uint64_t endNum, bool ip4 = true)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint64_t i = startNum; i < endNum; i++) {
        SetVertexProperty(g_stmt, i, ip4);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "execute insert unsuccessfully. ret=%d; i=%d", ret, i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
}
void testInsertData2(GmcStmtT *g_stmt, uint64_t startNum, uint64_t endNum)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint64_t i = startNum; i < endNum; i++) {
        SetVertexProperty2(g_stmt, i);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "execute insert unsuccessfully. ret=%d; i=%d", ret, i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// update数据
void testUpdateData(GmcStmtT *stmt, uint64_t startNum = 0, uint64_t endNum = 10, const char *indexName = "PK",
    const char *segName = "F1")
{
    int ret;
    for (uint64_t i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t setVal = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &setVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i + 2000000;
        ret = GmcSetVertexProperty(stmt, segName, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "execute update unsuccessfully. ret=%d; i=%d", ret, i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 删数据
void testDeleteData(GmcStmtT *stmt, uint32_t startNum, uint32_t endNum, const char *indexName = "PK")
{
    int ret;
    for (uint64_t i = startNum; i < endNum; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/* ==================================== 线程并发 ==================================== */
// update
void *TestLocalhashKeyUpdate(void *args)
{
    AW_FUN_Log(LOG_STEP, "pthread start.");
    const char *indexName = "localhashKey";  // 操作的二级索引名
    const char *segName = "F2";              // 指定要操作的字段
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    
    // 创建连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint64_t i = 0; i < 10; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t setVal = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &setVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i + 1000000;
        ret = GmcSetVertexProperty(stmt, segName, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "execute update unsuccessfully. ret=%d; i=%d", ret, i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "pthread stop.");
    return NULL;
}

void *TestHashclusterKeyUpdate(void *args)
{
    AW_FUN_Log(LOG_STEP, "pthread start.");
    const char *indexName = "hashclusterKey";  // 操作的二级索引名
    const char *segName = "F1";                // 指定要操作的字段
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    
    // 创建连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint64_t i = 0; i < 10; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t setVal = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &setVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i + 2000000;
        ret = GmcSetVertexProperty(stmt, segName, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "execute update unsuccessfully. ret=%d; i=%d", ret, i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "pthread stop.");
    return NULL;
}

void *TestLocalKeyUpdate(void *args)
{
    AW_FUN_Log(LOG_STEP, "pthread start.");
    const char *indexName = "localKey";  // 操作的二级索引名
    const char *segName = "F2";          // 指定要操作的字段
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    
    // 创建连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint64_t i = 0; i < 10; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t setVal = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &setVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i + 3000000;
        ret = GmcSetVertexProperty(stmt, segName, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "execute update unsuccessfully. ret=%d; i=%d", ret, i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "pthread stop.");
    return NULL;
}

// delete
void *TestLocalhashKeyDelete(void *args)
{
    AW_FUN_Log(LOG_STEP, "pthread start.");
    const char *indexName = "localhashKey";  // 操作的二级索引名
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    
    // 创建连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint64_t i = 0; i < 3; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "pthread stop.");
    return NULL;
}

void *TestHashclusterKeyDelete(void *args)
{
    AW_FUN_Log(LOG_STEP, "pthread start.");
    const char *indexName = "hashclusterKey";  // 操作的二级索引名
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    
    // 创建连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint64_t i = 3; i < 6; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "pthread stop.");
    return NULL;
}

void *TestLocalKeyDelete(void *args)
{
    AW_FUN_Log(LOG_STEP, "pthread start.");
    const char *indexName = "localKey";  // 操作的二级索引名
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    
    // 创建连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint64_t i = 0; i < 10; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t tmp = i;
        ret = GmcSetIndexKeyName(stmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "pthread stop.");
    return NULL;
}

/* ==================================== 分区对账 ==================================== */
// 设置主键
void TestGmcSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_INT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 设置其他字段值
void TestGmcSetVertexProperty(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t value = i % 10;

    uint32_t f1Value = value;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f2Value = value;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 设置分区字段值
void TestGmcSetVertexPropertyF5(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t value = i;

    uint8_t f5Value = value % 16;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_PARTITION, &f5Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 公共变量
int g_affectRows = 0;
// 获取属性值
void TestGmcGetStmtAttr(GmcStmtT *stmt, int expectAffectRows, uint32_t cycleNum)
{
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAffectRows, g_affectRows);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("GmcGetStmtAttr Fail, cycleNum = %d.\n", cycleNum);
    }
}
// 写数据
void TestInsertVertexLabelCheck(GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelName)
{
    int ret = 0;
    uint32_t i;
    uint32_t value;

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(stmt, value);

        // set Property
        TestGmcSetVertexProperty(stmt, value);
        TestGmcSetVertexPropertyF5(stmt, value);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(stmt, 1, i);
    }
}

/**************************  订阅 ****************************/
const char *g_vertexLabelName = "OP_T0";
const char *g_vertexSubName = "subVertexLabel";
const char *g_vertexSubConnName = "subVertexConnName";
const char *g_simpileVertexFilePath1 = "schemaFile/SimpleTable_001.gmjson";
const char *g_vertexIncSubPath = "schemaFile/IncSubInfo.gmjson";
int g_startNum = 0, g_endNum = 10, g_updateValue = 1000, g_chanRingLen = 256;

int TestGetAffectRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return ret;
}

int TestCreateLabel(GmcStmtT *stmt, const char *filePath, const char *labelName,
    const char *labelConfig = g_labelConfig)
{
    int ret = 0;
    char *schema = NULL;

    if (filePath) {
        readJanssonFile(filePath, &schema);
        AW_MACRO_EXPECT_NE_INT((void *)NULL, schema);
    }

    // create table
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, labelConfig);
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }

    free(schema);
    schema = NULL;

    return ret;
}

int TestUpdateVertexLabel(const char *filePath, char *expectValue, const char *labelName)
{
    char *schema = NULL;
    readJanssonFile(filePath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl 工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    char *uWay = (char *)"online"; // 在线升级
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s  -u %s -ns %s", g_toolPath, labelName,
        filePath, uWay, g_testNameSpace);
    
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int TestDownGradeVertexLabel(const char *labelName, int32_t schemaVersion, char *expectValue)
{
    // gmddl 工具降级表操作
    char cmd[512] = {0};
    int ret = 0;
    char *dWay = (char *)"sync"; // 同步降级
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath, labelName,
        schemaVersion, dWay, g_testNameSpace);
    
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

/****************** 简单表 *******************/
void TestSetVertexPropertyPK(GmcStmtT *stmt, uint32_t i)
{
    uint32_t f7Value = i;
    int ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetVertexProperty(GmcStmtT *stmt, int32_t i, bool isSimTable = false)
{
    uint32_t f0Value = i;
    int ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isSimTable) {
        uint32_t f1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f8Value = i;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT32, &f8Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        unsigned char f1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value, sizeof(unsigned char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool f8Value = false;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSetT1VertexProperty(GmcStmtT *stmt, int64_t value, bool t1NewFileIsFull = true)
{
    int ret = 0;
    int64_t f9Value = value;

    if (t1NewFileIsFull) {
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    } else {
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestSetT2VertexProperty(GmcStmtT *stmt, int64_t value, bool t2NewFileIsFull = true)
{
    int64_t f9Value = value;
    int64_t f10Value = value;
    int ret = 0;

    if (t2NewFileIsFull) {
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &f9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    } else {
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &f10Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestSetIndexValue(GmcStmtT *stmt, int32_t i)
{
    int ret = 0;
    int32_t f6Value = i;

    ret = GmcSetIndexKeyName(stmt, "localhashKey");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f6Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestQueryProperty(GmcStmtT *stmt, int32_t i, bool isSimTable)
{
    int ret = 0;

    uint32_t f0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &f2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &f6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isSimTable) {
        uint32_t f1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f8Value = i;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_UINT32, &f8Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        unsigned char f1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool f8Value = false;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestQueryT1NewFileValue(GmcStmtT *stmt, int64_t value, bool t1NewFileIsExist = true, bool t1NewFileIsFull = true)
{
    int ret = 0;
    int64_t f9Value = value;
    bool isNull = false;
    if (t1NewFileIsExist) {
        if (t1NewFileIsFull) {
            ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &f9Value);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else {
            ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9Value, sizeof(int64_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    }
}

void TestQueryT2NewFileValue(GmcStmtT *stmt, int64_t value, bool t2NewFileIsExist = true, bool t2NewFileIsFull = true)
{
    int ret = 0;
    int64_t f9Value = value;
    int64_t f10Value = value;
    bool isNull = false;

    if (t2NewFileIsExist) {
        if (t2NewFileIsFull) {
            ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_INT64, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_INT64, &f9Value);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10Value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    }
}

void TestWriteVertex(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum,
    GmcOperationTypeE operationType, bool isSimTable, int32_t schemaVersion = DB_MAX_UINT32)
{
    for (int i = startNum; i < endNum; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (operationType == GMC_OPERATION_MERGE) {
            TestSetIndexValue(stmt, i);
        } else {
            TestSetVertexPropertyPK(stmt, i);
        }
        // 设置低版本字段值
        TestSetVertexProperty(stmt, i, isSimTable);

        // 设置新增字段
        if (schemaVersion == 0) {
            TestSetT1VertexProperty(stmt, i, true);
            TestSetT2VertexProperty(stmt, i, true);
        } else if (schemaVersion == 1) {
            TestSetT1VertexProperty(stmt, i, false);
        } else if (schemaVersion == 2) {
            TestSetT2VertexProperty(stmt, i, false);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffectRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestUpdateVertex(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum,
    GmcOperationTypeE operationType, bool isSimTable, int32_t schemaVersion = DB_MAX_UINT32)
{
    for (int i = startNum; i < endNum; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (operationType == GMC_OPERATION_REPLACE) {
            // 设置低版本字段值
            TestSetVertexPropertyPK(stmt, i);
            TestSetVertexProperty(stmt, i + g_updateValue, isSimTable);
        } else {
            // 设置低版本字段值
            TestSetIndexValue(stmt, i);
            TestSetVertexProperty(stmt, i + g_updateValue, isSimTable);
        }

        // 设置新增字段
        if (schemaVersion == 0) {
            TestSetT1VertexProperty(stmt, i + g_updateValue, true);
            TestSetT2VertexProperty(stmt, i + g_updateValue, true);
        } else if (schemaVersion == 1) {
            TestSetT1VertexProperty(stmt, i + g_updateValue, false);
        } else if (schemaVersion == 2) {
            TestSetT2VertexProperty(stmt, i + g_updateValue, false);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (operationType == GMC_OPERATION_UPDATE) {
            // 设置低版本字段值
            ret = TestGetAffectRows(stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = TestGetAffectRows(stmt, 2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestDeleteVertex(GmcStmtT *stmt, const char *labelName, int32_t startNum, int32_t endNum,
    GmcOperationTypeE operationType, int32_t schemaVersion = DB_MAX_UINT32)
{
    for (int i = startNum; i < endNum; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestSetIndexValue(stmt, i);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffectRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void SubCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t f7Value = 0;

    int updateValue = 1000, count = 30;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            
            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_DELETE f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value + updateValue, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value + updateValue, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    break;
                }
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INSERT f7Value = %d\n", f7Value);
                        TestQueryProperty(subStmt, f7Value, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INSERT f7Value = %d\n", f7Value);
                        TestQueryProperty(subStmt, f7Value, false); // 非定长表
                    } else if (f7Value >= count / 3 * 2) {
                        if (f7Value % 10000 == 0) {
                            AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INSERT f7Value = %d\n", f7Value);
                        }
                        TestQueryProperty(subStmt, f7Value, true); // 定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_UPDATE New f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value + updateValue, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value + updateValue, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value + updateValue, false);
                    
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    
                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    break;
                }
                case GMC_SUB_EVENT_MERGE:
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_REPLACE f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT:
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_REPLACE_INSERT f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE:
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_REPLACE_UPDATE New f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value + updateValue, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value + updateValue, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value + updateValue, false);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    
                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value + updateValue, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value + updateValue, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_AGE f7Value = %d\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion GMC_SUB_EVENT_INITIAL_LOAD f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        TestQueryProperty(subStmt, f7Value, true); // 定长表
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        TestQueryProperty(subStmt, f7Value, false); // 非定长表
                    }

                    TestQueryT1NewFileValue(subStmt, f7Value, false);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                userData1->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                userData1->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                userData1->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void SubCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    int updateValue = 1000, count = 30;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false, isSimTable = true;
    uint32_t f7Value = 0;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                userData1->scanEofNum++;
                AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            
            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_DELETE f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value + updateValue, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);
                    break;
                }
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INSERT f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_UPDATE New f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value + updateValue, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value + updateValue);
                    
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);
                    break;
                }
                case GMC_SUB_EVENT_MERGE:
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_REPLACE f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);
                    
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT:
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_MERGE_INSERT f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE:
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_MERGE_UPDATE Old f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value + updateValue, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value + updateValue);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value + updateValue, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_AGE f7Value = %d\n", f7Value);
                    break;
                }case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD f7Value = %d\n", f7Value);

                    if (f7Value >= 0 && f7Value < count / 3) {
                        isSimTable = true;
                    } else if (f7Value >= count / 3 && f7Value < count / 3 * 2) {
                        isSimTable = false;
                    }
                    TestQueryProperty(subStmt, f7Value, isSimTable);
                    TestQueryT1NewFileValue(subStmt, f7Value);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                userData1->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                userData1->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                userData1->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestGmcSubscribe(GmcStmtT *stmt, GmcConnT *connSub, const char *filepath,
    const char *subName, SnUserDataT *userdata, bool oldVersion)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    // 创建订阅关系
    if (oldVersion == true) {
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, SubCallBackWithOldVersion, userdata);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (oldVersion == false) {
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, SubCallBackWithNewVersion, userdata);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(subInfo);
}

/******************  新订阅 *************************/
void NewSubCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t f7Value = 0;

    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            
            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_DELETE f7Value = %u\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_MODIFY f7Value = %u\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "OldVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD f7Value = %u\n", f7Value);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestGmcNewSubscribe(GmcStmtT *stmt, GmcConnT *connSub, const char *filepath,
    const char *subName, SnUserDataT *userdata)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = subInfo;
    ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, NewSubCallBackWithOldVersion, userdata);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
}

void NewSubCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;
    uint32_t f7Value = 0;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                userData1->scanEofNum++;
                AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            
            switch (info->eventType) {
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_DELETE f7Value = %u\n", f7Value);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_MODIFY f7Value = %u\n", f7Value);

                    break;
                }case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, (char *)"F7", &f7Value, sizeof(uint32_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "NewVersion NewSub GMC_SUB_EVENT_INITIAL_LOAD f7Value = %u\n", f7Value);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestGmcSubscribeNew(GmcStmtT *stmt, GmcConnT *connSub, const char *filepath,
    const char *subName, SnUserDataT *userdata, bool oldVersion)
{
    int ret = 0;
    char *subInfo = NULL;

    readJanssonFile(filepath, &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);

    // 创建订阅关系
    if (oldVersion == true) {
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, NewSubCallBackWithOldVersion, userdata);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (oldVersion == false) {
        GmcSubConfigT tmp_sub_info;
        tmp_sub_info.subsName = subName;
        tmp_sub_info.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_sub_info, connSub, NewSubCallBackWithNewVersion, userdata);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(subInfo);
}

#endif
