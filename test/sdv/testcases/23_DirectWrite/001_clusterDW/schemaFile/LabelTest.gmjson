[{"type": "record", "name": "labelTest", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "nullable": false, "size": 16}], "keys": [{"node": "labelTest", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "labelTest", "name": "local_key", "fields": ["F1"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "labelTest", "name": "localhash_key", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "labelTest", "name": "hashcluster_key", "fields": ["F1"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}]}]