/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :聚簇容器直连写老化测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/07/19
**************************************************************************** */

#include "DwAlterCluster.h"

class DwClusterAge : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DwClusterAge::SetUpTestCase()
{
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    } else {
        system("sh $TEST_HOME/tools/start.sh");
    }
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DwClusterAge::TearDownTestCase()
{
    int ret = 0;
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    }
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    testEnvClean();
}

void DwClusterAge::SetUp()
{
    int ret = 0;
    g_conn = NULL;
    g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    char errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}

void DwClusterAge::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  : 001.写入数据，开启对账，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    ret = GmcBeginCheck(g_stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 0;
    vertexCfg.count = endValue * 3;
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 002.insert数据，开启对账，insert新数据，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    GtGeneralLabelCfg vertexCfg4 = {(int)endValue * 3, endValue * 4, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    ret = GmcBeginCheck(g_stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg4, isDefaultValue);
    ret = GmcEndCheck(g_stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 0;
    vertexCfg.count = endValue * 3;
    for (int32_t keyId = 0; keyId < 3; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
    }
    vertexCfg4.expAffectRows = endValue;
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg4.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 003.insert数据，开启对账，update数据，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    ret = GmcBeginCheck(g_stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 2;
    vertexCfg3.schemaVersion = 0;
    isDefaultValue = true;
    int32_t updateValue = 500;
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg2, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg3, isDefaultValue, updateValue);
    ret = GmcEndCheck(g_stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, false, false);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, false, false);
        vertexCfg3.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, false, false);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 004.insert数据，开启对账，replace数据，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    GtGeneralLabelCfg vertexCfg4 = {(int)endValue * 3, endValue * 4, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    ret = GmcBeginCheck(g_stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg4, isDefaultValue);
    ret = GmcEndCheck(g_stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 0;
    vertexCfg.count = endValue * 3;
    for (int32_t keyId = 0; keyId < 3; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
    }
    vertexCfg4.expAffectRows = endValue;
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg4.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 005.insert数据，开启对账，delete数据，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);

    ret = GmcBeginCheck(g_stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg3);
    ret = GmcEndCheck(g_stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 0;
    vertexCfg.count = endValue * 3;
    for (int32_t keyId = 0; keyId < 3; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 006.insert数据，开启对账，DML操作不同的数据，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    ret = GmcBeginCheck(g_stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 2;
    vertexCfg.schemaVersion = 2;
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    vertexCfg.expAffectRows = 1;
    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 2;
    vertexCfg3.schemaVersion = 0;
    isDefaultValue = true;
    int32_t updateValue = 500;
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg2, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg3, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg3);
    GtGeneralLabelCfg vertexCfg4 = {(int)endValue * 3, endValue * 4, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg4, isDefaultValue);
    ret = GmcEndCheck(g_stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg4.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg4 keyId = %d\n", keyId);
        vertexCfg4.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 007.写入数据，开启分区对账，DML操作不同的数据，结束对账，查询数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaParth = (char *)"./schemaFile/partitionSimpleLabel.gmjson";
    // 完成表升级
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/partitionSimpleLabel1.gmjson";
    int fetchNum = 0;
    int64_t startValue = 0;
    int64_t endValue = 100;
    int32_t updateVal = 500;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 对新版本insert数据
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1OldVersionSetProperty(g_stmt, i, true);
        uint8_t partion = i % 16;
        ret = GmcSetVertexProperty(g_stmt, "F14", GMC_DATATYPE_PARTITION, &partion, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t f15 = i;
        ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 开启分区对账
    uint8_t partition = 1;
    ret = GmcBeginCheck(g_stmt, g_labelName, partition);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = endValue; i < endValue * 2; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1OldVersionSetProperty(g_stmt, i, true);
        uint8_t partion = i % 16;
        ret = GmcSetVertexProperty(g_stmt, "F14", GMC_DATATYPE_PARTITION, &partion, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t f15 = i;
        ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = endValue * 2; i < endValue * 3; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_REPLACE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1OldVersionSetProperty(g_stmt, i, true);
        uint8_t partion = i % 16;
        ret = GmcSetVertexProperty(g_stmt, "F14", GMC_DATATYPE_PARTITION, &partion, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < 10; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(g_stmt, i);
        TestSimpleT1UpdateSetProperty(g_stmt, i + updateVal, true);
        uint64_t f15 = i + updateVal;
        ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 10; i < 20; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(g_stmt, i);
        TestSimpleT1UpdateSetProperty(g_stmt, i + updateVal, true);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < 10; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    //  end check
    ret = GmcEndCheck(g_stmt, g_labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
 
    //扫描新版本的数据
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    int cnt = 0;
    while (!eof) {
        ret = GmcFetch(g_stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        cnt++;
        int64_t f0Value = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (f0Value >= 20 && f0Value < endValue) {
            if (f0Value % 16 == 1) {
                AW_FUN_Log(LOG_ERROR, "age error! parttion 1 should be aged!\n");
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
            }
        }
        uint8_t partVal = f0Value % 16;
        ret = queryPropertyAndCompare(g_stmt, "F14", GMC_DATATYPE_PARTITION, &partVal);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (f0Value >= 20 && f0Value < endValue) {
            uint64_t f15Value = f0Value;
            ret = queryPropertyAndCompare(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15Value);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            TestSimpleT1UpdateGetOldPropertyByName(g_stmt, f0Value, true);
        } else if (f0Value >= 0 && f0Value < 20){
            if (f0Value >= 0 && f0Value < 10) {
                AW_FUN_Log(LOG_ERROR, "age error! index 0-9 should be deleted!\n");
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
            } else {
                uint64_t f15Value = f0Value;
                ret = queryPropertyAndCompare(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15Value);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret); 
            }
            TestSimpleT1UpdateGetOldPropertyByName(g_stmt, f0Value + updateVal, true);
        }
        else {
            TestSimpleT1UpdateGetOldPropertyByName(g_stmt, f0Value, true);
            uint8_t partVal = f0Value % 16;
            ret = queryPropertyAndCompare(g_stmt, "F14", GMC_DATATYPE_PARTITION, &partVal);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (f0Value >= endValue * 2 && f0Value < endValue * 3) {
                ret = queryPropertyAndCompare(g_stmt, "F15", GMC_DATATYPE_UINT64, NULL);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                uint64_t f15Value = f0Value;
                ret = queryPropertyAndCompare(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15Value);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    AW_MACRO_EXPECT_EQ_INT(endValue * 3 - 80 / 16 - 10, cnt);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void *TestAgeThread(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    pthread_barrier_wait(&g_barrierT);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, g_labelName3, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = GmcEndCheck(stmt, g_labelName3, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *GeneralInsertDwThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200000;
    int32_t endValue = 200100;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, g_schemaVersion, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root = NULL, *r1 = NULL, *r2 = NULL;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty(root, i, true);
        ret = GmcNodeGetChild(root, "R1", &r1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralRNodeSetProperty(r1, i);
        // InsertOrReplace设置新增字段属性
        ret = GmcNodeGetChild(root, "R2", &r2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1SetProperty(root, i);
        TestGeneralT2SetProperty(root, i);
        TestGeneralRNodeSetProperty(r2, i);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = TestGetAffactRows(stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_FUN_Log(LOG_DEBUG, "dw replace failed!!! i = %d\n", i);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *GeneralInsertCsThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200100;
    int32_t endValue = 200200;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root = NULL, *r1 = NULL, *r2 = NULL;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty(root, i, true);
        ret = GmcNodeGetChild(root, "R1", &r1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralRNodeSetProperty(r1, i);
        // InsertOrReplace设置新增字段属性
        ret = GmcNodeGetChild(root, "R2", &r2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1SetProperty(root, i);
        TestGeneralT2SetProperty(root, i);
        TestGeneralRNodeSetProperty(r2, i);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = TestGetAffactRows(stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_FUN_Log(LOG_DEBUG, "cs replace failed!!! i = %d\n", i);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

int g_shouldCnt = 0;
int g_agedCnt = 0;
int g_readCnt = 0;
void TestGmsysviewLabelAgeNum(char *fileName)
{
    char command[512] = {0};
    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    int32_t ret = 0;

    FILE *pf = fopen(fileName, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "fopen(%s) error\n", fileName);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, pf);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "SHOULD_AGED_CNT:") == 0) {
            if (str[1]) {
                g_shouldCnt = atoi(str[1]);
            }
        } else if (strcmp(str[0], "REAL_AGED_CNT:") == 0) {
            if (str[1]) {
                g_agedCnt = atoi(str[1]);
            }
        } else if (strcmp(str[0], "RECORD_CNT:") == 0) {
            if (str[1]) {
                g_readCnt = atoi(str[1]);
            }
        } 
    }

    ret = fclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "fclose(%d) error.\n", ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void PartitionTGmsysviewLabelAgeNum(char *fileName)
{
    char command[512] = {0};
    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    int32_t ret = 0;
    int32_t shouldCnt[16] = {0};
    int32_t agedCnt[16] = {0};
    int32_t recordCnt[16] = {0};
    uint8_t id = 0;
    int num = 0;
    FILE *pf = fopen(fileName, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "fopen(%s) error\n", fileName);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, pf);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "PARTITION_ID:") == 0) {
            if (str[1]) {
                id = atoi(str[1]);
                AW_FUN_Log(LOG_INFO, "get partition %d\n", id);
            }
            while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
                length = strlen(tmpBuff);
                while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
                    tmpBuff[length - 1] = '\0';
                    --length;
                }
                (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
                if (str[0][0] == ' ' || str[1][0] == '\0') {
                    continue;
                }
                if (strcmp(str[0], "SHOULD_AGED_CNT:") == 0) {
                    if (str[1]) {
                        shouldCnt[id] = atoi(str[1]);
                    }
                } else if (strcmp(str[0], "REAL_AGED_CNT:") == 0) {
                    if (str[1]) {
                        agedCnt[id] = atoi(str[1]);
                    }
                } else if (strcmp(str[0], "RECORD_CNT:") == 0) {
                    if (str[1]) {
                        recordCnt[id] = atoi(str[1]);
                    }
                    break;
                }
            }
            num++;
            if (num == 16) {
                AW_FUN_Log(LOG_INFO, "get partition 0--15 finish.\n");
                break;
            }
        }
    }
    for (uint8_t i = 0; i < 16; i++) {
        g_shouldCnt += shouldCnt[i];
        g_agedCnt += agedCnt[i];
        g_readCnt += recordCnt[i];
    }
    ret = fclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "fclose(%d) error.\n", ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void *PartitionTAgeThread(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    for (uint8_t id = 0; id < 16; id++) {
        ret = GmcBeginCheck(stmt, g_labelName, id);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ret = GmcEndCheck(stmt, g_labelName, id, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *SimpleTInsertDwThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200000;
    int32_t endValue = 200100;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, false);
        uint8_t partion = i % 16;
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_PARTITION, &partion, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t f15 = i;
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = TestGetAffactRows(stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_FUN_Log(LOG_DEBUG, "dw replace failed!!! i = %d\n", i);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *SimpleTInsertCsThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200100;
    int32_t endValue = 200200;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, false);
        uint8_t partion = i % 16;
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_PARTITION, &partion, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t f15 = i;
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK) {
            ret = TestGetAffactRows(stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_FUN_Log(LOG_DEBUG, "cs replace failed!!! i = %d\n", i);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

/* ****************************************************************************
 Description  : 008.insert数据，线程1开启全表对账，结束对账与线程2直连写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";
    int threadNum = 2;
    bool isFinish = false;
    pthread_t thread[threadNum];
    g_shouldCnt = 0;
    g_agedCnt = 0;
    g_readCnt = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    ret = pthread_create(&thread[0], NULL, TestAgeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, GeneralInsertDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    sleep(2);
    // 查询老化的数据量
    char command[512];
    char const *viewName = "V\\$CATA_VERTEX_LABEL_CHECK_INFO";
    (void)snprintf(command, 512,
                   "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=%s >output.txt", g_toolPath, viewName, g_labelName3);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmsysviewLabelAgeNum((char *)"output.txt");

    uint32_t fetchNum = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, g_schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    AW_FUN_Log(LOG_INFO, "fetchNum(%d), g_readCnt: %d, g_agedCnt: %d\n", fetchNum, g_readCnt, g_agedCnt);
    AW_MACRO_EXPECT_EQ_INT(g_readCnt, fetchNum);
    g_shouldCnt = 0;
    g_agedCnt = 0;
    g_readCnt = 0;
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 009.insert数据，线程1开启16个分区对账，结束对账与线程2进行16个分区的直连写和线程3CS写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaParth = (char *)"./schemaFile/partitionSimpleLabel.gmjson";
    // 完成表升级
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/partitionSimpleLabel1.gmjson";
    uint32_t fetchNum = 0;
    int64_t startValue = 0;
    int64_t endValue = 100;
    int32_t threadNum = 3;
    bool isFinish = false;
    pthread_t thread[threadNum];
    g_shouldCnt = 0;
    g_agedCnt = 0;
    g_readCnt = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 对新版本insert数据
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1OldVersionSetProperty(g_stmt, i, true);
        uint8_t partion = i % 16;
        ret = GmcSetVertexProperty(g_stmt, "F14", GMC_DATATYPE_PARTITION, &partion, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t f15 = i;
        ret = GmcSetVertexProperty(g_stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    ret = pthread_create(&thread[0], NULL, PartitionTAgeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, SimpleTInsertDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[2], NULL, SimpleTInsertCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    sleep(2);
    // 查询老化的数据量
    char command[512];
    char const *viewName = "V\\$CATA_VERTEX_LABEL_CHECK_INFO";
    (void)snprintf(command, 512,
                   "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=%s >output.txt", g_toolPath, viewName, g_labelName);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PartitionTGmsysviewLabelAgeNum((char *)"output.txt");

    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, g_schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    AW_FUN_Log(LOG_INFO, "fetchNum(%d), g_readCnt: %d, g_agedCnt: %d\n", fetchNum, g_readCnt, g_agedCnt);
    AW_MACRO_EXPECT_EQ_INT(g_readCnt, fetchNum);
    g_shouldCnt = 0;
    g_agedCnt = 0;
    g_readCnt = 0;
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 010.insert数据，线程1开启全表对账，结束对账与线程2直连写和线程3CS写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwClusterAge, DW_006_005_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";
    int threadNum = 3;
    bool isFinish = false;
    pthread_t thread[threadNum];
    g_shouldCnt = 0;
    g_agedCnt = 0;
    g_readCnt = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    ret = pthread_create(&thread[0], NULL, TestAgeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, GeneralInsertDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[2], NULL, GeneralInsertCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    sleep(2);
    // 查询老化的数据量
    char command[512];
    char const *viewName = "V\\$CATA_VERTEX_LABEL_CHECK_INFO";
    (void)snprintf(command, 512,
                   "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=%s >output.txt", g_toolPath, viewName, g_labelName3);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmsysviewLabelAgeNum((char *)"output.txt");

    uint32_t fetchNum = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, g_schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    AW_FUN_Log(LOG_INFO, "fetchNum(%d), g_readCnt: %d, g_agedCnt: %d\n", fetchNum, g_readCnt, g_agedCnt);
    AW_MACRO_EXPECT_EQ_INT(g_readCnt, fetchNum);
    g_shouldCnt = 0;
    g_agedCnt = 0;
    g_readCnt = 0;
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
