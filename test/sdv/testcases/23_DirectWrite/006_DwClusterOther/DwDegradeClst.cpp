/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构降级直连写聚簇容器基本功能测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/07/14
**************************************************************************** */

#include "DwAlterCluster.h"

class DwDegradeClst : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DwDegradeClst::SetUpTestCase()
{
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    } else {
        system("sh $TEST_HOME/tools/start.sh");
    }
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DwDegradeClst::TearDownTestCase()
{
    int ret = 0;
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    }
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    testEnvClean();
}

void DwDegradeClst::SetUp()
{
    int ret = 0;
    g_conn = NULL;
    g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    char errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}

void DwDegradeClst::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  : 01.表降级后的insert直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    // 降级
    ret = TestDownGradeVertexLabel(g_labelName3, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg4 = {(int)endValue * 3, endValue * 4, 1, 3, 3, 0, GMC_OPERATION_INSERT,
                                    {false, true, true}};
    GtGeneralLabelCfg vertexCfg5 = {(int)endValue * 4, endValue * 5, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg4, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg5, isDefaultValue);
    vertexCfg.expAffectRows = endValue * 5;
    vertexCfg2.expAffectRows = endValue * 5;
    vertexCfg3.expAffectRows = endValue * 5;
    vertexCfg4.expAffectRows = endValue * 5;
    vertexCfg5.expAffectRows = endValue * 5;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg3 keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg4 keyId = %d\n", keyId);
        vertexCfg4.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg5 keyId = %d\n", keyId);
        vertexCfg5.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg5, keyId, isDefaultValue);
        vertexCfg5.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg5, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 02.表降级后的replace-insert直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    // 降级
    ret = TestDownGradeVertexLabel(g_labelName3, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg4 = {(int)endValue * 3, endValue * 4, 1, 3, 3, 0, GMC_OPERATION_REPLACE,
                                    {false, true, true}};
    GtGeneralLabelCfg vertexCfg5 = {(int)endValue * 4, endValue * 5, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, true}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg4, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg5, isDefaultValue);
    vertexCfg.expAffectRows = endValue * 5;
    vertexCfg2.expAffectRows = endValue * 5;
    vertexCfg3.expAffectRows = endValue * 5;
    vertexCfg4.expAffectRows = endValue * 5;
    vertexCfg5.expAffectRows = endValue * 5;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg3 keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg4 keyId = %d\n", keyId);
        vertexCfg4.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
        vertexCfg4.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg4, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg5 keyId = %d\n", keyId);
        vertexCfg5.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg5, keyId, isDefaultValue);
        vertexCfg5.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg5, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 03.表降级后的replace-update直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, true, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, true, true}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    // 降级
    ret = TestDownGradeVertexLabel(g_labelName3, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 2;
    vertexCfg2.expAffectRows = 2;
    vertexCfg3.expAffectRows = 2;
    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 0;
    vertexCfg3.schemaVersion = 0;
    vertexCfg.optType = GMC_OPERATION_REPLACE;
    vertexCfg2.optType = GMC_OPERATION_REPLACE;
    vertexCfg3.optType = GMC_OPERATION_REPLACE;
    isDefaultValue = true;
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;

    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg3 keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 04.表降级后的update直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    // 降级
    ret = TestDownGradeVertexLabel(g_labelName3, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 0;
    vertexCfg3.schemaVersion = 0;
    isDefaultValue = true;
    int32_t updateValue = 500;
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg2, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg3, isDefaultValue, updateValue);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, false, false);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, false, false);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 05.表降级后的delete直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    // 降级
    ret = TestDownGradeVertexLabel(g_labelName3, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg);
    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg2);
    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg3);
    vertexCfg.expAffectRows = 0;
    vertexCfg2.expAffectRows = 0;
    vertexCfg3.expAffectRows = 0;
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg2, keyId, 0);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg2, keyId, 0);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg3, keyId, 0);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg3, keyId, 0);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void *SimpleSyncDegradeThread(void *args)
{
    char *expectValue = (char *)"degrade successfully";
    uint32_t schemaVersion = 0;
    pthread_barrier_wait(&g_barrierT);
    int ret = TestDownGradeVertexLabel(g_labelName, 0, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *SimpleAsyncDegradeThread(void *args)
{
    char *expectValue = (char *)"degrade successfully";
    uint32_t schemaVersion = 0;
    pthread_barrier_wait(&g_barrierT);
    int ret = TestDownGradeVertexLabel(g_labelName, 0, expectValue, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *InsertDwThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200000;
    int32_t endValue = 200100;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, true);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *InsertCsThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200100;
    int32_t endValue = 200200;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, true);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *ReplaceDwThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200200;
    int32_t endValue = 200300;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, true);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *ReplaceCsThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 200300;
    int32_t endValue = 200400;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, true);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *UpdateDwThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 0;
    int32_t endValue = 100;
    int32_t updateValue = 100000;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(stmt, i);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, false);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *UpdateCsThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 100;
    int32_t endValue = 200;
    int32_t updateValue = 100000;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(stmt, i);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, false);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *DeleteDwThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t startValue = 0;
    int32_t endValue = 100;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_barrier_wait(&g_barrierT);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

/* ****************************************************************************
 Description  : 06.表同步降级过程中进行直连写insert并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    int threadNum = 2;
    pthread_t thread[threadNum];
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);

    ret = pthread_create(&thread[0], NULL, SimpleSyncDegradeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, InsertDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否降级成功
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue, {false}};

    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 07.表异步降级过程中进行replace直连写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    int threadNum = 2;
    pthread_t thread[threadNum];
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);

    ret = pthread_create(&thread[0], NULL, SimpleAsyncDegradeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, ReplaceDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否降级成功
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue, {false}};

    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 08.表异步降级过程中进行update直连写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    int threadNum = 2;
    pthread_t thread[threadNum];
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);

    ret = pthread_create(&thread[0], NULL, SimpleAsyncDegradeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, UpdateDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否降级成功
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    GtSimplelabelCfgRead pkReadCfg = {100, endValue, 0, (int32_t)endValue, {false}};

    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 09.表同步降级过程中进行delete直连写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    int threadNum = 2;
    pthread_t thread[threadNum];
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);

    ret = pthread_create(&thread[0], NULL, SimpleSyncDegradeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, DeleteDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否降级成功
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    GtSimplelabelCfgRead pkReadCfg = {100, endValue, 0, (int32_t)endValue, {false}};

    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 10.表异步降级过程中进行直连写和CS写并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    int threadNum = 7;
    pthread_t thread[threadNum];
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);

    ret = pthread_create(&thread[0], NULL, SimpleAsyncDegradeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, InsertDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[2], NULL, ReplaceCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[3], NULL, ReplaceDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[4], NULL, InsertCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[5], NULL, UpdateDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[6], NULL, UpdateCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否降级成功
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    GtSimplelabelCfgRead pkReadCfg = {200, endValue, 0, (int32_t)endValue, {false}};

    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void *SimpleLabelDegradeDemoteMergeThread(void *args)
{
    char *expectValue = (char *)"degrade successfully";
    uint32_t schemaVersion = 0;
    pthread_barrier_wait(&g_barrierT);
    int ret = TestDownGradeVertexLabel(g_labelName, 1, expectValue, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestDownGradeVertexLabel(g_labelName, 0, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

/* ****************************************************************************
 Description  : 11.表降级合并过程中进行直连写insert/replace/update/delete并发
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 1000;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    char *simpleParth3 = (char *)"./schemaFile/SimpleLabelUpgrade2.gmjson";
    int threadNum = 7;
    pthread_t thread[threadNum];
    pthread_barrier_init(&g_barrierT, NULL, threadNum);
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(simpleParth3, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg2, 2, false);

    ret = pthread_create(&thread[0], NULL, SimpleLabelDegradeDemoteMergeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[1], NULL, InsertDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[2], NULL, ReplaceCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[3], NULL, ReplaceDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[4], NULL, InsertCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[5], NULL, UpdateDwThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thread[6], NULL, UpdateCsThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否降级成功
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    GtSimplelabelCfgRead pkReadCfg = {200, endValue, 0, (int32_t)endValue, {false}};

    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 12.表跨版本降级后老版本的insert/replace/update/delete直连写并check数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwDegradeClst, DW_006_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    char *simpleParth3 = (char *)"./schemaFile/SimpleLabelUpgrade2.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(simpleParth3, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg2, 2, false);
    // 降级
    ret = TestDownGradeVertexLabel(g_labelName, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 3, endValue * 4, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg3, 0, false);
    vertexCfg2.expAffectRows = 2;
    vertexCfg2.optType = GMC_OPERATION_REPLACE;
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg2, 0, true);

    int32_t updateValue = 500;
    GtSimplelabelCfgT vertexCfg4 = {startValue, endValue * 4, 0, 1, GMC_OPERATION_UPDATE};
    TestSimpleTDirectWriteUpdate(g_stmt, g_labelName, vertexCfg4, 0, false, updateValue);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue * 4, 0, (int32_t)endValue * 4, {true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, false, updateValue, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
