/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构降级合并测试头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/03/23
**************************************************************************** */
#ifndef DW_UP_DEGRADE_H
#define DW_UP_DEGRADE_H

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "../../18_Upgrade/002_VertexLabelDowngrade/VertexLabelDowngrade.h"

static const char *g_resPoolTestName = "resource_pool_test";
static const char *g_resPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 1,
        "capacity" : 400,
        "order" : 0,
        "alloc_type" : 0
    })";

pthread_barrier_t g_barrierT;
uint32_t g_schemaVersion = 0xFFFFFFFF;
char *g_vertexLabelConfig = (char *)R"({"supportUndeterminedLength":false,"isFastReadUncommitted":1,
    "enableTableLock":0})";

void TestSimpleT1NewFieldSetSuccess(GmcStmtT *stmt, int64_t i)
{
    int64_t f14Value = i;
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT2NewFieldSetSuccess(GmcStmtT *stmt, int64_t i)
{
    int64_t f14Value = i;
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t f15Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_INT64, &f15Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleTDirectWrite(
    GmcStmtT *stmt, char *labelName, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion, bool isDefaultValue = true)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, g_schemaVersion, optType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            TestSimpleT1SetPk(stmt, i);
        }
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewFieldSetSuccess(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetSuccess(stmt, i);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "line: %d i=%d\n", i, __LINE__);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleTDirectWriteUpdate(
    GmcStmtT *stmt, char *labelName, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion, bool isDefaultValue = true,
    int32_t updateValue = 0)
{
    int32_t ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, g_schemaVersion, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(stmt, i);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewFieldSetSuccess(stmt, i + updateValue);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "line: %d i=%d\n", i, __LINE__);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleTDirectWriteDelete(
    GmcStmtT *stmt, char *labelName, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, g_schemaVersion, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "line: %d i=%d\n", i, __LINE__);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT1NewVersionGetNewValue1(GmcStmtT *stmt, int64_t i, bool fieldIsNull = false)
{
    int64_t f14Value = i;
    int64_t f14ValueR = 0;
    bool isNull = false;
    int32_t ret = 0;
    if (!fieldIsNull) {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14ValueR, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(f14Value, f14ValueR);
    } else {
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT1NewVersionGetNewValue2(GmcStmtT *stmt, int64_t i, bool fieldIsNull = false)
{
    int64_t f14Value = i;
    int64_t f14ValueR = 0;
    bool isNull = false;
    int32_t ret = 0;
    if (!fieldIsNull) {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14ValueR, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(f14Value, f14ValueR);
    } else {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14ValueR, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, f14ValueR);
    }
}

void TestSimpleT1NewVersionGetFaild(GmcStmtT *stmt, int64_t value)
{
    int64_t f14 = value;
    int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

int TestSimpleTNewOldVersionScan(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion, uint32_t keyId,
                                 bool isDefaultValue = true, int32_t updateValue = 0, bool isUpdate = false)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d line: %d", g_labelName, schemaVersion, __LINE__);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            while (!isFinish) {
                fetchNum++;
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVersion == 0) {
                TestSimpleT1NewVersionGetFaild(stmt, i + updateValue);
            } else if (schemaVersion == 1) {
                if (isUpdate) {
                    TestSimpleT1NewVersionGetNewValue1(stmt, i + updateValue, fieldIsNull[0]);
                } else {
                    TestSimpleT1NewVersionGetNewValue1(stmt, i, fieldIsNull[0]);
                }
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return ret;
}

void TestSimpleT2NewVersionGetPropertyValue(GmcStmtT *stmt, int64_t value, bool fieldIsNull = false)
{
    if (!fieldIsNull) {
        int64_t f14 = value;
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value;
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value;
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value;
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value) & 0x7fff;
        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value) & 0xffff;
        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value) & 0x7f;
        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value) & 0xff;
        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value;
        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value) & 0x1f;
        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f25 = (value) & 0x3ff;
        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f26 = (value) & 0x1ffff;
        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f27 = (value) & 0x1ffffffff;
        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        bool f28 = value;
        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value;
        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value;
        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT2NewVersionGetPropertyValue2(GmcStmtT *stmt, int64_t value, bool fieldIsNull = false)
{
    if (!fieldIsNull) {
        int64_t f14 = value;
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value;
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value;
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value;
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value) & 0x7fff;
        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value) & 0xffff;
        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value) & 0x7f;
        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value) & 0xff;
        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value;
        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value) & 0x1f;
        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f25 = (value) & 0x3ff;
        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f26 = (value) & 0x1ffff;
        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f27 = (value) & 0x1ffffffff;
        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        bool f28 = value;
        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value;
        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value;
        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int64_t f14 = 0;
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = 0;
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = 0;
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = 0;
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = 0;
        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = 0;
        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = 0;
        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = 0;
        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = 0;
        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = 0;
        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f25 = 0;
        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f26 = 0;
        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f27 = 0;
        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        bool f28 = 0;
        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = 0;
        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = 0;
        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}


int TestSimpleT2NewOldVersionScan(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVer, uint32_t keyId,
                                  bool isDefaultValue = true, int32_t updateValue = 0, bool isUpdate = false)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVer, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d startPkVal %d", g_labelName, schemaVer, startPkVal);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            while (!isFinish) {
                fetchNum++;
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVer == 0) {
                TestSimpleT1NewVersionGetFaild(stmt, i + updateValue);
            } else if (schemaVer == 1) {
                if (isUpdate) {
                    TestSimpleT1NewVersionGetNewValue1(stmt, i + updateValue, fieldIsNull[0]);
                } else {
                    TestSimpleT1NewVersionGetNewValue1(stmt, i, fieldIsNull[0]);
                }
            } else if (schemaVer == 2) {
                if (isUpdate) {
                    TestSimpleT2NewVersionGetPropertyValue(stmt, i + updateValue, fieldIsNull[1]);
                } else {
                    TestSimpleT2NewVersionGetPropertyValue(stmt, i, fieldIsNull[1]);
                }
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return ret;
}

int TestSimpleT2NewOldVersionScan2(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVer, uint32_t keyId,
                                   bool isDefaultValue = true, int32_t updateValue = 0, bool isUpdate = false)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVer, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d startPkVal %d", g_labelName, schemaVer, startPkVal);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            while (!isFinish) {
                fetchNum++;
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVer == 0) {
                TestSimpleT1NewVersionGetFaild(stmt, i + updateValue);
            } else if (schemaVer == 1) {
                if (isUpdate) {
                    TestSimpleT1NewVersionGetNewValue2(stmt, i + updateValue, fieldIsNull[0]);
                } else {
                    TestSimpleT1NewVersionGetNewValue2(stmt, i, fieldIsNull[0]);
                }
            } else if (schemaVer == 2) {
                if (isUpdate) {
                    TestSimpleT2NewVersionGetPropertyValue2(stmt, i + updateValue, fieldIsNull[1]);
                } else {
                    TestSimpleT2NewVersionGetPropertyValue2(stmt, i, fieldIsNull[1]);
                }
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return ret;
}

void TestSimpleT2NewFieldSetProperty(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f16 = value;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f17 = value;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f22 = value;
    ret = GmcSetVertexProperty(stmt, "F22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f28 = value;
    ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f29 = value;
    ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f30 = value;
    ret = GmcSetVertexProperty(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT2NewOldVersionDirectWrite(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                                          bool isDefaultValue = true)
{
    int32_t ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, optType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetProperty(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int TestSimpleT2NewVersionDirectUpdate(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                                       bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, g_schemaVersion, GMC_OPERATION_UPDATE);
        RETURN_IFERR(ret);
        TestSimpleT1PkIndexSet(stmt, i);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetProperty(stmt, i + updateValue);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

void SubSimpleTCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 200;
    int updateValue = 500;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2) {
                                newFieldIsNull[0] = false;
                            } else {
                                newFieldIsNull[0] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value + updateValue, newFieldIsNull[0]);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2) {
                                newFieldIsNull[0] = true;
                            } else {
                                newFieldIsNull[0] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value, newFieldIsNull[0]);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2 * 2) {
                                newFieldIsNull[0] = false;
                            } else {
                                newFieldIsNull[0] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value + updateValue, newFieldIsNull[0]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2) {
                                newFieldIsNull[0] = false;
                            } else {
                                newFieldIsNull[0] = true;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value, newFieldIsNull[0]);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2) {
                                newFieldIsNull[0] = false;
                            } else if (f0Value >= count / 2 && f0Value < count / 2 * 2) {
                                newFieldIsNull[0] = true;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value, newFieldIsNull[0]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2) {
                                newFieldIsNull[0] = true;
                            } else if (f0Value >= count / 2 && f0Value < count / 2 * 2) {
                                newFieldIsNull[0] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value, newFieldIsNull[0]);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 2) {
                                newFieldIsNull[0] = true;
                            } else {
                                newFieldIsNull[0] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetPropertyValue(subStmt, f0Value, newFieldIsNull[0]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

#pragma pack(1)
typedef struct TagSimplel0abelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
} GtSimplelabel0VertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSimplelabel2Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    int64_t f14;
    uint64_t f15;
    int32_t f16;
    uint32_t f17;
    int16_t f18;
    uint16_t f19;
    int8_t f20;
    uint8_t f21;
    uint64_t f22;
    uint8_t f23[8];
    uint8_t f24 : 5;
    uint8_t res4 : 3;
    uint16_t f25 : 10;
    uint16_t res5 : 6;
    uint32_t f26 : 17;
    uint32_t res6 : 15;
    uint64_t f27 : 33;
    uint64_t res7 : 31;
    bool f28;
    float f29;
    double f30;
} GtSimplelabel2VertexT;
#pragma pack()

typedef struct TagSimplelabelStructCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    int32_t threadId;       // 线程Id
} GtSimplelabelStructCfgT;

void GtSimplelabel2StructSetPk(GtSimplelabel2VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel2StructSetHashcluster(GtSimplelabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel2StructSetLocalhash(GtSimplelabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel2StructSetLpm4(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel2StructSetLocal(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel2StructSetOldProperty(GtSimplelabel2VertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

void GtSimplelabel2StructSetLmpProperty(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel2StructSetNewProperty(GtSimplelabel2VertexT *vertex, int64_t value, int64_t coefficient,
                                        int32_t updateValue = 0)
{
    vertex->f14 = value + updateValue;
    vertex->f15 = value + updateValue;
    vertex->f16 = value + updateValue;
    vertex->f17 = value + updateValue;
    vertex->f18 = (value + updateValue) & 0x7fff;
    vertex->f19 = (value + updateValue) & 0xffff;
    vertex->f20 = (value + updateValue) & 0x7f;
    vertex->f21 = (value + updateValue) & 0xff;
    vertex->f22 = value + updateValue;
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        vertex->f23[j] = j;
    }
    vertex->f24 = (value + updateValue) & 0x1f;
    vertex->f25 = (value + updateValue) & 0x3ff;
    vertex->f26 = (value + updateValue) & 0x1ffff;
    vertex->f27 = (value + updateValue) & 0x1ffffffff;
    vertex->f28 = value + updateValue;
    vertex->f29 = value + updateValue;
    vertex->f30 = value + updateValue;
}

int GtSimplelabel2StructDW(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                           uint32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel2StructSetPk(vertex, i);
        GtSimplelabel2StructSetLmpProperty(vertex, i);
        GtSimplelabel2StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        GtSimplelabel2StructSetNewProperty(vertex, i, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel2StructUpdateDW(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                 uint32_t schemaVersion, bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_UPDATE);
        RETURN_IFERR(ret);
        GtSimplelabel2StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT2NewFieldSetProperty(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel2StructDeleteDW(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                 uint32_t schemaVersion)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
        RETURN_IFERR(ret);
        GtSimplelabel2StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void GtSimplelabel0StructSetPk(GtSimplelabel0VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel0StructSetHashcluster(GtSimplelabel0VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel0StructSetLocalhash(GtSimplelabel0VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel0StructSetLpm4(GtSimplelabel0VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel0StructSetLocal(GtSimplelabel0VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel0StructSetOldProperty(GtSimplelabel0VertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

int GtSimplelabel0StructDW(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                           uint32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel0VertexT *vertex = (GtSimplelabel0VertexT *)malloc(sizeof(GtSimplelabel0VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel0VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel0StructSetPk(vertex, i);
        GtSimplelabel0StructSetLpm4(vertex, i);
        GtSimplelabel0StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel0StructUpdateDW(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                 uint32_t schemaVersion, bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel0VertexT *vertex = (GtSimplelabel0VertexT *)malloc(sizeof(GtSimplelabel0VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel0VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_UPDATE);
        RETURN_IFERR(ret);
        GtSimplelabel0StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}


int GtSimplelabel0StructDeleteDW(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                 uint32_t schemaVersion)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel0VertexT *vertex = (GtSimplelabel0VertexT *)malloc(sizeof(GtSimplelabel0VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel0VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
        RETURN_IFERR(ret);
        GtSimplelabel0StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

/*--------------------------------------特殊复杂表---------------------------------*/
#pragma pack(1)
typedef struct TagSpeciallabelT2VVertex {
    uint32_t v1;
    uint32_t v2;
    uint16_t v4Len;
    uint8_t *v4;
} GtSpeciallabelT2VVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelT1VVertex {
    uint32_t v1;
    uint32_t v2;
    uint16_t v4Len;
    uint8_t *v4;
    uint16_t t2VCount;
    GtSpeciallabelT2VVertexT *t2V;
} GtSpeciallabelT1VVertexT;
#pragma pack()

// 升级前
#pragma pack(1)
typedef struct TagSpeciallabelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
} GtSpeciallabelVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabel2Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;
    uint8_t *f15;
    uint16_t f16Len;
    uint8_t *f16;
} GtSpeciallabel2VertexT;
#pragma pack()

#pragma pack(1)
typedef struct recordR1 {
    int64_t p14;
    uint64_t p15;
    int32_t p16;
    uint32_t p17;
    int16_t p18;
    uint16_t p19;
    int8_t p20;
    uint8_t p21;
    uint64_t p22;
    uint8_t p23[13312];
    uint8_t p24 : 5;
    uint8_t res1 : 3;
    uint16_t p25 : 10;
    uint16_t res2 : 6;
    uint32_t p26 : 17;
    uint32_t res3 : 15;
    uint64_t p27 : 33;
    uint64_t res4 : 31;
    bool p28;
    float p29;
    double p30;
    uint16_t p32Len;
    uint8_t *p32;
    uint16_t p33Len;
    uint8_t *p33;
} GtSpeciallabelRecord;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabel4Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;
    uint8_t *f15;
    uint16_t f16Len;
    uint8_t *f16;
    uint16_t r1Flag;  // 1
    GtSpeciallabelRecord *r1;
    uint16_t f17Len;
    uint8_t *f17;
} GtSpeciallabel4VertexT;
#pragma pack()


void GtSpeciallabelStructFreeT2V(GtSpeciallabelT2VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
}

void GtSpeciallabelStructFreeT1V(GtSpeciallabelT1VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabelStructFree(GtSpeciallabelVertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
}

void GtSpeciallabel2StructFree(GtSpeciallabel2VertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
}

void GtSpeciallabel4StructFree(GtSpeciallabel4VertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
    if (vertex->f17) {
        free(vertex->f17);
    }
    if (vertex->r1->p32) {
        free(vertex->r1->p32);
    }
    if (vertex->r1->p33) {
        free(vertex->r1->p33);
    }
}

void GtSpeciallabelStructSetT2VProperty(GtSpeciallabelT2VVertexT *vertex, int32_t value, char *stringValue)
{
    vertex->v1 = value;
    vertex->v2 = value;

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
}

void GtSpeciallabelStructSetT1VProperty(GtSpeciallabelT1VVertexT *vertex, int32_t value, char *stringValue,
                                        uint16_t t2VCount)
{
    vertex->v1 = value;
    vertex->v2 = value;

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
    vertex->t2VCount = t2VCount;
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructSetT2VProperty(&vertex->t2V[i], value, stringValue);
    }
}

void GtSpeciallabelStructSetPk(GtSpeciallabelVertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabelStructSetHashcluster(GtSpeciallabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabelStructSetLocalhash(GtSpeciallabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabelStructSetLpm4(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabelStructSetLocal(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabelStructSetOldProperty(GtSpeciallabelVertexT *vertex, int64_t value, uint16_t t1VCount,
                                        uint16_t t2VCount, char *bytesValue, char *stringValue,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabelStructSetProperty(GtSpeciallabelVertexT *vertex, int32_t value, uint16_t t1VCount,
                                     uint16_t t2VCount, char *bytesValue, char *stringValue,
                                     bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabelStructSetPk(vertex, value);
    GtSpeciallabelStructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabelStructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabelStructSetLocal(vertex, value);
    GtSpeciallabelStructSetLpm4(vertex, value);
    GtSpeciallabelStructSetOldProperty(vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                       coefficient);
}

void GtSpeciallabelStructGetLmpProperty(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabelCompareVertexPropertyVector(
    GtSpeciallabelVertexT *d, int32_t index, int64_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    uint32_t v1Value = value;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v1, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v2, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].v4, strlen(stringValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < t2Count; i++) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].t2V[i].v1, sizeof(v1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].t2V[i].v2, sizeof(v1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].t2V[i].v4,
                                         strlen(stringValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void GtSpeciallabelStructGetVector(
    GtSpeciallabelVertexT *d, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i;
    for (i = 0; i < t1Count; ++i) {
        GtSpeciallabelCompareVertexPropertyVector(d, i, index, stringValue, t1Count, t2Count);
    }
}

void GtSpeciallabelStructGetOldProperty(GtSpeciallabelVertexT *vertex, int64_t index, char *bytesValue,
                                        char *stringValue, bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f14, vertex->f14, strlen(bytesValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelStructGetVector(vertex, index, stringValue, 3, 3);
}

void GtSpeciallabel2StructSetPk(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel2StructSetHashcluster(GtSpeciallabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel2StructSetLocalhash(GtSpeciallabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel2StructSetLpm4(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel2StructSetLocal(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel2StructSetOldProperty(GtSpeciallabel2VertexT *vertex, int64_t value, uint16_t t1VCount,
                                         uint16_t t2VCount, char * bytesValue, char *stringValue,
                                         bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabel2StructSetNewProperty(GtSpeciallabel2VertexT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);
    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabel2StructSetProperty(GtSpeciallabel2VertexT *vertex, int32_t value, uint16_t t1VCount,
                                      uint16_t t2VCount, char *bytesValue, char *stringValue,
                                      bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel2StructSetPk(vertex, value);
    GtSpeciallabel2StructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocal(vertex, value);
    GtSpeciallabel2StructSetLpm4(vertex, value);
    GtSpeciallabel2StructSetOldProperty(vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                        coefficient);
    GtSpeciallabel2StructSetNewProperty(vertex, bytesValue, stringValue);
}

int GtSpeciallabelSetT2VProperty(GmcNodeT *node, uint32_t value, char *stringValue)
{
    int ret = GMERR_OK;

    uint32_t v1 = value;
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT32, &v1, sizeof(v1));
    RETURN_IFERR(ret);

    uint32_t v2 = value;
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_UINT32, &v2, sizeof(v2));
    RETURN_IFERR(ret);

    uint8_t v4[STRING_LEN] = {0};
    (void)snprintf((char *)v4, STRING_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_STRING, v4, strlen(stringValue));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

int GtSpeciallabelSetT1VProperty(GmcNodeT *node, uint32_t value, char *stringValue)
{
    int ret = GMERR_OK;

    uint32_t v1 = value;
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT32, &v1, sizeof(v1));
    RETURN_IFERR(ret);

    uint32_t v2 = value;
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_UINT32, &v2, sizeof(v2));
    RETURN_IFERR(ret);

    uint8_t v4[STRING_LEN] = {0};
    (void)snprintf((char *)v4, STRING_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_STRING, v4, strlen(stringValue));
    RETURN_IFERR(ret);
    return GMERR_OK;
}

// 以结构化的方式 replace Speciallabel 表的数据
int GtSpeciallabel2StructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                               bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        free(vertex);
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);

    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        free(t1V);
        free(vertex);
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);

        GtSpeciallabel2StructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue,
                                         isDefaultValue, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabel2StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabel2SetLpmProperty(GmcNodeT *node, int64_t i)
{
    int32_t ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 以结构化的方式 merge or update表的数据
int GtSpeciallabel2StructUpdate(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                                bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1V = NULL;
    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel2StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        if (optType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            GtSpeciallabel2SetLpmProperty(root, i);
        }
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT2UpdateSetNewProperty(root, bytesValue, stringValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabel2StructGetLmpProperty(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void CompareVertexPropertyValueVector(
    GtSpeciallabel2VertexT *d, int32_t index, int64_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    uint32_t v1Value = value;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v1, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v2, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].v4, strlen(stringValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabel2StructGetVector(
    GtSpeciallabel2VertexT *d, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i;
    for (i = 0; i < t1Count; ++i) {
        CompareVertexPropertyValueVector(d, i, index, stringValue, t1Count, t2Count);
    }
}

void GtSpeciallabel2StructGetOldProperty(GtSpeciallabel2VertexT *vertex, int64_t index, char *bytesValue,
                                         char *stringValue, bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f14, vertex->f14, strlen(bytesValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabel2StructGetVector(vertex, index, stringValue, 3, 3);
}


void GtSpeciallabel2StructGetNewProperty(GtSpeciallabel2VertexT *vertex, char *bytesValue, char *stringValue,
                                         bool isNull = false)
{
    if (!isNull) {
        uint8_t f15[BYTES_LEN] = {0};
        uint8_t f16[STRING2_LEN] = {0};
        (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f15, vertex->f15, strlen(bytesValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, f16, vertex->f16, strlen(stringValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f15Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f16Len);
    }
}

int GtSpeciallabel2StructIndexScan(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                   char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    uint32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    bool fieldIsNull[8] = {0};
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    GtSpeciallabel2VertexT vertex = (GtSpeciallabel2VertexT){0};
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);
    AW_FUN_Log(LOG_INFO, "startPk=%d labelName=%s key=%d Version=%d\n", startPkVal, g_labelName2, keyId, schemaVersion);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel2StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel2StructSetHashcluster(&vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel2StructSetLocalhash(&vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel2StructSetLocal(&vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel2StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
                if (isFinish) {
                    break;
                }
                cnt++;
                ret = testStructGetVertexDeseri(stmt, &deseri);
                RETURN_IFERR(ret);
                int64_t f0Value = vertex.f0;
                if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                    GtSpeciallabel2StructGetLmpProperty(&vertex, f0Value);
                    GtSpeciallabel2StructGetOldProperty(&vertex, f0Value, bytesValue, stringValue,
                                                        isDefaultValue, coefficient);
                    GtSpeciallabel2StructGetNewProperty(&vertex, bytesValue, stringValue, fieldIsNull[1]);
                }
            }
            AW_MACRO_EXPECT_EQ_INT(vertexCfg.expAffectRows, cnt);
            deSeriFreeDynMem(&deseriCtx, true);
            return GMERR_OK;
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSpeciallabel2StructGetLmpProperty(&vertex, i);
            GtSpeciallabel2StructGetOldProperty(&vertex, i, bytesValue, stringValue,
                                                isDefaultValue, coefficient);
            GtSpeciallabel2StructGetNewProperty(&vertex, bytesValue, stringValue, fieldIsNull[1]);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

void GtSpeciallabel2NodeSet(GmcNodeT *node, uint32_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    GmcNodeT *t2Node = NULL;
    // 插入vector节点
    for (uint16_t j = 0; j < t1Count; j++) {
        int32_t ret = GmcNodeAppendElement(node, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelSetT1VProperty(node, value, stringValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 获取T2V节点
        ret = GmcNodeGetChild(node, "T2V", &t2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint16_t k = 0; k < t2Count; k++) {
            ret = GmcNodeAppendElement(t2Node, &t2Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GtSpeciallabelSetT1VProperty(t2Node, value, stringValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

int GtSpeciallabelStructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                              bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        free(vertex);
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        free(t1V);
        free(vertex);
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabelStructSetPk(vertex, i);
        GtSpeciallabelStructSetHashcluster(vertex, i, coefficient);
        GtSpeciallabelStructSetLocalhash(vertex, i, coefficient);
        GtSpeciallabelStructSetLocal(vertex, i);
        GtSpeciallabelStructSetLpm4(vertex, i);
        GtSpeciallabelStructSetOldProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                           coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabelStructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabelStructIndexScan(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                  char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GtSpeciallabelVertexT vertex = (GtSpeciallabelVertexT){0};
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);
    AW_FUN_Log(LOG_INFO, "scan labelName %s keyId: %d schemaVersion: %d\n", g_labelName2, keyId, schemaVersion);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabelStructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabelStructSetHashcluster(&vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabelStructSetLocalhash(&vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabelStructSetLocal(&vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabelStructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
                if (isFinish) {
                    break;
                }
                cnt++;
                ret = testStructGetVertexDeseri(stmt, &deseri);
                RETURN_IFERR(ret);
                int64_t f0Value = vertex.f0;
                if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                    GtSpeciallabelStructGetLmpProperty(&vertex, f0Value);
                    GtSpeciallabelStructGetOldProperty(&vertex, f0Value, bytesValue, stringValue,
                                                       isDefaultValue, coefficient);
                }
            }
            AW_MACRO_EXPECT_EQ_INT(vertexCfg.expAffectRows, cnt);
            deSeriFreeDynMem(&deseriCtx, true);
            return GMERR_OK;
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSpeciallabelStructGetLmpProperty(&vertex, i);
            GtSpeciallabelStructGetOldProperty(&vertex, i, bytesValue, stringValue,
                                               isDefaultValue, coefficient);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

void GtSpeciallabel4StructSetPk(GtSpeciallabel4VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel4StructSetHashcluster(GtSpeciallabel4VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel4StructSetLocalhash(GtSpeciallabel4VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel4StructSetLpm4(GtSpeciallabel4VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel4StructSetLocal(GtSpeciallabel4VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel4StructSetOldProperty(GtSpeciallabel4VertexT *vertex, int64_t value, uint16_t t1VCount,
                                         uint16_t t2VCount, char *bytesValue, char *stringValue,
                                         bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabel4StructSetMidProperty(GtSpeciallabel4VertexT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);
    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabel4StructSetNewProperty(GtSpeciallabel4VertexT *vertex, int32_t v, bool boolValue)
{
    // record子节点
    vertex->r1Flag = 1;
    vertex->r1->p14 = (int64_t)v;                                              // int64   8
    vertex->r1->p15 = (uint64_t)v + 0xFFFFFFFF;                                // uint64  8
    vertex->r1->p16 = v;                                                       // int32   4
    vertex->r1->p17 = v;                                                       // uint32  4
    vertex->r1->p18 = v & 0x7FFF;                                              // int16   2
    vertex->r1->p19 = v & 0xFFFF;                                              // uint16  2
    vertex->r1->p20 = v & 0x7F;                                                // int8    1
    vertex->r1->p21 = v & 0xFF;                                                // uint8   1
    vertex->r1->p22 = (uint64_t)v + 0xFFFFFFFF;
    for (int j = 0; j < 13312; j++) {
        vertex->r1->p23[j] = j % 0xFF;
    }
    vertex->r1->p24 = v & 0x1F;
    vertex->r1->p25 = v & 0x3FF;
    vertex->r1->p26 = v & 0x1FFFF;
    vertex->r1->p27 = v & 0x1FFFFFFFF;
    vertex->r1->p28 = boolValue;                                              // boolean 1
    vertex->r1->p29 = v;                                                       // float   4
    vertex->r1->p30 = v;                                                      // double  8

    vertex->r1->p32Len = 256;                                         // bytes
    if (!vertex->r1->p32) {
        vertex->r1->p32 = (uint8_t *)malloc(vertex->r1->p32Len + 1);
    }
    if (vertex->r1->p32 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->r1->p32 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->r1->p32, vertex->r1->p32Len + 1, "b%0255d", v);
    vertex->r1->p33Len = 256;  // string
    if (!vertex->r1->p33) {
        vertex->r1->p33 = (uint8_t *)malloc(vertex->r1->p33Len + 1);
    }
    if (vertex->r1->p33 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->r1->p33 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->r1->p33, vertex->r1->p33Len + 1, "s%0255d", v);
    vertex->f17Len = 256;  // string
    if (!vertex->f17) {
        vertex->f17 = (uint8_t *)malloc(vertex->f17Len + 1);
    }
    if (vertex->f17 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f17 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f17, vertex->f17Len + 1, "s%0255d", v);
}

void GtSpeciallabel4StructSetProperty(GtSpeciallabel4VertexT *vertex, int32_t value, uint16_t t1VCount,
                                      uint16_t t2VCount, char *bytesValue, char *stringValue,
                                      bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel4StructSetPk(vertex, value);
    GtSpeciallabel4StructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabel4StructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabel4StructSetLocal(vertex, value);
    GtSpeciallabel4StructSetLpm4(vertex, value);
    GtSpeciallabel4StructSetOldProperty(vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                        coefficient);
    GtSpeciallabel4StructSetMidProperty(vertex, bytesValue, stringValue);
    GtSpeciallabel4StructSetNewProperty(vertex, value + coefficient, isDefaultValue);
}

// 以结构化的方式 replace Speciallabel 表的数据
int GtSpeciallabel4StructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                               bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabel4VertexT *vertex = (GtSpeciallabel4VertexT *)malloc(sizeof(GtSpeciallabel4VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel4VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        free(vertex);
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        free(t1V);
        free(vertex);
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    GtSpeciallabelRecord *r1Node = (GtSpeciallabelRecord *)malloc(sizeof(GtSpeciallabelRecord));
    if (r1Node == NULL) {
        AW_FUN_Log(LOG_ERROR, "r1Node is NULL\n");
        free(t2V);
        free(t1V);
        free(vertex);
        return 1;
    }
    (void)memset(r1Node, 0, sizeof(GtSpeciallabelRecord));
    vertex->r1 = r1Node;
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);

        GtSpeciallabel4StructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue,
                                         isDefaultValue, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabel4StructFree(vertex);
    free(t2V);
    free(t1V);
    free(r1Node);
    free(vertex);
    return GMERR_OK;
}

void TestSpecialT4MostNewVersionSetNewField(GmcNodeT *node, int32_t value)
{
    char f17[265] = {0};
    (void)snprintf(f17, 265, "s%0255d", value);
    int ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, f17, strlen(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT4R1NodeFieldSetOk(GmcNodeT *node, int32_t value, bool boolValue)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcNodeSetPropertyByName(node, "P14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f15 = value;
    ret = GmcNodeSetPropertyByName(node, "P15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f16 = value;
    ret = GmcNodeSetPropertyByName(node, "P16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f17 = value;
    ret = GmcNodeSetPropertyByName(node, "P17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcNodeSetPropertyByName(node, "P18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcNodeSetPropertyByName(node, "P19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcNodeSetPropertyByName(node, "P20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcNodeSetPropertyByName(node, "P21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f22 = value;
    ret = GmcNodeSetPropertyByName(node, "P22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f23[13312] = {0};
    for (int j = 0; j < 13312; j++) {
        f23[j] = j % 0xFF;
    }
    ret = GmcNodeSetPropertyByName(node, "P23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcNodeSetPropertyByName(node, "P24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcNodeSetPropertyByName(node, "P25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcNodeSetPropertyByName(node, "P26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcNodeSetPropertyByName(node, "P27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f28 = boolValue;
    ret = GmcNodeSetPropertyByName(node, "P28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f29 = value;
    ret = GmcNodeSetPropertyByName(node, "P29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f30 = value;
    ret = GmcNodeSetPropertyByName(node, "P30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char p32[265] = {0};
    (void)snprintf(p32, 265, "b%0255d", value);
    ret = GmcNodeSetPropertyByName(node, "P32", GMC_DATATYPE_BYTES, p32, strlen(p32));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char p33[265] = {0};
    (void)snprintf(p33, 265, "s%0255d", value);
    ret = GmcNodeSetPropertyByName(node, "P33", GMC_DATATYPE_STRING, p33, strlen(p33));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int GtSpeciallabel4StructUpdate(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                                bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1V = NULL, *r1 = NULL;
    GtSpeciallabel4VertexT *vertex = (GtSpeciallabel4VertexT *)malloc(sizeof(GtSpeciallabel4VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel4VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel4StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT2UpdateSetNewProperty(root, bytesValue, stringValue);
        TestSpecialT4MostNewVersionSetNewField(root, i + coefficient);
        ret = GmcNodeGetChild(root, "R1", &r1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSpecialT4R1NodeFieldSetOk(r1, i + coefficient, isDefaultValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabel4StructDelete(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL, *r1 = NULL;
    GtSpeciallabel4VertexT *vertex = (GtSpeciallabel4VertexT *)malloc(sizeof(GtSpeciallabel4VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel4VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel4StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabelGeneralComparePropertyVector(GmcNodeT *node, int64_t value, char *stringValue)
{
    uint32_t v1Value = value;
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"V1", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V2", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V4", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabelGeneralGetVector(
    GmcNodeT *node, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i = 0;
    GmcNodeT *t2V = NULL;
    for (i = 0; i < t1Count; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabelGeneralComparePropertyVector(node, index, stringValue);
        ret = GmcNodeGetChild(node, "T2V", &t2V);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t k = 0; k < t2Count; k++) {
            ret = GmcNodeGetElementByIndex(t2V, k, &t2V);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabelGeneralComparePropertyVector(t2V, index, stringValue);
        }
    }
}

void TestSpecialT4GetR1NodeProperty(GmcNodeT *node, int32_t v, bool boolValue, bool fieldIsNull)
{
    int32_t ret = 0;
    if (!fieldIsNull) {
        int64_t p14Value = v;
        ret = queryNodePropertyAndCompare(node, (char *)"P14", GMC_DATATYPE_INT64, &p14Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool p28 = v;
        ret = queryNodePropertyAndCompare(node, "P28", GMC_DATATYPE_BOOL, &boolValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        float p29 = v;
        ret = queryNodePropertyAndCompare(node, "P29", GMC_DATATYPE_FLOAT, &p29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        double p30 = v;
        ret = queryNodePropertyAndCompare(node, "P30", GMC_DATATYPE_DOUBLE, &p30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char stringValue[265] = {0};
        (void)snprintf(stringValue, 265, "b%0255d", v);
        ret = queryNodePropertyAndCompare(node, (char *)"P32", GMC_DATATYPE_BYTES, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)snprintf(stringValue, 265, "s%0255d", v);
        ret = queryNodePropertyAndCompare(node, (char *)"P33", GMC_DATATYPE_STRING, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = queryNodePropertyAndCompare(node, (char *)"P14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P28", GMC_DATATYPE_BOOL, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P29", GMC_DATATYPE_FLOAT, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P30", GMC_DATATYPE_DOUBLE, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"P32", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"P33", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int TestSpecialT4NewOldVersionGeneralRead(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                          char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    GmcNodeT *root = NULL, *t1Node = NULL, *r1;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    AW_FUN_Log(LOG_INFO, "labelName = %s schemaVersion = %d keyId = %d", g_labelName2, schemaVersion, keyId);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + coefficient);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + coefficient);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            while (!isFinish) {
                fetchNum++;
                GtSpeciallabel2GetNode(stmt, &root, &t1Node);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                    TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + coefficient, bytesValue, isDefaultValue);
                    TestSpecialT3GetLpmProperty(root, f0Value);
                    GtSpeciallabelGeneralGetVector(t1Node, f0Value, stringValue, t1VCount, t2VCount);
                    if (schemaVersion == 0) {
                        TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
                        TestSpecialT4MostNewVersionGetNewFieldFailed(root);
                        ret = GmcNodeGetChild(root, "R1", &r1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
                    } else if (schemaVersion == 1) {
                        TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                        TestSpecialT4MostNewVersionGetNewFieldFailed(root);
                        ret = GmcNodeGetChild(root, "R1", &r1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
                    } else if (schemaVersion == 2) {
                        TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                        ret = GmcNodeGetChild(root, "R1", &r1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestSpecialT4GetNewField(root, f0Value + coefficient, fieldIsNull[2]);
                        TestSpecialT4GetR1NodeProperty(r1, f0Value + coefficient, isDefaultValue, fieldIsNull[2]);
                    }
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            GtSpeciallabel2GetNode(stmt, &root, &t1Node);
            TestSpecialT3UpdateGetOldPropertyByName(root, i + coefficient, bytesValue, isDefaultValue);
            TestSpecialT3GetLpmProperty(root, i);
            GtSpeciallabelGeneralGetVector(t1Node, i, stringValue, t1VCount, t2VCount);
            if (schemaVersion == 0) {
                TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
                TestSpecialT4MostNewVersionGetNewFieldFailed(root);
                ret = GmcNodeGetChild(root, "R1", &r1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
            } else if (schemaVersion == 1) {
                TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                TestSpecialT4MostNewVersionGetNewFieldFailed(root);
                ret = GmcNodeGetChild(root, "R1", &r1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
            } else if (schemaVersion == 2) {
                TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                ret = GmcNodeGetChild(root, "R1", &r1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestSpecialT4GetNewField(root, i + coefficient, fieldIsNull[2]);
                TestSpecialT4GetR1NodeProperty(r1, i + coefficient, isDefaultValue, fieldIsNull[2]);
            }
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    return 0;
}

/*--------------------------------------一般复杂表---------------------------------*/
// 设置属性start
void TestGeneralSetCommonProperty(GmcNodeT *node, int64_t i, bool isDefault = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefault) {
        uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralRNodeSetProperty(GmcNodeT *node, int32_t value)
{
    int ret = 0;
    int64_t p14 = value;
    ret = GmcNodeSetPropertyByName(node, "P14", GMC_DATATYPE_INT64, &p14, sizeof(p14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t p15 = value;
    ret = GmcNodeSetPropertyByName(node, "P15", GMC_DATATYPE_UINT64, &p15, sizeof(p15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1SetProperty(GmcNodeT *node, int32_t value)
{
    int ret = 0;
    int32_t f14 = value;
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_INT32, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT2SetProperty(GmcNodeT *node, int32_t value)
{
    int32_t ret = 0;
    int32_t f15 = value;
    ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_INT32, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralTUpdateCommonProperty(GmcNodeT *node, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralTOldNewVersionDirectWrite(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, bool isDefault = true)
{
    int32_t ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    AW_FUN_Log(LOG_INFO, "startValue: %d, endValue: %d operationType: %d\n", startPkVal, endValue, optType);
    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, g_schemaVersion, optType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root = NULL, *r1 = NULL, *r2 = NULL;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty(root, i, isDefault);
        ret = GmcNodeGetChild(root, "R1", &r1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralRNodeSetProperty(r1, i);
        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestGeneralT1SetProperty(root, i);
        } else if (schemaVersion == 2) {
            ret = GmcNodeGetChild(root, "R2", &r2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT1SetProperty(root, i);
            TestGeneralT2SetProperty(root, i);
            TestGeneralRNodeSetProperty(r2, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewOldVersionDirectUpdate(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, bool isDefaultValue = true,
    int32_t updateValue = 0)
{
    int32_t ret = 0;
    int32_t startValue = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, g_schemaVersion, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root = NULL, *r1 = NULL, *r2 = NULL;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Update设置公共部分属性
        TestGeneralTUpdateCommonProperty(root, i + updateValue, isDefaultValue);
        // 设置新增字段属性
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestGeneralT1SetProperty(root, i + updateValue);
        } else if (schemaVersion == 2) {
            ret = GmcNodeGetChild(root, "R2", &r2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralRNodeSetProperty(r2, i + updateValue);
            TestGeneralT1SetProperty(root, i + updateValue);
            TestGeneralT2SetProperty(root, i + updateValue);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewOldVersionDirectDelete(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg)
{
    int32_t ret = 0;
    int32_t startValue = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, g_schemaVersion, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1NewGetProperty(GmcNodeT *node, int32_t value, bool fieldIsNull)
{
    if (!fieldIsNull) {
        int32_t f14 = value;
        int32_t ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_INT32, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_INT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewGetProperty(GmcNodeT *node, int32_t value, bool fieldIsNull)
{
    if (!fieldIsNull) {
        int32_t f15 = value;
        int ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_INT32, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_INT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1NewVersionGetFaild(GmcNodeT *node, int64_t value)
{
    int32_t f14 = value;
    int32_t ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_INT32, &f14);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralT2NewVersionGetFaild(GmcNodeT *node, int64_t value)
{
    int32_t f15 = value;
    int32_t ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_INT32, &f15);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralTRNodeGetProperty(GmcNodeT *node, int32_t value, bool fieldIsNull = false)
{
    if (!fieldIsNull) {
        int64_t p14 = value;
        int ret = queryNodePropertyAndCompare(node, "P14", GMC_DATATYPE_INT64, &p14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P15", GMC_DATATYPE_UINT64, &p14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int ret = queryNodePropertyAndCompare(node, "P14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P15", GMC_DATATYPE_UINT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralTLpmKeySet(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    int64_t uiVrIndex = value;
    ret = GmcSetIndexKeyName(stmt, "lpm4_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (uiVrIndex <= MAX_MASK_LEN_16) {
        destIpAddr = ((uiVrIndex + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (uiVrIndex > MAX_MASK_LEN_16 && uiVrIndex <= MAX_MASK_LEN_24) {
        destIpAddr = ((uiVrIndex + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((uiVrIndex + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralTUpdateGetCommonPropertyByName(GmcNodeT *node, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}

void TestGeneralTGetLpmProperty(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestGeneralLocalKeySet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint32_t f3Value = 0;
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralTNewOldVersionReadNoData(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, uint32_t keyId, int32_t updateVal)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    uint32_t fetchNum = 0;
    bool isFinish = false;
    AW_FUN_Log(LOG_INFO, "scan: labelName=%s schemaVersion=%d startPk=%d", g_labelName3, schemaVersion, startPkVal);
    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i + updateVal);
        } else if (keyId == 2) {
            TestGeneralLocalhashIndexSet(stmt, i + updateVal);
        } else if (keyId == 3) {
            TestGeneralLocalKeySet(stmt, i + updateVal);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
}

int TestGeneralTNewOldVersionRead(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, uint32_t keyId,
    bool isDefaultValue = true, int32_t updateValue = 0, bool isUpdate1 = false, bool isUpdate2 = false)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    bool fieldIsNull[8] = {0};
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int localFlag = 0;
    AW_FUN_Log(LOG_INFO, "scan: labelName=%s schemaVersion=%d startPk=%d", g_labelName3, schemaVersion, startPkVal);
    for (int i = startPkVal; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestGeneralLocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestGeneralLocalKeySet(stmt, i + updateValue);
        } else if (keyId == 4) {
            TestGeneralTLpmKeySet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            while (!isFinish) {
                fetchNum++;
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            GmcNodeT *root = NULL, *r1 = NULL, *r2 = NULL;
            // 查询根节点
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralTUpdateGetCommonPropertyByName(root, i + updateValue, isDefaultValue);
            TestGeneralTGetLpmProperty(root, i);
            ret = GmcNodeGetChild(root, "R1", &r1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralTRNodeGetProperty(r1, i, false);
            if (schemaVersion == 0) {
                TestGeneralT1NewVersionGetFaild(root, i + updateValue);
                TestGeneralT2NewVersionGetFaild(root, i + updateValue);
                ret = GmcNodeGetChild(root, "R2", &r2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
            } else if (schemaVersion == 1) {
                ret = GmcNodeGetChild(root, "R2", &r2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
                TestGeneralT2NewVersionGetFaild(root, i + updateValue);
                if (isUpdate1) {
                    TestGeneralT1NewGetProperty(root, i + updateValue, fieldIsNull[1]);
                } else {
                    TestGeneralT1NewGetProperty(root, i, fieldIsNull[1]);
                }
            } else if (schemaVersion == 2) {
                if (isUpdate2) {
                    TestGeneralT2NewGetProperty(root, i + updateValue, fieldIsNull[2]);
                    ret = GmcNodeGetChild(root, "R2", &r2);
                    if (ret == GMERR_NO_DATA) {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    } else {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGeneralTRNodeGetProperty(r2, i + updateValue, fieldIsNull[2]);
                    }
                } else {
                    TestGeneralT2NewGetProperty(root, i, fieldIsNull[2]);
                    ret = GmcNodeGetChild(root, "R2", &r2);
                    if (ret == GMERR_NO_DATA) {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    } else {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGeneralTRNodeGetProperty(r2, i, fieldIsNull[2]);
                    }
                }
                if (isUpdate1) {
                    TestGeneralT1NewGetProperty(root, i + updateValue, fieldIsNull[1]);
                } else {
                    TestGeneralT1NewGetProperty(root, i, fieldIsNull[1]);
                }
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return ret;
}

#endif // DW_UP_DEGRADE_H
