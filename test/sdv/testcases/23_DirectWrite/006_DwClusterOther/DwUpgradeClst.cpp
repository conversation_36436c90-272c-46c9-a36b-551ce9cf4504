/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升级直连写聚簇容器基本功能测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/07/14
**************************************************************************** */

#include "DwAlterCluster.h"

class DwUpgradeClst : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DwUpgradeClst::SetUpTestCase()
{
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    } else {
        system("sh $TEST_HOME/tools/start.sh");
    }
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DwUpgradeClst::TearDownTestCase()
{
    int ret = 0;
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    }
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    testEnvClean();
}

void DwUpgradeClst::SetUp()
{
    int ret = 0;
    g_conn = NULL;
    g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    char errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}

void DwUpgradeClst::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  : 01.表升级后新老版本的insert直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {false}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 1, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 1, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 02.表升级后新老版本的replace-insert直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {false}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 1, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 1, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 03.表升级后新老版本的replace-update直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);
    vertexCfg1.expAffectRows = 2;
    vertexCfg0.expAffectRows = 2;
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 0, false);
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 1, true);
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 1, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 1, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 04.表升级后新老版本的update直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    int32_t updateValue = 500;
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);
    vertexCfg1.optType = GMC_OPERATION_UPDATE;
    vertexCfg0.optType = GMC_OPERATION_UPDATE;
    TestSimpleTDirectWriteUpdate(g_stmt, g_labelName, vertexCfg1, 0, false, updateValue);
    TestSimpleTDirectWriteUpdate(g_stmt, g_labelName, vertexCfg0, 1, true, updateValue);
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {false}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, false, updateValue, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 1, keyId, false, updateValue, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, true, updateValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 1, keyId, true, updateValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 05.表升级后新老版本的delete直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgrade1.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);
    vertexCfg1.optType = GMC_OPERATION_DELETE;
    vertexCfg0.optType = GMC_OPERATION_DELETE;
    TestSimpleTDirectWriteDelete(g_stmt, g_labelName, vertexCfg1, 0);
    TestSimpleTDirectWriteDelete(g_stmt, g_labelName, vertexCfg0, 1);
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, 0, {false}};
    ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg, 0, 3, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 06.表升级新增字段带默认值
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *simpleParth2 = (char *)"./schemaFile/SimpleLabelUpgradeDefault.gmjson";
    bool isFinish = false;
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(simpleParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg1, 1, true);
    GtSimplelabelCfgT vertexCfg0 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTDirectWrite(g_stmt, g_labelName, vertexCfg0, 0, false);

    GtSimplelabelCfgRead pkReadCfg2 = {startValue, endValue, 0, (int32_t)endValue * 2, {false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionScan(g_stmt, pkReadCfg2, 1, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 默认值校验
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = endValue; i < endValue * 2; i++) {
        TestSimpleT1PkIndexSet(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(g_stmt, i, false);
        TestSimpleT1GetLpmProperty(g_stmt, i);
        TestSimpleT1NewVersionGetNewValue1(g_stmt, 10, false);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 01.表添加node节点后新老版本的insert直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_INSERT,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_INSERT,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        vertexCfg.expAffectRows = endValue * 3;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg3 keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 08.表添加node节点后新老版本的replace-insert直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, true}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        vertexCfg.expAffectRows = endValue * 3;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
        vertexCfg2.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg3 keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
        vertexCfg3.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 09.表添加node节点后新老版本的replace-update直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, true, true}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    vertexCfg.expAffectRows = 2;
    vertexCfg2.expAffectRows = 2;
    vertexCfg3.expAffectRows = 2;
    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 2;
    vertexCfg2.schemaVersion = 0;
    isDefaultValue = true;
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 10.表添加node节点后新老版本的update直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);

    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 2;
    vertexCfg3.schemaVersion = 0;
    isDefaultValue = true;
    int32_t updateValue = 500;
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg2, isDefaultValue, updateValue);
    TestGeneralT2NewOldVersionDirectUpdate(g_stmt, vertexCfg3, isDefaultValue, updateValue);
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg3.expAffectRows = endValue * 3;
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg, keyId, isDefaultValue, updateValue, true, false);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg2.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg2, keyId, isDefaultValue, updateValue, true, true);
    }
    for (int32_t keyId = 0; keyId <= 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, true, true);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, false, false);
        vertexCfg3.schemaVersion = 2;
        TestGeneralTNewOldVersionRead(g_stmt, vertexCfg3, keyId, isDefaultValue, updateValue, false, false);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 11.表添加node节点后新老版本的delete直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char *generalParth1 = (char *)"./schemaFile/generalLabel1.gmjson";
    char *generalParth2 = (char *)"./schemaFile/generalLabelUpgrade.gmjson";
    char *generalParth3 = (char *)"./schemaFile/generalLabelUpgradeNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, generalParth1, g_labelName3, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth2, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(generalParth3, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
    // Insert插入数据
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 1, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 2, GMC_OPERATION_REPLACE,
                                    {false, false, false}};
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg2, isDefaultValue);
    TestGeneralTOldNewVersionDirectWrite(g_stmt, vertexCfg3, isDefaultValue);

    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg);
    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg2);
    TestGeneralT2NewOldVersionDirectDelete(g_stmt, vertexCfg3);
    vertexCfg.expAffectRows = 0;
    vertexCfg2.expAffectRows = 0;
    vertexCfg3.expAffectRows = 0;
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
        vertexCfg.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg, keyId, 0);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg2.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg2, keyId, 0);
        vertexCfg2.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg2, keyId, 0);
        vertexCfg2.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg2, keyId, 0);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg3.schemaVersion = 0;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg3, keyId, 0);
        vertexCfg3.schemaVersion = 1;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg3, keyId, 0);
        vertexCfg3.schemaVersion = 2;
        TestGeneralTNewOldVersionReadNoData(g_stmt, vertexCfg3, keyId, 0);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 12.简单表升级后新老版本的insert结构化直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    bool isFinish = false;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *) "upgrade successfully";
    uint32_t schemaVersion = 2;
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结构化写
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    GtSimplelabelStructCfgT vertexCfg1 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructDW(g_stmt, g_labelName, vertexCfg, 0, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructDW(g_stmt, g_labelName, vertexCfg1, 2, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {true, true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {false, false}};
    for (uint32_t keyId = 0; keyId < 4; keyId++) {
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 2, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 2, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 13.简单表升级后新老版本的replace-insert结构化直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    bool isFinish = false;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *) "upgrade successfully";
    uint32_t schemaVersion = 2;
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结构化写
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    GtSimplelabelStructCfgT vertexCfg1 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructDW(g_stmt, g_labelName, vertexCfg, 0, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructDW(g_stmt, g_labelName, vertexCfg1, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {true, true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {false, false}};
    for (uint32_t keyId = 0; keyId < 4; keyId++) {
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 2, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 2, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 14.简单表升级后新老版本的replace-update结构化直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    bool isFinish = false;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *) "upgrade successfully";
    uint32_t schemaVersion = 2;
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结构化写
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    GtSimplelabelStructCfgT vertexCfg1 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructDW(g_stmt, g_labelName, vertexCfg, 0, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructDW(g_stmt, g_labelName, vertexCfg1, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 2;
    vertexCfg1.expAffectRows = 2;
    ret = GtSimplelabel2StructDW(g_stmt, g_labelName, vertexCfg, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel0StructDW(g_stmt, g_labelName, vertexCfg1, 0, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {false, false}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {true, true}};
    for (uint32_t keyId = 0; keyId < 4; keyId++) {
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 2, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 2, keyId, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 15.简单表升级后新老版本的update结构化直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    bool isFinish = false;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *) "upgrade successfully";
    uint32_t schemaVersion = 2;
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    int32_t updateVal = 500;
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结构化写
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    GtSimplelabelStructCfgT vertexCfg1 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructDW(g_stmt, g_labelName, vertexCfg, 0, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructDW(g_stmt, g_labelName, vertexCfg1, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructUpdateDW(g_stmt, g_labelName, vertexCfg, 2, false, updateVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel0StructUpdateDW(g_stmt, g_labelName, vertexCfg1, 0, false, updateVal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 2, {false, false}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {false, false}};
    for (uint32_t keyId = 0; keyId < 4; keyId++) {
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, false, updateVal, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 2, keyId, false, updateVal, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 0, keyId, false, updateVal, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 2, keyId, false, updateVal, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 16.简单表升级后新老版本的delete结构化直连写并check
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DwUpgradeClst, DW_006_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    bool isFinish = false;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *) "upgrade successfully";
    uint32_t schemaVersion = 2;
    char *simpleParth1 = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    int32_t updateVal = 500;
    // 创表
    ret = TestCreateLabel(g_stmt, simpleParth1, g_labelName, g_vertexLabelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结构化写
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    GtSimplelabelStructCfgT vertexCfg1 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructDW(g_stmt, g_labelName, vertexCfg, 0, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructDW(g_stmt, g_labelName, vertexCfg1, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel2StructDeleteDW(g_stmt, g_labelName, vertexCfg, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtSimplelabel0StructDeleteDW(g_stmt, g_labelName, vertexCfg1, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue * 2, 0, 0, {false, false}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, 0, {false, false}};
    for (uint32_t keyId = 3; keyId < 4; keyId++) {
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg, 0, keyId, false, updateVal, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleT2NewOldVersionScan(g_stmt, pkReadCfg2, 2, keyId, false, updateVal, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
