/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef TABLELOCK_H
#define TABLELOCK_H

#include "syCommon.h"

int setVertexValue(int i, GmcStmtT *stmt, bool isLpmKey = false)
{
    int ret = 0;
    int16_t f3 = i % 32768;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &f3, sizeof(f3));
    RETURN_IFERR(ret);
    uint16_t F4 = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4, sizeof(F4));
    RETURN_IFERR(ret);
    int32_t f5 = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &f5, sizeof(f5));
    RETURN_IFERR(ret);
    uint32_t F6 = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
    RETURN_IFERR(ret);
    int64_t f7 = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &f7, sizeof(f7));
    RETURN_IFERR(ret);
    uint64_t F8 = i;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT64, &F8, sizeof(F8));
    RETURN_IFERR(ret);
    int8_t f9 = i % 127;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT8, &f9, sizeof(f9));
    RETURN_IFERR(ret);
    uint8_t F2 = i % 255;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT8, &F2, sizeof(F2));
    RETURN_IFERR(ret);
    double f10Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    RETURN_IFERR(ret);
    uint64_t f11Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
    RETURN_IFERR(ret);
    char f12Value = 'a' + (i & 0x1A);
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    RETURN_IFERR(ret);
    unsigned char f13Value = 'A' + (i & 0x1A);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    RETURN_IFERR(ret);
    char stringValue[532];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[531] = '\0';
    ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_STRING, stringValue, (strlen(stringValue)));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_BYTES, stringValue, 7);
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_FIXED, stringValue, 7);
    RETURN_IFERR(ret);
    uint8_t f17Value = i & 0xF;
    ret = GmcSetVertexProperty(stmt, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17Value, sizeof(uint8_t));
    RETURN_IFERR(ret);
    uint16_t f18Value = i & 0xF;
    ret = GmcSetVertexProperty(stmt, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f18Value, sizeof(uint16_t));
    RETURN_IFERR(ret);
    uint32_t f19Value = i & 0xFF;
    ret = GmcSetVertexProperty(stmt, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f19Value, sizeof(uint32_t));
    RETURN_IFERR(ret);
    uint64_t f20Value = i & 0xFFFF;
    ret = GmcSetVertexProperty(stmt, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f20Value, sizeof(uint64_t));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_FIXED, stringValue, 532);
    RETURN_IFERR(ret);
    if (isLpmKey) {
        uint32_t vr_id = 1;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
        RETURN_IFERR(ret);
        uint32_t vrf_index = 1;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
        RETURN_IFERR(ret);
        uint32_t dest_ip_addr = i;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(dest_ip_addr));
        RETURN_IFERR(ret);
        uint8_t mask_len = 32;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int vertexWrite(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, const char *labelName,
    GmcOperationTypeE OPType, bool isLpmKey = true, bool isSub = false)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, OPType);
    RETURN_IFERR(ret);
    if (isSub) {
        for (int i = recordCountStart; i < recordCountEnd; i++) {
            ((int *)(g_userData->new_value))[i] = i;
        }
    }
    
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = setVertexValue(i, stmt, isLpmKey);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            TEST_ERROR("create the %d record count", i);
            return GMERR_OK;
        } else if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return ret;
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int vertexWriteBigObj(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, const char *labelName,
    GmcOperationTypeE OPType, bool isLpmKey = true, bool isSub = false)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, OPType);
    RETURN_IFERR(ret);
    if (isSub) {
        for (int i = recordCountStart; i < recordCountEnd; i++) {
            ((int *)(g_userData->new_value))[i] = i;
        }
    }
    uint32_t stringLen = 13 * 1024;
    char *value = NULL;
    value = (char *)malloc(stringLen * sizeof(char));
    RETURN_IFERR(ret);
    memset_s(value, stringLen, 'a', stringLen - 1);
    value[stringLen - 1] = '\0';
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "string", GMC_DATATYPE_STRING, value, stringLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "string1", GMC_DATATYPE_STRING, value, stringLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "string2", GMC_DATATYPE_STRING, value, stringLen - 1);
        RETURN_IFERR(ret);
        ret = setVertexValue(i, stmt, isLpmKey);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            TEST_ERROR("create the %d record count", i);
            free(value);
            GmcResetStmt(stmt);
            return GMERR_OK;
        }
        RETURN_IFERR(ret);
    }
    free(value);
    return ret;
}

int vertexRead(int recordCountstart, int recordCountEnd, GmcStmtT *stmt, const char *labelName, const char *key)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountstart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, key);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);

        bool isNull;
        bool isFinish = false;
        GmcNodeT *root;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t F2;
            ret = GmcNodeGetPropertyByName(root, (char *)"F2", &F2, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((unsigned int)0, isNull);
            EXPECT_EQ(i % 255, F2);
        }
    }
    return ret;
}

int vertexUpdate(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, const char *labelName, const char *key,
    bool isLpmKey = true)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = recordCountStart; i < recordCountEnd; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        RETURN_IFERR(ret);
        ret = setVertexValue(i, stmt, isLpmKey);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, key);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_FEATURE_NOT_SUPPORTED) {
            RETURN_IFERR(ret);
        }
    }
    return GMERR_OK;
}

int vertexDelete(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, const char *labelName,
    const char *key, bool isSub = false)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    if (isSub) {
        for (int i = recordCountStart; i < recordCountEnd; i++) {
            ((int *)(g_userData->new_value))[i] = i;
            ((int *)(g_userData->old_value))[i] = i;
        }
    }
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, key);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return ret;
        }
        BREAK_IFERR(ret);
    }
    return ret;
}

int vertexWriteBatch(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, GmcConnT *conn,
    const char *labelName, GmcOperationTypeE OPType, bool isLpmKey = true)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, OPType);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = recordCountStart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        RETURN_IFERR(ret);
        ret = setVertexValue(i, stmt, isLpmKey);
        BREAK_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        BREAK_IFERR(ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(recordCountEnd - recordCountStart, totalNum);
    EXPECT_EQ(recordCountEnd - recordCountStart, successNum);
    return GMERR_OK;
}

int vertexUpdateBatch(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, GmcConnT *conn,
    const char *labelName, const char *key)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = recordCountStart; i < recordCountEnd; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        BREAK_IFERR(ret);
        ret = setVertexValue(i, stmt);
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, key);
        BREAK_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        BREAK_IFERR(ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(recordCountEnd - recordCountStart, totalNum);
    EXPECT_EQ(recordCountEnd - recordCountStart, successNum);
    return GMERR_OK;
}

int vertexDeleteBatch(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, GmcConnT *conn,
    const char *labelName, const char *key)
{
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = recordCountStart; i < recordCountEnd; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, key);
        BREAK_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        BREAK_IFERR(ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(recordCountEnd - recordCountStart, totalNum);
    EXPECT_EQ(recordCountEnd - recordCountStart, successNum);
    return GMERR_OK;
}

void vertexSnCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    bool eof = false;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        if (info->eventType == GMC_SUB_EVENT_INSERT) {
            user_data->insertNum++;
        } else if (info->eventType == GMC_SUB_EVENT_DELETE) {
            user_data->deleteNum++;
        } else if (info->eventType == GMC_SUB_EVENT_UPDATE) {
            user_data->updateNum++;
        } else if (info->eventType == GMC_SUB_EVENT_REPLACE) {
            user_data->replaceNum++;
        } else if (info->eventType == GMC_SUB_EVENT_AGED) {
            user_data->agedNum++;
        } else if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD) {
        }
    }
}

int subKvWrite(int recordCountStart, int recordCountEnd, GmcStmtT *stmt)
{
    int ret = 0;
    char key[recordCountEnd];
    for (uint32_t value = recordCountStart; value < recordCountEnd; value++) {
        ret = sprintf(key, "F%d", value);
        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int subKvRemove(int recordCountStart, int recordCountEnd, GmcStmtT *stmt)
{
    int ret = 0;
    uint32_t count = 0;
    char key[recordCountEnd];

    for (uint32_t value = recordCountStart; value < recordCountEnd; value++) {
        ret = sprintf(key, "F%d", value);
        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);
        ret = GmcKvRemove(stmt, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

void snKvCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    bool eof = false;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                break;
            }
        }
        if (eof) {
            break;
        }

        if (info->eventType == GMC_SUB_EVENT_KV_SET) {
            user_data->kvSetNum++;
        } else if (info->eventType == GMC_SUB_EVENT_DELETE) {
            user_data->deleteNum++;
        } else if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD) {
        } else if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            printf("eventtype scan eof");
        }
    }
}

void *ThreadDml(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    const char *labelName = (char *)"schema_datatype";
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);

#if defined TEST_STATIC_ASAN
int num = 6;
#else
int num = 6;
#endif
    for (int i = 0; i < num; i++) {
        retf = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, retf);
        const char *key = (char *)"localhash";
        retf = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
        EXPECT_EQ(GMERR_OK, retf);
        retf = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
        EXPECT_EQ(GMERR_OK, retf);
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadWrite(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    const char *labelName = (char *)"schema_datatype";
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    retf = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, retf);
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadWriteBatch(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        retf = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, retf);
        retf = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        EXPECT_EQ(GMERR_OK, retf);
        retf = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        EXPECT_EQ(GMERR_OK, retf);
        sleep(1);
    };
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadDmlKv(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    char *tableName = (char *)"kv";
    retf = GmcKvPrepareStmtByLabelName(stmt, tableName);
    EXPECT_EQ(GMERR_OK, retf);

#if defined TEST_STATIC_ASAN
int num = 6;
#else
int num = 20;
#endif
    for (int i = 0; i < 20; i++) {
        retf = subKvWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
        EXPECT_EQ(GMERR_OK, retf);
        retf = subKvRemove(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
        EXPECT_EQ(GMERR_OK, retf);
        sleep(1);
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}
    
void *ThreadWriteBigObj(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    const char *labelName = (char *)"schemaBigObj";
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    retf = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, retf);
    uint32_t stringLen = 13 * 1024;
    char *value = NULL;
    value = (char *)malloc(stringLen * sizeof(char));
    EXPECT_EQ(GMERR_OK, retf);
    memset_s(value, stringLen, 'a', stringLen - 1);
    value[stringLen - 1] = '\0';

#if defined TEST_STATIC_ASAN
int num = 6;
#else
int num = 20;
#endif
    for (int y = 0; y < num; y++) {
        for (int i = 0; i < 1000; i++) {
            uint32_t F1 = i;
            retf = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcSetVertexProperty(stmt, "string", GMC_DATATYPE_STRING, value, stringLen - 1);
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcSetVertexProperty(stmt, "string1", GMC_DATATYPE_STRING, value, stringLen - 1);
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcSetVertexProperty(stmt, "string2", GMC_DATATYPE_STRING, value, stringLen - 1);
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, retf);
        }
        sleep(1);
    }
    free(value);
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadWriteSimple(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    const char *labelName = (char *)"schemaSimple";
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    retf = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, retf);

#if defined TEST_STATIC_ASAN
int num = 6;
#else
int num = 20;
#endif
    for (int y = 0; y < num; y++) {
        for (int i = 0; i < 1000; i++) {
            uint32_t F1 = i;
            retf = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
            EXPECT_EQ(GMERR_OK, retf);
            int16_t f3 = i % 32768;
            retf = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &f3, sizeof(f3));
            EXPECT_EQ(GMERR_OK, retf);
            uint16_t F4 = i;
            retf = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4, sizeof(F4));
            EXPECT_EQ(GMERR_OK, retf);
            int32_t f5 = i;
            retf = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &f5, sizeof(f5));
            EXPECT_EQ(GMERR_OK, retf);
            uint32_t F6 = i;
            retf = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
            EXPECT_EQ(GMERR_OK, retf);
            int64_t f7 = i;
            retf = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &f7, sizeof(f7));
            EXPECT_EQ(GMERR_OK, retf);
            uint64_t F8 = i;
            retf = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT64, &F8, sizeof(F8));
            EXPECT_EQ(GMERR_OK, retf);
            int8_t f9 = i % 127;
            retf = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT8, &f9, sizeof(f9));
            EXPECT_EQ(GMERR_OK, retf);
            uint8_t F2 = i % 255;
            retf = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT8, &F2, sizeof(F2));
            EXPECT_EQ(GMERR_OK, retf);
            double f10Value = i;
            retf = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, retf);
        }
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadTruncate(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    const char *tableBigObj = (char *)"schemaBigObj";
    const char *tableSimple = (char *)"schemaSimple";
    const char *tableNameKV = (char *)"kv";
    const char *labelName = (char *)"schema_datatype";
    for (int i = 0; i < 20; i++) {
        retf = GmcDeleteAllFast(stmt, tableBigObj);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcDeleteAllFast(stmt, tableSimple);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcDeleteAllFast(stmt, labelName);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcDeleteAllFast(stmt, tableNameKV);
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, retf);
        sleep(1);
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadCheck(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    const char *tableBigObj = (char *)"schemaBigObj";
    const char *tableSimple = (char *)"schemaSimple";
    const char *tableNameKV = (char *)"kv";
    const char *labelName = (char *)"schema_datatype";
    bool isAbnormal = false;
    for (int i = 0; i < 5; i++) {
        retf = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcBeginCheck(stmt, tableBigObj, FULLTABLE);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcEndCheck(stmt, tableBigObj, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcBeginCheck(stmt, tableSimple, FULLTABLE);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcEndCheck(stmt, tableSimple, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcBeginCheck(stmt, tableNameKV, FULLTABLE);
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, retf);
        retf = GmcEndCheck(stmt, tableNameKV, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_UNDEFINED_TABLE, retf);
        sleep(1);
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadSub(void *args)
{
    const char *subKvType =
        R"({
        "name": "subKv",
        "label_name": "kv",
        "events":
            [
                { "type": "set", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retfry": true,
        "is_reliable": true
    })";

    const char *subSimple =
    R"({
        "name": "subSimple",
        "label_name": "schemaSimple",
        "comment": "VertexLabel subscription",
        "events":
            [
                { "type": "insert", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "age", "msgTypes": [ "new object", "old object" ]},
                { "type": "update", "msgTypes": [ "new object", "old object" ]},
                { "type": "replace", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retfry": true,
        "is_reliable": true
    })";

    const char *subBigObj =
    R"({
        "name": "subBigObj",
        "label_name": "schemaBigObj",
        "comment": "VertexLabel subscription",
        "events":
            [
                { "type": "insert", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "age", "msgTypes": [ "new object", "old object" ]},
                { "type": "update", "msgTypes": [ "new object", "old object" ]},
                { "type": "replace", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retfry": true,
        "is_reliable": true
    })";

    const char *subAllType =
    R"({
        "name": "subVertexLabel",
        "label_name": "schema_datatype",
        "comment": "VertexLabel subscription",
        "events":
            [
                { "type": "insert", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "age", "msgTypes": [ "new object", "old object" ]},
                { "type": "update", "msgTypes": [ "new object", "old object" ]},
                { "type": "replace", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retfry": true,
        "is_reliable": true
    })";

    int retf = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int g_retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, g_retf);
    const char *g_subConnName = (char *)"subConnName";
    int g_chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    g_retf = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    EXPECT_EQ(GMERR_OK, g_retf);
    const char *g_labelConfig = (char *)"{\"enableTableLock\":true}";
    const char *g_subName = (char *)"subVertexLabel";
    const char *g_subNameSimple = (char *)"subSimple";
    const char *g_subNameBigObj = (char *)"subBigObj";
    const char *g_subKv = (char *)"subKv";
    GmcSubConfigT tmp_schema, tmp_schema1, tmp_schema2, tmp_schema3;
    for (int i = 0; i < 20; i++) {
        tmp_schema.subsName = g_subName;
        tmp_schema.configJson = subAllType;
        retf = GmcSubscribe(stmt, &tmp_schema, connSub, vertexSnCallback, g_userData);
        EXPECT_EQ(GMERR_OK, retf);
        tmp_schema1.subsName = g_subNameSimple;
        tmp_schema1.configJson = subSimple;
        retf = GmcSubscribe(stmt, &tmp_schema1, connSub, vertexSnCallback, g_userData);
        EXPECT_EQ(GMERR_OK, retf);
        tmp_schema2.subsName = g_subNameBigObj;
        tmp_schema2.configJson = subBigObj;
        retf = GmcSubscribe(stmt, &tmp_schema2, connSub, vertexSnCallback, g_userData);
        EXPECT_EQ(GMERR_OK, retf);
        tmp_schema3.subsName = g_subKv;
        tmp_schema3.configJson = subKvType;
        retf = GmcSubscribe(stmt, &tmp_schema3, connSub, snKvCallback, g_userData);
        EXPECT_EQ(GMERR_OK, retf);

        retf = GmcUnSubscribe(stmt, g_subName);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcUnSubscribe(stmt, g_subNameSimple);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcUnSubscribe(stmt, g_subNameBigObj);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcUnSubscribe(stmt, g_subKv);
        EXPECT_EQ(GMERR_OK, retf);
        sleep(1);
    }
    int g_result = testGmcDisconnect(connSub, stmtSub);
    EXPECT_EQ(GMERR_OK, g_result);
    g_retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, g_retf);
    return NULL;
}

void *ThreadRead(void *args)
{
    int retf = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    const char *labelName = (char *)"schema_datatype";
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    const char *key = (char *)"localhash";
    retf = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    EXPECT_EQ(GMERR_OK, retf);
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadCreateDrop(void *args)
{
    int retf = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    const char *gLabelConfig = (char *)"{\"enableTableLock\":true}";
    int cycles = 200;
    for (int i = 0; i < cycles; i++) {
        retf = createVertexLabel((char *)"schema_datatype.gmjson", stmt, gLabelConfig);
        if (retf != GMERR_DUPLICATE_TABLE) {
            EXPECT_EQ(GMERR_OK, retf);
        }
        const char *labelName = (char *)"schema_datatype";
        retf = GmcDropVertexLabel(stmt, labelName);
        if (retf != GMERR_UNDEFINED_TABLE) {
            EXPECT_EQ(GMERR_OK, retf);
        }
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadCreateDrop1(void *args)
{
    int retf = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    retf = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);
    const char *config = (char *)"{\"enableTableLock\":true}";
    int cycles = 100;
    int count = *(int *)args;
    for (int i = 0; i < cycles; i++) {
        char labelName[20] = "";
        char labelSchema[1024] = "";
        retf = snprintf(labelSchema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T0\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            count);
        retf = snprintf(labelName, 20, "T%d", count);
        retf = GmcCreateVertexLabel(stmt, labelSchema, config);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, retf);
    }
    retf = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

void *ThreadTableDml(void *args)
{
    int retf = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    retf = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, retf);

    int count = *(int *)args;  // thread id
    int vetexlabelCount = 8;  // 每个线程创建8个vertex
    const char *config = (char *)"{\"enableTableLock\":true}";
    for (uint32_t num = vetexlabelCount * count; num < vetexlabelCount * count + vetexlabelCount;
         num++) {  // 128*8=1024
        char labelName[20] = "";
        char labelSchema[1024] = "";
        retf = snprintf(labelSchema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        retf = snprintf(labelName, 20, "T%d", num);
        if (num % 2 == 0) {
            retf = GmcCreateVertexLabel(stmt1, labelSchema, NULL);
        } else {
            retf = GmcCreateVertexLabel(stmt1, labelSchema, config);
        }
        EXPECT_EQ(GMERR_OK, retf);

        // vertex DML
        int data_num = 5000;
        for (int32_t i = 0; i < data_num; i++) {
            // insert
            retf = testGmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, retf);
            int32_t F0Value = i;
            retf = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, retf);
            int32_t F1Value = i;
            retf = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcExecute(stmt1);
            EXPECT_EQ(GMERR_OK, retf);

            // replace
            retf = testGmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, retf);
            int32_t newVal = i + 100;
            retf = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &newVal, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcExecute(stmt1);
            EXPECT_EQ(GMERR_OK, retf);

            // pk read
            retf = testGmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcSetIndexKeyName(stmt1, "vertex_pk");
            EXPECT_EQ(GMERR_OK, retf);
            retf = GmcExecute(stmt1);
            EXPECT_EQ(GMERR_OK, retf);
            bool isFinish = true;
            retf = GmcFetch(stmt1, &isFinish);
            EXPECT_EQ(GMERR_OK, retf);
            int read_f1;
            bool isNull = 1;
            retf = GmcGetVertexPropertyByName(stmt1, "F1", &read_f1, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, retf);
            EXPECT_EQ(isNull, 0);
            EXPECT_EQ(newVal, read_f1);
        }

        // truncate后全表扫描
        retf = GmcTruncateVertexLabel(stmt1, labelName);
        EXPECT_EQ(GMERR_OK, retf);
        retf = testGmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, retf);
        retf = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, retf);
        bool isEof = false;
        retf = GmcFetch(stmt1, &isEof);
        EXPECT_EQ(GMERR_OK, retf);
        EXPECT_EQ(true, isEof);

        // drop vertex
        retf = GmcDropVertexLabel(stmt1, labelName);
        EXPECT_EQ(GMERR_OK, retf);
    }

    retf = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, retf);
    return NULL;
}

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif
#endif
