#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;
char *vertex_src_fileds_label_schema = NULL;
char *vertex_dest_fileds_label_schema = NULL;
char *edge_label_schema = NULL;
char g_EdgeLabelName[] = "from_T80_to_T90";
char g_EdgeLabel_config[] = "{\"max_record_count\":1000}";
#define MAX_CMD_SIZE 1024
char compareMess[MAX_CMD_SIZE];
char compareMess1[MAX_CMD_SIZE];
char compareMess2[MAX_CMD_SIZE];
char g_command[MAX_CMD_SIZE];
const char *normal_config_json = R"(
    {
        "max_record_count":1000
    }
)";

class GmexportTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcConnect(&g_conn, &g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, "public");
        ASSERT_EQ(GMERR_OK, ret);
        readJanssonFile("schema_file/vertexlabel_dest_schem.gmjson", &vertex_dest_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_dest_fileds_label_schema);
        readJanssonFile("schema_file/vertexlabel_src_schem.gmjson", &vertex_src_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_src_fileds_label_schema);
        readJanssonFile("schema_file/edgelabel_schema.gmjson", &edge_label_schema);
        ASSERT_NE((void *)NULL, edge_label_schema);
    }
    static void TearDownTestCase()
    {
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmexportTest::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void GmexportTest::TearDown()
{
    AW_CHECK_LOG_END();
}
void insertvalue(GmcStmtT *stmt, const char *labelName, uint32_t pk)
{

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = (2 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = (3 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = (4 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = (5 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = (6 + pk);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[8] = "abcdefg";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = (17 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 9 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "abcde";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int affectRows, len;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
}
void inserttest(GmcStmtT *stmt, const char *labelName, uint32_t pk, unsigned int times)
{

    for (int i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int8_t value2 = (2 + pk + i);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t value3 = (3 + pk + i);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        int16_t value4 = (4 + pk + i);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint16_t value5 = (5 + pk + i);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value6 = (6 + pk + i);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t value10 = 10 + pk + i;
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        float value11 = (float)1.2 + (float)(pk + i);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
        EXPECT_EQ(GMERR_OK, ret);
        double value12 = 10.86 + pk + i;
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
        EXPECT_EQ(GMERR_OK, ret);
        char teststr14[] = "string";
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
        EXPECT_EQ(GMERR_OK, ret);
        char teststr15[8] = "abcdefg";
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t value17 = (17 + pk + i);
        ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t value7 = pk + i;  // F7是PK
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        bool value8 = false;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t value9 = 9 + pk + i;
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        char teststr16[6] = "abcde";
        EXPECT_EQ(6, strlen(teststr16) + 1);
        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        unsigned int affectRows, len;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        // printf("%d\n",i);
    }
}
void TreeReadVertex(GmcStmtT *stmt, int index, int start_num, int end_num, int array_num, int vector_num,
    const char *labelName, const char *keyName)
{

    void *label = NULL;
    bool isNull;

    GmcNodeT *root, *T1, *T2, *T3;

    // 查询顶点
    for (int i = start_num; i < end_num; i++) {
        uint32_t f0_value = index + i;
        uint32_t f1_value = f0_value + 1, f2_value = f0_value + 2, f3_value = f0_value + 3, f4_value = f0_value + 4,
                 f5_value = f0_value + 5, f6_value = f0_value + 6;
        uint32_t expectValue0, expectValue1, expectValue2, expectValue3, expectValue4, expectValue5, expectValue6;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        unsigned int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a2", &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcNodeGetPropertyByName(root, (char *)"a0", &expectValue0, sizeof(uint32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(f0_value, expectValue0);
            ret = GmcNodeGetPropertyByName(T1, (char *)"b1", &expectValue1, sizeof(uint32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(f1_value, expectValue1);
            ret = GmcNodeGetPropertyByName(T1, (char *)"b2", &expectValue2, sizeof(uint32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(f2_value, expectValue2);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetPropertyByName(T2, (char *)"b3", &expectValue3, sizeof(uint32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(f3_value, expectValue3);

                ret = GmcNodeGetPropertyByName(T2, (char *)"b4", &expectValue4, sizeof(uint32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(f4_value, expectValue4);
                if (j < array_num - 1) {
                    ret = GmcNodeGetNextElement(T2, &T2);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            // 读取vector节点
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetPropertyByName(T3, (char *)"b5", &expectValue5, sizeof(uint32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(f5_value, expectValue5);
                ret = GmcNodeGetPropertyByName(T3, (char *)"b6", &expectValue6, sizeof(uint32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(f6_value, expectValue6);
            }
        }
    }
}
void queryvalue(GmcStmtT *stmt, uint32_t pk, const char *labelName, const char *keyName)
{
    int ret;
    int8_t value2 = (2 + pk);
    uint8_t value3 = (3 + pk);
    int16_t value4 = (4 + pk);
    uint16_t value5 = (5 + pk);
    int32_t value6 = pk + 6;
    uint32_t value7 = pk;
    bool value8 = false;
    int64_t value9 = 9 + pk;
    uint64_t value10 = 10 + pk;
    uint32_t value13 = pk + 13;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    char teststr14[] = "string";
    uint32_t value17 = (17 + pk);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value7);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
        EXPECT_EQ(GMERR_OK, ret);
        char teststr15[8] = "abcdefg";
        char teststr16[6] = "abcde";
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
void TreeInsertVertex(
    GmcStmtT *stmt, int index, int start_num, int end_num, int array_num, int vector_num, const char *labelName)
{
    GmcNodeT *root, *T1, *T2, *T3;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        uint32_t f0_value = index + i;
        uint32_t f1_value = f0_value + 1, f2_value = f0_value + 2, f3_value = f0_value + 3, f4_value = f0_value + 4;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "a1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "a2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "a3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root, (char *)"a0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, (char *)"b1", GMC_DATATYPE_UINT32, &f1_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, (char *)"b2", GMC_DATATYPE_UINT32, &f2_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetPropertyByName(T2, (char *)"b3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T2, (char *)"b4", GMC_DATATYPE_UINT32, &f4_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        uint32_t f5_value = f0_value + 5;
        uint32_t f6_value = f0_value + 6;
        // 插入vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, (char *)"b5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, (char *)"b6", GMC_DATATYPE_UINT32, &f6_value, sizeof(uint32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 001 gmexport -c vschema -t 表名 ，预期导出成功，创建label
TEST_F(GmexportTest, Tool_006_GmexportTest_001)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmjson");
}
// 002 gmexport -c vschema -t 表名 -f 指定路径（不加-f默认当前路径），预期导出成功，导出的schema创建label成功
TEST_F(GmexportTest, Tool_006_GmexportTest_002)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./";
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmjson");
}
// 003 gmexport -c vdata -t  表名
TEST_F(GmexportTest, Tool_006_GmexportTest_003)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 导出data数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -s %s", g_toolPath, labelName,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    ASSERT_EQ(GMERR_OK, ret);
    const char *labelName1 = "sys_new";
    const char *keyName1 = "table_pk1";
    char const *g_vertexPath1 = "./schema_file/sys_new.gmjson";
    char const *g_dataPath1 = "./sys.gmdata";
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath1,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys_new.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t sys_new -f %s -s %s", g_toolPath,
        g_dataPath1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName1, keyName1);
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmdata");
}
// 004 gmexport  -c  vdata  -t  表名 -f 指定路径（不加-f默认当前路径），预期导出成功，导出的数据插入查询成功
TEST_F(GmexportTest, Tool_006_GmexportTest_004)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 导出data数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -s %s", g_toolPath, labelName,
        g_filePath, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    ASSERT_EQ(GMERR_OK, ret);
    const char *labelName1 = "sys_new";
    const char *keyName1 = "table_pk1";
    char const *g_vertexPath1 = "./schema_file/sys_new.gmjson";
    char const *g_dataPath1 = "./sys.gmdata";
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath1,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys_new.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t sys_new -f %s -s %s", g_toolPath,
        g_dataPath1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName1, keyName1);
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmdata");
}
// 005 gmexport -c xxx -t sys 预期返回Invalid gmimport parameter
TEST_F(GmexportTest, Tool_006_GmexportTest_005)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/test.gmdata";
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c xxx -t %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 006 gmexport -c vschema –t xxx  预期返回 Get file suffix from xxx failed
TEST_F(GmexportTest, Tool_006_GmexportTest_006)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/test.gmdata";
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t xxx -s %s", g_toolPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 007 gmexport -c vdata –t xxx  预期返回 Get file suffix from xxx failed
TEST_F(GmexportTest, Tool_006_GmexportTest_007)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/test.gmdata";
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t xxx -s %s", g_toolPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 008 gmexport -c vdata –t sys -f xxx (导出schema路径错误)  预期返回 Get file suffix from xxx failed
TEST_F(GmexportTest, Tool_006_GmexportTest_008)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t sys -f xxx -s %s", g_toolPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 009 gmexport -c vdata -t sys –f xxx （导出data路径错误） 预期返回 Get file suffix from xxx failed
TEST_F(GmexportTest, Tool_006_GmexportTest_009)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t sys -f xxx -s %s", g_toolPath,
        g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "rename file unsucc. ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    snprintf(
        compareMess1, MAX_CMD_SIZE, "export vertex data file unsucc. ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    snprintf(compareMess2, MAX_CMD_SIZE, "Export file unsucc. ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    ret = executeCommand(g_command, compareMess, compareMess1, compareMess2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 010 gmexport -c schema （缺少-t）预期返回Invalid gmimport parameter
TEST_F(GmexportTest, Tool_006_GmexportTest_010)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/test.gmdata";
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 缺少-t
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -s %s", g_toolPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 011 gmexport -c vschema  -t all –f 指定路径（导出所有数据）,预期 导出失败(Catlog不对外提供遍历所有表的接口)
TEST_F(GmexportTest, Tool_006_GmexportTest_011)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/test.gmdata";
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 导出所有数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t all -s %s", g_toolPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf sys*");
}
// 012 gmexport -c eschema -t 表名，导出edge 预期导出失败
TEST_F(GmexportTest, Tool_006_GmexportTest_012)
{

    GmcStmtT *g_stmt1;
    int ret = GmcAllocStmt(g_conn, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "T80");
    GmcDropVertexLabel(g_stmt1, "T90");
    char vertexLabel_config[] = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, vertex_dest_fileds_label_schema, vertexLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt1, vertex_src_fileds_label_schema, vertexLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    //建边
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T80", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "T90", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int F0Value, F1Value, F2Value, F3Value;
    for (int i = 0; i < 20; i++) {
        F0Value = i;
        F1Value = i;
        F2Value = i;
        F3Value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, "T80", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T80", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(20, cnt);
    for (int i = 0; i < 20; i++) {
        F0Value = i + 10;
        F1Value = i + 10;
        F2Value = i + 10;
        F3Value = i + 10;
        ret = testGmcPrepareStmtByLabelName(g_stmt1, "T90", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "T90", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt1, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(20, cnt);
    // export 导出edge
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c eschema -t %s -s %s", g_toolPath, g_EdgeLabelName,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(g_stmt, "T80");
    EXPECT_EQ(GMERR_OK, ret);
}
// 013 接口写入数据 export导出，支持多种类型类型
TEST_F(GmexportTest, Tool_006_GmexportTest_013)
{
    const char *labelName = "normal_all_type";
    const char *keyName = "normal_all_type_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/normal_all_type.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/normal_all_type.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    //插入数据查询
    uint32_t pkvalue = 7;
    insertvalue(g_stmt, labelName, pkvalue);
    queryvalue(g_stmt, pkvalue, labelName, keyName);
    // export 导出schema数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t %s -s %s", g_toolPath, labelName,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "unexpected option(\"-c\") param for gmexport.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmjson");
}
// 014 插入1024条数据，导出，查询导出是否成功
TEST_F(GmexportTest, Tool_006_GmexportTest_014)
{
    const char *labelName = "normal_all_type";
    const char *keyName = "normal_all_type_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/normal_all_type.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/normal_all_type.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    //插入数据查询
    uint32_t pkvalue = 7;
    inserttest(g_stmt, labelName, pkvalue, 1024);
    for (int i = 0; i < 1024; i++) {
        queryvalue(g_stmt, pkvalue + i, labelName, keyName);
        // printf("%d\n",i);
    }
    // export 导出数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -s %s", g_toolPath, labelName,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    ASSERT_EQ(GMERR_OK, ret);
    const char *labelName1 = "normal_all_type_test";
    const char *keyName1 = "normal_all_type_test_PK";
    char const *g_vertexPath1 = "./schema_file/normal_all_type_test.gmjson";
    char const *g_dataPath1 = "./normal_all_type.gmdata";
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath1,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/normal_all_type_test.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t normal_all_type_test -f %s -s %s",
        g_toolPath, g_dataPath1, g_connServer);
    printf("%s\n", g_command);
    ret =
        executeCommand(g_command, "Insert data succeed. totalNum: 1024, successNum: 1024", "Command type: import_vdata",
            "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/normal_all_type.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    // testGmcPrepareStmtByLabelName(g_stmt, labelName1, GMC_OPERATION_INSERT);
    // ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        queryvalue(g_stmt, pkvalue + i, labelName1, keyName1);
        // printf("%d\n",i);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmjson");
    system("rm -rf *.gmdata");
}
// 015 插入10000条数据，导出，查询导出是否成功
TEST_F(GmexportTest, Tool_006_GmexportTest_015)
{
    const char *labelName = "normal_all_type";
    const char *keyName = "normal_all_type_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/normal_all_type.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/normal_all_type.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    //插入数据查询
    uint32_t pkvalue = 7;
    inserttest(g_stmt, labelName, pkvalue, 10000);
    for (int i = 0; i < 10000; i++) {
        queryvalue(g_stmt, pkvalue + i, labelName, keyName);
        // printf("%d\n",i);
    }
    // export 导出schema数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -s %s", g_toolPath, labelName,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    ASSERT_EQ(GMERR_OK, ret);
    const char *labelName1 = "normal_all_type_test";
    const char *keyName1 = "normal_all_type_test_PK";
    char const *g_vertexPath1 = "./schema_file/normal_all_type_test.gmjson";
    char const *g_dataPath1 = "./normal_all_type.gmdata";
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath1,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/normal_all_type_test.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t normal_all_type_test -f %s -s %s",
        g_toolPath, g_dataPath1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10000, successNum: 10000",
        "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/normal_all_type.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    testGmcPrepareStmtByLabelName(g_stmt, labelName1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10000; i++) {
        queryvalue(g_stmt, pkvalue + i, labelName1, keyName1);
        // printf("%d\n",i);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
    system("rm -rf *.gmjson");
    system("rm -rf *.gmdata");
}
// 016 参数异常，gmexport -x vshcema -f 路径
TEST_F(GmexportTest, Tool_006_GmexportTest_016)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 导出schema数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -x vschema -t %s -s %s", g_toolPath, labelName,
        g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Export init args unsucc. ret = %d", GMERR_INVALID_OPTION);
    ret = executeCommand(g_command, "The first option \"-x\" is undefined.", compareMess);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 017 参数异常，gmexport -x vdata -f 路径
TEST_F(GmexportTest, Tool_006_GmexportTest_017)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    //建表
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //导入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 导出schema数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -x vdata -t %s -s %s", g_toolPath, labelName,
        g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Export init args unsucc. ret = %d", GMERR_INVALID_OPTION);
    ret = executeCommand(g_command, "The first option \"-x\" is undefined.", compareMess);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 018 gmexport -c vschema -t 表名 -f 具体文件名（路径即可，不加-f默认当前路径） ，导出失败
TEST_F(GmexportTest, Tool_006_GmexportTest_018)
{
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    char const *g_vertexPath = "./schema_file/sys.gmjson";
    char const *g_dataPath = "./schema_file/sys.gmdata";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/type.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 2, successNum: 2", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/006_gmimpexp/schema_file/sys.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);
    //查询数据
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    // export 导出data数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -s %s", g_toolPath, labelName,
        g_filePath, g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "rename file unsucc. ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    snprintf(
        compareMess1, MAX_CMD_SIZE, "export vertex data file unsucc. ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    snprintf(compareMess2, MAX_CMD_SIZE, "Export file unsucc. ret = %d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    ret = executeCommand(g_command, compareMess, compareMess1, compareMess2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
