/*****************************************************************************
 Description  : Delta Store和Catalog系统视图增强
 Notes        : Tool_009_CatalogEnhance_Resource_test_001	1.资源池name长度 0，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_002	2.资源池name长度 10，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_003	3.资源池pool id 0，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_004	4.资源池pool id 10，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_005	5. 资源池分配order 0，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_006	6. 资源池分配order 1，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_007	7.资源池start id 10，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_008	8．资源池start id 1，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_009	9.资源池capacity 1，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_010	10.资源池capacity 10，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_011	11. 创建资源池，并发查看视图，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_012	12. 创建资源池，循环查看视图，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_013	13.namespaceName长度128，正确打印资源池关键信息
                Tool_009_CatalogEnhance_Resource_test_014	14.namespaceName长度10，正确打印资源池关键信息



 History      :
 Author       : 廖想
 Modification :
 Date         : 2021/04/26
****************************************************************************/

extern "C" {
}

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

using namespace std;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

// 通用全局变量
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
static const char *gLabelName = "ResourceLable";
static const char *gLabelConfig = R"({"max_record_count":1000})";
static const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"ResourceLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"resource", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceLable",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

// 资源池全局变量
static const char *gResPoolName = "ResourcePool";
static const uint64_t gResPoolId = 0;
static const char *gResPoolConfigJson =
    R"({
        "name" : "ResourcePool",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *gExtendResPoolName = "ExtendResourcePool";
static const uint64_t gExtendResPoolId = 1;
static const char *gExtendResPoolConfigJson =
    R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 1,
        "start_id" : 10,
        "capacity" : 1000,
        "order" : 1,
        "alloc_type" : 0
    })";

class Resource_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Resource_test::SetUp()
{

    int ret;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char errorMsg8[128] = {};
    (void)snprintf(errorMsg8, sizeof(errorMsg8), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg8);

    char errorMsg7[128] = {};
    (void)snprintf(errorMsg7, sizeof(errorMsg7), "GMERR-%d", GMERR_RESOURCE_POOL_ALREADY_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg7);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);

    AW_CHECK_LOG_BEGIN();
}

void Resource_test::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 1.资源池name长度 1，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_001)
{
    const char *ResPoolName = "a";
    const char *resPoolConfigJson =
        R"({
            "name" : "a",
            "pool_id" : 66,
            "start_id" : 66,
            "capacity" : 66,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, ResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: a", "NAMESPACE_ID", "POOL_ID: 66", "START_ID: 66", "CAPACITY: 66");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, ResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 2.资源池name长度 10，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_002)
{
    const char *ResPoolName = "aaaaaaaaaa";
    const char *resPoolConfigJson =
        R"({
            "name" : "aaaaaaaaaa",
            "pool_id" : 66,
            "start_id" : 66,
            "capacity" : 66,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, ResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: aaaaaaaaaa", "NAMESPACE_ID",
        "POOL_ID: 66", "START_ID: 66", "CAPACITY: 66");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, ResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 3.资源池pool id 0，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_003)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 66,
            "capacity" : 66,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 10000", "START_ID: 66", "CAPACITY: 66");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 4.资源池pool id 10，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_004)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10010,
            "start_id" : 66,
            "capacity" : 66,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 10010", "START_ID: 66", "CAPACITY: 66");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 5. 资源池分配order 0，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_005)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10006,
            "start_id" : 6,
            "capacity" : 6,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 10006", "START_ID: 6", "CAPACITY: 6");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 6. 资源池分配order 1，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_006)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10006,
            "start_id" : 6,
            "capacity" : 6,
            "order" : 1,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 10006", "START_ID: 6", "CAPACITY: 6");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_SEQUENCE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 7.资源池start id 10，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_007)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 66,
            "start_id" : 10,
            "capacity" : 66,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 66", "START_ID: 10", "CAPACITY: 66");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 8．资源池start id 1，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_008)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 66,
            "start_id" : 1,
            "capacity" : 66,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 66", "START_ID: 1", "CAPACITY: 66");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 9.资源池capacity 1，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_009)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 66,
            "start_id" : 66,
            "capacity" : 1,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 66", "START_ID: 66", "CAPACITY: 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 10.资源池capacity 10，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_010)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 66,
            "start_id" : 66,
            "capacity" : 10,
            "order" : 0,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 66", "START_ID: 66", "CAPACITY: 10");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_CYCLE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

void *thread_sub_view_Multi(void *arg)
{
    int ret;
    // 视图打印
    for (int i = 0; i < 10; i++) {
        ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "POOL_ID: 88", "START_ID: 88", "CAPACITY: 88");
        EXPECT_EQ(GMERR_OK, ret);
    }

    return NULL;
}
// 11. 创建资源池，并发查看视图，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_011)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 88,
            "start_id" : 88,
            "capacity" : 88,
            "order" : 1,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);

    //多线程查看视图
    int tdNum = 1;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, thread_sub_view_Multi, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 12. 创建资源池，循环查看视图，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_012)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 99,
            "start_id" : 99,
            "capacity" : 99,
            "order" : 1,
            "alloc_type" : 0
        })";

    GmcDestroyResPool(g_stmt, gResPoolName);
    int ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 10; i++) {
        char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
            view_name);
        printf("g_command = %s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
            "POOL_ID: 99", "START_ID: 99", "CAPACITY: 99");
        EXPECT_EQ(GMERR_OK, ret);
        ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_SEQUENCE",
            "RESOURCE_JSON");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 13.namespaceName长度128，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_013)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 99,
            "start_id" : 99,
            "capacity" : 99,
            "order" : 1,
            "alloc_type" : 0
        })";

    const char *normal_config_json = R"(
        {
            "max_record_count":10000
        }
    )";

    int ret = 0;
    void *vertexLabel = NULL;
    char *schema_test = NULL;
    char *label_name = (char *)"namespace_test";
    const char *g_nameSpace_userName = (const char *)"abc";

    char nameSpace[128] = {0};
    memset(nameSpace, 'a', 127);
    nameSpace[127] = '\0';
    ret = GmcAllocStmt(g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, nameSpace);
    ret = GmcCreateNamespace(g_stmt, nameSpace, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDestroyResPool(g_stmt, gResPoolName);
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    //创建vertexLabel
    ret = GmcCreateVertexLabel(g_stmt, gLabelSchemaJson, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 99", "START_ID: 99", "CAPACITY: 99");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_SEQUENCE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    // 解绑资源池到表，第一要归还所有资源池，第二要解绑表，第三要解绑所有资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt, gLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, gLabelName);  // 2021.12.15 现支持删除 解绑后的资源表
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}

// 14.namespaceName长度10，正确打印资源池关键信息
TEST_F(Resource_test, Tool_009_CatalogEnhance_Resource_test_014)
{
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 99,
            "start_id" : 99,
            "capacity" : 99,
            "order" : 1,
            "alloc_type" : 0
        })";

    const char *normal_config_json = R"(
        {
            "max_record_count":10000
        }
    )";

    int ret = 0;
    void *vertexLabel = NULL;
    char *schema_test = NULL;
    char *label_name = (char *)"namespace_test";
    const char *g_nameSpace_userName = (const char *)"abc";

    char nameSpace[10] = {0};
    memset(nameSpace, 'a', 9);
    nameSpace[9] = '\0';
    ret = GmcAllocStmt(g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, nameSpace);
    ret = GmcCreateNamespace(g_stmt, nameSpace, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDestroyResPool(g_stmt, gResPoolName);
    ret = GmcCreateResPool(g_stmt, resPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);

    //创建vertexLabel
    ret = GmcCreateVertexLabel(g_stmt, gLabelSchemaJson, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, gLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$CATA_RESOURCE_INFO";  // 资源池视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "POOL_NAME: ResourcePool", "NAMESPACE_ID",
        "POOL_ID: 99", "START_ID: 99", "CAPACITY: 99");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "RESOURCE_POOL_ALLOC_ORDER: RES_COL_POOL_ALLOC_ORDER_SEQUENCE", "RESOURCE_JSON");
    EXPECT_EQ(GMERR_OK, ret);

    // 解绑资源池到表，第一要归还所有资源池，第二要解绑表，第三要解绑所有资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt, gLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, gLabelName);  // 2021.12.15 现支持删除 解绑后的资源表
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}
