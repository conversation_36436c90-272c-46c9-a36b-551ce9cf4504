/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"

#include "filterCond.h"

char g_configJson[128] = "{\"max_record_count\" : 10000}";
char g_KvName[128] = "KV6";
char g_VertexlabelName[128] = "update_all_support_type_label";
char g_VertexpkName[128] = "update_all_support_type_label_PK";
char *all_type_label = NULL;
char *g_testSchema1 = NULL;
char g_labelName1[16] = "OP_TX";

class GmcmdUpdateTest : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        // 创建客户端连接
        int ret = testGmcConnect(&g_conn, &g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // 删除环境中历史表
        GmcDropVertexLabel(g_stmt, g_labelName1);
        readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &g_testSchema1);
        ASSERT_NE((void *)NULL, g_testSchema1);
        readJanssonFile("./schema_file/update_all_support_type_label.gmjson", &all_type_label);
        ASSERT_NE((void *)NULL, all_type_label);
        ret = GmcCreateVertexLabel(g_stmt, g_testSchema1, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        char *pk_name = (char *)"OP_PK";
        snprintf(view_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
        printf("%s\n", view_command);
        // 插入记录1
        TestGmcInsertVertex_V(
            g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, g_labelName1);
        // 插入记录2
        TestGmcInsertVertex_V(
            g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, g_labelName1);
        // check
        TestGmcDirectFetchVertex_V(
            g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, g_labelName1, pk_name, true);
        TestGmcDirectFetchVertex_V(
            g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, g_labelName1, pk_name, true);

        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        printf("\n======================TEST:END========================\n");
        AW_CHECK_LOG_END();
        int ret = GmcDropVertexLabel(g_stmt, g_labelName1);
        ASSERT_EQ(GMERR_OK, ret);
        // 关闭 client g_connection
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(all_type_label);
        free(g_testSchema1);
    }
};

// 001 更新kv表数据
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_001)
{
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据
    insertKvValue(g_stmt, g_KvName);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    int32_t update_value = 1000;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002 更新全局kv表数据
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_002)
{
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据
    insertKvValue(g_stmt, "T_GMDB");
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    int32_t update_value = 100;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, "T_GMDB", "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);

    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003 连续更新kv表数据
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_003)
{
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据
    insertKvValue(g_stmt, g_KvName);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    int32_t update_value = 100;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);

    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004 条件更新int类型
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_004)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    for (uint32_t i = 0; i < 100; i++) {
        queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
    }
    const char *setValue = "F1=120 and F2=120 and F3=120 and F4=120 and F5=120 and F7=120 and F8=120 and F21=120";
    const char *filter = "F1\\<\\=0";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, 0, 120, true, 0, 0, 'F');
    for (uint32_t i = 1; i < 100; i++) {
        queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 005  条件更新tree模型record节点数据
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_005)
{
    int32_t ret = 0;
    const char *filter = (const char *)"140\\>OP_TX.T1/P5 and OP_TX.T1/P5 \\> 120";
    const char *setValue = "OP_TX.T1/P0\\=100 and OP_TX.T1/P5\\=105";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, g_labelName1,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
}

// 006  同个条件连续更新vertex表数据，条件字段与更新字段为同一字段
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_006)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    for (uint32_t i = 0; i < 100; i++) {
        queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
    }
    const char *setValue = "F1=120 and F2=120 and F3=120 and F4=120 and F5=120 and F7=120 and F8=120 and F21=120";
    const char *filter = "F1\\<\\=0";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    for (uint32_t i = 0; i < 2; i++) {
        ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
        EXPECT_EQ(GMERR_OK, ret);
    }
    memset(g_command, 0, sizeof(g_command));
    queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, 0, 120, true, 0, 0, 'F');
    for (uint32_t i = 1; i < 100; i++) {
        queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 007  同个条件连续更新vertex表数据，条件字段与更新字段为非同一字段
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_007)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    for (uint32_t i = 0; i < 100; i++) {
        queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
    }
    const char *setValue = "F1=120 and F2=120 and F3=120 and F4=120 and F5=120 and F7=120 and F8=120 and F21=120";
    const char *filter = "F0\\<\\=0";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    for (uint32_t i = 0; i < 2; i++) {
        ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
        EXPECT_EQ(GMERR_OK, ret);
    }
    memset(g_command, 0, sizeof(g_command));
    queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, 0, 120, true, 0, 0, 'F');
    for (uint32_t i = 1; i < 100; i++) {
        queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 008  更新Bitmap类型字段
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_008)
{
    int32_t ret = 0;
    char *testSchema1 = NULL;
    char *labelName = (char *)"bitmap_test";
    int num = 10;
    int affectRows = 0;
    unsigned int len = 0;
    readJanssonFile("./schema_file/bitmap_schema.gmjson", &testSchema1);
    ASSERT_NE((void *)NULL, testSchema1);
    ret = GmcCreateVertexLabel(g_stmt, testSchema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(testSchema1);
    // 插入数据与查询
    bitMapTypeInsertQuery(g_stmt, labelName, 0, num);
    // 过滤条件为bit类型的删除
    const char *setValue = "F1\\= 1";
    const char *filter = "F2\\<\\=5";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, labelName,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Update set property unsuccessfully, ret = %d", GMERR_INVALID_PROPERTY);
    ret = executeCommand(g_command, "Update property value F1 is invalid type", compareMess);
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 009  十六进制格式更新BITFIELD8，BITFIELD16，BITFIELD32，BITFIELD64，bytes，fixed字段
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_009)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F17\\=0xf and F18\\=0xf and F19\\=0xf and F20\\=0xf and F12\\=0x756573746572"
                           " and F13\\=0x7565737400";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 010  Set的表达式之间用and和非and连接更新数据
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_010)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F17\\=0xf or F18\\=0xf";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Parse set expression item fail.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 011  非record节点的更新
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_011)
{
    int32_t ret = 0;
    const char *filter = (const char *)"140\\>OP_TX.T1/P5 and OP_TX.T1/P5 \\> 120";
    const char *setValue = "OP_TX.T1/T2/A0=100 and OP_TX.T1/T2/A1=100";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, g_labelName1,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
}

// 012  非全路径更新节点，全路径更新节点
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_012)
{
    int32_t ret = 0;
    const char *filter = (const char *)"140\\>OP_TX.T1/P5 and OP_TX.T1/P5 \\> 120";
    const char *abnormal_path = "OP_TX.P0\\=100 and OP_TX.P5\\=105";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, g_labelName1,
        abnormal_path, filter, g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Update get property type unsuccessfully, ret = %d", GMERR_INVALID_PROPERTY);
    snprintf(compareMess1, MAX_CMD_SIZE, "Update get property info unsuccessfully, ret = %d", GMERR_INVALID_PROPERTY);
    ret = executeCommand(g_command, compareMess, compareMess1);
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    memset(compareMess1, 0, sizeof(compareMess1));
    EXPECT_EQ(GMERR_OK, ret);
}

// 013  8个and组合条件更新
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_013)
{
    int32_t ret = 0;
    const char *filter = (const char
            *)"OP_TX.T1/P12\\>\\=OP_TX.F12 and OP_TX.T1/P1\\>\\=101 and OP_TX.F4\\<120 and "
              "OP_TX.T1/P0\\<\\=110 and OP_TX.T1/P12\\>\\=OP_TX.F12 and OP_TX.T1/P1\\>\\=101 and OP_TX.F4\\<120 and "
              "OP_TX.T1/P0\\<\\=110 ";
    const char *setValue = "OP_TX.T1/P0\\=100 and OP_TX.T1/P5\\=105";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, g_labelName1,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
}

// 014  条件更新资源字段
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_014)
{
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1001000", "GMERR-1003000");
    char *resource_label = NULL;
    const char *labelName = "ResourceLabel";
    readJanssonFile("schema_file/resource_type_label.gmjson", &resource_label);
    ASSERT_NE((void *)NULL, resource_label);
    // 创建资源池
    int ret = GmcCreateResPool(g_stmt, gResPoolTest);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建VertexLabel
    ret = GmcCreateVertexLabel(g_stmt, resource_label, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(resource_label);
    // 绑定资源池到表，顺序是先绑定后打开，否则失败
    ret = GmcBindResPoolToLabel(g_stmt, gResPoolName, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int F0Value = 9;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 99;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 999;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t respoolId = AUTO_POOL_ID;
    uint64_t count = 100;
    uint64_t startIndex = AUTO_START_IDX;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询表record数
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "table: ResourceLabel", "record count: 1");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    // update resource field
    const char *setValue = "F2\\=100 and F3\\=4294967297";
    const char *filter = "F0\\>\\=0";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, labelName,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Update vertex label unsuccessfully, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, compareMess, "Resource property can not modify.");
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    EXPECT_EQ(GMERR_OK, ret);
    deleteByCond(labelName, "F0=9");
    ret = executeCommand(view_command, "record count: 0");
    memset(view_command, 0, sizeof(view_command));
    EXPECT_EQ(GMERR_OK, ret);
    // 解绑
    ret = GmcUnbindResPoolFromLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    // truncate
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c truncate table %s -s %s", ToolPath, labelName, g_connServer);
    printf("%s\n", g_command);
    char cmpStr[128] = {0};
    snprintf(cmpStr, 128, "TRUNCATE TABLE \"%s\" success.", labelName);
    ret = executeCommand(g_command, cmpStr);
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    // 查询表record数
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "table: ResourceLabel", "record count: 0");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除资源池
    ret = GmcDestroyResPool(g_stmt, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 015 更新32层schema的record节点
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_015)
{
    int32_t ret = 0;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    const char *cond1 = (const char *)"50 \\> "
                                      "all\\(test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/"
                                      "T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/T28/T29/T30/T31/A4\\) and "
                                      "test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/Q1 \\> "
                                      "all\\(test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/"
                                      "T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/T28/T29/T30/T31/A15\\)";
    int affectRows = 0;
    uint32_t len = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    char *pk_name = (char *)"primary_key";
    int update_value = 100;
    char labelName_32Deep[64] = "test32_deeep";
    char *testSchema = NULL;

    ret = GmcDropVertexLabel(g_stmt, labelName_32Deep);
    readJanssonFile("./schema_file/thirty-two_depth_node.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(testSchema);
    // 插入数据
    TestThirtyTwoDepthInsert(g_stmt, bool_value, test1, test2, 0, 100, 3, labelName_32Deep);
    // 读
    TestThirtyTwoDepthGetPropertyAndCheck(g_stmt, bool_value, test1, test2, 0, 100, 3, labelName_32Deep, pk_name, true);
    const char *setValue = "test32_deeep.T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/"
                           "T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/T28/T29/T30/T31/A4=100";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        labelName_32Deep, setValue, cond1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 016  Update命令参数错误，如命令拼写问题，参数顺序不对，表名不存在，key值不存在，参数缺失等
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_016)
{
    const char *setValue = "F9\\=200.2 and F10\\=200.86";
    const char *filter = "F1\\<\\=10";
    uint32_t update_value = 1;
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 参数错误
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c updates %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Unknown command:updates");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s selt %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Cmd error, you may miss the keyword set or table name.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s wheere %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Cmd error, you may miss the keyword where or where expression.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    // 参数顺序错误
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s where %s set %s -s %s", ToolPath, g_VertexlabelName,
        filter, setValue, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Cmd error, the keyword 'set' and 'where' may be reversed!");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %d %s -s %s", ToolPath, g_KvName, update_value, "zhangsan"
        , g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    // 表名不存在
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, "test",
        setValue, filter, g_connServer);
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, "test", "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    snprintf(
        compareMess, MAX_CMD_SIZE, "Prepare stmt unsuccessfully when set kvtable, ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, compareMess);
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    EXPECT_EQ(GMERR_OK, ret);
    // 参数缺失
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017 循环执行update命令1万次
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_017)
{
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, count);
    int32_t update_value = 100;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    for (int i = 0; i < cycle_times; i++) {
        ret = executeCommand(g_command, "SET success");
        EXPECT_EQ(GMERR_OK, ret);
    }
    memset(g_command, 0, sizeof(g_command));
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);
    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
    const char *filter = (const char *)"140\\>OP_TX.T1/P5 and OP_TX.T1/P5 \\> 120";
    const char *setValue = "OP_TX.T1/P0\\=100 and OP_TX.T1/P5\\=105";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, g_labelName1,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    for (int i = 0; i < cycle_times; i++) {
        ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
        EXPECT_EQ(GMERR_OK, ret);
    }
    memset(g_command, 0, sizeof(g_command));
}

// 018 满连接后执行update命令，释放链接后执行update命令
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_018)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    uint32_t ret = 0;
    GmcConnT *conn[MAX_CONN_SIZE];
    for (int i = 1; i < MAX_CONN_SIZE; i++) {
        ret = testGmcConnect(&conn[i], NULL);
        if (ret != GMERR_OK) {
            break;
        }
    }
    ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, count);
    int32_t update_value = 100;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Connect with(\"%s\") unsuccessfully!", g_connServer);
    ret = executeCommand(g_command, compareMess);
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    EXPECT_EQ(GMERR_OK, ret);
    const char *filter = (const char *)"140\\>OP_TX.T1/P5 and OP_TX.T1/P5 \\> 120";
    const char *setValue = "OP_TX.T1/P0\\=100 and OP_TX.T1/P5\\=105";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s set %s where %s -s %s", ToolPath, g_KvName,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, compareMess);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i < MAX_CONN_SIZE; i++) {
        ret = testGmcDisconnect(conn[i], NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

void *UpdateKvThread(void *arg)
{
    char command[MAX_CMD_SIZE];
    int index = *(int *)arg;
    snprintf(command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, "KV6", "zhangsan", 100
        , g_connServer);
    int ret = executeCommand(command, "SET success");
    memset(command, 0, sizeof(command));
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *UpdateKvThread1(void *arg)
{
    char command[MAX_CMD_SIZE];
    int index = *(int *)arg;
    snprintf(command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, "KV6", "zhangsan", 10
        , g_connServer);
    int ret = executeCommand(command, "SET success");
    memset(command, 0, sizeof(command));
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *UpdateVertexThread(void *arg)
{
    char command[MAX_CMD_SIZE];
    int index = *(int *)arg;
    const char *setValue = "F1=120 and F2=120 and F3=120 and F4=120 and F5=120 and F7=120 and F8=200 and F21=120";
    const char *filter = "F1\\<\\=10";
    snprintf(command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    int ret = executeCommand(command, "Update set value Successful.", "Update vertex label Successful.");
    memset(command, 0, sizeof(command));
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *UpdateVertexThread1(void *arg)
{
    char command[MAX_CMD_SIZE];
    int index = *(int *)arg;
    const char *setValue = "F17\\=0xf and F18\\=0xf and F19\\=0xf and F20\\=0xf and F12\\=0x756573746572"
                           " and F13\\=0x7565737400";
    const char *filter = "F1\\>\\=10 and F2\\<\\=30";
    snprintf(command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    int ret = executeCommand(command, "Update set value Successful.", "Update vertex label Successful.");
    memset(command, 0, sizeof(command));
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

// 019 多线程并发条件更新vertex表，包含相同条件和不同条件，更新相同字段和不同字段等
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_019)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    int theadCount = 128;
    int index2;
    pthread_t tid_up[theadCount];
    for (int i = 0; i < theadCount / 2; i++) {
        index2 = i;
        ret = pthread_create(&tid_up[index2], NULL, UpdateVertexThread, &index2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid_up[index2 + 64], NULL, UpdateVertexThread1, &index2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < theadCount; i++) {
        pthread_join(tid_up[i], NULL);
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 020 多线程并发更新kv表，包含相同key和不同key
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_020)
{
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    insertKvValue(g_stmt, g_KvName);
    int theadCount = 128;
    pthread_t tid_up[theadCount];
    int index3;
    for (int i = 0; i < theadCount / 2; i++) {
        index3 = i;
        ret = pthread_create(&tid_up[index3], NULL, UpdateKvThread, &index3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&tid_up[index3 + 64], NULL, UpdateKvThread1, &index3);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < theadCount; i++) {
        pthread_join(tid_up[i], NULL);
    }
    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021 校验表名长度128
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_021)
{
    const char *labelName1 = "T1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                             "11111111111111111111111111111111111111";
    uint32_t insert_nums = 100;
    const char *setValue = "F2=1";
    const char *filter = "F1\\<5";
    // 128字节表生成数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, labelName1,
        setValue, filter, g_connServer);
    snprintf(
            compareMess, MAX_CMD_SIZE, "unable to prepare stmt by version in cmd update,labelName =T111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111,ret = %d", GMERR_UNDEFINED_TABLE);
    int ret = executeCommand(g_command, compareMess);
    memset(compareMess, 0, sizeof(compareMess));
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
}

// 022 不插入数据更新kv表数据
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_022)
{
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, count);
    int32_t update_value = 100;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);

    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023 条件更新float/double类型
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_023)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F9\\=200.2 and F10\\=200.86";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 024 条件更新bool类型
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_024)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F6\\=0";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 100; i++) {
        if (i < 11) {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, false, i, i, 'F');
        } else {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
        }
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 025 条件更新字符串类型（string/byte/fixed）
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_025)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F11===estvestvest and F12=vester and F13=veste";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 026 条件更新位域类型（bitfield8/16/32/64）
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_026)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F17\\=200 and F18\\=200 and F19\\=200 and F20\\=200";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 100; i++) {
        if (i < 11) {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, 200, 'F');
        } else {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
        }
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 027 条件更新字符类型（char/uchar）
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_027)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F15=G and F16=G";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 100; i++) {
        if (i < 11) {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'G');
        } else {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
        }
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 028 条件更新time类型
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_028)
{
    int ret = GmcCreateVertexLabel(g_stmt, all_type_label, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入数据
    insertVertexValue(g_stmt, g_VertexlabelName, 100);
    const char *setValue = "F14\\=200";
    const char *filter = "F1\\<\\=10";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        g_VertexlabelName, setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful.", "Update vertex label Successful.");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 100; i++) {
        if (i < 11) {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, 200, i, 'F');
        } else {
            queryVertexValue(g_stmt, g_VertexlabelName, g_VertexpkName, i, i, true, i, i, 'F');
        }
    }
    ret = GmcDropVertexLabel(g_stmt, g_VertexlabelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 029 条件更新partition类型
TEST_F(GmcmdUpdateTest, Tool_023_GmcmdUpdateTest_029)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1015000");
    int32_t ret = 0;
    char *testSchema1 = NULL;
    char *labelName = (char *)"partition_schema";
    int num = 10;
    int affectRows = 0;
    unsigned int len = 0;
    readJanssonFile("./schema_file/partition_schema.gmjson", &testSchema1);
    ASSERT_NE((void *)NULL, testSchema1);
    ret = GmcCreateVertexLabel(g_stmt, testSchema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(testSchema1);
    // 插入数据与查询
    partitionTypeInsert(g_stmt, labelName, 0, num);
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    // 过滤条件为bit类型的删除
    const char *setValue = "F2=1";
    const char *filter = "F1\\<\\=5 and F0\\>\\=0";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath, labelName,
        setValue, filter, g_connServer);
    printf("%s\n", g_command);
    snprintf(compareMess, MAX_CMD_SIZE, "Update vertex label unsuccessfully, ret = %d", GMERR_INTERNAL_ERROR);
    ret = executeCommand(g_command, "Update set value Successful.", compareMess,
        "Update Internal unsucc. Update partition value, old value is 3 new value is 1.");
    memset(g_command, 0, sizeof(g_command));
    memset(compareMess, 0, sizeof(compareMess));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

char *normal_vertexlabel_schema = NULL;
class GmcmdUpdateTest_01 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        if (g_envType != 2) {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
        }
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        // 创建客户端连接
        readJanssonFile("schema_file/Vertex_01_Memory.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        int ret = testGmcConnect(&g_conn, &g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        insertKvValue(g_stmt, g_KvName);
        snprintf(view_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
        printf("%s\n", view_command);
    }

    virtual void TearDown()
    {
        printf("\n======================TEST:END========================\n");
        // 关闭 client g_connection
        int ret = GmcKvDropTable(g_stmt, g_KvName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, "vertex_01_Memory");
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(normal_vertexlabel_schema);
    }
};

// 030  内存写满后条件更新
TEST_F(GmcmdUpdateTest_01, Tool_023_GmcmdUpdateTest_030)
{
    uint32_t i = 0;
    uint32_t ret = 0;
    uint32_t value = 0;
    uint32_t count = 0;
    while (ret == 0) {
        // 写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_01_Memory", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 写string数据
        uint32_t SuperSize = 256;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(g_stmt, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        free(SuperValue);
        ret = GmcExecute(g_stmt);
        i++;
        if ((i % 50000) == 0) {
            printf("till now:insert records %d\n", i);
        }
    }
    // 查询表record数
    system(view_command);
    ret = executeCommand(view_command, "table: vertex_01_Memory", "record count: 10000");
    EXPECT_EQ(GMERR_OK, ret);
    // update vertex
    const char *setValue = "F1=1 and F0=1";
    const char *filter = "F0\\>100 and F1\\<500";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c update %s set %s where %s -s %s", ToolPath,
        "vertex_01_Memory", setValue, filter, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Update set value Successful", "Update vertex label Successful.");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(view_command, "table: vertex_01_Memory", "record count: 10000");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    // update kv
    int32_t update_value = 100;
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "zhangsan",
        update_value, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmcmd -c set kvtable %s %s %d -s %s", ToolPath, g_KvName, "gmdbv5",
        update_value, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "SET success");
    memset(g_command, 0, sizeof(g_command));
    memset(view_command, 0, sizeof(view_command));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, count);
}
