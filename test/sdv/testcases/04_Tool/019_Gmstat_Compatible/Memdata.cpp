/*****************************************************************************
 Description  : gmstat工具兼容性分析 (STORAGE_MEMDATA_STAT)
 Notes        : 001.查询视图新增字段
                002.循环查询视图新增字段
                003.多线程查询视图新增字段
                004.开启事务，查询视图新增字段信息
                005.满连接，查询视图新增字段信息
                006.工具导入表导入数据，查询视图新增字段信息
                007.工具导入KV数据，查询视图新增字段信息
                008.接口导入数据，查询视图新增字段信息
                009.建表，插入数据，查询视图，执行merge操作，查询视图
                010.建表，插入数据，查询视图，执行update操作，查询视图
                011.建表，插入数据，查询视图，执行replace操作，查询视图
                012.建表，批量插入数据，查询视图，删除部分数据，查询视图
                013.建表，并发插入数据，查询视图
                014.建KV表，并发插入数据，查询视图
                015.建KV表，批量插入数据，查询视图，删除部分数据，查询视图
                016.创建多张表，分别执行dml操作，查询视图新增字段信息
                017.查询视图冗余字段
                018.查询视图FREE_CHUNK_COUNT和TOTAL_FREE_CHUNK_COUNT
                019.无表无数据，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
                020.有表有数据后，删数据删表，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
                021.有表且数据未写满内存，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
                022.有表且数据写满内存，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
                023.Devicesize为默认值，校验CHUNK_COUNT_PER_DEVICE值和查询DEVICE_DESCRIPTOR_SIZE值
                024.Devicesize值为：1，校验CHUNK_COUNT_PER_DEVICE值和查询CHUNK_COUNT_PER_DEVICE值
                025.Devicesize值为：1024，校验CHUNK_COUNT_PER_DEVICE值和查询DEVICE_DESCRIPTOR_SIZE值
                026.maxSeMem为默认值，查询DEVICE_MAX_COUNT值
                027.maxSeMem值为：8，查询DEVICE_MAX_COUNT值
                028.maxSeMem值为：2048，查询DEVICE_MAX_COUNT值
                029.构造大对象，插入数据，查询视图

 History      :
 Author       : 廖想
 Modification :
 Date         : 2021/12/16
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define COMPARE_NE(expect_value, actual_value)                                \
    do {                                                                      \
        if ((expect_value) == (actual_value)) {                               \
            printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);       \
            printf("Value of: " #actual_value " = %d\n", (actual_value));     \
            printf("Not Expected: " #expect_value " = %d\n", (expect_value)); \
            return -1;                                                        \
        };                                                                    \
    } while (0)
char g_command[MAX_CMD_SIZE];

GmcConnT *g_conn1, *g_conn2, *g_conn3;
GmcStmtT *g_stmt1, *g_stmt2, *g_stmt3;
char *g_schema1 = NULL, *g_schema2 = NULL, *g_schema3 = NULL;
const char *labelname1 = "OP_T0", *labelname2 = "OP_T1", *labelname3 = "OP_T2", *labelname4 = "OP_T3",
           *labelname5 = "OP_T4";
const char *pk_name1 = "OP_T0_PK", *pk_name2 = "OP_T1_PK", *pk_name3 = "OP_T2_PK", *pk_name4 = "OP_T3_PK",
           *pk_name5 = "OP_T4_PK";
int g_start_num = 0, g_end_num = 1000,
    deviceCount = 0, deviceCount1 = 0, deviceCount2 = 0,
    perDevice = 0, perDevice1 = 0, perDevice2 = 0,
    freeChunkCount = 0, freeChunkCount1 = 0, freeChunkCount2 = 0,
    totalFreeChunkCount = 0, totalFreeChunkCount1 = 0, totalFreeChunkCount2 = 0,
    idleDeviceCount = 0, idleDeviceCount1 = 0, idleDeviceCount2 = 0;

class Memdata : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");  // 2022.01.17
                                                                        // v5表带有superfield字段的需要修改配置项为0
        system("sh $TEST_HOME/tools/start.sh");

        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

int get_memdata_size(char *cmd, int memdata)
{
    FILE *pf = popen(cmd, "r");
    char s[100] = {0};
    if (pf == NULL) {
        LOG("popen(%s) error.\n", cmd);
        EXPECT_EQ(0, 1);
        return -1;
    }
    fgets(s, 100, pf);
    memdata = atoi(s); // 字符串转整型
    pclose(pf);
    return memdata;
}

int get_value(const char *command, string key_word)
{
    const char *keyWordCh = key_word.c_str();
    string value_str;
    int value;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        cout << endl << "WRONG!" << endl;
        return -1;
    }
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        value_str.assign(cmdOutPut);
        string::size_type idx = value_str.find(key_word);
        if (idx != string::npos) {
            value_str = value_str.substr(value_str.find(key_word) + key_word.length());
            value = stoi(value_str);
            break;
        }
    }
    pclose(pf);
 
    return value;
}

void Memdata::SetUp()
{
    printf("---------------START--------------\n");

    // 创建同步连接
    int ret = 0;
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取 FREE_CHUNK_COUNT_IN_DEVICE 初始值
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount.log |cut -d ':' -f 2"); // 截取字段值
    freeChunkCount1 = get_memdata_size(g_command, freeChunkCount1);

    // 获取 DEVICE_COUNT 初始值
    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'DEVICE_COUNT'"
            " > deviceCount.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat deviceCount.log |cut -d ':' -f 2");
    deviceCount1 = get_memdata_size(g_command, deviceCount1);

    // 获取 TOTAL_FREE_CHUNK_COUNT 初始值
    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'TOTAL_FREE_CHUNK_COUNT'"
            " > totalFreeChunkCount1.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat totalFreeChunkCount1.log |cut -d ':' -f 2");
    totalFreeChunkCount1 = get_memdata_size(g_command, totalFreeChunkCount1);

    // 获取 IDLE_DEVICE_COUNT 初始值
    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'IDLE_DEVICE_COUNT'"
            " > idleDeviceCount1.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat idleDeviceCount1.log |cut -d ':' -f 2");
    idleDeviceCount1 = get_memdata_size(g_command, idleDeviceCount1);

    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", deviceCount1, freeChunkCount1);
    AW_FUN_Log(LOG_DEBUG, "TOTAL_FREE_CHUNK_COUNT: %d, IDLE_DEVICE_COUNT: %d\n",
                totalFreeChunkCount1, idleDeviceCount1);

    AW_CHECK_LOG_BEGIN();
}

void Memdata::TearDown()
{
    AW_CHECK_LOG_END();

    // 断连
    int ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    printf("---------------END----------------\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f3_value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t f4_value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f6_value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool f8_value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t f9_value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f10_value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float f11_value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double f12_value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f13_value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char f14_value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);
    char f15_value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, f15_value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char f16_value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, f16_value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i, const char *labelname, const char *pk_name)
{
    int ret = 0;

    // scan
    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    uint32_t pk_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        // Get F0
        char f0_value = i;
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F1
        unsigned char f1_value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &f1_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F2
        int8_t f2_value = i;
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &f2_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F3
        uint8_t f3_value = i;
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F4
        int16_t f4_value = i;
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F5
        uint16_t f5_value = i;
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F6
        int32_t f6_value = i;
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &f6_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F8
        bool f8_value = false;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F10
        uint64_t f10_value = i;
        ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F11
        float f11_value = i;
        ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F12
        double f12_value = i;
        ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F13
        uint64_t f13_value = i;
        ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &f13_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F14
        char f14_value[] = "testver";
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, &f14_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F15
        char f15_value[12] = "12";
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, &f15_value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F16
        char f16_value[12] = "13";
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, &f16_value);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile)
{
    int ret = 0;
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
}

// 比较两个文本文件
int compare_file_content(char *actual_file_path, char *expect_file_path, int lenth = 2048)
{
    int ret = 0;
    char *expect_value = NULL;
    ret = readJanssonFile(expect_file_path, &expect_value);
    COMPARE_NE((char *)NULL, expect_value);

    char *actual_value = NULL;
    ret = readJanssonFile(actual_file_path, &actual_value);
    COMPARE_NE((char *)NULL, (char *)actual_value);

    ret = strncmp(expect_value, actual_value, lenth);
    if (ret != 0) {
        printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);
        printf("Value of: actual_value : \n%s\n", actual_value);
        printf("Expected: expect_value : \n%s\n", expect_value);
    };

    free(expect_value);
    free(actual_value);
    return ret;
}

// 001.查询视图新增字段
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_001)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.循环查询视图新增字段
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_002)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 循环查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    for (int i = g_start_num; i < g_end_num / 10; i++) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
            view_name);
        // printf("g_command = %s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
        EXPECT_EQ(GMERR_OK, ret);
        ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
        EXPECT_EQ(GMERR_OK, ret);
        ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_sub_view_Multi(void *arg)
{
    int ret;
    // 视图打印
    for (int i = 0; i < 10; i++) {
        if (g_envType == 3) {
            ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
            ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
            EXPECT_EQ(GMERR_OK, ret);
            ret = executeCommand(g_command, "DEVICE_ID", "STATUS",
                "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = testScanSysview("V$STORAGE_MEMDATA_STAT", (char *)"OWN_ID", (char *)"0",
                (char *)"default", false, false);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testScanSysview("V$STORAGE_MEMDATA_STAT", (char *)"CHUNK_COUNT_PER_DEVICE", (char *)"0",
                (char *)"default", false, false);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testScanSysview("V$STORAGE_MEMDATA_STAT", (char *)"DEVICE_DESCRIPTOR_SIZE", (char *)"0",
                (char *)"default", false, false);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testScanSysview("V$STORAGE_MEMDATA_STAT", (char *)"IDLE_DEVICE_COUNT", (char *)"0",
                (char *)"default", false, false);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testScanSysview("V$STORAGE_MEMDATA_STAT", (char *)"DEVICE_MAX_COUNT", (char *)"0",
                (char *)"default", false, false);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testScanSysview("V$STORAGE_MEMDATA_STAT", (char *)"DEVICE_MEMORY_CONTEXT_ID", (char *)"0",
                (char *)"default", false, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    return NULL;
}
// 003.多线程查询视图新增字段
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_003)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    //多线程查看视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);

    int tdNum = 4;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, thread_sub_view_Multi, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.开启事务，查询视图新增字段信息
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_004)
{
    int ret = 0;
    char labelConfig[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, labelConfig);
    ASSERT_EQ(GMERR_OK, ret);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;

    // 开启事务
    ret = GmcTransStart(g_conn1, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn1);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.满连接，查询视图新增字段信息
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_005)
{
    int ret = 0;

    // 满连接
    GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    ASSERT_EQ(0, ret);

    for (int i = 0; i < (MAX_CONN_SIZE - 3 - existConnNum); i++) {
        ret = testGmcConnect(&conn[i]);
        if (ret != GMERR_OK) {
            printf("i: %d, ret: %d\n", i, ret);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);

    // 断连
    for (int i = 0; i < (MAX_CONN_SIZE - 3 - existConnNum); i++) {
        ret = testGmcDisconnect(conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 006.工具导入表导入数据，查询视图新增字段信息
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_006)
{
    const char *g_vertexPath = "./schema_file/NormalVertexLabel_001.gmjson";
    const char *g_dataPath = "./schema_file/OP_T0.vertexdata";  // 必须以表名开头

    int ret;

    // 导入 schema 建表
    GmcDropVertexLabel(g_stmt1, labelname1);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_vertexPath, g_connServer);
    printf("g_command1 = %s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/04_Tool/019_Gmstat_Compatible/schema_file/NormalVertexLabel_001.gmjson\" "
        "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 导入 data 插入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s", g_toolPath, g_dataPath, g_connServer);
    printf("g_command2 = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 100000, successNum: 100000, duplicateNum: 0",
        "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/04_Tool/019_Gmstat_Compatible/schema_file/OP_T0.vertexdata\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command3 = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.工具导入KV数据，查询视图新增字段信息
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_007)
{
    const char *kvTableName = "KV0";
    char g_config[128] = "{\"max_record_count\":100000,\"writers\":\"abc\",\"max_record_count_check\":false}";
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    /* ------------------------ 准备导入的kv数据 --------------------------- */
    // create kv table
    GmcKvDropTable(g_stmt1, kvTableName);
    ret = GmcKvCreateTable(g_stmt1, kvTableName, g_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);

    // 初始化BatchOption结构体中的所有设置为默认值
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改batchOption结构体中的批量执行顺序设置
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    ret = GmcBatchPrepare(g_conn1, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT KvInfo1 = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan_%d", i);
        int32_t value1 = i;
        KvInfo1.key = key_set;
        KvInfo1.keyLen = strlen(key_set);
        KvInfo1.value = &value1;
        KvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt1, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, g_end_num);
    EXPECT_EQ(successNum, g_end_num);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;

    // gmexport 工具导出kv数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c kvdata -t KV0 -f schema_file   -s %s", g_toolPath,
        g_connServer);
    printf("\n g_command1 is : %s \n \n", g_command);
    ret = executeCommand(g_command, "Command type: export_kv, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    /* -------------------- 导入 kv 数据 ---------------------- */

    // drop kv table
    ret = GmcKvDropTable(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);
    // create kv table
    ret = GmcKvCreateTable(g_stmt1, kvTableName, g_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);

    // 导入kv数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c kvdata -f schema_file/KV0.gmkv -t %s   -s %s",
        g_toolPath, kvTableName, g_connServer);
    printf("\n g_command2 is : %s \n\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Insert data succeed. successNum: 1000",
        "Command type: import_kvdata, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/019_Gmstat_Compatible/schema_file/KV0.gmkv\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop kv table
    ret = GmcKvDropTable(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 008.接口导入数据，查询视图新增字段信息
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_008)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt1, "./schema_file/NormalVertexLabel_001.vertexdata");

    // query
    for (int i = g_start_num; i < g_end_num / 10; i++) {
        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "OWN_ID", "CHUNK_COUNT_PER_DEVICE", "DEVICE_DESCRIPTOR_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT", "DEVICE_MAX_COUNT", "DEVICE_MEMORY_CONTEXT_ID");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DEVICE_ID", "STATUS", "FREE_CHUNK_COUNT_IN_DEVICE", "OFFSET", "SEGMENT_ID");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.建表，插入数据，查询视图，执行merge操作，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_009)
{
    int ret = 0;
    char *insert_view = (char *)"./compare_file/Memdata_insert09.txt";
    char *merge_view = (char *)"./compare_file/Memdata_merge09.txt";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_insert09.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command1 = %s\n", g_command);
    system(g_command);

    // merge && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt1, pk_name1);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        // set_VertexProperty_PK(g_stmt1, i + g_end_num);
        set_VertexProperty(g_stmt1, i + g_end_num);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i + g_end_num, labelname1, pk_name1);
    }

    // 查询视图新增字段
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_merge09.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command2 = %s\n", g_command);
    system(g_command);

    // 比较两次查询的视图信息是否一样
    ret = compare_file_content(insert_view, merge_view, 2048);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.建表，插入数据，查询视图，执行update操作，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_010)
{
    int ret = 0;
    char *insert_view = (char *)"./compare_file/Memdata_insert10.txt";
    char *update_view = (char *)"./compare_file/Memdata_update10.txt";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_insert10.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command1 = %s\n", g_command);
    system(g_command);

    // update && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt1, pk_name1);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty(g_stmt1, i + g_end_num);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i + g_end_num, labelname1, pk_name1);
    }

    // 查询视图新增字段
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_update10.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command2 = %s\n", g_command);
    system(g_command);

    // 比较两次查询的视图信息是否一样
    ret = compare_file_content(insert_view, update_view, 2048);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011.建表，插入数据，查询视图，执行replace操作，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_011)
{
    int ret = 0;
    char *insert_view = (char *)"./compare_file/Memdata_insert11.txt";
    char *replace_view = (char *)"./compare_file/Memdata_replace11.txt";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_insert11.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command1 = %s\n", g_command);
    system(g_command);

    // replace && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i + g_end_num);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i + g_end_num, labelname1, pk_name1);
    }

    // 查询视图新增字段
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_replace11.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command2 = %s\n", g_command);
    system(g_command);

    // 比较两次查询的视图信息是否一样
    ret = compare_file_content(insert_view, replace_view, 2048);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.建表，批量插入数据，查询视图，删除部分数据，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_012)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    // 初始化BatchOption结构体中的所有设置为默认值
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改batchOption结构体中的批量执行顺序设置。
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改batchOption结构体中的批量报文大小上限
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    ret = GmcBatchPrepare(g_conn1, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcBatchAddDML(batch, g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(g_end_num, totalNum);
    EXPECT_EQ(g_end_num, successNum);

    // query
    for (int i = g_start_num; i < g_end_num; i++) {
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 写入1K数据后，获取 FREE_CHUNK_COUNT_IN_DEVICE、DEVICE_COUNT
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount2.log |cut -d ':' -f 2");
    freeChunkCount2 = get_memdata_size(g_command, freeChunkCount2);

    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'DEVICE_COUNT'"
            " > deviceCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat deviceCount2.log |cut -d ':' -f 2");
    deviceCount2 = get_memdata_size(g_command, deviceCount2);
    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", deviceCount2, freeChunkCount2);

    // 获取 1K数据数据占用的pagesize
    freeChunkCount = freeChunkCount1 - freeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "1K数据数据占用的pagesize: %d\n", freeChunkCount);

    // 根据主键删除前500条数据
    for (int i = g_start_num; i < g_end_num / 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt1, pk_name1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 0.5K数据，获取 FREE_CHUNK_COUNT_IN_DEVICE、DEVICE_COUNT
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount2.log |cut -d ':' -f 2");
    freeChunkCount2 = get_memdata_size(g_command, freeChunkCount2);

    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'DEVICE_COUNT'"
            " > deviceCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat deviceCount2.log |cut -d ':' -f 2");
    deviceCount2 = get_memdata_size(g_command, deviceCount2);
    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", deviceCount2, freeChunkCount2);

    // 0.5K数据数据占用的pagesize
    freeChunkCount = freeChunkCount1 - freeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "0.5K数据数据占用的pagesize: %d\n", freeChunkCount);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

void *BatchInsert(void *args)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int count = (int)(*(int *)args);  // 通过count构造 来使并发插入不出现相同的主键值
    const char *OP_T3_superfields = "OP_T3_superfields";
    int ret;

    // 建链
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert data
    ret = testGmcPrepareStmtByLabelName(stmt, labelname4, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量准备
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 30; i++) {
        int64_t F0_value = i * (i + 10) + count * 1000;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);

        //写fixed数据
        uint32_t SuperSize = 256 * 16;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'A', SuperSize);

        ret = GmcSetSuperfieldByName(stmt, OP_T3_superfields, SuperValue, SuperSize);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);

        free(SuperValue);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(30, totalNum);
    EXPECT_EQ(30, successNum);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
// 013.建表，并发插入数据，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_013)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname4);
    readJanssonFile("./schema_file/SuperfiledVertexLabel.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 并发批量写
    int tdNum = 30;
    int err = 0;
    pthread_t sameNameth[tdNum];
    int index[tdNum];
    void *retval[tdNum];
    for (int i = 0; i < tdNum; i++) {
        index[i] = i;
        pthread_create(&sameNameth[i], NULL, BatchInsert, (void *)&index[i]);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], &retval[i]);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command4 = %s\n", g_command);
    // system(g_command);
#if defined ENV_SUSE
    ret = executeCommand(g_command, "DEVICE_COUNT: 5", "CHUNK_COUNT_PER_DEVICE: 128", "DEVICE_ID: 0",
        "STATUS: DEV_USED", "FREE_CHUNK_COUNT_IN_DEVICE: 109");
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "DEVICE_COUNT: 2", "CHUNK_COUNT_PER_DEVICE: 128", "DEVICE_ID: 0",
        "STATUS: DEV_FULL", "FREE_CHUNK_COUNT_IN_DEVICE: 0");
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname4);
    EXPECT_EQ(GMERR_OK, ret);
}

void *BatchSetKvTable(void *args)
{
    const char *kvTableName = "KV0";
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    GmcConnT *conn;
    GmcStmtT *stmt;

    // 建连
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 初始化BatchOption结构体中的所有设置为默认值
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改batchOption结构体中的批量执行顺序设置
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT KvInfo1 = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan_%d", i);
        int32_t value1 = i;
        KvInfo1.key = key_set;
        KvInfo1.keyLen = strlen(key_set);
        KvInfo1.value = &value1;
        KvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(stmt, key_set, strlen(key_set), &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, g_end_num);
    EXPECT_EQ(successNum, g_end_num);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    return NULL;
}
// 014.建KV表，并发插入数据，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_014)
{
    const char *kvTableName = "KV0";
    char g_config[128] = "{\"max_record_count\":100000,\"writers\":\"abc\",\"max_record_count_check\":false}";
    int ret = 0;

    // create kv table
    GmcKvDropTable(g_stmt1, kvTableName);
    ret = GmcKvCreateTable(g_stmt1, kvTableName, g_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 并发批量写
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int tdNum = MAX_CONN_SIZE - existConnNum;
    AW_FUN_Log(LOG_STEP, "创建%d个线程", tdNum);
#else
    int tdNum = 30;
    AW_FUN_Log(LOG_STEP, "创建%d个线程", tdNum);
#endif

    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        pthread_create(&sameNameth[i], NULL, BatchSetKvTable, NULL);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    // 并发插入数据，获取 FREE_CHUNK_COUNT_IN_DEVICE、DEVICE_COUNT
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount2.log |cut -d ':' -f 2");
    freeChunkCount2 = get_memdata_size(g_command, freeChunkCount2);

    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'DEVICE_COUNT'"
            " > deviceCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat deviceCount2.log |cut -d ':' -f 2");
    deviceCount2 = get_memdata_size(g_command, deviceCount2);
    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", deviceCount2, freeChunkCount2);

    // 获取数据占用的pagesize
    freeChunkCount = freeChunkCount1 - freeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "并发插入数据占用的pagesize: %d\n", freeChunkCount);

    // drop kv table
    ret = GmcKvDropTable(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 015.建KV表，批量插入数据，查询视图，删除部分数据，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_015)
{
    const char *kvTableName = "KV0";
    char g_config[128] = "{\"max_record_count\":100000,\"writers\":\"abc\",\"max_record_count_check\":false}";
    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    // create kv table
    GmcKvDropTable(g_stmt1, kvTableName);
    ret = GmcKvCreateTable(g_stmt1, kvTableName, g_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);

    // 初始化BatchOption结构体中的所有设置为默认值
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改batchOption结构体中的批量执行顺序设置
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    ret = GmcBatchPrepare(g_conn1, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT KvInfo1 = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan_%d", i);
        int32_t value1 = i;
        KvInfo1.key = key_set;
        KvInfo1.keyLen = strlen(key_set);
        KvInfo1.value = &value1;
        KvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt1, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, g_end_num);
    EXPECT_EQ(successNum, g_end_num);

    // 插入1024条数据，获取 FREE_CHUNK_COUNT_IN_DEVICE、DEVICE_COUNT
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount2.log |cut -d ':' -f 2");
    freeChunkCount2 = get_memdata_size(g_command, freeChunkCount2);

    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'DEVICE_COUNT'"
            " > deviceCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat deviceCount2.log |cut -d ':' -f 2");
    deviceCount2 = get_memdata_size(g_command, deviceCount2);
    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", deviceCount2, freeChunkCount2);

    // 获取1024条数据占用的pagesize
    freeChunkCount = freeChunkCount1 - freeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "1024条数据占用的pagesize: %d\n", freeChunkCount);

    // batch delete
    char key_delete[1024];
    for (int i = 0; i < g_end_num / 1; i++) {
        sprintf(key_delete, "zhangsan_%d", i);
        KvInfo1.key = key_delete;
        KvInfo1.keyLen = strlen(key_delete);
        KvInfo1.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt1, key_delete, strlen(key_delete), NULL, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt1, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, g_end_num / 1);
    EXPECT_EQ(successNum, g_end_num / 1);

    // 删除全部数据，获取 FREE_CHUNK_COUNT_IN_DEVICE、DEVICE_COUNT
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount2.log |cut -d ':' -f 2");
    freeChunkCount2 = get_memdata_size(g_command, freeChunkCount2);

    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'DEVICE_COUNT'"
            " > deviceCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat deviceCount2.log |cut -d ':' -f 2");
    deviceCount2 = get_memdata_size(g_command, deviceCount2);
    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", deviceCount2, freeChunkCount2);

    // 获取空数据占用的pagesize
    freeChunkCount = freeChunkCount1 - freeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "空数据占用的pagesize: %d\n", freeChunkCount);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;

    // 删除KvTable
    ret = GmcKvDropTable(g_stmt1, kvTableName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 016.创建多张表，分别执行dml操作，查询视图新增字段信息
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_016)
{
    int ret = 0;
    char *insert_view = (char *)"./compare_file/Memdata_insert16.txt";
    char *dml_view = (char *)"./compare_file/Memdata_dml16.txt";

    // create vertexlabel 1
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    // create vertexlabel 2
    GmcDropVertexLabel(g_stmt2, labelname2);
    readJanssonFile("./schema_file/NormalVertexLabel_002.gmjson", &g_schema2);
    ASSERT_NE((void *)NULL, g_schema2);
    ret = GmcCreateVertexLabel(g_stmt2, g_schema2, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn3, &g_stmt3);
    EXPECT_EQ(GMERR_OK, ret);
    // create vertexlabel 3
    GmcDropVertexLabel(g_stmt3, labelname3);
    readJanssonFile("./schema_file/NormalVertexLabel_003.gmjson", &g_schema3);
    ASSERT_NE((void *)NULL, g_schema3);
    ret = GmcCreateVertexLabel(g_stmt3, g_schema3, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        /* ------------- 表 1 ------------- */
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);

        /* ------------- 表 2 ------------- */
        ret = testGmcPrepareStmtByLabelName(g_stmt2, labelname2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt2, i);
        set_VertexProperty(g_stmt2, i);
        ret = GmcExecute(g_stmt2);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt2, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt2, i, labelname2, pk_name2);

        /* ------------- 表 3 ------------- */
        ret = testGmcPrepareStmtByLabelName(g_stmt3, labelname3, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt3, i);
        set_VertexProperty(g_stmt3, i);
        ret = GmcExecute(g_stmt3);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt3, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt3, i, labelname3, pk_name3);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_insert16.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command1 = %s\n", g_command);
    system(g_command);

    // 表1 merge && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt1, pk_name1);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        // set_VertexProperty_PK(g_stmt1, i + g_end_num);
        set_VertexProperty(g_stmt1, i + g_end_num);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i + g_end_num, labelname1, pk_name1);
    }

    // 表2 update && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt2, labelname2, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt2, pk_name2);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty(g_stmt2, i + g_end_num);
        ret = GmcExecute(g_stmt2);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt2, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt2, i + g_end_num, labelname2, pk_name2);
    }

    // 表3 replace && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt3, labelname3, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt3, i);
        set_VertexProperty(g_stmt3, i + g_end_num);
        ret = GmcExecute(g_stmt3);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt3, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt3, i + g_end_num, labelname3, pk_name3);
    }

    // 查询视图新增字段
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >./compare_file/Memdata_dml16.txt",
        g_toolPath, g_connServer, view_name);
    printf("g_command2 = %s\n", g_command);
    system(g_command);

    // 比较两次查询的视图信息是否一样
    ret = compare_file_content(insert_view, dml_view, 2048);

    // drop vertexlabel 1
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
    // drop vertexlabel 2
    free(g_schema2);
    ret = GmcDropVertexLabel(g_stmt2, labelname2);
    EXPECT_EQ(GMERR_OK, ret);
    // drop vertexlabel 3
    free(g_schema3);
    ret = GmcDropVertexLabel(g_stmt3, labelname3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn2, g_stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.查询视图冗余字段
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_017)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图冗余字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "TOTAL_PAGE_DESC_COUNT", "PAGE_DESC_COUNT_USED", "EXTENT_ARRAY_COUNT",
        "EXTENT_ARRAY_COUNT_USED", "EACH_EXT_PAGE_DESC_COUNT");
    EXPECT_EQ(-1, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.查询视图FREE_CHUNK_COUNT和TOTAL_FREE_CHUNK_COUNT
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_018)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图FREE_CHUNK_COUNT
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    // system(g_command);
    ret = executeCommand(g_command, "  FREE_CHUNK_COUNT: ");  // 该字段不存在，预期匹配失败
    EXPECT_EQ(-1, ret);

    // 获取当前空闲内存页的总个数
    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'TOTAL_FREE_CHUNK_COUNT'"
            " > totalFreeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat totalFreeChunkCount2.log |cut -d ':' -f 2");
    totalFreeChunkCount2 = get_memdata_size(g_command, totalFreeChunkCount2);
    AW_FUN_Log(LOG_DEBUG, "DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d, TOTAL_FREE_CHUNK_COUNT: %d\n",
            deviceCount2, freeChunkCount2, totalFreeChunkCount2);

    // 获取数据占用的内存页的总个数
    totalFreeChunkCount = totalFreeChunkCount1 - totalFreeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "1K数据占用的pagesize: %d\n", totalFreeChunkCount);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.无表无数据，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_019)
{
    int ret;

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    // system(g_command);
    ret = executeCommand(g_command, "STATUS: DEV_USED");
    EXPECT_EQ(GMERR_OK, ret);

    // 获取 空数据使用的device个数
    AW_FUN_Log(LOG_DEBUG, "空数据使用的device个数: %d\n", idleDeviceCount1);
}

class Memdata_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Memdata_01::SetUp()
{
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
}

void Memdata_01::TearDown()
{}

// 020.有表有数据后，删数据删表，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
TEST_F(Memdata_01, Tool_019_020)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/start.sh ");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=4\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num * 30; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command1 = %s\n", g_command);
    // 2023.07.13 undo也会占用一个device页
#if defined ENV_SUSE
    ret = executeCommand(g_command, "STATUS: DEV_USED");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT: 0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "FREE_CHUNK_COUNT_IN_DEVICE: 111");
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "STATUS: DEV_USED");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT: 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "FREE_CHUNK_COUNT_IN_DEVICE: 127");
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 021.有表且数据未写满内存，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_021)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    // system(g_command);
    ret = executeCommand(g_command, "STATUS: DEV_USED");
    EXPECT_EQ(GMERR_OK, ret);

    // 写入1K数据后，获取 FREE_CHUNK_COUNT_IN_DEVICE、IDLE_DEVICE_COUNT
    snprintf(g_command,MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 6 'DEVICE_ID: 0'"
            "| grep -E -A 0 'FREE_CHUNK_COUNT_IN_DEVICE' > freeChunkCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat freeChunkCount2.log |cut -d ':' -f 2");
    freeChunkCount2 = get_memdata_size(g_command, freeChunkCount2);

    snprintf(g_command, MAX_CMD_SIZE,"%s/gmsysview -s %s -q %s | grep -E -A 0 'IDLE_DEVICE_COUNT'"
            " > idleDeviceCount2.log\n",
             g_toolPath, g_connServer, view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat idleDeviceCount2.log |cut -d ':' -f 2");
    idleDeviceCount2 = get_memdata_size(g_command, idleDeviceCount2);
    AW_FUN_Log(LOG_DEBUG, "IDLE_DEVICE_COUNT: %d, FREE_CHUNK_COUNT_IN_DEVICE: %d\n", idleDeviceCount2, freeChunkCount2);

    // 获取 1K数据占用的pagesize
    freeChunkCount = freeChunkCount1 - freeChunkCount2;
    AW_FUN_Log(LOG_DEBUG, "1K数据数据占用的pagesize: %d\n", freeChunkCount);
    
    // 获取 1K数据使用的device个数
    if (idleDeviceCount1 == idleDeviceCount2) {
        AW_FUN_Log(LOG_DEBUG, "1K数据使用的device个数: %d\n", deviceCount1);
    } else {
        idleDeviceCount = idleDeviceCount1 - idleDeviceCount2;
        AW_FUN_Log(LOG_DEBUG, "1K数据使用的device个数: %d\n", idleDeviceCount);
    }

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.有表且数据写满内存，校验STATUS值和查询IDLE_DEVICE_COUNT值、FREE_CHUNK_COUNT_IN_DEVICE值
TEST_F(Memdata_01, Tool_019_022)
{
    int ret = 0, k = 0;

    system("sh $TEST_HOME/tools/start.sh ");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/stop.sh");                        //修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  //内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f ");
    } else if (g_envType == 1) {
        system("sh $TEST_HOME/tools/stop.sh");                       //修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=30\"");  //内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f ");
    } else {
        printf("IoT does not need to be modified.\n");
    }

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 数据写满内存
    while (1) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, k);
        set_VertexProperty(g_stmt1, k);
        ret = GmcExecute(g_stmt1);
        if (ret != 0) {
            printf("------------- insert num = %d, ret = %d -------------\n", k, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
        k++;

        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "STATUS: DEV_FULL");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "IDLE_DEVICE_COUNT: 0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "FREE_CHUNK_COUNT_IN_DEVICE: 0");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 023.Devicesize为默认值，校验CHUNK_COUNT_PER_DEVICE值和查询DEVICE_DESCRIPTOR_SIZE值
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_023)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
#if defined ENV_RTOSV2
    ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 128", "DEVICE_DESCRIPTOR_SIZE: 12");
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#elif defined ENV_RTOSV2X
    ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 1024", "DEVICE_DESCRIPTOR_SIZE: 12");
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 512", "DEVICE_DESCRIPTOR_SIZE: 12");
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#elif FEATURE_PERSISTENCE
    char cmd[MAX_CMD_SIZE];
    const char *viewName = "V\\$DB_SERVER";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, viewName, g_connServer);
    ret = executeCommand(cmd, "release");

    if (ret == GMERR_OK) {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 128", "DEVICE_DESCRIPTOR_SIZE: 12");  // release
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 128", "DEVICE_DESCRIPTOR_SIZE: 16");  // debug
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#else
    ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 128", "DEVICE_DESCRIPTOR_SIZE: 12");
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024.Devicesize值为：1，校验CHUNK_COUNT_PER_DEVICE值和查询CHUNK_COUNT_PER_DEVICE值
TEST_F(Memdata_01, Tool_019_024)
{
#ifdef FEATURE_PERSISTENCE
    system("rm -rf /data/gmdb/*");
#endif
    int ret = 0;

    system("sh $TEST_HOME/tools/start.sh ");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    system(g_command);

#if FEATURE_PERSISTENCE
    char cmd[MAX_CMD_SIZE];
    const char *viewName = "V\\$DB_SERVER";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, viewName, g_connServer);
    ret = executeCommand(cmd, "release");
    if (ret == GMERR_OK) {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 32", "DEVICE_DESCRIPTOR_SIZE: 12");  // release
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 32", "DEVICE_DESCRIPTOR_SIZE: 16");  // debug
        EXPECT_EQ(GMERR_OK, ret);
    }
#else
    ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 32", "DEVICE_DESCRIPTOR_SIZE: 12");
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 025.Devicesize值为：1024，校验CHUNK_COUNT_PER_DEVICE值和查询DEVICE_DESCRIPTOR_SIZE值
TEST_F(Memdata_01, Tool_019_025)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh ");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
        system("ipcs");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=512\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    } else {
        system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
        system("ipcs");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=128\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    }

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    system(g_command);

#if defined ENV_RTOSV2 && defined CPU_BIT_32
    ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 4096", "DEVICE_DESCRIPTOR_SIZE: 12");
    EXPECT_EQ(GMERR_OK, ret);
#elif FEATURE_PERSISTENCE
    char cmd[MAX_CMD_SIZE];
    const char *viewName = "V\\$DB_SERVER";
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, viewName, g_connServer);
    ret = executeCommand(cmd, "release");
    if (ret == GMERR_OK) {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 4096", "DEVICE_DESCRIPTOR_SIZE: 12"); // release
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 4096", "DEVICE_DESCRIPTOR_SIZE: 16"); // debug
        EXPECT_EQ(GMERR_OK, ret);
    }

#else
    if (g_envType == 0) {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 16384", "DEVICE_DESCRIPTOR_SIZE: 12");
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = executeCommand(g_command, "CHUNK_COUNT_PER_DEVICE: 4096", "DEVICE_DESCRIPTOR_SIZE: 12");
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 026.maxSeMem为默认值，查询DEVICE_MAX_COUNT值
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_026)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);

    int deviceMaxCount = get_value(g_command, "DEVICE_MAX_COUNT: ");
    AW_FUN_Log(LOG_STEP, "deviceMaxCount is %d", deviceMaxCount);
#if defined ENV_RTOSV2
    EXPECT_GE(deviceMaxCount, 200); //RTOSV2:209, arm32:256
    EXPECT_LE(deviceMaxCount, 300);
#elif defined ENV_RTOSV2X // SOHO, AP, maxSeMem配置项不一致
    ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 16"); // DEVICE_MAX_COUNT = maxSeMem / deviceSize
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 17");
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#else
    ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 256");
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 027.maxSeMem值为：8，查询DEVICE_MAX_COUNT值
TEST_F(Memdata_01, Tool_019_027)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/start.sh ");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=4\"");

#ifdef ENV_SUSE
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=16\"");  // 光启maxSeMem >= 4*deviceSize
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");  // 2022.01.21 deviceSize 默认值从 4m 改成 32m,maxSeMem
                                                                // 必须大于 deviceSize
#endif

    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        // query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);

#ifdef ENV_SUSE
    ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 4");
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 2");
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 028.maxSeMem值为：2048，查询DEVICE_MAX_COUNT值
TEST_F(Memdata_01, Tool_019_028)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/start.sh ");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=512\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 128");
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    EXPECT_EQ(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 029.构造大对象，插入数据，查询视图
TEST_F(Memdata, Tool_019_Gmstat_Compatible_Memdata_029)
{
    int ret = 0;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname5);
    readJanssonFile("./schema_file/BigObjectVertexLabel.gmjson", &g_schema1);
    ASSERT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname5, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);

        // query
        query_VertexProperty(g_stmt1, i, labelname5, pk_name5);
    }

    // 查询视图新增字段
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("g_command = %s\n", g_command);
    // system(g_command);

    int deviceSize = get_value(g_command, "DEVICE_SIZE: ");
    AW_FUN_Log(LOG_STEP, "deviceSize is %d", deviceSize);
    int maxMemSize = get_value(g_command, "MAX_MEM_SIZE: ");
    AW_FUN_Log(LOG_STEP, "maxMemSize is %d", maxMemSize);
#if defined ENV_RTOSV2
    EXPECT_GE(deviceSize, 4000000); //RTOSV2:4194304
    EXPECT_LE(deviceSize, 5000000);
    EXPECT_GE(maxMemSize, 800000000); //RTOSV2:876609536, arm32:1073741824
    EXPECT_LE(maxMemSize, 1100000000);
#elif defined ENV_RTOSV2X // SOHO, AP, maxSeMem配置项不一致
    ret = executeCommand(g_command, "DEVICE_SIZE: 4194304");
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
    ret = executeCommand(g_command, "MAX_MEM_SIZE: 67108864"); // maxSeMem
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "MAX_MEM_SIZE: 71303168");
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#else
    ret = executeCommand(g_command, "DEVICE_SIZE: 4194304");
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
    ret = executeCommand(g_command, "MAX_MEM_SIZE: 1073741824");
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        system(g_command);
    }
#endif

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname5);
    EXPECT_EQ(GMERR_OK, ret);
}

// 030.STORAGE_MEMDATA_STAT，1024个device页
TEST_F(Memdata_01, Tool_019_030)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  // 修改配置，先停服务
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"deviceSize=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create vertexlabel
    GmcDropVertexLabel(g_stmt1, labelname1);
    readJanssonFile("./schema_file/NormalVertexLabel_001.gmjson", &g_schema1);
    AW_MACRO_ASSERT_NOTNULL(g_schema1);
    ret = GmcCreateVertexLabel(g_stmt1, g_schema1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert && query
    int i = 0;
    while (1) {
        ret = testGmcPrepareStmtByLabelName(g_stmt1, labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt1, i);
        set_VertexProperty(g_stmt1, i);
        ret = GmcExecute(g_stmt1);
        if (ret == GMERR_OK) {
            // query
            query_VertexProperty(g_stmt1, i, labelname1, pk_name1);
            i++;
        } else if (ret == GMERR_OUT_OF_MEMORY) {
            AW_FUN_Log(LOG_INFO, "最终写入 %d 条数据\n", i);
            break;
        }

        if (i % 10000 == 0) {
            AW_FUN_Log(LOG_INFO, "成功写入 %d 条数据\n", i);
        }
    }

    // 查询视图
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_FUN_Log(LOG_INFO, "[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "DEVICE_MAX_COUNT: 32");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // drop vertexlabel
    free(g_schema1);
    ret = GmcDropVertexLabel(g_stmt1, labelname1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.\n");
}
