#!/bin/bash
# for multi-label

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi

## 数据清除及准备  $CUR_DIR/multi_vertexlabel文件夹
cd $CUR_DIR
rm -r multi_vertexlabel > /dev/null 2>&1
mkdir multi_vertexlabel > /dev/null 2>&1
cp $TEST_HOME/testcases/04_Tool/016_BatchExport/schema_file/BatchExport_op.gmjson ./multi_vertexlabel/BatchExport_op.gmjson
sleep 1

# 构造多个 subinfo
cd $CUR_DIR/multi_vertexlabel
echo create_multi_label $1

for i in $(seq 0 $1)
do	
	cp BatchExport_op.gmjson BatchExport_op_$i.gmjson
	sed -i "s/\"name\":\"OP_T0\"/\"name\": \"OP_T0_"$i"\"/g" BatchExport_op_$i.gmjson
    sed -i "s/\"node\":\"OP_T0\"/\"node\": \"OP_T0_"$i"\"/g" BatchExport_op_$i.gmjson
    sed -i "s/\"node\":\"OP_T0\"/\"node\": \"OP_T0_"$i"\"/g" BatchExport_op_$i.gmjson
done
