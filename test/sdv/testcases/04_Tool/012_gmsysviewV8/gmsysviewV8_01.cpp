/*****************************************************************************
 Description  : gmsysview对接V8命令行，提供内部接口GmcSysview(int32_t argc, char **argv, int32_t (*printFunc)(const char
*format, ...))查询视图， 参数：字符串数组、函数指针、打印函数; 包含头文件： Notes        : Tool_012_001
argc传入0，预期报错 Tool_012_002 argc传入负数，预期报错 Tool_012_003 argv传入NULL，预期报错 Tool_012_004
传入空串数组，预期报错 Tool_012_005 传入int32最大值数组，预期报错 Tool_012_006
print传入NULL，直接打印视图结果，视图命令：CATA_VERTEX_LABEL_INFO Tool_012_007 测试视图命令：gmsysview -h Tool_012_008
测试视图命令：DRT_CONN_STAT Tool_012_009 测试视图命令：QRY_DML_INFO Tool_012_010 测试视图命令：STORAGE_KV_STAT
                Tool_012_011 满连接，内部建连失败
                Tool_012_012 建立1022个连接后，内部占用逃生通道建连
                Tool_012_013 循环调用50次
                Tool_012_014 10个线程并发调用
                Tool_012_015 和dml操作并发查询
                Tool_012_016 record命令
                Tool_012_017 打开鉴权，需要导入白名单
 History      :
 Author       : chenbo cwx5332626
 Modification :
 Date         : 2021/7/19
*****************************************************************************/

#include "commonFunc.h"

class gmsysviewV8_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testEnvClean();
        ASSERT_EQ(GMERR_OK, ret);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void gmsysviewV8_01::SetUp()
{
    // 导入的文件名、清理文件
    sprintf(fileName, "gmsysview-temp-file%d", getpid());
    sprintf(command, "> %s", fileName);
    system(command);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_OPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
}

void gmsysviewV8_01::TearDown()
{
    AW_CHECK_LOG_END();
    // 删除文件
    sprintf(fileName, "gmsysview-temp-file%d", getpid());
    sprintf(command, "rm -rf %s", fileName);
    system(command);
}

// argc 传0
TEST_F(gmsysviewV8_01, Tool_012_001)
{
    sprintf(cmd, "V$DRT_CONN_SUBS_STAT");
    char *argv[9];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(0, argv, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(0, argv, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    }
}

// argc 传负
TEST_F(gmsysviewV8_01, Tool_012_002)
{
    sprintf(cmd, "V$DRT_CONN_SUBS_STAT");
    char *argv[9];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(-1, argv, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(-1, argv, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    }
}

// argv 传NULL
TEST_F(gmsysviewV8_01, Tool_012_003)
{
    sprintf(cmd, "V$DRT_CONN_SUBS_STAT");
    char *argv[9];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, NULL, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, NULL, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    }
}

// 传入空串等非法字符
TEST_F(gmsysviewV8_01, Tool_012_004)
{
    sprintf(cmd, "V$DRT_CONN_SUBS_STAT");
    char a[] = "";
    char b[] = "";
    char c[] = "";
    char d[] = "";
    char *argv[] = {a, b, c, d};
    ret = GmcSysview(4, argv, NULL);
    EXPECT_EQ(GMERR_INVALID_OPTION, ret);
}

// 传大数组
TEST_F(gmsysviewV8_01, Tool_012_005)
{
    sprintf(cmd, "V$DRT_CONN_SUBS_STAT");
    char c[] = "a";
    if (g_runMode == 0) {
        char *argv[1024] = {gmsysview, q, cmd, s, eulerServer};
        for (int i = 5; i < 1024; i++) {
            argv[i] = c;
        }
        ret = GmcSysview(1024, argv, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    } else {
        char *argv[1024] = {gmsysview, q, cmd, s, dopraServer};
        for (int i = 5; i < 1024; i++) {
            argv[i] = c;
        }
        ret = GmcSysview(1024, argv, NULL);
        EXPECT_EQ(GMERR_INVALID_OPTION, ret);
    }
}

// 函数传NULL,和gmsysview工具一样打印
TEST_F(gmsysviewV8_01, Tool_012_006)
{
    // freopen("test.txt", "w", stdout);cida上重定向打印会出问题,无法校验打印
    sprintf(cmd, "V$CATA_KV_TABLE_INFO");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// gmsysview -h,这个走sysview框架不会调用print函数,会直接打印\ DTS2022042005496，现在也可以打印
TEST_F(gmsysviewV8_01, Tool_012_007)
{
    char h[] = "-h";
    char *argv[] = {gmsysview, h};
    ret = GmcSysview(2, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    printf("%s\n", fileName);
    // gmsysview结果文件对比
    system("gmsysview -h > test.txt"); // gmsysview -h 内容存入文件
    char *file1 = NULL;
    char *file2 = NULL;
    ret = readJanssonFile("./fileCompile/gmsysview_h", &file1);  // AC联调定制osc去查询表视图和记录等
    EXPECT_NE(0, ret);
    ret = readJanssonFile(fileName, &file2);
    EXPECT_NE(0, ret);
    ret = strcmp(file1, file2);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "gmsysview -h 有更新");
        system("diff ./fileCompile/gmsysview_h test.txt");  // 预期文件和最新的gmsysview -h文件比较，输出更新的地方
        system("gmsysview -h > ./fileCompile/gmsysview_h");  // 再次更新预期文件
        free(file1);
        file1 = NULL;
        ret = readJanssonFile("./fileCompile/gmsysview_h", &file1);  // 再次比较
        EXPECT_NE(0, ret);
        ret = strcmp(file1, file2);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(file1);
    free(file2);
    system("rm -rf test.txt");
}

// DRT_CONN_STAT
TEST_F(gmsysviewV8_01, Tool_012_008)
{
    sprintf(cmd, "V$DRT_CONN_STAT");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    }
    sprintf(command, "cat %s", fileName);
    printf("%s\n", fileName);
    ret = executeCommand(command, "CONN_ID", "TIME_STAMP", "NODE_NAME");
    EXPECT_EQ(GMERR_OK, ret);
}

// STORAGE_VERTEX_LABEL_STAT
TEST_F(gmsysviewV8_01, Tool_012_009)
{
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelJson = R"(
        [{
    "type":"record",
    "name":"T0",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"int32", "nullable":true}
    ],
    "keys":[
       {
            "node":"T0",
            "name":"T0_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
]
    )";
    GmcDropVertexLabel(syncStmt, "T0");
    ret = GmcCreateVertexLabel(syncStmt, labelJson, "{\"max_record_count\": 100}");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(syncStmt, "T0", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(cmd, "V$STORAGE_HEAP_STAT");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    }
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "LABEL_NAME", "HEAP_TYPE");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(syncStmt, "T0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// STORAGE_KV_STAT
TEST_F(gmsysviewV8_01, Tool_012_010)
{
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    sprintf(cmd, "V$STORAGE_KV_STAT");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    }
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "T_GMDB", "HEAP_ROW_HEAD_SIZE", "FSM_PAGE_NUM");
    EXPECT_EQ(GMERR_OK, ret);
}

// 满连接1024，内部建连失败,
TEST_F(gmsysviewV8_01, Tool_012_011)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0},
        errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    GmcConnT *conn[MAX_CONN_SIZE];
    for (int i = 0; i < MAX_CONN_SIZE; i++) {
        testGmcConnect(&conn[i]);
    }
    sprintf(cmd, "V\\$DRT_CONN_SUBS_STAT");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, NULL);
        EXPECT_EQ(GMERR_TOO_MANY_CONNECTIONS, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, NULL);
        // IOT环境，无法占用逃生通道
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    for (int i = 0; i < MAX_CONN_SIZE; i++) {
        ret = testGmcDisconnect(conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/* 建立1022个连接后，内部占用逃生通道建连,手动测试用，不上架。
   先起服务，执行测用例，再切换用户，执行gmsysviewV8_02
   root用户起服务，其他用户连不上：DTS202107270NVD0OP0G00
   其他用户起服务，root无法占用逃生通道：DTS202107280KEZ1VP1400
   注意新用户需要的权限：
   useradd xxx 
  chmod 777 -R /root/GMDBV5
  chmod 777 -R /root/GMDBV5/test/sdv
  chmod 777 -R /run/verona/     起服务需要的权限
  chmod 777 -R /usr/local/file/ 客户端需要访问配置文件的位置，客户端与配置文件尚未解耦
*/
TEST_F(gmsysviewV8_01, Tool_012_012)
{
    GmcConnT *conn[MAX_CONN_SIZE - 2];
    for (int i = 0; i < MAX_CONN_SIZE - 2; i++) {
        ret = testGmcConnect(&conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 1021) {
            printf("1022 connections connnected\n");
        }
    }
    sleep(10);
    for (int i = 0; i < MAX_CONN_SIZE - 2; i++) {
        ret = testGmcDisconnect(conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 循环调用50次
TEST_F(gmsysviewV8_01, Tool_012_013)
{
    for (int i = 0; i < 50; i++) {
        sprintf(cmd, "V$DRT_CONN_STAT");
        char *argv[5];
        if (g_runMode == 0) {
            char *argv[] = {gmsysview, q, cmd, s, eulerServer};
            ret = GmcSysview(5, argv, printToFile);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            char *argv[] = {gmsysview, q, cmd, s, dopraServer};
            ret = GmcSysview(5, argv, printToFile);
            EXPECT_EQ(GMERR_OK, ret);
        }
        sprintf(command, "cat %s", fileName);
        ret = executeCommand(command, "CONN_ID", "TIME_STAMP", "NODE_NAME");
        EXPECT_EQ(GMERR_OK, ret);
        sprintf(command, "rm -rf %s", fileName);
        system(command);
    }
}

void *kvInfo(void *arg)
{
    sprintf(cmd, "V$CATA_KV_TABLE_INFO");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char fileName2[100] = {0};
    sprintf(fileName2, "gmsysview-temp-file%d", getpid());
    sprintf(command, "cat %s", fileName2);
    ret = executeCommand(command, "KV_TABLE", "T_GMDB", "HASH_INDEX");
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "rm -rf %s", fileName2);
    system(command);
    return NULL;
}
// 10个进程并发调用 -->由于HPE环境不用fork，所以将进程改为线程
TEST_F(gmsysviewV8_01, Tool_012_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int cnt = 10;
    pthread_t tid[cnt];
    for (int i = 0; i < cnt; i++) {
        ret = pthread_create(&tid[i], NULL, kvInfo, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(2);
    }
    for (int i = 0; i < cnt; i++) {
       pthread_join(tid[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 和dml操作并发查询
void *dmlThread(void *arg)
{
    for (int i = 0; i < 10; i++) {
        GmcConnT *syncConn = NULL;
        GmcStmtT *syncStmt = NULL;
        ret = testGmcConnect(&syncConn, &syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        const char *labelJson = R"(
        [{
    "type":"record",
    "name":"T0",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"int32", "nullable":true}
    ],
    "keys":[
       {
            "node":"T0",
            "name":"T0_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
]
    )";
        GmcDropVertexLabel(syncStmt, "T0");
        ret = GmcCreateVertexLabel(syncStmt, labelJson, "{\"max_record_count\": 100000}");
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < 1000; j++) {
            ret = testGmcPrepareStmtByLabelName(syncStmt, "T0", GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &j, 4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &j, 4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(syncStmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        int32_t getValue = 0;
        for (int j = 0; j < 1000; j++) {
            ret = testGmcPrepareStmtByLabelName(syncStmt, "T0", GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &j, 4));
            ret = GmcSetIndexKeyName(syncStmt, "T0_PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(syncStmt);
            EXPECT_EQ(GMERR_OK, ret);
            bool isEOF = false;
            ret = GmcFetch(syncStmt, &isEOF);
            EXPECT_EQ(GMERR_OK, ret);
            bool isNULL = true;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(syncStmt, "F1", &getValue, sizeof(getValue), &isNULL));
            EXPECT_EQ(j, getValue);
            EXPECT_FALSE(isNULL);
        }
        ret = GmcDropVertexLabel(syncStmt, "T0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(syncConn, syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}
void *viewThread(void *arg)
{
    for (int i = 0; i < 10; i++) {
        sprintf(cmd, "V$DRT_CONN_STAT");
        char *argv[5];
        if (g_runMode == 0) {
            char *argv[] = {gmsysview, q, cmd, s, eulerServer};
            ret = GmcSysview(5, argv, printToFile);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            char *argv[] = {gmsysview, q, cmd, s, dopraServer};
            ret = GmcSysview(5, argv, printToFile);
            EXPECT_EQ(GMERR_OK, ret);
        }
        sprintf(command, "cat %s", fileName);
        ret = executeCommand(command, "CONN_ID", "TIME_STAMP", "NODE_NAME");
        EXPECT_EQ(GMERR_OK, ret);
        sprintf(command, "rm -rf %s", fileName);
        system(command);
    }
    return NULL;
}

TEST_F(gmsysviewV8_01, Tool_012_015)
{
    pthread_t threads[2];
    ret = pthread_create(&threads[0], NULL, dmlThread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&threads[1], NULL, viewThread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(threads[0], NULL);
    pthread_join(threads[1], NULL);
}

// record命令
TEST_F(gmsysviewV8_01, Tool_012_016)
{
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelJson = R"(
        [{
    "type":"record",
    "name":"T0",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"int32", "nullable":true}
    ],
    "keys":[
       {
            "node":"T0",
            "name":"T0_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
]
    )";
    GmcDropVertexLabel(syncStmt, "T0");
    ret = GmcCreateVertexLabel(syncStmt, labelJson, "{\"max_record_count\": 1000000}");
    EXPECT_EQ(GMERR_OK, ret);
    int recordNum = 100000;
    if (g_envType == 2) {  // iot 减小数据量
        recordNum = 2000;
    }
    for (int i = 0; i < recordNum; i++) {
        ret = testGmcPrepareStmtByLabelName(syncStmt, "T0", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char record[] = "record";
    char label[] = "T0";
    char *argv[7];
    char ns[] = "-ns";
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, record, label, s, eulerServer, ns, g_testNameSpace};
        ret = GmcSysview(7, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, record, label, s, dopraServer, ns, g_testNameSpace};
        ret = GmcSysview(7, argv, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (g_envType == 2) {
        sprintf(command, "grep 1999 %s", fileName);
        ret = executeCommand(command, "1999");
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        sprintf(command, "grep 99999 %s", fileName);
        ret = executeCommand(command, "99999");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(syncStmt, "T0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************2022/5/19 场景加固*******************/
/************已覆盖不需要加固：查看帮助信息**************/
/*********已覆盖不需要加固：查询普通系统视图信息*********/
/*********已覆盖不需要加固：查询普通表的记录信息*********/
// 查看工具版本信息
TEST_F(gmsysviewV8_01, Tool_012_018)
{
    char v[] = "-v";
    char *argv[] = {gmsysview, v};
    ret = GmcSysview(2, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    printf("%s\n", fileName);
    ret = executeCommand(command, "sysview version v");
    EXPECT_EQ(GMERR_OK, ret);
}
// 查询特殊资源视图信息
TEST_F(gmsysviewV8_01, Tool_012_019)
{
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *resPoolName = "ResourcePool";
    const char *resourcePool = R"(
        {
            "name" : "ResourcePool",
            "pool_id" : 0,
            "start_id" : 200,
            "capacity" : 2000,
            "order" : 0,
            "alloc_type" : 0
        }
    )";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(stmt, resourcePool);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "ResourceLabel";
    const char *resVertexLabel = R"(
        [{
        "type":"record",
        "name":"ResourceLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceLabel",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }]
    )";
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, resVertexLabel, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(stmt, resPoolName, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t tmpResIdx = 0;
    const uint32_t gResPoolId = 0;
    const uint32_t gResPoolStartId = 200;
    const uint32_t gResPoolCapacity = 200;
    ret = GmcSetPoolIdResource(gResPoolId, &tmpResIdx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(gResPoolCapacity, &tmpResIdx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(gResPoolStartId + 1, &tmpResIdx);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t F0Value = 10;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    char view[] = "V$STORAGE_RESOURCE_RESID_STAT";
    char f[] = "-f";
    char resFilter[] = "[r]205";
    char *argv[] = {gmsysview, q, view, f, resFilter};
    ret = GmcSysview(5, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "START_ID: 200", "RANGE: 2000", "START_INDEX: 205");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(stmt, resPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 查询KV表的记录信息
TEST_F(gmsysviewV8_01, Tool_012_020)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *kvName = "kv1";
    GmcKvDropTable(stmt, kvName);
    ret = GmcKvCreateTable(stmt, kvName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *key[3] = {"key1", "key2", "key3"};
    const char *value[3] = {"value1", "value2", "value3"};
    for (int i = 0; i < 3; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvSet(stmt, key[i], strlen(key[i]) + 1, value[i], strlen(value[i]) + 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char record_kv[] = "record_table_kv";
    char table[] = "kv1";
    char num[] = "3";
    char ns[] = "-ns";
    char *argv[] = {gmsysview, record_kv, table, ns, g_testNameSpace, num};
    ret = GmcSysview(6, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "|key       |0x6b65793100", "|key       |0x6b65793200", "|key       |0x6b65793300",
        "|value     |0x76616c75653300");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 查询普通表的记录数
TEST_F(gmsysviewV8_01, Tool_012_021)
{
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelJson = R"(
        [{
    "type":"record",
    "name":"T0",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"int32", "nullable":true}
    ],
    "keys":[
       {
            "node":"T0",
            "name":"T0_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
]
    )";
    GmcDropVertexLabel(syncStmt, "T0");
    ret = GmcCreateVertexLabel(syncStmt, labelJson, "{\"max_record_count\": 1000000}");
    EXPECT_EQ(GMERR_OK, ret);
    int recordNum = 500;
    for (int i = 0; i < recordNum; i++) {
        ret = testGmcPrepareStmtByLabelName(syncStmt, "T0", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_INT32, &i, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char count[] = "count";
    char label[] = "T0";
    char ns[] = "-ns";
    char *argv[] = {gmsysview, count, label, ns, g_testNameSpace};
    ret = GmcSysview(5, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    system(command);
    ret = executeCommand(command, "500");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(syncStmt, "T0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 查询KV表的记录数
TEST_F(gmsysviewV8_01, Tool_012_022)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *kvName = "kv1";
    GmcKvDropTable(stmt, kvName);
    ret = GmcKvCreateTable(stmt, kvName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *key[3] = {"key1", "key2", "key3"};
    const char *value[3] = {"value1", "value2", "value3"};
    for (int i = 0; i < 3; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvSet(stmt, key[i], strlen(key[i]) + 1, value[i], strlen(value[i]) + 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char count_kv[] = "count_table_kv";
    char table[] = "kv1";
    char ns[] = "-ns";
    char *argv[] = {gmsysview, count_kv, table, ns, g_testNameSpace};
    ret = GmcSysview(5, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "kv1", "3");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 获取告警信息
TEST_F(gmsysviewV8_01, Tool_012_023)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    uint32_t currentConnNum = 0;
    ret = testGetConnNum(&currentConnNum);
    EXPECT_EQ(0, ret);
    double connNumMax = (MAX_CONN_SIZE - currentConnNum) * 0.9;
    int connNum = (int)connNumMax + 1;
    GmcConnT *conn[connNum];
    memset(conn, 0, connNum * sizeof(GmcConnT *));
    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            printf("%d\n", i);
        }
    }
    for (int i = 0; i < connNum; i++) {
        ret = testGmcDisconnect(conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char alarm[] = "alarm";
    char *argv[] = {gmsysview, alarm};
    ret = GmcSysview(2, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "alarm_source: SERVER", "alarm_middle_status: ACTIVE_CLEARED",
        "alarm_current_status: NORMAL", "alarm_active_threshold = 0.90");
    EXPECT_EQ(GMERR_OK, ret);
}
// 根据表结构获取预估内存占用信息
TEST_F(gmsysviewV8_01, Tool_012_024)
{
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    char estimate[] = "estimate";
    char path[] = "./fileCompile/estimate.gmjson";
    char *argv[] = {gmsysview, estimate, path};
    ret = GmcSysview(3, argv, printToFile);
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "Schema file name                    ./fileCompile/estimate.gmjson", "Object num",
        "Object size", "Total size");
    EXPECT_EQ(GMERR_OK, ret);
}
// DRT_CONN_STAT新增字段校验
TEST_F(gmsysviewV8_01, Tool_012_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    sprintf(cmd, "V$DRT_CONN_STAT");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, printToFile);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, printToFile);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sprintf(command, "cat %s", fileName);
    system(command);
    ret = executeCommand(command, "CLT_THREAD_NAME", "CLT_THREAD_ID");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
