[OPTION]
  -h               [all | yang]                                                                 print online help manual
  -v                                                                                            print the gmsysview version
  -q               <sysview_name>                                                               query sysview
  -f               <filter | format>                                                            filter in sysview or format in record/record_kv_table
  -rc                                                                                           use reserved connection
  -o               <property_name> <asc | desc>                                                 specify sort property and sort mode for sysview. should use -l option meanwhile
  -l               <record_num>                                                                 specify the max num of vertex to print for sysview
  -s               <server_locator>                                                             serverLocator for connection
  osc              <shm:unix_emserver>                                                          AC Customizing Rules
  record           <vertex_label_name> [record_num]                                             query and print vertex label record
  record_table_kv  [kv_table_name] [record_num]                                                 query and print kv table record
  count            [vertex_label_name]                                                          query and print vertex label count
  count_table_kv   [kv_table_name]                                                              query and print kv table count
  top              <num>                                                                        top n data of kv table count or vertex label count
  -ns              <namespace_name>                                                             namespace for query record
  -c               <property_name> <-eq | -ge | -gt | -le | -lt> <property_value> | <key_name>  condition to filter record
  -view_fmt        <json | flat_full | flat_truncate>                                           view format, flat print format is not supported for records
  alarm                                                                                         display info about all alarms in the active state
  estimate         <schema_file_path>                                                           estimate size by schema
  subtree          <help | -h> [-ns]                                                            to get information of subtree filter
  resource         <help | -h>                                                                  to get information of resource pool
  show             <table_name> [config_name]                                                   to show the config value of a given config table name
  diagnostic_view  [-i <interval_ms>]                                                           query and print all key view
  systable         <vertex> <ptree/precord> -name <name>                                        show record of systable in tree mode or record mode
