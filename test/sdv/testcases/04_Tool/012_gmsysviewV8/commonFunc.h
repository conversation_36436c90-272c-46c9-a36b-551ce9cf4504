extern "C" {
#if !defined(RUN_DATACOM_HPE)
#endif
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <stdarg.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include <sys/syscall.h>

int ret = 0;
// GmcSysview入参数组元素，e.g : gmsysview -q V\$DRT_CONN_SUBS_STAT -e RTOS -s
// usocket:/run/verona/unix_emserver
char gmsysview[] = "gmsysview";
char q[] = "-q";
char cmd[50] = {0};
char user[] = "user123";
char passwd[] = "password.123";
// char e[] = "-e"; 2022/1/25不必指定环境
// char RTOS[] = "RTOS";
// char DAP[] = "DAP";
char s[] = "-s";
char eulerServer[] = "usocket:/run/verona/unix_emserver";
char dopraServer[] = "channel:";
char fileName[50] = {0};
char command[100] = {0};
/*******************************************************************************
  函 数 名		:  NULL
  功能描述		:  将格式化字符串写入文件,作为接口第三个参数，按行处理视图输出
  输入参数		:  同printf
  输出参数		:  None
  返 回 值		:  同printf
*******************************************************************************/
int32_t printToFile(const char *format, ...)
{
    int ret = 0;
    FILE *fd = NULL;
    char fileName[100] = {0};
    sprintf(fileName, "gmsysview-temp-file%d", getpid());
    fd = fopen(fileName, "a");
    if (fd == NULL) {
        printf("fopen failed!\n");
        return -1;
    };
    va_list ap;
    va_start(ap, format);
    ret = vfprintf(fd, format, ap);
    if (ret < 0) {
        printf("print fail\n");
    }
    va_end(ap);
    fclose(fd);
    return ret;
}
