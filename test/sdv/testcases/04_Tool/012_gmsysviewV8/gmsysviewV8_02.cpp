/*****************************************************************************
 Description  : 逃生通道测试用 	DTS202107280KEZ1VP1400
 Notes        :
 History      :
 Author       :
 Modification :
 Date         :
*****************************************************************************/
#include "commonFunc.h"

class gmsysviewV8_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // system("sh $TEST_HOME/tools/start.sh");
        // 获取系统时间
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testEnvClean();
        ASSERT_EQ(GMERR_OK, ret);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void gmsysviewV8_01::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}
void gmsysviewV8_01::TearDown()
{
    AW_CHECK_LOG_END();
}

TEST_F(gmsysviewV8_01, Tool_012_001)
{
    /*
        GmcConnT *conn = NULL;
        ret = testGmcConnect(&conn);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret == 0) {
            ret = testGmcDisconnect(conn);
            EXPECT_EQ(GMERR_OK, ret);
        }*/
    sprintf(cmd, "V$DRT_CONN_STAT");
    char *argv[5];
    if (g_runMode == 0) {
        char *argv[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
