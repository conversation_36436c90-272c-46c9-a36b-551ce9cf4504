cmake_minimum_required(VERSION 3.14.1)

project(testcase)

set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../../../../src/tools/utils/include/)

file(GLOB fileList RELATIVE "${CMAKE_CURRENT_SOURCE_DIR}" "*.cpp")
foreach(sinFile IN LISTS fileList)
    get_filename_component(mainName ${sinFile} NAME_WE)
    add_executable(${mainName} ${sinFile})
    
endforeach()

