/*****************************************************************************
 Description  : 权限控制
 Notes        :
 History      :
 Author       :
 Modification :
 Date         :
*****************************************************************************/
#include "commonFunc.h"

class gmsysviewV8_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void gmsysviewV8_01::SetUp()
{
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void gmsysviewV8_01::TearDown()
{
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

//鉴权
TEST_F(gmsysviewV8_01, Tool_012_017)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    GmcConnT *conn = NULL;
    sprintf(cmd, "V$DRT_CONN_STAT");
    //打开权限控制后查询失败
    char *argv1[5];
    if (g_runMode == 0) {
        char *argv1[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv1, NULL);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    } else {
        char *argv1[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv1, NULL);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }

    //导入白名单和权限文件
    char gmrule[500] = {0};
    char schema_path[128] = "./allow_list/user.gmuser";
    snprintf(gmrule, 500, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, schema_path, g_connServer);
    // printf("%s\n", gmrule);
    ret = system(gmrule);
    EXPECT_EQ(GMERR_OK, ret);
    char policy_path[128] = "./allow_list/policy.gmpolicy";
    snprintf(gmrule, 500, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, policy_path, g_connServer);
    // printf("%s\n", gmrule);
    ret = system(gmrule);
    EXPECT_EQ(GMERR_OK, ret);
    //查询成功
    sprintf(fileName, "gmsysview-temp-file%d", getpid());
    sprintf(command, "> %s", fileName);
    system(command);
    if (g_runMode == 0) {
        char *argv2[] = {gmsysview, q, cmd, s, eulerServer};
        ret = GmcSysview(5, argv2, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        char *argv2[] = {gmsysview, q, cmd, s, dopraServer};
        ret = GmcSysview(5, argv2, printToFile);
        EXPECT_EQ(GMERR_OK, ret);
    }
    sprintf(command, "cat %s", fileName);
    ret = executeCommand(command, "CONN_ID", "CONN_STATUS");
    EXPECT_EQ(GMERR_OK, ret);
    sprintf(command, "rm -rf %s", fileName);
    system(command);
}
