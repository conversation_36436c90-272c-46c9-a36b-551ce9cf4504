set(compile_list "")
if(FEATURE_FASTPATH)
list(APPEND compile_list 001_gmimport)
list(APPEND compile_list 002_gmsysview)
list(APPEND compile_list 004_Cacalog)
list(APPEND compile_list 006_gmimpexp)
list(APPEND compile_list 007_OldDataToNewSchema)
list(APPEND compile_list 009_CatalogEnhance)
list(APPEND compile_list 008_memory)
list(APPEND compile_list 010_Tools_Kv)
list(APPEND compile_list 011_GmimpexpBitmap)
list(APPEND compile_list 012_gmsysviewV8)
list(APPEND compile_list 013_OnlineModify_conf)
list(APPEND compile_list 014_flatAndJson)
list(APPEND compile_list 015_Tools_Optimization)
list(APPEND compile_list 016_BatchExport)
list(APPEND compile_list 017_gmimport_support_nest_folder)
list(APPEND compile_list 019_Gmstat_Compatible)
list(APPEND compile_list 020_Alarm_SysView)
list(APPEND compile_list 021_EstimatedMemorySize)
list(APPEND compile_list 023_gmcmd)
list(APPEND compile_list 024_gmimport_update)
list(APPEND compile_list 025_gmlogOptimize)
list(APPEND compile_list 026_SchemaLoader)
list(APPEND compile_list 027_schemaDataTest)
list(APPEND compile_list 028_OnlineConfigTest)
list(APPEND compile_list 031_importFromSharedMemory)
list(APPEND compile_list 032_gmddlDrop)
list(APPEND compile_list 033_memtraceTest)
endif()

verify_and_add_directory(${compile_list})
