/*****************************************************************************
 Description  : gmimport工具导入json创建kv表
 Notes        : Tool_010_Tools_Kv_Gmimport_test_001	1. gmimport  -c  ktable（参数错误）  -f XXX
                Tool_010_Tools_Kv_Gmimport_test_002	2. gmimport  -c  XXX –f （文件路径错误）
                Tool_010_Tools_Kv_Gmimport_test_003	3. gmimport  -c  XXX –f（文件名称错误）
                Tool_010_Tools_Kv_Gmimport_test_004	4. gmimport  -c XXX  -f  XXX -t kvTable1（错误的kv表名）
                Tool_010_Tools_Kv_Gmimport_test_005	5. 导入无效的kv表配置文件（writers长度超过32字节）
                Tool_010_Tools_Kv_Gmimport_test_006	6．导入已存在的kv配置文件
                Tool_010_Tools_Kv_Gmimport_test_007	7．循环100次导入已存在的kv数据，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_008	8.参数正确，通过import 创建KV表，并导入kv表配置文件，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_009	9.导入kv数据，删除key-value对，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_010	10.导入kv数据，全表订阅，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_011	11.开启事务，导入kv数据，查询数据，commit
                Tool_010_Tools_Kv_Gmimport_test_012	12.批量导入kv数据，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_013	13.导入kv表配置文件，再导出kv表配置文件，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_014	14.循环 100 导入kv数据，再导出kv数据，查询kv数据。
                Tool_010_Tools_Kv_Gmimport_test_015 15. 导入无效的kv表配置文件（9个writers）
                Tool_010_Tools_Kv_Gmimport_test_016 16. 导入无效的kv表配置文件（writers长度超过256字节）
                Tool_010_Tools_Kv_Gmimport_test_017 17．建完表（表的配置参数为NULL）后，再次导入配置文件
                Tool_010_Tools_Kv_Gmimport_test_018 18.导入kv数据(key 长度为 512)，查询kv数据。

 History      :
 Author       : 廖想
 Modification :
 Date         : 2021/05/29
*****************************************************************************/
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>

#include "gmimport_kvdata.h"

#include "t_datacom_lite.h"

#define KVNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024
#define MAX_NAME_LENGTH 128
char g_configJson[128] = "{\"max_record_count\":10000,\"writers\":\"abc\",\"max_record_count_check\":false}";
char g_tableName[KVNAME_MAX_LENGTH] = "KV0";

int32_t g_data_num = 1;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
int affectRows;
unsigned int len;
#define MAX_NAME_LENGTH 128
/**********订阅相关**********/
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;
char *g_sub_info = NULL;
int g_subIndex = 0;

const char *g_subConnName = "SubConnName";
int ret = 0;

class Gmimport_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");  // 重启服务
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        // 因不能提交二进制文件到仓库，故需要将数据导出
        char g_tableName2[KVNAME_MAX_LENGTH] = "KV-0";
        test_gmimport_data(g_conn, g_stmt, g_tableName2, g_configJson);
        sleep(1);
        char g_tableName4[KVNAME_MAX_LENGTH] = "KV-00";
        test_gmimport_key_512(g_conn, g_stmt, g_tableName4, g_configJson);
        sleep(1);
        char g_tableName3[KVNAME_MAX_LENGTH] = "KV-01";
        test_batch_gmimport_data(g_conn, g_stmt, g_tableName3, g_configJson);
        sleep(1);
    }
    static void TearDownTestCase()
    {
        // close epoll
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();

    SnUserDataT *user_data;
};

void Gmimport_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步客户端链接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    AW_CHECK_LOG_BEGIN();
}

void Gmimport_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    // close connection
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
};

// 1. gmimport  -c  ktable（参数错误）  -f XXX
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_001)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];
    snprintf(cmd, 512,
        "%s/gmimport -c Ktable -f schema_file/gmimport/gmimport_Kvtabel_01.gmjson -s %s", g_toolPath,
        g_connServer);

    ret = executeCommand(cmd, "unexpected gmimport parameter. ret = 1004004");
    EXPECT_EQ(GMERR_OK, ret);
}

// 2. gmimport  -c  XXX –f （文件路径错误）
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_002)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c kvtable -f ./A/gmimport_Kvtabel_01.gmjson -s %s", g_toolPath,
        g_connServer);

    ret = executeCommand(cmd, "get realpath from ./A/gmimport_Kvtabel_01.gmjson unsucc. ret = 1015000");
    EXPECT_EQ(GMERR_OK, ret);
}

// 3. gmimport  -c  XXX –f（文件名称错误）
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_003)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FILE_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c kvtable -f schema_file/gmimport/ABC.gmjson -s %s", g_toolPath,
        g_connServer);

    ret = executeCommand(cmd, "no such file or directory: ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/ABC.gmjson. ret = 1013000");
    EXPECT_EQ(GMERR_OK, ret);
}

// 4. gmimport  -c XXX  -f  XXX -t kvTable1（错误的kv表名）
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_004)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];
    const char *labelName = "error";

    // 创建kvtable
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // gmimport 导入数据时，表名错误
    snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-0.gmkv -t labelName  -ns %s -s %s", g_toolPath,
        g_testNameSpace, g_connServer);

    ret = executeCommand(cmd, "Import single file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-0.gmkv\" unsucc. ret = 1009010");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 5. 导入无效的kv表配置文件（max_record_count_check 参数值错误）
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_005)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];
    // 导入无效的kv表配置文件，建表失败
    snprintf(cmd, 512,
        "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_error.gmconfig    -s %s",
        g_toolPath, g_connServer);

    ret = executeCommand(cmd, "Import single file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/gmimport_Kvtabel_error.gmconfig\" "
        "unsucc. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
}

// 6．导入已存在的kv配置文件
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_006)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];

    // 创建kvtabel
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(cmd, 512,
        "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_01.gmconfig -t KV0  -s %s -ns %s", g_toolPath,
        g_connServer, g_testNameSpace);

    ret = executeCommand(cmd, "import batch exec unsucc. ret = 1009013");
    EXPECT_EQ(GMERR_OK, ret);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 7．循环100次导入已存在的kv数据，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_007)
{
    void *kvtable = NULL;
    char cmd[512];

    // create kvtable
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    int32_t value = 88;
    char key[32] = "zhangsan";
    // set k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 循环100次导入已存在的kv数据
    for (int i = 0; i < 100; i++) {
        snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-0.gmkv  -t KV0  -ns %s -s %s", g_toolPath,
            g_testNameSpace, g_connServer);

        ret =
            executeCommand(cmd, "Insert data succeed. successNum: 1", "Command type: import_kvdata, Import file from ",
                "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-0.gmkv\" successfully");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查询插入的k-v值
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(88, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 8.参数正确，通过import 创建KV表，并导入kv表配置文件，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_008)
{
    char cmd[512];
    void *kvtable = NULL;
    char g_tableName[KVNAME_MAX_LENGTH] = "KV1";
    // gmimport 工具导入创建 kvtable
    snprintf(cmd, 512,
        "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_01.gmconfig"
        " -t KV1 -ns %s -s %s",
        g_toolPath, g_testNameSpace, g_connServer);

    ret = executeCommand(cmd, "Command type: import_kvtable, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/gmimport_Kvtabel_01.gmconfig\" "
        "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    int32_t value = 66;
    char key[32] = "lishi";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入的k-v值
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(66, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 9.导入kv数据，删除key-value对，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_009)
{
    void *kvtable = NULL;
    char cmd[512];

    // create kvtable
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入数据
    snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-0.gmkv -t KV0 -ns %s -s %s", g_toolPath,
        g_testNameSpace, g_connServer);

    ret = executeCommand(cmd, "Insert data succeed. successNum: 1", "Command type: import_kvdata, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-0.gmkv\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 删除数据
    char key[32] = "zhangsan";
    ret = GmcKvRemove(g_stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);

    // 检查删除的信息是否存在
    bool isExist = 0;

    ret = GmcKvIsExist(g_stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isExist);

    // 查询数据信息
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_NO_DATA, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(0, *(uint32_t *)output);
    EXPECT_EQ(128, outputLen);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback_no_rewrite(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, i, index;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char *outKey = NULL, *outValue = NULL;
    uint32_t outKeyLen = 512, outValueLen = 0;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    void *tableLabel = 0;

    char key[128] = "zhangsan";

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->msgType) {
                case 1:  //推送old object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            index = ((int *)user_data->new_value)[g_subIndex];
                            sprintf(key, "zhangsan_%d", index);
                            printf("[OLD OBJECT] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            //读old
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                index = ((int *)user_data->old_value)[g_subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                printf("[OLD OBJECT] GMC_SUB_EVENT_KV_SET old_value is %d\r\n", index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            //读old
                            index = ((int *)user_data->old_value)[g_subIndex];
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            printf("[OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 2:  //推送new object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            // index = *(int *)((SnUserDataT *)user_data)->new_value;
                            index = ((int *)user_data->new_value)[g_subIndex];
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            printf("[NEW OBJECT] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 3:  //推送new object + old object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            // index = *(int *)((SnUserDataT *)user_data)->new_value;
                            index = ((int *)user_data->new_value)[g_subIndex];
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));

                            //读old
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET old insert\r\n");
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                index = ((int *)user_data->old_value)[g_subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET old update old_value is %d\r\n", index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 4:  //推送key
                {
                    //创建同步连接
                    ret = testGmcConnect(&conn_sync, &stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcKvPrepareStmtByLabelName(stmt_sync, labelName);
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            // index = *(int *)((SnUserDataT *)user_data)->new_value;
                            index = ((int *)user_data->new_value)[g_subIndex];
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                                printf("[KEY] GMC_SUB_EVENT_KV_SET insert new_value is %d\r\n", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                                printf("[KEY] GMC_SUB_EVENT_KV_SET update new_value is %d\r\n", index);
                            }
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt_sync, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(index, *(uint32_t *)outValue);
                            EXPECT_EQ(4, outValueLen);

                            //读old
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                                ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(outKey[i], key[i]);
                                }
                                ret = GmcKvGet(stmt_sync, key, strlen(key), outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(index, *(uint32_t *)outValue);
                                EXPECT_EQ(4, outValueLen);
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            index = ((int *)user_data->old_value)[g_subIndex];
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            //读old
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt_sync, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
                            ret = testGmcGetLastError(NULL);
                            EXPECT_EQ(GMERR_OK, ret);

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    // ret = GmcCloseKvTable(stmt_sync);
                    // EXPECT_EQ(GMERR_OK, ret);
                    ret = testGmcDisconnect(conn_sync, stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case 5:  //推送key 和 old object
                {
                    //创建同步连接
                    ret = testGmcConnect(&conn_sync, &stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcKvPrepareStmtByLabelName(stmt_sync, labelName);
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            index = ((int *)user_data->old_value)[g_subIndex];
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            //读old
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt_sync, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
                            ret = testGmcGetLastError(NULL);
                            EXPECT_EQ(GMERR_OK, ret);

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    // ret = GmcCloseKvTable(stmt_sync);
                    // EXPECT_EQ(GMERR_OK, ret);
                    ret = testGmcDisconnect(conn_sync, stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case 7:  //推送new object + old object + key
                {
                    //创建同步连接
                    ret = testGmcConnect(&conn_sync, &stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcKvPrepareStmtByLabelName(stmt_sync, labelName);
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new key
                            index = ((int *)user_data->new_value)[g_subIndex];
                            // index = *(int *)((SnUserDataT *)user_data)->new_value;
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            printf("[NEW/OLD OBJECT/KEY] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt_sync, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(index, *(uint32_t *)outValue);
                            EXPECT_EQ(4, outValueLen);

                            //读old key
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                index = ((int *)user_data->old_value)[g_subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(outKey[i], key[i]);
                                }
                                ret = GmcKvGet(stmt_sync, key, strlen(key), outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(index, *(uint32_t *)outValue);
                                EXPECT_EQ(4, outValueLen);
                            }

                            //读new kv
                            // index = *(int *)((SnUserDataT *)user_data)->new_value;
                            index = ((int *)user_data->new_value)[g_subIndex];
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            printf("[NEW/OLD OBJECT/KEY] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            //读old kv
                            if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                index = ((int *)user_data->old_value)[g_subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                printf("[NEW/OLD OBJECT/KEY] GMC_SUB_EVENT_KV_SET old_value is %d\r\n", index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    // ret = GmcCloseKvTable(stmt_sync);
                    // EXPECT_EQ(GMERR_OK, ret);
                    ret = testGmcDisconnect(conn_sync, stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid msgType %d\r\n", info->msgType);
                    break;
                }
            }
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
// 10.导入kv数据，全表订阅，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_010)
{

    void *kvtable = NULL;
    char *g_sub_info = NULL;
    uint32_t userDataIdx = 0;
    const char *g_subName = "subVertexLabel";
    char cmd[512];

    // 创建订阅链接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    // create kvtable
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/gmimport/KV_subInfo_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    // 开启订阅
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_no_rewrite, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    ((int *)(user_data->new_value))[userDataIdx] = 88;
    ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
    userDataIdx++;

    // 导入数据
    snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-0.gmkv -t KV0   -ns %s -s %s", g_toolPath,
        g_testNameSpace, g_connServer);

    ret = executeCommand(cmd, "Insert data succeed. successNum: 1", "Command type: import_kvdata, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-0.gmkv\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 校验插入的数据
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_KV_SET, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询数据信息
    char output[128] = {0};
    char key[32] = "zhangsan";
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(88, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // 关闭订阅
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅链接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
}

// 11.开启事务，导入kv数据，查询数据，commit
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_011)
{
    char cmd[512];
    void *kvtable = NULL;

    // create kvtable
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcKvCreateTable(g_stmt, g_tableName, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务类型
    GmcTxConfigT MSTrxConfig;
    // 定义 DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    MSTrxConfig.trxType = GMC_PESSIMISITIC_TRX ; // 悲观事务
    // 开启事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入kv数据
    snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-0.gmkv -t %s  -s %s -ns %s", g_toolPath,
        g_tableName, g_connServer, g_testNameSpace);
    ret = executeCommand(cmd, "Insert data succeed. successNum: 1", "Command type: import_kvdata, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-0.gmkv\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 查询kv数据
    char output[128] = {0};
    char key[32] = "zhangsan";
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(88, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 12.批量导入kv数据，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_012)
{
    void *kvtable = NULL;
    char cmd[512];

    // create kvtable
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入数据
    snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-01.gmkv -t KV0  -ns %s  -s %s", g_toolPath,
        g_testNameSpace, g_connServer);
    ret = executeCommand(cmd, "Insert data succeed. successNum: 100", "Command type: import_kvdata, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-01.gmkv\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 13.导入kv表配置文件，再导出kv表配置文件，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_013)
{
    char cmd[512];
    void *kvtable = NULL;
    char g_tableName2[KVNAME_MAX_LENGTH] = "gmimport_Kvtabel_01";
    // gmimport 工具导入创建 kvtable
    snprintf(cmd, 512, "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_01.gmconfig -ns %s -s %s",
        g_toolPath, g_testNameSpace, g_connServer);

    ret = executeCommand(cmd, "Command type: import_kvtable, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/gmimport_Kvtabel_01.gmconfig\" "
        "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName2);
    EXPECT_EQ(GMERR_OK, ret);

    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    int32_t value = 66;
    char key[32] = "lishi";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入的k-v值
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(66, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // gmexport 工具导出 kvtable
    snprintf(
        cmd, 512, "%s/gmexport -c kvtable -f schema_file  -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(cmd, "Command type: export_kvtable, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    system(cmd);
    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 14.循环 100 导入kv数据，再导出kv数据，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_014)
{
    void *kvtable = NULL;
    char cmd[512];
    char g_tableName3[KVNAME_MAX_LENGTH] = "KV2";

    // create kvtable
    GmcKvDropTable(g_stmt, g_tableName3);
    ret = GmcKvCreateTable(g_stmt, g_tableName3, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName3);
    EXPECT_EQ(GMERR_OK, ret);

    // 循环100次导入kv数据
    for (int i = 0; i < 100; i++) {
        snprintf(cmd, 512, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-0.gmkv -t KV2   -ns %s -s %s", g_toolPath,
            g_testNameSpace, g_connServer);
        ret =
            executeCommand(cmd, "Insert data succeed. successNum: 1", "Command type: import_kvdata, Import file from ",
                "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-0.gmkv\" successfully");
        EXPECT_EQ(GMERR_OK, ret);

        // gmexport 工具导出 kvdata
        snprintf(cmd, 512, "%s/gmexport -c kvdata -t KV2 -f schema_file -ns %s  -s %s", g_toolPath, g_testNameSpace,
            g_connServer);
        ret = executeCommand(cmd, "Command type: export_kv, export file successfully.");
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查询插入的k-v值
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    char key[32] = "zhangsan";
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(88, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName3);
    EXPECT_EQ(GMERR_OK, ret);
}

// 15. 导入无效的kv表配置文件（9个writers） // 2021.09.01 Ds不支持kv表,所以kv没有读者和写者。故该场景不存在
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_015)
{
    char cmd[512];
    // 导入无效的kv表配置文件，建表失败
    snprintf(cmd, 512, "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_error_2.gmconfig -ns %s -s %s",
        g_toolPath, g_testNameSpace, g_connServer);

    ret = executeCommand(cmd, "Command type: import_kvtable, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/gmimport_Kvtabel_error_2.gmconfig\" "
        "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, "gmimport_Kvtabel_error_2");
    EXPECT_EQ(GMERR_OK, ret);
}

// 16. 导入无效的kv表配置文件（writers长度超过256字节）
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_016)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512]; // 2023.4.17 writers 不再生效
    // 导入无效的kv表配置文件，建表失败
    snprintf(cmd, 512, "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_error_3.gmconfig -s %s -ns %s",
        g_toolPath, g_connServer, g_testNameSpace);
    ret = executeCommand(cmd, "Command type: import_kvtable, Import file from ", "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[] = "gmimport_Kvtabel_error_3";
    ret = GmcKvDropTable(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 17．建完表（表的配置参数为NULL）后，再次导入配置文件
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_017)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char cmd[512];

    // 创建kvtabel
    ret = GmcKvCreateTable(g_stmt, g_tableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(cmd, 512,
        "%s/gmimport -c kvtable -f schema_file/gmimport/gmimport_Kvtabel_01.gmconfig -t KV0 -s %s ",
        g_toolPath, g_connServer);
    system(cmd);
    ret = executeCommand(cmd, "import batch exec unsucc. ret = 1009013");
    EXPECT_EQ(GMERR_OK, ret);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 18.导入kv数据(key 长度为 512)，查询kv数据。
TEST_F(Gmimport_test, Tool_010_Tools_Kv_Gmimport_test_018)
{
    void *kvtable = NULL;
    char cmd[1024];

    // create kvtable
    ret = GmcKvCreateTable(g_stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // open kvtable
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入数据
    snprintf(cmd, 1024, "%s/gmimport -c kvdata -f schema_file/gmimport/KV-00.gmkv -t KV0  -ns %s  -s %s", g_toolPath,
        g_testNameSpace, g_connServer);
    ret = executeCommand(cmd, "Insert data succeed. successNum: 1", "Command type: import_kvdata, Import file from ",
        "/GMDBV5/test/sdv/testcases/04_Tool/010_Tools_Kv/schema_file/gmimport/KV-00.gmkv\" successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入的kv值
    char output[1024] = {0};
    char key[512] =
        "zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_"
        "0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_"
        "0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_"
        "0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_"
        "0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0zhangsan_0A";
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(88, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);

    // drop kvtable
    ret = GmcKvDropTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}
