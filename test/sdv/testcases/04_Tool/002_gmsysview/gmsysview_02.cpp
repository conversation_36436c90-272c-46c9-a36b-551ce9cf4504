#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

class gmsysview_02 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void gmsysview_02::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void gmsysview_02::TearDown()
{
    AW_CHECK_LOG_END();
    testGmcDisconnect(g_conn, g_stmt);
}

/*****************************************************************************
 * Description  : 001 -u连接数据库查看视图，错误的用户名参数
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * 备注： 当前迭代不做用户名/密码校验(非鉴权)
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_001)  // -u已删除
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    char const *error_username = "xxx";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s  -q %s", g_toolPath, g_connServer, view_name);
    // system(g_command);
    ret = executeCommand(g_command, "WORKER_NAME", "WORKER_ID", "WORKER_TYPE", "THREAD_ID");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*****************************************************************************
 * Description  : 002 -s连接数据库查看视图，错误的IP
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_002)
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    char const *error_connServer = "127.0.0.1";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s   -q %s", g_toolPath, error_connServer, view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "sysview connect unsucc");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*****************************************************************************
 * Description  : 003 -u/-s连接数据库查看视图，不加参数
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_003)  // -u已删除，测-q
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s   -q", g_toolPath, g_connServer);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The option(\"-q\") must input 1 parameter(s).");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s    -q %s", g_toolPath, view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 004 -h查看帮助信息，后加错误参数(正常不需要加参数)
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_004)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -h xx", g_toolPath);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "xx is unsound parameter value! only support <all | yang>");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 005 -v查看版本信息，后加错误参数(正常不需要加参数)
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_005)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -v xx", g_toolPath);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The option(\"-v\") does not require any parameter, the \"xx\" is unsound.");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 006 只运行-e,不加参数 例如./gmsysview -e
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_006)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -e", g_toolPath);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "ber or expr");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 007 -e指定运行环境非RTOS/DAP
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_007)
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    char const *error_runEnv = "XXX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s   -q %s", g_toolPath, g_connServer, error_runEnv, view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "sysview get records unsucc, ret = 1009001");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 008 -q查看视图信息指定错误不支持的视图名
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_008)
{
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    char const *view_name = "V\\$DRT_WORKER_STAT";
    char const *error_view_name = "V\\$XXX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s   -q %s", g_toolPath, g_connServer, error_view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "sysview get records unsucc, ret = 1009001");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 009 -q查看视图信息不加视图名参数
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_009)
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    char const *error_view_name = "";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s   -q %s", g_toolPath, g_connServer, error_view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The option(\"-q\") must input 1 parameter(s).");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 010 只运行./gmsysview，不加选项参数 //迭代四新增交互式命令，迭代二用例不支持 2020/11/23
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_010)
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -z", g_toolPath);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The first option \"-z\" is undefined.");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 011 运行./gmsysview，后加上不支持的选项参数，如-d、-f、z-g等
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_011)
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -d", g_toolPath);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The first option \"-d\" is undefined.");
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -f", g_toolPath);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The number of parameters for the option(\"-f\") is in the range((1, 3))");
    ASSERT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -g", g_toolPath);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The first option \"-g\" is undefined.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*****************************************************************************
 * Description  : 012 重复参数调用
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : huanghonglin/hwx936883   2020.09.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(gmsysview_02, gmsysview_02_012)
{
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s   -q %s -q %s", g_toolPath, g_connServer, view_name, view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The option \"-q\" is not allowed to reuse.");
    ASSERT_EQ(GMERR_OK, ret);

    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s    -q %s", g_toolPath,  g_connServer,
    //    view_name); printf("%s\n", g_command);
    // system(g_command);
    // ret = executeCommand(g_command, "The option \"-e\" is used before.");
    // ASSERT_EQ(GMERR_OK, ret); //-e 不再生效，去除该场景

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s  -s  -q %s", g_toolPath, g_connServer, view_name);
    printf("%s\n", g_command);
    ret = system(g_command);
    ASSERT_LT(0, ret);
    ret = executeCommand(g_command, "The option \"-s\" is not allowed to reuse.");
    ASSERT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -s %s  -q %s   -q %s", g_toolPath, g_connServer, g_connServer,
        view_name);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "The option \"-q\" is not allowed to reuse.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
