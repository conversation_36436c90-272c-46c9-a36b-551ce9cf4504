#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

class gmsysview_04 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void gmsysview_04::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void gmsysview_04::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static const char *g_resPoolTestName = "resource_pool_test";
static const char *g_resPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 1,
        "start_id" : 1,
        "capacity" : 400,
        "order" : 0,
        "alloc_type" : 0
    })";

/*****************************************************************************
 * Description  : 001 -c参数正常连接数据库，-rc + 帮助信息
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    // -rc + 帮助信息
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview   -h -rc", g_toolPath);
    ret = executeCommand(g_command, "print online help manual", "print the gmsysview version");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

}
/*****************************************************************************
 * Description  : 002 -c参数正常连接数据库，-rc + 版本信息
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    // -rc + 版本信息
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview   -v -rc", g_toolPath);
    ret = executeCommand(g_command, "sysview version");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

}
/*****************************************************************************
 * Description  : 003 -c参数正常连接数据库，-rc + 普通表 -view_fmt json模式
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";


    readJanssonFile("schema_file/insert_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    // -rc + 普通系统视图
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -rc -s %s  -q %s", g_toolPath, g_connServer, view_name);
    ret = executeCommand(g_command, "WORKER_NAME", "WORKER_ID", "WORKER_TYPE", "THREAD_ID");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char const *view_name1 = "V\\$STORAGE_HEAP_STAT";
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s   -q %s -rc -view_fmt json", g_toolPath, g_connServer, view_name1);
    ret = executeCommand(g_command, "LABEL_ID", "FILE_ID", "PAGE_COUNT", "CUR_ITEM_NUM");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 004 -c参数正常连接数据库，-rc + 普通表记录数 视图命令
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";
    readJanssonFile("schema_file/insert_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    // -rc + 普通系统视图
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -rc -s %s  -q %s", g_toolPath, g_connServer, view_name);
    ret = executeCommand(g_command, "WORKER_NAME", "WORKER_ID", "WORKER_TYPE", "THREAD_ID");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char const *view_name1 = "V\\$STORAGE_HEAP_STAT";
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s   -q %s -rc -view_fmt json", g_toolPath, g_connServer, view_name1);
    ret = executeCommand(g_command, "LABEL_ID", "FILE_ID", "PAGE_COUNT", "CUR_ITEM_NUM");
    ASSERT_EQ(GMERR_OK, ret);
    
    // -rc + 普通表的记录数
    char const *view_name2 = "V\\$STORAGE_VERTEX_COUNT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -rc -q %s ", g_toolPath, g_connServer, view_name2);
    ret = executeCommand(g_command, "table", "record count");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 005 -c参数正常连接数据库，-rc + 普通表记录数 count命令
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    readJanssonFile("schema_file/insert_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    // -rc + 普通系统视图
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -rc -s %s  -q %s", g_toolPath, g_connServer, view_name);
    ret = executeCommand(g_command, "WORKER_NAME", "WORKER_ID", "WORKER_TYPE", "THREAD_ID");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char const *view_name1 = "V\\$STORAGE_HEAP_STAT";
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s   -q %s -rc -view_fmt json", g_toolPath, g_connServer, view_name1);
    ret = executeCommand(g_command, "LABEL_ID", "FILE_ID", "PAGE_COUNT", "CUR_ITEM_NUM");
    ASSERT_EQ(GMERR_OK, ret);

    // -rc + 普通表的记录数 count命令方式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -ns public -rc count T39 top 10", g_toolPath, g_connServer);
    ret = executeCommand(g_command, "table", "record count");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 006 -c参数正常连接数据库，-rc + 普通表记录信息 record命令方式
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    readJanssonFile("schema_file/insert_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    // -rc + 普通系统视图
    char const *view_name = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview  -rc -s %s  -q %s", g_toolPath, g_connServer, view_name);

    ret = executeCommand(g_command, "WORKER_NAME", "WORKER_ID", "WORKER_TYPE", "THREAD_ID");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char const *view_name1 = "V\\$STORAGE_HEAP_STAT";
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s   -q %s -rc -view_fmt json", g_toolPath, g_connServer, view_name1);

    ret = executeCommand(g_command, "LABEL_ID", "FILE_ID", "PAGE_COUNT", "CUR_ITEM_NUM");
    ASSERT_EQ(GMERR_OK, ret);
    

    // -rc + 普通表的记录信息
    char const *view_name3 = "V\\$CATA_VERTEX_LABEL_INFO";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -rc -s %s   -q %s  -l 5", g_toolPath, g_connServer, view_name3);

    ret = executeCommand(g_command, "VERTEX_LABEL_TYPE");
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name15 = "V\\$DRT_WORKER_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -rc -s %s   -q %s -o WORKER_ID asc -l 66 -f"
        " WORKER_TYPE='DRT_WORKER_BGTASK'", g_toolPath, g_connServer, view_name15);

    ret = executeCommand(g_command, "WORKER_MONITOR");
    ASSERT_EQ(GMERR_OK, ret);

    // -rc + 普通表的记录信息 record命令方式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -rc -s %s record T39 -c F0 -lt 10", g_toolPath, g_connServer);
    ret = executeCommand(g_command, "check_version");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 007 -c参数正常连接数据库，-rc + kv表记录数 视图命令
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";


    // gmimport工具导入创建kv
    char KvName[1024] = "KV";
    GmcKvDropTable(g_stmt, KvName);
    const char filePath1[256] = "./kvtable.gmconfig";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f %s -ns %s -t KV ", g_toolPath, filePath1, g_testNameSpace);

    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KvName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
    char key1[128];
    for (int i = 100; i < 200; i++) {
        // data 1 set
        sprintf(key1, "zhangsan%d", i);
        int32_t value = i + 10;
        ret = GmcKvSet(g_stmt, key1, strlen(key1), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    // -rc + KV表的记录数
    char const *view_name4 = "V\\$STORAGE_KV_COUNT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -rc -q %s ", g_toolPath, g_connServer, view_name4);

    ret = executeCommand(g_command, "table", "kv count");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, KvName);
    ASSERT_EQ(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 008 -c参数正常连接数据库，-rc + kv表记录数 count命令
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    // gmimport工具导入创建kv
    char KvName[1024] = "KV";
    GmcKvDropTable(g_stmt, KvName);
    const char filePath1[256] = "./kvtable.gmconfig";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f %s -ns %s -t KV ", g_toolPath, filePath1, g_testNameSpace);

    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KvName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
    char key1[128];
    for (int i = 100; i < 200; i++) {
        // data 1 set
        sprintf(key1, "zhangsan%d", i);
        int32_t value = i + 10;
        ret = GmcKvSet(g_stmt, key1, strlen(key1), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }


    // -rc + KV表的记录数 count命令方式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -rc count_table_kv KV top 10", g_toolPath, g_connServer);

    ret = executeCommand(g_command, "table", "kv count");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, KvName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 001 -c参数正常连接数据库，-rc + kv表记录信息
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    // gmimport工具导入创建kv
    char KvName[1024] = "KV";
    GmcKvDropTable(g_stmt, KvName);
    const char filePath1[256] = "./kvtable.gmconfig";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f %s -ns %s -t KV ", g_toolPath, filePath1, g_testNameSpace);

    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KvName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
    char key1[128];
    for (int i = 100; i < 200; i++) {
        // data 1 set
        sprintf(key1, "zhangsan%d", i);
        int32_t value = i + 10;
        ret = GmcKvSet(g_stmt, key1, strlen(key1), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // -rc + KV表的记录信息
    char const *view_name5 = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -rc -q %s ", g_toolPath, g_connServer, view_name5);

    ret = executeCommand(g_command, "LABEL_NAME");
    ASSERT_EQ(GMERR_OK, ret);


    ret = GmcKvDropTable(g_stmt, KvName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 001 -c参数正常连接数据库，-rc + -f kv表信息 record命令
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    // gmimport工具导入创建kv
    char KvName[1024] = "KV";
    GmcKvDropTable(g_stmt, KvName);
    const char filePath1[256] = "./kvtable.gmconfig";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f %s -ns %s -t KV ", g_toolPath, filePath1, g_testNameSpace);

    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KvName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
    char key1[128];
    for (int i = 100; i < 200; i++) {
        // data 1 set
        sprintf(key1, "zhangsan%d", i);
        int32_t value = i + 10;
        ret = GmcKvSet(g_stmt, key1, strlen(key1), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }


    // -rc + KV表信息
    char const *view_name11 = "V\\$CATA_KV_TABLE_INFO ";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -rc -q %s -f BASE_LABEL/LABEL_NAME=KV ", g_toolPath, g_connServer, view_name11);
    ret = executeCommand(g_command, "CONFIG_INFO");
    ASSERT_EQ(GMERR_OK, ret);

    // -rc + KV表的记录信息 record命令方式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -rc -s %s record_table_kv KV ", g_toolPath, g_connServer);
    ret = executeCommand(g_command, "key_len");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, KvName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 011 -c参数正常连接数据库，-rc +告警信息
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    char teststr[] = "testver2";
    char g_label_name[] = "T39";
    char *test_schema = NULL;
    char g_label_config[] = "{\"max_record_count\":10000}";

    // -rc + 获取告警
    char const *view_name6 = "alarm";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -rc %s ", g_toolPath, view_name6);

    ret = executeCommand(g_command, "No alarm data");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

}
/*****************************************************************************
 * Description  : 012 -c参数正常连接数据库，-rc +预估内存占用
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    // -rc + 预估内存占用
    char const *view_name7 = "estimate";
    char const *file_path1 = "schema_file/EstimatedMemorySize_test_001.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -rc %s %s", g_toolPath,
        g_connServer, view_name7, file_path1);

    ret = executeCommand(g_command, "Unique hash size");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
/*****************************************************************************
 * Description  : 013 -c参数正常连接数据库，-rc +特殊资源视图
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    // -rc + 特殊资源视图
    // 预制一张表
    char *labelName = (char *)"simpleLabel";
    char *resource_schema = NULL;
    const char *tabelConfig = "{\"max_record_count\" : 1000000}";
    readJanssonFile("schema_file/resourceSimpleLabel.gmjson", &resource_schema);
    ASSERT_NE((void *)NULL, resource_schema);
    GmcDestroyResPool(g_stmt, g_resPoolTestName);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateResPool(g_stmt, g_resPoolTest);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, resource_schema, tabelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, g_resPoolTestName, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0 = 1;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F1 = 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT64, &F1, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F14", GMC_DATATYPE_RESOURCE, &F0, sizeof(F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // -rc + 特殊资源视图
    char const *view_name8 = "V\\$STORAGE_RESOURCE_BIND_TO_LABEL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -f [t]simpleLabel -rc -q %s", g_toolPath, g_connServer, view_name8);

    ret = executeCommand(g_command, "RES_POOL_BIND_TO_LABEL_INFO");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, g_resPoolTestName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    free(resource_schema);
}
/*****************************************************************************
 * Description  : 014 -c参数正常连接数据库，-rc +subtree 条件查询
 * Input        : None
 * Output       : None
 * *****************************************************************************/
TEST_F(gmsysview_04, gmsysview_04_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    const char *allow_list_file = "./reserve2.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);

    uint32_t priK = 3;
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    char *sublabelName = (char *)"SubT0Con";
    char *subtree_schema = NULL;
    const char *tabelConfigsub = "{\"max_record_count\" : 1000000}";
    readJanssonFile("schema_file/SubTreeVertexLabel.gmjson", &subtree_schema);
    ASSERT_NE((void *)NULL, subtree_schema);
    ret = GmcCreateVertexLabel(g_stmt, subtree_schema, tabelConfigsub);
    EXPECT_EQ(GMERR_OK, ret);
    const char *Filteraa=
    R"({
        "F1": 1
    })";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -ns public -rc -s %s subtree SubT0Con -depth 1 -mode EXPLICIT"
                          " -config DEFAULT -json '%s'",
                          g_toolPath, g_connServer, Filteraa);
    system(g_command);

    ret = GmcDropVertexLabel(g_stmt, sublabelName);
    EXPECT_EQ(GMERR_OK, ret);
    free(subtree_schema);

}

