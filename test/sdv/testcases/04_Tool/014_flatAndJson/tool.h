extern "C" {
#if !defined(RUN_DATACOM_HPE)
#endif
}
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

// TEST_DEBUG为1则用system打印结果便于调试，为0则用popen获取结果用作校验
#define TEST_DEBUG 0  // 入库必须是0
#define MAX_CMD_SIZE 1024

using namespace std;

char cmd[MAX_CMD_SIZE];
char schema[MAX_CMD_SIZE * 2];
int ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
int subCount = 0;
const char *g_printMode[4] = {"-view_fmt json", "-view_fmt flat_truncate", "-view_fmt flat_full", " "};
const char *config = "{\"max_record_num\": 100}";
// 格式化用vertex schema
const char *vertex = R"(
    [{
        "type":"record",
        "name":"%s",
        "version":"2.0",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32", "nullable":true},
            {"name":"F2", "type":"uint32", "nullable":true},
            {"name":"F3", "type":"string", "nullable":true}
        ],
        "keys":[
            {
                "node":"%s",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }]
)";
const char *vertex_index = R"(
    [{
        "type":"record",
        "name":"%s",
        "version":"2.0",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"%s",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"%s",
                "name":"localhash_key",
                "fields":["F1"],
                "index":{"type":"localhash"},
                "constraints":{"unique":true}
            },{
                "node":"%s",
                "name":"hashcluster_key",
                "fields":["F2"],
                "index":{"type":"hashcluster"},
                "constraints":{"unique":true}
            }, {
                "node":"%s",
                "name":"local_key",
                "fields":["F2"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    }]
)";

const char *vertex_index2 = R"(
    [{
        "type":"record",
        "name":"%s",
        "version":"2.0",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"%s",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"%s",
                "name":"localhash_key",
                "fields":["F1"],
                "index":{"type":"localhash"},
                "constraints":{"unique":false}
            },{
                "node":"%s",
                "name":"hashcluster_key",
                "fields":["F2"],
                "index":{"type":"hashcluster"},
                "constraints":{"unique":true}
            }
        ]
    }]
)";

const char *vertex_res = R"(
    [{
        "type":"record",
        "name":"%s",
        "version":"2.0",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"resource", "nullable":false},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":[
            {
                "node":"%s",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }]
)";

const char *vertex_tree = R"(
    [{
        "comment": "全字段复杂表",
        "version": "2.0", "type": "record", "name": "label0",
        "fields": [
            { "name": "rfield_int8", "type": "int8"},
            { "name": "rfield_int16", "type": "int16" },
            { "name": "rfield_int32", "type": "int32"  },
            { "name": "rfield_int64", "type": "int64"  },
            { "name": "rfield_uint8", "type": "uint8"  },
            { "name": "rfield_uint16", "type": "uint16"  },
            { "name": "rfield_uint32", "type": "uint32"  },
            { "name": "rfield_uint64", "type": "uint64"  },
            { "name": "rfield_float", "type": "float"  },
            { "name": "rfield_double", "type": "double"  },
            { "name": "rfield_boolean", "type": "boolean"  },
            { "name": "rfield_fixed", "type": "fixed", "size": 8  },
            { "name": "rfield_bitmap", "type": "bitmap", "size": 8  },
            { "name": "rfield_time", "type": "time"  },
            { "name": "rfield_bytes", "type": "bytes", "size": 8  },
            { "name": "rfield_string", "type": "string", "size": 8  },
            { "name": "rfield_record", "type": "record",  
            "fixed_array": true, "size": 3,
            "fields": [
                { "name": "cfield_uint32", "type": "uint32" },
                { "name": "cfield_string", "type": "string", "size": 8 }
            ]
            }
        ],
        "keys": [
            { "name": "PK", "index": { "type": "primary" },
            "node": "label0",
            "fields": [ "rfield_int8", "rfield_int16", "rfield_int32", "rfield_int64" ],
            "constraints": { "unique": true },
            "comment": "主键索引"
            }
        ]
}]
)";

const char *vertex_allType = R"(
    [{
    "comment": "全字段简单表",
    "version": "2.0", "type": "record", "name": "label0",
    "config": {
        "check_validity": true
    },
    "fields": [
        { "name": "rfield_int8", "type": "int8", "nullable":false },
        { "name": "rfield_int16", "type": "int16", "nullable":false },
        { "name": "rfield_int32", "type": "int32" , "nullable":false},
        { "name": "rfield_int64", "type": "int64" , "nullable":false},
        { "name": "rfield_uint8", "type": "uint8", "nullable":false },
        { "name": "rfield_uint16", "type": "uint16" , "nullable":false},
        { "name": "rfield_uint32", "type": "uint32" , "nullable":false},
        { "name": "rfield_uint64", "type": "uint64", "nullable":false },
        { "name": "rfield_float", "type": "float" , "nullable":false},
        { "name": "rfield_double", "type": "double" , "nullable":false},
        { "name": "rfield_boolean", "type": "boolean" , "nullable":false},
        { "name": "rfield_fixed", "type": "fixed", "size": 8 , "nullable":false},
        { "name": "rfield_bitmap", "type": "bitmap", "size": 8, "nullable":false },
        { "name": "rfield_time", "type": "time" , "nullable":false}
    ],
    "keys": [
        { "name": "PK", "index": { "type": "primary" },
        "node": "label0",
        "fields": [ "rfield_int8", "rfield_int16", "rfield_int32", "rfield_int64" ],
        "constraints": { "unique": true },
        "comment": "主键索引"
        }
    ]
}]
)";
// 格式化用edge schema
const char *edge = R"(
    [{
            "name":"%s",
            "source_vertex_label":"%s",
            "comment": "edge",
            "dest_vertex_label":"%s",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F1",
                            "dest_property": "F1"
                        }
                    ]
            }
    }]
)";
// 格式化用subInfo
const char *subInfo = R"(
    {
        "label_name":"%s",
        "comment":"VertexLabel subscription",
        "type":"before_commit",
        "events":
            [
                {"type":"insert", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "is_reliable": true
    }
)";

// resource
const char *resPool =
    R"({
        "name" : "%s",
        "pool_id" : %d,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";
const char *Dstore =
    R"(
        {"delta_stores":
            [{
                "name": "%s",
                "init_mem_size": 32768,
                "max_mem_size": 131072,
                "extend_mem_size": 8192,
                "page_size": 4096 
            }]
        }
    )";
/*******************************************************************************
  函 数 名      :  testSysview
  功能描述      :  校验视图 视图结果长度要小于4K
  输入参数      :  sysviewName 视图的-q选项参数如："V\\$CATA_KV_TABLE_INFO"
                   printMode传入全局数组：g_printMode[0]打印json；g_printMode[1]打印truncate；g_printMode[2]打印full
                   expect1/expect2/expect3/expect4/expect5
预期匹配字符串，需要按字符串出现顺序排列,且两个字符串不能紧密相连,至少隔一个字符，否则只能匹配到前一个 输出参数      :
None 返 回 值      :  None
*******************************************************************************/
void testSysview(const char *sysviewName, const char *printMode = "", const char *expect1 = NULL,
    const char *expect2 = NULL, const char *expect3 = NULL, const char *expect4 = NULL, const char *expect5 = NULL,
    const char *tag = " ")
{
    sprintf(cmd, "%s/gmsysview -q %s  -s %s  %s %s", g_toolPath, sysviewName, g_connServer, printMode, tag);
    printf("[info]: the cmd is: %s\n", cmd);
#if TEST_DEBUG
    system(cmd);
#else
    ret = executeCommand(cmd, expect1, expect2, expect3, expect4, expect5);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    memset(cmd, 0, MAX_CMD_SIZE);
}
/*******************************************************************************
  函 数 名      :  testLargeOutputSysview
  功能描述      :  结果太大的视图导入到文件，通过grep校验匹配
  输入参数      :  入参i为g_printMode全局数组下标。即打印模式为printMode[i]
                   其他同上
  输出参数      :  None
  返 回 值      :  None
*******************************************************************************/
void testLargeOutputSysview(const char *sysviewName, uint32_t i, const char *expect1 = NULL, const char *expect2 = NULL,
    const char *expect3 = NULL, const char *expect4 = NULL, const char *expect5 = NULL, const char *tag = " ")
{
    sprintf(cmd, "%s/gmsysview -q %s  -s %s  %s %s > test.%d", g_toolPath, sysviewName,
        g_connServer, g_printMode[i], tag, i);
    printf("[info]: the cmd is: %s\n", cmd);
    system(cmd);
#if TEST_DEBUG
    printf("[info]::\n");
    printf("print json in file test.0\n");
    printf("print flat_truncate in file test.1\n");
    printf("print flat_full in file test.2\n");
    printf("print default file test.3 \n");
#else
    char fileName[30];
    sprintf(fileName, "test.%d", i);
    if (expect1 != NULL) {
        sprintf(cmd, "grep '%s' %s", expect1, fileName);
        ret = executeCommand(cmd, expect1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (expect2 != NULL) {
        sprintf(cmd, "grep '%s' %s", expect2, fileName);
        ret = executeCommand(cmd, expect2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (expect3 != NULL) {
        sprintf(cmd, "grep '%s' %s", expect3, fileName);
        ret = executeCommand(cmd, expect3);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (expect4 != NULL) {
        sprintf(cmd, "grep '%s' %s", expect4, fileName);
        ret = executeCommand(cmd, expect4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (expect5 != NULL) {
        sprintf(cmd, "grep '%s' %s", expect5, fileName);
        ret = executeCommand(cmd, expect5);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("rm -rf test.0");
    system("rm -rf test.1");
    system("rm -rf test.2");
    system("rm -rf test.3");
#endif
    memset(cmd, 0, MAX_CMD_SIZE);
}

/*******************************************************************************
  函 数 名      :  snCallbackNull
  功能描述      :  仅作参数用
  输入参数      :  None
  输出参数      :  None
  返 回 值      :  None
*******************************************************************************/
void snCallbackNull(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    printf("info: callback\n");
}

void writeToSimple(GmcStmtT *stmt, const char *label_name, int start_num, int write_num)
{
    int8_t int8Value = 8;
    int16_t int16Value = 16;
    int32_t int32Value = 32;
    float f = 1.2;
    double d = 2.2;
    bool b = true;
    char fixed[8] = {1, 2, 3, 4, 5, 6, 7, 8};
    GmcBitMapT bitMap = {0, 7, NULL};
    uint8_t bits[8 / 2];
    memset(bits, 0xffff, 8 / 2);
    bits[8 / 2 - 1] = '\0';
    bitMap.bits = bits;
    for (int i = start_num; i < write_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_int8", GMC_DATATYPE_INT8, &int8Value, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_int16", GMC_DATATYPE_INT16, &int16Value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_int32", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t int64Value = i;
        ret = GmcSetVertexProperty(stmt, "rfield_int64", GMC_DATATYPE_INT64, &int64Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t uint8value = i % 10;
        ret = GmcSetVertexProperty(stmt, "rfield_uint8", GMC_DATATYPE_UINT8, &uint8value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint16_t uint16Value = i % 100;
        ret = GmcSetVertexProperty(stmt, "rfield_uint16", GMC_DATATYPE_UINT16, &uint16Value, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t uint32Value = i % 1000;
        ret = GmcSetVertexProperty(stmt, "rfield_uint32", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t uint64Value = i;
        ret = GmcSetVertexProperty(stmt, "rfield_uint64", GMC_DATATYPE_UINT64, &uint64Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_float", GMC_DATATYPE_FLOAT, &f, sizeof(float));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_double", GMC_DATATYPE_DOUBLE, &d, sizeof(double));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_boolean", GMC_DATATYPE_BOOL, &b, sizeof(bool));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_fixed", GMC_DATATYPE_FIXED, fixed, sizeof(fixed));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "rfield_bitmap", GMC_DATATYPE_BITMAP, &bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t time = i;
        ret = GmcSetVertexProperty(stmt, "rfield_time", GMC_DATATYPE_TIME, &time, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void writeToComplex(GmcStmtT *stmt, const char *label_name, int start_num, int write_num)
{
    int8_t int8Value = 8;
    int16_t int16Value = 16;
    int32_t int32Value = 32;
    float f = 1.2;
    double d = 2.2;
    bool b = true;
    char fixed[8] = {1, 2, 3, 4, 5, 6, 7, 8};
    char bytes[8] = {1, 2, 3, 4, 5, 6, 7, 8};
    GmcBitMapT bitMap = {0, 7, NULL};
    uint8_t bits[8 / 2];
    memset(bits, 0xffff, 8 / 2);
    bits[8 / 2 - 1] = '\0';
    bitMap.bits = bits;
    const char *str = "string";
    for (int i = start_num; i < write_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root_node, *rfield_record;
        ret = GmcGetRootNode(stmt, &root_node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root_node, "rfield_record", &rfield_record);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_int8", GMC_DATATYPE_INT8, &int8Value, sizeof(int8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_int16", GMC_DATATYPE_INT16, &int16Value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_int32", GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t int64Value = i;
        ret = GmcNodeSetPropertyByName(root_node, "rfield_int64", GMC_DATATYPE_INT64, &int64Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t uint8value = i % 10;
        ret = GmcNodeSetPropertyByName(root_node, "rfield_uint8", GMC_DATATYPE_UINT8, &uint8value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint16_t uint16Value = i % 100;
        ret = GmcNodeSetPropertyByName(root_node, "rfield_uint16", GMC_DATATYPE_UINT16, &uint16Value, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t uint32Value = i % 1000;
        ret = GmcNodeSetPropertyByName(root_node, "rfield_uint32", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t uint64Value = i;
        ret = GmcNodeSetPropertyByName(root_node, "rfield_uint64", GMC_DATATYPE_UINT64, &uint64Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_float", GMC_DATATYPE_FLOAT, &f, sizeof(f));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_double", GMC_DATATYPE_DOUBLE, &d, sizeof(d));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_boolean", GMC_DATATYPE_BOOL, &b, sizeof(b));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_fixed", GMC_DATATYPE_FIXED, fixed, sizeof(fixed));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_bitmap", GMC_DATATYPE_BITMAP, &bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t time = i;
        ret = GmcNodeSetPropertyByName(root_node, "rfield_time", GMC_DATATYPE_TIME, &time, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_bytes", GMC_DATATYPE_BYTES, bytes, sizeof(bytes));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root_node, "rfield_string", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < 3; j++) {
            uint32Value = j;
            ret = GmcNodeSetPropertyByName(
                rfield_record, "cfield_uint32", GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(rfield_record, "cfield_string", GMC_DATATYPE_STRING, str, strlen(str));
            EXPECT_EQ(GMERR_OK, ret);
            if (j < 2) {
                ret = GmcNodeGetNextElement(rfield_record, &rfield_record);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

int getValue(const char *command, string key_word)
{
    string value_str;
    int value;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        cout << endl << "WRONG!" << endl;
        return -1;
    }
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        value_str.assign(cmdOutPut);
        string::size_type idx = value_str.find(key_word);
        if (idx != string::npos) {
            value_str = value_str.substr(value_str.find(key_word) + key_word.length());
            value = stoi(value_str);
            break;
        }
    }
    pclose(pf);

    return value;
}

int CountExistLabelNum()
{
    (void)sprintf(cmd,
        "%s/gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -s %s > Tool_014_071_1.txt", g_toolPath, g_connServer);
    system(cmd);
    system("grep -r \"index = \" Tool_014_071_1.txt > Tool_014_071_2.txt");
    int labelNum = getValue("tail -n 1 Tool_014_071_2.txt", "index = ");
    system("rm -rf Tool_014_071_1.txt");
    system("rm -rf Tool_014_071_2.txt");
    return labelNum + 1;
}

// 获取视图字段值
static int ExecuteCommandValue(char *cmd2, uint32_t *value)
{
    char cmdOutput[128] = {0};
    uint32_t getvalue = 0;
    FILE *pf = popen(cmd2, "r");
    if (pf == NULL) {
        return -1;
    }
    while (NULL != fgets(cmdOutput, 128, pf)) {
        getvalue = atoi(cmdOutput);
        memcpy_s(value, sizeof(getvalue), &getvalue, sizeof(getvalue));
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

// 获取STORAGE_INDEX_GLOBAL_STAT的视图过滤信息
void GetStorageIndexInfo(const char *grepName, uint32_t *value)
{
    int ret2 = 0;
    char command[MAX_CMD_SIZE];
    if (grepName == NULL) {
        (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_INDEX_GLOBAL_STAT", g_toolPath);
        system(command);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_INDEX_GLOBAL_STAT"
            "| grep %s | awk -F\": \" '{print $2}'", g_toolPath, grepName);
        if ((grepName != NULL) && (value != NULL)) {
            ret2 = ExecuteCommandValue(command, value);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }
    memset_s(command, sizeof(command), 0, sizeof(command));
}
