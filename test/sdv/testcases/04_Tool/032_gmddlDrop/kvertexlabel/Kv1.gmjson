[{
    "type":"record", 
    "name":"Kv1", 
    "fields":[
        {"name":"F0", "type":"int32"},"
        {"name":"F1", "type":"int32", "nullable" : false , "default" : 1},
        {"name":"F2", "type":"int"},
        {"name":"F3", "type":"int32"}],
    "keys":[
        {
            "node":"Kv1", 
            "name":"Kv1_K0", 
            "fields":["F0"], 
            "index":{"type":"primary"},
            "constraints":{ "unique":true}
            }
        ]
}
]
