extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
int affectRows;

char *test_delat_config_json = NULL;
char *scan_vertex_label_schema1 = NULL;
char *scan_vertex_label_schema2 = NULL;
char *normal_vertexlabel_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":10000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

void set_VertexProperty_F7(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_F9(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[8] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[8] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

class DML_011_scanVertexTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schemaFile/scan_vertex_lable.gmconfig", &test_delat_config_json);
        ASSERT_NE((void *)NULL, test_delat_config_json);
        readJanssonFile("schemaFile/scan_vertex_label_1.gmjson", &scan_vertex_label_schema1);
        ASSERT_NE((void *)NULL, scan_vertex_label_schema1);
        readJanssonFile("schemaFile/scan_vertex_label_2.gmjson", &scan_vertex_label_schema2);
        ASSERT_NE((void *)NULL, scan_vertex_label_schema2);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        int ret;
        free(test_delat_config_json);
        free(scan_vertex_label_schema1);
        free(scan_vertex_label_schema2);
        free(normal_vertexlabel_schema);
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DML_011_scanVertexTest::SetUp()
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_DUPLICATE_TABLE);
}

void DML_011_scanVertexTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

const char *g_label_name1 = "DML_011_scanVertexTest_001";
const char *g_label_name2 = "DML_011_scanVertexTest_002";

// 1.GmcExecScanVertex接口空指针
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_001)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(NULL, NULL);
    // 错误码变更兼容V3
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcExecute(NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    int fetchNum = 2 * insertNum;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        bool isNull = false;
        uint32_t pkValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &pkValue, sizeof(int32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(pkValue, *(int32_t *)sp_1_get);
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt++;
        fetchNum--;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(insertNum, cnt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 2.GmcExecScanVertex接口指定存在的keyName
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_002)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Value = 1;
    bool isFinish = 0;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *keyName = "DML_011_scanVertexTest_K0";
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(F0Value, *(int32_t *)sp_1_get);
    EXPECT_EQ(F0Value, *(int32_t *)(sp_1_get + 4));
    EXPECT_EQ(F0Value, *(int32_t *)(sp_1_get + 8));
    EXPECT_EQ(F0Value, *(int32_t *)(sp_1_get + 12));
    free(sp_1_get);
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isFinish);
    GmcResetStmt(g_stmt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 3.GmcExecScanVertex接口传入不存在的keyName
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_003)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *noExistkeyName = "F8";
    ret = GmcSetIndexKeyName(g_stmt, noExistkeyName);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(g_stmt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 4.GmcVertexFetch接口空指针
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_004)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = 0;
    ret = GmcFetch(NULL, &isFinish);
    // 错误码变更 兼容V3 
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcResetStmt(g_stmt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 5.接口空指针
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_005)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcResetStmt(g_stmt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 6. 创建游标，扫描一张空表，扫描空表正确返回finish标识,读数据失败
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_006)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // scan 空表
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = 0;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isFinish);
    GmcResetStmt(g_stmt);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F0Value = 0;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = true;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(eof, true);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 7.创建游标，扫描一张有数据的表，能扫描到所有数据
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_007)
{
    GmcDropVertexLabel(g_stmt, g_label_name1);
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 10;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);

        printf("NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    int fetchNum = 2 * insertNum;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt++;
        fetchNum--;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(insertNum, cnt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 8.创建游标，在扫描数据的过程中，关闭游标，在创建游标，再重新扫描数据，能正确扫描到数据，并且能正确关闭游标。再第二次启动的时候，游标返回的数据是从头开始的数据。
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_008)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 1000;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // 扫描500条，关闭
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = 0;
    int cnt = 0;
    int fetchNum = 2000;
    for (uint32_t num = 0; num < 500; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        // 聚簇容器变更，不关注顺序
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt++;
        fetchNum--;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(500, cnt);

    // 重新开始扫描1000条
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    cnt = 0;
    fetchNum = 2000;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        // 聚簇容器变更，不关注顺序
        printf("insert NUM:%d\n", num);
        free(sp_1_get);
        cnt++;
        fetchNum--;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(insertNum, cnt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

void *scan_vertex_009(void *args)
{
    void *vertexLabel = NULL;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    int insertNum = 1000;
    int ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt_t, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt_t, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt_t, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        // 聚簇容器变更，不关注顺序
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt++;
    }
    GmcResetStmt(stmt_t);
    EXPECT_EQ(insertNum, cnt);
    GmcFreeStmt(stmt_t);
    ret = testGmcDisconnect(conn_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 9.创建多个表的游标，同线程同时扫描数据，每个游标都能正确的扫描到数据。
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_009)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel_normal = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 1000;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    int err = 0;
    pthread_t sameNameth[3];
    for (int i = 0; i < 3; i++) {
        err = pthread_create(&sameNameth[i], NULL, scan_vertex_009, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < 3; i++) {
        pthread_join(sameNameth[i], NULL);
    }
    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 线程1扫a,b
void *scan_vertex_010_1(void *args)
{
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t1 = NULL;
    GmcStmtT *stmt_t2 = NULL;
    int insertNum = 10;
    int ret = testGmcConnect(&conn_t, &stmt_t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_t, &stmt_t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t1, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t2, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt_t1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_t1);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish1 = 0;
    int cnt1 = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt_t1, &isFinish1);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish1 == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt_t1, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt1, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt1++;
    }
    GmcResetStmt(stmt_t1);
    EXPECT_EQ(insertNum, cnt1);
    GmcFreeStmt(stmt_t1);
    ret = GmcSetIndexKeyName(stmt_t2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_t2);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish2 = 0;
    int cnt2 = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt_t2, &isFinish2);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish2 == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt_t2, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt2, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt2++;
    }
    GmcResetStmt(stmt_t2);
    EXPECT_EQ(insertNum, cnt2);
    GmcFreeStmt(stmt_t2);
    ret = testGmcDisconnect(conn_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2扫a,a
void *scan_vertex_010_2(void *args)
{
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t1 = NULL;
    GmcStmtT *stmt_t2 = NULL;
    int insertNum = 10;
    int ret = testGmcConnect(&conn_t, &stmt_t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_t, &stmt_t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t1, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t2, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt_t1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_t1);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish1 = 0;
    int cnt1 = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt_t1, &isFinish1);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish1 == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt_t1, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt1, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt1++;
    }
    GmcResetStmt(stmt_t1);
    EXPECT_EQ(insertNum, cnt1);
    GmcFreeStmt(stmt_t1);
    ret = GmcSetIndexKeyName(stmt_t2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_t2);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish2 = 0;
    int cnt2 = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt_t2, &isFinish2);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish2 == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt_t2, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt2, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt2++;
    }
    GmcResetStmt(stmt_t2);
    EXPECT_EQ(insertNum, cnt2);
    GmcFreeStmt(stmt_t2);
    ret = testGmcDisconnect(conn_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 10.
// 不同线程创建不同表的游标和同张表的游标，同时扫描数据，每个线程的每个游标都能正确的扫描到数据。(2张表，线程1扫a,b,线程2扫a,a)
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_010)
{
    // 建2个vertexLabel,并写入数据
    GmcStmtT *stmt2 = NULL;
    GmcConnT *conn2 = NULL;
    int ret = testGmcConnect(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt2, scan_vertex_label_schema2, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel2;
    ret = testGmcPrepareStmtByLabelName(stmt2, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 10;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetSuperfieldByName(stmt2, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        free(sp_1);
    }
    // qry vertex
    int err = 0;
    pthread_t sameNameth[2];
    err = pthread_create(&sameNameth[0], NULL, scan_vertex_010_1, NULL);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&sameNameth[1], NULL, scan_vertex_010_2, NULL);
    EXPECT_EQ(GMERR_OK, err);
    for (int i = 0; i < 2; i++) {
        pthread_join(sameNameth[i], NULL);
    }
    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt2, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 11.并发扫描一张表，表中存在数据，每个扫描结果都应正确
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_011)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel_normal;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 1000;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    // qry vertex
    int err = 0;
    pthread_t sameNameth[3];
    for (int i = 0; i < 3; i++) {
        err = pthread_create(&sameNameth[i], NULL, scan_vertex_009, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < 3; i++) {
        pthread_join(sameNameth[i], NULL);
    }
    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 12.用一个连接同时打开一个表的多个全表扫描游标，mainStore有数据，每个游标都能正确读取数据
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_012)
{
    // 建2个vertexLabel,并写入数据
    GmcStmtT *stmt2 = NULL;
    int ret = GmcAllocStmt(g_conn, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel2;
    ret = testGmcPrepareStmtByLabelName(stmt2, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 10;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        free(sp_1);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish1 = 0;
    int cnt1 = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(g_stmt, &isFinish1);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish1 == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt1, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt1, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt1++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(insertNum, cnt1);
    ret = testGmcPrepareStmtByLabelName(stmt2, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish2 = 0;
    int cnt2 = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt2, &isFinish2);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish2 == true) {
            break;
        }
        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt2, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt2, *(int32_t *)sp_1_get);
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(cnt2, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt2++;
    }
    GmcResetStmt(stmt2);
    EXPECT_EQ(insertNum, cnt2);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt2);
}

// 13.用一个连接同时打开多个表的多个全表扫描游标，交替扫描数据，持有游标，扫描下一个表，直到所有表上的数据被取完。（业务侧对账场景）
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_013)
{
    // 建2个vertexLabel,并写入数据
    GmcStmtT *stmt2 = NULL;
    GmcConnT *conn2 = NULL;
    int ret = testGmcConnect(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt2, scan_vertex_label_schema2, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel2;
    ret = testGmcPrepareStmtByLabelName(stmt2, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetSuperfieldByName(stmt2, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        free(sp_1);
    }

    // 交替扫描读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish1 = 0;
    bool isFinish2 = 0;
    int cnt = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(g_stmt, &isFinish1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt2, &isFinish2);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish1 && isFinish2 == true) {
            break;
        }
        bool isNull = false, isNull2 = false;
        uint32_t pkValue = 0,  pkValue2 = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &pkValue, sizeof(int32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(pkValue, *(int32_t *)sp_1_get);
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 12));

        ret = GmcGetVertexPropertyByName(stmt2, "F0", &pkValue2, sizeof(int32_t), &isNull2);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_2_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(stmt2, 0, sp_2_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(pkValue2, *(int32_t *)sp_2_get);
        EXPECT_EQ(pkValue2, *(int32_t *)(sp_2_get + 4));
        EXPECT_EQ(pkValue2, *(int32_t *)(sp_2_get + 8));
        EXPECT_EQ(pkValue2, *(int32_t *)(sp_2_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        free(sp_2_get);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    GmcResetStmt(stmt2);
    EXPECT_EQ(insertNum, cnt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt2, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt2);
}

// 14
// 使用同一个连接，先删除一表的部分数据，开启该表的扫描，被删除的数据不应该被扫描到（测试点为：写者应读到自己变更的最新数据）
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_014)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }
    // delete 10 条数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t num = 0; num < 10; num++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &num, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // qry vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = 0;
    int cnt = 10;
    for (uint32_t num = 0; num < insertNum; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        bool isNull = false;
        uint32_t pkValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &pkValue, sizeof(int32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_1_get = (char *)malloc(16);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 16);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(pkValue, *(int32_t *)sp_1_get);
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 4));
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 8));
        EXPECT_EQ(pkValue, *(int32_t *)(sp_1_get + 12));
        printf("fetch NUM:%d\n", num);
        free(sp_1_get);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(insertNum, cnt);
    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}
#if 0
// 015 GmcExecScanVertex之后不调用reset,调用GmcDropVertexLabel删除Label
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_015)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel=NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    
    // insert vertex   
    int insertNum = 100;
    for (uint32_t num=0; num<insertNum; num++)
    {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt,"superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    //scan open
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt,  NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}
#endif
// 016 不调用GmcExecScanVertex直接reset
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_016)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    GmcResetStmt(g_stmt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017 不GmcExecScanVertex直接调用GmcFetch
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_017)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int insertNum = 100;
    for (uint32_t num = 0; num < insertNum; num++) {
        char *sp_1 = (char *)malloc(16);
        *(int32_t *)sp_1 = num;
        *(int32_t *)(sp_1 + 4) = num;
        *(int32_t *)(sp_1 + 8) = num;
        *(int32_t *)(sp_1 + 12) = num;
        ret = GmcSetSuperfieldByName(g_stmt, "superfield0", sp_1, 16);
        EXPECT_EQ(GMERR_OK, ret);
        printf("insert NUM:%d\n", num);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(sp_1);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish1 = 0;
    ret = GmcFetch(g_stmt, &isFinish1);
    EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
        ret);  // 2022.01.11 由于前面没有调用GmcExecute 当前做报错处理 而不是返回查不到数据 用例适配
    // EXPECT_EQ(true, isFinish1);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018 不插入顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_018)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish1 = 0;
    ret = GmcFetch(g_stmt, &isFinish1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isFinish1);
    ret = GmcFetch(g_stmt, &isFinish1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isFinish1);
    GmcResetStmt(g_stmt);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.不插入顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_019)
{
    int ret = GmcCreateVertexLabel(g_stmt, scan_vertex_label_schema1, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    // test point: 获取vertex lable，并设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    uint32_t F0Value = 1;
    bool isFinish = 0;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *keyName = "DML_011_scanVertexTest_K0";
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isFinish);
    GmcResetStmt(g_stmt);
    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_label_name1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 020.插入顶点，更新顶点，全表扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_020)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i+count);
        // set_VertexProperty_F9(g_stmt, i+count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] update vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;

        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        int val_f7_f9 = cnt;
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val_f7_f9);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val_f7_f9;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.插入顶点，更新顶点，基于主键扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_021)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i+count);
        // set_VertexProperty_F9(g_stmt, i+count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] update vertex:%d\n", count);

    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        int Val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &Val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = Val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.插入顶点，删除顶点，全表扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_022)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] delete vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023.插入顶点，删除顶点，基于主键扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_023)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] delete vertex:%d\n", count);
    // scan vertex
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(0, cnt);
    }
    printf("[INFO] scan vertex:%d\n", 0);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024.merge顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_024)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // merge Vertex
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025.merge顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_025)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // merge Vertex
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 026.replace顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_026)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // replace Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 027.replace顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_027)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // replace Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 028.插入顶点，merge顶点，全表扫描到merge后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_028)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt, i + count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &cnt);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 029.插入顶点，merge顶点，基于主键扫到merge后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_029)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt, i + count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &i);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 030.插入顶点，replace顶点，全表扫描到replace后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_030)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i + count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &cnt);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 031.插入顶点，replace顶点，基于主键扫到replace后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_031)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i + count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &i);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 032.异步插入顶点后，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_032)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabelAsync = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] insert vertex:%d\n", count);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 033.异步插入顶点后，基于主键全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_033)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabelAsync = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] insert vertex:%d\n", count);

    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &i);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 034.插入顶点，异步更新顶点，全表扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_034)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabelAsync = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // update vertex
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        // set_VertexProperty_F9(g_stmt_async, i+count);
        set_VertexProperty(g_stmt_async, i + count);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] update vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        int val_f7_f9 = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val_f7_f9);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val_f7_f9;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 035.插入顶点，异步更新顶点，基于主键扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_035)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabelAsync = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // update vertex
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        // set_VertexProperty_F9(g_stmt_async, i+count);
        set_VertexProperty(g_stmt_async, i + count);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] update vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        int val_f7_f9 = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val_f7_f9, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 036.插入顶点，异步删除顶点，全表扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_036)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabelAsync = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // delete vertex
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] delete vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 037.插入顶点，异步删除顶点，基于主键扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_037)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabelAsync = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // delete vertex
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] delete vertex:%d\n", count);
    // scan vertex
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(0, cnt);
    }
    printf("[INFO] scan vertex:%d\n", 0);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 038.异步merge顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_038)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    // merge Vertex
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt_async, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 039.异步merge顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_039)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // merge Vertex
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt_async, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 040.异步replace顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_040)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // replace Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 041.异步replace顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_041)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // replace Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 042.插入顶点，异步merge顶点，全表扫描到merge后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_042)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // merge vertex
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt_async, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &cnt);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 043.插入顶点，异步merge顶点，基于主键扫到merge后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_043)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // merge vertex
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt_async, PKName_);
        EXPECT_EQ(GMERR_OK, ret);


        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] merge vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &i);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 044.插入顶点，异步replace顶点，全表扫描到replace后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_044)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // replace vertex
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &cnt);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 045.插入顶点，异步replace顶点，基于主键扫到replace后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_045)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // replace vertex
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expectAffectRows, data.affectRows);
    }
    printf("[INFO] replace vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &i);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 046.批量插入顶点后，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_046)
{
    int ret = 0;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_INSERT_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch insert vertex:%d\n", count);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 047.批量插入顶点后，主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_047)
{
    int ret = 0;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_INSERT_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch insert vertex:%d\n", count);

    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 048.插入顶点，批量更新顶点，全表扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_048)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i+count);
        // set_VertexProperty_F9(g_stmt, i+count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch update vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        // ret=queryPropertyAndCompare(g_stmt,"F7",GMC_DATATYPE_UINT32,&val);
        // EXPECT_EQ(GMERR_OK,ret);
        // int64_t F9Value=val;
        // ret=queryPropertyAndCompare(g_stmt,"F9",GMC_DATATYPE_INT64,&F9Value);
        // EXPECT_EQ(GMERR_OK,ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 049.插入顶点，批量更新顶点，基于主键扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_049)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i+count);
        // set_VertexProperty_F9(g_stmt, i+count);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch update vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        int Val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            // ret=queryPropertyAndCompare(g_stmt,"F7",GMC_DATATYPE_UINT32,&val);
            // EXPECT_EQ(GMERR_OK,ret);
            // int64_t F9Value=val;
            // ret=queryPropertyAndCompare(g_stmt,"F9",GMC_DATATYPE_INT64,&F9Value);
            // EXPECT_EQ(GMERR_OK,ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 050.插入顶点，批量删除顶点，全表扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_050)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(g_stmt,GMC_CMD_DELETE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch delete vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 051.插入顶点，批量删除顶点，基于主键扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_051)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_DELETE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch delete vertex:%d\n", count);
    // scan vertex
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(0, cnt);
    }
    printf("[INFO] scan vertex:%d\n", 0);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 052.批量merge顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_052)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_MERGE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch merge vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 053.批量merge顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_053)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_MERGE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch merge vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 054.批量replace顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_054)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch replace vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 055.批量replace顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_055)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    printf("[INFO] batch replace vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 056.异步批量插入顶点后，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_056)
{
    int ret = 0;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_INSERT_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch insert vertex:%d\n", count);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 057.异步批量插入顶点后，主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_057)
{
    int ret = 0;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_INSERT_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch insert vertex:%d\n", count);

    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 058.插入顶点，异步批量更新顶点，全表扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_058)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // batch update vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        // set_VertexProperty_F9(g_stmt_async, i+count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch update vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt + count;
        int Val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &Val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = Val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 059.插入顶点，异步批量更新顶点，基于主键扫描到更新后的顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_059)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // batch update vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        // set_VertexProperty_F9(g_stmt_async, i+count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch update vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i + count;
        int Val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &Val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            // ret=queryPropertyAndCompare(g_stmt,"F7",GMC_DATATYPE_UINT32,&val);
            // EXPECT_EQ(GMERR_OK,ret);
            // int64_t F9Value=val;
            // ret=queryPropertyAndCompare(g_stmt,"F9",GMC_DATATYPE_INT64,&F9Value);
            // EXPECT_EQ(GMERR_OK,ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 060.插入顶点，异步批量删除顶点，全表扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_060)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // batch delete vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch delete vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 061.插入顶点，异步批量删除顶点，基于主键扫描不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_061)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // batch delete vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // // 批量准备
    // ret = GmcBatchPrepare(g_conn_async, NULL, &batch);// 第二个参数设置为NULL 设置为batchOption 有其他变化
    // EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch delete vertex:%d\n", count);
    // scan vertex
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(0, cnt);
    }
    printf("[INFO] scan vertex:%d\n", 0);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 062.异步批量merge顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_062)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch merge Vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // // 批量准备
    // ret = GmcBatchPrepare(g_conn_async, NULL, &batch);// 第二个参数设置为NULL 设置为batchOption 有其他变化
    // EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt_async, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch merge vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 063.异步批量merge顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_063)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch merge Vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // // 批量准备
    // ret = GmcBatchPrepare(g_conn_async, NULL, &batch);// 第二个参数设置为NULL 设置为batchOption 有其他变化
    // EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {

        uint32_t f7_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "T39_K0";
        ret = GmcSetIndexKeyName(g_stmt_async, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch merge vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 064.异步批量replace顶点，全表扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_064)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch replace Vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // // 批量准备
    // ret = GmcBatchPrepare(g_conn_async, NULL, &batch);// 第二个参数设置为NULL 设置为batchOption 有其他变化
    // EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch replace vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 065.异步批量replace顶点，基于主键扫描
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_065)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    void *vertexLabelAsync = NULL;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch replace Vertex
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // // 批量准备
    // ret = GmcBatchPrepare(g_conn_async, NULL, &batch);// 第二个参数设置为NULL 设置为batchOption 有其他变化
    // EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt_async);  // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(g_stmt_async, true);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch replace vertex:%d\n", count);
    // scan vertex by pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 066.GmcScanOpen后不fetch顶点，调用GmcScanClose，然后删除vertexLabel
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_066)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(g_stmt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 067.开启事务插入数据，提交数据，全表扫描，预期扫到所有顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_067)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn, &config);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] insert vertex:%d\n", count);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(count, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 068.开启事务插入数据，构造覆盖写，触发事务回滚，全表扫描，预期扫不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_068)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[INFO] insert vertex:%d\n", count);
    set_VertexProperty_F7(g_stmt, 1);
    set_VertexProperty_F9(g_stmt, 1);
    set_VertexProperty(g_stmt, 1);
    ret = GmcExecute(g_stmt);
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 069.开启事务插入数据至资源耗尽，触发事务回滚，全表扫描，预期扫不到顶点
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_069)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    int ret = 0;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    int i = 0;
    do {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        i++;
        if (ret != GMERR_OK) {
            printf("i:%d,ret:%d\n", i, ret);
        }

    } while (ret == GMERR_OK);
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    printf("[INFO] scan cnt:%d\n", cnt);
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 070.开启事务插入数据，手动触发回滚，全表扫描，预期扫不到数据
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_070)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    ret = testGmcCreateVertexLabel(
        g_stmt, "schemaFile/NormalVertexLabel.gmjson", g_normal_vertexlabel_name, 0, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int val = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F9Value = val;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(0, cnt);
    printf("[INFO] scan cnt:%d\n", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 071.多主键联合扫描，扫描成功
TEST_F(DML_011_scanVertexTest, DML_011_scanVertexTest_071)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    const char *vertexLabelName = "MP1";
    const char *pkName = "PK";
    char *vertexLabelJson = NULL;
    readJanssonFile("schemaFile/MulitPKVertexLabel_1.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabelJson);
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // scan vertex by multi pk
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, vertexLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int val = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            query_VertexProperty(g_stmt, val);
            ret = queryPropertyAndCompare(g_stmt, "F7", GMC_DATATYPE_UINT32, &val);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t F9Value = val;
            ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        GmcResetStmt(g_stmt);
        EXPECT_EQ(1, cnt);
        printf("[INFO] scan vertex by pk:%d\n", i);
    }
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}
