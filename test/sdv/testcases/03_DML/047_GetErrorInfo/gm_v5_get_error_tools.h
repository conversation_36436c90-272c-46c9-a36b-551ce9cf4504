#ifndef _GM_V5_COMMON_H_
#define _GM_V5_COMMON_H_

#include "gm_v5_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

// 同步创建VertexLabel, 需注意label_name 必须是schema中的name一致
int func_create_vertex_label_sync(char *file_path, GmcStmtT *stmt, const char *g_configJson, char *label_name)
{
    int32_t ret = 0;
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 异步创建VertexLabel
int func_create_vertex_label_async(
    char *file_path, GmcStmtT *stmt, char *g_configJson, int32_t expect = 0)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    test_schema = NULL;
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabelAsync(stmt, test_schema, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect, data.status);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}
void *thread_create_label(void *arg)
{
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    AsyncUserDataT data = {0};
    data.lastError = (char*)"Duplicate table. Label is KV0.";
    const char *lastErrorStr = NULL;
    int i = *(int *)arg;
    TEST_INFO("Thread %d is start \n", i);
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(stmt_async, kv_table_name_sync, g_configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 获取表中记录数
int TestGmcGetVertexInterCount(GmcStmtT *stmt, char *label_name, int32_t expect)
{
    int ret = 0;
    void *label = NULL;
    bool isFinish = false;
    uint32_t scan_count = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    if (expect != UNEXPECT)
        EXPECT_EQ(expect, scan_count);
    TEST_INFO("Scan_count is %d.\n", scan_count);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}
int TestInsertVertexByJson(
    GmcStmtT *stmt, const char *jsonFile, int expect_count = 1, const char *labelName = label_name03)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json = NULL;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    COMPARE_NE(NULL, data_json);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expect_count);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expect_count);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
    return 0;
}

// localhash scan
int func_T39_all_type_superfield_read(GmcStmtT *stmt, int32_t field_value, int expect_count)
{
    int ret = 0;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &field_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(2);
    ret = GmcGetSuperfieldById(stmt, 0, sp_1_get, 2);
    EXPECT_EQ(expect_count, *(char *)sp_1_get);
    EXPECT_EQ(expect_count, *(unsigned char *)(sp_1_get + 1));
    free(sp_1_get);
    GmcFreeIndexKey(stmt);
    return ret;
}

// 同步写vertexLabel ip4forward
int test_insert_vertex_ip4forward(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        char wr_fixed[34] = "write";
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

int test_insert_vertex_ip4forward_big_object(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    int ret = 0;
    char *string_1024 = (char *)malloc(STRING_MAX_SIZE * (sizeof(char)));
    memset(string_1024, 'b', STRING_MAX_SIZE - 1);
    string_1024[STRING_MAX_SIZE - 1] = '\0';

    char *string_880 = (char *)malloc(836 * (sizeof(char)));
    memset(string_880, 'b', 836 - 1);
    string_880[836 - 1] = '\0';

    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        char wr_fixed[34] = "write";
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "test_str_01", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_02", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_03", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_04", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_05", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_06", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_07", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_08", GMC_DATATYPE_STRING, string_880, strlen(string_880));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    free(string_880);
    free(string_1024);
    return ret;
}

int test_insert_vertex_ip4forward_big_object_over_1B(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    int ret = 0;
    char *string_1024 = (char *)malloc(STRING_MAX_SIZE * (sizeof(char)));
    memset(string_1024, 'b', STRING_MAX_SIZE - 1);
    string_1024[STRING_MAX_SIZE - 1] = '\0';

    char *string_837 = (char *)malloc(837 * (sizeof(char)));
    memset(string_837, 'b', 837 - 1);
    string_837[837 - 1] = '\0';

    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {
        ((int *)(user_data->new_value))[loop] = loop;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        char wr_fixed[34] = "write";
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "test_str_01", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_02", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_03", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_04", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_05", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_06", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_07", GMC_DATATYPE_STRING, string_1024, strlen(string_1024));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str_08", GMC_DATATYPE_STRING, string_837, strlen(string_837));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(string_837);
    free(string_1024);
    return ret;
}

int TestGmcSetNodePropertyByName_PK(GmcNodeT *nodeField, char *fixed_value, int i)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F1", GMC_DATATYPE_FIXED, fixed_value, 5);
    EXPECT_EQ(GMERR_OK, ret);
    int16_t f2_value = i % 32768;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}
int TestGmcSetNodePropertyByName_R(GmcNodeT *nodeField, int i, char *fixed_value, bool bool_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 16;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    float f3_value = 9 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F3", GMC_DATATYPE_FLOAT, &f3_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f4_value = 10 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F4", GMC_DATATYPE_DOUBLE, &f4_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    bool f5_value = bool_value;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F5", GMC_DATATYPE_BOOL, &f5_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f6_value = 11 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F6", GMC_DATATYPE_TIME, &f6_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f7_value = value_u16;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F7", GMC_DATATYPE_UINT16, &f7_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f8_value = 2 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F8", GMC_DATATYPE_INT32, &f8_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f9_value = 3 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F9", GMC_DATATYPE_UINT32, &f9_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f10_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F10", GMC_DATATYPE_INT64, &f10_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F11", GMC_DATATYPE_UINT64, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f12_value = 2 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F12", GMC_DATATYPE_INT32, &f12_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f13_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F13", GMC_DATATYPE_INT64, &f13_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F14", GMC_DATATYPE_FIXED, fixed_value, 5);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F15", GMC_DATATYPE_BYTES, fixed_value, 6);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F16", GMC_DATATYPE_STRING, fixed_value, (strlen(fixed_value)));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f17_value = value_u8;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F17", GMC_DATATYPE_PARTITION, &f17_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int TestGmcSetNodePropertyByName_A(GmcNodeT *nodeField, int i, char *fixed_value, bool bool_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int8_t a1_value = value_8;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A1", GMC_DATATYPE_INT8, &a1_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t a2_value = value_u8;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A2", GMC_DATATYPE_UINT8, &a2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t a3_value = value_16;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A3", GMC_DATATYPE_INT16, &a3_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t a4_value = value_u16;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A4", GMC_DATATYPE_UINT16, &a4_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t a5_value = 2 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A5", GMC_DATATYPE_INT32, &a5_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t a6_value = 3 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A6", GMC_DATATYPE_UINT32, &a6_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t a7_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A7", GMC_DATATYPE_INT64, &a7_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t a8_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A8", GMC_DATATYPE_UINT64, &a8_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t a9_value = 2 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A9", GMC_DATATYPE_INT32, &a9_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t a10_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A10", GMC_DATATYPE_INT64, &a10_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    float a11_value = 9 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A11", GMC_DATATYPE_FLOAT, &a11_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double a12_value = 10 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A12", GMC_DATATYPE_DOUBLE, &a12_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    bool a13_value = bool_value;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A13", GMC_DATATYPE_BOOL, &a13_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t a14_value = 11 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A14", GMC_DATATYPE_TIME, &a14_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A15", GMC_DATATYPE_STRING, fixed_value, (strlen(fixed_value)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A16", GMC_DATATYPE_BYTES, fixed_value, 6);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"A17", GMC_DATATYPE_FIXED, fixed_value, 5);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}
int TestGmcSetNodePropertyByName_V(GmcNodeT *nodeField, int i, char *fixed_value, bool bool_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int8_t a1_value = value_8;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V1", GMC_DATATYPE_INT8, &a1_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t a2_value = value_u8;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V2", GMC_DATATYPE_UINT8, &a2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    int16_t a3_value = value_16;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V3", GMC_DATATYPE_INT16, &a3_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t a4_value = value_u16;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V4", GMC_DATATYPE_UINT16, &a4_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t a5_value = 2 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V5", GMC_DATATYPE_INT32, &a5_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t a6_value = 3 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V6", GMC_DATATYPE_UINT32, &a6_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t a7_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V7", GMC_DATATYPE_INT64, &a7_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t a8_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V8", GMC_DATATYPE_UINT64, &a8_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t a9_value = 2 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V9", GMC_DATATYPE_INT32, &a9_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int64_t a10_value = i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V10", GMC_DATATYPE_INT64, &a10_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);

    float a11_value = 9 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V11", GMC_DATATYPE_FLOAT, &a11_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double a12_value = 10 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V12", GMC_DATATYPE_DOUBLE, &a12_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    bool a13_value = bool_value;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V13", GMC_DATATYPE_BOOL, &a13_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t a14_value = 11 * i;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V14", GMC_DATATYPE_TIME, &a14_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V15", GMC_DATATYPE_FIXED, fixed_value, 5);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V16", GMC_DATATYPE_STRING, fixed_value, (strlen(fixed_value)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V17", GMC_DATATYPE_STRING, fixed_value, (strlen(fixed_value)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V18", GMC_DATATYPE_STRING, fixed_value, (strlen(fixed_value)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"V19", GMC_DATATYPE_STRING, fixed_value, (strlen(fixed_value)));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// 同步写 vertexLabel typical_schema / T0
int test_insert_vertex_typical_schema(GmcStmtT *stmt, int index, bool bool_value, char *fixed_value, int oper_begin,
    int oper_end, int array_num, int vector_num, const char *labelName)
{
    int ret = 0;
    void *label = NULL;

    // 插入顶点
    GmcNodeT *root, *T1, *T2, *T3;

    for (int i = oper_begin; i < oper_end; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestGmcSetNodePropertyByName_PK(root, fixed_value, i * index);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestGmcSetNodePropertyByName_R(root, i * index, fixed_value, bool_value);
        EXPECT_EQ(GMERR_OK, ret);

        // 插入array节点
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            int32_t p1_value = j;
            ret = GmcNodeSetPropertyByName(T1, (char *)"P1", GMC_DATATYPE_INT32, &p1_value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T1, "T2", &T2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < array_num; k++) {
                ret = TestGmcSetNodePropertyByName_A(T2, k, fixed_value, bool_value);
                EXPECT_EQ(GMERR_OK, ret);
                if (k < array_num - 1) {
                    ret = GmcNodeGetNextElement(T2, &T2);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1, &T1);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = TestGmcSetNodePropertyByName_V(T3, j, fixed_value, bool_value);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return 0;
}

int TestGmcSetNodePropertyByName_BigString(GmcNodeT *nodeField, char *big_string)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F18", GMC_DATATYPE_STRING, big_string, (strlen(big_string)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F19", GMC_DATATYPE_STRING, big_string, (strlen(big_string)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F20", GMC_DATATYPE_STRING, big_string, (strlen(big_string)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(nodeField, (char *)"F21", GMC_DATATYPE_STRING, big_string, (strlen(big_string)));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int test_insert_vertex_typical_schema_big_object(GmcStmtT *stmt, int index, bool bool_value, char *fixed_value,
    int oper_begin, int oper_end, int array_num, int vector_num, const char *labelName)
{
    int ret = 0;
    void *label = NULL;
    char *big_string = (char *)malloc(sizeof(char) * (1024));
    (void)memset(big_string, 0, sizeof(char) * (1024));
    (void)memset(big_string, 'a', sizeof(char) * (1023));

    // 插入顶点
    GmcNodeT *root, *T1, *T2, *T3;

    for (int i = oper_begin; i < oper_end; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestGmcSetNodePropertyByName_PK(root, fixed_value, i * index);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestGmcSetNodePropertyByName_R(root, i * index, fixed_value, bool_value);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestGmcSetNodePropertyByName_BigString(root, big_string);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            int32_t p1_value = j;
            ret = GmcNodeSetPropertyByName(T1, (char *)"P1", GMC_DATATYPE_INT32, &p1_value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T1, "T2", &T2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < array_num; k++) {
                ret = TestGmcSetNodePropertyByName_A(T2, k, fixed_value, bool_value);
                EXPECT_EQ(GMERR_OK, ret);
                if (k < array_num - 1) {
                    ret = GmcNodeGetNextElement(T2, &T2);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1, &T1);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = TestGmcSetNodePropertyByName_V(T3, j, fixed_value, bool_value);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(big_string);
    return 0;
}

int test_read_ip4forward_by_pk(GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype,
    unsigned short qos_profile_id_value = 1111, unsigned char mask_len_value = '1')
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;
            unsigned int rd_vr_id;
            ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(4, valueSize);
            EXPECT_EQ(loop, rd_vr_id);
            EXPECT_EQ(0, isNull);

            unsigned int rd_dest_ip_addr;
            ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(4, valueSize);
            EXPECT_EQ(loop, rd_dest_ip_addr);
            EXPECT_EQ(0, isNull);

            unsigned char rd_mask_len;
            ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, valueSize);
            EXPECT_EQ(mask_len_value, rd_mask_len);
            EXPECT_EQ(0, isNull);

            unsigned short rd_qos_profile_id;
            ret = GmcGetVertexPropertySizeByName(stmt, "qos_profile_id", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "qos_profile_id", &rd_qos_profile_id, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(2, valueSize);
            EXPECT_EQ(qos_profile_id_value, rd_qos_profile_id);
            EXPECT_EQ(0, isNull);

            uint32_t rd_primary_label;
            ret = GmcGetVertexPropertySizeByName(stmt, "primary_label", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "primary_label", &rd_primary_label, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(4, valueSize);
            EXPECT_EQ(loop, rd_primary_label);
            EXPECT_EQ(0, isNull);

            char rd_svc_ctx_high_prio[35] = {0};
            ret = GmcGetVertexPropertySizeByName(stmt, "svc_ctx_high_prio", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "svc_ctx_high_prio", &rd_svc_ctx_high_prio, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(34, valueSize);
            // EXPECT_EQ("write", rd_svc_ctx_high_prio);  // 此处转换存在问题, 需补充一下
            EXPECT_EQ(0, isNull);

            unsigned long long rd_app_obj_id;
            ret = GmcGetVertexPropertySizeByName(stmt, "app_obj_id", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "app_obj_id", &rd_app_obj_id, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(8, valueSize);
            EXPECT_EQ(11111111, rd_app_obj_id);
            EXPECT_EQ(0, isNull);

            char rd_test_str[50] = {0};
            ret = GmcGetVertexPropertySizeByName(stmt, "test_str", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "test_str", &rd_test_str, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(string_tmp) + 1, valueSize);
            EXPECT_STREQ(string_tmp, rd_test_str);
            EXPECT_EQ(0, isNull);
        }
    }
    return ret;
}

int test_read_typical_schema_by_pk(GmcConnT *conn, const char *keyName, int read_begin, int read_end, int thread_id)
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    char *fixed_value = (char *)"fixed";
    int16_t f2_value;
    GmcNodeT *root, *T1, *T2, *T3;
    bool isFinish = true;
    GmcStmtT *g_stmt_rd;
    ret = GmcAllocStmt(conn, &g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd, label_name06, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(g_stmt_rd, 0, GMC_DATATYPE_FIXED, fixed_value, 5);
        EXPECT_EQ(GMERR_OK, ret);
        f2_value = loop % 32768;
        ret = GmcSetIndexKeyValue(g_stmt_rd, 1, GMC_DATATYPE_INT16, &f2_value, sizeof(f2_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_rd, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_rd);
        if (ret != GMERR_OK) {
            GmcFreeIndexKey(g_stmt_rd);
            GmcFreeStmt(g_stmt_rd);
            return ret;
        }
        ret = GmcFetch(g_stmt_rd, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_rd, &root);
        EXPECT_EQ(GMERR_OK, ret);

        float f3_value;
        ret = GmcNodeGetPropertyByName(root, "F3", &f3_value, sizeof(float), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(9 * loop, f3_value);
        EXPECT_FALSE(isNull);

        uint64_t f6_value;
        ret = GmcNodeGetPropertyByName(root, "F6", &f6_value, sizeof(uint64_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(11 * loop, f6_value);
        EXPECT_FALSE(isNull);

        uint32_t f9_value;
        ret = GmcNodeGetPropertyByName(root, "F9", &f9_value, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(3 * loop, f9_value);
        EXPECT_FALSE(isNull);
        GmcFreeIndexKey(g_stmt_rd);
    }
    ret = GmcResetVertex(g_stmt_rd, false);
    GmcFreeStmt(g_stmt_rd);  // stmt申请了 也要注意释放, 是从连接那里alloc
    return ret;
}

// hashcluster索引 ip4forward 表 扫描
int func_ip4forward_hashcluster_scan(GmcConnT *conn, int expect_count, unsigned short qos_profile_id)
{
    int ret = 0;
    unsigned int scan_count = 0;
    unsigned int isNull;
    bool isFinish = false;
    GmcStmtT *g_stmt_rd;
    ret = GmcAllocStmt(conn, &g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd, label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_rd, 0, GMC_DATATYPE_UINT16, &qos_profile_id, sizeof(qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_rd, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    while (isFinish == false) {
        ret = GmcFetch(g_stmt_rd, &isFinish);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", scan_count, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    ret = GmcResetVertex(g_stmt_rd, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt_rd);
    GmcFreeStmt(g_stmt_rd);
    return ret;
}

int func_hashcluster_ip4forward_update(GmcConnT *conn, int expect_count, bool is_batch = 0)
{
    int ret = 0, affectRows;
    unsigned int isNull;
    unsigned short up_qos_profile_id = 2222;
    char up_fixed[36] = "update";
    unsigned char up_uint8 = '2';
    unsigned int len;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcStmtT *g_stmt_rd;

    ret = GmcAllocStmt(conn, &g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd, label_name02, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    if (is_batch == 1) {
        ret = GmcBatchPrepare(conn, NULL, &batch);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcSetIndexKeyValue(g_stmt_rd, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt_rd, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_rd, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        g_stmt_rd, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_rd, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    if (is_batch == 1) {
        ret = GmcSetIndexKeyName(g_stmt_rd, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_rd);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(1, totalNum);
        EXPECT_EQ(1, successNum);
        GmcBatchDestroy(batch);
    } else {
        ret = GmcSetIndexKeyName(g_stmt_rd, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_rd);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // get affect row
    ret = GmcGetStmtAttr(g_stmt_rd, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect_count, affectRows);
    ret = GmcResetVertex(g_stmt_rd, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt_rd);
    GmcFreeStmt(g_stmt_rd);

    return ret;
}

// 异步 hashcluster 索引 ip4forward 表更新
int func_hashcluster_ip4forward_update_async(GmcStmtT *stmt, int expect_count, bool is_batch = 0)
{
    int ret = 0, affectRows;
    unsigned int valueSize;
    unsigned int isNull;
    unsigned short up_qos_profile_id = 2222;
    char up_fixed[36] = "update";
    unsigned char up_uint8 = '2';
    unsigned int len;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    AsyncUserDataT data = {0};

    void *vtxLabel_async = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, (char *)"hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmt, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(expect_count, data.affectRows);
    return ret;
}

// 同步 hashcluster 索引 ip4forward 表删除
int func_hashcluster_ip4forward_del(
    GmcConnT *conn, int expect_count, bool is_batch = 0, unsigned short qos_profile_id = 1111)
{
    int ret = 0, affectRows;
    unsigned int valueSize;
    unsigned int isNull;
    char up_fixed[36] = "update";
    unsigned char up_uint8 = '2';
    unsigned int len;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcStmtT *stmt;

    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    if (is_batch == 1) {
        ret = GmcBatchPrepare(conn, NULL, &batch);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &qos_profile_id, sizeof(qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    if (is_batch == 1) {
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(1, totalNum);
        EXPECT_EQ(1, successNum);
        GmcBatchDestroy(batch);
    } else {
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect_count, affectRows);

    ret = GmcResetVertex(stmt, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(stmt);
    GmcFreeStmt(stmt);
    return ret;
}

// 异步 hashcluster 索引 ip4forward 表 删除
int func_hashcluster_ip4forward_del_async(GmcStmtT *stmt, int expect_count, bool is_batch = 0)
{
    int ret = 0, affectRows;
    unsigned int valueSize;
    unsigned int isNull;
    unsigned int len;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    AsyncUserDataT data = {0};
    void *vtxLabel_async = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, (char *)"hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &data;

    ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(expect_count, data.affectRows);

    return ret;
}

// localkey ip4forward 区间范围删除
int func_localkey_ip4forward_range_del(
    GmcConnT *conn, unsigned int start_id, unsigned int end_id, bool is_batch = 0)
{
    int ret = 0, affectRows;
    unsigned short same_val = 2222;
    unsigned int arrLen = 2;
    unsigned int len;
    GmcStmtT *g_stmt_rd;
    ret = GmcAllocStmt(conn, &g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd, label_name02, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &start_id;
    leftKeyProps_del[0].size = sizeof(start_id);
    leftKeyProps_del[1].type = GMC_DATATYPE_UINT16;
    leftKeyProps_del[1].value = &same_val;
    leftKeyProps_del[1].size = sizeof(same_val);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &end_id;
    rightKeyProps_del[0].size = sizeof(end_id);
    rightKeyProps_del[1].type = GMC_DATATYPE_UINT16;
    rightKeyProps_del[1].value = &same_val;
    rightKeyProps_del[1].size = sizeof(same_val);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps_del[1];
    items[1].rValue = &rightKeyProps_del[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    ret = GmcSetKeyRange(g_stmt_rd, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetRangeScanParams(g_stmt_rd, GMC_RANGE_TYPE_CLOSED, GMC_RANGE_TYPE_CLOSED, GMC_ORDER_ASC);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_rd, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt_rd, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_id - start_id+1, affectRows);

    ret = GmcResetVertex(g_stmt_rd, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt_rd);
    GmcFreeStmt(g_stmt_rd);
    free(leftKeyProps_del);
    free(rightKeyProps_del);
    return ret;
}

// localkey ip4forward 区间范围扫描
int func_localkey_ip4forward_range_scan(
    GmcConnT *conn, unsigned int start_id, unsigned int end_id, unsigned int expect_scan_count)
{
    int ret = 0, affectRows;
    unsigned short same_val = 2222;
    unsigned int arrLen = 2;
    unsigned int len;
    bool isFinish = false;
    unsigned int scan_count = 0;
    GmcStmtT *g_stmt_rd;
    ret = GmcAllocStmt(conn, &g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd, label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &start_id;
    leftKeyProps_del[0].size = sizeof(start_id);
    leftKeyProps_del[1].type = GMC_DATATYPE_UINT16;
    leftKeyProps_del[1].value = &same_val;
    leftKeyProps_del[1].size = sizeof(same_val);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &end_id;
    rightKeyProps_del[0].size = sizeof(end_id);
    rightKeyProps_del[1].type = GMC_DATATYPE_UINT16;
    rightKeyProps_del[1].value = &same_val;
    rightKeyProps_del[1].size = sizeof(same_val);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps_del[1];
    items[1].rValue = &rightKeyProps_del[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    ret = GmcSetKeyRange(g_stmt_rd, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetRangeScanParams(g_stmt_rd, GMC_RANGE_TYPE_CLOSED, GMC_RANGE_TYPE_CLOSED, GMC_ORDER_ASC);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_rd, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    while (isFinish == false) {
        ret = GmcFetch(g_stmt_rd, &isFinish);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", scan_count, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_scan_count, scan_count);
    GmcFreeIndexKey(g_stmt_rd);
    GmcFreeStmt(g_stmt_rd);
    free(leftKeyProps_del);
    free(rightKeyProps_del);
    return ret;
}

// ip4forward 表 localhash 索引扫描
int func_loacalhash_scan_ip4forward(GmcConnT *conn, int expect_count)
{
    int ret = 0;
    unsigned int count_record = 0;
    unsigned short up_qos_profile_id = 2222;
    bool isFinish = false;
    GmcStmtT *g_stmt_rd = NULL;
    ret = GmcAllocStmt(conn, &g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd, label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(
        g_stmt_rd, 0, GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_rd, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        ret = GmcFetch(g_stmt_rd, &isFinish);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", count_record, ret);
            break;
        }
        count_record++;
    }
    EXPECT_EQ(count_record, expect_count);
    ret = GmcResetVertex(g_stmt_rd, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt_rd);
    GmcFreeStmt(g_stmt_rd);
    return ret;
}

// create sub relation func
int func_create_sub_relation(GmcStmtT *stmt, GmcConnT *sn_conn, char *file_path, GmcSubCallbackT callback,
    SnUserDataT *user_data, const char *sub_name)
{
    int ret = 0;
    pthread_mutex_lock(&LockSubChannel);
    char *sub_info = NULL;
    readJanssonFile(file_path, &sub_info);
    COMPARE_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = sub_name;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, sn_conn, callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

/************* SN 条件订阅 *************/
int32_t check_value_sn(GmcStmtT *sub_stmt, const char *fieldName, GmcDataTypeE datatype, void *expectValue)
{
    int ret = 0;
    bool isNull;
    unsigned int valueSize;
    switch (datatype) {
        case GMC_DATATYPE_UINT32:
            unsigned int rd_uint32;
            ret = GmcGetVertexPropertySizeByName(sub_stmt, fieldName, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(sub_stmt, fieldName, &rd_uint32, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(uint32_t *)expectValue, rd_uint32);
            EXPECT_EQ(4, valueSize);
            break;
        case GMC_DATATYPE_CHAR:
            char rd_int8;
            ret = GmcGetVertexPropertySizeByName(sub_stmt, fieldName, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(sub_stmt, fieldName, &rd_int8, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(char *)expectValue, rd_int8);
            EXPECT_EQ(1, valueSize);
            break;
        case GMC_DATATYPE_UINT16:
            unsigned short rd_uint16;
            ret = GmcGetVertexPropertySizeByName(sub_stmt, fieldName, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(sub_stmt, fieldName, &rd_uint16, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            // EXPECT_EQ(*(unsigned short *)expectValue, rd_uint16); // 此处放开更新那里触发core 用例问题
            EXPECT_EQ(2, valueSize);
            break;
        case GMC_DATATYPE_UINT8:
            unsigned char rd_uint8;
            ret = GmcGetVertexPropertySizeByName(sub_stmt, fieldName, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(sub_stmt, fieldName, &rd_uint8, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned char *)expectValue, rd_uint8);
            EXPECT_EQ(1, valueSize);
            break;
        case GMC_DATATYPE_UINT64:
            unsigned long long rd_uint64;
            ret = GmcGetVertexPropertySizeByName(sub_stmt, fieldName, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(sub_stmt, fieldName, &rd_uint64, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(*(unsigned long long *)expectValue, rd_uint64);
            EXPECT_EQ(8, valueSize);
            break;
        case GMC_DATATYPE_FIXED:
            char rd_fixed[37] = {};
            ret = GmcGetVertexPropertySizeByName(sub_stmt, fieldName, &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(sub_stmt, fieldName, rd_fixed, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ((char *)expectValue, rd_fixed);
            EXPECT_EQ(34, valueSize);
            break;

            // case GMC_DATATYPE_UCHAR :
    }
    return 0;
}

void *thread_nonUniHashUpdate_test(void *args)
{
    int conn_id = *((int *)args);
    int oper_nums = 10;
    int ret = 0;
    unsigned short up_qos_profile_id = 8888;
    unsigned int len;

    int res = testGmcConnect(&g_conn_thread[conn_id], &g_stmt_rd[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd[conn_id], label_name02, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // write
    int oper_begin = conn_id * oper_nums;
    int oper_end = oper_nums + oper_begin;
    pthread_mutex_lock(&LockSubChannel);
    test_insert_vertex_ip4forward(g_stmt_rd[conn_id], oper_begin, oper_end);
    // hashcluste update
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd[conn_id], label_name02, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd[conn_id], 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd[conn_id], 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        g_stmt_rd[conn_id], "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_rd[conn_id], "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt_rd[conn_id], GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows[conn_id], sizeof(int));
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("[ INFO ][ THREAD_%d ] non-uniq hash update affect row is %d \n", conn_id, g_affectRows[conn_id]);

    // pk read after hash update
    ret = test_read_ip4forward_by_pk(
        g_stmt_rd[conn_id], "primary_key", oper_begin, oper_end, GMC_DATATYPE_UINT32, 8888);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeIndexKey(g_stmt_rd[conn_id]);
    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_thread[conn_id], g_stmt_rd[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&LockSubChannel);
    return ((void *)0);
}

int query_VertexProperty(GmcStmtT *stmt, int i)
{
    // Get F0
    int ret = 0;
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F9
    int64_t F9Value = i;
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    if (g_runMode) {  //欧拉和hpe环境有8h时差
        F13Value += 28800;
    }
    if (g_envType == 0) {
        ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    return ret;
}

int func_T39_all_type_pk_scan(GmcStmtT *stmt, uint32_t field_value, int expect_count)
{
    int ret = 0;
    unsigned int scan_count = 0;
    unsigned int isNull;
    bool isFinish = false;
    int64_t sk = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &field_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = query_VertexProperty(stmt, field_value);
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    // GmcFreeStmt(stmt);
    return ret;
}

// 全表扫描
int func_T39_all_type_all_scan(GmcStmtT *stmt, uint32_t field_value, int expect_count)
{
    int ret = 0;
    unsigned int scan_count = 0;
    unsigned int isNull;
    bool isFinish = false;
    int64_t sk = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = query_VertexProperty(stmt, field_value);
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    return ret;
}

// localhash scan
int func_T39_all_type_localhash_scan(GmcStmtT *stmt, int64_t field_value, int expect_count)
{
    int ret = 0;
    unsigned int scan_count = 0;
    unsigned int isNull;
    bool isFinish = false;
    int64_t sk = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &field_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_hash");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = query_VertexProperty(stmt, (int)sk);
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    return ret;
}

// filter scan
int T39_all_type_filter_query(GmcStmtT *stmt, const char *filter_content, int expect_count)
{
    int ret = 0;
    unsigned int scan_count = 0;
    unsigned int isNull;
    bool isFinish = false;
    int64_t sk = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetFilter(stmt, filter_content);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = query_VertexProperty(stmt, sk);
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    return ret;
}

void test_checkVertexProperty_sub(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = index;
    uint8_t value3 = index;
    int16_t value4 = index;
    uint16_t value5 = index;
    int32_t value6 = index;
    uint32_t value7 = index;  // F7是pk
    bool value8 = false;
    int64_t value9 = index;
    uint64_t value10 = index;
    float value11 = (float)1.2 + (float)index;
    double value12 = 10.86 + index;
    uint64_t value13 = index;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    uint32_t value17 = index;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value7);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_setVertexPK(GmcStmtT *stmt, int index)
{
    int ret;
    uint32_t value7 = index;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_setVertexProperty(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = index;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = index;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = index;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = index;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)index;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + index;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = index;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = index;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = index;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = index;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = index;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

#ifdef __cplusplus
}
#endif
#endif
