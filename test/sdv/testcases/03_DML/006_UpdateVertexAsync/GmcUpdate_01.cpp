/*****************************************************************************
 Description  : 异步更新基本功能验证
 Notes        : DML_006_005 设置完属性后删除label，更新图顶点
                DML_006_006 更新的stmt未设置label属性值
                DML_006_007 更新主键值
                DML_006_008 filter指定的主键属性值不正确
                DML_006_009 filter指定的主键属性类型不正确
                DML_006_010 keyName为非主键
                DML_006_011 设置的属性值的顺序与schema中定义的不一致
                DML_006_012 重复使用同一个stmt更新，主键相同，其余属性值相同
                DML_006_013 重复使用同一个stmt更新，主键相同，其余属性值不同
                DML_006_014 重复使用同一个stmt更新，主键不同
                DML_006_015 使用不同的stmt更新，主键相同，其余属性值相同
                DML_006_016 使用不同的stmt更新，主键相同，其余属性值不同
                DML_006_017 使用不同的stmt更新，主键不同
 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2020/9/23
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "../005_InsertVertexAsync/dml_tools.h"

char g_label_name[] = "T30";
char g_lable_PK[] = "T30_PK";

using namespace std;

class GmcUpdateVertexByPrimFilterAsync_func_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmcUpdateVertexByPrimFilterAsync_func_01::SetUp()
{
    printf("GmcUpdateVertexByPrimFilterAsync_func_01 Start.\n");

    uint32_t value0 = 100;
    char teststr1[] = "string";
    char teststr2[10] = "bytes";
    char teststr3[6] = "fixed";
    uint64_t value4 = 1000;
    AsyncUserDataT asyncData = {0};

    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_schema_2 = NULL;
    g_label_2 = NULL;

    test_prepare_connect();
    readJanssonFile("schema_file/bytes_fixed_schema.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    
    ret = GmcDropVertexLabelAsync(g_stmt_async, g_label_name, drop_vertex_label_callback, &asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    test_prepare_create_label_async(g_stmt_async, g_label_name, g_schema);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    value0 = value0 + 1;
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    
    asyncData = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------insert vertex------------\r\n");
    AW_CHECK_LOG_BEGIN();
}
void GmcUpdateVertexByPrimFilterAsync_func_01::TearDown()
{
    AW_CHECK_LOG_END();
    test_close_and_drop_label_async(g_stmt_async, g_label, g_label_name);
    test_tearDown_disconnect();
    printf("GmcUpdateVertexByPrimFilterAsync_func_01 End.\n");
}

//设置完属性后删除label，更新图顶点
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_005_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    // Drop 顶点，然后再删除图顶点
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
}

//更新的stmt未设置label属性值
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_006_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    uint32_t value0 = 100;
    char teststr1[] = "string";
    char teststr2[10] = "bytes";
    char teststr3[6] = "fixed";
    uint64_t value4 = 1000;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex------------\r\n");

    void *label_sync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);
}

//更新主键值存在setkey和value时会优先使用key和value进行更新否则做全表更新
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_007_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    for (int i = 0; i < 10; i++) {
        printf("[INFO] i = %d\r\n", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value01 = 100 + i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t value0 = 101 + i;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);  // 当前update不再支持使用GmcSetVertexProperty设置主键值
        char teststr1[] = "stringnew";
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
        EXPECT_EQ(GMERR_OK, ret);
        char teststr2[10] = "bytesnew";
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
        EXPECT_EQ(GMERR_OK, ret);
        char teststr3[6] = "fxnew";
        EXPECT_EQ(6, strlen(teststr3) + 1);
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t value4 = 2000;
        ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        printf("[INFO] 100 + i = %d\r\n", value01);
        printf("[INFO] 101 + i = %d\r\n", value0);
        AsyncUserDataT asyncData = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        int expectAffectRows = 0;
        if (i == 0) {
            expectAffectRows = 1;  // 存在同主键数据所以能成功
        }
        EXPECT_EQ(expectAffectRows, asyncData.affectRows);  // update一个不存在的主键返回ok,affectrows预期为0
        ret = testGmcGetLastError();
        EXPECT_EQ(GMERR_OK, ret);
        printf("------------update vertex------------\r\n");
    }
}

// filter指定的主键属性值不正确
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_008_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    uint32_t value0 = 100;
    uint32_t invalid_value0 = 1000;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &invalid_value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr1[] = "stringnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr2[10] = "bytesnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[6] = "fxnew";
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value4 = 2000;
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(0, asyncData.affectRows);
    printf("------------update vertex------------\r\n");
}

// filter指定的主键属性类型不正确
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_009_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    uint32_t value0 = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr1[] = "stringnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr2[10] = "bytesnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[6] = "fxnew";
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value4 = 2000;
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------update vertex------------\r\n");

    uint32_t value0old = 100;
    char teststr1old[] = "string";
    char teststr2old[10] = "bytes";
    char teststr3old[6] = "fixed";
    uint64_t value4old = 1000;
    void *label_sync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0old, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0old);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1old);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2old);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3old);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4old);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(g_stmt_sync);
    printf("------------query vertex------------\r\n");
}

// keyName为非主键
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_010_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value0 = 100;
    char invalid_lable_PK[] = "T20_PK";
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr1[] = "stringnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr2[10] = "bytesnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[6] = "fxnew";
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value4 = 2000;
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, invalid_lable_PK);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

//设置的属性值的顺序与schema中定义的不一致
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_011_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value0 = 100;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t value4 = 2000;
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr1[] = "stringnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[6] = "fxnew";
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr2[10] = "bytesnew";
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex------------\r\n");

    void *label_sync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);
}

//重复使用同一个stmt更新，主键相同，其余属性值相同
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_012_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    int i;
    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    char teststr2[10] = "bytesnew";
    char teststr3[6] = "fxnew";
    uint64_t value4 = 2000;
    void *label_sync = NULL;

    for (i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(6, strlen(teststr3) + 1);
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        AsyncUserDataT asyncData = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
        printf("------------update vertex: %d------------\r\n", i);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt_sync, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
            EXPECT_EQ(GMERR_OK, ret);
        }
        GmcFreeIndexKey(g_stmt_sync);
        printf("------------query vertex: %d------------\r\n", i);
    }
}

//重复使用同一个stmt更新，主键相同，其余属性值不同
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_013_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    int i;
    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    char teststr2[10] = "bytesnew";
    char teststr3[6] = "fxnew";
    uint64_t value4 = 2000;
    void *label_sync = NULL;

    for (i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(6, strlen(teststr3) + 1);
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        AsyncUserDataT asyncData = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
        printf("------------update vertex: %d------------\r\n", i);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt_sync, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
            EXPECT_EQ(GMERR_OK, ret);
        }
        GmcFreeIndexKey(g_stmt_sync);
        printf("------------query vertex: %d------------\r\n", i);

        value4++;
    }
}

//重复使用同一个stmt更新，主键不同
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_014_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    int i;
    uint32_t value01 = 300;
    char teststr11[] = "string";
    char teststr21[10] = "bytes";
    char teststr31[6] = "fixed";
    uint64_t value41 = 3000;

    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    char teststr2[10] = "bytesnew";
    char teststr3[6] = "fxnew";
    uint64_t value4 = 2000;

    void *label_sync = NULL;

    //插入第二个记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr11, (strlen(teststr11)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr21, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr31) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr31, (strlen(teststr31)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value41, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT asyncData = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------insert vertex： 2------------\r\n");

    //更新第一个记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 1------------\r\n");

    //查询第一条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(g_stmt_sync);
    printf("------------query vertex: 1------------\r\n");

    //更新第二个记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 2------------\r\n");

    //查询第二条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value01);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr11);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr21);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr31);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value41);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcFreeIndexKey(g_stmt_sync);
    printf("------------query vertex: 2------------\r\n");
}

//使用不同的stmt更新，主键相同，其余属性值相同
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_015_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    char teststr2[10] = "bytesnew";
    char teststr3[6] = "fxnew";
    uint64_t value4 = 2000;
    void *label_sync = 0, *label = 0;

    //第二个异步连接
    ret = testGmcConnect(&g_conn_async_2, &g_stmt_async_2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 1------------\r\n");

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex: 1------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async_2, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async_2, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_async_2, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async_2, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 2------------\r\n");

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex: 2------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);
    ret = testGmcDisconnect(g_conn_async_2, g_stmt_async_2);
    EXPECT_EQ(GMERR_OK, ret);
}

//使用不同的stmt更新，主键相同，其余属性值不同
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_016_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    char teststr2[10] = "bytesnew";
    char teststr3[6] = "fxnew";
    uint64_t value4 = 2000;
    void *label = 0, *label_sync = 0;

    //第二个异步连接
    ret = testGmcConnect(&g_conn_async_2, &g_stmt_async_2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT asyncData = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 1------------\r\n");

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex: 1------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async_2, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async_2, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    value4++;
    ret = GmcSetVertexProperty(g_stmt_async_2, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async_2, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async_2, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 2------------\r\n");

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex: 2------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);
    ret = testGmcDisconnect(g_conn_async_2, g_stmt_async_2);
    EXPECT_EQ(GMERR_OK, ret);
}

//使用不同的stmt更新，主键不同
TEST_F(GmcUpdateVertexByPrimFilterAsync_func_01, DML_006_017_GmcUpdateVertexByPrimFilterAsync_func_01)
{
    GmcStmtT *stmt = NULL;
    uint32_t value01 = 300;
    char teststr11[] = "string";
    char teststr21[10] = "bytes";
    char teststr31[6] = "fixed";
    uint64_t value41 = 3000;

    uint32_t value0 = 100;
    char teststr1[] = "stringnew";
    char teststr2[10] = "bytesnew";
    char teststr3[6] = "fxnew";
    uint64_t value4 = 2000;
    void *label_sync = 0, *label = 0;

    //第二个异步连接
    ret = testGmcConnect(&g_conn_async_2, &g_stmt_async_2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_2, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //插入第二个记录
    ret = GmcSetVertexProperty(g_stmt_async_2, "F0", GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F1", GMC_DATATYPE_STRING, teststr11, (strlen(teststr11)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F2", GMC_DATATYPE_BYTES, teststr21, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr31) + 1);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F3", GMC_DATATYPE_FIXED, teststr31, (strlen(teststr31)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F4", GMC_DATATYPE_TIME, &value41, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT asyncData = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async_2, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------insert vertex------------\r\n");

    //更新第一条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 1------------\r\n");

    //查询第一条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex: 1------------\r\n");
    GmcFreeIndexKey(g_stmt_sync);

    //更新第二条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_2, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async_2, 0, GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F2", GMC_DATATYPE_BYTES, teststr2, 10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, strlen(teststr3) + 1);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F3", GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async_2, "F4", GMC_DATATYPE_TIME, &value4, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_async_2, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async_2, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    printf("------------update vertex: 2------------\r\n");

    //查询第二条记录
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value01, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value01);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_STRING, teststr1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_BYTES, teststr2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_FIXED, teststr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F4", GMC_DATATYPE_TIME, &value4);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("------------query vertex: 2------------\r\n");

    GmcFreeIndexKey(g_stmt_sync);
    ret = testGmcDisconnect(g_conn_async_2, g_stmt_async_2);
    EXPECT_EQ(GMERR_OK, ret);
}
