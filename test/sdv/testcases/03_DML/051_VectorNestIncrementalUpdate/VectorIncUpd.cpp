#include "VectorNestIncUpdate_test.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;

class VectorNestIncUpdate_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void VectorNestIncUpdate_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void VectorNestIncUpdate_test::TearDownTestCase()
{
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void VectorNestIncUpdate_test::SetUp()
{
    // 定义MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    // 封装创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    testCreateLabelMS(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void VectorNestIncUpdate_test::TearDown()
{
    AW_CHECK_LOG_END();
    testDropLabelMS(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
}

/*****************************************************************************
 Description  : 001.Tree模型Vector嵌套，插入数据，增量更新update嵌套内层vector节点的数据，
                查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_001)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        // 未update的数据值不变
        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 002.Tree模型Vector嵌套，插入数据，启动事务，增量更新update嵌套内层vector节点的数据，
                事务commit，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_002)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        // 未update的数据值不变
        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 003.Tree模型Vector嵌套，插入数据，启动事务，增量更新update嵌套内层vector节点的数据，
                事务rollback，查询数据为update前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_003)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 004.Tree模型Vector嵌套，插入数据，启动事务，增量更新update嵌套内层vector节点的数据，
                构造更新失败，事务rollback，查询数据为update前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_004)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        // uint32_t f0_value = i;
        // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, "MS_Tree_Vector_01.F0 = \"aaa\"");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);
        TestGmcNodeSetPropertyByName_V_Fail(V1_V2);

        // ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_INVALID_PROPERTY);

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 005.Tree模型Vector嵌套，插入数据，增量更新remove嵌套内层vector节点的数据，
                查询数据为remove后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_005)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, size);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 再次更新node，删除vector中剩余数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, size);
    }
}

/*****************************************************************************
 Description  : 006.Tree模型Vector嵌套，插入数据，启动事务，增量更新remove嵌套内层vector节点的数据，
                事务commit，查询数据为remove后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_006)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, size);
    }
}

/*****************************************************************************
 Description  : 007.Tree模型Vector嵌套，插入数据，启动事务，增量更新remove嵌套内层vector节点的数据，
                事务rollback，查询数据为remove前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_007)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 008.Tree模型Vector嵌套，插入数据，启动事务，增量更新remove嵌套内层vector节点的数据，
                设置index为不存在的index值，更新失败，由于是DM失败，不会触发回滚，
                事务rollback，数据为remove前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_008)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 5;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    }
    AddWhiteList(GMERR_DATA_EXCEPTION);

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 009.Tree模型Vector嵌套，插入数据，增量更新append嵌套内层vector节点的数据，
                查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_009)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 010.Tree模型Vector嵌套，插入数据，启动事务，增量更新append嵌套内层vector节点的数据，
                事务commit，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_010)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 011.Tree模型Vector嵌套，插入数据，启动事务，增量更新append嵌套内层vector节点的数据，
                事务rollback，查询数据为append前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_011)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 012.Tree模型Vector嵌套，插入数据，启动事务，增量更新append嵌套内层vector节点的数据，
                构造更新失败，事务rollback，查询数据为append前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_012)
{
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    ASSERT_EQ(GMERR_OK, ret);

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;
    char *MS_Tree_Vector_32_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        // uint32_t f0_value = i;
        // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, "MS_Tree_Vector_01.F0 = \"aaa\"");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);
        TestGmcNodeSetPropertyByName_V_Fail(V1_V2);

        // ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_INVALID_PROPERTY);

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 013.Tree模型Vector嵌套，插入数据，GmcClearNode清空（走replace流程）嵌套内层vector的数据，
                查询数据已清空，增量更新append和清空前同样的数据，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_013)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num,
    MS_Tree_Vector_01_Name);

    // GmcNodeClear 清空vector数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i = start_num; i < end_num; i++)
    {
        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeClear(V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 更新
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    for(int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 014.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                remove部分数据，append部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_014)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 015.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                带superfield，remove部分数据，append部分数据，带superfield，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_015)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertexSuperfield(
        g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_SF(V1_V2, newValue, "superfield2");

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_SF(V1_V2, newValue, "superfield2");

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_SF(V1_V2, getValue, (char *)"superfield2");

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_SF(V1_V2, getValue, (char *)"superfield2");

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_SF(V1_V2, getValue, (char *)"superfield2");

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 016.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                remove部分数据，append部分数据，批量更新，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_016)
{
    int start_num = 0;
    int end_num = 10;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertexBatch(
        g_conn, g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt, true);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 017.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                remove部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_017)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 2;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 018.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                append部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_018)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 3, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 4;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 019.Tree模型Vector嵌套，插入数据，按照顺序增量更新remove嵌套内层数据，
                append部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_019)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 4;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 020.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                remove部分数据，append部分数据，全表扫描数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_020)
{
    int start_num = 0;
    int end_num = 3;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;
    GmcNodeT *R1 = NULL, *R1_A1 = NULL, *R1_V4 = NULL, *R1_V4_V5 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* ******** 全表扫描 ******** */
    int isPrint = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int scan_num = 0;
    for (uint32_t num = 0; num < end_num - start_num; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if ((isFinish == true) || (ret != GMERR_OK)) {
            break;
        }
        // 获取 根节点、普通节点 字段值
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(root, num, false, f8_value, isPrint);

        // 设置R1节点
        ret = GmcNodeGetChild(root, "R1", &R1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(R1, num, false, f8_value, isPrint);

        // 获取 array 节点 字段值
        ret = GmcNodeGetChild(R1, "A1", &R1_A1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(R1_A1, j, &R1_A1);
            EXPECT_EQ(GMERR_OK, ret);
            printf("\n[INFO] scan full vertexLabel, \"R1.A1\" array_node[%d] \n", j);
            TestGmcNodeGetPropertyByName(R1_A1, num, false, f8_value, isPrint);
        }

        // 读取vector节点V1
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        int getValue = num + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        printf("\n[INFO] scan full vertexLabel, \"V1.V2\" Vector_node[0] \n");
        TestGmcNodeGetPropertyByName(V1_V2, getValue, false, f8_value, isPrint);

        getValue = num;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        printf("\n[INFO] scan full vertexLabel, \"V1.V2\" Vector_node[1] \n");
        TestGmcNodeGetPropertyByName(V1_V2, getValue, false, f8_value, isPrint);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        printf("\n[INFO] scan full vertexLabel, \"V1.V2\" Vector_node[2] \n");
        TestGmcNodeGetPropertyByName(V1_V2, getValue, false, f8_value, isPrint);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
        scan_num++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(end_num - start_num, scan_num);
}

/*****************************************************************************
 Description  : 021.Tree模型Vector嵌套，插入数据，append部分数据，更新数据，
                update接口更新刚刚append的数据，更新数据，查询数据为两次更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_021)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 3, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 4;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 再次更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 3;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 3, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 4;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 022.Tree模型Vector嵌套，插入数据，按照顺序增量更新update嵌套内层vector节点的数据，
                remove部分数据，append部分数据，使用merge接口更新数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_022)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 023.Tree模型Vector嵌套，Vertex中有3个嵌套内层的vector，在一次更新操作中，
                vector1进行update、remove、append操作，vector2进行update、remove、append操作，
                vector3进行update、remove、append操作，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_023)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;
    GmcNodeT *R1 = NULL, *R1_A1 = NULL, *R1_V4 = NULL, *R1_V4_V5 = NULL;
    GmcNodeT *V6 = NULL, *V6_R2 = NULL, *V6_R2_V7 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        /*======================================================================*/
        ret = GmcNodeGetChild(root, "R1", &R1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(R1, "V4", &R1_V4);
        EXPECT_EQ(GMERR_OK, ret);
        // 增量更新vector节点R1.V4.V5
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(R1_V4, updateindex[0], &R1_V4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(R1_V4, "V5", &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(R1_V4_V5, updateindex[1], &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(R1_V4_V5, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(R1_V4, updateindex[0], &R1_V4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(R1_V4, "V5", &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(R1_V4_V5, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(R1_V4, updateindex[0], &R1_V4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(R1_V4, "V5", &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(R1_V4_V5, &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(R1_V4_V5, newValue, 0, f8_value);

        /*======================================================================*/
        ret = GmcNodeGetChild(root, "V6", &V6);
        EXPECT_EQ(GMERR_OK, ret);
        // 增量更新vector节点V6.R2.V7
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        // ret = TestSetVectorUpdateIndex(g_stmt, (char *)"V6.R2.V7", updateindex, indexNum);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V6, updateindex[0], &V6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6, "R2", &V6_R2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6_R2, "V7", &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V6_R2_V7, updateindex[1], &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V6_R2_V7, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V6, updateindex[0], &V6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6, "R2", &V6_R2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6_R2, "V7", &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V6_R2_V7, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V6, updateindex[0], &V6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6, "R2", &V6_R2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6_R2, "V7", &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V6_R2_V7, &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V6_R2_V7, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点R1.V4
        ret = GmcNodeGetChild(root, "R1", &R1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(R1, "V4", &R1_V4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(R1_V4, 1, &R1_V4);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点R1.V4.V5
        ret = GmcNodeGetChild(R1_V4, "V5", &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(R1_V4_V5, 0, &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(R1_V4_V5, getValue, 0, f8_value, 0);

        getValue = i;
        ret = ret = GmcNodeGetElementByIndex(R1_V4_V5, 1, &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(R1_V4_V5, getValue, 0, f8_value, 0);

        ret = ret = GmcNodeGetElementByIndex(R1_V4_V5, 2, &R1_V4_V5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(R1_V4_V5, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(R1_V4_V5, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V6
        ret = GmcNodeGetChild(root, "V6", &V6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V6, 1, &V6);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6, "R2", &V6_R2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V6_R2, "V7", &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V6.R2.V7
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V6_R2_V7, 0, &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V6_R2_V7, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V6_R2_V7, 1, &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V6_R2_V7, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V6_R2_V7, 2, &V6_R2_V7);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V6_R2_V7, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V6_R2_V7, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 024.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update、remove、append操作，2层vector进行update、remove、append操作，
                3层vector进行update、remove、append操作，2层和3层设置的vector index为上一层
                没有进行增量更新3种操作的vector index，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_024)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        // remove
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2_V3, 2, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 025.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update、remove、append操作，2层vector进行update、remove、append操作，
                3层vector进行update、remove、append操作，2层和3层设置的vector index为上一层
                有进行增量更新update操作的vector index，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_025)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 0;
        updateindex[1] = 0;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        // remove
        indexNum = 3;
        updateindex[0] = 0;
        updateindex[1] = 0;
        updateindex[2] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 3;
        updateindex[0] = 0;
        updateindex[1] = 0;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2_V3, 2, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 026.先内层再外层，Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                3层vector进行update、remove、append操作，2层vector进行update、remove、append操作，
                1层vector进行update、remove、append操作，2层和3层设置的vector index为1层
                没有进行增量更新3种操作的vector index，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_026)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        // remove
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2_V3, 2, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 027.先内层再外层，Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                3层vector进行update、remove、append操作，2层vector进行update、remove、append操作，
                1层vector进行update、remove、append操作， 2层和3层设置的vector index为1层
                有进行增量更新update操作的vector index，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_027)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 0;
        updateindex[1] = 0;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        // remove
        indexNum = 3;
        updateindex[0] = 0;
        updateindex[1] = 0;
        updateindex[2] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 3;
        updateindex[0] = 0;
        updateindex[1] = 0;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2_V3, 2, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 028.Tree模型Vector嵌套，插入数据，循环增量更新append数据直至vector最大size 1024，
                查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_028)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 1;
    uint32_t vector_num_V2 = 1023;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(root, i * index);

        // 插入vector节点 V1和V1.V2和V1.V2.V3
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, i * index);

            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num_V2; k++) {
                ret = GmcNodeAppendElement(V1_V2, &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_02(V1_V2, i * index);
            }
        }

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    // 这里已经有1023个node，再append 1个
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 1024;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 029.Tree模型Vector嵌套，插入数据，循环增量更新append数据超过vector最大size 1024，
                update失败，查询数据为append前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_029)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 1;
    uint32_t vector_num_V2 = 1023;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(root, i * index);

        // 插入vector节点 V1和V1.V2和V1.V2.V3
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, i * index);

            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num_V2; k++) {
                ret = GmcNodeAppendElement(V1_V2, &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_02(V1_V2, i * index);
            }
        }

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    // 这里已经有1023个node，再append 2个
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 1023;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 030.Tree模型Vector嵌套，插入数据，对同一个record，先update数据，然后remove数据，
                remove接口报错，最终更新成功，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_030)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove, 对同一record操作，返回失败
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 031.Tree模型Vector嵌套，插入数据，对同一个record，先update数据1，
                然后对同一record update数据2，第二次update接口报错，最终更新成功，
                查询数据为第一次update的数据。
                210729 现在可以对同一node update多次，以最后一次的值为准
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_031)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // update, 对同一record操作，返回失败
        newValue = i + 200;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;

        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 200;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 032.Tree模型Vector嵌套，在一次更新操作中，2层vector进行update数据1，
                1层vector进行update操作，2层vector对同一record进行update数据2，
                2层vector 在第二次update时报错。
                210729 现在可以对同一node update多次，以最后一次的值为准
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_032)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // update, 对同一record操作，返回失败
        newValue = i + 200;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;

        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 200;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 033.Tree模型Vector嵌套，插入数据，按照顺序增量更新append数据，remove部分数据，
                update部分数据，remove和update接口报错。210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_033)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // update, 操作顺序错误，返回失败
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove, 操作顺序错误，返回失败
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 034.Tree模型Vector嵌套，在一次更新操作中，2层vector进行remove部分数据，
                1层vector进行update、remove、append操作，2层vector进行update数据，
                2层vector在update时报错。
                210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_034)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // update, 操作顺序不对，返回失败
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 2;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点V1
        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);
    }
}

/*****************************************************************************
 Description  : 035.Tree模型Vector嵌套，插入数据，按照顺序增量更新append数据，update部分数据，
                update接口报错，再次append数据，更新成功，查询数据为2次append的数据。
                210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_035)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // update, 操作顺序错误，返回失败
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 5;
        for (int j = 1; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 036.Tree模型Vector嵌套，一次增量更新操作包含16个update、remove、append的混合操作，执行成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_036)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2, newValue);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        getValue = i;
        vector_num = 16;
        for (int j = 1; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 037.Tree模型Vector嵌套，一次增量更新操作包含1025个update、remove、append的混合操作，报错。
                2021.11.27: 一次增量更新个数从16改为1024
                2023.11.08: 一次增量更新个数兼容V3，改为无限制
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_037)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 1023次
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        for (int j = 0; j < 1023; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2, newValue);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
}

/*****************************************************************************
 Description  : 038.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update（index=1）、remove、append混合共16次操作，
                2层vector（index=1,1）进行update、remove、append混合共16次操作，
                3层vector（index=1,1,X）进行update、remove、append混合共16次操作，
                操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_038)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1, newValue);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, newValue);
        }
        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2, newValue);
        }

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);

        // remove
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 15, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 16;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 15, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        vector_num = 16;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2_V3, 15, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        vector_num = 16;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 039.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update（index=1）、remove、append混合共14次操作，
                2层vector（index=0,X）进行update、remove、append混合共16次操作，
                3层vector（index=3,0,X）进行update、remove、append混合共16次操作，
                操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_039)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 4;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1, newValue);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 12次
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        for (int j = 0; j < 12; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, newValue);
        }
        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        // remove
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2, newValue);
        }

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 3;
        updateindex[1] = 0;
        updateindex[2] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);

        // remove
        indexNum = 3;
        updateindex[0] = 3;
        updateindex[1] = 0;
        updateindex[2] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 3;
        updateindex[0] = 3;
        updateindex[1] = 0;
        updateindex[2] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 14, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 15;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 16, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        vector_num = 17;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1, 因为remove的V1的index=2的元素，这里index从3变为2
        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2_V3, 16, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        vector_num = 17;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 040.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，每层Vector内都有17个数组元素，
                在一次更新操作中，3层vector（index=N,1,X，N=0~15）进行update、remove、append混合共16次操作，
                操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_040)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num_V1 = 17;
    uint32_t vector_num_V2 = 2;
    uint32_t vector_num_V3 = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(root, i * index);

        // 插入vector节点 V1和V1.V2和V1.V2.V3
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num_V1; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, i * index);

            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num_V2; k++) {
                ret = GmcNodeAppendElement(V1_V2, &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_02(V1_V2, i * index);

                ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);
                for (uint32_t m = 0; m < vector_num_V3; m++) {
                    ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeSetPropertyByName_02(V1_V2_V3, i * index);
                }
            }
        }

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        for (int j = 0; j < (vector_num_V1 - 1); j++) {
            printf("V1 index = %d.\n", j);
            // update
            newValue = i + 100;
            indexNum = 3;
            updateindex[0] = j;
            updateindex[1] = 1;
            updateindex[2] = 1;
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);

            // remove
            indexNum = 3;
            updateindex[0] = j;
            updateindex[1] = 1;
            updateindex[2] = 2;
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
            EXPECT_EQ(GMERR_OK, ret);

            // append 14次
            newValue = i;
            indexNum = 3;
            updateindex[0] = j;
            updateindex[1] = 1;
            updateindex[2] = 0;
            for (int j = 0; j < 14; j++) {
                ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(V1, "V2", &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);
            }
        }
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        for (int j = 0; j < (vector_num_V1 - 1); j++) {
            // 读取vector节点V1
            ret = GmcNodeGetElementByIndex(V1, j, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            // 读取vector节点V1.V2
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);

            // 读取vector节点V1.V2.V3
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);

            getValue = i + 100;
            ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

            ret = GmcNodeGetElementByIndex(V1_V2_V3, 15, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

            vector_num_V3 = 16;
            uint32_t size = 0;
            ret = GmcNodeGetElementCount(V1_V2_V3, &size);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(vector_num_V3, size);
        }
    }
}

/*****************************************************************************
 Description  : 041.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update（index=1）、remove、append混合共15次操作，
                2层vector（index=0,1）进行update、remove、append混合共16次操作，执行成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_041)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1, newValue);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 13次
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        for (int j = 0; j < 13; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, newValue);
        }
        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        // remove
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2, newValue);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 14, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 15;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 15, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        vector_num = 16;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 042.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update（index=1）、remove、append混合共1024次操作，
                2层vector（index=0,1）进行update、remove、append操作，报错。
                2021.11.27: 一次增量更新个数从16改为1024
                2023.11.08: 一次增量更新个数兼容V3，改为无限制
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_042)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1, newValue);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 1022次
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        for (int j = 0; j < 1022; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, newValue);
        }
        /*======================================================================*/
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 15, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 1024;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 3;
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
    AW_ADD_ERR_WHITE_LIST(1, "DynCtx reaches mem peak. CtxName: sessionMemCtx");
}

/*****************************************************************************
 Description  : 043.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，在一次更新操作中，
                1层vector进行update（index=1）、remove、append混合共16次操作，
                2层vector（index=1,1）进行update、remove、append混合共16次操作，
                3层vector（index=1,0,1）进行update、remove、append操作，报错。
                2021.11.27: 一次增量更新个数从16改为1024
                2023.11.08: 一次增量更新个数兼容V3，改为无限制
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_043)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1, newValue);

        // remove
        indexNum = 1;
        updateindex[0] = 2;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        for (int j = 0; j < 1022; j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, newValue);
        }
        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        for (int j = 0; j < 1022; j++) {
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeAppendElement(V1_V2, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2, newValue);
        }

        /*======================================================================*/
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 0;
        updateindex[2] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 15, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 1024;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 15, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        vector_num = 1024;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 3;
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
    AW_ADD_ERR_WHITE_LIST(1, "DynCtx reaches mem peak. CtxName: sessionMemCtx");
}

/*****************************************************************************
 Description  : 044.Tree模型Vector嵌套，Vertex中有3层嵌套的vector，每层Vector内都有17个数组元素，
                在一次更新操作中，3层vector（index=N,1,X，N=0~16）进行update、remove、append混合共16次操作，
                第N=16时操作报错，查询数据为N=0~15时更新后的数据。
                2021.11.27: 一次增量更新个数从16改为1024
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_044)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num_V1 = 1025;
    uint32_t vector_num_V2 = 2;
    uint32_t vector_num_V3 = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(root, i * index);

        // 插入vector节点 V1和V1.V2和V1.V2.V3
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < (vector_num_V1 - 1); j++) {
            ret = GmcNodeAppendElement(V1, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, i * index);

            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t k = 0; k < vector_num_V2; k++) {
                ret = GmcNodeAppendElement(V1_V2, &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_02(V1_V2, i * index);

                ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);
                for (uint32_t m = 0; m < vector_num_V3; m++) {
                    ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeSetPropertyByName_02(V1_V2_V3, i * index);
                }
            }
        }

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2.V3
        for (int j = 0; j < vector_num_V1; j++) {
            printf("V1 index = %d.\n", j);

            // j = 1024时，V1的操作数已经达到1025，操作会报错
            if (j == (vector_num_V1 - 1)) {
                // update
                newValue = i + 100;
                indexNum = 3;
                updateindex[0] = j;
                updateindex[1] = 1;
                updateindex[2] = 1;

                ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
                EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
                break;
            }

            // update
            newValue = i + 100;
            indexNum = 3;
            updateindex[0] = j;
            updateindex[1] = 1;
            updateindex[2] = 1;
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);

            // remove
            indexNum = 3;
            updateindex[0] = j;
            updateindex[1] = 1;
            updateindex[2] = 2;
            ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
            EXPECT_EQ(GMERR_OK, ret);

            // append 14次
            newValue = i;
            indexNum = 3;
            updateindex[0] = j;
            updateindex[1] = 1;
            updateindex[2] = 0;
            for (int j = 0; j < 14; j++) {
                ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(V1, "V2", &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeSetPropertyByName_02(V1_V2_V3, newValue);
            }
        }
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_CONFIGURATION_LIMIT_EXCEEDED);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        for (int j = 0; j < (vector_num_V1 - 1); j++) {
            // 读取vector节点V1
            ret = GmcNodeGetElementByIndex(V1, j, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            // 读取vector节点V1.V2
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);

            // 读取vector节点V1.V2.V3
            ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);

            getValue = i + 100;
            ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2_V3, 0, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

            ret = GmcNodeGetElementByIndex(V1_V2_V3, 15, &V1_V2_V3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_Node_02(V1_V2_V3, getValue);

            vector_num_V3 = 16;
            uint32_t size = 0;
            ret = GmcNodeGetElementCount(V1_V2_V3, &size);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(vector_num_V3, size);
        }
    }
    AW_ADD_ERR_WHITE_LIST(1, "DynCtx reaches mem peak. CtxName: sessionMemCtx");
}

/*****************************************************************************
 Description  : 045.Tree模型Vector嵌套，嵌套外层的vector index可以不为0，根据实际情况填写。
                嵌套vector在append时设置index为非0且已经存在的index值，报错。
                append时设置index为非0且不存在的index值，报错。
                210728 接口更改后，已经没有这个场景了，都会成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_045)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 5;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 046.Tree模型Vector嵌套，在一次更新操作中，1层vector进行remove操作（index=1），
                2层vector进行update操作（index=1,1），update操作报错，查询数据为remove后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_046)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1
        // remove
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;

        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 2;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 047.Tree模型Vector嵌套，在一次更新操作中，2层vector进行update操作（index=1,1），
                1层vector进行remove操作（index=1），remove操作报错，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_047)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V1_V2, newValue);

        /*======================================================================*/
        // 增量更新vector节点V1
        // remove
        indexNum = 1;
        updateindex[0] = 1;

        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i;
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        ret = GmcNodeGetElementByIndex(V1, 2, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1, getValue);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V1_V2, getValue);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 048.Tree模型Vector嵌套，Vertex中有32层嵌套的vector（根节点算1层，实际只有31层vector），
                对第32层的vector进行update、remove、append操作，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_048)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V31 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_32(g_stmt, index, start_num, end_num, vector_num, MS_Tree_Vector_32_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_32_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V01", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 设置V01 index
        updateindex[0] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 跳过29层
        indexNum = 30;
        char NodeName[MAX_PROPERTY_NAME_LEN] = {0};
        char tmp[MAX_PROPERTY_NAME_LEN] = {0};
        for (int j = 2; j <= indexNum; j++) {
            int len = strlen(NodeName);
            strncpy(tmp, NodeName, len);
            snprintf(NodeName, 4, "V%02d", j);

            ret = GmcNodeGetChild(V1, (const char *)NodeName, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, i * index);
        }

        // 获取V31
        ret = GmcNodeGetChild(V1, "V31", &V31);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V31, updateindex[1], &V31);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V31, newValue);

        // remove
        updateindex[1] = 1;
        ret = GmcNodeRemoveElementByIndex(V31, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        updateindex[1] = 0;
        ret = GmcNodeAppendElement(V31, &V31);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_02(V31, newValue);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_32_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_32_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_32_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V01", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetElementByIndex(V1, 0, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 跳过29层
        int indexNum = 30;
        char NodeName[MAX_PROPERTY_NAME_LEN] = {0};
        char tmp[MAX_PROPERTY_NAME_LEN] = {0};
        for (int j = 2; j <= indexNum; j++) {
            int len = strlen(NodeName);
            strncpy(tmp, NodeName, len);
            snprintf(NodeName, 4, "V%02d", j);

            ret = GmcNodeGetChild(V1, (const char *)NodeName, &V1);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_02(V1, i * index);
        }

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V31", &V31);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V31, 0, &V31);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V31, getValue);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V31, 1, &V31);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V31, getValue);

        ret = GmcNodeGetElementByIndex(V31, 2, &V31);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_Node_02(V31, getValue);

        vector_num = 3;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V31, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 049.Tree模型Vector嵌套，update、remove、append接口的nodeName为不存在的节点名，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_049)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update, nodename错误
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;

        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V5", &V1_V2);
        EXPECT_EQ(GMERR_INVALID_NAME, ret);

        // remove, nodename错误
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;

        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V5", &V1_V2);
        EXPECT_EQ(GMERR_INVALID_NAME, ret);

        // append, nodename错误
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;

        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V5", &V1_V2);
        EXPECT_EQ(GMERR_INVALID_NAME, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_INVALID_NAME);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 050.Tree模型Vector嵌套，update和remove输入的index超过实际存在的index值，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_050)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update, index错误
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 10;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove, index错误
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 11;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    }
    AddWhiteList(GMERR_DATA_EXCEPTION);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(V1_V2, j, &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 051.Tree模型Vector嵌套，通过GmcDynamicArrayAppend接口插入的index数量超过层数，报错。
                通过GmcDynamicArrayAppend接口插入的index数量小于层数，报错。
                210728 接口修改后已经没有该场景
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_051)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);
}

/*****************************************************************************
 Description  : 052.异步：Tree模型Vector嵌套，使用异步句柄进行增量更新操作，操作成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_052)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;
    AsyncUserDataT asyncData;
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 普通异步插入数据
    TestGmcInsertVertexAsync(
        g_stmt_async, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt_async, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData, 1, 1000000);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 054.订阅推送：Tree模型Vector嵌套，全表订阅，嵌套vector上的数据增量更新后，
                可以触发推送，数据正确。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_054)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValueupdate = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    // 创建订阅连接
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    const char *g_subConnName = "subConnName";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);

    g_subIndex = 0;

    // 创建订阅关系
    char *sub_info = NULL;
    readJanssonFile("schema_file/TreeModel_subinfo_fullLabelSub.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    int userDataIdx = 0;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValueupdate = i + 100;
        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValueupdate, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 0;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValueupdate, 0, f8_value);

        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = newValueupdate;
        userDataIdx++;

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅、断开订阅连接
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

/*****************************************************************************
 Description  : 055.数组排序：Tree模型Vector嵌套，数组排序后，进行增量更新，操作成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(VectorNestIncUpdate_test, DML_051_055)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    void *treenode = NULL;
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 普通同步插入数据
    // V1.V2插入数据的顺序：1,0,2
    TestGmcInsertVertexdifferent(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num,
    MS_Tree_Vector_01_Name);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        ret = GmcNodeSortElement(V1_V2, "F0", GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = 0;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, stringA, 0);

        getValue = 1;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, stringB, 0);

        getValue = 2;
        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, stringC, 0);
    }

    // 更新node
    for(int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    AddWhiteList(GMERR_NO_DATA);
}

/*****************************************************************************
 Description  : 056.Tree模型Vector嵌套，多个线程同时增量更新vector的同一条record，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：update 数据1
void *client_thread_056_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 1]Incremental Update Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：update 数据2
void *client_thread_056_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 200;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 2;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 2]Incremental Update Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(VectorNestIncUpdate_test, DML_051_056)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02;

    ret = pthread_create(&client_thr_01, NULL, client_thread_056_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_056_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue1 = i + 100;
        int getValue2 = i + 200;

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint32_t f0_value_node = 0;
        ret = GmcNodeGetPropertyByName(V1_V2, "F0", &f0_value_node, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        if ((f0_value_node == getValue1) || (f0_value_node == getValue2)) {
            printf(
                "[Multiple threads][V1(1).V2(0)]Incremental Update success. V1.V2.F0 newvalue = %d.\n", f0_value_node);
        } else {
            printf("[Multiple threads][V1(1).V2(0)]Incremental Update fail. V1.V2.F0 newvalue = %d.\n", f0_value_node);
        }

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        f0_value_node = 0;
        ret = GmcNodeGetPropertyByName(V1_V2, "F0", &f0_value_node, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        if ((f0_value_node == getValue1) || (f0_value_node == getValue2)) {
            printf(
                "[Multiple threads][V1(1).V2(2)]Incremental Update success. V1.V2.F0 newvalue = %d.\n", f0_value_node);
        } else {
            printf("[Multiple threads][V1(1).V2(2)]Incremental Update fail. V1.V2.F0 newvalue = %d.\n", f0_value_node);
        }
    }
}

/*****************************************************************************
 Description  : 057.Tree模型Vector嵌套，多个线程分别update、remove、append同一个vector，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：update
void *client_thread_057_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 1]Incremental update Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：remove
void *client_thread_057_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 2]Incremental remove Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程3：append
void *client_thread_057_03(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 3]Incremental append Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(VectorNestIncUpdate_test, DML_051_057)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02, client_thr_03;

    ret = pthread_create(&client_thr_01, NULL, client_thread_057_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_057_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_03, NULL, client_thread_057_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    pthread_join(client_thr_03, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        // 未update的数据值不变
        getValue = i;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        ret = GmcNodeGetElementByIndex(V1_V2, 3, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        vector_num = 4;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 058.Tree模型Vector嵌套，多个线程分别update同一vector的多层子树，
                上层index作为下层的输入index，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：update V1
void *client_thread_058_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1
        // update
        newValue = i + 100;
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 1]Incremental Update V1 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：update V1.V2
void *client_thread_058_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1.V2
        // update
        newValue = i + 100;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 2]Incremental update V1.V2 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程3：update V1.V2.V3
void *client_thread_058_03(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1.V2.V3
        // update
        newValue = i + 100;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2_V3, updateindex[2], &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 3]Incremental update V1.V2.V3 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(VectorNestIncUpdate_test, DML_051_058)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02, client_thr_03;

    ret = pthread_create(&client_thr_01, NULL, client_thread_058_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_058_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_03, NULL, client_thread_058_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    pthread_join(client_thr_03, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        // 读取vector节点V1
        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1, getValue, 0, f8_value, 0);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2, getValue, 0, f8_value, 0);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(V1_V2_V3, 1, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName(V1_V2_V3, getValue, 0, f8_value, 0);
    }
}

/*****************************************************************************
 Description  : 059.Tree模型Vector嵌套，多个线程分别remove同一vector的多层子树，
                每层vertex插入数据3条，设置每层的remove index都为1，即使其他线程remove成功，
                也会存在index=1的节点，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：remove V1
void *client_thread_059_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // // 增量更新vector节点V1
        // remove
        indexNum = 1;
        updateindex[0] = 1;
        ret = GmcNodeRemoveElementByIndex(V1, updateindex[0]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 1]Incremental remove V1 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：update V1.V2
void *client_thread_059_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1.V2
        // remove
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2, updateindex[1]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 2]Incremental remove V1.V2 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程3：update V1.V2.V3
void *client_thread_059_03(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1.V2.V3
        // remove
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 1;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(V1_V2_V3, updateindex[2]);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 3]Incremental remove V1.V2.V3 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(VectorNestIncUpdate_test, DML_051_059)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02, client_thr_03;

    ret = pthread_create(&client_thr_01, NULL, client_thread_059_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_059_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_03, NULL, client_thread_059_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    pthread_join(client_thr_03, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        uint32_t size1 = 0;
        ret = GmcNodeGetElementCount(V1, &size1);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V1 size = %d.\n", size1);

        /*======================================================================*/
        for (uint32_t V1size = 0; V1size < size1; V1size++) {
            // 读取vector节点V1
            ret = GmcNodeGetElementByIndex(V1, V1size, &V1);
            EXPECT_EQ(GMERR_OK, ret);

            // 获取V2
            ret = GmcNodeGetChild(V1, "V2", &V1_V2);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t size2 = 0;
            ret = GmcNodeGetElementCount(V1_V2, &size2);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V1(%d).V2 size = %d.\n", V1size, size2);

            for (uint32_t V2size = 0; V2size < size2; V2size++) {
                // 读取vector节点V1.V2
                ret = GmcNodeGetElementByIndex(V1_V2, V2size, &V1_V2);
                EXPECT_EQ(GMERR_OK, ret);

                // 获取V3
                ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
                EXPECT_EQ(GMERR_OK, ret);

                uint32_t size3 = 0;
                ret = GmcNodeGetElementCount(V1_V2_V3, &size3);
                EXPECT_EQ(GMERR_OK, ret);
                printf("V1(%d).V2(%d).V3 size = %d.\n", V1size, V2size, size3);
            }
        }
    }
}

/*****************************************************************************
 Description  : 060.Tree模型Vector嵌套，多个线程分别append同一vector的多层子树，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：append V1
void *client_thread_060_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1
        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        // append
        newValue = i;
        indexNum = 1;
        updateindex[0] = 0;
        ret = GmcNodeAppendElement(V1, &V1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 1]Incremental append V1 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：append V1.V2
void *client_thread_060_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1.V2
        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        // append
        newValue = i;
        indexNum = 2;
        updateindex[0] = 1;
        updateindex[1] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 2]Incremental append V1.V2 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程3：append V1.V2.V3
void *client_thread_060_03(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点V1.V2.V3
        // append
        newValue = i;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        // append
        newValue = i;
        indexNum = 3;
        updateindex[0] = 1;
        updateindex[1] = 1;
        updateindex[2] = 0;
        ret = GmcNodeGetElementByIndex(V1, updateindex[0], &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1_V2, updateindex[1], &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(V1_V2_V3, &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V1_V2_V3, newValue, 0, f8_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Thread 3]Incremental append V1.V2.V3 Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(VectorNestIncUpdate_test, DML_051_060)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f8_value[8] = "string";
    int newValue = 0;
    int getValue = 0;

    uint32_t indexNum = 0;
    uint32_t updateindex[40] = {0};
    GmcNodeT *root = NULL, *V1 = NULL, *V1_V2 = NULL, *V1_V2_V3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f8_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02, client_thr_03;

    ret = pthread_create(&client_thr_01, NULL, client_thread_060_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_060_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_03, NULL, client_thread_060_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    pthread_join(client_thr_03, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);

        /*======================================================================*/
        vector_num = 5;
        uint32_t size = 0;
        ret = GmcNodeGetElementCount(V1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 5;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        // 读取vector节点V1.V2
        ret = GmcNodeGetElementByIndex(V1_V2, 1, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2.V3
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 5;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        /*======================================================================*/
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "V1", &V1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V1, 1, &V1);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点V1.V2
        ret = GmcNodeGetChild(V1, "V2", &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetElementByIndex(V1_V2, 0, &V1_V2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(V1_V2, "V3", &V1_V2_V3);
        EXPECT_EQ(GMERR_OK, ret);

        vector_num = 3;
        size = 0;
        ret = GmcNodeGetElementCount(V1_V2_V3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}
