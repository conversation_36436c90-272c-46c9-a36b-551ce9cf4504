[{"type": "record", "name": "M1", "fields": [{"name": "F0", "type": "char", "nullable": true}, {"name": "F1", "type": "uchar", "nullable": true}, {"name": "F2", "type": "int8", "nullable": true}, {"name": "F3", "type": "uint8", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "int64", "nullable": true}, {"name": "F10", "type": "uint64", "nullable": true}, {"name": "F11", "type": "float", "nullable": true}, {"name": "F12", "type": "double", "nullable": true}, {"name": "F13", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "nullable": true, "size": 100}, {"name": "F15", "type": "bytes", "size": 12}, {"name": "F16", "type": "fixed", "size": 12}], "super_fields": [{"name": "superfield0", "comment": "test", "fields": ["F0", "F1"]}], "keys": [{"node": "M1", "name": "T39_K0", "fields": ["F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "M1", "name": "T39_hash", "fields": ["F9"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}]