extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tree_tools.h"

GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL;

GmcConnT *conn;
GmcStmtT *stmt;
GmcConnT *connectionAsync = NULL;
char label_name1[] = "OP_T0";
char lalable_name_PK1[] = "OP_PK";
char label_name2[] = "DST_T0";
char lalable_name_PK2[] = "DST_PK";
char label_name3[] = "edgelabel_testEdge";
char label_name4[] = "OP_T0";
char g_label_config_test[] = "{\"max_record_num\":10000}";
#define MAX_VERTEX_NUM 10000

char *test_schema1 = NULL;
char *test_schema2 = NULL;
char *test_schema3 = NULL;
char *test_schema4 = NULL;
const char *edgeLabelName = "edgelabel_testEdge";
char g_command[1024];
char cfg_name[50] = "compatibleV3";

int start_num = 0;
int end_num = 10;
int array_num = 3;
int vector_num = 3;

void TestGmcInsertVertexdifferent(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 插入顶点   数组成员分别传 1 ，0,2
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            if (j == 0) {
                TestGmcNodeSetPropertyByName_A(t2, 1 * index, bool_value, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeSetPropertyByName_A(t2, 0 * index, bool_value, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeSetPropertyByName_A(t2, 2 * index, bool_value, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 插入vector节点
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1 * index, bool_value, stringB);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0 * index, bool_value, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2 * index, bool_value, stringC);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcDirectFetchVertexdifferent(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num)
{
    int32_t ret = 0;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isFinish);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_R(root, i * index, bool_value, f14_value);
            TestGmcNodeGetPropertyByName_p(t1, i * index, bool_value, f14_value);
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_A(t2, 1 * index, bool_value, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_A(t2, 0 * index, bool_value, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_A(t2, 2 * index, bool_value, stringC);
                }
            }
            // 读取vector节点
            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1 * index, bool_value, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0 * index, bool_value, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2 * index, bool_value, stringC);
                }
            }
        }
    }
}

class TreeModelEnhanced_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TreeModelEnhanced_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    g_stmt_async = NULL;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));
}

void TreeModelEnhanced_test::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    ASSERT_EQ(0, ret);

    testEnvClean();
}

void TreeModelEnhanced_test::SetUp()
{
    int ret = 0;
    // 封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name1);
    GmcDropVertexLabel(stmt, label_name2);
    GmcDropVertexLabel(stmt, label_name3);
    readJanssonFile("schema_file/TreeModelEnhanced_test_op.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);

    AW_CHECK_LOG_BEGIN();

    char errorMsgg1[128] = {}, errorMsgg2[128] = {};
    (void)snprintf(errorMsgg1, sizeof(errorMsgg1), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsgg2, sizeof(errorMsgg2), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, errorMsgg1, errorMsgg2);
}

void TreeModelEnhanced_test::TearDown()
{
    int32_t ret = 0;
    AW_CHECK_LOG_END();

    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGet参数stmt为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_001)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(NULL, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : GmcNodeGet参数nodepath为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_002)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NULL, &treenode);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGetType参数node为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_003)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(NULL, &nodeType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGetType参数type为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_004)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(treenode, NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
Description  : GmcSortNode参数node为空
Input        : None
Output       : None
Return Value :
Notes        :
History      :
Author       : qinjianhua wx620469
Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_005)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    GmcNodeT *treenode;
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // 降序排序
    GmcOrderDirectionE increase = GMC_ORDER_DESC;
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(NULL, (char *)"A0", increase);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcSortNode参数propName为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_006)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    GmcNodeT *treenode;
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // 降序排序
    GmcOrderDirectionE increase = GMC_ORDER_DESC;
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, NULL, increase);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcSortNode参数propName不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_007)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    GmcNodeT *treenode;
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // 降序排序
    GmcOrderDirectionE increase = GMC_ORDER_DESC;
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, (char *)"A18", increase);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcSortNode参数increase为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_008)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 降序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A14", GMC_ORDER_DESC);  // 降序排序
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A14", GMC_ORDER_ASC);  // 升序排序
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";

        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : GmcSortNode参数propName非获取到的node的propName
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_009)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    GmcNodeT *treenode;
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // 降序排序
    GmcOrderDirectionE increase = GMC_ORDER_DESC;
    GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetName获取根节点的nodeName
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_010)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"OP_T0", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetName获取普通子节点的nodeName
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_011)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
    const char *nodeName = NULL;
    ret = GmcNodeGetName(treenode, &nodeName);
    ASSERT_EQ(GMERR_OK, ret);
    int comp = strcmp(nodeName, "T1");
    EXPECT_EQ(0, comp);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetName获取array数组节点的nodeName
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_012)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
    const char *nodeName = NULL;
    ret = GmcNodeGetName(treenode, &nodeName);
    ASSERT_EQ(GMERR_OK, ret);
    int comp = strcmp(nodeName, "T2");
    EXPECT_EQ(0, comp);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetName获取vector数组节点的nodeName
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_013)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
    const char *nodeName = NULL;
    ret = GmcNodeGetName(treenode, &nodeName);
    ASSERT_EQ(GMERR_OK, ret);
    int comp = strcmp(nodeName, "T3");
    EXPECT_EQ(0, comp);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGet获取根节点的node
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"OP_T0", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGet获取普通子节点的node
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_015)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGet获取array数组节点的node
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_016)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGet获取vector数组节点的node
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_017)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetType获取根节点的type
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_018)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"OP_T0", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetType获取普通子节点的type
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_019)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(treenode, &nodeType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMC_NODE_RECORD, nodeType);
}

/* ****************************************************************************
  Description  : 调用GmcNodeGetType获取array数组节点的type
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_020)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(treenode, &nodeType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMC_NODE_ARRAY, nodeType);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetType获取vector数组节点的type
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_021)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(treenode, &nodeType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMC_NODE_VECTOR, nodeType);
}

/* ****************************************************************************
 Description  : 调用GmcNodeExptFieldValue，验证Node节点中的字段值是否符合预期
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_022)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {}, errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    GmcNodeT *treenode1 = NULL;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    bool isNull;
    GmcPropValueT fieldValue;
    bool asExpt;
    int end_num = 1;
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        fieldValue.type = GMC_DATATYPE_UINT32;
        // memcpy(fieldValue.propertyName, "P3", strlen("P3"));
        memcpy(fieldValue.propertyName, "P3", sizeof("P3"));

        fieldValue.size = sizeof(uint32_t);
        GmcGetChildNode(stmt, (char *)"T1", &treenode);

        // 校验普通node字段
        uint32_t tmp = 3 * i;
        fieldValue.value = (void *)&tmp;
        ret = GmcNodeExptFieldValue(treenode, &fieldValue, &asExpt);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, asExpt);

        // 字段不属于该node
        GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ret = GmcNodeExptFieldValue(treenode, &fieldValue, &asExpt);
        ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, asExpt);

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        fieldValue.type = GMC_DATATYPE_UINT32;
        // memcpy(fieldValue.propertyName, "A3", strlen("A3"));
        memcpy(fieldValue.propertyName, "A3", sizeof("A3"));
        fieldValue.size = sizeof(uint32_t);
        GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                // 校验array node字段，预期值与实际值一致
                uint32_t tmp = 6;
                uint32_t tmp2 = 8;
                fieldValue.value = (void *)&tmp;
                ret = GmcNodeExptFieldValue(treenode, &fieldValue, &asExpt);
                ASSERT_EQ(GMERR_OK, ret);
                EXPECT_EQ(true, asExpt);
                // 校验普通node字段，预期值与实际值不一致
                fieldValue.value = (void *)&tmp2;
                ret = GmcNodeExptFieldValue(treenode, &fieldValue, &asExpt);
                ASSERT_EQ(GMERR_OK, ret);
                EXPECT_EQ(false, asExpt);
                // 参数1非法node
                ret = GmcNodeExptFieldValue(treenode1, &fieldValue, &asExpt);
                ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(false, asExpt);
                // 参数1为NULL
                ret = GmcNodeExptFieldValue(NULL, &fieldValue, &asExpt);
                ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(false, asExpt);
                // 参数2为NULL
                ret = GmcNodeExptFieldValue(treenode, NULL, &asExpt);
                ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(false, asExpt);
                // 参数3为NULL
                ret = GmcNodeExptFieldValue(treenode, &fieldValue, NULL);
                EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(false, asExpt);
            }
        }

        fieldValue.type = GMC_DATATYPE_UINT32;
        // memcpy(fieldValue.propertyName, "V3", strlen("V3"));
        memcpy(fieldValue.propertyName, "V3", sizeof("V3"));
        fieldValue.size = sizeof(uint32_t);
        GmcGetChildNode(stmt, (char *)"T3", &treenode);
        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                // 校验array node字段，预期值与实际值一致
                uint32_t tmp = 6;
                fieldValue.value = (void *)&tmp;
                ret = GmcNodeExptFieldValue(treenode, &fieldValue, &asExpt);
                ASSERT_EQ(GMERR_OK, ret);
                EXPECT_EQ(true, asExpt);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcVertexExptFieldValue，验证vetex节点中的字段值是否符合预期
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_023)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    bool isNull;
    GmcPropValueT fieldValue;
    bool asExpt;
    int end_num = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        // 插入顶点
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeSetPropertyByName_P(t1, i, 0, (char *)"string");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            if (j == 0) {
                TestGmcNodeSetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeSetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeSetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 插入vector节点
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1, 0, stringB);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0, 0, stringA);
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2, 0, stringC);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        uint32_t f3_value = 3 * i;
        uint32_t f3_value2 = 3 * i + 1;
        fieldValue.type = GMC_DATATYPE_UINT32;
        // memcpy(fieldValue.propertyName, "F3", strlen("F3"));
        memcpy(fieldValue.propertyName, "F3", sizeof("F3"));
        fieldValue.size = sizeof(uint32_t);
        fieldValue.value = (void *)&f3_value;
        // 校验非node字段，预期值与实际值一致
        ret = GmcVertexExptFieldValue(stmt, &fieldValue, &asExpt);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, asExpt);
        // 校验非node字段，预期值与实际值不一致
        fieldValue.value = (void *)&f3_value2;
        ret = GmcVertexExptFieldValue(stmt, &fieldValue, &asExpt);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, asExpt);
        // 参数1为NULL
        ret = GmcVertexExptFieldValue(NULL, &fieldValue, &asExpt);
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        EXPECT_EQ(false, asExpt);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // 参数2为NULL
        ret = GmcVertexExptFieldValue(stmt, NULL, &asExpt);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
        EXPECT_EQ(false, asExpt);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // 参数3为NULL
        ret = GmcVertexExptFieldValue(stmt, &fieldValue, NULL);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
        EXPECT_EQ(false, asExpt);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/*****************************************************************************
 Description  : 调用GmcSortNode 在array数组里根据int成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_024)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据int数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_025)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据uint数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_026)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A1", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据uint数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_027)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A1", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据string数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_028)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A14", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据string数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_029)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A14", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据bytes数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_030)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A15", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据bytes数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_031)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A15", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据fixed数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_032)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A16", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在array数组里根据fixed数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_033)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A16", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode 在vector数组里根据int成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_034)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在vector数组里根据int数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_035)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在vector数组里根据uint数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_036)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V1", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在vector数组里根据uint数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_037)
{
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V1", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在vector数组里根据string数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_038)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V14", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在vector数组里根据string数组成员倒序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_039)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V14", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortNode在vector数组里根据bytes数组成员正序排序，读取排序后的数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_040)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V15", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
  Description  : 调用GmcSortNode在vector数组里根据bytes数组成员倒序排序，读取排序后的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_041)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V15", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
  Description  : 调用GmcSortNode在vector数组里根据fixed数组成员正序排序，读取排序后的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_042)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V16", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
  Description  : 调用GmcSortNode在vector数组里根据fixed数组成员倒序排序，读取排序后的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_043)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V16", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
            }
        }

        // 读取vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
  Description  : 调用GmcSortNode根据uint数组成员倒序排序，字段相等
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_044)
{
    int32_t ret = 0;
}

/* ****************************************************************************
  Description  : 普通节点属性个数最大为1023个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_045)
{
    int32_t ret = 0;

    int32_t F0 = 0;
    int start_num = 0;
    int end_num = 1;
    int filed_num = 1024;

    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1023";
    char lable_name_PK[16] = "fields_1023_pk";

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1023_int32_fields_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    for (int k = start_num; k < end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t field_value = k;
        for (int i = start_num; i < filed_num - 3; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeSetPropertyByName(root, field_name_f, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        for (int i = start_num; i < filed_num; i++) {
            sprintf((char *)field_name_a, (char *)"T1/A%d", i);
            // 插入array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeSetPropertyByName(t1, field_name_a, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
                GmcNodeGetNextElement(t1, &t1);
            }
        }
        for (int i = start_num; i < 1; i++) {
            sprintf((char *)field_name_v, (char *)"T2/V%d", i);
            // 插入vector节点
            for (uint32_t j = 0; j < vector_num; j++) {

                ret = GmcNodeAppendElement(t2, &t2);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcNodeSetPropertyByName(t2, field_name_v, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < end_num; k++) {
        bool isNull;
        int32_t field_value;
        // 查询
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(stmt, lable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isFinish);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < filed_num - 3; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeGetPropertyByName(root, field_name_f, &field_value, sizeof(int32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(k, field_value);
        }

        for (int i = start_num; i < filed_num - 1; i++) {
            sprintf((char *)field_name_a, (char *)"A%d", i);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t1, j, &t1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t1, field_name_a, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
        for (int i = start_num; i < filed_num - 1; i++) {
            sprintf((char *)field_name_v, (char *)"V%d", i);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t2, field_name_v, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : 普通节点属性个数最大为1024个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_046)
{
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1;
    int filed_num = 1024;

    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1024";
    char lable_name_PK[16] = "fields_1024_pk";

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1024_int32_fields_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    for (int k = start_num; k < end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t field_value = k;
        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeSetPropertyByName(root, field_name_f, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        for (int i = start_num; i < filed_num; i++) {
            sprintf((char *)field_name_a, (char *)"T1/A%d", i);
            // 插入array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeSetPropertyByName(t1, field_name_a, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
                GmcNodeGetNextElement(t1, &t1);
            }
        }
        for (int i = start_num; i < 1; i++) {
            sprintf((char *)field_name_v, (char *)"T2/V%d", i);
            // 插入vector节点
            for (uint32_t j = 0; j < vector_num; j++) {

                ret = GmcNodeAppendElement(t2, &t2);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcNodeSetPropertyByName(t2, field_name_v, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < end_num; k++) {
        bool isNull;
        int32_t field_value;
        // 查询
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(stmt, lable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isFinish);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeGetPropertyByName(root, field_name_f, &field_value, sizeof(int32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(k, field_value);
        }

        for (int i = start_num; i < filed_num - 1023; i++) {
            sprintf((char *)field_name_a, (char *)"A%d", i);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t1, j, &t1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t1, field_name_a, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
        for (int i = start_num; i < filed_num - 1023; i++) {
            sprintf((char *)field_name_v, (char *)"V%d", i);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t2, field_name_v, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : 普通节点属性个数最大为1025个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_047)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1024;
    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1025";
    char lable_name_PK[16] = "fields_1025_pk";
    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1025_int32_fields_r_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);
}

/* ****************************************************************************
  Description  : Array数组节点属性个数最大为1023个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_048)
{
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1;
    int filed_num = 1023;

    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1023";
    char lable_name_PK[16] = "fields_1023_pk";

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1023_int32_fields_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    for (int k = start_num; k < end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t field_value = k;
        for (int i = start_num; i < filed_num - 3; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeSetPropertyByName(root, field_name_f, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        for (int i = start_num; i < filed_num; i++) {
            sprintf((char *)field_name_a, (char *)"T1/A%d", i);
            // 插入array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeSetPropertyByName(t1, field_name_a, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
                GmcNodeGetNextElement(t1, &t1);
            }
        }
        for (int i = start_num; i < 1; i++) {
            sprintf((char *)field_name_v, (char *)"T2/V%d", i);
            // 插入vector节点
            for (uint32_t j = 0; j < vector_num; j++) {

                ret = GmcNodeAppendElement(t2, &t2);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcNodeSetPropertyByName(t2, field_name_v, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < end_num; k++) {
        bool isNull;
        int32_t field_value;
        // 查询
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(stmt, lable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isFinish);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < filed_num - 3; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeGetPropertyByName(root, field_name_f, &field_value, sizeof(int32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(k, field_value);
        }

        for (int i = start_num; i < filed_num - 1; i++) {
            sprintf((char *)field_name_a, (char *)"A%d", i);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t1, j, &t1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t1, field_name_a, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
        for (int i = start_num; i < filed_num - 1; i++) {
            sprintf((char *)field_name_v, (char *)"V%d", i);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t2, field_name_v, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : Array数组节点属性个数最大为1024个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_049)
{
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1;
    int filed_num = 1024;

    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1024";
    char lable_name_PK[16] = "fields_1024_pk";

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1024_int32_fields_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    for (int k = start_num; k < end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t field_value = k;
        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeSetPropertyByName(root, field_name_f, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        for (int i = start_num; i < filed_num; i++) {
            sprintf((char *)field_name_a, (char *)"T1/A%d", i);
            // 插入array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeSetPropertyByName(t1, field_name_a, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
                GmcNodeGetNextElement(t1, &t1);
            }
        }
        for (int i = start_num; i < 1; i++) {
            sprintf((char *)field_name_v, (char *)"T2/V%d", i);
            // 插入vector节点
            for (uint32_t j = 0; j < vector_num; j++) {

                ret = GmcNodeAppendElement(t2, &t2);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcNodeSetPropertyByName(t2, field_name_v, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < end_num; k++) {
        bool isNull;
        int32_t field_value;
        // 查询
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(stmt, lable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isFinish);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeGetPropertyByName(root, field_name_f, &field_value, sizeof(int32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(k, field_value);
        }

        for (int i = start_num; i < filed_num - 1023; i++) {
            sprintf((char *)field_name_a, (char *)"A%d", i);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t1, j, &t1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t1, field_name_a, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
        for (int i = start_num; i < filed_num - 1023; i++) {
            sprintf((char *)field_name_v, (char *)"V%d", i);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t2, field_name_v, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : Array数组节点属性个数最大为1025个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_050)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1024;
    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1025";
    char lable_name_PK[16] = "fields_1025_pk";
    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1025_int32_fields_a_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);
}

/* ****************************************************************************
  Description  : Vector数组节点属性个数最大为1023个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_051)
{
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1;
    int filed_num = 1023;

    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1023";
    char lable_name_PK[16] = "fields_1023_pk";

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1023_int32_fields_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    for (int k = start_num; k < end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t field_value = k;
        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeSetPropertyByName(root, field_name_f, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        for (int i = start_num; i < filed_num; i++) {
            sprintf((char *)field_name_a, (char *)"T1/A%d", i);
            // 插入array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeSetPropertyByName(t1, field_name_a, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
                GmcNodeGetNextElement(t1, &t1);
            }
        }
        for (int i = start_num; i < 1; i++) {
            sprintf((char *)field_name_v, (char *)"T2/V%d", i);
            // 插入vector节点
            for (uint32_t j = 0; j < vector_num; j++) {

                ret = GmcNodeAppendElement(t2, &t2);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcNodeSetPropertyByName(t2, field_name_v, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < end_num; k++) {
        bool isNull;
        int32_t field_value;
        // 查询
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(stmt, lable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isFinish);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeGetPropertyByName(root, field_name_f, &field_value, sizeof(int32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(k, field_value);
        }

        for (int i = start_num; i < filed_num - 1; i++) {
            sprintf((char *)field_name_a, (char *)"A%d", i);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t1, j, &t1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t1, field_name_a, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
        for (int i = start_num; i < filed_num - 1; i++) {
            sprintf((char *)field_name_v, (char *)"V%d", i);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t2, field_name_v, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : Vector数组节点属性个数最大为1024个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_052)
{
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1;
    int filed_num = 1024;

    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1024";
    char lable_name_PK[16] = "fields_1024_pk";

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1024_int32_fields_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
    for (int k = start_num; k < end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t field_value = k;
        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeSetPropertyByName(root, field_name_f, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
        }
        for (int i = start_num; i < filed_num; i++) {
            sprintf((char *)field_name_a, (char *)"T1/A%d", i);
            // 插入array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeSetPropertyByName(t1, field_name_a, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
                GmcNodeGetNextElement(t1, &t1);
            }
        }
        for (int i = start_num; i < 1; i++) {
            sprintf((char *)field_name_v, (char *)"T2/V%d", i);
            // 插入vector节点
            for (uint32_t j = 0; j < vector_num; j++) {

                ret = GmcNodeAppendElement(t2, &t2);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcNodeSetPropertyByName(t2, field_name_v, GMC_DATATYPE_INT32, &field_value, sizeof(int32_t));
            }
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < end_num; k++) {
        bool isNull;
        int32_t field_value;
        // 查询
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(stmt, lable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isFinish);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < filed_num - 2; i++) {
            sprintf((char *)field_name_f, (char *)"F%d", i);
            ret = GmcNodeGetPropertyByName(root, field_name_f, &field_value, sizeof(int32_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(k, field_value);
        }

        for (int i = start_num; i < filed_num - 1023; i++) {
            sprintf((char *)field_name_a, (char *)"A%d", i);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t1, j, &t1);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t1, field_name_a, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
        for (int i = start_num; i < filed_num - 1023; i++) {
            sprintf((char *)field_name_v, (char *)"V%d", i);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t2, field_name_v, &field_value, sizeof(int32_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                // ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(k, field_value);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : Vector数组节点属性个数最大为1025个，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_053)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    uint32_t F0 = 0;
    int start_num = 0;
    int end_num = 1024;
    char *test_schema = NULL;
    char field_name_f[16];
    char field_name_a[16];
    char field_name_v[16];
    char label_name[32] = "fields_1025";
    char lable_name_PK[16] = "fields_1025_pk";
    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/1025_int32_fields_v_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);
}

/* ****************************************************************************
  Description  : Localhash索引建在普通节点上，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_054)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    readJanssonFile("schema_file/TreeModelEnhanced_test_localhash.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema1);
}

/* ****************************************************************************
  Description  : Localhash索引建在array数组节点上，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_055)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    readJanssonFile("schema_file/TreeModelEnhanced_test_array.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema1);
}

/* ****************************************************************************
  Description  : Localhash索引建在vector数组节点上，建表
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_056)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    readJanssonFile("schema_file/TreeModelEnhanced_test_vector.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema1);
}

/* ****************************************************************************
  Description  : 调用GmcSortNode根据bool类型数组成员正序排序，读取排序后的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_057)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A8", increase);
        ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *client_thread_058_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num / 2, array_num, vector_num, label_name1);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *client_thread_058_02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", end_num / 2, end_num, array_num, vector_num, label_name1);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/* ****************************************************************************
  Description  : Tree模型并发写入不同的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_058)
{
    int ret = 0;

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};

    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_058_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_058_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

void *client_thread_059_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 普通同步更新数据
    TestGmcUpdateVertexByIndexKey(
        stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *client_thread_059_02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 普通同步更新数据
    TestGmcUpdateVertexByIndexKey(
        stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/* ****************************************************************************
  Description  : Tree模型并发更新相同数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_059)
{
    int ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};

    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_059_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_059_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

void *client_thread_060_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;

    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        uint32_t repeatcount = 10;
        while (repeatcount > 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                repeatcount--;
            } else {
                printf("-----GmcDeleteVertexByIndexKey1=%d------\n");
                break;
            }
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *client_thread_060_02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;

    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        uint32_t repeatcount = 10;
        while (repeatcount > 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                repeatcount--;
            } else {
                printf("-----GmcDeleteVertexByIndexKey2=%d------\n");
                break;
            }
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/* ****************************************************************************
  Description  : Tree模型并发删除相同数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_060)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};

    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_060_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_060_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, false);
}

/* ****************************************************************************
  Description  : Tree模型truncate数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_061)
{
    int ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = GmcTruncateVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, false);
}

void *client_thread_062_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *client_thread_062_02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        uint32_t repeatcount = 10;
        while (repeatcount > 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                repeatcount--;
                printf("-----GmcDeleteVertexByIndexKey2=%d------\n");
                sleep(1);
            } else {
                break;
            }
        }
    }

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/* ****************************************************************************
  Description  : Tree模型并发写入与删除数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_062)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};

    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_062_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_062_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    // 读取顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            TestGmcDirectFetchVertex(
                stmt, 1, 0, (char *)"string", i, i + 1, array_num, vector_num, label_name1, lalable_name_PK1, false);
        } else {
            TestGmcDirectFetchVertex(
                stmt, 1, 0, (char *)"string", i, i + 1, array_num, vector_num, label_name1, lalable_name_PK1, true);
        }
    }
}

/* ****************************************************************************
 Description  : GmcNodeGet参数出参node为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_063)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGet获取非node结点
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_064)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"F1", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, (char *)"T1/P0", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1,
        GMC_OPERATION_UPDATE);  //全量更新会自动找第一个元素，增量也会自动找第一个元素，不过会先创建个增量元素
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, (char *)"T3/V0", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1,
        GMC_OPERATION_INSERT);  //全量更新会自动找第一个元素，增量也会自动找第一个元素，不过会先创建个增量元素
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, (char *)"T3/V0", &treenode);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_065)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    /*   // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);
          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          // 读取array节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里正序排序，然后把排序后的数据update进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_066)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    /*
        // 从服务端拿出指定的veter进行数组节点排序
        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            // 升序排序
            GmcOrderDirectionE increase = GMC_ORDER_ASC;
            ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
            ASSERT_EQ(GMERR_OK, ret);

            // 排序后的veter验证

            TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
            TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }

            // 读取vector节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                }
            }

            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // 再从服务端读取数据
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        // 读取顶点
        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
            TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }

            // 读取vector节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                }
            }
        } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里正序排序，然后把排序后的数据replace进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_067)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    /*
        // 从服务端拿出指定的veter进行数组节点排序
        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);

            // 升序排序
            GmcOrderDirectionE increase = GMC_ORDER_ASC;
            ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
            ASSERT_EQ(GMERR_OK, ret);

            // 排序后的veter验证
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
            TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
            // 读取array节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }

            // 读取vector节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                }
            }

            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // 再从服务端读取数据
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        // 读取顶点
        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
            TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }

            // 读取vector节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                }
            }
        } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里正序排序，然后把排序后的数据insert进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_068)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
          ret = testGmcGetLastError(NULL);
          EXPECT_EQ(GMERR_OK, ret);
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在vector数组里正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_069)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          // ret = GmcExecute(stmt);
          ret = GmcExecute(stmt);  //排序后的数据index变了，对应的buff也变了，这个时候只能要GmcReplaceVertex操作

          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在vector数组里正序排序，然后把排序后的数据update进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_070)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);
          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          // ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
          // EXPECT_EQ(GMERR_OK, ret);
          // ret = GmcExecute(stmt);
          ret = GmcExecute(stmt);  //排序后的数据index变了，对应的buff也变了，这个时候只能要GmcReplaceVertex操作
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在vector数组里正序排序，然后把排序后的数据replace进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_071)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在vector数组里正序排序，然后把排序后的数据insert进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_072)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*  //读取数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     ASSERT_EQ(GMERR_OK, ret);

     // 从服务端拿出指定的veter进行数组节点排序
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
         GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);

         // 升序排序
         GmcOrderDirectionE increase = GMC_ORDER_ASC;
         ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
         ASSERT_EQ(GMERR_OK, ret);

         // 排序后的veter验证

         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             }
         }

         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
         ret = testGmcGetLastError(NULL);
         EXPECT_EQ(GMERR_OK, ret);
     } */
}

/* ****************************************************************************
 Description  : 没有OpenVertex进行排序
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_073)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_WRONG_STMT_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 没有openvetex 获取node会失败 报错
    // 倒序排序
    GmcOrderDirectionE increase = GMC_ORDER_DESC;
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    // 之前openvetex过然后close，没有释放stme，那么stmt会还是存有元数据，此时不openvetex继续排序，会默认对最后一条记录排序，这里是第10条记录
    // ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    // ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < 1; i++) {
        int64_t f0_value = i;
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        TestGmcNodeGetPropertyByName_R(root, 9, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, 9, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 没有设置主键索引进行排序
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_074)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < 1; i++) {
        int64_t f0_value = i;

        // 倒序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";
        // 没有设置索引。不指定对哪条记录排序，调用GmcSortNode返回成功，实际并没有对vetex排序，数据保持不变
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 通过localhash索引进行排序
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_075)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/* ****************************************************************************
 Description  : 没有OpenVertex进行GmcNodeGet
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_076)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_WRONG_STMT_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;

    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGetName参数node为NULL
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_077)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    const char *nodeName = NULL;
    ret = GmcNodeGetName(NULL, &nodeName);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGetName参数nodeName为NULL
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_078)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    const char *nodeName = NULL;
    ret = GmcNodeGetName(treenode, NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里正序排序，写入相同值
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_079)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入顶点   数组成员分别传 1 ，0,2
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeSetPropertyByName_P(t1, i, 0, (char *)"string");
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            if (j == 0) {
                TestGmcNodeSetPropertyByName_A(t2, 1, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeSetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeSetPropertyByName_A(t2, 2, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 插入vector节点

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1, 0, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0, 0, stringA);
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2, 0, stringA);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A14", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringA);
            }
        }
        // 排序后的vetex重新merger进服务端
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 再从服务端读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在vector数组里正序排序，写入相同值
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_080)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入顶点   数组成员分别传 1 ，0,2
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeSetPropertyByName_P(t1, i, 0, (char *)"string");
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            if (j == 0) {
                TestGmcNodeSetPropertyByName_A(t2, 1, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeSetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeSetPropertyByName_A(t2, 2, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 插入vector节点

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1, 0, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0, 0, stringA);
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2, 0, stringA);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"V14", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringA);
            }
        }
        // 排序后的vetex重新merger进服务端
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 再从服务端读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringA);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringA);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在普通子节点里倒序排序
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_081)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 降序排序----普通子节点只有一条record，排序不处理，数据不变
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"P0", increase);
        ASSERT_EQ(GMERR_OK, ret);
        // 排序后的veter验证
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里正序排序，未把排序后的数据merge进服务端，读取服务端数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_082)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 降序排序
        GmcOrderDirectionE increase = GMC_ORDER_DESC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array和vector数组里正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_083)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*  //读取数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     ASSERT_EQ(GMERR_OK, ret);

     // 从服务端拿出指定的veter进行数组节点排序
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
         GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);
         // 升序排序
         GmcOrderDirectionE increase = GMC_ORDER_ASC;
         ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
         ASSERT_EQ(GMERR_OK, ret);
         // 排序后的veter验证

         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点
         ret = GmcNodeGetChild(t1, "T2", &t2);
         EXPECT_EQ(GMERR_OK, ret);
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             }
         }

         // ret = GmcExecute(stmt);
         ret = GmcExecute(stmt);  //排序后的数据index变了，对应的buff也变了，这个时候只能要GmcReplaceVertex操作
         ASSERT_EQ(GMERR_OK, ret);
     }

     // 再从服务端读取数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     ASSERT_EQ(GMERR_OK, ret);

     // 读取顶点
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
         GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);
         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点
         ret = GmcNodeGetChild(t1, "T2", &t2);
         EXPECT_EQ(GMERR_OK, ret);
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             }
         }
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
     }

     // 同步更新数据
     TestGmcUpdateVertexByIndexKey(
         stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1);
     // 读取数据
     TestGmcDirectFetchVertex(
         stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

     // 普通同步删除数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
     ASSERT_EQ(GMERR_OK, ret);
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
     }

     // 读取数据
     TestGmcDirectFetchVertex(
         stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, false);
   */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array和vector数组里倒序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_084)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*  //读取数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     ASSERT_EQ(GMERR_OK, ret);

     // 从服务端拿出指定的veter进行数组节点排序
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
         GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);

         // 倒序排序
         GmcOrderDirectionE increase = GMC_ORDER_DESC;
         ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcGetChildNode(stmt, (char *)"T3", &treenode);
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcNodeSortElement(treenode, (char *)"V0", increase);
         ASSERT_EQ(GMERR_OK, ret);
         // 排序后的veter验证

         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点
         ret = GmcNodeGetChild(t1, "T2", &t2);
         EXPECT_EQ(GMERR_OK, ret);
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             }
         }

         // ret = GmcExecute(stmt);
         ret = GmcExecute(stmt);  //排序后的数据index变了，对应的buff也变了，这个时候只能要GmcReplaceVertex操作
         ASSERT_EQ(GMERR_OK, ret);
     }

     // 再从服务端读取数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     ASSERT_EQ(GMERR_OK, ret);

     // 读取顶点
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
         GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);

         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点
         ret = GmcNodeGetChild(t1, "T2", &t2);
         EXPECT_EQ(GMERR_OK, ret);
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             }
         }

         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
     }
  */
    // 同步更新数据
    TestGmcUpdateVertexByIndexKey(
        stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1);
    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    // 普通同步删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, false);
}

/* ****************************************************************************
 Description  : 通过localhash索引指定vetex进行数组排序
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_085)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*
        for (int i = start_num; i < end_num; i++) {
            //读取顶点
            ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
            ASSERT_EQ(GMERR_OK, ret);

            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            int scan = 0;
            bool eof;
            ret = GmcSetIndexKeyName(stmt, "localhash_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcFetch(stmt, &eof);
            ASSERT_EQ(GMERR_OK, ret);
            if (eof == true) {
                break;
            }
            ASSERT_EQ(false, eof);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);

            // 升序排序
            GmcOrderDirectionE increase = GMC_ORDER_ASC;
            ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
            ASSERT_EQ(GMERR_OK, ret);

            // 排序后的veter验证

            TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
            TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }

            // 读取vector节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                }
            }

            scan++;

            ASSERT_EQ(1, scan);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            // 关闭查询
            GmcResetStmt(stmt);
        }

        // 再从服务端读取数据
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        // 读取顶点
        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
            TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }

            // 读取vector节点

            for (uint32_t j = 0; j < vector_num; j++) {
                if (j == 0) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
                } else if (j == 1) {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
                } else {
                    ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
                }
            }
        } */
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetType,获取普通子节点32层的type
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_086)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char label_name[] = "test32_deeep";
    char *test_schema = NULL;

    GmcDropVertexLabel(stmt, label_name);

    readJanssonFile("schema_file/thirty-two_depth_node.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt,
        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/"
                "T28/T29/T30",
        &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(treenode, &nodeType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMC_NODE_RECORD, nodeType);

    ret = GmcGetChildNode(stmt,
        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/"
                "T28/T29/T30/T31",
        &treenode);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetType(treenode, &nodeType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMC_NODE_ARRAY, nodeType);

    GmcDropVertexLabel(stmt, label_name);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGetName获取array数组节点的nodeName,Node无效
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_087)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    GmcNodeT *treenode2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
    const char *nodeName = NULL;
    ret = GmcNodeGetName(treenode2, &nodeName);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 调用GmcNodeGet获取array数组节点的node，非根节点传入node name
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_088)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, (char *)"T2", &treenode);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
}

/* ****************************************************************************
 Description  : GmcNodeGet参数stmt为异步stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_095)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_WRONG_STMT_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_async, (char *)"T1", &treenode);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 开启事务：调用GmcSortNode 在array数组里根据int成员正序排序，读取排序后的数据，commit
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_096)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 开启一个事务(cs模式)
          GmcTxConfigT config;
          config.transMode = GMC_TRANS_USED_IN_CS;
          config.type = GMC_TX_ISOLATION_COMMITTED;
          config.readOnly = false;
          ret = GmcTransStart(conn, &config);
          ASSERT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcTransCommit(conn);
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/*****************************************************************************
 Description  : 开启事务：调用GmcSortNode 在array数组里根据int成员正序排序，读取排序后的数据，rollback
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_097)
{
    int32_t ret = 0;
    char *test_schema_097 = NULL;
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";
    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_test_op.gmjson", &test_schema_097);
    ASSERT_NE((void *)NULL, test_schema_097);

    ret = GmcCreateVertexLabel(stmt, test_schema_097, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema_097);

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 开启一个事务(cs模式)
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_PESSIMISITIC_TRX;
        ret = GmcTransStart(conn, &config);
        ASSERT_EQ(GMERR_OK, ret);

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }

        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcTransRollBack(conn);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

void *client_thread_098_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;

    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    /*  ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     EXPECT_EQ(GMERR_OK, ret);
     GmcNodeT *root, *t1, *t2, *t3;
     ret = GmcGetRootNode(stmt, &root);
     EXPECT_EQ(GMERR_OK, ret);
     ret = GmcNodeGetChild(root, "T1", &t1);
     EXPECT_EQ(GMERR_OK, ret);
     ret = GmcNodeGetChild(root, "T3", &t3);
     EXPECT_EQ(GMERR_OK, ret);

     // 从服务端拿出指定的veter进行数组节点排序
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         EXPECT_EQ(GMERR_OK, ret);

         GmcNodeT *treenode;
         char *stringA = (char *)"aaaaaa";
         char *stringB = (char *)"bbbbbb";
         char *stringC = (char *)"cccccc";

         // 升序排序
         GmcOrderDirectionE increase = GMC_ORDER_ASC;
         ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
         EXPECT_EQ(GMERR_OK, ret);

         // 排序后的veter验证
         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点
         ret = GmcNodeGetChild(t1, "T2", &t2);
         EXPECT_EQ(GMERR_OK, ret);
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             }
         }

         ret = GmcExecute(stmt);
         EXPECT_EQ(GMERR_OK, ret);
     }

     EXPECT_EQ(GMERR_OK, ret);
  */
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *client_thread_098_02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;

    int32_t ret = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    /*    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
       EXPECT_EQ(GMERR_OK, ret);

       // 从服务端拿出指定的veter进行数组节点排序
       for (int i = start_num; i < end_num; i++) {
           int64_t f0_value = i;
           ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcExecute(stmt);
           EXPECT_EQ(GMERR_OK, ret);
           GmcNodeT *root, *t1, *t2, *t3;
           ret = GmcGetRootNode(stmt, &root);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T1", &t1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T3", &t3);
           EXPECT_EQ(GMERR_OK, ret);

           GmcNodeT *treenode;
           char *stringA = (char *)"aaaaaa";
           char *stringB = (char *)"bbbbbb";
           char *stringC = (char *)"cccccc";

           // 升序排序
           GmcOrderDirectionE increase = GMC_ORDER_ASC;
           ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
           EXPECT_EQ(GMERR_OK, ret);

           // 排序后的veter验证
           TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
           TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
           // 读取array节点
           ret = GmcNodeGetChild(t1, "T2", &t2);
           EXPECT_EQ(GMERR_OK, ret);
           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                   GmcNodeGetNextElement(t2, &t2);
               } else if (j == 1) {
                   TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                   GmcNodeGetNextElement(t2, &t2);
               } else {
                   TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                   GmcNodeGetNextElement(t2, &t2);
               }
           }

           // 读取vector节点
           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
               } else if (j == 1) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
               } else {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
               }
           }

           ret = GmcExecute(stmt);
           EXPECT_EQ(GMERR_OK, ret);
       }

       EXPECT_EQ(GMERR_OK, ret); */

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

/* ****************************************************************************
  Description  : 调用GmcSortNode，并发排序
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_098)
{
    int ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};

    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_098_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_098_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    /*  // 再从服务端读取数据
     ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
     ASSERT_EQ(GMERR_OK, ret);

     // 读取顶点
     for (int i = start_num; i < end_num; i++) {
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         ASSERT_EQ(GMERR_OK, ret);
         GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);

         TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
         TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
         // 读取array节点
         ret = GmcNodeGetChild(t1, "T2", &t2);
         EXPECT_EQ(GMERR_OK, ret);
         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                 GmcNodeGetNextElement(t2, &t2);
             } else if (j == 1) {
                 TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                 GmcNodeGetNextElement(t2, &t2);
             } else {
                 TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                 GmcNodeGetNextElement(t2, &t2);
             }
         }

         // 读取vector节点

         for (uint32_t j = 0; j < vector_num; j++) {
             if (j == 0) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
             } else if (j == 1) {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
             } else {
                 ret = GmcNodeGetElementByIndex(t3, j, &t3);
                 EXPECT_EQ(GMERR_OK, ret);
                 TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
             }
         }
     } */
}

/* ****************************************************************************
 Description  : 全表扫描的vetex进行排序
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_099)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    //读取数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 从服务端拿出指定的veter进行数组节点排序

    int scan = 0;
    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        ASSERT_EQ(false, eof);
        scan++;

        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }
    }
    ASSERT_EQ(end_num, scan);

    // 关闭查询
    GmcResetStmt(stmt);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 排序之后调用GmcReplaceVertex操作
    // 全表扫描拿出来的vetex不能做replace操作，会破坏里面的stmt,replace后继续fetch会报14000

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        if (i == 0) {
            ASSERT_EQ(GMERR_OK, ret);
        } else {
            printf("ret=%d,num=%d\n", ret, i);
            break;
        }

        if (eof == true) {
            break;
        }
        ASSERT_EQ(false, eof);
        scan++;
        // 升序排序
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(treenode, (char *)"A0", increase);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
        TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
            } else if (j == 1) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
            } else {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
            }
        }

        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 关闭查询
    GmcResetStmt(stmt);
}

/* ****************************************************************************
 Description  : 各层filed_name重复,插入数据
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_100)
{
    int32_t ret = 0;

    GmcNodeT *treenode;

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_test_filed_name.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);

    // 插入顶点   数组成员分别传 1 ，0,2
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            if (j == 0) {

                int64_t f0_value = 1;
                ret = GmcNodeSetPropertyByName(t2, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
                ASSERT_EQ(GMERR_OK, ret);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                int64_t f0_value = 0;
                ret = GmcNodeSetPropertyByName(t2, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
                ASSERT_EQ(GMERR_OK, ret);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                int64_t f0_value = 2;
                ret = GmcNodeSetPropertyByName(t2, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
                ASSERT_EQ(GMERR_OK, ret);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 插入vector节点

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = 1;
        ret = GmcNodeSetPropertyByName(t3, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        f0_value = 0;
        ret = GmcNodeSetPropertyByName(t3, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        f0_value = 2;
        ret = GmcNodeSetPropertyByName(t3, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        bool isNull;
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                int64_t f0_value;
                ret = GmcNodeGetPropertyByName(t2, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(1, f0_value);
                GmcNodeGetNextElement(t2, &t2);
            } else if (j == 1) {
                int64_t f0_value;
                ret = GmcNodeGetPropertyByName(t2, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(0, f0_value);
                GmcNodeGetNextElement(t2, &t2);
            } else {
                int64_t f0_value;
                ret = GmcNodeGetPropertyByName(t2, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(2, f0_value);
                GmcNodeGetNextElement(t2, &t2);
            }
        }

        // 读取vector节点

        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 0) {
                int64_t f0_value;
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t3, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(1, f0_value);
            } else if (j == 1) {
                int64_t f0_value;
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t3, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(0, f0_value);
            } else {
                int64_t f0_value;
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetPropertyByName(t3, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(2, f0_value);
            }
        }
    }
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里根据float正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_101)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"A9", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里根据double正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_102)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"A10", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里根据time正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_103)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*    //读取数据
       ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
       ASSERT_EQ(GMERR_OK, ret);

       // 从服务端拿出指定的veter进行数组节点排序
       for (int i = start_num; i < end_num; i++) {
           int64_t f0_value = i;
           ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
           ASSERT_EQ(GMERR_OK, ret);
           ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcExecute(stmt);
           ASSERT_EQ(GMERR_OK, ret);
           GmcNodeT *root, *t1, *t2, *t3;
           ret = GmcGetRootNode(stmt, &root);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T1", &t1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T3", &t3);
           EXPECT_EQ(GMERR_OK, ret);

           // 升序排序
           GmcOrderDirectionE increase = GMC_ORDER_ASC;
           ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
           ASSERT_EQ(GMERR_OK, ret);
           ret = GmcNodeSortElement(treenode, (char *)"A11", increase);
           ASSERT_EQ(GMERR_OK, ret);

           // 排序后的veter验证
           TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
           TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
           // 读取array节点
           ret = GmcNodeGetChild(t1, "T2", &t2);
           EXPECT_EQ(GMERR_OK, ret);
           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                   GmcNodeGetNextElement(t2, &t2);
               } else if (j == 1) {
                   TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                   GmcNodeGetNextElement(t2, &t2);
               } else {
                   TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                   GmcNodeGetNextElement(t2, &t2);
               }
           }

           // 读取vector节点

           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
               } else if (j == 1) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
               } else {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
               }
           }

           ret = GmcExecute(stmt);
           ASSERT_EQ(GMERR_OK, ret);
       }

       // 再从服务端读取数据
       ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
       ASSERT_EQ(GMERR_OK, ret);

       // 读取顶点
       for (int i = start_num; i < end_num; i++) {
           int64_t f0_value = i;
           ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
           ASSERT_EQ(GMERR_OK, ret);
           ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcExecute(stmt);
           ASSERT_EQ(GMERR_OK, ret);
           GmcNodeT *root, *t1, *t2, *t3;
           ret = GmcGetRootNode(stmt, &root);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T1", &t1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T3", &t3);
           EXPECT_EQ(GMERR_OK, ret);

           TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
           TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
           // 读取array节点
           ret = GmcNodeGetChild(t1, "T2", &t2);
           EXPECT_EQ(GMERR_OK, ret);
           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                   GmcNodeGetNextElement(t2, &t2);
               } else if (j == 1) {
                   TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                   GmcNodeGetNextElement(t2, &t2);
               } else {
                   TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                   GmcNodeGetNextElement(t2, &t2);
               }
           }

           // 读取vector节点

           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
               } else if (j == 1) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
               } else {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
               }
           }
       } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里根据char正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_104)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*   //读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 从服务端拿出指定的veter进行数组节点排序
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          // 升序排序
          GmcOrderDirectionE increase = GMC_ORDER_ASC;
          ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcNodeSortElement(treenode, (char *)"A12", increase);
          ASSERT_EQ(GMERR_OK, ret);

          // 排序后的veter验证
          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }

          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
      }

      // 再从服务端读取数据
      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);

      // 读取顶点
      for (int i = start_num; i < end_num; i++) {
          int64_t f0_value = i;
          ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
          ASSERT_EQ(GMERR_OK, ret);
          ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcExecute(stmt);
          ASSERT_EQ(GMERR_OK, ret);
          GmcNodeT *root, *t1, *t2, *t3;
          ret = GmcGetRootNode(stmt, &root);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T1", &t1);
          EXPECT_EQ(GMERR_OK, ret);
          ret = GmcNodeGetChild(root, "T3", &t3);
          EXPECT_EQ(GMERR_OK, ret);

          TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
          TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
          // 读取array节点
          ret = GmcNodeGetChild(t1, "T2", &t2);
          EXPECT_EQ(GMERR_OK, ret);
          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                  GmcNodeGetNextElement(t2, &t2);
              } else if (j == 1) {
                  TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                  GmcNodeGetNextElement(t2, &t2);
              } else {
                  TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                  GmcNodeGetNextElement(t2, &t2);
              }
          }

          // 读取vector节点

          for (uint32_t j = 0; j < vector_num; j++) {
              if (j == 0) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
              } else if (j == 1) {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
              } else {
                  ret = GmcNodeGetElementByIndex(t3, j, &t3);
                  EXPECT_EQ(GMERR_OK, ret);
                  TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
              }
          }
      } */
}

/* ****************************************************************************
 Description  : 调用GmcSortArrayNode 在array数组里根据uchar正序排序，然后把排序后的数据merge进服务端
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
**************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_105)
{
    int32_t ret = 0;

    GmcNodeT *treenode;
    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";
    // 普通同步插入数据
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertexdifferent(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
    /*    //读取数据
       ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
       ASSERT_EQ(GMERR_OK, ret);

       // 从服务端拿出指定的veter进行数组节点排序
       for (int i = start_num; i < end_num; i++) {
           int64_t f0_value = i;
           ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
           ASSERT_EQ(GMERR_OK, ret);
           ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcExecute(stmt);
           ASSERT_EQ(GMERR_OK, ret);
           GmcNodeT *root, *t1, *t2, *t3;
           ret = GmcGetRootNode(stmt, &root);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T1", &t1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T3", &t3);
           EXPECT_EQ(GMERR_OK, ret);

           // 升序排序
           GmcOrderDirectionE increase = GMC_ORDER_ASC;
           ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
           ASSERT_EQ(GMERR_OK, ret);
           ret = GmcNodeSortElement(treenode, (char *)"A13", increase);
           ASSERT_EQ(GMERR_OK, ret);

           // 排序后的veter验证
           TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
           TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
           // 读取array节点
           ret = GmcNodeGetChild(t1, "T2", &t2);
           EXPECT_EQ(GMERR_OK, ret);
           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                   GmcNodeGetNextElement(t2, &t2);
               } else if (j == 1) {
                   TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                   GmcNodeGetNextElement(t2, &t2);
               } else {
                   TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                   GmcNodeGetNextElement(t2, &t2);
               }
           }

           // 读取vector节点

           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
               } else if (j == 1) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
               } else {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
               }
           }

           ret = GmcExecute(stmt);
           ASSERT_EQ(GMERR_OK, ret);
       }

       // 再从服务端读取数据
       ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
       ASSERT_EQ(GMERR_OK, ret);

       // 读取顶点
       for (int i = start_num; i < end_num; i++) {
           int64_t f0_value = i;
           ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
           ASSERT_EQ(GMERR_OK, ret);
           ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcExecute(stmt);
           ASSERT_EQ(GMERR_OK, ret);
           GmcNodeT *root, *t1, *t2, *t3;
           ret = GmcGetRootNode(stmt, &root);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T1", &t1);
           EXPECT_EQ(GMERR_OK, ret);
           ret = GmcNodeGetChild(root, "T3", &t3);
           EXPECT_EQ(GMERR_OK, ret);

           TestGmcNodeGetPropertyByName_R(root, i, 0, (char *)"string");
           TestGmcNodeGetPropertyByName_p(t1, i, 0, (char *)"string");
           // 读取array节点
           ret = GmcNodeGetChild(t1, "T2", &t2);
           EXPECT_EQ(GMERR_OK, ret);
           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   TestGmcNodeGetPropertyByName_A(t2, 0, 0, stringA);
                   GmcNodeGetNextElement(t2, &t2);
               } else if (j == 1) {
                   TestGmcNodeGetPropertyByName_A(t2, 1, 0, stringB);
                   GmcNodeGetNextElement(t2, &t2);
               } else {
                   TestGmcNodeGetPropertyByName_A(t2, 2, 0, stringC);
                   GmcNodeGetNextElement(t2, &t2);
               }
           }

           // 读取vector节点

           for (uint32_t j = 0; j < vector_num; j++) {
               if (j == 0) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 1, 0, stringB);
               } else if (j == 1) {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 0, 0, stringA);
               } else {
                   ret = GmcNodeGetElementByIndex(t3, j, &t3);
                   EXPECT_EQ(GMERR_OK, ret);
                   TestGmcNodeGetPropertyByName_V(t3, 2, 0, stringC);
               }
           }
       } */
}

/* ****************************************************************************
  Description  : 调用GmcSortNode根据bitmap类型数组成员正序排序，读取排序后的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_106)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_test_bitmap_bitfield.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 升序排序
    GmcOrderDirectionE increase = GMC_ORDER_ASC;
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, (char *)"A17", increase);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : 调用GmcSortNode根据bitfiled类型数组成员正序排序，读取排序后的数据
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_107)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    GmcNodeT *treenode;

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_test_bitmap_bitfield.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 升序排序
    GmcOrderDirectionE increase = GMC_ORDER_ASC;
    ret = GmcGetChildNode(stmt, (char *)"T1/T2", &treenode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, (char *)"A18", increase);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
  Description  : filed命名除了不能加“.”，其他无限制
  Input        : None
  Output       : None
  Return Value :
  Notes        :
  History      :
  Author       : qinjianhua wx620469
  Modification :
  **************************************************************************** */
TEST_F(TreeModelEnhanced_test, DML_034_108)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    char *string_filed_R = (char *)"123F1_char@@ 123-123F1_char@@ 123-123F1_char@@ 123-123F1_char@@ 123F1_char@@ "
                                   "123-123F1_char@@ 123-123F1_char@@ 123-123F1_char@@";
    char *string_filed_P = (char *)"T1/123F1_char@@ 123-123F1_char@@ 123-123F1_char@@ 123-123F1_char@@ 123F1_char@@ "
                                   "123-123F1_char@@ 123-123F1_char@@ 123-123F1_char@@";
    char *string_filed_A = (char *)"T1/T2/123F1_char@@ 123-123F1_char@@ 123-123F1_char@@ 123-123F1_char@@ 123F1_char@@ "
                                   "123-123F1_char@@ 123-123F1_char@@ 123-123F1_char@@";
    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_Naming_Rules.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    free(test_schema1);
#if 0
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
              GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        int8_t value_8 = i % 128;
        uint8_t value_u8 = i % 256;
        int16_t value_16 = i % 32768;
        uint16_t value_u16 = i % 65566;

        int64_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t f1_value = i;
        ret = GmcNodeSetPropertyByName(root, string_filed_R, GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);
        int64_t p0_value = i;
        ret = GmcNodeSetPropertyByName(root, string_filed_P, GMC_DATATYPE_INT64, &p0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);

        // 插入array节点
        	        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {

            int64_t a0_value = j;
            ret = GmcNodeSetPropertyByName(t1, string_filed_A, GMC_DATATYPE_INT64, &a0_value, sizeof(int64_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(t2, &t2);
        }

        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
               ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
               EXPECT_EQ(GMERR_OK, ret);
               ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
                     GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint64_t f1_value;
        ret = GmcNodeGetPropertyByName(stmt, string_filed_R, &f1_value, sizeof(uint64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        ASSERT_EQ(i, f1_value);

        int64_t p0_value;
        ret = GmcNodeGetPropertyByName(stmt, string_filed_P, &p0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        ASSERT_EQ(i, p0_value);
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            int64_t a0_value;
            ret = GmcNodeGetPropertyByName(stmt, string_filed_A, &a0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(j, a0_value);
            GmcNodeGetNextElement(t2, &t2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);

    //更新顶点
    for (int i = start_num; i < end_num; i++) {
        int8_t value_8 = i % 128;
        uint8_t value_u8 = i % 256;
        int16_t value_16 = i % 32768;
        uint16_t value_u16 = i % 65566;
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t f1_value = 2 * i;
        ret = GmcNodeSetPropertyByName(root, string_filed_R, GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);
        int64_t p0_value = 2 * i;
        ret = GmcNodeSetPropertyByName(root, string_filed_P, GMC_DATATYPE_INT64, &p0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);

        // 插入array节点

        for (uint32_t j = 0; j < array_num; j++) {
            int64_t a0_value = 2 * j;
            ret = GmcNodeSetPropertyByName(t2, string_filed_A, GMC_DATATYPE_INT64, &a0_value, sizeof(int64_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(t2, &t2);
        }

        ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
               ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
               EXPECT_EQ(GMERR_OK, ret);
               ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
                     GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        uint64_t f1_value;
        ret = GmcNodeGetPropertyByName(stmt, string_filed_R, &f1_value, sizeof(uint64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        ASSERT_EQ(2 * i, f1_value);

        int64_t p0_value;
        ret = GmcNodeGetPropertyByName(stmt, string_filed_P, &p0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        ASSERT_EQ(2 * i, p0_value);
        // 读取array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            int64_t a0_value;
            ret = GmcNodeGetPropertyByName(stmt, string_filed_A, &a0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(2 * j, a0_value);
            GmcNodeGetNextElement(t2, &t2);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    // 普通同步删除数据

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt,  lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
}

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_Naming_Rules2.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema1);

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/TreeModelEnhanced_Naming_Rules.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);
#endif

    readJanssonFile("schema_file/TreeModelEnhanced_test_op.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);
}
