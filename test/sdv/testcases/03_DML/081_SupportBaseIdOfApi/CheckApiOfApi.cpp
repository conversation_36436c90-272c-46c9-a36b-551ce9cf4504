extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "base_id_of_api.h"

using namespace std;

class CheckApiSafetyBaseIdOfApi : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_async, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
        g_conn_sync = NULL;
        g_stmt_sync = NULL;
        g_conn_async = NULL;
        g_stmt_async = NULL;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CheckApiSafetyBaseIdOfApi::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_WRONG_STMT_OBJECT);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
}
void CheckApiSafetyBaseIdOfApi::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.GmcSetVertexPropertyById的stmt参数传入NULL
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_001)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    ret = GmcSetVertexPropertyById(NULL, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.GmcSetVertexPropertyById的id参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_002)
{
    bool isFinish, isNull;
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    ret = GmcSetVertexPropertyById(g_stmt_sync, -1, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.GmcSetVertexPropertyById的type参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_003)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_STRING, &c0Node, sizeof(c0Node));
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.GmcSetVertexPropertyById的value参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_004)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    const char *invalidVal = "  ";
    ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &invalidVal, sizeof(invalidVal));
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.GmcSetVertexPropertyById的valueSize参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_005)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_STRING, &c0Node, -999999999);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.GmcGetVertexPropertySizeById的id参数传入负数
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_006)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    ret = GmcGetVertexPropertySizeById(g_stmt_sync, -1, &size);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.GmcGetVertexPropertySizeById的stmt参数传入NULL
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_007)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    ret = GmcGetVertexPropertySizeById(NULL, 0, &size);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.GmcGetVertexPropertySizeById的id参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_008)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    ret = GmcGetVertexPropertySizeById(g_stmt_sync, 0xffffffff, &size);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.GmcGetVertexPropertySizeById的propSize参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_009)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    ret = GmcGetVertexPropertySizeById(g_stmt_sync, 0, 0);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);

    ret = GmcGetVertexPropertySizeById(g_stmt_sync, 0, NULL);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish, isNull;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.GmcGetVertexPropertyById的stmt参数传入NULL
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_010)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;

    uint32_t propVal = 0;
    bool isFinish, isNull;
    ret = GmcGetVertexPropertyById(NULL, 0, &propVal, sizeof(propVal), &isNull);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011.GmcGetVertexPropertyById的id参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_011)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;

    uint32_t propVal = 0;
    bool isFinish, isNull;
    ret = GmcGetVertexPropertyById(g_stmt_sync, -1, &propVal, sizeof(propVal), &isNull);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.GmcGetVertexPropertyById的propValue参数传入NULL
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_012)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;

    uint32_t propVal = 0;
    bool isFinish, isNull;
    ret = GmcGetVertexPropertyById(g_stmt_sync, 0, NULL, sizeof(propVal), &isNull);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013.GmcGetVertexPropertyById的propSize参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_013)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;

    uint32_t propVal = 0;
    bool isFinish, isNull;
    ret = GmcGetVertexPropertyById(g_stmt_sync, 0, &propVal, -999999, &isNull);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);

    ret = GmcGetVertexPropertyById(g_stmt_sync, 0, &propVal, 0, &isNull);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014.GmcGetVertexPropertyById的isNull参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_014)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;

    uint32_t propVal = 0;
    bool isFinish, isNull;
    ret = GmcGetVertexPropertyById(g_stmt_sync, 0, &propVal, sizeof(propVal), NULL);
    EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_WRONG_STMT_OBJECT);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 015.GmcNodeSetPropertyById的node参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_015)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;
    ret = GmcNodeSetPropertyById(NULL, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 016.GmcNodeSetPropertyById的id参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_016)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;
    ret = GmcNodeSetPropertyById(node, -1, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.GmcNodeSetPropertyById的type参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_017)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;
    ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_STRING, &h1Node, sizeof(h1Node));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.GmcNodeSetPropertyById的value参数传入非法值NULL
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_018)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;
    ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, NULL, sizeof(h1Node));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, 0, sizeof(h1Node));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.GmcNodeSetPropertyById的size参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_019)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;
    ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, 0);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 020.GmcNodeGetPropertySizeById的node参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_020)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;

    ret = GmcNodeGetPropertySizeById(nullptr, 1, &size);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.GmcNodeGetPropertySizeById的id参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_021)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;

    ret = GmcNodeGetPropertySizeById(node, -1, &size);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.GmcNodeGetPropertySizeById的size参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_022)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;

    ret = GmcNodeGetPropertySizeById(node, 1, nullptr);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetPropertySizeById(node, 1, 0);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023.GmcNodeGetPropertyById的node参数传入非法值
TEST_F(CheckApiSafetyBaseIdOfApi, DML_081_01_023)
{
    GmcNodeT *node = NULL;
    char *test_schema1 = NULL;
    const char *labelName = "idApiSafetyVertex";
    uint32_t record = 1;
    uint32_t c0Node, c1Node, h1Node, h2Node;
    uint32_t size = 0;
    bool isFinish, isNull;
    uint32_t propVal = 0;
    ret = GmcNodeGetPropertyById(NULL, 0, &propVal, sizeof(propVal), &isNull);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < record; i++) {
        c0Node = i, c1Node = i + 100;
        ret = GmcSetVertexPropertyById(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Node = j, h2Node = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Node, sizeof(h1Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c1Node = 1024;
    ret = GmcSetVertexPropertyById(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &c1Node, sizeof(c1Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(g_stmt_sync, nodeSrting3, &node);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Node = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Node, sizeof(h2Node));
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    c0Node = 0;
    ret = GmcSetIndexKeyId(g_stmt_sync, 0);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &c0Node, sizeof(c0Node));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
