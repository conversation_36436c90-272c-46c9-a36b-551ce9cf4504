extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

using namespace std;

int32_t ret = 0;
const char *expect = NULL;
const char *g_label_name = "BatchInsertVertex";
const char *g_label_config = "{\"max_record_num\":300000}";
const char *batch_label_name_resource = "TestBatchResource";
const char *batch_label_name = "TestBatchOp";
const char *batch_label_name2 = "TestBatchOp2";
const char *batch_label_config = R"({"max_vertex_num":2000})";
const char *string_schemal_json =
    R"([{
        "type":"record",
        "name":"TestBatchOp",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"TestBatchOp",
                    "name":"TestBatchOp_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

const char *string_schemal_json2 =
    R"([{
    "type":"record",
    "name":"TestBatchOp2",
    "fields":
        [
            {"name":"F0", "type":"int32", "nullable":false},
            {"name":"F1", "type":"int32", "nullable":false},
            {"name":"F2", "type":"int32", "nullable":false},
            {"name":"F3", "type":"int32", "nullable":false}],
    "keys":
        [
            {
                "node":"TestBatchOp2",
                "name":"TestBatchOp_K2",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{ "unique":true}
            }
        ]
    }])";
char *gBatchLabelSchema = NULL;
const char *labelNameT = "KEY_S0";
const char *pkLabelName = "KEY_PK";
const char *g_primary_key = "T35_PK";
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;
#define DEFINE_RESULT_FAILED 1

void TestSetVertexPropertyGeneral(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[10] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexPropertyResource(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    uint64_t respoolId = 0xFFFF;
    uint64_t count = 2;
    uint64_t startIndex = 0xFFFFFFFF;

    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[10] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    EXPECT_EQ(GMERR_OK, ret);
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    EXPECT_EQ(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    EXPECT_EQ(GMERR_OK, ret);
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexPropertyUpdateGeneral(GmcStmtT *stmt, int pk = 0, bool is_union_pk = 0, bool is_bytes_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = (1 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = (10 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = (100 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
#ifdef PK_FORBID_UPDATE  //不能更新主键字段
    if (!is_union_pk) {
        int32_t value6 = pk;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (!is_bytes_union_pk) {
        char teststr15[10] = "bytes";
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexPKPropertyValue(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetVertexPropertyValue(GmcStmtT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestQueryVertexProperty(GmcStmtT *stmt, int index, bool bool_value, char *f14_value)
{
    int ret = 0;
    uint64_t f1_value = 1 * index;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * index;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &f2_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * index;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value);
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * index;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * index;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value);
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * index;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &f6_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * index;
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value);
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value);
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * index;
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value);
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * index;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * index;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &f11_value);
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 12 * index;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * index;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, f14_value);
    EXPECT_EQ(GMERR_OK, ret);
}

void CreateVertexLabelPdeal()
{
    system("sh scripts/batch_create_label.sh 1024");
}

void CreateVertexLabelPdealLittle()
{
    system("sh scripts/batch_create_label.sh 256");
}

void *batch_insert_scan_pthread3(void *args)
{
    const char *strBatchLabelName2 = "TBL_K0";
    const char *strBatchLabelVertexPK2 = "TBL_PK";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 1024;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, strBatchLabelName2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        TestSetVertexPKPropertyValue(stmt, i);
        TestSetVertexPropertyValue(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, strBatchLabelName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, strBatchLabelVertexPK2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(stmt, i, 0, (char *)"string");
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void TestSetVertexNodesProperty(
    GmcStmtT *stmt, int i, bool bool_value, char *f14_value, uint64_t respoolId, uint64_t count, uint64_t startIndex)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexPropertyCurrent(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    uint64_t respoolId = 0xFFFF;
    uint64_t count = 2;
    uint64_t startIndex = 0xFFFFFFFF;

    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[10] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    EXPECT_EQ(GMERR_OK, ret);
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    EXPECT_EQ(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    EXPECT_EQ(GMERR_OK, ret);
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    EXPECT_EQ(GMERR_OK, ret);
}
