/*****************************************************************************
 Description  : 清除子节点兼容V3特性功能用例
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/06/01
*****************************************************************************/
#include "ClearNodeTest.h"

char *labelJson2 = NULL;
char *g_labelJson3 = NULL;
class ClearNodeFuncTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ClearNodeFuncTest::SetUpTestCase()
{
    printf("[INFO] ClearNodeFuncTest Start.\n");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void ClearNodeFuncTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] ClearNodeFuncTest End.\n");
}

void ClearNodeFuncTest::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, labelName1);
    // 保证兼容V3配置项打开
    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    readJanssonFile("schemaFile/TreeModelOneLevelNoMemkey.gmjson", &labelJson1);
    ASSERT_NE((void *)NULL, labelJson1);
    readJanssonFile("schemaFile/TreeModelTwoLevel.gmjson", &labelJson2);
    ASSERT_NE((void *)NULL, labelJson2);
    readJanssonFile("schemaFile/TreeModelTwoLevelHaveMemkey.gmjson", &g_labelJson3);
    ASSERT_NE((void *)NULL, g_labelJson3);
    AW_CHECK_LOG_BEGIN();
}

void ClearNodeFuncTest::TearDown()
{
    AW_CHECK_LOG_END();
    free(labelJson1);
    free(labelJson2);
    free(g_labelJson3);
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 001.清空普通record
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_001)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexOneLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value, false, checkNull);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 002.清空array
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_002)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexOneLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // array
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false, checkNull);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 003.清空vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_003)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexOneLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // array
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 004.同时清空普通record/array/vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_004)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexOneLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1002);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeClear(T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value, false, checkNull);

        // array
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false, checkNull);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 005.数组嵌套，清空一层的普通record
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_005)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value, false, checkNull);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T5, &T5);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 006.数组嵌套，清空一层的array
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_006)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T5, &T5);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 007.数组嵌套，清空一层的vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_007)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 008.数组嵌套，同时清空一层的普通的record/array/vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_008)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);
 
        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value, false, checkNull);
 
        // array
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 009.数组嵌套，清空2层的普通record
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_009)
{
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelTwoLevelEle.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;  // array下的record
    GmcNodeT *T4;
    GmcNodeT *T103;
    GmcNodeT *T104;  // vector下的record
    GmcNodeT *T105;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T3);
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T104);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        unsigned int nodeSize;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T4, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, nodeSize);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T105, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(vector_num, nodeSize);
        }

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T3, pk * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T103, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T104, pk * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T105, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T105, &T105);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T103, &T103);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 010.数组嵌套，清空2层的array
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_010)
{
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelTwoLevelEle.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;  // array下的record
    GmcNodeT *T4;
    GmcNodeT *T103;
    GmcNodeT *T104;  // vector下的record
    GmcNodeT *T105;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T4);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        unsigned int nodeSize;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T4, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, nodeSize);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T105, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(vector_num, nodeSize);
        }

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T3, pk * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T103, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T104, pk * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T105, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T105, &T105);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T103, &T103);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 011.数组嵌套，清空2层的vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_011)
{
        char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelTwoLevelEle.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;  // array下的record
    GmcNodeT *T4;
    GmcNodeT *T103;
    GmcNodeT *T104;  // vector下的record
    GmcNodeT *T105;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T105);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        unsigned int nodeSize;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T4, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, nodeSize);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T105, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, nodeSize);
        }
        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T3, pk * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T103, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T104, pk * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(T105, 0, &T105);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T103, &T103);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 012.数组嵌套，同时清空2层的普通的record/array/vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_012)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelTwoLevelEle.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;  // array下的record
    GmcNodeT *T4;
    GmcNodeT *T103;
    GmcNodeT *T104;  // vector下的record
    GmcNodeT *T105;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T4);
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T104);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T105);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        unsigned int nodeSize;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T4, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, nodeSize);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T105, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, nodeSize);
        }
        // 读array节点
        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T3, pk * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T103, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T104, pk * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(T105, 0, &T105);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T103, &T103);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 013. clear一个空的array节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_013)
{
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelOneLevelNoMK.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    // insert vertex
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1002, i * index, bool_value, f14_value);

        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // clear node

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);

        char *nodeName = (char *)"T1.T2";
        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // array
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false, checkNull);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 014. clear一个空的普通record节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_014)
{
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelOneLevelNoMK.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;

    // insert vertex
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // clear node

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value, false, checkNull);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 015. clear一个空的vector节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_015)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelOneLevelNoMK.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    // insert vertex
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1002, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // clear node

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // array
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // vector
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 016.clear不同层的array和vector
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_016)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelTwoLevelEle.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;  // array下的record
    GmcNodeT *T4;
    GmcNodeT *T103;
    GmcNodeT *T104;  // vector下的record
    GmcNodeT *T105;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T105);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        unsigned int nodeSize;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T4, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, nodeSize);
        }
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T103, j, &T103);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T105, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, nodeSize);
        }

        // 读array节点
        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T3, pk * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T103, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T104", &T104);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T104, pk * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T103, "T105", &T105);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(T105, 0, &T105);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T103, &T103);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 017.clear不同层重名节点  C20不支持重名
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_017)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModelTwoLevelEle.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertex_027(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;  // array下的record
    GmcNodeT *T4;
    GmcNodeT *T103;
    GmcNodeT *T104;  // vector下的record
    GmcNodeT *T105;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(T3);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcNodeClear(T103);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        unsigned int nodeSize;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementCount(T4, &nodeSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(array_num, nodeSize);
        }
        ret = GmcNodeGetElementCount(T103, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);
        // 读array节点
        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T103", &T103);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_p(T3, pk * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        ret = GmcNodeGetElementByIndex(T103, 0, &T103);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 018.32层，清空第一层和32层
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_018)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModel32LevelVector.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // insert vertex
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    char f0_value = 'a';
    char *NodeName32 = (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/"
                               "T25/T26/T27/T28/T29/T30/T31";
    char *NodeName1 = (char *)"T1";
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T31;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T31, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T31, (char *)"P0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // clear node
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // check null
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int nodeSize;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    // check size
    ret = GmcNodeGetElementCount(T31, &nodeSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, nodeSize);

    // vector
    ret = GmcNodeGetElementByIndex(T31, 0, &T31);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 019.32层，清空第32层
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_019)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/TreeModel32LevelVector.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // insert vertex
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    char f0_value = 'a';
    char *NodeName32 = (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/"
                               "T25/T26/T27/T28/T29/T30/T31";
    char *NodeName1 = (char *)"T1";
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T31;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T31, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T31, (char *)"P0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // clear node
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NodeName1, &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(T31);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // check null
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int nodeSize;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, NodeName32, &T31);
    EXPECT_EQ(GMERR_OK, ret);
    // check size
    ret = GmcNodeGetElementCount(T31, &nodeSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, nodeSize);

    // vector
    ret = GmcNodeGetElementByIndex(T31, 0, &T31);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
020.vector节点，1次update操作中先clear父节点尝试操作它的子节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_020)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *ParentNodeName = (char *)"T3";
    char *ChildNodeName = (char *)"T5";
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *Parent;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *Child;
    GmcNodeT *tmp;
    bool new_bool_value = true;
    char *new_f14_value = (char *)"newstr";
    int new_value = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);

        // clear
        ret = GmcNodeClear(Parent);
        EXPECT_EQ(GMERR_OK, ret);

        // update
        uint32_t level1_index = 0;
        ret = GmcNodeGetElementByIndex(Parent, level1_index, &tmp);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = GmcNodeGetChild(Parent, ChildNodeName, &Child);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(Parent, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, nodeSize);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
021.vector节点，1次update操作中先clear子节点然后update/remove/append它的父节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_021)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *ParentNodeName = (char *)"T3";
    char *ChildNodeName = (char *)"T5";
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 1;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    vector_num = 2;
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *Parent;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *Child;
    GmcNodeT *tmp;
    bool new_bool_value = true;
    char *new_f14_value = (char *)"newstr";
    int new_value = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        // clear
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(Parent, j, &Parent);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(Parent, ChildNodeName, &Child);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeClear(Child);
            EXPECT_EQ(GMERR_OK, ret);
        }

        // update
        uint32_t level1_index = 0;
        ret = GmcNodeGetElementByIndex(Parent, level1_index, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(Parent, level1_index, new_bool_value, new_f14_value);

        // remove
        level1_index = 1;
        ret = GmcNodeRemoveElementByIndex(Parent, level1_index);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

        // append
        ret = GmcNodeAppendElement(Parent, &Parent);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(Parent, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(3, nodeSize);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
022.vector节点，1次update操作中先update/remove/append它的父节点然后clear子节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_022)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *ParentNodeName = (char *)"T3";
    char *ChildNodeName = (char *)"T5";
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 1;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    vector_num = 2;
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *Parent;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *Child;
    GmcNodeT *tmp;
    bool new_bool_value = true;
    char *new_f14_value = (char *)"newstr";
    int new_value = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        // update
        uint32_t level1_index = 0;
        ret = GmcNodeGetElementByIndex(Parent, level1_index, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(Parent, level1_index, new_bool_value, new_f14_value);

        // remove
        level1_index = 1;
        ret = GmcNodeRemoveElementByIndex(Parent, level1_index);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        ret = GmcNodeAppendElement(Parent, &Parent);
        EXPECT_EQ(GMERR_OK, ret);

        // clear
        for (uint32_t j = 0; j < vector_num; j++) {
            if (j == 1) {
                ret = GmcNodeGetElementByIndex(Parent, j, &tmp);
                EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);  //增量更新约束remove父节点不能对其直接点做其它操作
            } else {
                ret = GmcNodeGetElementByIndex(Parent, j, &Parent);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetChild(Parent, ChildNodeName, &Child);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeClear(Child);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }

        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(Parent, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, nodeSize);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
023.vector节点，1次update操作中先update/remove/append它的子节点然后clear父节点
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_023)
{

    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char *ParentNodeName = (char *)"T3";
    char *ChildNodeName = (char *)"T5";
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *A_T3;  // array下的record
    GmcNodeT *Parent;
    GmcNodeT *V_T4;  // vector下的record
    GmcNodeT *T4;
    GmcNodeT *Child;
    GmcNodeT *tmp;
    bool new_bool_value = true;
    char *new_f14_value = (char *)"newstr";
    int new_value = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);

        // update
        uint32_t level1_index = 0;
        uint32_t level2_index = 0;
        ret = GmcNodeGetElementByIndex(Parent, level1_index, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(Parent, ChildNodeName, &Child);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(Child, level1_index, new_bool_value, new_f14_value);

        // remove
        level2_index = 1;
        ret = GmcNodeRemoveElementByIndex(Child, level2_index);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        ret = GmcNodeAppendElement(Child, &Child);
        EXPECT_EQ(GMERR_OK, ret);

        // clear
        ret = GmcNodeClear(Parent);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, ParentNodeName, &Parent);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(Parent, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(3, nodeSize);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description :024.树模型多层，从叶子节点向父节点逐层clear；
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_024)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1_T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description :025.对Array节点进行排序，排序后clear；
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_025)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear node
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1_T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcNodeSortElement(T1_T2, (char *)"A7", increase);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // check null
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false, checkNull);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T5, &T5);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description :026.构造三层obj，节点字段均写入值，clear 第三层array节点，重新set该节点，
                预期增量更新后set值不生效，再对该obj进行merge操作，重新写入数据，查询第三层array节点字段值，
                查询后再次clear该array节点；
*******************************************************************************/
TEST_F(ClearNodeFuncTest, DML_055_ClearNodeFuncTest_026)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, g_labelJson3, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    end_num = 100;
    // insert vertex
    TestInsertVertexTwoLevel(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // clear 第3层的array T4 同时set T4，预期update接口成功，但set不生效即T4被clear掉
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T1002;
    GmcNodeT *T3;
    GmcNodeT *T1_T2;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1_T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        //清掉下标为0的T2下的T4节点
        ret = GmcNodeClear(T4);
        EXPECT_EQ(GMERR_OK, ret);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T4, j * index, bool_value, f14_value);
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T4, &T4);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // check null
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                if (j == 0) {
                    //校验下标为0的T2下的T4节点为空
                    TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                } else {
                    TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false);
                }

                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T5, &T5);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    //进行merge操作，将T4的值set上去
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        char PKName_[] = "OP_PK";
        ret = GmcSetIndexKeyName(stmt, PKName_);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    //校验T4和其它节点
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertexTwoLevel(
            stmt, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    // clear T4 again
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1_T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        //清掉下标为0的T2下的T4节点
        ret = GmcNodeClear(T4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // check null
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1002", &T1002);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        // check size
        unsigned int nodeSize;
        ret = GmcNodeGetElementCount(T1002, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, nodeSize);
        ret = GmcNodeGetElementCount(T1_T2, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(array_num, nodeSize);
        ret = GmcNodeGetElementCount(T3, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, nodeSize);

        bool checkNull = true;
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1002, pk * index, bool_value, f14_value);

        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcGetNodePropertyByName_A(T1_T2, j * index, bool_value, f14_value, false);
            ret = GmcNodeGetChild(T1_T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                if (j == 0) {
                    //校验下标为0的T2下的T4节点为空
                    TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false, checkNull);
                } else {
                    TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value, false);
                }

                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T1_T2, &T1_T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //读vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                TestGmcGetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
                if (m < vector_num - 1) {
                    ret = GmcNodeGetNextElement(T5, &T5);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < vector_num - 1) {
                ret = GmcNodeGetNextElement(T3, &T3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}
