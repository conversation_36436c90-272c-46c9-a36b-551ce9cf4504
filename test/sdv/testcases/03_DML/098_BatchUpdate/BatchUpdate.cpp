/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "BatchUpdate.h"

class BatchUpdate : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void BatchUpdate::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void BatchUpdate::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void BatchUpdate::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_pessi, &g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 悲观+读已提交事务配置
    g_msTrxCfgCommitted.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgCommitted.type = GMC_TX_ISOLATION_COMMITTED;
    g_msTrxCfgCommitted.readOnly = false;
    g_msTrxCfgCommitted.trxType = GMC_PESSIMISITIC_TRX;
    
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    // 创建异步连接
    ret = TestYangGmcConnect(&g_conn_async_full, &g_stmt_async_full, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testBatchPrepare(g_conn_sync, &g_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testBatchPrepare(g_conn_async, &g_batchAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}
void BatchUpdate::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    GmcBatchDestroy(g_batch);
    GmcBatchDestroy(g_batchAsync);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_pessi, g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001、轻量化事务、隐式事务、同步、根据主键update操作、简单表、定长key、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/simple_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据 并校验
    SetVertexSimpleTableData(g_stmt_sync, 0, 100);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateVertexSimpleTableData(g_stmt_sync, 0, 100);

    // 更新数据、仅更新一个字段
    for (int i = 0; i < 2; i++) {
        int64_t f0_value = i;
        uint64_t f1_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、轻量化事务、隐式事务、同步、根据主键update操作、简单表、定长key、失败回滚、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/simple_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 1024;
    SetVertexSimpleTableData(g_stmt_sync, startNum, endNum);

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexProperty(g_stmt_sync_pessi, 1111, 1);
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_lableNamePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    ASSERT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(0, successNum);

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexProperty(g_stmt_sync, i , 0);
    }

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_pessi);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync_pessi, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync_pessi, i , 0);
        } else {
            queryVertexProperty(g_stmt_sync_pessi, 1111, 1);
        }
    }

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexProperty(g_stmt_sync, 1111, 1);
        }
    }
    free(g_schemaStruct);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、轻量化事务、隐式事务、同步、根据主键update操作、一般复杂表、变长key、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 1024;
    SetVertexSimpleTableData(g_stmt_sync, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateGeneralComplexTable(g_stmt_sync, startNum, endNum);

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、轻量化事务、隐式事务、同步、根据主键update操作、一般复杂表、变长key、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 1024;
    SetVertexSimpleTableData(g_stmt_sync, startNum, endNum);

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_lableNamePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    ASSERT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(0, successNum);

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexProperty(g_stmt_sync, i , 0);
    }

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_pessi);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync_pessi, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync_pessi, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
        }
    }

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync, 1111, 1, (char *)"string1");
        }
    }
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、轻量化事务、隐式事务、同步、根据主键update操作、简单表、key有32个字段、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/table_005.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName5);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelSimpleName5, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyWithLarFields(g_stmt_sync, i);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelSimpleName5, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = startNum; i < endNum; i++) {
        updateVertexPropertyWithLarFields(g_stmt_sync, i, 3232, (char *)"string3232");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、轻量化事务、隐式事务、同步、根据主键update操作、简单表、key有32个字段、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    readJanssonFile("schemaFile/table_005.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_tableName5);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelSimpleName5, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 1024;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyWithLarFields(g_stmt_sync, i);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_labelSimpleName5, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更改第1000条数据
    updateVertexPropertyWithLarFields(g_stmt_sync_pessi, 1000, 32, (char *)"string32");
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelSimpleName5, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        updateVertexPropertyWithLarFields(g_stmt_sync, i, 3232, (char *)"string3232");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(0, successNum);
    free(g_schemaStruct);

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、轻量化事务、隐式事务、同步、根据主键update操作、一般复杂表、节点嵌套、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table1.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_generalComplexTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_generalComplexTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_generalComplexTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_generalComplexLableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_generalComplexTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 008、轻量化事务、隐式事务、同步、根据主键update操作、一般复杂表、节点嵌套、失败回滚、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table1.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_generalComplexTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_generalComplexTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 1024;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_generalComplexTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexProperty(g_stmt_sync_pessi, 1111, 1);
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_generalComplexLableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_generalComplexTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_generalComplexLableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    ASSERT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(0, successNum);

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_generalComplexTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_generalComplexLableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexProperty(g_stmt_sync, i , 0);
    }

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_generalComplexTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_generalComplexLableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_pessi);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync_pessi, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync_pessi, i , 0);
        } else {
            queryVertexProperty(g_stmt_sync_pessi, 1111, 1);
        }
    }

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_generalComplexTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_generalComplexLableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexProperty(g_stmt_sync, 1111, 1);
        }
    }
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_generalComplexTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、轻量化事务、隐式事务、同步、根据主键update操作、特殊复杂表、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/special_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_specialComplexTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_specialComplexTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 1000;
    for (int i = startNum; i < 1; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_specialComplexTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_specialComplexLalableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、轻量化事务、隐式事务、同步、根据主键update操作、特殊复杂表、失败回滚、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/special_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_specialComplexTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_specialComplexTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 1024;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_specialComplexTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexProperty(g_stmt_sync_pessi, 1111, 1);
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_specialComplexLalableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_specialComplexTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_specialComplexLalableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    ASSERT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(0, successNum);

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_specialComplexTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_specialComplexLalableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexProperty(g_stmt_sync, i , 0);
    }

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_specialComplexTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_specialComplexLalableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_pessi);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync_pessi, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync_pessi, i , 0);
        } else {
            queryVertexProperty(g_stmt_sync_pessi, 1111, 1);
        }
    }

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_specialComplexTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_specialComplexLalableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexProperty(g_stmt_sync, 1111, 1);
        }
    }
    free(g_schemaStruct);

    ret = GmcDropVertexLabel(g_stmt_sync, g_specialComplexTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、轻量化事务、隐式事务、同步、根据localhash更新、一般复杂表、变长key、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testBatchPrepare(g_conn_sync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 根据localhash更新数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"stringlocalhash");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"stringlocalhash");
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 根据local更新数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 3 * i, 0, (char *)"stringlocal");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localkey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localkey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, 3 * i, 0, (char *)"stringlocal");
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 根据hashcluster更新数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 4 * i, 0, (char *)"stringhashcluster");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_hashclusterKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_hashclusterKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, 4 * i, 0, (char *)"stringhashcluster");
    }
    free(g_schemaStruct);

    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、轻量化事务、隐式事务、同步、根据localhash更新、一般复杂表、变长key
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 1024;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_localhashKey);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    ASSERT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(0, successNum);

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync, 1111, 1, (char *)"string1");
        }
    }
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、轻量化事务、隐式事务、同步、member key、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/member_key_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_memberKeyTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_memberKeyTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        SetVertexProperty_MemKey_PK(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_memberKeyTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_memberKeyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_memberKeyTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_memberKeyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
    }
    ret = GmcDropVertexLabel(g_stmt_sync, g_memberKeyTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 异步
// 014、轻量化事务、隐式事务、异步、根据主键update操作、一般复杂表、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    // 建表
    GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_async, g_simpleTableName);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schemaStruct, NULL, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTableAsync(g_stmt_async, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateGeneralComplexTableAsyn(g_stmt_async, startNum, endNum);

    // 读取并比对数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
    }
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、轻量化事务、隐式事务、异步、根据主键update操作、一般复杂表、变长key、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schemaStruct, g_tabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 1024;
    SetGeneralComplexTableAsync(g_stmt_async, startNum, endNum);

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_lableNamePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_async, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_async, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batchAsync, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcBatchExecuteAsync(g_batchAsync, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并比对数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
    }

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_pessi);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync_pessi, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync_pessi, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
        }
    }

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync, 1111, 1, (char *)"string1");
        }
    }
    free(g_schemaStruct);

    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、轻量化事务、隐式事务、异步、根据localhash更新、一般复杂表、变长key、验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_async, g_localhashTable);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schemaStruct, NULL, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_async, i);
        SetVertexPropertyWithStr(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batchAsync, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(g_batchAsync, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根据localhash更新数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_async, 2 * i, 1, (char *)"stringlocalhash");
        ret = GmcSetIndexKeyName(g_stmt_async, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batchAsync, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(g_batchAsync, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并比对数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"stringlocalhash");
    }
    free(g_schemaStruct);

    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、轻量化事务、隐式事务、异步、根据localhash更新、一般复杂表、变长key、验证ok
// 预制1024条数据(0~1023)
// 连接1开启显式读已提交事务, 更新第1000条数据, 事务不提交;
// 连接2批量更新1024条数据, 预期 BatchExecute失败, 报12002
TEST_F(BatchUpdate, DML_098_BatchUpdate_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);

    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schemaStruct, g_tabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 1024;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_async, i);
        SetVertexPropertyWithStr(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batchAsync, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(g_batchAsync, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    // 启动事务
    ret = GmcTransStart(g_conn_pessi, &g_msTrxCfgCommitted);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 1000;
    ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
    ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_localhashKey);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync_pessi);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_async, 2 * i, 1, (char *)"stringlocalhash");
        ret = GmcSetIndexKeyName(g_stmt_async, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batchAsync, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcBatchExecuteAsync(g_batchAsync, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);

    // 读取并比对数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
    }

    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_pessi, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync_pessi, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_pessi, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_pessi);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync_pessi, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync_pessi, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync_pessi, 1111, 1, (char *)"string1");
        }
    }

    // 提交事务
    ret = GmcTransCommit(g_conn_pessi);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 1000) {
            queryVertexProperty(g_stmt_sync, i , 0);
        } else {
            queryVertexPropertyWithStr(g_stmt_sync, 1111, 1, (char *)"string1");
        }
    }
    free(g_schemaStruct);

    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、乐观事务、可重复读（yang）、显示事务、异步、namespace、不支持semi模式 验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t fieldValue = 0;
    AsyncUserDataT userData = {0};

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步/异步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建yang的namespace
    // use namespace
    const char *namespace1 = "Namespace001_1_A";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 乐观+可重复读事务配置
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务，显示事务
    ret = GmcTransStartAsync(g_conn_async, &TrxConfig, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、乐观事务、隔离级别：读已提交（vertex）、隐式事务、同步 验证ok
// TEST_F(OneThreadAsync01_interface_check, Connect_008_002)
TEST_F(BatchUpdate, DML_098_BatchUpdate_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_vertexLabelCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateGeneralComplexTable(g_stmt_sync, startNum, endNum);

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、悲观事务、隔离级别：读已提交（vertex）、隐式事务、同步、根据主键更新 验证ok
// TEST_F(OneThreadAsync01_interface_check, Connect_008_002)
TEST_F(BatchUpdate, DML_098_BatchUpdate_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_vertexLabelCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、悲观事务、隔离级别：读已提交（vertex）、隐式事务、同步、根据localhash更新 验证ok
// TEST_F(OneThreadAsync01_interface_check, Connect_008_002)
TEST_F(BatchUpdate, DML_098_BatchUpdate_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据主键更新、查事务锁的视图，查占用个事务锁的数量，验证ok
// TEST_F(OneThreadAsync01_interface_check, Connect_008_002)
TEST_F(BatchUpdate, DML_098_BatchUpdate_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_labelSimpleName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, view_name);
    ret = system(g_command);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    EXPECT_EQ(GMERR_OK, ret);


    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateGeneralComplexTable(g_stmt_sync, startNum, endNum);

    free(g_schemaStruct);

    ret = system(g_command);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 101");
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023、悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据localhash更新 验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、悲观事务、隔离级别：读已提交（vertex）、隐式事务、同步、根据主键更新 验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);


    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateGeneralComplexTable(g_stmt_sync, startNum, endNum);

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、悲观事务、隔离级别：读已提交（vertex）、隐式事务、同步、根据localhash更新 验证ok
// TEST_F(OneThreadAsync01_interface_check, Connect_008_002)
TEST_F(BatchUpdate, DML_098_BatchUpdate_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026、悲观事务、隔离级别：可串行化（datalog）、隐式事务、同步
TEST_F(BatchUpdate, DML_098_BatchUpdate_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_SERIALIZABLE; // 可串行化
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据并校验
    UpdateGeneralComplexTable(g_stmt_sync, startNum, endNum);

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027、悲观事务、隔离级别：可串行化（datalog）、隐式事务、同步、根据二级索引update操作
TEST_F(BatchUpdate, DML_098_BatchUpdate_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_SERIALIZABLE; // 可串行化
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();
    free(g_schemaStruct);

    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、主键值设置为0-100，更新101的主键值，预期结果，不会返回失败，查询成功，totalNum为1，succNum为1
TEST_F(BatchUpdate, DML_098_BatchUpdate_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/simple_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 写入数据
    int startNum = 0;
    int endNum = 100;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    VerifyData();

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f0_value = 101;
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SetVertexProperty(g_stmt_sync, 2 * 101, 1);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(g_batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    VerifyData();
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029、写数据，设置批量操作数量为最大4k，更新数据，根据主键更新
TEST_F(BatchUpdate, DML_098_BatchUpdate_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    readJanssonFile("schemaFile/localhash_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批写的数据的记录数
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 4096;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_localhashTable, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_localhashKey);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_localhashTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030、写数据，设置批量操作数量为最大4k，根据二级索引更新
TEST_F(BatchUpdate, DML_098_BatchUpdate_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    readJanssonFile("schemaFile/simple_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_tableName1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批写的数据的记录数
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum = 0;
    int endNum = 4096;
    for (int i = startNum; i < endNum; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexProperty(g_stmt_sync, i, 0);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexProperty(g_stmt_sync, 2 * i, 1);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);
    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 031、多次batch事务提交，悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据主键更新 验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_labelSimpleName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int times = 0; times < 100; times++) {
        // 开启悲观事务，显示事务
        ret = GmcTransStart(g_conn_sync, &TrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 更新数据并校验
        UpdateGeneralComplexTable(g_stmt_sync, startNum, endNum);
        
        // 提交事务
        ret = GmcTransCommit(g_conn_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032、多次batch事务提交，悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据主键更新 验证ok
TEST_F(BatchUpdate, DML_098_BatchUpdate_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_labelSimpleName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(g_batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    
    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(g_schemaStruct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033、悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据主键更新、部分设置semi、部分设置strict、部分不设置
TEST_F(BatchUpdate, DML_098_BatchUpdate_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_labelSimpleName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = startNum; i < 10; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = 10; i < 20; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 3 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = 20; i < 30; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 3 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    free(g_schemaStruct);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034、悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据主键更新、部分设置semi、部分设置strict、部分不设置
TEST_F(BatchUpdate, DML_098_BatchUpdate_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_labelSimpleName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据并校验
    int startNum = 0;
    int endNum = 100;
    SetGeneralComplexTable(g_stmt_sync, startNum, endNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新数据
    for (int i = startNum; i < 10; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = 10; i < 20; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 3 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = 20; i < 30; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 3 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(30, totalNum);
    ASSERT_EQ(30, successNum);
    free(g_schemaStruct);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035、悲观事务、隔离级别：读已提交（vertex）、显示事务、同步、根据主键更新
// 插入十条，更新一条，再插入十条，更新一条，算四个事务
TEST_F(BatchUpdate, DML_098_BatchUpdate_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcBatchRetT batchRet;
    // 建表
    readJanssonFile("schemaFile/general_complex_table.gmjson", &g_schemaStruct);
    ASSERT_NE((void *)NULL, g_schemaStruct);
    GmcDropVertexLabel(g_stmt_sync, g_labelSimpleName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schemaStruct, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_COMMITTED; // 读已提交
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_PESSIMISITIC_TRX; // 悲观事务

    // 开启悲观事务，显示事务
    ret = GmcTransStart(g_conn_sync, &TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum1 = 0;
    int endNum1 = 10;
    for (int i = startNum1; i < endNum1; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = startNum1; i < endNum1; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    int startNum2 = 10;
    int endNum2 = 20;
    for (int i = startNum2; i < endNum2; i++) {
        SetVertexPropertyPK(g_stmt_sync, i);
        SetVertexPropertyWithStr(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_simpleTableName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    for (int i = startNum2; i < endNum2; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        SetVertexPropertyWithStr(g_stmt_sync, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableNamePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(g_batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(g_batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(4, totalNum);
    ASSERT_EQ(4, successNum);
    free(g_schemaStruct);

    // 提交事务
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_simpleTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
