#include "TreeModelIncUpdate_test.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;

GmcConnT *connectionAsync = NULL;

class TreeModelIncUpdate_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void TreeModelIncUpdate_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void TreeModelIncUpdate_test::TearDownTestCase()
{
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void TreeModelIncUpdate_test::SetUp()
{
    // 定义MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    // 封装创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    testCreateLabelMS(g_stmt);

    g_subIndex = 0;

    AW_CHECK_LOG_BEGIN();
}

void TreeModelIncUpdate_test::TearDown()
{
    AW_CHECK_LOG_END();
    testDropLabelMS(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
}

/*****************************************************************************
 Description  : 01.GmcNodeGetElementByIndex node指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_001)
{
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

/*****************************************************************************
 Description  : 02.GmcNodeGetElementByIndex element指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_002)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, NULL);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 03.GmcNodeRemoveElementByIndex node指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_003)
{
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = GmcNodeRemoveElementByIndex(T3, updateindex);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

/*****************************************************************************
 Description  : 04.GmcNodeAppendElement node指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_004)
{
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = GmcNodeAppendElement(T3, &T3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

/*****************************************************************************
 Description  : 05.GmcDeltaVectorRemove element指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_005)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, NULL);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 06.GmcGetRootNode stmt指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_006)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = GmcGetRootNode(NULL, &root);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 07.GmcGetRootNode node指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_007)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 08.GmcNodeGetChild node指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_008)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = GmcNodeGetChild(NULL, "T3", &T3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

/*****************************************************************************
 Description  : 09.GmcNodeGetChild element指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_009)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 10.GmcNodeGetChild nodeName指针为空，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_010)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, NULL, &T3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 11.GmcNodeGetChild nodeName为非法值，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_011)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "中文", &T3);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcNodeGetChild(root, "T1.T5", &T3);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcNodeGetChild(root, "T1/T5", &T3);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcNodeGetChild(root, "T1::T5", &T3);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    AddWhiteList(GMERR_INVALID_NAME);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 12.GmcNodeGetChild nodeName为超长，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_012)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    char overLengthNodeName[129] = {0};
    memset(overLengthNodeName, 'a', 128);
    overLengthNodeName[128] = '\0';

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, overLengthNodeName, &T3);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    AddWhiteList(GMERR_INVALID_VALUE);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 13.GmcNodeGetChild nodeName为空字符串，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_013)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "", &T3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 14.GmcNodeGetChild nodeName为存在但层数错误的nodename，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_014)
{
    GmcNodeT *root = NULL, *T3 = NULL;

    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T5", &T3);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    AddWhiteList(GMERR_INVALID_NAME);

    // 设置根节点
    TestGmcNodeSetPropertyByName_PK(root, 1);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 15.插入数据，增量更新update部分vector节点的数据，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_015)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        // 未update的数据值不变
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, i, 0, f14_value);
    }
}

/*****************************************************************************
 Description  : 16.插入数据，增量更新update部分vector节点的数据，带superfield，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_016)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertexSuperfield(
        g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_1 = (char *)malloc(26);
        char *temp = sp_1;
        *(int64_t *)(temp) = i + 100;
        *(uint64_t *)(temp + 8) = i + 100;
        *(int32_t *)(temp + 16) = i + 100;
        *(uint32_t *)(temp + 20) = i + 100;
        *(int16_t *)(temp + 24) = i + 100;

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T3, "superfield2", sp_1, 26);
        EXPECT_EQ(GMERR_OK, ret);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T3, "superfield2", sp_1, 26);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        free(sp_1);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int16_t getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);

        // 未update的数据值不变
        getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);
    }
}

/*****************************************************************************
 Description  : 17.插入数据，增量更新update部分vector节点的数据，批量更新，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_017)
{
    int start_num = 0;
    int end_num = 10;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertexBatch(
        g_conn, g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt, true);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        // 未update的数据值不变
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, i, 0, f14_value);
    }
}

/*****************************************************************************
 Description  : 18.插入数据，启动事务，增量更新update部分vector节点的数据，事务commit，
                查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_018)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        // 未update的数据值不变
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, i, 0, f14_value);
    }
}

/*****************************************************************************
 Description  : 19.插入数据，启动事务，增量更新update部分vector节点的数据，事务rollback，
                查询数据为update前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_019)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }
    }
}

/*****************************************************************************
 Description  : 20.插入数据，启动事务，增量更新update部分vector节点的数据，构造更新失败，
                事务commit，查询数据为update前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_020)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        // int64_t f0_value = i;
        // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, "MS_Tree_Vector_01.F0 = \"aaa\"");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);
        TestGmcNodeSetPropertyByName_V_Fail(T3);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);
        TestGmcNodeSetPropertyByName_V_Fail(T3);

        // ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_INVALID_PROPERTY);

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 21.插入数据，增量更新remove部分vector节点的数据，查询数据为remove后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_021)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        updateindex = 2;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        // 未remove的数据值不变,index变化
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, i, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, size);
    }

    // 再次更新node，删除vector中剩余数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, size);
    }
}

/*****************************************************************************
 Description  : 22.插入数据，增量更新remove部分vector节点的数据，批量更新，查询数据为remove后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_022)
{
    int start_num = 0;
    int end_num = 10;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertexBatch(
        g_conn, g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        updateindex = 2;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt, true);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        // 未remove的数据值不变,index变化
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, i, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, size);
    }
}

/*****************************************************************************
 Description  : 23.插入数据，启动事务，增量更新remove部分vector节点的数据，
                事务commit，查询数据为remove后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_023)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        updateindex = 2;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        // 未remove的数据值不变,index变化
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, i, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, size);
    }
}

/*****************************************************************************
 Description  : 24.插入数据，启动事务，增量更新remove部分vector节点的数据，
                事务rollback，查询数据为remove前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_024)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        updateindex = 2;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }
    }
}

/*****************************************************************************
 Description  : 25.插入数据，启动事务，增量更新remove部分vector节点的数据，设置index为不存在的index值，
                更新失败，由于是DM失败，不会触发回滚，事务commit，
                查询update变更记录数GMC_STATISTICS_TYPE_UPDATE_FAILED为0，数据为remove前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_025)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 5;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    }
    AddWhiteList(GMERR_DATA_EXCEPTION);

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 26.插入数据，增量更新append部分vector节点的数据，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_026)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            printf("j = %d\n", j);
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 27.插入数据，增量更新append部分vector节点的数据，带superfield，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_027)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertexSuperfield(
        g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_1 = (char *)malloc(26);
        char *temp = sp_1;
        *(int64_t *)(temp) = i;
        *(uint64_t *)(temp + 8) = i;
        *(int32_t *)(temp + 16) = i;
        *(uint32_t *)(temp + 20) = i;
        *(int16_t *)(temp + 24) = i;

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T3, "superfield2", sp_1, 26);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T3, "superfield2", sp_1, 26);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        free(sp_1);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 28.插入数据，增量更新append部分vector节点的数据，批量更新，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_028)
{
    int start_num = 0;
    int end_num = 10;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertexBatch(
        g_conn, g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt, true);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 29.插入数据，启动事务，增量更新append部分vector节点的数据，
                事务commit，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_029)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 30.插入数据，启动事务，增量更新append部分vector节点的数据，
                事务rollback，查询数据为append前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_030)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 31.插入数据，启动事务，增量更新append部分vector节点的数据，构造更新失败，
                事务commit，查询数据为append前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_031)
{
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_02_Name);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char *MS_Tree_Vector_01_schema = NULL;
    char *MS_Tree_Vector_02_schema = NULL;

    readJanssonFile("schema_file/MS_Tree_Vector_01.gmjson", &MS_Tree_Vector_01_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_01_schema);
    readJanssonFile("schema_file/MS_Tree_Vector_02.gmjson", &MS_Tree_Vector_02_schema);
    ASSERT_NE((void *)NULL, MS_Tree_Vector_02_schema);

    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_01_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, MS_Tree_Vector_02_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(MS_Tree_Vector_01_schema);
    free(MS_Tree_Vector_02_schema);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        // int64_t f0_value = i;
        // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, "MS_Tree_Vector_01.F0 = \"aaa\"");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);
        TestGmcNodeSetPropertyByName_V_Fail(T3);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);
        TestGmcNodeSetPropertyByName_V_Fail(T3);

        // ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_INVALID_PROPERTY);

    // MS事务rollback
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 32.插入数据，GmcClearNode清空vector的数据，查询数据已清空，
                增量更新append和清空前同样的数据，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_032)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num,
    MS_Tree_Vector_01_Name);

    // GmcClearNode 清空vector数据
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeClear(T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 更新
        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret=testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for(int j = 0; j < vector_num; j++)
        {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 33.插入数据，按照顺序增量更新update部分vector节点的数据，remove部分数据，
                append部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_033)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 34.插入数据，按照顺序增量更新update部分vector节点的数据，带superfield，
                remove部分数据，append部分数据，带superfield，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_034)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertexSuperfield(
        g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        char *sp_1 = (char *)malloc(26);
        char *temp = sp_1;
        *(int64_t *)(temp) = i + 100;
        *(uint64_t *)(temp + 8) = i + 100;
        *(int32_t *)(temp + 16) = i + 100;
        *(uint32_t *)(temp + 20) = i + 100;
        *(int16_t *)(temp + 24) = i + 100;

        // 增量更新vector节点
        // update
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T3, "superfield2", sp_1, 26);
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T3, "superfield2", sp_1, 26);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        free(sp_1);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int16_t getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_SF(T3, "superfield2", getValue);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 35.插入数据，按照顺序增量更新update部分vector节点的数据，remove部分数据，
                append部分数据，批量更新，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_035)
{
    int start_num = 0;
    int end_num = 10;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    // 普通同步插入数据
    TestGmcInsertVertexBatch(
        g_conn, g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt, true);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 36.插入数据，按照顺序增量更新update部分vector节点的数据，
                remove部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_036)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        vector_num = 2;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 37.插入数据，按照顺序增量更新update部分vector节点的数据，
                append部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_037)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        vector_num = 4;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 38.插入数据，按照顺序增量更新remove部分数据，append部分数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_038)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 4;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 39.插入数据，按照顺序增量更新update部分vector节点的数据，remove部分数据，
                append部分数据，全表扫描数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_039)
{
    int start_num = 0;
    int end_num = 2;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T1_T2 = NULL, *T3 = NULL, *T4 = NULL, *T1_T5 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* ******** 全表扫描 ******** */
    int isPrint = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int scan_num = 0;
    for (uint32_t num = 0; num < end_num - start_num; num++) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if ((isFinish == true) || (ret != GMERR_OK)) {
            if (ret != GMERR_OK) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            break;
        }

        // 获取 根节点、普通节点 字段值
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_R_forNonUniq(root, num, false, (char *)"string", "scan full vertex", isPrint);

        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_P_Print(T1, num, false, (char *)"string", "scan full vertex", isPrint);

        // 获取 array 节点 字段值
        ret = GmcNodeGetChild(T1, "T2", &T1_T2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T1_T2, j, &T1_T2);
            EXPECT_EQ(GMERR_OK, ret);
            printf("[INFO] scan full vertexLabel, \"T1.T2\" array_node[%d] \n", j);
            TestGmcNodeGetPropertyByName_A_Print(T1_T2, num, false, (char *)"string", "scan full vertex", isPrint);
        }

        // 读取vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        int getValue = num;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        printf("[INFO] scan full vertexLabel, \"T3\" vector_node[0] \n");
        TestGmcNodeGetPropertyByName_V_Print(T3, getValue, 0, f14_value, "scan full vertex", isPrint);

        getValue = num + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        printf("[INFO] scan full vertexLabel, \"T3\" vector_node[1] \n");
        TestGmcNodeGetPropertyByName_V_Print(T3, getValue, 0, f14_value, "scan full vertex", isPrint);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        printf("[INFO] scan full vertexLabel, \"T3\" vector_node[2] \n");
        TestGmcNodeGetPropertyByName_V_Print(T3, getValue, 0, f14_value, "scan full vertex", isPrint);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
        scan_num++;
    }
    GmcResetStmt(g_stmt);
    EXPECT_EQ(end_num - start_num, scan_num);
}

/*****************************************************************************
 Description  : 40.插入数据，append部分数据，更新数据，update接口更新刚刚append的数据，
                更新数据，查询数据为两次更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_040)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新刚刚append的node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 3;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        vector_num = 4;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 41.vector中插入3条数据，remove index=1的数据，更新数据，
                update index=1的数据，更新数据，查询数据为两次更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_041)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新index=2的node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 1;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        vector_num = 2;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 42.插入数据，按照顺序增量更新update部分vector节点的数据，remove部分数据，
                append部分数据，使用merge接口更新数据，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_042)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 43.Vertex中有3个node，在一次更新操作中，node1(T3)进行update、remove、append操作，
                node2(T4)进行update、remove、append操作，node3(T1.T5)进行update、remove、append操作，
                操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_043)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T3 = NULL, *T4 = NULL, *T1_T5 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node1
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // 增量更新vector节点node2
        // update
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T4, updateindex, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T4, newValue, 0, f14_value);
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T4, updateindex);
        EXPECT_EQ(GMERR_OK, ret);
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T4, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T4, newValue, 0, f14_value);

        // 增量更新vector节点node3
        // update
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T1_T5, updateindex, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T1_T5, newValue, 0, f14_value);
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T1_T5, updateindex);
        EXPECT_EQ(GMERR_OK, ret);
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T1_T5, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T1_T5, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点node1
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点node2
        getValue = i;
        ret = GmcNodeGetElementByIndex(T4, 0, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T4, 1, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T4, 2, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        size = 0;
        ret = GmcNodeGetElementCount(T4, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点node3
        getValue = i;
        ret = GmcNodeGetElementByIndex(T1_T5, 0, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T1_T5, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T1_T5, 1, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T1_T5, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T1_T5, 2, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T1_T5, getValue, 0, f14_value);

        size = 0;
        ret = GmcNodeGetElementCount(T1_T5, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 44.Vertex中有3个node，在一次更新操作中，node1进行append操作，node2进行remove操作，
                node3进行update操作，操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_044)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T3 = NULL, *T4 = NULL, *T1_T5 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node1
        int newValue = i;
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // 增量更新vector节点node2
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T4, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node3
        // update
        updateindex = 2;
        newValue = i + 100;
        ret = GmcNodeGetElementByIndex(T1_T5, updateindex, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T1_T5, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        int getValue = 0;
        // 读取vector节点node1
        vector_num = 4;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点node2
        vector_num = 2;
        for (int j = 0; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(T4, j, &T4);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);
        }

        size = 0;
        ret = GmcNodeGetElementCount(T4, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点node3
        vector_num = 3;
        getValue = i;
        ret = GmcNodeGetElementByIndex(T1_T5, 0, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T1_T5, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T1_T5, 1, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T1_T5, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T1_T5, 2, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T1_T5, getValue, 0, f14_value);

        size = 0;
        ret = GmcNodeGetElementCount(T1_T5, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 45.插入数据，循环增量更新append数据直至vector最大size 1024，查询数据为append后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_045)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 1023;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 插入vector节点1
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t v0_value = i;
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &v0_value, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    // 这里已经有1023个node，再append 1个
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        uint8_t newValue = i;
        updateindex = 0;

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 1024;
        for (int j = 0; j < vector_num; j++) {
            uint8_t getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V_02(T3, getValue);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 46.插入数据，循环增量更新append数据超过vector最大size 1024，
                update失败，查询数据为append前的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_046)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 1023;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 插入vector节点1
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t v0_value = i;
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &v0_value, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    // 这里已经有1023个node，再append 2个
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        uint8_t newValue = i;
        updateindex = 0;

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 1023;
        for (int j = 0; j < vector_num; j++) {
            uint8_t getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V_02(T3, getValue);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 47.插入数据，对同一个record，先update数据，然后remove数据，
                remove接口报错，最终更新成功，查询数据为update后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_047)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove, 对同一record操作，返回失败
        updateindex = 2;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 48.插入数据，对同一个record，先update数据1，然后对同一record update数据2，
                第二次update接口报错，最终更新成功，查询数据为第一次update的数据。
                210729 现在可以对同一node update多次，以最后一次的值为准
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_048)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // update, 对同一record操作，返回失败
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 49.Vertex中有3个node，在一次更新操作中，node1进行update数据1，
                node2进行update、remove、append操作，node1对同一record进行update数据2，
                node1在第二次update时报错。
                210729 现在返回成功，以最后一次update的值为准
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_049)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL, *T4 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node1
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // 增量更新vector节点node2
        // update
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T4, updateindex, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T4, newValue, 0, f14_value);
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T4, updateindex);
        EXPECT_EQ(GMERR_OK, ret);
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T4, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T4, newValue, 0, f14_value);

        // 增量更新vector节点node1
        // update, 对同一record操作，返回失败 210729 现在返回成功，以最后一次update的值为准
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T4, updateindex, &T4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点node1
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点node2
        getValue = i;
        ret = GmcNodeGetElementByIndex(T4, 0, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T4, 1, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T4, 2, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        size = 0;
        ret = GmcNodeGetElementCount(T4, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 50.插入数据，按照顺序增量更新append数据，remove部分数据，update部分数据，
                remove和update接口报错。210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_050)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // update, 操作顺序错误，返回失败
        updateindex = 1;
        newValue = i;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove, 操作顺序错误，返回失败
        updateindex = 2;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 51.插入数据，按照顺序增量更新append数据，update部分数据，update接口报错。
                210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_051)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // update, 操作顺序错误，返回失败
        updateindex = 0;
        newValue = i;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 4;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 52.插入数据，按照顺序增量更新remove部分数据，update部分数据，update接口报错。
                210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_052)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // remove
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // update, 操作顺序错误，返回失败
        updateindex = 1;
        int newValue = i;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 2;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 53.Vertex中有3个node，在一次更新操作中，node1进行remove部分数据，
                node2进行update、remove、append操作，node1进行update数据，node1在update时报错。
                210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_053)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL, *T4 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node1
        // remove
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node2
        // update
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T4, updateindex, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T4, newValue, 0, f14_value);
        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T4, updateindex);
        EXPECT_EQ(GMERR_OK, ret);
        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T4, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T4, newValue, 0, f14_value);

        // 增量更新vector节点node1
        // update, 顺序错误，返回失败
        newValue = i + 200;
        updateindex = 1;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点node1
        int getValue = i + 200;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        vector_num = 2;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点node2
        getValue = i;
        ret = GmcNodeGetElementByIndex(T4, 0, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T4, 1, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T4, 2, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T4, getValue, 0, f14_value);

        size = 0;
        vector_num = 3;
        ret = GmcNodeGetElementCount(T4, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 54.一次增量更新操作包含16个update、remove、append的混合操作，执行成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_054)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, array_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        uint8_t getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T3, getValue);

        ret = GmcNodeGetElementByIndex(T3, 15, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T3, getValue);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T3, getValue);

        uint32_t size = 0;
        vector_num = 16;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 55.Vertex中有3个node，在一次更新操作中，3个node的update、remove、append分别都达到16次，
                操作成功，查询数据为更新后的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_055)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T3 = NULL, *T4 = NULL, *T1_T5 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, array_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node1
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        // 增量更新vector节点node2
        // update
        newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T4, updateindex, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T4, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T4, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeAppendElement(T4, &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T4, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        // 增量更新vector节点node3
        // update
        newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T1_T5, updateindex, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1_T5, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T1_T5, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 14次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 14; j++) {
            ret = GmcNodeAppendElement(T1_T5, &T1_T5);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T1_T5, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点1
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T3, getValue);

        ret = GmcNodeGetElementByIndex(T3, 15, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T3, getValue);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T3, getValue);

        uint32_t size = 0;
        vector_num = 16;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点2
        getValue = i;
        ret = GmcNodeGetElementByIndex(T4, 0, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T4, getValue);

        ret = GmcNodeGetElementByIndex(T4, 15, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T4, getValue);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T4, 1, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T4, getValue);

        size = 0;
        vector_num = 16;
        ret = GmcNodeGetElementCount(T4, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 读取vector节点3
        getValue = i;
        ret = GmcNodeGetElementByIndex(T1_T5, 0, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T1_T5, getValue);

        ret = GmcNodeGetElementByIndex(T1_T5, 15, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T1_T5, getValue);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T1_T5, 1, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_02(T1_T5, getValue);

        size = 0;
        vector_num = 16;
        ret = GmcNodeGetElementCount(T1_T5, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 56.一次增量更新操作包含1025个update、remove、append的混合操作，报错。
                2021.11.27: 一次增量更新个数从16改为1024
                2023.11.08: 一次增量更新个数兼容V3，改为无限制
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_056)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, array_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 1023次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 1023; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
}

/*****************************************************************************
 Description  : 57.Vertex中有3个node，在一次更新操作中，3个node的update、remove、append分别都达到1025次，报错。
                2021.11.27: 一次增量更新个数从16改为1024
                2023.11.08: 一次增量更新个数兼容V3，改为无限制
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_057)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T3 = NULL, *T4 = NULL, *T1_T5 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex_02(g_stmt, index, start_num, end_num, array_num, vector_num, MS_Tree_Vector_02_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T5", &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点node1
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 1023次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 1023; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        // 增量更新vector节点node2
        // update
        newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T4, updateindex, &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T4, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T4, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 1023次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 1023; j++) {
            ret = GmcNodeAppendElement(T4, &T4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T4, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        // 增量更新vector节点node3
        // update
        newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T1_T5, updateindex, &T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1_T5, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T1_T5, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append 1023次
        newValue = i;
        updateindex = 0;
        for (int j = 0; j < 1023; j++) {
            ret = GmcNodeAppendElement(T1_T5, &T1_T5);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(T1_T5, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
}

/*****************************************************************************
 Description  : 58.插入数据，按照顺序增量更新append数据，update部分数据，update接口报错，
                再次append数据，更新成功，查询数据为2次append的数据。
                210729 现在返回成功，没有顺序要求
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_058)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // append
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // update, 操作顺序错误，返回失败
        updateindex = 0;
        newValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // append
        newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        vector_num = 5;
        for (int j = 1; j < vector_num; j++) {
            getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 59.非数组嵌套，append时设置index为非0且已经存在的index值，报错。
                append时设置index为非0且不存在的index值，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_059)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 210727 修改接口后不会再设置index，不会报错了
        // 增量更新vector节点
        int newValue = i;
        updateindex = 1;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 10;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 5;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 60.update、remove、append接口的nodeName为不存在的节点名，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_060)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T6", &T3);  // 210727 报错位置提前
        EXPECT_EQ(GMERR_INVALID_NAME, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_INVALID_NAME);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 61.update和remove输入的index超过实际存在的index值，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_061)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 10;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // remove
        updateindex = 11;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    }
    AddWhiteList(GMERR_DATA_EXCEPTION);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 62.使用同步stmt打开vertex，使用异步stmt调用新增接口，返回错误码。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_062)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);  // 210727 接口修改后报错位置改变

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        vector_num = 3;
        for (int j = 0; j < vector_num; j++) {
            int getValue = i;
            ret = GmcNodeGetElementByIndex(T3, j, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);
        }

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 63.异步：使用异步句柄进行增量更新操作，操作成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_063)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    AsyncUserDataT asyncData;
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 普通异步插入数据
    TestGmcInsertVertexAsync(
        g_stmt_async, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt_async, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData, 1, 1000000);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 65.订阅推送：全表订阅，vector上的数据增量更新后，可以触发推送，数据正确。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_065)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    // 创建订阅连接
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    const char *g_subConnName = "subConnName";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);

    g_subIndex = 0;

    // 创建订阅关系
    char *sub_info = NULL;
    readJanssonFile("schema_file/TreeModel_subinfo_fullLabelSub.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    int userDataIdx = 0;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValueupdate = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValueupdate, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValueupdate, 0, f14_value);

        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = newValueupdate;
        userDataIdx++;

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅、断开订阅连接
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

/*****************************************************************************
 Description  : 66.数组排序：数组排序后，进行增量更新，操作成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_066)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据，顺序1/0/2
    TestGmcInsertVertexdifferent(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num,
    MS_Tree_Vector_01_Name);

    for (int i = start_num; i < end_num; i++)
    {
        // 从服务端拿出指定的veter进行数组节点排序
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 升序排序
        ret = GmcNodeSortElement(T3, "V0", GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);

        // 排序后的veter验证
        char *stringA = (char *)"aaaaaa";
        char *stringB = (char *)"bbbbbb";
        char *stringC = (char *)"cccccc";

        // 读取vector节点
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++)
        {
            if (j == 0)
            {
                TestGmcNodeGetPropertyByName_V(T3, 0, 0, stringA);
                GmcNodeGetNextElement(T3, &T3);
            }
            else if (j == 1)
            {
                TestGmcNodeGetPropertyByName_V(T3, 1, 0, stringB);
                GmcNodeGetNextElement(T3, &T3);
            }
            else
            {
                TestGmcNodeGetPropertyByName_V(T3, 2, 0, stringC);
                GmcNodeGetNextElement(T3, &T3);
            }
        }
    }

    // 更新node
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt,  MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    AddWhiteList(GMERR_NO_DATA);
}

/*****************************************************************************
 Description  : 67.truncate表：增量更新后，truncate表，查询数据被清空。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_067)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }

    // truncate表
    ret = GmcTruncateVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);

    // 再次读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, true);
    }
}

/*****************************************************************************
 Description  : 68.vertex转json：增量更新后，调用GmcGreateJsonByVertex成功转json，查看数据是否正确。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_068)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);

        // 结果转json
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
        GmcFreeJsonStr(g_stmt, itemJson);
    }
}

/*****************************************************************************
 Description  : 69.实现json对象的写入：通过json文件插入数据，在插入数据的基础上增量更新成功，查询数据正确。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_069)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // insert vertex by json
    TestInsertVertexByJson(g_stmt, "schema_file/MS_Tree_Vector_01.gmdata", MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 70.gmimport/ gmexport：导入tree模型vertex，增量更新操作成功，然后导出vertex和数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_070)
{
    // 先删除label
    ret = GmcDropVertexLabel(g_stmt, MS_Tree_Vector_01_Name);
    EXPECT_EQ(GMERR_OK, ret);

    // gmimport工具导入创建vertexLabel
    char schema_file[128] = "schema_file/MS_Tree_Vector_01.gmjson";
    char cmd[MAX_CMD_SIZE];
    // -c: dstore | vschema | vdata | eschema | edata | cache
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schema_file,
        g_testNameSpace, g_connServer);

    printf("%s\n", cmd);
    ret = executeCommand(cmd, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/03_DML/044_TreeModelIncrementalUpdate/schema_file/MS_Tree_Vector_01.gmjson\" "
        "successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }

    // export 导出data数据
    char const *g_filePath = "./";
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -ns %s -s %s", g_toolPath,
        MS_Tree_Vector_01_Name, g_testNameSpace, g_connServer);

    printf("%s\n", cmd);
    ret = executeCommand(cmd, "Command type: export_vdata, export file successfully.");
    // ret = executeCommand(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);
}

/*****************************************************************************
 Description  : 71.Namespace：创建namespace，use namespace，进行vector增量更新操作，操作成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_071)
{
    // 先删除label
    testDropLabelMS(g_stmt);

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 创建namespace
    const char *nameSpace = (const char *)"user001";
    const char *g_namespaceUserName = "abc";
    ret = GmcCreateNamespace(g_stmt, nameSpace, g_namespaceUserName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    testCreateLabelMS(g_stmt);

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // remove
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }

    testDropLabelMS(g_stmt);

    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    testCreateLabelMS(g_stmt);
}

/*****************************************************************************
 Description  : 72.多个线程同时增量更新vector的同一条record，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：update 数据1
void *client_thread_072_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Thread 1]Incremental Update Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：update 数据2
void *client_thread_072_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 200;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Thread 2]Incremental Update Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

TEST_F(TreeModelIncUpdate_test, DML_044_072)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02;

    ret = pthread_create(&client_thr_01, NULL, client_thread_072_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_072_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int64_t getValue1 = i + 100;
        int64_t getValue2 = i + 200;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        int64_t f0_value_node = 0;
        ret = GmcNodeGetPropertyByName(T3, "V0", &f0_value_node, sizeof(int64_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        if ((f0_value_node == getValue1) || (f0_value_node == getValue2)) {
            printf("[Multiple threads]Incremental Update success. T3.V0 newvalue = %lld.\n", f0_value_node);
        } else {
            printf("[Multiple threads]Incremental Update fail. T3.V0 newvalue = %lld.\n", f0_value_node);
        }
    }
}

/*****************************************************************************
 Description  : 73.多个线程分别update、remove、append同一个vector，都执行成功。
 Author       : hanyang
*****************************************************************************/
// 线程1：update
void *client_thread_073_01(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Thread 1]Incremental Update Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 线程2：remove
void *client_thread_073_02(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 1;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Thread 2]Incremental Remove Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 线程3：append
void *client_thread_073_03(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;
    int ret = 0;

    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新node
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_tmp, MS_Tree_Vector_01_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt_tmp, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt_tmp, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt_tmp, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_tmp);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Thread 3]Incremental Append Finished.\n");

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

TEST_F(TreeModelIncUpdate_test, DML_044_073)
{
    int ret = 0;
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    TestGmcInsertVertex(g_stmt, index, 0, f14_value, start_num, end_num, array_num, vector_num, MS_Tree_Vector_01_Name);

    pthread_t client_thr_01, client_thr_02, client_thr_03;

    ret = pthread_create(&client_thr_01, NULL, client_thread_073_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, client_thread_073_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&client_thr_03, NULL, client_thread_073_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    pthread_join(client_thr_03, NULL);

    // 读取数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_01_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_01_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取vector节点
        int64_t getValue = i + 100;
        ret = GmcNodeGetElementByIndex(T3, 0, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        getValue = i;
        ret = GmcNodeGetElementByIndex(T3, 1, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 2, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        ret = GmcNodeGetElementByIndex(T3, 3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(T3, getValue, 0, f14_value);

        uint32_t size = 0;
        vector_num = 4;
        ret = GmcNodeGetElementCount(T3, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(vector_num, size);
    }
}

/*****************************************************************************
 Description  : 77.测试增量更新Update的性能参数。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_077)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 1024;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 插入vector节点1
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t f0_value = i;
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    struct timeval tmBegin, tmEnd;
    unsigned long long opsBegin, opsEnd, cycleBegin, cycleEnd;

    gettimeofday(&tmBegin, NULL);
    opsBegin = getCyclesUS();
    cycleBegin = getAndCalCpuCycles();

    for (uint32_t oprNum = 0; oprNum < vector_num; oprNum++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int i = 0;
        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i + 100;
        updateindex = 0;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    cycleEnd = getAndCalCpuCycles();
    gettimeofday(&tmEnd, NULL);
    unsigned long long s1, s2, s3;
    s1 = (tmEnd.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd.tv_usec - tmBegin.tv_usec);
    if (s1 > 0) {
        s2 = (vector_num * 1000000) / s1;
        s3 = (cycleEnd - cycleBegin) / vector_num;
    } else {
        s2 = 0;
        s3 = 0;
    }
    printf("[Incremental Update][update] cost_time:%llds|%06lldus, ops:%lld, cpu_cycles:%lld\n", s1 / 1000000,
        s1 % 1000000, s2, s3);
}

/*****************************************************************************
 Description  : 78.测试增量更新remove的性能参数。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_078)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 1024;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 插入vector节点1
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t f0_value = i;
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    struct timeval tmBegin, tmEnd;
    unsigned long long opsBegin, opsEnd, cycleBegin, cycleEnd;

    gettimeofday(&tmBegin, NULL);
    opsBegin = getCyclesUS();
    cycleBegin = getAndCalCpuCycles();

    for (uint32_t oprNum = 0; oprNum < vector_num; oprNum++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int i = 0;
        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    cycleEnd = getAndCalCpuCycles();
    gettimeofday(&tmEnd, NULL);
    unsigned long long s1, s2, s3;
    s1 = (tmEnd.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd.tv_usec - tmBegin.tv_usec);
    if (s1 > 0) {
        s2 = (vector_num * 1000000) / s1;
        s3 = (cycleEnd - cycleBegin) / vector_num;
    } else {
        s2 = 0;
        s3 = 0;
    }
    printf("[Incremental Update][remove] cost_time:%llds|%06lldus, ops:%lld, cpu_cycles:%lld\n", s1 / 1000000,
        s1 % 1000000, s2, s3);
}

/*****************************************************************************
 Description  : 79.测试增量更新append的性能参数。
 Author       : hanyang
*****************************************************************************/
TEST_F(TreeModelIncUpdate_test, DML_044_079)
{
    int start_num = 0;
    int end_num = 1;
    uint32_t array_num = 3;
    uint32_t vector_num = 3;
    uint32_t vector_append_num = 1000;
    int index = 1;
    char f14_value[8] = "string";
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;

    // 普通同步插入数据
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        uint8_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 插入vector节点1
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t f0_value = i;
            ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &f0_value, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 更新node
    struct timeval tmBegin, tmEnd;
    unsigned long long opsBegin, opsEnd, cycleBegin, cycleEnd;

    gettimeofday(&tmBegin, NULL);
    opsBegin = getCyclesUS();
    cycleBegin = getAndCalCpuCycles();

    for (uint32_t oprNum = 0; oprNum < vector_append_num; oprNum++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, MS_Tree_Vector_02_Name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int i = 0;
        uint8_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        int newValue = i;
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "V0", GMC_DATATYPE_UINT8, &newValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt, MS_Tree_Vector_02_Key_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    cycleEnd = getAndCalCpuCycles();
    gettimeofday(&tmEnd, NULL);
    unsigned long long s1, s2, s3;
    s1 = (tmEnd.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd.tv_usec - tmBegin.tv_usec);
    if (s1 > 0) {
        s2 = (vector_append_num * 1000000) / s1;
        s3 = (cycleEnd - cycleBegin) / vector_append_num;
    } else {
        s2 = 0;
        s3 = 0;
    }
    printf("[Incremental Update][append] cost_time:%llds|%06lldus, ops:%lld, cpu_cycles:%lld\n", s1 / 1000000,
        s1 % 1000000, s2, s3);
}
