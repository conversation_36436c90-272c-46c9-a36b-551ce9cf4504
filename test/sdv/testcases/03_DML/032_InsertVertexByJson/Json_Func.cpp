/*****************************************************************************
 Description  : 实现json对象插入GmcSetVertexByJson接口功能测试
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/03/20
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

int ret = 0;
GmcConnT *g_conn;
GmcStmtT *g_stmt;
int affectRows;
unsigned int len;
#define MAX_NAME_LENGTH 128

/**********订阅相关**********/
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;
char *g_sub_info = NULL;
int g_subIndex = 0;
int g_data_num = 10;
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

/**********JSON**********/
char *normal_vertexlabel_schema = NULL;
char *normal_graph_vertex_label_schema = NULL;
char *normal_graph_edge_label_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":1000000
    }
)";

const char *deltaStoreJsonNormalSingle = R"(
    {  
    "delta_stores":                        
        [{                                       
            "name": "dsdml1",    
            "init_mem_size": 10485760,         
            "max_mem_size": 20971520,          
            "extend_mem_size": 6291456,        
            "page_size": 16384                 
        }]                                       
})";

/**********NAME**********/
const char *g_delta_store_name = "dsdml1";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

class InsertVertexByJson_FuncTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        free(normal_vertexlabel_schema);
    }

public:
    SnUserDataT *user_data;

    virtual void SetUp();
    virtual void TearDown();
};

void InsertVertexByJson_FuncTest::SetUp()
{
    printf("[INFO] InsertVertexByJson_FuncTest Start.\n");
    g_sub_info = NULL;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void InsertVertexByJson_FuncTest::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
    printf("[INFO] InsertVertexByJson_FuncTest End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
#if (defined ENV_RTOSV2) || (defined CPU_BIT_32)
    uint64_t F13Value = i + 28800;
#else
    uint64_t F13Value = i;
#endif
    uint64_t F13 = i + 28800;
    uint64_t F13v = i;
    if (GMERR_OK == queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13) ||
        GMERR_OK == queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13v)) {
            ret = 0;
    }
    EXPECT_EQ(GMERR_OK, ret);

    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile)
{
    int ret = 0;
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
}

// 001.schema包含所有数据类型的vertexLabel，通过json插入记录，主键读成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_001)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // Query Vertex by pk
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    query_VertexProperty(g_stmt, val);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.json中字段值包含入中文，特殊字符，日语，插入成功，主键读成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_002)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_002.vertexdata");

    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[100] = "牛すごいわ#￥%";
    ret = queryPropertyAndCompare(g_stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.json插入记录，全表扫描
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_003)
{
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    // 索引名称和索引值修改用例适配 全表扫描当前不需要这是keyvalue
    // ret=GmcSetIndexKeyValue(g_stmt,0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyName(g_stmt, NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = 0;
    int cnt = 0;
    while (!eof) {
        ret = GmcFetch(g_stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(1, cnt);
    printf("scan vertex cnt:%d", cnt);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.json插入记录，哈希扫描
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_004)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &sk, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_sk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = 0;
    int cnt = 0;
    while (!eof) {
        ret = GmcFetch(g_stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(1, cnt);
    printf("scan vertex cnt:%d", cnt);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.开启事务json插入顶点，读数据，deleteVertex,开启事务插入顶点，然后回滚，预期查不到顶点
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_005)
{
    char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    query_VertexProperty(g_stmt, val);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
    EXPECT_EQ(GMERR_OK, ret);

    // delete vetex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex and rollback
    ret = GmcTransStart(g_conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // query vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(eof, true);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.schema带super filed，通过json插入数据，GmcGetSuperFiledByName接口读取成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_006)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    //通过superfiled读vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(2);
    ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 2);  //支持基于ID的API 参数位置修改 用例适配
    EXPECT_EQ(1, *(char *)sp_1_get);
    EXPECT_EQ(1, *(unsigned char *)(sp_1_get + 1));
    free(sp_1_get);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.正常插入数据后，调用GmcSetVertexByJson 覆盖写
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_007)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int64_t sk = 1;
    int val = 1;
    set_VertexProperty_PK(g_stmt, pk);
    set_VertexProperty_SK(g_stmt, sk);
    set_VertexProperty(g_stmt, val);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // reinsert by json
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type.vertexdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    EXPECT_EQ(GMERR_OK, ret);
    free(jStr);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.schema带默认值，调用GmcSetVertexByJson 写数据，json文件中未包含有默认值的字段
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_008)
{
    int ret = 0;
    char *vertexlabel_schema = NULL;
    const char *vertexlabel_name = "T39_D";
    const char *pk_name = "T39_D_K0";
    const char *sk_name = "T39_D_hash";
    readJanssonFile("schemaFile/DefaultVertexLabel.gmjson", &vertexlabel_schema);
    ASSERT_NE((void *)NULL, vertexlabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexlabel_schema);

    // json文件中不包含有默认值的F0
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_008.vertexdata");

    // F0为默认值
    ret = testGmcPrepareStmtByLabelName(g_stmt, vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    char defaultF0V = 'a';
    // read F0
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_CHAR, &defaultF0V);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009. 调用GmcSetVertexByJson 写数据，update顶点
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_009)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");
    // update by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = 20;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // query by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010. 调用GmcSetVertexByJson 写数据，merge顶点
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_010)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f7_value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f7_value, sizeof(f7_value));
    EXPECT_EQ(GMERR_OK, ret);
    char PKName_[] = "T39_K0";
    ret = GmcSetIndexKeyName(g_stmt, PKName_);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t pk = 1;
    uint8_t F3Value = 20;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // query by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011. 调用GmcSetVertexByJson 写数据，replace顶点
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_011)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int newVal = 20;
    int64_t sk = 1;
    set_VertexProperty_PK(g_stmt, pk);
    set_VertexProperty_SK(g_stmt, sk);
    set_VertexProperty(g_stmt, newVal);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // query by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = newVal;
    ret = queryPropertyAndCompare(g_stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012. 调用GmcSetVertexByJson 写数据，delete顶点
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_012)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // delete by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013. 调用GmcSetVertexByJson 写数据，调用GmcCreateJson转json
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_013)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // query by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    // vertex to json
    char *outJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &outJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", outJson);
    GmcFreeJsonStr(g_stmt, outJson);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014. 调用GmcSetVertexByJson 写数据，过滤查询
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_014)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // flter query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int val = 1;
    int64_t sk = 1;
    const char *condStr = "F2<2";
    ret = GmcSetFilter(g_stmt, condStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    int32_t cnt = 0;
    while (!eof) {
        ret = GmcFetch(g_stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(1, cnt);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

void *insertVertexByJson(void *args)
{
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;

    int ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_1000.vertexdata", 0, &data_json_error);
    size_t array_size = json_array_size(data_json);
    size_t i;
    printf("Insert %d vertex by json.\n", array_size);
    bool flag = false;
    for (i = 0; i < array_size; i++) {
        json_t *data_json_item = json_array_get(data_json, i);
        char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt_t, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            flag = true;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (flag) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 015. 多线程并发调用GmcSetVertexByJson 写数据
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_015)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // multi threads merge
    int tdNum = 8;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, insertVertexByJson, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback_insert_by_json(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->msgType) {
                case 2:  //推送new object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[g_subIndex];
                            printf("[NEW OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                            query_VertexProperty(subStmt, index);
                            int64_t sk = index;
                            ret = queryPropertyAndCompare(subStmt, "F9", GMC_DATATYPE_INT64, &sk);
                            EXPECT_EQ(GMERR_OK, ret);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
            }
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
        }
    }
}

// 016. 创建订阅关系 ，调用GmcSetVertexByJson 写数据，触发推送
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_016)
{
    int userDataIdx = 0;
    g_subIndex = 0;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalSubinfo.gmjson", &g_sub_info);
    ASSERT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_insert_by_json, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_10.vertexdata", 0, &data_json_error);
    size_t array_size = json_array_size(data_json);

    //通过json插入触发
    for (int i = 0; i < g_data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        json_t *data_json_item = json_array_get(data_json, i);
        char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
        ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.循环调用GmcSetVertexByJson插入多条数据
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_017)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_1000.vertexdata");

    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    query_VertexProperty(g_stmt, val);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.json直接传入object_array插入失败
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_018)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_1000.vertexdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);  //开发修改错误码适配

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    free(jStr);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.创建驻留DeltaStore的表，GmcSetVertexByJson插入数据成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_019)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type.vertexdata");

    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    query_VertexProperty(g_stmt, val);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 020.插入空字符串""成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_020)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_020.vertexdata");

    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F14", GMC_DATATYPE_STRING, (char *)"");
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.允许为空字段，不插入值，insert成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_021)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_021.vertexdata");

    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(g_stmt, "F2", GMC_DATATYPE_INT8, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.不允许为空字段，不插入值，insert失败
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_022)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char *vertexLabelJson = NULL;
    readJanssonFile("schemaFile/NullFalseVertexLabel.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabelJson);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_022.vertexdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    free(jStr);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023.int类型插入字符串，插入失败
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_023)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_023.vertexdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    free(jStr);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024.char类型插入数字，插入成功
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_024)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_024.vertexdata");
    // Query Vertex by pk
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    int val = 1;
    int64_t sk = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    query_VertexProperty(g_stmt, val);
    ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025.int8插入越界数字
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_025)
{
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schemaFile/data/T39_all_type_025.vertexdata");

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 026.char类型插入长度为5的字符串，插入失败
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_026)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_026.vertexdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    free(jStr);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 027.bytes/fixed类型插入字符串
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_027)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schemaFile/data/T39_all_type_027.vertexdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(g_stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    free(jStr);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 028.响应在毫秒至秒级
TEST_F(InsertVertexByJson_FuncTest, DML_032_InsertVertexByJson_FuncTest_028)
{
    if (g_envType != 2) {
        ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
        EXPECT_EQ(GMERR_OK, ret);

        // insert Vertex
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int count;
#if defined TEST_STATIC_ASAN
        count = 100;
# else
        count = 100000;
#endif
        printf("********************************GmcInsertVertex:%d\n", count);
        for (int i = 0; i < count; i++) {
            uint32_t pk = i;
            int64_t sk = i;
            int val = i;
            set_VertexProperty_PK(g_stmt, pk);
            set_VertexProperty_SK(g_stmt, sk);
            set_VertexProperty(g_stmt, val);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
#if 1
// export vertex
#define MAX_CMD_SIZE 2048
        char g_command[MAX_CMD_SIZE] = {0};
        printf("********************************gmexport vertex:%d\n", count);
        const char *g_filePath = "schemaFile";
#if defined ENV_RTOSV2
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -s %s -ns %s", g_toolPath,
            g_normal_vertexlabel_name, g_filePath, g_connServer, g_testNameSpace);
#else
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -s %s", g_toolPath,
            g_normal_vertexlabel_name, g_filePath, g_connServer);
#endif
        printf("%s\n", g_command);
        ret = executeCommand(g_command, "export file successfully");
        EXPECT_EQ(GMERR_OK, ret);
#endif
        // delete vertex by pk
#if 0
    for(int num=0;num<count;num++){
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret=GmcSetIndexKeyValue(g_stmt,0, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret); 
        ret =GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret); 
    }
#endif
#if 1
        // delete vertex by truncate
        ret = GmcTruncateVertexLabel(g_stmt, g_normal_vertexlabel_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // scan vertex
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool eof = 0;
        int cnt = 0;
        while (!eof) {
            ret = GmcFetch(g_stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(0, cnt);
#endif
#if 0
    //insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i=0;i<count;i++){
        uint32_t pk = i;
        int64_t sk = i;
        int val = i;
        set_VertexProperty_PK(g_stmt, pk);
        set_VertexProperty_SK(g_stmt, sk);
        set_VertexProperty(g_stmt, val);
        ret=GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if(ret!=GMERR_OK){
            printf("******insert failure***%d*******", i);
        }
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
#if 1
        // inset vertex by json
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        printf("********************************begin insert vertex by json: %d\n", count);
        struct timeval tmBegin, tmEnd;
        gettimeofday(&tmBegin, NULL);
        TestInsertVertexByJson(g_stmt, "schemaFile/T39_all_type.vertexdata");
        gettimeofday(&tmEnd, NULL);
        printf("********************************end insert \n");
        long long totalMicrosecond;
        totalMicrosecond = (tmEnd.tv_sec - tmBegin.tv_sec) * 1000000 + (tmEnd.tv_usec - tmBegin.tv_usec);  // 微妙数
        printf("********************************Insert %d vertex,time taken:%d us\n", count, totalMicrosecond);
#endif
        // free
        GmcFreeIndexKey(g_stmt);
        ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
