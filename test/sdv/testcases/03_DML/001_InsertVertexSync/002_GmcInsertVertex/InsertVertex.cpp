extern "C" {}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *conn;
GmcStmtT *stmt;

char label_name[] = "T90";
char lable_name_PK[] = "T90_PK";
char label2_name[] = "T39_all_type";
char label3_name[] = "T80";
char label3_name_PK[] = "T80_PK";
char label4_name[] = "T39";
char g_label_config[] = "{\"max_record_count\":1000}";
char g_label_config_test[] = "{\"max_record_count\":10000}";
#define MAX_VERTEX_NUM 10000

char g_label_schema[] = "[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
                        "\"type\":\"int32\"}],"
                        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
                        "\"index\":{\"type\":\"primary\"},\"raints\":{ \"unique\":true}}]}]";

char g_label_schema2[] = "[{\"type\":\"record\", \"name\":\"T30\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                         "{\"name\":\"F1\", \"type\":\"int32\",\"nullable\":true}],"
                         "\"keys\":[{\"node\":\"T30\", \"name\":\"T30_K0\", \"fields\":[\"F0\"], "
                         "\"index\":{\"type\":\"primary\"},\"raints\":{ \"unique\":true}}]}]";

class test_GmcInsertVertex : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void test_GmcInsertVertex::SetUpTestCase()
{
    system("$TEST_HOME/tools/start.sh");
    int32_t ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    void *label = NULL;
    void *label2 = NULL;
    void *label3 = NULL;
    char *test_schema = NULL;
    char *test_schema_type = NULL;
    char *test_schema_pro = NULL;

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name);
    GmcDropVertexLabel(stmt, label2_name);
    GmcDropVertexLabel(stmt, label3_name);
    GmcDropVertexLabel(stmt, label4_name);

    readJanssonFile("schema_file/GmcInsertVertex_test_type_schema.gmjson", &test_schema_type);
    ASSERT_NE((void *)NULL, test_schema_type);
    readJanssonFile("schema_file/GmcInsertVertex_test_insert_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema_type, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schema_file/GmcGetVertex_test_schema.gmjson", &test_schema_pro);
    ASSERT_NE((void *)NULL, test_schema_pro);
    ret = GmcCreateVertexLabel(stmt, test_schema_pro, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema_pro);
    free(test_schema);
    free(test_schema_type);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_GmcInsertVertex::TearDownTestCase()
{
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name);
    GmcDropVertexLabel(stmt, label2_name);
    GmcDropVertexLabel(stmt, label3_name);
    GmcDropVertexLabel(stmt, label4_name);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}
//
void test_GmcInsertVertex::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void test_GmcInsertVertex::TearDown()
{
    AW_CHECK_LOG_END();
}

// normal insert one record and query
TEST_F(test_GmcInsertVertex, DML_001_002_001)
{
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    uint32_t priK = 1;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, teststr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// abnormal insert: The parameter is NULL
TEST_F(test_GmcInsertVertex, DML_001_002_002)
{
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    uint32_t priK = 3;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver2";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(NULL);
    ASSERT_EQ(
        GMERR_NULL_VALUE_NOT_ALLOWED, ret);  // 实际错误码为GMERR_INVALID_PARAMETER_VALUE 非 GMERR_UNEXPECTED_NULL_VALUE
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// abnormal insert: vertex  not set property
TEST_F(test_GmcInsertVertex, DML_001_002_003)
{
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    uint32_t priK = 5;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver5";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // vertex  not set property
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// abnormal insert: vertex pk not set or nullable false property not set
TEST_F(test_GmcInsertVertex, DML_001_002_004)
{
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    uint32_t priK = 6;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver6";
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // pk not set
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // nullable false property not set
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// abnormal insert: before insert vertex has free
TEST_F(test_GmcInsertVertex, DML_001_002_005)
{
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    uint32_t priK = 7;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver7";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    stmt = NULL;
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// abnormal insert: insert the same vertex
TEST_F(test_GmcInsertVertex, DML_001_002_006)
{
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    void *query2_vertex = NULL;
    uint32_t priK = 8;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver8";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // insert second
    ret = GmcExecute(stmt);
    if (ret == GMERR_UNIQUE_VIOLATION) {
        ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    } else if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
        ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    } else {
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 查询
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, teststr);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, teststr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// abnormal insert: insert the same primary key property,the other properties change
TEST_F(test_GmcInsertVertex, DML_001_002_007)
{
    char errorMsg1[128] = {0}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    int32_t ret = 0;
    uint32_t value6 = 9;
    bool value7 = 0;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    char teststr3[] = "testver9";
    char teststr4[] = "second_test";

    // 设置属性值
    for (int8_t i = 0; i < 10; i++) {
        char teststr1 = 1 + i;
        ret = testGmcPrepareStmtByLabelName(stmt, label2_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
        ASSERT_EQ(GMERR_OK, ret);
        unsigned char teststr2 = 2 + i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
        ASSERT_EQ(GMERR_OK, ret);
        int8_t vaule1 = 1 + i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &vaule1, sizeof(int8_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t value2 = 10 + i;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        int16_t value3 = 100 + i;
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t value4 = 1000 + i;
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
        ASSERT_EQ(GMERR_OK, ret);
        int32_t value5 = 1000 + i;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        if (i % 2 == 0) {
            value7 = 0;
        } else {
            value7 = 1;
        }
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
        ASSERT_EQ(GMERR_OK, ret);
        int64_t value8 = 1000 + i;
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t value9 = 1000 + i;
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);
        float value10 = 1.8 + i;
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
        ASSERT_EQ(GMERR_OK, ret);
        double value11 = 9.111 + i;
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t value12 = 1000 + i;
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);
        if (i % 2 == 0) {
            ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
            ASSERT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr4, (strlen(teststr4)));
            ASSERT_EQ(GMERR_OK, ret);
        }

        // 插入顶点
        ret = GmcExecute(stmt);
        if (i == 0) {
            ASSERT_EQ(GMERR_OK, ret);
        } else {
            ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }

        bool isFinish = true;
        ret = testGmcPrepareStmtByLabelName(stmt, label2_name, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        // GET F0 and compare
        char F0val = 1;
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0val);
        ASSERT_EQ(GMERR_OK, ret);
        // Get F1
        unsigned char F1val = 2;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F2
        int8_t F2val = 1;
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F3
        uint8_t F3val = 10;
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F4
        int16_t F4val = 100;
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4val);
        ASSERT_EQ(GMERR_OK, ret);
        // Get F5
        uint16_t F5val = 1000;
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F6
        int32_t F6val = 1000;
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F7
        ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value6);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F8
        bool F8val = 0;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F9
        int64_t F9val = 1000;
        ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &F9val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F10
        uint64_t F10val = 1000;
        ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10val);
        ASSERT_EQ(GMERR_OK, ret);

        // Get F11
        float F11val = 1.8;
        ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11val);
        ASSERT_EQ(GMERR_OK, ret);
        // Get F12
        double F12val = 9.111;
        ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12val);
        ASSERT_EQ(GMERR_OK, ret);
        // Get F13
        uint64_t F13val = 1000;
        ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13val);
        ASSERT_EQ(GMERR_OK, ret);
        // Get F14
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// normal: vertex handle repeat use
TEST_F(test_GmcInsertVertex, DML_001_002_008)
{
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    void *query2_vertex = NULL;
    void *query3_vertex = NULL;
    uint32_t priK = 10;
    uint32_t priK2 = 12;
    uint32_t priK3 = 13;
    int32_t ret = 0;
    void *filter = NULL;
    char F1_value1[] = "testver10";
    char F1_value2[] = "vertex_insert2";
    char F1_value3[] = "vertex_insert3";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value1, (strlen(F1_value1)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value2, (strlen(F1_value2)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value3, (strlen(F1_value3)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK2, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, F1_value2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK3, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK3);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, F1_value3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, F1_value1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // ASSERT_EQ(GMERR_OK,ret);
}

// abnormal
TEST_F(test_GmcInsertVertex, DML_001_002_009)
{
    char errorMsg1[128] = {0}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *vertex3 = NULL;
    void *query_vertex = NULL;
    uint32_t priK = 18;
    int32_t ret = 0;
    void *filter = NULL;
    char F1_value[] = "vertex_insert";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // insert success
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value, (strlen(F1_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // insert
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value, (strlen(F1_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value, (strlen(F1_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, F1_value);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// normal: multi label mixing insert
TEST_F(test_GmcInsertVertex, DML_001_002_010)
{
    void *label = NULL;
    void *label2 = NULL;
    void *label3 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtT *stmt_2 = NULL;
    GmcStmtT *stmt_3 = NULL;
    GmcConnT *conn_t = NULL;
    uint32_t priK = 19;
    uint32_t priK2 = 20;
    uint32_t priK3 = 33;
    int32_t ret = 0;
    char F1Str[] = "vertex_insert";
    char F1Str2[] = "vertexInsertTest";
    int32_t F2Val = 2;
    int16_t F3Val = 5;
    int32_t F1Val2 = 1;
    int32_t F2Val2 = 2;
    int32_t F3Val2 = 3;

    ret = testGmcConnect(&conn_t);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_t, &stmt_1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_t, &stmt_2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_t, &stmt_3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_1, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_2, label3_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_3, label4_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // insert label_name
    ret = GmcSetVertexProperty(stmt_1, "F0", GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_1, "F1", GMC_DATATYPE_STRING, F1Str, (strlen(F1Str)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_1);
    ASSERT_EQ(GMERR_OK, ret);
    // insert label2_name
    ret = GmcSetVertexProperty(stmt_2, "F0", GMC_DATATYPE_UINT32, &priK2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_2, "F1", GMC_DATATYPE_STRING, F1Str2, (strlen(F1Str2)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_2, "F2", GMC_DATATYPE_UINT32, &F2Val, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_2, "F3", GMC_DATATYPE_INT16, &F3Val, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_2);
    ASSERT_EQ(GMERR_OK, ret);
    // insert label4_name
    ret = GmcSetVertexProperty(stmt_3, "F0", GMC_DATATYPE_UINT32, &priK3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_3, "F1", GMC_DATATYPE_INT32, &F1Val2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_3, "F2", GMC_DATATYPE_INT32, &F2Val2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt_3, "F3", GMC_DATATYPE_INT32, &F3Val2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_3);
    ASSERT_EQ(GMERR_OK, ret);
    // 第一张label
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt_1, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt_1, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt_1, lable_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt_1, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned int sizeF1;
    GmcGetVertexPropertySizeByName(stmt_1, "F1", &sizeF1);
    char *valueF1 = (char *)malloc(sizeF1);
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt_1, "F1", valueF1, sizeF1, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull == 0) {
        ret = strcmp(F1Str, valueF1);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF1);

    // 第二张label
    ret = testGmcPrepareStmtByLabelName(stmt_2, label3_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt_2, 0, GMC_DATATYPE_UINT32, &priK2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt_2, 1, GMC_DATATYPE_UINT32, &F2Val, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt_2, label3_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt_2, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned int sizeF11;
    bool isNull2;
    GmcGetVertexPropertySizeByName(stmt_2, "F1", &sizeF11);
    char *valueF11 = (char *)malloc(sizeF11);
    ret = GmcGetVertexPropertyByName(stmt_2, "F1", valueF11, sizeF11, &isNull2);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull2 == 0) {
        ret = strcmp(F1Str2, valueF11);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF11);
    // Get F2
    GmcGetVertexPropertySizeByName(stmt_2, "F2", &sizeF11);
    char *valueF12 = (char *)malloc(sizeF11);
    ret = GmcGetVertexPropertyByName(stmt_2, "F2", valueF12, sizeF11, &isNull2);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull2 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F2Val, valueF12);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF12);
    // Get F3
    GmcGetVertexPropertySizeByName(stmt_2, "F3", &sizeF11);
    char *valueF13 = (char *)malloc(sizeF11);
    ret = GmcGetVertexPropertyByName(stmt_2, "F3", valueF13, sizeF11, &isNull2);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull2 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F3Val, valueF13);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF13);

    // 第三张label
    ret = testGmcPrepareStmtByLabelName(stmt_3, label4_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt_3, 0, GMC_DATATYPE_UINT32, &priK3, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt_3, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_3);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt_3, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned int sizeF12 = 0;
    bool isNull3 = 0;
    GmcGetVertexPropertySizeByName(stmt_3, "F1", &sizeF12);
    char *valueF14 = (char *)malloc(sizeF12);
    ret = GmcGetVertexPropertyByName(stmt_3, "F1", valueF14, sizeF12, &isNull3);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull3 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F1Val2, valueF14);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF14);
    // Get F2
    GmcGetVertexPropertySizeByName(stmt_3, "F2", &sizeF12);
    char *valueF15 = (char *)malloc(sizeF12);
    ret = GmcGetVertexPropertyByName(stmt_3, "F2", valueF15, sizeF12, &isNull3);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull3 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Val2, valueF15);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF15);
    // Get F3
    GmcGetVertexPropertySizeByName(stmt_3, "F3", &sizeF12);
    char *valueF16 = (char *)malloc(sizeF12);
    ret = GmcGetVertexPropertyByName(stmt_3, "F3", valueF16, sizeF12, &isNull3);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull3 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F3Val2, valueF16);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF16);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt_2);
    GmcFreeStmt(stmt_3);
    ret = testGmcDisconnect(conn_t);
    EXPECT_EQ(GMERR_OK, ret);
}

// normal
TEST_F(test_GmcInsertVertex, DML_001_002_011)
{
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *vertex3 = NULL;
    void *query_vertex = NULL;
    uint32_t priKF0 = 30;
    uint32_t priKF2 = 40;
    uint16_t F3Val = 111;
    int32_t ret = 0;
    void *filter = NULL;
    char teststr[] = "testver";
    char F1_value[] = "vertex_insert";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label3_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // insert success
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3Val, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &priKF2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_value, (strlen(F1_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priKF0, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // query
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, label3_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priKF0, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &priKF2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, label3_name_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priKF0);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned int sizeF11;
    bool isNull2;
    GmcGetVertexPropertySizeByName(stmt, "F1", &sizeF11);
    char *valueF11 = (char *)malloc(sizeF11);
    ret = GmcGetVertexPropertyByName(stmt, "F1", valueF11, sizeF11, &isNull2);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull2 == 0) {
        ret = strcmp(F1_value, valueF11);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF11);
    // Get F2
    GmcGetVertexPropertySizeByName(stmt, "F2", &sizeF11);
    char *valueF12 = (char *)malloc(sizeF11);
    ret = GmcGetVertexPropertyByName(stmt, "F2", valueF12, sizeF11, &isNull2);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull2 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &priKF2, valueF12);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF12);
    // Get F3
    GmcGetVertexPropertySizeByName(stmt, "F3", &sizeF11);
    char *valueF13 = (char *)malloc(sizeF11);
    ret = GmcGetVertexPropertyByName(stmt, "F3", valueF13, sizeF11, &isNull2);
    ASSERT_EQ(GMERR_OK, ret);
    if (isNull2 == 0) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F3Val, valueF13);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ASSERT_EQ(GMERR_OK, 1);
    }
    free(valueF13);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // ASSERT_EQ(GMERR_OK,ret);
}

// all type test
TEST_F(test_GmcInsertVertex, DML_001_002_012)
{
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *vertex3 = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    int32_t ret = 0;
    char teststr[] = "testver6";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label2_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置属性值
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t vaule1 = 1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &vaule1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t value2 = 10;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t value3 = 100;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t value4 = 1000;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t value5 = 1000;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t value6 = 1000;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool value7 = 1;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t value8 = 1000;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value9 = 1000;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float value10 = 1.2;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double value11 = 10.86;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value12 = 1000;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char teststr3[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询
    //  F7 为主键
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, label2_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // 获取顶点
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // GET F0 and compare
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &vaule1);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F3
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value2);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value3);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F5
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value4);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F6
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value5);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F7
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value6);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F8
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value7);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F9
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value8);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F10
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value9);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F11
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value10);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F12
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F13
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value12);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F14
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr3);
    ASSERT_EQ(GMERR_OK, ret);
    // 资源释放与断链
    GmcFreeStmt(stmt);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(test_GmcInsertVertex, DML_001_002_013)
{
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *vertex3 = NULL;
    void *query_vertex = NULL;
    void *query2_vertex = NULL;
    void *query3_vertex = NULL;
    void *filter = NULL;
    char label100_name[] = "T100";
    uint32_t priK0 = 100;
    uint32_t priK1 = 2;
    uint32_t priK2 = 3;
    int32_t F1Val = 8;
    int32_t F1Val2 = 9;
    int32_t ret = 0;
    char *schematest = NULL;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schema_file/GmcGetVertexProperty_test_nullable_schema.gmjson", &schematest);
    ASSERT_NE((void *)NULL, schematest);
    // 创建之前先删表
    ret = GmcDropVertexLabel(stmt, label100_name);
    ret = GmcCreateVertexLabel(stmt, schematest, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schematest);
    ret = testGmcPrepareStmtByLabelName(stmt, label100_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // insert success
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, label100_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T100_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F3
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F5
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F6
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F7
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &priK0);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F8
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F9
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F10
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F11
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F12
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F13
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F14
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F15
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT32, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testGmcPrepareStmtByLabelName(stmt, label100_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &priK1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t value1 = 1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t value2 = 10;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t value3 = 100;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t value4 = 1000;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t value5 = 1000;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool value7 = 1;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t value8 = 1000;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value9 = 1000;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float value10 = 1.2;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double value11 = 10.86;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value12 = 1000;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char teststr3[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr3, strlen(teststr3));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t value13 = 107;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT32, &value13, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询
    ret = testGmcPrepareStmtByLabelName(stmt, label100_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T100_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value1);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F3
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value2);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value3);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value4);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F6
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value5);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F7
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &priK1);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F8
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value7);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F9
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value8);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F10
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value9);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F11
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value10);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value12);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr3);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT32, &value13);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testGmcPrepareStmtByLabelName(stmt, label100_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &priK2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT32, NULL, 0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询
    ret = testGmcPrepareStmtByLabelName(stmt, label100_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T100_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F3
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F5
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F6
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F7
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &priK2);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F8
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F9
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F10
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Get F11
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F12
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F13
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F14
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F15
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT32, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label100_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 属性带默认值进行未设置与设置插入与查询
TEST_F(test_GmcInsertVertex, DML_001_002_014)
{
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *query_vertex = NULL;
    void *query2_vertex = NULL;
    void *query3_vertex = NULL;
    void *filter = NULL;
    char *schematest = NULL;
    char label6_name[] = "T40";
    uint32_t priK0 = 1;
    uint32_t priK1 = 2;
    uint32_t priK2 = 3;
    int32_t F1Val = 5;
    char F6val = 'a';
    unsigned char F7val = 'f';
    int8_t F2val = 8;
    uint8_t F3val = 9;
    int16_t F4val = 20;
    uint16_t F5val = 30;
    uint32_t F8val = 1;
    int64_t F9val = 1000;
    uint64_t F10val = 10000;
    float F11val = 1.58;
    double F12val = 5.55;
    uint64_t F13val = 1000;
    char F14val[] = "testvertex";
    uint32_t F15val = 100;
    int32_t F1Val2 = 500;
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/GmcInsertVertex_test_type_schema_default.gmjson", &schematest);
    ASSERT_NE((void *)NULL, schematest);
    // 创建之前先删表
    ret = GmcDropVertexLabel(stmt, label6_name);
    ret = GmcCreateVertexLabel(stmt, schematest, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schematest);
    ret = testGmcPrepareStmtByLabelName(stmt, label6_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // insert:带默认值未设置属性进行插入并查询
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, label6_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T40_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // GET F0 and compare
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK0);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F6
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_INT32, &F1Val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_CHAR, &F6val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UCHAR, &F7val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &F9val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13val);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14val);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT32, &F15val);
    ASSERT_EQ(GMERR_OK, ret);

    // 带默认值属性进行设置属性值并查询
    ret = testGmcPrepareStmtByLabelName(stmt, label6_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Val2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    char teststr1 = '7';
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char teststr2 = 'g';
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t value1 = -1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t value2 = 105;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t value3 = -100;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t value4 = 305;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool value7 = 0;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t value8 = 737;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value9 = 599;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float value10 = 8.86;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double value11 = 50.765;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value12 = 453;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char teststr3[] = "string_test";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr3, strlen(teststr3));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t value13 = 539;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT32, &value13, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询
    ret = testGmcPrepareStmtByLabelName(stmt, label6_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK1, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T40_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // GET F0 and compare
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &priK1);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_INT32, &F1Val2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_CHAR, &teststr1);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F1
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UCHAR, &teststr2);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value1);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F3
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value2);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value3);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value4);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F8
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value7);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F9
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value8);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F10
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value9);
    EXPECT_EQ(GMERR_OK, ret);

    // Get F11
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value10);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value12);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr3);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT32, &value13);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label6_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *writeAndReadThead(void *args)
{
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;
    char label5_name[] = "T30";
    uint32_t priK0 = 1;
    uint32_t priK1 = 2;
    uint32_t priK2 = 3;
    int32_t F1Val = 0;
    int32_t F1Val2 = 1;
    int32_t ret = 0;
    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    // insert recored
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t, label5_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "F0", GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "F1", GMC_DATATYPE_INT32, &F1Val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            do {
                usleep(100);
                ret = GmcExecute(stmt_t);
            } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        } else if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_PRIMARY_KEY_VIOLATION) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        // 查询
        bool isFinish = true;
        ret = testGmcPrepareStmtByLabelName(stmt_t, label5_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "T30_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt_t, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt_t, "F0", GMC_DATATYPE_UINT32, &priK0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt_t, "F1", GMC_DATATYPE_INT32, &F1Val);
        EXPECT_EQ(GMERR_OK, ret);
        priK0++;
        F1Val++;
    }
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 多个线程并发读写一个vertexLabel
TEST_F(test_GmcInsertVertex, DML_001_002_015)
{
    char errorMsg1[128] = {0};
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;
    char label5_name[16] = "T30";
    ret = testGmcConnect(&conn_t, &stmt_t);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建之前先删表
    ret = GmcDropVertexLabel(stmt_t, label5_name);
    ret = GmcCreateVertexLabel(stmt_t, g_label_schema2, g_label_config_test);
    ASSERT_EQ(GMERR_OK, ret);
    int err = 0;
    pthread_t wrth[CONCURRENT_CONN_SIZE];
    int threadNum = CONCURRENT_CONN_SIZE;
#if defined ENV_RTOSV2X
    uint32_t existConnNum = 0;
    uint32_t maxConnNum = 64;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LE(0, existConnNum);
    threadNum = 64 - 4 - existConnNum;
    threadNum = (CONCURRENT_CONN_SIZE - 4) > (maxConnNum - 4 - existConnNum) ? (maxConnNum - 4 - existConnNum) :
                                                                               (CONCURRENT_CONN_SIZE - 4);
    AW_FUN_Log(LOG_INFO, "CONCURRENT_CONN_SIZE :%d, existConnNum:%u.", CONCURRENT_CONN_SIZE, existConnNum);
#endif
    AW_FUN_Log(LOG_INFO, "threadNum = %d.", threadNum);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_attr_t pThreadAttrs;
    pthread_attr_init(&pThreadAttrs);
    // 创建线程时，设置线程属性的stack size为128kb，也可以用ulimit -s 128直接设置系统的stack size默认值
    pthread_attr_setstacksize(&pThreadAttrs, 131072);
    for (int i = 0; i < threadNum; i++) {
        err = pthread_create(&wrth[i], NULL, writeAndReadThead, NULL);
        ASSERT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(wrth[i], NULL);
    }

    err = pthread_attr_destroy(&pThreadAttrs);
    EXPECT_EQ(GMERR_OK, err);

    ret = testGmcConnect(&conn_t, &stmt_t);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt_t, label5_name);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
}

// test 018：超时写数据：迭代一不支持
TEST_F(test_GmcInsertVertex, test_GmcInsertVertex_018)
{
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *vertex3 = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    int32_t ret = 0;
    char teststr[] = "testver6";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label2_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置属性值
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t vaule1 = 1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &vaule1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t value2 = 10;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t value3 = 100;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t value4 = 1000;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t value5 = 1000;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t value6 = 500;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool value7 = 1;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t value8 = 1000;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value9 = 1000;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float value10 = 1.2;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double value11 = 10.86;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value12 = 1000;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char teststr3[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 资源释放与断链
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // ASSERT_EQ(GMERR_OK,ret);
}

// 属性带char与unchar类型默认值为字符与数字形式进行插入与查询
TEST_F(test_GmcInsertVertex, DML_001_002_021)
{
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    char *schematest = NULL;
    char char_label_name[16] = "T300";
    uint32_t priK0 = 1;
    uint32_t priK1 = 2;
    char F1val = 'a';
    unsigned char F2val = 'f';
    char F3val = '1';
    unsigned char F4val = '2';
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/GmcInsertVertex_char_type_schema_default.gmjson", &schematest);
    ASSERT_NE((void *)NULL, schematest);
    // 创建之前先删表
    ret = GmcDropVertexLabel(stmt, char_label_name);
    ret = GmcCreateVertexLabel(stmt, schematest, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schematest);
    ret = testGmcPrepareStmtByLabelName(stmt, char_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // insert:带默认值未设置属性进行插入并查询
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询
    bool isFinish = true;
    ret = testGmcPrepareStmtByLabelName(stmt, char_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T300_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_CHAR, &F1val);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_UCHAR, &F2val);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_CHAR, &F3val);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_UCHAR, &F4val);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置
    ret = testGmcPrepareStmtByLabelName(stmt, char_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priK1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    char F1val2 = 10;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_CHAR, &F1val2, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char F2val2 = 20;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UCHAR, &F2val2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    char F3val2 = 'f';
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_CHAR, &F3val2, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char F4val2 = 'd';
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UCHAR, &F4val2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询
    ret = testGmcPrepareStmtByLabelName(stmt, char_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK1, sizeof(uint32_t));
    // Query Vertex
    ret = GmcSetIndexKeyName(stmt, "T300_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_CHAR, &F1val2);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F2
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_UCHAR, &F2val2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_CHAR, &F3val2);
    ASSERT_EQ(GMERR_OK, ret);
    // Get F4
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_UCHAR, &F4val2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, char_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    // 资源释放与断链
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *writeAndReadDiffDatasThead(void *args)
{
    void *label = NULL;
    void *vertex = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;
    char label5_name[] = "T30";
    int32_t startInsert = *(int32_t *)args;
    uint32_t priK0 = (uint32_t)startInsert;
    int32_t F1Val = startInsert;
    int32_t ret = 0;
    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    // insert recored
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t, label5_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "F0", GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "F1", GMC_DATATYPE_INT32, &F1Val, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        int32_t circleNum = 0;
        while (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
            circleNum++;
            ret = GmcSetVertexProperty(stmt_t, "F0", GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t, "F1", GMC_DATATYPE_INT32, &F1Val, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt_t);
            if (ret == GMERR_OK || ret == GMERR_UNIQUE_VIOLATION) {
                break;
            } else if (ret != GMERR_LOCK_NOT_AVAILABLE && ret != GMERR_LOCK_NOT_AVAILABLE &&
                       ret != GMERR_LOCK_NOT_AVAILABLE) {
                EXPECT_EQ(GMERR_OK, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                break;
            }
            if (circleNum == 10) {
                EXPECT_EQ(GMERR_OK, ret);
                printf("Retransmission 10 times error !!! line:%d\n", __LINE__);
                break;
            }
        }
        if (ret != GMERR_OK && ret != GMERR_UNIQUE_VIOLATION) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        // 查询
        bool isFinish = false;
        ret = testGmcPrepareStmtByLabelName(stmt_t, label5_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &priK0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "T30_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt_t, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt_t, "F0", GMC_DATATYPE_UINT32, &priK0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt_t, "F1", GMC_DATATYPE_INT32, &F1Val);
        EXPECT_EQ(GMERR_OK, ret);
        priK0++;
        F1Val++;
    }
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 多个线程并发读写一个vertexLabel,多线程之间读写不同数据
TEST_F(test_GmcInsertVertex, DML_001_002_022)
{
    int32_t ret = 0;
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;
    char label5_name[16] = "T30";
    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(stmt_t, label5_name);
    ret = GmcCreateVertexLabel(stmt_t, g_label_schema2, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    int err = 0;
    pthread_t wrth[CONCURRENT_CONN_SIZE];
    int32_t a[CONCURRENT_CONN_SIZE] = {0};
    int threadNum = CONCURRENT_CONN_SIZE;
#if defined ENV_RTOSV2X
    uint32_t existConnNum = 0;
    uint32_t maxConnNum = 64;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LE(0, existConnNum);
    threadNum = 64 - 4 - existConnNum;
    threadNum = (CONCURRENT_CONN_SIZE - 4) > (maxConnNum - 4 - existConnNum) ? (maxConnNum - 4 - existConnNum) :
                                                                               (CONCURRENT_CONN_SIZE - 4);
    AW_FUN_Log(LOG_INFO, "CONCURRENT_CONN_SIZE :%d, existConnNum:%u.", CONCURRENT_CONN_SIZE, existConnNum);
#endif
    AW_FUN_Log(LOG_INFO, "threadNum = %d.", threadNum);
    for (int i = 0; i < threadNum; i++) {
        a[i] = (i * 100);
        err = pthread_create(&wrth[i], NULL, writeAndReadDiffDatasThead, &a[i]);
        ASSERT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(wrth[i], NULL);
    }
    ret = GmcDropVertexLabel(stmt_t, label5_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
}

// test 016 数据库故障后写数据失败
TEST_F(test_GmcInsertVertex, DML_001_002_016)
{
#ifndef DIRECT_WRITE
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *label = NULL;
    void *vertex = NULL;
    void *vertex2 = NULL;
    void *vertex3 = NULL;
    void *query_vertex = NULL;
    void *filter = NULL;
    int32_t ret = 0;
    char teststr[] = "testver6";
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label2_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置属性值
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t vaule1 = 1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &vaule1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t value2 = 10;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t value3 = 100;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t value4 = 1000;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t value5 = 1000;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t value6 = 500;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool value7 = 1;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t value8 = 1000;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value9 = 1000;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float value10 = 1.2;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double value11 = 10.86;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t value12 = 1000;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char teststr3[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
    ASSERT_EQ(GMERR_OK, ret);
    // server故障退出
    system("$TEST_HOME/tools/stop.sh -f");
    GmcDetachAllShmSeg();
    system("$TEST_HOME/tools/start.sh -f");  // 数据库服务故障测试用例，需重启服务
    // 插入顶点
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);

    // 资源释放与断链
    //  ret = testGmcDisconnect(conn,stmt);
    //  EXPECT_EQ(GMERR_OK, ret);
    //  用例本身为预期断连失败场景, 此处临时规避断连core, 后续统一整改(添加返回值)
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    pthread_mutex_lock(&g_connLock);
    --g_connOnline;
    pthread_mutex_unlock(&g_connLock);
    pthread_mutex_lock(&g_connConcurrent);
    --g_connRequest;
    pthread_mutex_unlock(&g_connConcurrent);
#endif
}
