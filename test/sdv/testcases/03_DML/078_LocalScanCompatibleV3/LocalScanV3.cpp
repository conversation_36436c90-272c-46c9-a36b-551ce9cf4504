/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2027. All rights reserved.
 * File Name:localScanCompatibleV3.cpp
 * Author: yaosiyuan ywx758883
 * Date: 2021-10-23
 * Describle:
 */

#include "gtest/gtest.h"
#include "syCommon.h"
#include "t_datacom_lite.h"
#include "vertex.h"

class localScanCompatibleV3 : public testing::Test {
public:
    virtual void SetUp()
    {
        mallocSubData(&g_userData, 1000);
        AW_CHECK_LOG_BEGIN();
    };
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        freeMallocSqace(g_userData);
    };
    static void SetUpTestCase()
    {
        int ret = 0;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = 0;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
    };
};

// 排序索引使用int8作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_001)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F9\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_INT8, conn);
    vertexLocalDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_INT8);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用uint8作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_002)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F2\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_UINT8, conn);
    vertexLocalDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_UINT8);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用int64作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_003)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F7\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_INT64, conn);
    vertexLocalDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_INT64);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用int64作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_004)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F8\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_UINT64, conn);
    vertexLocalDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_DATATYPE_UINT64);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用char作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_005)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F12\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, 1, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_CHAR, conn);
    vertexLocalDelete(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_CHAR);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用uchar作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_006)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F13\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, 1, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_UCHAR, conn);
    vertexLocalDelete(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_UCHAR);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用fixed作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_007)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F16\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, 1, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_FIXED, conn);
    vertexLocalDelete(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_FIXED);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引使用time作为索引字段，写数据后通过GmcSetIndexKeyValue设置单值进行time更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_008)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F11\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, 1, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    vertexLocalkeyUpdate(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_TIME, conn);
    vertexLocalDelete(RECORDCOUNTSTART, 1, stmt, labelName, localkey, GMC_DATATYPE_TIME);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，设置int8类型字段作为索引，写数据，进行范围扫描删除数据
TEST_F(localScanCompatibleV3, DML_078_009)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F9\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, 1, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = vertexLimitSingeLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, 1, GMC_DATATYPE_INT8, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLimitSingeLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, 1, GMC_DATATYPE_INT8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，设置int64类型字段作为索引，写数据，进行范围扫描删除数据
TEST_F(localScanCompatibleV3, DML_078_010)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\", \"fields\":[\"F7\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, 1, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = vertexLimitSingeLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, 1, GMC_DATATYPE_INT64, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLimitSingeLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, 1, GMC_DATATYPE_INT64);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，每个索引字段设置左右值，写数据，进行范围扫描删除数据
TEST_F(localScanCompatibleV3, DML_078_011)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLimitManyLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，只设置第一个字段的左右值，进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_012)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned long long r_val = RECORDCOUNTEND;
    unsigned int ValsNum = 1;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    /* leftKeyProps[1].type = GMC_DATATYPE_INT16;
    leftKeyProps[1].value = NULL;
    leftKeyProps[1].size = sizeof(int16_t); */
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t rk_v0 = r_val;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    /* rightKeyProps[1].type = GMC_DATATYPE_INT16;
    rightKeyProps[1].value = NULL;
    rightKeyProps[1].size = sizeof(int16_t); */
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    int16_t F3 = 0;
    bool isNull = 1;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &F3, sizeof(F3), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("[info]F3=%d\n", F3);
    }
    EXPECT_EQ(RECORDCOUNTEND, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，设置第一个字段范围[1,8]，第二个字段范围为[2,5]进行扫描删除
TEST_F(localScanCompatibleV3, DML_078_013)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 2;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val + 1;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    int16_t lk_v1 = l_val + 2;
    leftKeyProps[1].type = GMC_DATATYPE_INT16;
    leftKeyProps[1].value = &lk_v1;
    leftKeyProps[1].size = sizeof(int16_t);
    GmcPropValueT rightKeyProps[ValsNum];
    int8_t rk_v0 = l_val + 8;
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    int16_t rk_v1 = l_val + 5;
    rightKeyProps[1].type = GMC_DATATYPE_INT16;
    rightKeyProps[1].value = &rk_v1;
    rightKeyProps[1].size = sizeof(int16_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    int16_t F3 = 0;
    bool isNull = 1;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &F3, sizeof(F3), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(8, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，设置第一个字段范围[1,8]，第二个字段设置等值进行扫描删除
TEST_F(localScanCompatibleV3, DML_078_014)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 2;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val + 1;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    leftKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t lk_v1 = l_val + 2;
    leftKeyProps[1].value = &lk_v1;
    leftKeyProps[1].size = sizeof(int16_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t rk_v0 = l_val + 8;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    rightKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t rk_v1 = l_val + 2;
    rightKeyProps[1].value = &rk_v1;
    rightKeyProps[1].size = sizeof(int16_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    EXPECT_EQ(8, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，设置第一个字段范围，第二个字段设置等值第三个字段设置范围进行扫描删除
TEST_F(localScanCompatibleV3, DML_078_015)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 3;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val + 1;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    leftKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t lk_v1 = l_val + 2;
    leftKeyProps[1].value = &lk_v1;
    leftKeyProps[1].size = sizeof(int16_t);
    int32_t lk_v2 = l_val;
    leftKeyProps[2].type = GMC_DATATYPE_INT32;
    leftKeyProps[2].value = &lk_v2;
    leftKeyProps[2].size = sizeof(int32_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t rk_v0 = l_val + 10;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    rightKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t rk_v1 = l_val + 2;
    rightKeyProps[1].value = &rk_v1;
    rightKeyProps[1].size = sizeof(int16_t);
    rightKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t rk_v2 = l_val + 9;
    rightKeyProps[2].value = &rk_v2;
    rightKeyProps[2].size = sizeof(int32_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，设置第一个字段为1，第二个字段为[1,8]，第三个字段为[1,3]
// :将第一个索引字段改为1第二个范围扫描第三个范围扫描，扫描范围异常
TEST_F(localScanCompatibleV3, DML_078_016)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 3;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val + 1;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    leftKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t lk_v1 = l_val + 1;
    leftKeyProps[1].value = &lk_v1;
    leftKeyProps[1].size = sizeof(int16_t);
    leftKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t lk_v2 = l_val + 1;
    leftKeyProps[2].value = &lk_v2;
    leftKeyProps[2].size = sizeof(int32_t);
    GmcPropValueT rightKeyProps[ValsNum];
    int8_t rk_v0 = l_val + 1;
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    rightKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t rk_v1 = l_val + 8;
    rightKeyProps[1].value = &rk_v1;
    rightKeyProps[1].size = sizeof(int16_t);
    rightKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t rk_v2 = l_val + 3;
    rightKeyProps[2].value = &rk_v2;
    rightKeyProps[2].size = sizeof(int32_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    EXPECT_EQ(1, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，设置第一个字段为等值1，第二个字段为范围，第三个字段为范围，第四个字段为等值
TEST_F(localScanCompatibleV3, DML_078_017)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 4;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val + 1;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    leftKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t lk_v1 = l_val + 1;
    leftKeyProps[1].value = &lk_v1;
    leftKeyProps[1].size = sizeof(int16_t);
    leftKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t lk_v2 = l_val + 1;
    leftKeyProps[2].value = &lk_v2;
    leftKeyProps[2].size = sizeof(int32_t);
    leftKeyProps[3].type = GMC_DATATYPE_INT64;
    int64_t lk_v3 = l_val;
    leftKeyProps[3].value = &lk_v3;
    leftKeyProps[3].size = sizeof(int64_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t rk_v0 = l_val + 1;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    rightKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t rk_v1 = l_val + 1;
    rightKeyProps[1].value = &rk_v1;
    rightKeyProps[1].size = sizeof(int16_t);
    rightKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t rk_v2 = l_val + 3;
    rightKeyProps[2].value = &rk_v2;
    rightKeyProps[2].size = sizeof(int32_t);
    rightKeyProps[3].type = GMC_DATATYPE_INT64;
    int64_t rk_v3 = l_val;
    rightKeyProps[3].value = &rk_v3;
    rightKeyProps[3].size = sizeof(int64_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < 4; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    EXPECT_EQ(1, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，4个索引字段，设置第一个字段为等值1，第二个字段为范围，第三个字段为范围，第四个字段为等值
TEST_F(localScanCompatibleV3, DML_078_018)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 4;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t lk_v0 = l_val + 1;
    leftKeyProps[0].value = &lk_v0;
    leftKeyProps[0].size = sizeof(int8_t);
    leftKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t lk_v1 = l_val + 1;
    leftKeyProps[1].value = &lk_v1;
    leftKeyProps[1].size = sizeof(int16_t);
    leftKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t lk_v2 = l_val + 1;
    leftKeyProps[2].value = &lk_v2;
    leftKeyProps[2].size = sizeof(int32_t);
    leftKeyProps[3].type = GMC_DATATYPE_INT64;
    int64_t lk_v3 = l_val + 1;
    leftKeyProps[3].value = &lk_v3;
    leftKeyProps[3].size = sizeof(int64_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    int8_t rk_v0 = l_val + 1;
    rightKeyProps[0].value = &rk_v0;
    rightKeyProps[0].size = sizeof(int8_t);
    rightKeyProps[1].type = GMC_DATATYPE_INT16;
    int16_t rk_v1 = l_val + 5;
    rightKeyProps[1].value = &rk_v1;
    rightKeyProps[1].size = sizeof(int16_t);
    rightKeyProps[2].type = GMC_DATATYPE_INT32;
    int32_t rk_v2 = l_val + 6;
    rightKeyProps[2].value = &rk_v2;
    rightKeyProps[2].size = sizeof(int32_t);
    rightKeyProps[3].type = GMC_DATATYPE_INT64;
    int64_t rk_v3 = l_val + 1;
    rightKeyProps[3].value = &rk_v3;
    rightKeyProps[3].size = sizeof(int64_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    EXPECT_EQ(1, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，第一个字段不设置值，进行等值查询更新删除
// :暂不支持待dm模块合入后补充 19-24
TEST_F(localScanCompatibleV3, DML_078_019)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，第三个字段不设置值，进行等值查询更新删除
TEST_F(localScanCompatibleV3, DML_078_020)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，最后一个字段不设置值，进行等值查询更新删除
TEST_F(localScanCompatibleV3, DML_078_021)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，第一个字段不设置值，进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_022)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，第三个字段不设置值，进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_023)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，8个索引字段，最后一个字段不设置值，进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_024)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，范围扫描范围删除只设置了左值，右值为NULL
TEST_F(localScanCompatibleV3, DML_078_025)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 1;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    leftKeyProps[0].value = &l_val;
    leftKeyProps[0].size = sizeof(int8_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    // rightKeyProps[0].value = &r_val; // 扫描结果有差异
    rightKeyProps[0].value = NULL;
    rightKeyProps[0].size = sizeof(int8_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = &leftKeyProps[i];
        items[i].rValue = NULL;
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    uint32_t f1 = 0;
    bool isNull;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        scanNumber++;
    }
    EXPECT_EQ(RECORDCOUNTEND, scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，范围扫描范围删除只设置了右值，左值为NULL
TEST_F(localScanCompatibleV3, DML_078_026)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned long long l_val = RECORDCOUNTSTART;
    unsigned int ValsNum = 1;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    leftKeyProps[0].value = NULL;
    // leftKeyProps[0].value = &r_val;
    leftKeyProps[0].size = sizeof(int8_t);
    GmcPropValueT rightKeyProps[ValsNum];
    int8_t l_aval = l_val + 1;
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    rightKeyProps[0].value = &l_aval;
    rightKeyProps[0].size = sizeof(int8_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = NULL;
        items[i].rValue = &rightKeyProps[i];
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    printf("[info]scanNumber=%d\n", scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 排序索引，范围扫描范围删除只设置了左右值均为NULL
TEST_F(localScanCompatibleV3, DML_078_027)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    // 范围扫描
    bool isFinish = false;
    unsigned int ValsNum = 1;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    leftKeyProps[0].value = NULL;
    // leftKeyProps[0].value = &r_val;
    leftKeyProps[0].size = sizeof(int8_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    rightKeyProps[0].value = NULL;
    rightKeyProps[0].size = sizeof(int8_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = NULL;
        items[i].rValue = NULL;
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    printf("[info]scanNumber=%d\n", scanNumber);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 导入表，8个索引字段，通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_028)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = toolModelOperation(GMEXPORT, (char *)"vdata", NULL, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    char *localkey = (char *)"local";
    ret = vertexLocalkeyMultiField(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalkeyMultiField(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalkeyMultiField(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 导入表，8个索引字段，进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_029)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLimitManyLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 8个索引字段，通过GmcSetIndexKeyValue设置单值进行local批量更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_030)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": false }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    ret = vertexLocalkeyBatchUpdate(
        RECORDCOUNTSTART, RECORDCOUNTEND, stmt, conn, labelName, localkey, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalkeyMultiField(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，8个索引字段，通过GmcSetIndexKeyValue设置单值进行local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_031)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    char *subName = (char *)"subVertexLabel";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexSnCallback, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    char *localkey = (char *)"local";
    ret = vertexLocalkeyMultiField(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalkeyMultiField(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，8个索引字段，进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_032)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    char *subName = (char *)"subVertexLabel";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexSnCallback, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLimitManyLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，创建多个namespace，进行等值local更新、删除扫描数据再进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_033)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    char nameSpace[100];
    char *userName = (char *)"username";
    char *subName = (char *)"subVertexLabel";
    int nameSpaceNum = 10;
    for (int i = 0; i < nameSpaceNum; i++) {
        sprintf(nameSpace, "nameSpace_%d", i);
        ret = GmcCreateNamespace(stmt, nameSpace, userName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUseNamespace(stmt, nameSpace);
        EXPECT_EQ(GMERR_OK, ret);
        char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                    "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                    "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
        ret = spliceSchemaCreateTable(stmt, schemaSring);
        EXPECT_EQ(GMERR_OK, ret);
        GmcSubConfigT tmp_schema;
        tmp_schema.subsName = subName;
        tmp_schema.configJson = subAllType;
        ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexSnCallback, g_userData);
        EXPECT_EQ(GMERR_OK, ret);
        writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
        ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
            GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);
        ret = vertexLimitManyLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
            GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC, conn);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < nameSpaceNum; i++) {
        sprintf(nameSpace, "nameSpace_%d", i);
        ret = GmcUseNamespace(stmt, nameSpace);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(stmt, nameSpace);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写满内存，进行范围扫描
TEST_F(localScanCompatibleV3, DML_078_034)
{
    const char *schemaData =
        R"([{
        "version":"2.0",
        "type":"record",
        "name":"schema_datatype",
        "fields":[
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"string"},
            { "name":"F3", "type":"string"},
            { "name":"F4", "type":"string"},
            { "name":"F5", "type":"string"},
            { "name":"F6", "type":"string"},
            { "name":"F7", "type":"string"},
            { "name":"F8", "type":"string"}
        ],
        "keys":
        [
            {
                "name":"PK",
                "fields":["F1"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "name":"local",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    }])";
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaData, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[13312];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[13311] = '\0';
    ret = vertexWriteBigObject(RECORDCOUNTSTART, 99999999, stmt, labelName, stringValue);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    // 范围扫描
    bool isFinish = false;
    unsigned int ValsNum = 1;
    char *keyName = (char *)"local";
    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = NULL;
    leftKeyProps[0].size = sizeof(uint32_t);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = NULL;
    rightKeyProps[0].size = sizeof(uint32_t);
    GmcRangeItemT items[ValsNum];
    for (int i = 0; i < ValsNum; i++) {
        items[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[i].lValue = NULL;
        items[i].rValue = NULL;
        items[i].order = GMC_ORDER_ASC;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int scanNumber = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        scanNumber++;
    }
    EXPECT_EQ(scanNumber, g_maxRecordCout);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写满内存，进行等值扫描
TEST_F(localScanCompatibleV3, DML_078_035)
{
    const char *schemaData =
        R"([{
        "version":"2.0",
        "type":"record",
        "name":"schema_datatype",
        "fields":[
            { "name":"F1", "type":"uint32" },
            { "name":"F2", "type":"string" },
            { "name":"F3", "type":"string" },
            { "name":"F4", "type":"string" },
            { "name":"F5", "type":"string" },
            { "name":"F6", "type":"string" },
            { "name":"F7", "type":"string" },
            { "name":"F8", "type":"string" },
            { "name":"F9", "type":"uint64", "default":1 }
        ],
        "keys":
        [
            {
                "name":"PK",
                "fields":["F1"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "name":"local",
                "fields":["F1"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            }
        ]
    }])";
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaData, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[13312];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[13311] = '\0';
    ret = vertexWriteBigObject(RECORDCOUNTSTART, 9999999, stmt, labelName, stringValue);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    // local更新
    char *keyName = (char *)"local";
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    bool isNull;
    bool isFinish = false;
    uint64_t F9;
    for (uint32_t i = RECORDCOUNTSTART; i < g_maxRecordCout; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                isFinish = false;
                break;
            }
            ret = GmcGetVertexPropertyByName(stmt, "F9", &F9, sizeof(uint64_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, F9);
        }
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多线程并发，范围扫描、范围删除、等值更新删除
TEST_F(localScanCompatibleV3, DML_078_036)
{
    AddWhiteList(GMERR_NO_DATA);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSringPk = (char *)"\n%*s{ \"name\":\"pk\", \"node\":\"schema_datatype\","
                                  "\"fields\":[\"F1\"],"
                                  "\"index\":{ \"type\":\"primary\" }, \"constraints\": { \"unique\": true }},";
    char *schemaSringLocal = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                     "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                     "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaBigObj);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringPk, 12, " ", 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringLocal, 12, " ", 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    GmcDropVertexLabel(stmt, schemaJson);
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    // 多线程
    int thr_num = 10;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[5] = {threadVertexBigObjWrite, threadLocalLimitDelete, threadLocalLimitScan,
        threadLocalMultiDelete, threadLocalMultiUpdate};
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 5], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多线程并发，范围扫描、索引更新，覆盖写
TEST_F(localScanCompatibleV3, DML_078_037)
{
    AddWhiteList(GMERR_NO_DATA);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSringPk = (char *)"\n%*s{ \"name\":\"pk\", \"node\":\"schema_datatype\","
                                  "\"fields\":[\"F1\"],"
                                  "\"index\":{ \"type\":\"primary\" }, \"constraints\": { \"unique\": true }},";
    char *schemaSringLocal = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                     "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                     "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypes);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringPk, 12, " ", 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringLocal, 12, " ", 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    GmcDropVertexLabel(stmt, schemaJson);
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    // 多线程
    int thr_num = 1;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[5] = {
        threadVertexWrite, threadLocalLimitDelete, threadLocalLimitScan, threadLocalMultiDelete, threadVertexPkUpdate};
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 5], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多线程并发，多种扫描交互
TEST_F(localScanCompatibleV3, DML_078_038)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSringPk = (char *)"%*s{ \"name\":\"pk\", \"node\":\"schema_datatype\","
                                  "\"fields\":[\"F1\"],"
                                  "\"index\":{ \"type\":\"primary\" }, \"constraints\": { \"unique\": true }},";
    char *schemaSringLocal = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                     "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                     "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypes);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringPk, 12, " ", 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringLocal, 12, " ", 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);
    // 多线程
    int thr_num = 10;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[4] = {threadVertexWrite, threadVertexPkUpdate, threadLocalLimitScan, threadLocalLimitScan};
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 4], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 小对象变大对象，再进行范围扫描删除
TEST_F(localScanCompatibleV3, DML_078_039)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSringPk = (char *)"%*s{ \"name\":\"pk\", \"node\":\"schema_datatype\","
                                  "\"fields\":[\"F1\"],"
                                  "\"index\":{ \"type\":\"primary\" }, \"constraints\": { \"unique\": true }},";
    char *schemaSringLocal = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                     "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                     "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaBigObj);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringPk, 12, " ", 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringLocal, 12, " ", 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);

    char fixValue[529];
    memset(fixValue, 'a', sizeof(fixValue) - 1);
    fixValue[528] = '\0';
    char stringValue[1024];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1023] = '\0';
    char *labelName = (char *)"schema_datatype";
    writeBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, fixValue, stringValue);
    ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    char stringValue1[13312];
    memset(stringValue1, 'a', sizeof(stringValue1) - 1);
    stringValue1[13311] = '\0';
    writeBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, fixValue, stringValue);
    ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 小对象变大对象，再进行等值扫描更新
TEST_F(localScanCompatibleV3, DML_078_040)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSringPk = (char *)"%*s{ \"name\":\"pk\", \"node\":\"schema_datatype\","
                                  "\"fields\":[\"F1\"],"
                                  "\"index\":{ \"type\":\"primary\" }, \"constraints\": { \"unique\": true }},";
    char *schemaSringLocal = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                     "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                     "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaBigObj);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringPk, 12, " ", 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaSringLocal, 12, " ", 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schemaJson);

    char fixValue[529];
    memset(fixValue, 'a', sizeof(fixValue) - 1);
    fixValue[528] = '\0';
    char stringValue[1024];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1023] = '\0';
    char *labelName = (char *)"schema_datatype";
    writeBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, fixValue, stringValue);
    char *localkey = (char *)"local";
    ret = vertexLocalkeyMultiFieldScan(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char stringValue1[13312];
    memset(stringValue1, 'b', sizeof(stringValue1) - 1);
    stringValue1[13311] = '\0';
    writeBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, fixValue, stringValue1);
    ret = vertexLocalkeyMultiFieldScan(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启事务，排序索引，8个索引字段，每个索引字段设置左右值，写数据，进行范围扫描删除数据
TEST_F(localScanCompatibleV3, DML_078_041)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    char *Label_config = (char *)"{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";
    ret = spliceSchemaCreateTable(stmt, schemaSring, GMERR_OK, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[20];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[19] = '\0';
    writeMaxIndexSize(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, stringValue, GMC_OPERATION_INSERT);
    ret = vertexLimitManyLocalkeyScan(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLimitManyLocalkeyDelete(stmt, labelName, RECORDCOUNTSTART, RECORDCOUNTEND, GMC_COMPARE_RANGE_CLOSED,
        GMC_COMPARE_RANGE_CLOSED, GMC_ORDER_ASC, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 8个索引字段，通过GmcSetIndexKeyValue设置单值进行异步写和异步local更新、删除扫描数据
TEST_F(localScanCompatibleV3, DML_078_042)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    char *schemaSring = (char *)"\n%*s{ \"name\":\"local\", \"node\":\"schema_datatype\","
                                "\"fields\":[\"F9\",\"F3\",\"F5\",\"F7\",\"F12\",\"F11\",\"F16\",\"F1\"],"
                                "\"index\":{ \"type\":\"local\" }, \"constraints\": { \"unique\": true }}";
    ret = spliceSchemaCreateTable(stmt, schemaSring);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';

    writeMaxIndexSizeAsync(RECORDCOUNTSTART, RECORDCOUNTEND, stmtAsync, labelName, stringValue);
    char *localkey = (char *)"local";
    ret = vertexLocalkeyMultiFieldAsync(
        RECORDCOUNTSTART, RECORDCOUNTEND, stmtAsync, labelName, localkey, GMC_OPERATION_UPDATE, update_vertex_callback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalkeyMultiFieldAsync(
        RECORDCOUNTSTART, RECORDCOUNTEND, stmtAsync, labelName, localkey, GMC_OPERATION_DELETE, delete_vertex_callback);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
}
