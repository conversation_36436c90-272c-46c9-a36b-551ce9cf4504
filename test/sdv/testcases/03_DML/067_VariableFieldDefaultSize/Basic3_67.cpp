//门槛用例
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "DefaultSize.h"
#include <time.h>
#include <sys/time.h>
class DefaultSize_Basic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DefaultSize_Basic::SetUp()
{
    usleep(1000);
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    testCreateLabelMS(g_stmt);
    AW_CHECK_LOG_BEGIN();
}

void DefaultSize_Basic::TearDown()
{
    AW_CHECK_LOG_END();
    testDropLabelMS(g_stmt);
    usleep(1000);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 1. 	创建表时string/bytes属性设定最大size小于16KB，预期建表成功
// 9.15当前改为13KB
TEST_F(DefaultSize_Basic, DML_067_001_001)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSize_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
}
// 2. 	创建表时string/bytes属性最大size大于13KB，预期建表失败
//当前改为13KB
// 2022.7.22 最大限制改为64K
TEST_F(DefaultSize_Basic, DML_067_001_002)
{
    char *Full_SizePriError_schema = NULL;
    readJanssonFile("schema_file/Full_SizePriError.gmjson", &Full_SizePriError_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, Full_SizePriError_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_SizePriError_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(Full_SizePriError_schema);  // new
}
// 3. 	创建表时string/bytes不设置最大size，预期建表成功，合计插入2KB数据成功。
TEST_F(DefaultSize_Basic, DML_067_001_003)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSize_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[1024];
    char VarField2_value1k[1024];
    memset(VarField_value1k, 'c', sizeof(VarField_value1k) - 1);
    VarField_value1k[1023] = '\0';
    memset(VarField2_value1k, 'd', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[1023] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        value = 0 + i;
        ret = GmcSetVertexProperty(g_stmt, "PrimaryField", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField_value1k, sizeof(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 4. 	创建表设置string/bytes不设置最大size，合计插入数据变长字段值大于8KB，插入数据失败
// 9.15改为合计大于13KB。插入数据成功
TEST_F(DefaultSize_Basic, DML_067_001_004)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSize_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[7000];
    char VarField2_value1k[7000];
    memset(VarField_value1k, 'c', sizeof(VarField_value1k) - 1);
    VarField_value1k[6999] = '\0';
    memset(VarField2_value1k, 'd', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[6999] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        value = 0 + i;
        ret = GmcSetVertexProperty(g_stmt, "PrimaryField", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField_value1k, sizeof(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);  //
        // EXPECT_EQ(GMERR_INTERNAL_ERROR,ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 5. 	创建表设置不设置STRING默认值，插入数据变长字段值等于8KB，插入数据报错
// 9.15变长字段最大支持13K，存储取消了pagesize四分之一操作的限制,当前不报错
TEST_F(DefaultSize_Basic, DML_067_001_005)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, PriDs_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField2_value1k[8192];  //插入8K数据，但是实际会带有其他一些数据，适当降低(8192-25)刚好为8192,8192-26断连
    memset(VarField2_value1k, 'd', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[8191] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str8K", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 6. 	创建表设置不设置BYTES默认值，插入数据变长字段值等于8KB，插入数据报错
// 2022.7已经支持64K
TEST_F(DefaultSize_Basic, DML_067_001_006)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPriDb_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField2_value1k[13333];  //插入8K数据，但是实际会带有其他一些数据，适当降低(8192-25)刚好为8192,8192-26断连
    memset(VarField2_value1k, 'b', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[13332] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPriDb_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    bool isNull;
    char VarField[13333];
    uint32_t prop_size = 0;
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize2", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize2", &VarField, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", VarField);
    }
}
// 7. 	创建表设置string/bytes设置最大size为16KB，预期建表成功，合计插入2KB数据成功。
//改为最大size13KB
TEST_F(DefaultSize_Basic, DML_067_001_007)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri16b_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[1024];
    char VarField2_value1k[1024];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[1023] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[1023] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt16KB", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str16KB", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 8.    创建表设置string/bytes设置最大size为16KB，预期建表成功，合计插入10KB数据失败。
//改为最大size13KB，当前合计插入会成功
TEST_F(DefaultSize_Basic, DML_067_001_008)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri16b_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[5120];
    char VarField2_value1k[5120];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[5119] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[5119] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt16KB", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str16KB", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 9.    创建表设置string/bytes设置最大size为4KB，预期建表成功，合计插入2KB数据成功。
TEST_F(DefaultSize_Basic, DML_067_001_009)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[2048];
    char VarField2_value1k[2048];
    char VarField3_value1k[10];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[2047] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[2047] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[9] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str4K", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Eq_Byt4K", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 10.   创建表设置string/bytes设置最大size为256B，预期建表成功，设置值失败。
TEST_F(DefaultSize_Basic, DML_067_001_010)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[2048];
    char VarField2_value1k[2048];
    char VarField3_value1k[10];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[2047] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[2047] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[9] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
        ret = GmcExecute(g_stmt);  //没有插入进去等同于只有主键数据
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 11.   创建表设置string设置最大size为256B，且含有不设置的string字段，预期建表成功，各插入256B数据，预计成功。
TEST_F(DefaultSize_Basic, DML_067_001_011)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[256];
    char VarField2_value1k[256];
    char VarField3_value1k[256];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[255] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[255] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 12.   创建表设置bytes设置最大size为256B，且含有不设置的bytes字段，预期建表成功，各插入256B数据，预计成功。
TEST_F(DefaultSize_Basic, DML_067_001_012)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[256];
    char VarField2_value1k[256];
    char VarField3_value1k[256];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[255] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[255] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//无法插入超过8KB的数据，即不需要再做其他DML的测试，C20考虑维护8K粒度以上的用例(pagesize的1/4)
// 13.
// 创建表设置多个string字段size为默认值，128B,256B，4KB(改为13KB)分别插入对应字段大小的数据，对该字段属性值进行扫描读取，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_013)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 14.
// 创建表设置多个string字段size为默认值，128B,256B,4KB(改为13KB)分别插入对应字段大小的数据，对该字段属性值进行删除，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_014)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
    }
}
// 15.
// 创建表设置多个string字段size为默认值，128B,256B，4KB(13KB)分别插入对应字段大小的数据，对该字段属性值进行更新，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_015)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char UP_value1k[128];
    char UP2_value1k[256];
    char UP3_value1k[13312];
    memset(UP_value1k, 'x', sizeof(UP_value1k) - 1);
    UP_value1k[127] = '\0';
    memset(UP2_value1k, 'y', sizeof(UP2_value1k) - 1);
    UP2_value1k[255] = '\0';
    memset(UP3_value1k, 'z', sizeof(UP3_value1k) - 1);
    UP3_value1k[13311] = '\0';
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &UP3_value1k, strlen(UP3_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &UP2_value1k, strlen(UP2_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &UP_value1k, strlen(UP_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 16.
// 创建表设置多个string字段size为默认值，128B,256B，4KB(13KB)分别插入对应字段大小的数据，对该字段属性值进行替换，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_016)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t UP_value = 2;
    char UP_value1k[128];
    char UP2_value1k[256];
    char UP3_value1k[13312];
    memset(UP_value1k, 'x', sizeof(UP_value1k) - 1);
    UP_value1k[127] = '\0';
    memset(UP2_value1k, 'y', sizeof(UP2_value1k) - 1);
    UP2_value1k[255] = '\0';
    memset(UP3_value1k, 'z', sizeof(UP3_value1k) - 1);
    UP3_value1k[13311] = '\0';
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &UP_value, sizeof(UP_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &UP3_value1k, strlen(UP3_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &UP2_value1k, strlen(UP2_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &UP_value1k, strlen(UP_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 17.
// 创建表设置多个bytes字段size为默认值，128B,256B，4KB(13KB)分别插入对应字段大小的数据，对该字段属性值进行扫描读取，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_017)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Byt128B_value[128];
    char Byt256B_value[256];
    char Byt4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Byt4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Byt4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Byt256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Byt256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Byt128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 18.
// 创建表设置多个bytes字段size为默认值，128B,256B,4KB(13KB)分别插入对应字段大小的数据，对该字段属性值进行删除，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_018)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Byt128B_value[128];
    char Byt256B_value[256];
    char Byt4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Byt4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!\n", Byt4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Byt256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Byt128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt128B_value);
    }
}
// 19.
// 创建表设置多个bytes字段size为默认值，128B,256B，4KB(13KB)分别插入对应字段大小的数据，对该字段属性值进行更新，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_019)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char UP_value1k[128];
    char UP2_value1k[256];
    char UP3_value1k[13312];
    memset(UP_value1k, 'X', sizeof(UP_value1k) - 1);
    UP_value1k[127] = '\0';
    memset(UP2_value1k, 'Y', sizeof(UP2_value1k) - 1);
    UP2_value1k[255] = '\0';
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[13311] = '\0';

    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &UP3_value1k, strlen(UP3_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &UP2_value1k, strlen(UP2_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &UP_value1k, strlen(UP_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Byt128B_value[128];
    char Byt256B_value[256];
    char Byt4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Byt4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Byt4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Byt256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Byt256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Byt128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 20.
// 创建表设置多个bytes字段size为默认值，128B,256B，4KB分别插入对应字段大小的数据，对该字段属性值进行替换，预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_020)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t UP_value = 6;
    char UP_value1k[128];
    char UP2_value1k[256];
    char UP3_value1k[13312];
    memset(UP_value1k, 'X', sizeof(UP_value1k) - 1);
    UP_value1k[127] = '\0';
    memset(UP2_value1k, 'Y', sizeof(UP2_value1k) - 1);
    UP2_value1k[255] = '\0';
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[13311] = '\0';

    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &UP_value, sizeof(UP_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &UP3_value1k, strlen(UP3_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &UP2_value1k, strlen(UP2_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &UP_value1k, strlen(UP_value1k));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Byt128B_value[128];
    char Byt256B_value[256];
    char Byt4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Byt4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Byt4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Byt256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Byt256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Byt128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt128B_value);
        if (isEof == true) {
            break;
        }
    }
}

// 21.   创建表设置多个STRING字段size为默认值，128B,256B，4KB批量插入50个4KB对应字段大小的数据预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_021)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 50;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    uint32_t bactchLimitSize = 2048;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, bactchLimitSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';

    for (int i = start_num; i < end_num; i++) {
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取顶点
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 22.   创建表设置多个STRING字段size为默认值，128B,256B，4KB批量更新10个4KB对应字段大小的数据预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_022)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 10;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    uint32_t bactchLimitSize = 2048;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, bactchLimitSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';

    for (int i = start_num; i < end_num; i++) {
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // ret = GmcBatchPrepare(g_stmt);
    // ASSERT_EQ(GMERR_OK, ret);
    char UP_value1k[128];
    char UP2_value1k[256];
    char UP3_value1k[13312];
    memset(UP_value1k, 'X', sizeof(UP_value1k) - 1);
    UP_value1k[127] = '\0';
    memset(UP2_value1k, 'Y', sizeof(UP2_value1k) - 1);
    UP2_value1k[255] = '\0';
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[13311] = '\0';

    for (int i = start_num; i < end_num; i++) {
        value = i;
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[4095] = '\0';
        memset(UP3_value1k, 'a' + char(i), sizeof(UP3_value1k) - 1);
        UP3_value1k[4095] = '\0';
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &UP3_value1k, strlen(UP3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &UP2_value1k, strlen(UP2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &UP_value1k, strlen(UP_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    // 读取顶点
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 23.   创建表设置多个STRING字段size为默认值，批量删除100个4KB(改为13KB)对应字段大小的数据预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_023)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 70;  //插入數據超過batch緩衝區大小，接口即將重構，暫時適配改小
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    uint32_t bactchLimitSize = 2048;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, bactchLimitSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (uint32_t i = start_num; i < end_num; i++) {
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        // memset(VarField3_value1k,'0'+char(i),sizeof(VarField3_value1k) - 1);
        // VarField3_value1k[9999]='\0';
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    char DEL_value1k[13312];
    char DEL2_value1k[256];
    char DEL3_value1k[4096];
    memset(DEL2_value1k, 'e', sizeof(DEL2_value1k) - 1);
    DEL2_value1k[127] = '\0';
    memset(DEL3_value1k, 'f', sizeof(DEL3_value1k) - 1);
    DEL3_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[13311] = '\0';
    for (uint32_t i = start_num; i < end_num; i++) {
        value = i;
        // memset(DEL_value1k,'0'+char(i),sizeof(DEL_value1k) - 1);
        // DEL_value1k[13311]='\0';
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, FullSizeBytPriDs_PriKey_Name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    // 读取顶点
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_StrSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_StrSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Str128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Str128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 24.   创建表设置int型和多个BYTES型混合的表格，size为默认值，删除数据后全表读取，应该能读取到没删除的数据。
TEST_F(DefaultSize_Basic, DML_067_001_024)
{
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeByt2Pri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;

    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[4096];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[4095] = '\0';
    uint32_t VarField0 = 32;
    uint64_t Var2Field0 = 64;
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "int1", GMC_DATATYPE_UINT32, &VarField0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "int2", GMC_DATATYPE_UINT64, &Var2Field0, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //删除int32主键字段
    // ret=GmcPrepareStmtByLabelName(g_stmt, FullSizeByt2Pri_LName, GMC_OPERATION_DELETE);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &VarField0,sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyName(g_stmt, "primary_key2");
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcExecute(g_stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    // EXPECT_EQ(GMERR_OK, ret);

    //删除hashcluter字段
    // ret=GmcPrepareStmtByLabelName(g_stmt, FullSizeByt2Pri_LName, GMC_OPERATION_DELETE);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &Var2Field0,sizeof(uint64_t));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyName(g_stmt, "hc_key2");
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcExecute(g_stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    // EXPECT_EQ(GMERR_OK, ret);

    //删除bytes的localhash字段
    // ret=GmcPrepareStmtByLabelName(g_stmt, FullSizeByt2Pri_LName, GMC_OPERATION_DELETE);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, &VarField2_value1k,strlen(VarField2_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyName(g_stmt, "lh_key2");
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcExecute(g_stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    // EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size1;
    uint32_t prop_size2;
    uint32_t prop_size3;
    uint32_t prop_size4;
    uint32_t prop_size5;
    char Byt128B_value[128];
    char Byt256B_value[256];
    char Byt4k_value[4096];
    uint32_t scan_pri = 0;
    uint64_t scan_pri2 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeByt2Pri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //非全表扫描
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_BYTES, &VarField2_value1k,strlen(VarField2_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyName(g_stmt, "lh_key2");
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Byt4k_value, prop_size1, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!\n", Byt4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "int1", &prop_size2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "int1", &scan_pri, prop_size2, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%d!!!!!!!!!!\n", scan_pri);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "int2", &prop_size5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "int2", &scan_pri2, prop_size5, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%d!!!!!!!!!!\n", scan_pri2);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Byt256B_value, prop_size3, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Byt128B_value, prop_size4, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Byt128B_value);
    }
}
// 25.   创建表设置多个BYTES字段size为默认值，128B,256B，4KB(13KB)批量插入50个4KB对应字段大小的数据预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_025)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 50;

    // 插入顶点
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    uint32_t bactchLimitSize = 2048;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, bactchLimitSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';

    for (int i = start_num; i < end_num; i++) {
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    // 读取顶点
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}

// 26.   创建表设置多个STRING字段size为默认值，128B,256B，4KB批量更新10个4KB对应字段大小的数据预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_026)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 10;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    uint32_t bactchLimitSize = 2048;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, bactchLimitSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';

    for (int i = start_num; i < end_num; i++) {
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    char UP_value1k[128];
    char UP2_value1k[256];
    char UP3_value1k[13312];
    memset(UP_value1k, 'X', sizeof(UP_value1k) - 1);
    UP_value1k[127] = '\0';
    memset(UP2_value1k, 'Y', sizeof(UP2_value1k) - 1);
    UP2_value1k[255] = '\0';
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[4095] = '\0';

    for (int i = start_num; i < end_num; i++) {
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        memset(UP3_value1k, 'a' + char(i), sizeof(UP3_value1k) - 1);
        UP3_value1k[4095] = '\0';
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &UP3_value1k, strlen(UP3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &UP2_value1k, strlen(UP2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &UP_value1k, strlen(UP_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    // 读取顶点
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {

        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}

// 27.   创建表设置多个STRING字段size为默认值，批量删除10个4KB(改为13KB)对应字段大小的数据预期成功。
TEST_F(DefaultSize_Basic, DML_067_001_027)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 70;  //暫時適配改小batch緩衝區接受的數據量
    // 插入顶点
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    uint32_t bactchLimitSize = 2048;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, bactchLimitSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[13312];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    for (int i = start_num; i < end_num; i++) {
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        memset(VarField3_value1k, '0' + char(i), sizeof(VarField3_value1k) - 1);
        VarField3_value1k[13311] = '\0';
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    // 读取顶点
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str128B_value[128];
    char Str256B_value[256];
    char Str4k_value[13312];
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_STRING, &VarField3_value1k,strlen(VarField3_value1k));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Default_BytSize", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Default_BytSize", &Str4k_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s!!!!!!!!!!", Str4k_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt256B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt256B", &Str256B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s,", Str256B_value);
        ret = GmcGetVertexPropertySizeByName(g_stmt, "Eq_Byt128B", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt, "Eq_Byt128B", &Str128B_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", Str128B_value);
        if (isEof == true) {
            break;
        }
    }
}
// 28.   【其他索引交互，预期建表失败】hashcluster
// 2023.2.13 索引支持变长
TEST_F(DefaultSize_Basic, DML_067_001_028)
{
    char *HS_Error_schema = NULL;
    GmcDropVertexLabel(g_stmt, FullSizeBytPriDs_LName);
    readJanssonFile("schema_file/Hc_error.gmjson", &HS_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, HS_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, HS_Error_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(HS_Error_schema);  // new
}
// 29.   【其他索引交互，预期建表失败】hashcluster
TEST_F(DefaultSize_Basic, DML_067_001_029)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *LC_Error_schema = NULL;
    readJanssonFile("schema_file/Local_error.gmjson", &LC_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, LC_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, LC_Error_schema, V_config);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(LC_Error_schema);  // new
}
// 30.   【其他索引交互，预期建表失败】Lpmv4
TEST_F(DefaultSize_Basic, DML_067_001_030)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *Lpmv4_Error_schema = NULL;
    readJanssonFile("schema_file/Lpm_error.gmjson", &Lpmv4_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, Lpmv4_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, Lpmv4_Error_schema, V_config);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(Lpmv4_Error_schema);  // new
}
// 31.   【其他索引交互，预期建表失败】Lpmv6
TEST_F(DefaultSize_Basic, DML_067_001_031)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *Lpmv6_Error_schema = NULL;
    readJanssonFile("schema_file/Lpm6_error.gmjson", &Lpmv6_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, Lpmv6_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, Lpmv6_Error_schema, V_config);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(Lpmv6_Error_schema);  // new
}
// 32.   【autoincrement交互str,预期建表失败】
TEST_F(DefaultSize_Basic, DML_067_001_032)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *StrAuto_Error_schema = NULL;
    readJanssonFile("schema_file/Priauto_Error.gmjson", &StrAuto_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, StrAuto_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, StrAuto_Error_schema, V_config);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(StrAuto_Error_schema);  // new
}
// 33.   【autoincrement交互byt,预期建表失败】
TEST_F(DefaultSize_Basic, DML_067_001_033)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *BytAuto_Error_schema2 = NULL;
    readJanssonFile("schema_file/Priauto_Error2.gmjson", &BytAuto_Error_schema2);  //做了变更请关注
    ASSERT_NE((void *)NULL, BytAuto_Error_schema2);
    ret = GmcCreateVertexLabel(g_stmt, BytAuto_Error_schema2, V_config);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(BytAuto_Error_schema2);  // new
}
// 34.   【default交互str,预期建表失败】
TEST_F(DefaultSize_Basic, DML_067_001_034)
{
    char *StrDefault_Error_schema = NULL;
    readJanssonFile("schema_file/PriDefault.gmjson", &StrDefault_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, StrDefault_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, StrDefault_Error_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(StrDefault_Error_schema);  // new
}
// 34.   【default交互Byt,预期建表失败】
TEST_F(DefaultSize_Basic, DML_067_001_035)
{
    char *BytDefault_Error_schema = NULL;
    readJanssonFile("schema_file/PriDefault2.gmjson", &BytDefault_Error_schema);  //做了变更请关注
    ASSERT_NE((void *)NULL, BytDefault_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, BytDefault_Error_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *MS_Vlabel = NULL;
    free(BytDefault_Error_schema);  // new
}
// 35.   创建一层的tree表，第一层string/bytes含有默认值和其他有size的值，预期建表成功
TEST_F(DefaultSize_Basic, DML_067_001_036)
{
    char *Full_Tree_schema = NULL;
    readJanssonFile("schema_file/Tree.gmjson", &Full_Tree_schema);
    ASSERT_NE((void *)NULL, Full_Tree_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName1 = "Full_Tree1";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, TreeName1);
    ASSERT_EQ(GMERR_OK, ret);
    free(Full_Tree_schema);  // new
}
// 37.   创建三层的tree表，第一、二、三层string/bytes含有默认值和其他有size的值，预期建表成功
TEST_F(DefaultSize_Basic, DML_067_001_037)
{
    char *Full_Tree2_schema = NULL;
    readJanssonFile("schema_file/Tree2.gmjson", &Full_Tree2_schema);
    ASSERT_NE((void *)NULL, Full_Tree2_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree2_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName2 = "Full_Tree2";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, TreeName2);
    ASSERT_EQ(GMERR_OK, ret);
    free(Full_Tree2_schema);  // new
}
// 38.   创建三层的tree表，第二层string默认值超过16K，预期建表失败
//改为超过13K
TEST_F(DefaultSize_Basic, DML_067_001_038)
{
    char *Full_Tree2_Error_schema = NULL;
    readJanssonFile("schema_file/Tree2_Error.gmjson", &Full_Tree2_Error_schema);
    ASSERT_NE((void *)NULL, Full_Tree2_Error_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree2_Error_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName3 = "Full_Tree2_Error";
    //获取顶点label

    free(Full_Tree2_Error_schema);  // new
}
// 39.   创建三层的tree表，第二层Bytes默认值超过16K，预期建表失败
//改为超过13K
TEST_F(DefaultSize_Basic, DML_067_001_039)
{
    char *Full_Tree2_Error2_schema = NULL;
    readJanssonFile("schema_file/Tree2_Error2.gmjson", &Full_Tree2_Error2_schema);
    ASSERT_NE((void *)NULL, Full_Tree2_Error2_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree2_Error2_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName3 = "Full_Tree2_Error2";
    //获取顶点label
    free(Full_Tree2_Error2_schema);  // new
}
// 40.
// 创建三层的tree表，第一、二、三层string/bytes含有默认值和其他有size的值，对第二、三层默认值的string进行插入，更新，扫描操作
TEST_F(DefaultSize_Basic, DML_067_001_040)
{
    char *Full_Tree2_schema = NULL;
    readJanssonFile("schema_file/Tree2.gmjson", &Full_Tree2_schema);
    ASSERT_NE((void *)NULL, Full_Tree2_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree2_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName2 = "Full_Tree2";
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[4096];
    char VarField4_value1k[256];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[4095] = '\0';
    memset(VarField4_value1k, 'q', sizeof(VarField4_value1k) - 1);
    VarField4_value1k[255] = '\0';
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    //第一层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(root, "P1", GMC_DATATYPE_UINT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root, "Default_BytSize0", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root, "Default_StrSize0", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(root, "Eq_Str4K0", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(root, "Eq_Byt4K0", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    //第二层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(
        T1, "Default_BytSize1", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        T1, "Default_StrSize1", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Eq_Str4K1", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Eq_Byt4K1", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    //第三层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(
        T2, "Default_BytSize2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        T2, "Default_StrSize2", GMC_DATATYPE_STRING, &VarField4_value1k, strlen(VarField4_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Eq_Str4K2", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Eq_Byt4K2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char UP3_value1k[4096];
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[4095] = '\0';

    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "Tree_key1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Default_StrSize1", GMC_DATATYPE_STRING, &UP3_value1k, strlen(UP3_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str4k_value[4096];
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcNodeGetPropertySizeByName(T1, "Default_StrSize1", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(T1, "Default_StrSize1", &Str4k_value, prop_size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("after update:%s!!!!!!!!!!\n", Str4k_value);
    }

    char RP3_value1k[1024];
    memset(RP3_value1k, '4', sizeof(RP3_value1k) - 1);
    RP3_value1k[1023] = '\0';
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "Tree_key1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Default_StrSize2", GMC_DATATYPE_STRING, &RP3_value1k, strlen(RP3_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    isEof = false;
    uint32_t prop_size2;
    char Str4k_value2[4096];
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcNodeGetPropertySizeByName(T2, "Default_StrSize2", &prop_size2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(T2, "Default_StrSize2", &Str4k_value2, prop_size2, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("after UPDATE:%s!!!!!!!!!!\n", Str4k_value2);
    }

    ret = GmcDropVertexLabel(g_stmt, TreeName2);
    ASSERT_EQ(GMERR_OK, ret);
    free(Full_Tree2_schema);  // new
}
// 41.
// 创建三层的tree表，第一、二、三层string/bytes含有默认值和其他有size的值，对第二、三层默认值的Bytes进行插入，更新，扫描操作
TEST_F(DefaultSize_Basic, DML_067_001_041)
{
    char *Full_Tree2_schema = NULL;
    readJanssonFile("schema_file/Tree2.gmjson", &Full_Tree2_schema);
    ASSERT_NE((void *)NULL, Full_Tree2_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree2_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName2 = "Full_Tree2";
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[4096];
    char VarField4_value1k[256];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[4095] = '\0';
    memset(VarField4_value1k, 'q', sizeof(VarField4_value1k) - 1);
    VarField4_value1k[255] = '\0';
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    //第一层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(root, "P1", GMC_DATATYPE_UINT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root, "Default_BytSize0", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root, "Default_StrSize0", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(root, "Eq_Str4K0", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(root, "Eq_Byt4K0", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    //第二层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(
        T1, "Default_BytSize1", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        T1, "Default_StrSize1", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Eq_Str4K1", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Eq_Byt4K1", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    //第三层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(
        T2, "Default_BytSize2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        T2, "Default_StrSize2", GMC_DATATYPE_STRING, &VarField4_value1k, strlen(VarField4_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Eq_Str4K2", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Eq_Byt4K2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char UP3_value1k[4096];
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[4095] = '\0';
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "Tree_key1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Default_BytSize1", GMC_DATATYPE_BYTES, &UP3_value1k, strlen(UP3_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str4k_value[4096];
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcNodeGetPropertySizeByName(T1, "Default_BytSize1", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(T1, "Default_BytSize1", &Str4k_value, prop_size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("after update:%s!!!!!!!!!!\n", Str4k_value);
    }

    char RP3_value1k[1024];
    memset(RP3_value1k, '4', sizeof(RP3_value1k) - 1);
    RP3_value1k[1023] = '\0';
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "Tree_key1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Default_StrSize2", GMC_DATATYPE_STRING, &RP3_value1k, strlen(RP3_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    isEof = false;
    uint32_t prop_size2;
    char Str4k_value2[4096];
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcNodeGetPropertySizeByName(T2, "Default_StrSize2", &prop_size2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(T2, "Default_StrSize2", &Str4k_value2, prop_size2, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("after UPDATE:%s!!!!!!!!!!\n", Str4k_value2);
    }

    ret = GmcDropVertexLabel(g_stmt, TreeName2);
    ASSERT_EQ(GMERR_OK, ret);
    free(Full_Tree2_schema);  // new
}
// 42.   Edge表，string为默认值，插入点为合计为13KB，自动建边，插入删除成功
TEST_F(DefaultSize_Basic, DML_067_001_042)
{
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[6656];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[6655] = '\0';
    //打开Vertex Label src 写数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    printf("===================After Delete Src===========================\n");

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_ms_src_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    printf("===================After Delete Dst===========================\n");

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_ms_dst_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
// 43.   创建Edge表，string为默认值，两端插入的点合计为16KB，自动建边插入失败
TEST_F(DefaultSize_Basic, DML_067_001_043)
{
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[8166];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[8165] = '\0';
    //打开Vertex Label src 写数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_INTERNAL_ERROR,ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_INTERNAL_ERROR,ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    printf("===================After Delete Src===========================\n");

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_ms_src_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // testGmcGetStmtAttr(g_stmt, 0);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    printf("===================After Delete Dst===========================\n");

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_ms_dst_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // testGmcGetStmtAttr(g_stmt, 0);
}
// 44.   创建Edge表，string为默认值，插入点为合计为13KB，手动建边，插入删除成功
TEST_F(DefaultSize_Basic, DML_067_001_044)
{
    ret = GmcDropVertexLabel(g_stmt, PriKey_SrcLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, PriKey_DstLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }

    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0,\"defragmentation\":false}";

    readJanssonFile("schema_file/Src.gmjson", &PriKey_SrcLabel_schema);
    ASSERT_NE((void *)NULL, PriKey_SrcLabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, PriKey_SrcLabel_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Dst.gmjson", &PriKey_DstLabel_schema);
    ASSERT_NE((void *)NULL, PriKey_DstLabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, PriKey_DstLabel_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Edge.gmjson", &PriKey_ELabel_schema);
    ASSERT_NE((void *)NULL, PriKey_ELabel_schema);
    ret = GmcCreateEdgeLabel(g_stmt, PriKey_ELabel_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(PriKey_ELabel_schema);
    free(PriKey_SrcLabel_schema);
    free(PriKey_DstLabel_schema);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[6656];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[6655] = '\0';
    //打开Vertex Label src 写数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(0, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(0, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    void *edgelabe = NULL;
    ret = GmcOpenEdgeLabelByName(g_stmt, "edge_ms", &edgelabe);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(g_stmt, "vertex_ms_src_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(g_stmt, "vertex_ms_dst_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    //删除edge
    ret = GmcDeleteEdgeByIndexKey(g_stmt, edgelabe);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(g_stmt, edgelabe);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(g_stmt, "vertex_ms_src");
    EXPECT_EQ(GMERR_OK, ret);
}
//
// 45.
// 创建三层的tree表，第一、二、三层string/bytes含有默认值和其他有size的值，对第三层默认值的Bytes进行插入，更新数据4K，预期失败，Tree模型第三层更新实际上会对整棵树进行操作
TEST_F(DefaultSize_Basic, DML_067_001_045)
{
    char *Full_Tree2_schema = NULL;
    readJanssonFile("schema_file/Tree2.gmjson", &Full_Tree2_schema);
    ASSERT_NE((void *)NULL, Full_Tree2_schema);
    ret = GmcCreateVertexLabel(g_stmt, Full_Tree2_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *TreeName2 = "Full_Tree2";
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[4096];
    char VarField4_value1k[256];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[4095] = '\0';
    memset(VarField4_value1k, 'q', sizeof(VarField4_value1k) - 1);
    VarField4_value1k[255] = '\0';
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    //第一层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(root, "P1", GMC_DATATYPE_UINT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root, "Default_BytSize0", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root, "Default_StrSize0", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(root, "Eq_Str4K0", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(root, "Eq_Byt4K0", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    //第二层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(
        T1, "Default_BytSize1", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        T1, "Default_StrSize1", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Eq_Str4K1", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Eq_Byt4K1", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    //第三层插入合计1K左右数据
    ret = GmcNodeSetPropertyByName(
        T2, "Default_BytSize2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        T2, "Default_StrSize2", GMC_DATATYPE_STRING, &VarField4_value1k, strlen(VarField4_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Eq_Str4K2", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Eq_Byt4K2", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char UP3_value1k[4096];
    memset(UP3_value1k, 'Z', sizeof(UP3_value1k) - 1);
    UP3_value1k[4095] = '\0';

    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "Tree_key1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "Default_BytSize1", GMC_DATATYPE_BYTES, &UP3_value1k, strlen(UP3_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    bool isEof = false;
    bool isNull;
    uint32_t prop_size;
    char Str4k_value[4096];
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isEof) {
        ret = GmcFetch(g_stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcNodeGetPropertySizeByName(T1, "Default_BytSize1", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(T1, "Default_BytSize1", &Str4k_value, prop_size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("after update:%s!!!!!!!!!!\n", Str4k_value);
    }

    char RP3_value1k[4096];
    memset(RP3_value1k, '4', sizeof(RP3_value1k) - 1);
    RP3_value1k[4095] = '\0';
    ret = testGmcPrepareStmtByLabelName(g_stmt, TreeName2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "F2", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "A3", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "Tree_key1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "Default_StrSize2", GMC_DATATYPE_STRING, &RP3_value1k, strlen(RP3_value1k));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_INTERNAL_ERROR,ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, TreeName2);
    ASSERT_EQ(GMERR_OK, ret);
    free(Full_Tree2_schema);  // new
}
// 46.   Edge表，bytes为默认值，插入点为合计为8KB，自动建边，插入删除成功
//改为合计13KB
TEST_F(DefaultSize_Basic, DML_067_001_046)
{
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[6656];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[6655] = '\0';
    //打开Vertex Label src 写数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src2", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    printf("===================After Delete Src===========================\n");

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_ms_src_key2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst2", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    printf("===================After Delete Dst===========================\n");

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "vertex_ms_dst_key2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
// 47.   创建Edge表，bytes为默认值，两端插入的点合计为16KB，自动建边插入失败
//合计插入大于13KB
TEST_F(DefaultSize_Basic, DML_067_001_047)
{
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[8167];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[8166] = '\0';
    //打开Vertex Label src 写数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_INTERNAL_ERROR,ret);
        ret = testGmcGetLastError(NULL);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_INTERNAL_ERROR,ret);
        ret = testGmcGetLastError(NULL);
    }
}
// 48.   创建Edge表，bytes为默认值，插入点为合计为8KB，手动建边，插入删除成功
//合计插入为13KB
TEST_F(DefaultSize_Basic, DML_067_001_048)
{
    ret = GmcDropVertexLabel(g_stmt, PriKey_SrcLabel2_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, PriKey_DstLabel2_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    char *PriKey_ELabel2_schema = NULL;
    char *PriKey_SrcLabel2_schema = NULL;
    char *PriKey_DstLabel2_schema = NULL;
    char vertexLabel_config[] = "{\"max_record_num\":100000000, \"isFastReadUncommitted\":0,\"defragmentation\":false}";

    //注意先创建点再创建边
    readJanssonFile("schema_file/Src2.gmjson", &PriKey_SrcLabel2_schema);
    ASSERT_NE((void *)NULL, PriKey_SrcLabel2_schema);
    ret = GmcCreateVertexLabel(g_stmt, PriKey_SrcLabel2_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Dst2.gmjson", &PriKey_DstLabel2_schema);
    ASSERT_NE((void *)NULL, PriKey_DstLabel2_schema);
    ret = GmcCreateVertexLabel(g_stmt, PriKey_DstLabel2_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Edge2.gmjson", &PriKey_ELabel2_schema);
    ASSERT_NE((void *)NULL, PriKey_ELabel2_schema);
    ret = GmcCreateEdgeLabel(g_stmt, PriKey_ELabel2_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(PriKey_ELabel2_schema);
    free(PriKey_SrcLabel2_schema);
    free(PriKey_DstLabel2_schema);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[128];
    char VarField2_value1k[256];
    char VarField3_value1k[6656];
    int affectRows = 0;
    int expectAffectRows;
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[127] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[255] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[6655] = '\0';
    //打开Vertex Label src 写数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_src2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(0, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_ms_dst2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(0, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void *edgelabe = NULL;
    ret = GmcOpenEdgeLabelByName(g_stmt, "edge_ms2", &edgelabe);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(g_stmt, "vertex_ms_src_key2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(g_stmt, "vertex_ms_dst_key2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropGraphLabel(g_stmt, "vertex_ms_src2");
    EXPECT_EQ(GMERR_OK, ret);
}
// 49.  	创建表设置多个string字段size为默认值，128B,256B分别插入大于对应字段大小的数据，预期失敗。
TEST_F(DefaultSize_Basic, DML_067_001_049)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeStrPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[500];
    char VarField2_value1k[500];
    char VarField3_value1k[5000];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[499] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[499] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[4999] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_StrSize", GMC_DATATYPE_STRING, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str256B", GMC_DATATYPE_STRING, &VarField2_value1k, strlen(VarField2_value1k));
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Str128B", GMC_DATATYPE_STRING, &VarField_value1k, strlen(VarField_value1k));
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 50.  	创建表设置多个string字段size为默认值，128B,256B分别插入大于对应字段大小的数据，预期失敗。
TEST_F(DefaultSize_Basic, DML_067_001_050)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    void *MS_Vlabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, FullSizeBytPri_LName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t oper_nums = 1;
    uint32_t value = 0;
    char VarField_value1k[500];
    char VarField2_value1k[500];
    char VarField3_value1k[5000];
    memset(VarField_value1k, 'e', sizeof(VarField_value1k) - 1);
    VarField_value1k[499] = '\0';
    memset(VarField2_value1k, 'f', sizeof(VarField2_value1k) - 1);
    VarField2_value1k[499] = '\0';
    memset(VarField3_value1k, 'g', sizeof(VarField3_value1k) - 1);
    VarField3_value1k[4999] = '\0';
    for (int i = oper_nums; i > 0; i--) {
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Default_BytSize", GMC_DATATYPE_BYTES, &VarField3_value1k, strlen(VarField3_value1k));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "Eq_Byt256B", GMC_DATATYPE_BYTES, &VarField2_value1k, strlen(VarField2_value1k));
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(g_stmt, "Eq_Byt128B", GMC_DATATYPE_BYTES, &VarField_value1k, strlen(VarField_value1k));
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

