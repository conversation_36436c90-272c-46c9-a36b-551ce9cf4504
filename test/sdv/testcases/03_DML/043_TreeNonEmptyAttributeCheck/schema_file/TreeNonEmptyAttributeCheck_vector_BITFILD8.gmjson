[{"type": "record", "name": "TreeNonEmptyAttributeCheck_vector_BITFILD8", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "char", "nullable": true}, {"name": "F2", "type": "uchar", "nullable": true}, {"name": "F3", "type": "int8", "nullable": true}, {"name": "F4", "type": "uint8", "nullable": true}, {"name": "F5", "type": "int16", "nullable": true}, {"name": "F6", "type": "uint16", "nullable": true}, {"name": "F7", "type": "int32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "int64", "nullable": true}, {"name": "F10", "type": "uint64", "nullable": true}, {"name": "F11", "type": "boolean", "nullable": true}, {"name": "F12", "type": "float", "nullable": true}, {"name": "F13", "type": "double", "nullable": true}, {"name": "F15", "type": "time", "nullable": true}, {"name": "F16", "type": "uint8: 4", "nullable": true}, {"name": "F17", "type": "uint16: 15", "nullable": true}, {"name": "F18", "type": "uint32: 31", "nullable": true}, {"name": "F19", "type": "uint64: 59", "nullable": true}, {"name": "F21", "type": "string", "size": 100, "nullable": true}, {"name": "F22", "type": "bytes", "size": 7, "nullable": true}, {"name": "F23", "type": "fixed", "size": 7, "nullable": true}, {"name": "F24", "type": "bitmap", "size": 128, "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}, {"name": "P1", "type": "char", "nullable": true}, {"name": "P2", "type": "uchar", "nullable": true}, {"name": "P3", "type": "int8", "nullable": true}, {"name": "P4", "type": "uint8", "nullable": true}, {"name": "P5", "type": "int16", "nullable": true}, {"name": "P6", "type": "uint16", "nullable": true}, {"name": "P7", "type": "int32", "nullable": true}, {"name": "P8", "type": "uint32", "nullable": true}, {"name": "P9", "type": "int64", "nullable": true}, {"name": "P10", "type": "uint64", "nullable": true}, {"name": "P11", "type": "boolean", "nullable": true}, {"name": "P12", "type": "float", "nullable": true}, {"name": "P13", "type": "double", "nullable": true}, {"name": "P15", "type": "time", "nullable": true}, {"name": "P16", "type": "uint8: 4", "nullable": true}, {"name": "P17", "type": "uint16: 15", "nullable": true}, {"name": "P18", "type": "uint32: 31", "nullable": true}, {"name": "P19", "type": "uint64: 59", "nullable": true}, {"name": "P21", "type": "string", "size": 100, "nullable": true}, {"name": "P22", "type": "bytes", "size": 7, "nullable": true}, {"name": "P23", "type": "fixed", "size": 7, "nullable": true}, {"name": "P24", "type": "bitmap", "size": 128, "nullable": true}, {"name": "T2", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "A0", "type": "int64", "nullable": true}, {"name": "A1", "type": "char", "nullable": true}, {"name": "A2", "type": "uchar", "nullable": true}, {"name": "A3", "type": "int8", "nullable": true}, {"name": "A4", "type": "uint8", "nullable": true}, {"name": "A5", "type": "int16", "nullable": true}, {"name": "A6", "type": "uint16", "nullable": true}, {"name": "A7", "type": "int32", "nullable": true}, {"name": "A8", "type": "uint32", "nullable": true}, {"name": "A9", "type": "int64", "nullable": true}, {"name": "A10", "type": "uint64", "nullable": true}, {"name": "A11", "type": "boolean", "nullable": true}, {"name": "A12", "type": "float", "nullable": true}, {"name": "A13", "type": "double", "nullable": true}, {"name": "A15", "type": "time", "nullable": true}, {"name": "A16", "type": "uint8: 4", "nullable": true}, {"name": "A17", "type": "uint16: 15", "nullable": true}, {"name": "A18", "type": "uint32: 31", "nullable": true}, {"name": "A19", "type": "uint64: 59", "nullable": true}, {"name": "A21", "type": "string", "size": 100, "nullable": true}, {"name": "A22", "type": "bytes", "size": 7, "nullable": true}, {"name": "A23", "type": "fixed", "size": 7, "nullable": true}, {"name": "A24", "type": "bitmap", "size": 128, "nullable": true}], "super_fields": [{"name": "superfiled1", "comment": "test", "fields": ["A0", "A1", "A2", "A3", "A4"]}]}], "super_fields": [{"name": "superfiled0", "comment": "test", "fields": ["P0", "P1", "P2", "P3", "P4"]}]}, {"name": "T3", "type": "record", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "char", "nullable": true}, {"name": "V2", "type": "uchar", "nullable": true}, {"name": "V3", "type": "int8", "nullable": true}, {"name": "V4", "type": "uint8", "nullable": true}, {"name": "V5", "type": "int16", "nullable": true}, {"name": "V6", "type": "uint16", "nullable": true}, {"name": "V7", "type": "int32", "nullable": true}, {"name": "V8", "type": "uint32", "nullable": true}, {"name": "V9", "type": "int64", "nullable": true}, {"name": "V10", "type": "uint64", "nullable": true}, {"name": "V11", "type": "boolean", "nullable": true}, {"name": "V12", "type": "float", "nullable": true}, {"name": "V13", "type": "double", "nullable": true}, {"name": "V15", "type": "time", "nullable": true}, {"name": "V16", "type": "uint8: 4", "nullable": false}, {"name": "V17", "type": "uint16: 15", "nullable": true}, {"name": "V18", "type": "uint32: 31", "nullable": true}, {"name": "V19", "type": "uint64: 59", "nullable": true}, {"name": "V21", "type": "string", "size": 100, "nullable": true}, {"name": "V22", "type": "bytes", "size": 7, "nullable": true}, {"name": "V23", "type": "fixed", "size": 7, "nullable": true}, {"name": "V24", "type": "bitmap", "size": 128, "nullable": true}], "super_fields": [{"name": "superfiled2", "comment": "test", "fields": ["V0", "V1", "V2", "V3", "V4"]}]}], "keys": [{"node": "TreeNonEmptyAttributeCheck_vector_BITFILD8", "name": "TreeNonEmptyAttributeCheck_vector_BITFILD8_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "TreeNonEmptyAttributeCheck_vector_BITFILD8", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0"], "constraints": {"unique": true}}]}]