/*****************************************************************************
 Description  : 数组嵌套特性与其它特性交互测试
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/04/28
*****************************************************************************/
#include "ArrayNestTest.h"
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

/********************订阅相关*********************/
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;
char *g_sub_info = NULL;
int g_data_num = 10;
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

class ArrayNestInteractTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void ArrayNestInteractTest::SetUpTestCase()
{
    printf("[INFO] ArrayNestInteractTest Start.\n");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void ArrayNestInteractTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] ArrayNestInteractTest End.\n");
}

void ArrayNestInteractTest::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, labelName1);
    readJanssonFile("schemaFile/NormalTreeModel.gmjson", &labelJson1);
    ASSERT_NE((void *)NULL, labelJson1);
    AW_CHECK_LOG_BEGIN();
}

void ArrayNestInteractTest::TearDown()
{
    AW_CHECK_LOG_END();
    free(labelJson1);
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 001. array嵌套array, vector嵌套vector,调用DumpVertexToJson成功转json
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_001)
{
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/noBitMapTreeModel.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    start_num = 0;
    end_num = 10;
    int index = 1;
    bool bool_value = 0;
    bool BitMap = false;
    bool sp = false;
    char *f14_value = (char *)"string";
    void *label = NULL;

    // insert vertex
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value, BitMap);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value, sp, BitMap);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value, sp, BitMap);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value, sp, BitMap);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value, sp, BitMap);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value, sp, BitMap);
            }
        }

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // DumpVertexToJson
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        char *outJson = NULL;
        ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &outJson);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeJsonStr(stmt, outJson);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 002. acl.gmjson表调用GmcDumpVertexToJson成功转json
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_002)
{
    int ret = 0;
    char *labelJson = NULL;
    const char *labelName = "access_list";
    const char *PKName = "access_control_list_key";
    const char *mkName1 = "nftable_chain_key";
    const char *mkName2 = "nftable_rule_key";
    // create vertexLabel
    readJanssonFile("schemaFile/acl.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // insert vertex
    int index = 1;
    int i = 0;
    start_num = 0;
    end_num = 1;
    array_num = 3;
    TestAclInsertVertex(stmt, index, start_num, end_num, array_num, labelName);
    // read vertex
    TestAclDirectFetchVertex(stmt, i, index, array_num, labelName, PKName);

    // dump vertex to json
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char nftable_table_name[SIZE] = {0};
    snprintf(nftable_table_name, SIZE, "nftable_table_name%d", i);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, &nftable_table_name, strlen(nftable_table_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);

    char *outJson = NULL;
    ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &outJson);
    EXPECT_EQ(GMERR_OK, ret);
    if (outJson != NULL) {
        printf("%s\n", outJson);
    }
    GmcFreeJsonStr(stmt, outJson);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 003. dhcpd_config.gmjson表调用GmcDumpVertexToJson成功转json
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_003)
{
    int ret = 0;
    char *labelJson = NULL;
    const char *labelName = "dhcpd_config";
    const char *PKName = "dhcpd_config_pk";
    const char *mkName1 = "dhcpd_config_host_key";
    const char *mkName2 = "dhcpd_config_subnet_key";
    // create vertexLabel
    readJanssonFile("schemaFile/dhcpd_config.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // insert vertex
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(stmt, "schemaFile/data/dhcpd_config.gmdata");

    // dump vertex to json
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 0;
    char if_name[] = "str0";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, &if_name, strlen(if_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    char *outJson = NULL;
    ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &outJson);
    EXPECT_EQ(GMERR_OK, ret);
    if (outJson != NULL) {
        printf("%s\n", outJson);
    }
    GmcFreeJsonStr(stmt, outJson);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description :004. array嵌套array, vector嵌套vector,调用GmcSetVertexByJson插入成功
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_004)
{

    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    void *label = NULL;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";

    // insert vertex
    int count = 10;
    TestInsertVertex(stmt, index, bool_value, f14_value, start_num, count, array_num, vector_num, labelName1);

    // gmexport
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -s %s", g_toolPath, labelName1,
        g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    // truncate
    ret = GmcTruncateVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);

    // insertvertexbyjson
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(stmt, "./OP_T0.vertexdata");

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 005. acl.gmjson表调用GmcSetVertexByJson插入成功
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_005)
{
    int ret = 0;
    char *labelJson = NULL;
    const char *labelName = "access_list";
    // create vertexLabel
    readJanssonFile("schemaFile/acl.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // insert vertex by json
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(stmt, "schemaFile/data/acl.gmdata");

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 006. dhcpd_config.gmjson表调用GmcSetVertexByJson插入成功
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_006)
{
    int ret = 0;
    char *labelJson = NULL;
    const char *PKName = "dhcpd_config_pk";
    const char *labelName = "dhcpd_config";
    // create vertexLabel
    readJanssonFile("schemaFile/dhcpd_config.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // insert vertex by json
    void *label = NULL;
    int i = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(stmt, "schemaFile/data/dhcpd_config.gmdata");

    // dump vertex to json
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char if_name[] = "str0";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, &if_name, strlen(if_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);

    char *outJson = NULL;
    ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &outJson);
    EXPECT_EQ(GMERR_OK, ret);
    if (outJson != NULL) {
        printf("%s\n", outJson);
    }
    GmcFreeJsonStr(stmt, outJson);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description :007. array嵌套array, vector嵌套vector,导入导出数据
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_007)
{
    int ret = 0;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/noBitMapTreeModel.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    // gmimport
    char const *g_dataPath = "schemaFile/data/OP_T0.vertexdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s -ns %s", g_toolPath, g_dataPath,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10");
    EXPECT_EQ(GMERR_OK, ret);

    // gmexport
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -s %s -ns %s", g_toolPath, labelName1,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    system("rm OP_T0.vertexdata -f");

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 008. acl.gmjson表,导入导出数据
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_008)
{
    int ret = 0;
    char *labelJson = NULL;
    const char *labelName = "access_list";
    // create vertexLabel
    readJanssonFile("schemaFile/acl.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // gmimport
    char const *g_dataPath = "schemaFile/data/acl.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s -ns %s", g_toolPath,
        g_dataPath, labelName, g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 3, successNum: 3");
    EXPECT_EQ(GMERR_OK, ret);

    // gmexport
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    system("rm access_list.vertexdata -f");

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 009. dhcpd_config.gmjson表,导入导出数据
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_009)
{
    int ret = 0;
    char *labelJson = NULL;
    const char *labelName = "dhcpd_config";
    // create vertexLabel
    readJanssonFile("schemaFile/dhcpd_config.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // gmimport
    char const *g_dataPath = "schemaFile/data/dhcpd_config.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s -ns %s", g_toolPath,
        g_dataPath, labelName, g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 1, successNum: 1");
    EXPECT_EQ(GMERR_OK, ret);

    // gmexport
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s  -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    system("rm dhcpd_config.vertexdata -f");

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 010. array嵌套array, vector嵌套vector开启事务插入vertex,rollback
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_010)
{
    int ret = 0;
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcCreateVertexLabel(stmt, labelJson1, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    start_num = 0;
    end_num = 100;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    // insert vertex
    TestInsertVertex(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // delete vertex
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t pk = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert and rollback
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    TestInsertVertex(stmt, index, bool_value, f14_value, start_num, 1, array_num, vector_num, labelName1);
    ret = GmcTransRollBack(conn);

    // query vertex
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, PKName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = true;
    ret = GmcFetch(stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(eof, true);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 011. array嵌套array, vector嵌套vector订阅推送（dml）
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_011)
{
    int ret = 0;
    int userDataIdx = 0;
    g_sub_info = NULL;
    g_data_num = 100;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // subscrib vertexLabel
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/NormalSubinfo.gmjson", &g_sub_info);
    ASSERT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    start_num = 0;
    end_num = g_data_num;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // replace vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // delete vertex
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 012. array嵌套array, vector嵌套vector批量插入vertex（dml）
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_012)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    start_num = 0;
    end_num = 100;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";

    // insert vertex
    TestInsertVertexBatch(
        conn, stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertex(stmt, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    // update vertex
    index = 100;
    f14_value = (char *)"newstr";
    TestGmcReplaceVertexBatch(
        conn, stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1, PKName1);

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertex(stmt, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    // delete vertex
    TestGmcDeleteVertexByIndexKeyBatch(
        conn, stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1, PKName1);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 013. array嵌套array, vector嵌套vector,通过superfiled插入顶点，查询顶点
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_013)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    start_num = 0;
    end_num = 100;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";

    // insert vertex
    TestInsertVertexBySP(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertexBySP(
            stmt, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 014. array嵌套array, vector嵌套vector,第2层array/vector排序
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_014)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    start_num = 0;
    end_num = 100;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *treenode = NULL;

    // insert vertex
    TestInsertVertexBySP(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    //排序
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;

    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        //第二层array 正反序都不影响通过mk读record
        GmcOrderDirectionE increase = GMC_ORDER_ASC;
        ret = GmcNodeGetChild(T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(T4, (char *)"A7", increase);
        EXPECT_EQ(GMERR_OK, ret);

        //第二层vector
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSortElement(T5, (char *)"V7", increase);
        EXPECT_EQ(GMERR_OK, ret);

        // check
        TestGmcGetNodePropertyByName_R(root, pk * index, bool_value, f14_value);
        TestGmcGetNodePropertyByName_p(T1, pk * index, bool_value, f14_value);
        // read by mk
        GmcIndexKeyT *T2_mk;
        ret = GmcNodeAllocKey(T2, "mk1", &T2_mk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T2, "T4", &T4);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T4_mk;
        ret = GmcNodeAllocKey(T4, "mk2", &T4_mk);
        EXPECT_EQ(GMERR_OK, ret);
        // 读array节点
        for (uint32_t j = 0; j < array_num; j++) {
            uint64_t mk1 = 7 * j * index;
            ret = GmcNodeSetKeyValue(T2_mk, 0, GMC_DATATYPE_UINT64, &mk1, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T2, T2_mk, &T2);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            for (uint32_t m = 0; m < array_num; m++) {
                uint64_t mk2 = 7 * m * index;
                ret = GmcNodeSetKeyValue(T4_mk, 0, GMC_DATATYPE_UINT64, &mk2, sizeof(uint64_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetElementByKey(T4, T4_mk, &T4);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
            }
        }
        GmcNodeFreeKey(T2_mk);
        GmcNodeFreeKey(T4_mk);
        //读vector节点
        GmcIndexKeyT *T3_mk;
        ret = GmcNodeAllocKey(T3, "mk3", &T3_mk);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T3, "T5", &T5);
        EXPECT_EQ(GMERR_OK, ret);
        GmcIndexKeyT *T5_mk;
        ret = GmcNodeAllocKey(T5, "mk4", &T5_mk);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            uint64_t mk3 = 7 * j * index;
            ret = GmcNodeSetKeyValue(T3_mk, 0, GMC_DATATYPE_UINT64, &mk3, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T3, T3_mk, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            for (uint32_t m = 0; m < vector_num; m++) {
                uint64_t mk4 = 7 * m * index;
                ret = GmcNodeSetKeyValue(T5_mk, 0, GMC_DATATYPE_UINT64, &mk4, sizeof(uint64_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcNodeGetElementByKey(T5, T5_mk, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        GmcNodeFreeKey(T3_mk);
        GmcNodeFreeKey(T5_mk);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 016. array嵌套array, vector嵌套vector，异步插入顶点，异步更新顶点，异步查询顶点，异步删除顶点
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_016)
{
    int ret = 0;
    //创建epoll监听线程
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    start_num = 0;
    end_num = 100;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *treenode = NULL;

    void *vertexLabel = NULL;
    AsyncUserDataT data = {0};

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    // insert
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    // update vertex

    bool_value = true;
    f14_value = (char *)"newstr";
    index = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName1, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(2, data.affectRows);
    }

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertexBySP(
            stmt, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    // delete vertex

    for (int64_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 017. array嵌套array, vector嵌套vector，不同namespace下操作同名表
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_017)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    GmcConnT *conn1;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    const char *nameSpace = (const char *)"user001";
    ret = GmcDropNamespace(stmt1, nameSpace);
    ret = GmcCreateNamespace(stmt1, nameSpace, g_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt1, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);

    start_num = 0;
    end_num = 100;
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    void *treenode = NULL;
    void *vertexLabel = NULL;
    /***************user001******************/
    ret = GmcCreateVertexLabel(stmt1, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    TestInsertVertexBySP(stmt1, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    // update vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, labelName1, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertexBySP(
            stmt1, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    // delete vertex

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, labelName1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t pk = i;
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /***************user001******************/

    ret = GmcUseNamespace(stmt, "system");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    index = 1;
    TestInsertVertexBySP(stmt, index, bool_value, f14_value, start_num, end_num, array_num, vector_num, labelName1);

    // update vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // query vertex
    for (int i = start_num; i < end_num; i++) {
        int64_t pk = i;
        TestDirectFetchVertexBySP(
            stmt, pk, index, bool_value, f14_value, array_num, vector_num, labelName1, PKName1, 1);
    }

    // delete vertex

    for (int64_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt1, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt1, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 018. array嵌套array, vector嵌套vector，操作资源字段resource
*******************************************************************************/
TEST_F(ArrayNestInteractTest, DML_045_ArrayNestInteractTest_018)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    static const char *gResPoolName = "ResourcePool";
    static const uint64_t gResPoolId = 0;
    static const char *gResPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65535,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";

    // 创建资源池
    ret = GmcCreateResPool(stmt, gResPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/NormalTreeModelResource.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    ret = GmcBindResPoolToLabel(stmt, gResPoolName, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    void *vertexLabel = NULL;

    GmcNodeT *root;
    GmcNodeT *T1;
    GmcNodeT *T2;
    GmcNodeT *T3;
    GmcNodeT *T4;
    GmcNodeT *T5;
    // insert vertex
    int index = 1;
    bool bool_value = 0;
    char *f14_value = (char *)"string";
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        uint64_t respoolId = 0;
        uint64_t count = 1;
        uint64_t startIndex = i;
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(count, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
        EXPECT_EQ(GMERR_OK, ret);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update vertex
    bool_value = true;
    f14_value = (char *)"newstr";
    index = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);
        uint64_t respoolId = i;
        uint64_t count = 1;
        uint64_t startIndex = i + 10;
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(count, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
        EXPECT_EQ(GMERR_OK, ret);
        // 插array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T2, "T4", &T4);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < array_num; m++) {
                TestGmcSetNodePropertyByName_A(T4, m * index, bool_value, f14_value);
                if (m < array_num - 1) {
                    ret = GmcNodeGetNextElement(T4, &T4);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            if (j < array_num - 1) {
                ret = GmcNodeGetNextElement(T2, &T2);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, j * index, bool_value, f14_value);
            ret = GmcNodeGetChild(T3, "T5", &T5);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeAppendElement(T5, &T5);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcSetNodePropertyByName_V(T5, m * index, bool_value, f14_value);
            }
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete vertex

    for (int64_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &i, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, PKName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    //解绑
    ret = GmcUnbindResPoolFromLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName1);
    // 删除资源池
    ret = GmcDestroyResPool(stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}
