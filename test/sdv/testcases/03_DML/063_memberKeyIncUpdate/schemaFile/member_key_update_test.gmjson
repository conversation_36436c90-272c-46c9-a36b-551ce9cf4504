[{"type": "record", "name": "member_key_test", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F15", "type": "bytes", "size": 7, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "R1", "type": "record", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "A1", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}]}, {"name": "V1", "type": "record", "vector": true, "size": 18, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}]}]}, {"name": "V2", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "V3", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int8", "nullable": false}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": false}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": false}, {"name": "V5", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}]}, {"name": "R2", "type": "record", "fields": [{"name": "F0", "type": "int64", "nullable": true}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "V4", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}]}]}]}]}], "keys": [{"node": "member_key_test", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "R1/A1", "name": "member_keyA1", "index": {"type": "none"}, "fields": ["F0"], "constraints": {"unique": false}}, {"node": "R1/V1", "name": "member_key1_V1_unique", "index": {"type": "none"}, "fields": ["F1"], "constraints": {"unique": true}}, {"node": "R1/V1", "name": "member_key1_V1_un_unique", "index": {"type": "none"}, "fields": ["F3"], "constraints": {"unique": false}}, {"node": "V2", "name": "member_keyV2_unique", "index": {"type": "none"}, "fields": ["F1", "F2", "F3"], "constraints": {"unique": true}}, {"node": "V2/V3", "name": "member_keyV3", "index": {"type": "none"}, "fields": ["F0", "F1", "F2", "F4", "F5", "F6", "F16", "F11"], "constraints": {"unique": false}}, {"node": "V2/V3/V5", "name": "member_keyV5_unique", "index": {"type": "none"}, "fields": ["F0", "F2", "F4"], "constraints": {"unique": true}}, {"node": "V2/V3/R2/V4", "name": "member_keyV4_un_unique", "index": {"type": "none"}, "fields": ["F1", "F2", "F3"], "constraints": {"unique": false}}]}]