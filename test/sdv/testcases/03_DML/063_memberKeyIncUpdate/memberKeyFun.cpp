/* ****************************************************************************
 Description  : memberkey的功能测试
001.最内层vector的update操作
002.中间层vector的update操作
003.最外层vector的update操作
004.最内层vector的remove部分数据
005.中间层vector的remove部分数据
006.最外层vector的remove操作
007.最内层vector的append操作
008.中间层vector的append操作
009.最外层vector的append操作
010.update/remove/append 顺序的16次操作
011.update/append/remove 顺序的16次操作
012.remove/update/append顺序的16次操作
013.remove/append/update顺序的16次操作
014.append/remove/update顺序的16次操作
015.append/update/remove顺序的16次操作
016.append数据成功后进行remove操作
017.append数据成功后进行update操作
018.memberKey为唯一索引，update时更新memberKey字段为已存在的值
019.memberKey为唯一索引，append时memberKey字段为已存在的值
020.memberKey为唯一索引，update时更新memberKey为不存在的值，append时memberKey字段为update的值
021.member key非唯一时存在多个相同key值元素时重复update操作
022.member key唯一时重复update操作(memberKey不更新)
023.嵌套的3层vector同时进行update/remove/append操作，每层均进行满16次操作
024.中间层vector的remove操作(节点remove)
025.最内层vector remove(节点remove)
026.最外层vector remove(节点remove)
027.上层vector节点进行memberkey更新，下层节点进行index更新
028.两个横向不同vector节点同时进行memberkey更新
029.设置的key值不存在于记录中时进行更新或remove
030.append时超过schema中定义的vector节点size的大小
031.静态更新方式的校验
032.与GmcNodeGetElementByIndex混合使用
033.member key不唯一时重复update操作(memberkey更新)
034.member key唯一时重复update操作(memberkey更新)
035.member key为string类型
036.member key为bool/double类型
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/08/03
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "memberKeyIncUpdate.h"

class memberKeyIncUpdateFunctionTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void memberKeyIncUpdateFunctionTest::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void memberKeyIncUpdateFunctionTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void memberKeyIncUpdateFunctionTest::SetUp()
{
    int ret = 0;
    char *vertexLabel_schema = NULL;
    bool bool_value = false;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/member_key_update_test.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);
    //预置数据
    TestMemberKeyInsertVertex(
        g_conn, g_stmt, g_labelName1, bool_value, f14_value, fixed_value, start_num, end_num, array_num, vector_num);

    //查询
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num,
        end_num, array_num, vector_num, true);

    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    char errorMsg2[128] = {0};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}

void memberKeyIncUpdateFunctionTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

// 001.最内层vector的update操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_001)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    // update 1次
    TestGmcNodeSetPropertyByName_Node(V2_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0\n");
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, update_value, f14_value, fixed_value);
    }
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}
// 002.中间层vector的update操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_002)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    // update V3
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 003.最外层vector的update操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_003)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V5_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // V5 update
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, k, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 2; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, update_value, f14_value, fixed_value);
            }
            for (uint32_t m = 0; m < 2; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 004.最内层vector的remove部分数据
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_004)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        v2_key1 = i;
        v2_key2 = i;
        v2_key3 = i;
        TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
        ret = GmcNodeRemoveElementByKey(V2Node, V2key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2Node, 2, f14_value, fixed_value);
    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V3 Node\n");
    TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 0, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:1--2\n");
    GmcNodeT *V2NodeTest = NULL;
    for (int32_t j = 1; j < vector_num; j++) {
        printf("j:%d\n", j);
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2NodeTest);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
}

// 005.中间层vector的remove部分数据
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_005)
{
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 1; i < vector_num; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2Node, 0, f14_value, fixed_value);
    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V3 Node index:0\n");
    TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 0, 1, vector_num, f14_value, fixed_value);
    GmcNodeT *V3NodeTest = NULL;
    for (int32_t j = 1; j < vector_num; j++) {
        printf("j:%d\n", j);
        ret = GmcNodeGetElementByIndex(V2_V3Node, j, &V3NodeTest);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 006.最外层vector的remove操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_006)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 1; i < vector_num; i++) {
        int64_t key1 = i;
        int32_t key2 = i;
        int16_t key3 = i;
        TestMemberKeySetKeyValue_V5(V5key, key1, key2, key3);
        ret = GmcNodeRemoveElementByKey(V2_V3_V5Node, V5key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2Node, 0, f14_value, fixed_value);
    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V3 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2_V3Node, 0, &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2_V3Node, 0, f14_value, fixed_value);
    //获取R2节点
    ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, 0, f14_value, fixed_value);
    //获取V5节点
    ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, 0, f14_value, fixed_value);
    //获取V4节点
    ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t n = 0; n < vector_num; n++) {
        ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
    }
    GmcNodeT *V5NodeTest = NULL;
    for (int32_t j = 1; j < vector_num; j++) {
        printf("j:%d\n", j);
        ret = GmcNodeGetElementByIndex(V2_V3_V5Node, j, &V5NodeTest);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V3 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 007.最内层vector的append操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_007)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    // append 16次
    for (uint32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(V2Node, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }
    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V2 Node index:0--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 0, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:3--19\n");
    for (int32_t i = 3; i < 19; i++) {
        ret = GmcNodeGetElementByIndex(V2Node, i, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, i, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V2_V3Node, i, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
}

// 008.中间层vector的append操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_008)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    // append 16次
    for (uint32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }
    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:0\n");
    for (int32_t i = 0; i < 1; i++) {
        ret = GmcNodeGetElementByIndex(V2Node, i, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, i, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 0, vector_num, vector_num, f14_value, fixed_value);

        for (int j = 3; j < 19; j++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, j, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, j, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, (j - 2), &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, (j - 2), &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
    }
}

// 009.最外层vector的append操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_009)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "R2", &V2_V3_R2Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
    EXPECT_EQ(GMERR_OK, ret);
    // append 16次
    for (uint32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(V2_V3_R2_V4Node, &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3_R2_V4Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }
    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:0\n");
    for (int32_t i = 0; i < 1; i++) {
        ret = GmcNodeGetElementByIndex(V2Node, i, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, i, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, k, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t n = 0; n < 19; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
    }
}

// 010.update/remove/append 顺序的16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_010)
{
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);

    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // append 13次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);

    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2-14\n");
        for (uint32_t m = 2; m < 14; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, m, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 15, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 011.update/append/remove 顺序的16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_011)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);

    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    // append 13次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }

    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);

    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2-14\n");
        for (uint32_t m = 2; m < 14; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, m, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 15, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 012.remove/update/append顺序的16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_012)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);

    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    // append 13次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);

    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2-14\n");
        for (uint32_t m = 2; m < 14; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, m, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 15, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 013.remove/append/update顺序的16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_013)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);

    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // append 13次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);

    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2-14\n");
        for (uint32_t m = 2; m < 14; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, m, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 15, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 014.append/remove/update顺序的16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_014)
{
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // append 13次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);

    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2-14\n");
        for (uint32_t m = 2; m < 14; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, m, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 15, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 015.append/update/remove顺序的16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_015)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);

    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // append 13次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 2, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);

    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2-14\n");
        for (uint32_t m = 2; m < 14; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, m, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 15, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 016.append数据成功后进行remove操作(最内层)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_016)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    // append 16次
    for (uint32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(V2Node, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    // remove
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 2; i < 18; i++) {
        v2_key1 = i;
        v2_key2 = i;
        v2_key3 = i;
        TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
        ret = GmcNodeRemoveElementByKey(V2Node, V2key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);

    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }
    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0--1\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 0, 2, vector_num, f14_value, fixed_value);
    printf("V2 Node index:2\n");
    for (int32_t i = 2; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(V2Node, i, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, 18, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(V2_V3Node, i, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2 Node index:3--17\n");
    for (int32_t i = 3; i < 18; i++) {
        ret = GmcNodeGetElementByIndex(V2Node, i, &V2_Value1);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
}

// 017.append数据成功后进行update操作(中间层)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_017)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    uint8_t fixed_value_update[FIXED_PROPERTY_SIZE] = {0x11, 0x12, 0xff, 0xff, 0xff, 0xff, 0x11};
    char *f14_value_update = (char *)"1100xx00aabb";

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    // append 16次
    for (uint32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 16次
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 3; i < 19; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value_update, fixed_value_update);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }
    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:0\n");
    for (int32_t i = 0; i < 1; i++) {
        ret = GmcNodeGetElementByIndex(V2Node, i, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, i, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 0, vector_num, vector_num, f14_value, fixed_value);
        printf("V3 Node index:3--18\n");
        for (int j = 3; j < 19; j++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, j, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value_update, fixed_value_update);
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
    }
}

// 018.memberKey为唯一索引，update时更新memberKey字段为已存在的值
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_018)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEMBER_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    // update 1次
    TestGmcNodeSetPropertyByName_Node(V2_Value1, 2, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num,
        end_num, array_num, vector_num, true);
}

// 019.memberKey为唯一索引，append时memberKey字段为已存在的值
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_019)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEMBER_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    // append 16次
    for (uint32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(V2_V3_V5Node, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3_V5Node, m - 1, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);

    // check
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num,
        end_num, array_num, vector_num, true);
}

// 020.memberKey为唯一索引，update时更新memberKey为不存在的值，append时memberKey字段为update的值
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_020)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEMBER_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V5_Value1 = NULL;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    //"F0","F2","F4"
    TestMemberKeySetKeyValue_V5(V5key, 0, 0, 0);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // update
    TestGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    // append
    for (uint32_t m = 100; m < 110; m++) {
        ret = GmcNodeAppendElement(V2_V3_V5Node, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num,
        end_num, array_num, vector_num, true);
}

// 021.member key非唯一时存在多个相同key值元素时重复update操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_021)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V4_Value1 = NULL;
    ;
    uint32_t update_value = 100;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // V4 update
    ret = GmcNodeGetChild(V3_Value1, "R2", &V2_V3_R2Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_R2_V4Node, V4_member_key_name, &V4key);
    EXPECT_EQ(GMERR_OK, ret);
    //先把V4下面的元素都更新成相同key值得元素
    for (int32_t i = 0; i < vector_num; i++) {
        uint64_t v4key1 = i;
        int32_t v4key2 = i;
        uint32_t v4key3 = i;
        TestMemberKeySetKeyValue_V4(V4key, v4key1, v4key2, v4key3);
        ret = GmcNodeGetElementByKey(V2_V3_R2_V4Node, V4key, &V4_Value1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V4_Value1, update_value, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V4key);
    EXPECT_EQ(GMERR_OK, ret);

    //当相同key值时不更新key值进行重复更新
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // V4 update
    ret = GmcNodeGetChild(V3_Value1, "R2", &V2_V3_R2Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_R2_V4Node, V4_member_key_name, &V4key);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v4key1 = update_value;
    int32_t v4key2 = update_value;
    uint32_t v4key3 = update_value;
    int32_t update_value2 = 200;
    TestMemberKeySetKeyValue_V4(V4key, v4key1, v4key2, v4key3);
    ret = GmcNodeGetElementByKey(V2_V3_R2_V4Node, V4key, &V4_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeUpdateNotUpdateMemberKey_NodeV4(V4_Value1, update_value2, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    //重复执行
    for (int i = 0; i < 10; i++) {
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V4key);
    EXPECT_EQ(GMERR_OK, ret);

    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, k, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node index:1--2\n");
            for (uint32_t n = 1; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, update_value, f14_value, fixed_value);
            }
            printf("V4 Node index:0\n");
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, 0, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodeUpdateMemberKeyValue_NodeV4(
                V2_V3_R2_V4Node, (uint64_t)update_value, (int32_t)update_value, update_value);
            TestGmcGetNodeUpdateValueNotMemberKey_NodeV4(V2_V3_R2_V4Node, update_value2, f14_value, fixed_value);
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}
// 022.member key唯一时重复update操作(memberkey不更新)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_022)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    char *f14_value_update = (char *)"11111111";
    uint8_t fixed_value_update[7] = {0x11, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V5_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // V5 update
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeUpdateMemberkeyNotUpdate_NodeV5(V5_Value1, update_value, f14_value_update, fixed_value_update);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, k, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 2; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByNameMemberKey_NodeV5(V2_V3_V5Node, 2, 2, 2);
                TestGmcGetNodePropertyByNameNotMemberKey_NodeV5(
                    V2_V3_V5Node, update_value, f14_value_update, fixed_value_update);
            }
            for (uint32_t m = 0; m < 2; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 023.嵌套的3层vector同时进行update/remove/append操作，每层均进行满16次操作
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_023)
{
    int64_t primary_index_value = 1;
    TestMemberkeyUpdateRemoveAppend(g_stmt);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    TestMemberKeyCheckData(g_stmt);
}
// 024.中间层vector的remove操作(节点remove)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_024)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < vector_num; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2Node, 0, f14_value, fixed_value);
    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V3 Node index:0\n");
    GmcNodeT *V3NodeTest = NULL;
    for (int32_t j = 0; j < vector_num; j++) {
        printf("j:%d\n", j);
        ret = GmcNodeGetElementByIndex(V2_V3Node, j, &V3NodeTest);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 025.最内层vector remove(节点remove)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_025)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        v2_key1 = i;
        v2_key2 = i;
        v2_key3 = i;
        TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
        ret = GmcNodeRemoveElementByKey(V2Node, V2key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    printf("V2 Node index:0--2\n");
    GmcNodeT *V2NodeTest = NULL;
    for (int32_t j = 0; j < vector_num; j++) {
        printf("j:%d\n", j);
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2NodeTest);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_NO_DATA, ret);
    }
}

// 026.最外层vector remove(节点remove)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_026)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < vector_num; i++) {
        int64_t key1 = i;
        int32_t key2 = i;
        int16_t key3 = i;
        TestMemberKeySetKeyValue_V5(V5key, key1, key2, key3);
        ret = GmcNodeRemoveElementByKey(V2_V3_V5Node, V5key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    printf("V2 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2Node, 0, f14_value, fixed_value);
    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    printf("V3 Node index:0\n");
    ret = GmcNodeGetElementByIndex(V2_V3Node, 0, &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2_V3Node, 0, f14_value, fixed_value);
    //获取R2节点
    ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, 0, f14_value, fixed_value);

    //获取V4节点
    ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t n = 0; n < vector_num; n++) {
        ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
    }
    GmcNodeT *V5NodeTest = NULL;
    //获取V5节点
    ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t j = 0; j < vector_num; j++) {
        printf("j:%d\n", j);
        ret = GmcNodeGetElementByIndex(V2_V3_V5Node, j, &V5NodeTest);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V3 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    printf("V2 Node index:1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 027.上层vector节点进行memberkey更新，下层节点进行index更新
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_027)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // remove V3下面1个元素
    for (int i = 0; i < 1; i++) {
        TestMemberKeySetKeyValue_V2_V3(V3key, i, fixed_value);
        ret = GmcNodeRemoveElementByKey(V2_V3Node, V3key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // append 12次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);

    //再对V3下面的V5进行操作
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    // remove
    for (int i = 0; i < 2; i++) {
        ret = GmcNodeRemoveElementByIndex(V2_V3_V5Node, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // append
    for (uint32_t m = 3; m < 10; m++) {
        ret = GmcNodeAppendElement(V2_V3_V5Node, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
    }
    // update
    ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 2, &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V2_V3_V5Node, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t count = 0;
        ret = GmcNodeGetElementCount(V2_V3Node, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(14, count);
        ret = GmcNodeSortElement(V2_V3Node, "F1", GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t k = 1; k < 13; k++) {

            int32_t value = k + 2;
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            bool isNull = false;
            int64_t f0_value = 0;
            ret = GmcNodeGetPropertyByName(V2_V3_R2Node, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 0, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3Node, 2, f14_value, fixed_value);
        //获取R2节点
        ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, 2, f14_value, fixed_value);
        //获取V5节点
        ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
        }
        //获取V4节点
        ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t n = 0; n < vector_num; n++) {
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
        }

        ret = GmcNodeGetElementByIndex(V2_V3Node, 13, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
        //获取R2节点
        ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, 1, f14_value, fixed_value);
        //获取V5节点
        ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementCount(V2_V3_V5Node, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(8, count);
        ret = GmcNodeSortElement(V2_V3_V5Node, "F1", GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < 7; m++) {
            int32_t value = m + 3;
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, value, f14_value, fixed_value);
        }
        ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 7, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, update_value, f14_value, fixed_value);
        //获取V4节点
        ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t n = 0; n < vector_num; n++) {
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
        }
    }
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 028.两个横向不同vector节点同时进行memberkey更新
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_028)
{
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V5_Value1 = NULL, *V4_Value1 = NULL;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // V5 key为2的update
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    // append
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3_V5Node, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
    }
    // V4 key为0和1remove，2 update
    ret = GmcNodeGetChild(V3_Value1, "R2", &V2_V3_R2Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_R2_V4Node, V4_member_key_name, &V4key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V4(V4key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_R2_V4Node, V4key, &V4_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V4_Value1, update_value, f14_value, fixed_value);
    for (int i = 0; i < 2; i++) {
        TestMemberKeySetKeyValue_V4(V4key, i, i, i);
        ret = GmcNodeRemoveElementByKey(V2_V3_R2_V4Node, V4key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V4key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, k, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 2; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, update_value, f14_value, fixed_value);
            }
            for (uint32_t m = 0; m < 2; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            for (uint32_t m = 3; m < 15; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < 1; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, update_value, f14_value, fixed_value);
            }
            for (int i = 1; i < vector_num; i++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, i, &V4_Value1);
                EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
            }
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}
// 029.设置的key值不存在于记录中时进行更新或remove
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_029)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V2Key2 = NULL;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V2_Value1 = NULL;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, 2, 2, 1);
    ret = GmcNodeRemoveElementByKey(V2Node, V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 2021.11.23兼容V3GMERR_RESTRICT_VIOLATION修改成GMERR_OK
    ret = GmcNodeFreeKey(V2key);  // 2022.03.29 下一步开表前需要释放操作
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2Key2);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2Key2, 2, 2, 5);
    ret = GmcNodeGetElementByKey(V2Node, V2Key2, &V2_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V2_Value1, 5, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);  // 2023.11.27兼容V3  基于memberkey不存在增量更新，update报错
    TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    ret = GmcNodeFreeKey(V2Key2);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num,
        end_num, array_num, vector_num, true);
}

// 030.append时超过schema中定义的vector节点size的大小
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_030)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *key = NULL, *key2 = NULL, *key3 = NULL;
    uint64_t r1_v1_value = 1;
    GmcNodeT *R1_V1_Value1 = NULL, *R1_V1_Value2 = NULL, *R1_V1_Value3 = NULL;

    // 插入vector节点 R1.V1  16次
    for (int32_t m = 3; m < 19; m++) {
        ret = GmcNodeAppendElement(R1_V1Node, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);  // 适配错误码
    TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    // check
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num,
        end_num, array_num, vector_num, true);
}

// 031.静态更新方式的校验
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_031)
{
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 0;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    // update V3
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 第2个元素的值为第1个元素的值
    TestMemberKeySetKeyValue_V2_V3(V3key, 1, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    // remove第1个元素
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeRemoveElementByKey(V3_Value1, V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node index:0--1\n");
        for (uint32_t k = 0; k < 2; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            if (k == 0) {
                TestGmcGetNodePropertyByName_Node(V2_V3Node, 0, f14_value, fixed_value);
            } else {
                TestGmcGetNodePropertyByName_Node(V2_V3Node, 2, f14_value, fixed_value);
            }
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k + 1, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 2\n");
        ret = GmcNodeGetElementByIndex(V2_V3Node, 2, &V2_V3Node);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 032.与GmcNodeGetElementByIndex混合使用
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_032)
{
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V5_Value1 = NULL, *V2_Value1 = NULL;
    ;
    uint32_t update_value = 100;

    ret = GmcNodeGetElementByIndex(V2Node, 0, &V2Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    // index删除第0个元素
    ret = GmcNodeRemoveElementByIndex(V2_V3Node, 0);
    EXPECT_EQ(GMERR_OK, ret);
    // append 12次
    for (uint32_t m = 3; m < 15; m++) {
        ret = GmcNodeAppendElement(V2_V3Node, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3Node, m, f14_value, fixed_value);
    }
    // index update 1次
    ret = GmcNodeGetElementByIndex(V2_V3Node, 1, &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);

    // memberkey对V3下面的V5进行操作
    ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        int64_t v5_key1 = i;
        int32_t v5_key2 = i;
        int16_t v5_key3 = i;
        TestMemberKeySetKeyValue_V5(V5key, v5_key1, v5_key2, v5_key3);
        ret = GmcNodeRemoveElementByKey(V2_V3_V5Node, V5key);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // append
    for (uint32_t m = 3; m < 10; m++) {
        ret = GmcNodeAppendElement(V2_V3_V5Node, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
    }
    // update
    TestMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t count = 0;
        ret = GmcNodeGetElementCount(V2_V3Node, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(14, count);
        ret = GmcNodeSortElement(V2_V3Node, "F1", GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t k = 1; k < 13; k++) {

            int32_t value = k + 2;
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            bool isNull = false;
            int64_t f0_value = 0;
            ret = GmcNodeGetPropertyByName(V2_V3_R2Node, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 0, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        }
        ret = GmcNodeGetElementByIndex(V2_V3Node, 0, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3Node, 2, f14_value, fixed_value);
        //获取R2节点
        ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, 2, f14_value, fixed_value);
        //获取V5节点
        ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < vector_num; m++) {
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
        }
        //获取V4节点
        ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t n = 0; n < vector_num; n++) {
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
        }

        ret = GmcNodeGetElementByIndex(V2_V3Node, 13, &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
        //获取R2节点
        ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, 1, f14_value, fixed_value);
        //获取V5节点
        ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementCount(V2_V3_V5Node, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(8, count);
        ret = GmcNodeSortElement(V2_V3_V5Node, "F1", GMC_ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t m = 0; m < 7; m++) {
            int32_t value = m + 3;
            ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, value, f14_value, fixed_value);
        }
        ret = GmcNodeGetElementByIndex(V2_V3_V5Node, 7, &V2_V3_V5Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, update_value, f14_value, fixed_value);
        //获取V4节点
        ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t n = 0; n < vector_num; n++) {
            ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
        }
    }
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, start_num, 1,
        array_num, vector_num, true);
    TestMemberKeyQueryVertex(g_stmt, g_labelName1, g_primary_key_name, bool_value, f14_value, fixed_value, 2, end_num,
        array_num, vector_num, true);
}

// 033.memberKey不唯一时重复update操作(memberkey更新)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_033)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    // update V3
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);

    printf("重复执行后\n");
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    // update V3
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 1次
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V3_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);  // 2023.11.27兼容V3  基于memberkey不存在增量更新，update报错
    ret = TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, update_value, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 0; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

// 034.member key唯一时重复update操作(memberkey更新)
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_034)
{
    bool bool_value = false;
    int ret = 0;
    int start_num = 0;
    int end_num = 10;
    char *f14_value = (char *)"vertexTest";
    char *fixed_value = (char *)"vertex1";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *V2key = NULL, *V3key = NULL, *V4key = NULL, *V5key = NULL, *V3Key2 = NULL;
    uint64_t v2_key1 = 0;
    int32_t v2_key2 = 0;
    uint32_t v2_key3 = 0;
    uint64_t r1_v1_value = 1;
    GmcNodeT *V3_Value1 = NULL, *V2_Value1 = NULL, *V5_Value1 = NULL;
    uint32_t update_value = 100;
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    // V5 update
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);

    // 重复执行
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value);
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    ret = GmcNodeAllocKey(V2Node, V2_member_key_name, &V2key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2(V2key, v2_key1, v2_key2, v2_key3);
    ret = GmcNodeGetElementByKey(V2Node, V2key, &V2_Value1);
    ret = GmcNodeGetChild(V2_Value1, "V3", &V2_V3Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3Node, V3_member_key_name, &V3key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V2_V3(V3key, 0, fixed_value);
    ret = GmcNodeGetElementByKey(V2_V3Node, V3key, &V3_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(V3_Value1, "V5", &V2_V3_V5Node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(V2_V3_V5Node, V5_member_key_name, &V5key);
    EXPECT_EQ(GMERR_OK, ret);
    TestMemberKeySetKeyValue_V5(V5key, 2, 2, 2);
    ret = GmcNodeGetElementByKey(V2_V3_V5Node, V5key, &V5_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Node(V5_Value1, update_value, f14_value, fixed_value);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);  // 2023.11.27兼容V3  基于memberkey不存在增量更新，update报错
    ret = TestMemberKeyDmlVertexAffactRows(g_stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V2key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V3key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(V5key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName1, g_primary_key_name, primary_index_value, true);
    //获取根节点与非嵌套的vector节点和array节点
    uint32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value, fixed_value);
    }
    // 获取vector节点 R1.V1
    for (int32_t m = 0; m < vector_num; m++) {
        ret = GmcNodeGetElementByIndex(R1_V1Node, m, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_V1Node, m, f14_value, fixed_value);
    }

    // 获取vector节点 V2和V2.V3和V2.V3.V5和V2.V3.R2.V4
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeGetElementByIndex(V2Node, j, &V2Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(V2Node, j, f14_value, fixed_value);
        //获取V3节点
        ret = GmcNodeGetChild(V2Node, "V3", &V2_V3Node);
        EXPECT_EQ(GMERR_OK, ret);
        printf("V3 Node\n");
        for (uint32_t k = 0; k < 1; k++) {
            ret = GmcNodeGetElementByIndex(V2_V3Node, k, &V2_V3Node);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_Node(V2_V3Node, k, f14_value, fixed_value);
            //获取R2节点
            ret = GmcNodeGetChild(V2_V3Node, "R2", &V2_V3_R2Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("R2 Node\n");
            TestGmcGetNodePropertyByName_Node(V2_V3_R2Node, k, f14_value, fixed_value);
            //获取V5节点
            ret = GmcNodeGetChild(V2_V3Node, "V5", &V2_V3_V5Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V5 Node\n");
            for (uint32_t m = 2; m < vector_num; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, update_value, f14_value, fixed_value);
            }
            for (uint32_t m = 0; m < 2; m++) {
                ret = GmcNodeGetElementByIndex(V2_V3_V5Node, m, &V2_V3_V5Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_V5Node, m, f14_value, fixed_value);
            }
            //获取V4节点
            ret = GmcNodeGetChild(V2_V3_R2Node, "V4", &V2_V3_R2_V4Node);
            EXPECT_EQ(GMERR_OK, ret);
            printf("V4 Node\n");
            for (uint32_t n = 0; n < vector_num; n++) {
                ret = GmcNodeGetElementByIndex(V2_V3_R2_V4Node, n, &V2_V3_R2_V4Node);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_Node(V2_V3_R2_V4Node, n, f14_value, fixed_value);
            }
        }
        printf("V2_V3Node index 1--2\n");
        TestMemberKeyNotUpdateGetV3Node(V2_V3Node, 1, vector_num, vector_num, f14_value, fixed_value);
    }
    printf("V2Node index 1--2\n");
    TestMemberKeyNotUpdateGetV2Node(V2Node, 1, vector_num, vector_num, f14_value, fixed_value);
}

const char *g_labelName4 = (const char *)"member_key_test2";
// 035.member key为string类型
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_035)
{
    int ret = 0;
    char *vertexLabel_schema = NULL;
    bool bool_value = false;
    int start_num = 0;
    int end_num = 10;
    char *f14_value1 = (char *)"test11";
    char *f14_value2 = (char *)"11test";
    char *f14_value3 = (char *)"test22";
    char *fixed_value = (char *)"vertex1";
    char *f14_update_value = (char *)"111111111111111111111111111111111111111111111111111111111111111111111";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    readJanssonFile("schemaFile/member_key_test2.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);
    //预置数据
    TestMemberKeyInsertVertexLabelName4(g_conn, g_stmt, g_labelName4, bool_value, f14_value1, f14_value2, f14_value3,
        fixed_value, start_num, end_num, array_num, vector_num);
    // check
    TestMemberKeyQueryVertexLabelName4(g_stmt, g_labelName4, g_primary_key_name, bool_value, f14_value1, f14_value2,
        f14_value3, fixed_value, start_num, end_num, array_num, vector_num, true);
    //增量更新
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName4, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *key = NULL, *key2 = NULL, *key3 = NULL;
    uint64_t r1_v1_value = 1;
    GmcNodeT *R1_V1_Value1 = NULL, *R1_V1_Value2 = NULL, *R1_V1_Value3 = NULL;
    int32_t update_value = 100;

    ret = GmcNodeAllocKey(R1_V1Node, V1_member_key_name, &key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 1次
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_STRING, f14_value3, strlen(f14_value3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(R1_V1Node, key, &R1_V1_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Root(R1_V1_Value1, 2, bool_value, f14_update_value, fixed_value);
    // append节点 R1.V1  1次
    for (int32_t m = 3; m < 4; m++) {
        ret = GmcNodeAppendElement(R1_V1Node, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Root(R1_V1Node, m, bool_value, fixed_value, fixed_value);
    }
    // remove 2次
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_STRING, f14_value1, strlen(f14_value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(R1_V1Node, key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_STRING, f14_value2, strlen(f14_value2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(R1_V1Node, key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName4, g_primary_key_name, primary_index_value, true);

    //获取根节点与非嵌套的vector节点和array节点
    u_int32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value1, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value1, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value1, fixed_value);
    }
    // 获取vector节点 R1.V1 此处已更新
    uint32_t count = 0;
    ret = GmcNodeGetElementCount(R1_V1Node, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    ret = GmcNodeSortElement(R1_V1Node, "F1", GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(R1_V1Node, 0, &R1_V1Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Root(R1_V1Node, 2, bool_value, f14_update_value, fixed_value);
    ret = GmcNodeGetElementByIndex(R1_V1Node, 1, &R1_V1Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Root(R1_V1Node, 3, bool_value, fixed_value, fixed_value);
    // V2节点以及以下节点未update
    TestMemberKeyNotUpdateGetV2Node(V2Node, 0, vector_num, vector_num, f14_value1, fixed_value);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    EXPECT_EQ(GMERR_OK, ret);
}
/* 2022.04.21索引不支持double/float/bitmap/bool/partition/bitfield 该用例下架
// 036.member key为bool double类型
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_036)
{
    int ret = 0;
    char *vertexLabel_schema = NULL;
    bool bool_value = false;
    int start_num = 0;
    int end_num = 10;
    char *f14_value1 = (char *)"test11";
    char *f14_value2 = (char *)"11test";
    char *f14_value3 = (char *)"test22";
    char *fixed_value = (char *)"vertex1";
    char *f14_update_value = (char *)"111111111111111111111111111111111111111111111111111111111111111111111";
    int array_num = 3;
    int vector_num = 3;
    int64_t primary_index_value = 1;
    GmcNodeT *rootNode = NULL, *R1Node = NULL, *R1_A1Node = NULL, *R1_V1Node = NULL, *V2Node = NULL,
             *V2_V3_R2Node = NULL, *V2_V3Node = NULL, *V2_V3_R2_V4Node = NULL, *V2_V3_V5Node = NULL;

    readJanssonFile("schemaFile/member_key_test2.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);
    //预置数据
    TestMemberKeyInsertVertexLabelName4(g_conn, g_stmt, g_labelName4, bool_value, f14_value1, f14_value2, f14_value3,
        fixed_value, start_num, end_num, array_num, vector_num);
    // check
    TestMemberKeyQueryVertexLabelName4(g_stmt, g_labelName4, g_primary_key_name, bool_value, f14_value1, f14_value2,
        f14_value3, fixed_value, start_num, end_num, array_num, vector_num, true);
    //增量更新
    TestMemberKeyUpdateVertexSetPrimaryIndex(g_stmt, g_labelName4, g_primary_key_name, primary_index_value);
    //获取根节点与非嵌套的vector节点和array节点
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    GmcIndexKeyT *key = NULL, *key2 = NULL, *key3 = NULL;
    uint64_t r1_v1_value = 1;
    GmcNodeT *R1_V1_Value1 = NULL, *R1_V1_Value2 = NULL, *R1_V1_Value3 = NULL;
    int32_t update_value = 100;

    ret = GmcNodeAllocKey(R1_V1Node, V1_member_key_name2, &key);
    EXPECT_EQ(GMERR_OK, ret);
    // update 1次
    double v1key2value = 12.68;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_BOOL, &bool_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_DOUBLE, &v1key2value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(R1_V1Node, key, &R1_V1_Value1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_Root(R1_V1_Value1, 2, bool_value, f14_update_value, fixed_value);
    // append节点 R1.V1  1次
    for (int32_t m = 3; m < 4; m++) {
        ret = GmcNodeAppendElement(R1_V1Node, &R1_V1Node);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_Root(R1_V1Node, m, bool_value, fixed_value, fixed_value);
    }
    // remove 2次
    v1key2value = 10.68;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_BOOL, &bool_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_DOUBLE, &v1key2value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(R1_V1Node, key);
    EXPECT_EQ(GMERR_OK, ret);
    v1key2value = 11.68;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_BOOL, &bool_value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 1, GMC_DATATYPE_DOUBLE, &v1key2value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(R1_V1Node, key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestMemberKeyDmlVertexAffactRows(g_stmt, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeFreeKey(key);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    TestMemberKeyScanVertex(g_stmt, g_labelName4, g_primary_key_name, primary_index_value, true);

    //获取根节点与非嵌套的vector节点和array节点
    u_int32_t i = primary_index_value;
    TestMemberKeyGetRootAndChild_V(g_stmt, &rootNode, &R1Node, &R1_A1Node, &R1_V1Node, &V2Node);
    TestGmcGetNodePropertyByName_Root(rootNode, i, bool_value, f14_value1, fixed_value);
    TestGmcGetNodePropertyByName_Node(R1Node, i, f14_value1, fixed_value);
    // 获取array节点 R1.A1
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(R1_A1Node, j, &R1_A1Node);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_Node(R1_A1Node, j, f14_value1, fixed_value);
    }
    // 获取vector节点 R1.V1 此处已更新
    uint32_t count = 0;
    ret = GmcNodeGetElementCount(R1_V1Node, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    ret = GmcNodeSortElement(R1_V1Node, "F1", GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(R1_V1Node, 0, &R1_V1Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Root(R1_V1Node, 2, bool_value, f14_update_value, fixed_value);
    ret = GmcNodeGetElementByIndex(R1_V1Node, 1, &R1_V1Node);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcGetNodePropertyByName_Root(R1_V1Node, 3, bool_value, fixed_value, fixed_value);
    // V2节点以及以下节点未update
    TestMemberKeyNotUpdateGetV2Node(V2Node, 0, vector_num, vector_num, f14_value1, fixed_value);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    EXPECT_EQ(GMERR_OK, ret);
}
*/

// 038.member不支持为根节点，fixarray节点，不支持为非根节点reocrd
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_038)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    // 1.member不支持为根节点字段
    readJanssonFile("schemaFile/member_key_In_Root.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    free(vertexLabel_schema);

    // 2.member不支持为fix_array字段
    readJanssonFile("schemaFile/member_key_In_Record.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    free(vertexLabel_schema);

    // 3.member不支持为非根节点record字段
    readJanssonFile("schemaFile/member_key_In_Array.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    free(vertexLabel_schema);
    AW_FUN_Log(LOG_STEP, "test end");
}

// 039.nullable仅一般复杂表生效，简单表以及特殊复杂表不生效，check_validity设置为true
TEST_F(memberKeyIncUpdateFunctionTest, DML_063_002_039)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;
    char *vertexLabel_schema = NULL;
    // 1.简单表设置nullable为false不生效
    readJanssonFile("schemaFile/simpleLabel.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    GmcDropVertexLabel(g_stmt, (char *)"simpleLabel");
    char config[24] = {"check_validity:false"};
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);

    // 写数据不写非主键nullable false字段
    ret = testGmcPrepareStmtByLabelName(g_stmt, (char *)"simpleLabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, (char *)"simpleLabel");
    EXPECT_EQ(GMERR_OK, ret);

    // 2.一般负责表设置nullable为false生效
    readJanssonFile("schemaFile/gencomplex.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    GmcDropVertexLabel(g_stmt, (char *)"complexLabel");
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);

    // 写数据不写非主键nullable false字段
    ret = testGmcPrepareStmtByLabelName(g_stmt, (char *)"complexLabel", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    ret = GmcDropVertexLabel(g_stmt, (char *)"complexLabel");
    EXPECT_EQ(GMERR_OK, ret);

    // 3.特殊复杂表设置nullbale为false不生效
    readJanssonFile("schemaFile/specComplexLabel.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    GmcDropVertexLabel(g_stmt, (char *)"specialLabel");
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);
    ret = GmcDropVertexLabel(g_stmt, (char *)"specialLabel");
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end");
}
