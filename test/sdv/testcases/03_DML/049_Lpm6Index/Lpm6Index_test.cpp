/*****************************************************************************
 Description  : 迭代二 支持Lpm6索引 测试用例
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.26]
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_configJson[128] = "{\"max_record_count\" : 999999}";
int res = 0;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
int g_data_num = 100;

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

// 更新的字段值
uint8_t wr_fixed_update[16] = {
    0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
uint8_t loop_2 = 129;
//完全不同的值
uint8_t wr_fixed_diff[16] = {
    0x22, 0xc1, 0x84, 0x9a, 0x17, 0x66, 0x34, 0x91, 0x13, 0x56, 0xee, 0xff, 0x11, 0xed, 0xee, 0xff};
uint8_t wr_fixed_diff2[16] = {
    0x48, 0x1c, 0x04, 0x10, 0xe1, 0x22, 0x1e, 0x27, 0xf1, 0xb1, 0xf0, 0x01, 0x77, 0x14, 0x11, 0x02};
// 非法边界值
uint8_t uint8_tmp_01 = 0;
uint8_t uint8_tmp_02 = 129;
// uint32_t wr_fixed_err_01[5] = {0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff};
uint8_t wr_fixed_err_01[18] = {
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

// 待查询的ip
uint8_t wr_fixed_qury[16] = {
    0xcd, 0xcd, 0x11, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
uint8_t wr_fixed_notexist[16] = {
    0x4d, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};

//事务类型
GmcTxConfigT MSTrxConfig;

// path订阅
char vertexLabelNameT7[] = "lpm6";
char vertexLabelNameT8[] = "nhp_group";
char vertexLabelNameT9[] = "nhp_group_node";
char vertexLabelNameT10[] = "nhp";

char edgeLabelName_7_8[] = "from_7_to_8";
char edgeLabelName_8_9[] = "from_8_to_9";
char edgeLabelName_9_10[] = "from_9_to_10";

char *vertexLabelJson7 = NULL;
char *vertexLabelJson8 = NULL;
char *vertexLabelJson9 = NULL;
char *vertexLabelJson10 = NULL;
char *edgeLabelJson_7_8 = NULL;
char *edgeLabelJson_8_9 = NULL;
char *edgeLabelJson_9_10 = NULL;

void *vertexlabelT7 = NULL;
void *vertexlabelT8 = NULL;
void *vertexlabelT9 = NULL;
void *vertexlabelT10 = NULL;

class Lpm6Index_test : public testing::Test {
public:
    static void SetUpTestCase()
    {

        // 重启server
        system("sh $TEST_HOME/tools/start.sh");

        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    };
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void Lpm6Index_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    //定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    conn = NULL;
    stmt = NULL;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    res = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(1);
}

void Lpm6Index_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    res = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
    // 断开同步连接
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data);
}

/*****************************************************************************
 Description  : 001 schema定义新增key type：lpm6_tree_bitmap，同步创建两个vertexLabel，一个edgeLabel相关联；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.26]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_001)
{
    // 创建 vertexLabel_1
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    char labelName[128] = "lpm6";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 创建 vertexLabel_2
    char labelName2[128] = "lpm6_02";
    char *schema_json1 = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_02.gmjson", &schema_json1);
    ASSERT_NE((void *)NULL, schema_json1);
    ret = GmcCreateVertexLabel(stmt, schema_json1, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json1);

    //创建 edgeLabel
    char edgeName[128] = "lpm6_to_lpm6_02";
    char *edge_schema = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateEdgeLabel_test.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    ret = GmcCreateEdgeLabel(stmt, edge_schema, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(edge_schema);

    // drop edgeLabel, vertexLabel
    ret = GmcDropEdgeLabel(stmt, edgeName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 002 schema定义新增key type：lpm6_tree_bitmap，异步创建vertexLabel；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_002)
{
    char *schema_json = NULL;
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, res);

    // 异步创建vertexLabel
    AsyncUserDataT data = {0};
    readJanssonFile("schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    res = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, res);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("------------create vertexlabel------------\r\n");

    // drop vertexLabel
    res = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, res);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);
}

/*****************************************************************************
 Description  : 003 schema定义lpm6的constraint属性为false，预期建表报错；
 Notes        : lpm6索引为唯一索引，constraint属性必须为true；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_003)
{
    // 创建 vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_constraint_false_test.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 004 schema定义lpm6的constraint属性为true，预期建表成功；
 Notes        : lpm6索引为唯一索引，constraint属性必须为true；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_004)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 005 schema定义lmp6索引字段为3个字段vr_id、vrf_index、dest_ip_addr，预期建表失败；
 Notes        : lpm6索引必须为4个字段；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_fields_less_test.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 006 schema定义lmp6索引字段为4个字段vr_id、vrf_index、dest_ip_addr、mask_len，预期建表成功；
 Notes        : lpm6索引必须为4个字段；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_006)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 007
schema定义lmp6索引字段为5个字段vr_id、vrf_index、dest_ip_addr、mask_len、F0(自定义字段)，预期建表失败； Notes        :
lpm6索引必须为4个字段； History      : Author       : jiangshan/j30011431 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_fields_more_test.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 008 schema定义lmp6索引为4个字段vr_id、vrf_index、dest_ip_addr、F0(自定义字段)，预期建表成功；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_customfield_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 009 schema定义lmp6索引字段类型正确，分别为uint32、unit32、fixed（16字节）、uint8，预期建表成功；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_009)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010 schema定义lmp6索引字段类型错误，分别为uint8、unit32、fixed（16字节）、uint8，预期建表失败；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_010)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_type_error_test_01.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 011 schema定义lmp6索引字段类型错误，分别为uint32、unit8、fixed（16字节）、uint8,预期建表失败；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_011)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_type_error_test_02.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 012 schema定义lmp6索引字段类型错误，分别为uint32、unit32、unit32、uint8, 预期建表失败；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_012)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_type_error_test_03.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 013 schema定义lmp6索引字段类型错误，分别为uint32、unit32、fixed(8个字节)、uint8, 预期建表失败；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_013)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_type_error_test_04.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 014 schema定义lmp6索引字段类型错误，分别为uint32、unit32、fixed、uint32, 预期建表失败；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_014)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_type_error_test_05.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 015 schema定义lmp6索引字段类型顺序错误，分别为uint8、unit32、fixed（16字节）、uint32, 预期建表失败；
 Notes        : lpm6索引字段类型必须分别为uint32、unit32、fixed（16字节）、uint8；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_015)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_type_error_test_06.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

class Lpm6Index_test_02 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {
        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
        GmcDetachAllShmSeg();
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void Lpm6Index_test_02::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    conn = NULL;
    stmt = NULL;
    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

void Lpm6Index_test_02::TearDown()
{
    printf("\n======================TEST:END========================\n");

    // 断开同步连接
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016 schema定义lmp6索引字段为superfields中的vr_id、vrf_index、dest_ip_addr、mask_len，预期建表成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test_02, DML_049_LPM6_016)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_superfields_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017 schema定义lmp6索引在数组节点上，预期建表失败；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test_02, DML_049_LPM6_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *schema_json = NULL;
    readJanssonFile(
        "./schema_file/Lpm6Index_CreateVertexLabel_arrayNode_test.gmjson", &schema_json, false, g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema_json);
}

/*****************************************************************************
 Description  : 018 schema定义lmp6索引在非根节点上，预期建表失败；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test_02, DML_049_LPM6_018)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_notRootNode_test.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema_json);
}

/*****************************************************************************
 Description  : 019 schema定义2个lpm6索引，预期建表失败；
 Notes        : 一个表仅支持创建一个lpm4或lpm6索引；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_019)
{
    char *schema_json = NULL;
    readJanssonFile(
        "./schema_file/Lpm6Index_CreateVertexLabel_two_lpm6_test.gmjson", &schema_json, false, g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 020 schema定义1个lpm6索引，1个lpm4索引，预期建表失败；
 Notes        : 一个表仅支持创建一个lpm4或lpm6索引；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_020)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_lpm6_and_lpm4_test.gmjson", &schema_json, false,
        g_testNameSpace, false);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

/*****************************************************************************
 Description  : 021 scheme定义lpm6索引，创建多个表，实现全局多个lmp6索引，预期建表成功；
 Notes        : 支持全局多个lpm6索引
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_021)
{
    // create vertexLabel1
    char labelName[128] = "lpm6";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // create vertexLabel2
    char labelName2[128] = "lpm6_02";
    char *schema_json1 = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_02.gmjson", &schema_json1);
    ASSERT_NE((void *)NULL, schema_json1);
    ret = GmcCreateVertexLabel(stmt, schema_json1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json1);

    // create vertexLabel3
    char labelName3[128] = "lpm6_01";
    char *schema_json2 = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_01.gmjson", &schema_json2);
    ASSERT_NE((void *)NULL, schema_json2);
    ret = GmcCreateVertexLabel(stmt, schema_json2, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json2);

    // drop vertexLabel1、vertexLabel2、vertexLabel3
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName3);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022
插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据中已存在，dest_ip_addr与mask_len的组合在数据库中不存在，预期成功；
 Notes        :
vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.28]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_022)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据中已存在，dest_ip_addr与mask_len的组合在数据库中不存在，预期成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint8_t rd_primary_label = 0;
        uint8_t rd_attribute_id = 0;
        uint8_t rd_dest_ip_addr[16] = {0};
        uint8_t rd_mask_len = 0;
        ret = GmcGetVertexPropertyByName(stmt, "primary_label", &rd_primary_label, sizeof(oper_nums), &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(oper_nums), &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(oper_nums), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        cnt++;
    }
    printf("cnt:%lu \n", cnt);
    EXPECT_EQ(oper_nums + 1, cnt);

    // 主键读，插入成功，从原来的128条数据变成129条，所以此处用loop_2
    printf("\n========== insert one vertex, pk read, expect: %d ===============\n", loop_2);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, loop_2, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check插入数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023
插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据库中不存在、dest_ip_addr与mask_len在数据库中已存在，预期失败；【迭代五】预期成功；
 Notes        :
【迭代二】vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
【迭代五】修改为：vr_id、vrf_index在一个索引表中可以不同，vr_id、vrf_index、dest_ip_addr与mask_len四个字段组合一定是唯一的，适配用例；
 Author       : jiangshan/j30011431
 Modification : [2021.04.28]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_023)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据库中不存在、dest_ip_addr与mask_len在数据库中已存在，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    unsigned int wr_unit32_tmp = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n========== insert num: %d ===============\n", affectRows);

    // 主键读，插入成功，从原来的128条数据变成129条，所以此处用loop_2
    printf("\n========== insert one vertex, pk read, expect: %d ===============\n", loop_2);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, loop_2, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check插入数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32_tmp, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32_tmp, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024
插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index、dest_ip_addr、mask_len在数据库中不存在，预期失败；【迭代五】预期成功；
 Notes        :
【迭代二】vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
【迭代五】修改为：vr_id、vrf_index在一个索引表中可以不同，vr_id、vrf_index、dest_ip_addr与mask_len四个字段组合一定是唯一的，适配用例；
 Author       : jiangshan/j30011431
 Modification : [2021.04.28]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_024)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index、dest_ip_addr、mask_len在数据库中不存在，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    unsigned int wr_unit32_tmp = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n========== insert num: %d ===============\n", affectRows);

    // 主键读，插入成功，从原来的128条数据变成129条，所以此处用loop_2
    printf("\n========== insert one vertex, pk read, expect: %d ===============\n", loop_2);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, loop_2, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check插入数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32_tmp, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32_tmp, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025
插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index、dest_ip_addr、mask_len在数据库中已存在，预期失败； Notes        :
vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_025)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index、dest_ip_addr、mask_len在数据库中已存在，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 026
插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index、mask_len已存在，dest_ip_addr在数据库中不存在，预期成功； Notes :
vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_026)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index、mask_len已存在，dest_ip_addr在数据库中不存在,预期成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint8_t rd_primary_label = 0;
        uint8_t rd_attribute_id = 0;
        uint8_t rd_dest_ip_addr[16] = {0};
        uint8_t rd_mask_len = 0;
        ret = GmcGetVertexPropertyByName(stmt, "primary_label", &rd_primary_label, sizeof(oper_nums), &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(oper_nums), &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(oper_nums), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        cnt++;
    }
    EXPECT_EQ(oper_nums + 1, cnt);

    // 主键读
    printf("\n========== insert one vertex, pk read, expect: %d ===============\n", loop_2);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, loop_2, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check插入数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 027 根据pk更新lpm6索引引用的字段值vr_id、vrf_index为数据库中不存在的值，预期失败；【迭代五】预期成功；
 Notes        :
【迭代二】vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
【迭代五】修改为：vr_id、vrf_index在一个索引表中可以不同，vr_id、vrf_index、dest_ip_addr与mask_len四个字段组合一定是唯一的，适配用例；
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_027)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新的lpm6索引引用的字段值vr_id、vrf_index为数据库中不存在的值
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    unsigned int wr_unit32_tmp = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n========== update num: %d ===============\n", affectRows);

    // 主键读，更新成功
    printf("\n========== update one vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check更新数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vrf_index);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 028 根据pk更新lpm6索引引用的字段值mask_len在数据库中已存在，dest_ip_addr在数据库中不存在，预期成功；
 Notes        :
vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_028)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新的lpm6索引引用的字段值mask_len在数据库中已存在，dest_ip_addr在数据库中不存在
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n========== pk index update num: %d ===============\n", affectRows);

    // 主键读
    printf("\n========== update vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "update vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check更新数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 029 根据pk更新lpm6索引引用的字段值mask_len和dest_ip_addr在数据库中不存在，预期成功；
 Notes        :
vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_029)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新的lpm6索引引用的字段值mask_len和dest_ip_addr在数据库中不存在，预期成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n========== pk index update num: %d ===============\n", affectRows);

    // 主键读
    printf("\n========== update vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "update vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check更新数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 030 根据pk更新lpm6索引引用的字段值vr_id、vrf_index、dest_ip_addr、mask_len为数据库中存在的值，预期失败；
 Notes        :
vr_id、vrf_index在一个索引表中全部相同，dest_ip_addr有可能出现相同的，但此时mask_len不一样，即dest_ip_addr与mask_len的组合一定是唯一的；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_030)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新的lpm6索引引用的字段值vr_id、vrf_index、dest_ip_addr、mask_len为数据库中存在的值，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    uint8_t masklen_tmp = 1;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_tmp, sizeof(masklen_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 031 vertexLabel1和vertexLabel2插入相同的数据，预期成功；
 Notes        : 表间的vr_id、vrf_index没有互异校验；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_031)
{
    // create vertexLabel1
    char *schema_json1 = NULL;
    void *vertexLabel1 = NULL;
    char labelName1[128] = "lpm6";
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json1);
    ASSERT_NE((void *)NULL, schema_json1);
    ret = GmcCreateVertexLabel(stmt, schema_json1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json1);

    // create vertexLabel2
    char *schema_json2 = NULL;
    void *vertexLabel2 = NULL;
    char labelName2[128] = "lpm6_02";
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_02.gmjson", &schema_json2);
    ASSERT_NE((void *)NULL, schema_json2);
    ret = GmcCreateVertexLabel(stmt, schema_json2, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json2);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, labelName1, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, labelName1, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, labelName2, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, labelName2, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 032 插入数据，lpm6索引引用字段的规格校验，dest_ip格式非法；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_032)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 插入数据，dest_ip格式非法，uint32_t wr_fixed_err_01[5] = {0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
    // 0xffffffff}，预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_err_01, 18);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 033 插入数据，lpm6索引引用字段的规格校验，masklen最大是128位；
 Notes        : ipv6的masklen范围是[1,128]
 History      : [2021.8.14]支持masklen为0
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_033)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    uint32_t wr_unit32_033 = 15;
    uint8_t loop_033 = 128;
    // 写入1条数据，masklen为0，预期报错
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_033, sizeof(wr_unit32_033));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_033, sizeof(wr_unit32_033));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &uint8_tmp_01, sizeof(uint8_tmp_01));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_033, sizeof(loop_033));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_033, sizeof(loop_033));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 写入1条数据，masklen为129，预期报错
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &uint8_tmp_02, sizeof(uint8_tmp_02));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 034 更新数据，lpm6索引引用字段的规格校验，dest_ip格式错误；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_034)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新的lpm6索引引用的字段值dest_ip非法，预期失败
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_err_01, 18);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 035 更新数据，lpm6索引引用字段的规格校验，masklen最大是128位；
 Notes        : ipv6的masklen范围是[1,128]
 History      :[2021.8.14]支持masklen为0
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_035)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新的lpm6索引引用的字段值masklen为0，预期失败
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &uint8_tmp_01, sizeof(uint8_tmp_01));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 根据pk更新，更新的lpm6索引引用的字段值masklen为129，预期失败
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &uint8_tmp_02, sizeof(uint8_tmp_02));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 036 scheme定义新增key type：lpm6_tree_bitmap，调用batch DDL接口同步批量建表、删表，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 准备127个3kb大小的schema json文件;
    system("sh create_multi_label_1k.sh 127");

    char configJson[] = "{\"max_record_count\" : 1000}";

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 添加批量执行操作：create vertexLabel 127

    char labelName[512];
    char schema_path[512];
    for (int i = 1; i <= 127; i++) {

        char *schema_json = NULL;
        sprintf(labelName, "lpm6_%d", i);
        sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);

        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(127, totalNum);
    EXPECT_EQ(127, successNum);
    printf("\n[INFO] batch create vertexLabel status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量drop vertexLabel
    GmcBatchT *batchDrop = NULL;
    GmcBatchRetT batchRetDrop;
    ret = GmcBatchPrepare(conn, NULL, &batchDrop);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 1; i <= 127; i++) {
        schema_json = NULL;
        sprintf(labelName, "lpm6_%d", i);
        ret = GmcBatchAddDDL(batchDrop, GMC_OPERATION_DROP_VERTEX_LABEL, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 执行批量操作
    ret = GmcBatchExecute(batchDrop, &batchRetDrop);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRetDrop, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(127, totalNum);
    EXPECT_EQ(127, successNum);
    printf("\n[INFO] batch drop vertexLabel status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    ret = GmcBatchDestroy(batchDrop);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 037 scheme定义新增key type：lpm6_tree_bitmap，调用batch DDL接口异步批量建表、删表，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    int res = 0;
    //创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, res);

    char *vertexLabel_schema1, *vertexLabel_schema2, *edgeLabel_schema = NULL;
    char labelName1[128] = "lpm6";
    char labelName2[128] = "lpm6_02";
    char edgeName[128] = "lpm6_to_lpm6_02";
    GmcDropVertexLabel(stmt, labelName1);
    GmcDropVertexLabel(stmt, labelName2);
    // 读取两个顶点label和一个边label的schema json
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &vertexLabel_schema1);
    ASSERT_NE((void *)NULL, vertexLabel_schema1);

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_02.gmjson", &vertexLabel_schema2);
    ASSERT_NE((void *)NULL, vertexLabel_schema2);

    readJanssonFile("./schema_file/Lpm6Index_CreateEdgeLabel_test.gmjson", &edgeLabel_schema);
    ASSERT_NE((void *)NULL, edgeLabel_schema);

    // 预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // DDL add cmd: 2 vetex label, 1 edge label
    char new_configJson[128] = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0}";
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, new_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, new_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, new_configJson);
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // 执行异步批量操作
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    sleep(2);
    EXPECT_EQ(GMERR_OK, data.status);
#ifdef NRELEASE
#else
    EXPECT_EQ(3, data.totalNum);
    EXPECT_EQ(3, data.succNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO][CLIENT] DDL batch execute callback status %d, totalNum is %d, succNum is %d\n",
        data.status, data.totalNum, data.succNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    // drop edge, vertex label
#ifdef NRELEASE
#else
    ret = GmcDropEdgeLabel(stmt, edgeName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema1);
    free(vertexLabel_schema2);
    free(edgeLabel_schema);
}

/*****************************************************************************
 Description  : 038 scheme json定义新增key type：lpm6_tree_bitmap，使用gmimport工具导入创建vertexLabel，预期成功；
 Notes        : gmimport -f 场景
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.27]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_038)
{
    // gmimport工具导入创建vertexLabel
    char schema_file[128] = "schema_file/Lpm6Index_CreateVertexLabel_test.gmjson";
    char cmd[512];
    // -c: dstore | vschema | vdata | eschema | edata | cache
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -s %s -ns %s", g_toolPath, schema_file,
        g_connServer, g_testNameSpace);
    printf("\ncmd is: %s\n\n", cmd);
    sleep(1);
    system(cmd);

    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 接口创建同名VertexLabel
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);  // 预期返回label已存在
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 039 lpm6索引查询，所要查询的ip在索引表中没有可以匹配的数据；
 Notes        : 扫描出来的数据为0，不报错
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_039)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 通过lpm6索引查询
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_notexist, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, isNull);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 040 lpm6索引查询，所要查询的ip在索引表中有多个匹配的数据，验证最长前缀匹配,预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_040)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据,ip为"0xcdcd910a222254988475111139002021", mask_len为128；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 1
    // 通过lpm6索引查询 "0xcdcd910a222254988475111139002020",匹配128位，查询应该得到最后一条数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(oper_nums, rd_mask_len);
    }

    // 2
    // 通过lpm6索引查询
    // "0xcdcd910a222254988475111139002020",匹配128位，查询应该得到最后一条数据,但是此时传入的masklen为16，masklen对查询不影响，校验；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t oper_nums_tmp = 16;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(oper_nums, rd_mask_len);
    }
    // wr_fixed[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    // wr_fixed_qury[16] = {0xcd, 0xcd, 0x11, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x21}; wr_fixed_update[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00,
    // 0x20, 0x21};
    // 3
    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    // 4
    // 通过lpm6索引查询
    // "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16,但是此时传入的masklen为128，masklen对查询不影响，校验；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        uint8_t expect_mask_len = 16;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    // 5
    // 通过lpm6索引查询
    // "0xcdcd910a222254988475111139002021",此时应该匹配新插入的那条ip,masklen给16，但是匹配出来的应该是128
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(oper_nums, rd_mask_len);
    }

    // 6
    // 通过lpm6索引查询
    // "0xcdcd910a222254988475111139002021",此时应该匹配新插入的那条ip,masklen给128，匹配出来的应该是128
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        uint8_t ex_rd_mask_len_01 = 126;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(oper_nums, rd_mask_len);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 041 不支持按照lpm6索引更新，GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_041)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, sizeof(wr_fixed_update));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 042 不支持根据lpm6索引删除，GmcDeleteVertexByIndexKey接口传入lpm6, 预期报错；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_042)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 删除失败后，扫描，数据没有减少
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }

    EXPECT_EQ(128, cnt);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 043 根据主键删除，再次插入数据，反复执行多次上述操作, 预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_043)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据主键删除，再次插入数据，反复执行多次上述操作
    for (uint8_t i = 0; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // 根据主键删除
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        int affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        // 插入数据
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        // fixed 34
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
        EXPECT_EQ(GMERR_OK, ret);

        // fixed 34
        ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 044 条件过滤删除，再次插入数据，反复执行多次上述操作, 预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_044)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 条件过滤删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    const char *cond1 = (const char *)"lpm6.mask_len<=64";
    ret = GmcSetFilter(stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(64, affectRows);

    // 插入数据
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, 64);
    EXPECT_EQ(GMERR_OK, ret);

    // 条件过滤删除，再次插入数据，反复执行多次上述操作
    for (uint8_t i = 0; i < 200; i++) {
        // 条件过滤删除
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        const char *cond1 = (const char *)"lpm6.mask_len<=64";
        ret = GmcSetFilter(stmt, cond1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        int affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(64, affectRows);

        // 插入数据
        ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, 64);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 045 lpm6索引同步drop Label，再次创建同名Label，写入数据，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_045)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 再次create vertexLabel
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);

    // 主键读
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 046 lpm6索引异步drop Label，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_046)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, res);

    // drop vertexLabel
    AsyncUserDataT data = {0};
    res = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, res);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 047 lpm6索引同步truncate Label，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_047)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate后，扫描，数据不存在
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(0, cnt);

    // truncate只删除数据，不删除索引结构；再次插入数据，验证lpm6索引仍然可以使用
    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 048 lpm6索引异步truncate Label，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_048)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 断开同步连接
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, res);

    // 异步 truncate vertexlabel
    AsyncUserDataT data = {0};
    res = GmcTruncateVertexLabelAsync(g_stmt_async, g_labelName, truncate_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, res);
    res = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, res);
    ASSERT_EQ(GMERR_OK, data.status);

    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    //查询truncate结果
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(0, cnt);

    // truncate只删除数据，不删除索引结构；再次插入数据，验证lpm6索引仍然可以使用
    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 049 开启事务，根据pk更新lpm6索引引用字段数据，commit；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_049)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新dest_ip_addr为0xcdcd910a222254988475111139002021;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // MS事务commit
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect rows
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // 使用主键check更新数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 050 开启事务，根据pk更新lpm6索引引用字段数据，rollback；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_050)
{
    // create vertexLabel
    char *schema_json = NULL;
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 根据pk更新，更新dest_ip_addr为0xcdcd910a222254988475111139002021
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // MS事务Rollback
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check更新数据，lpm6索引引用的字段，没有变化
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 051 开启事务，根据pk删除数据，commit；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_051)
{
    // create vertexLabel
    char *schema_json = NULL;
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据pk删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // MS事务commit
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect rows
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 主键读被删除的字段，应该不存在
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 052 开启事务，根据pk删除数据，rollback；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_052)
{
    // create vertexLabel
    char *schema_json = NULL;
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据pk删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // MS事务Rollback
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读被删除的字段，应该存在
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 053 创建全表订阅，根据pk索引更新、删除数据，触发全表订阅推送；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_053)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //创建订阅连接
    int chanRingLen = 256;
    conn_sn_sync = NULL;
    stmt_sn_sync = NULL;
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);

    // 创建全表订阅
    char *sub_info = NULL;
    readJanssonFile("./schema_file/all_type_union_pk_schema_subinfo.gmjson", &sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, conn_sn_sync, sn_callback_simple, user_data);
    ASSERT_NE((void *)NULL, sub_info);
    ASSERT_EQ(GMERR_OK, ret);
    free(sub_info);

    // 写入数据，写入1条数据，欧拉 写入128条会报超时
    int userDataIdx = 0;
    ((int *)(user_data->new_value))[userDataIdx] = 1111;
    uint8_t oper_nums = 1;
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // sn check
    // sn_push_check(conn_sn_sync, vertexLabel, GMC_SUB_EVENT_INSERT, g_labelName, "dest_ip_addr", GMC_DATATYPE_FIXED,
    // oper_nums, NULL, wr_fixed, "sn push insert", 1);

    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // pk更新dest_ip_addr
    userDataIdx += 1;
    ((int *)(user_data->old_value))[userDataIdx] = 1111;
    ((int *)(user_data->new_value))[userDataIdx] = 2222;
    unsigned short up_qos_profile_id = 2222;
    char up_fixed[36] = "update";
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, sizeof(wr_fixed_update));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n========== pk update num: %d ===============\n", affectRows);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "update vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // sn_push_check(conn_sn_sync, vertexLabel, GMC_SUB_EVENT_UPDATE, g_labelName, "dest_ip_addr", GMC_DATATYPE_FIXED,
    // 1, wr_fixed, wr_fixed_update, "sn push update", 1);

    // pk 删除
    userDataIdx += 1;
    ((int *)(user_data->old_value))[userDataIdx] = 2222;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n[INFO] [ pk delete ] affect row: %d \n\n", affectRows);

    printf("\n========== pk delete num: %d, pk read ===============\n", affectRows);
    // 删除后，128条还剩127条数据
    // ret = test_PK_read_lpm6(conn, "primary_key", 1, 1, GMC_DATATYPE_UINT8, conn_id, "after delete vertex", isPrint);
    // EXPECT_EQ(GMERR_OK, ret);

    // sn_push_check(conn_sn_sync, vertexLabel, GMC_SUB_EVENT_DELETE, g_labelName, "dest_ip_addr", GMC_DATATYPE_FIXED,
    // 1, wr_fixed_update, NULL, "sn push delete", 1);

    // 取消订阅
    GmcFreeIndexKey(stmt);
    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    testSubDisConnect(conn_sn_sync);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 056
同一张表的lpm6索引并发场景：T1：vertexLabel根据lpm6索引循环查询；T2：vertexLabel根据lpm6索引循环查询； Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
#define THR_NUM 2
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];
void *g_vertexLabel_tht[THR_NUM * 2];
void *thread_Lpm6IndexRead_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);

        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("thread_1:");
        for (int i = 0; i < 16; i++) {
            printf("%u ", rd_dest_ip_addr[i]);
        }
        printf("\n");
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

TEST_F(Lpm6Index_test, DML_049_LPM6_056)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 多线程并发唯一lpm6索引查询
    pthread_t thr_arr[32];
    void *thr_ret[32];
    int index[32] = {0};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < THR_NUM; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_Lpm6IndexRead_test, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < THR_NUM; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 057
不同表的lpm6索引并发场景：T1：vertexLabel1根据lpm6索引循环查询；T2：vertexLabel2根据lpm6索引循环查询； Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
char labelName2[128] = "lpm6_02";
void *thread_Lpm6IndexRead02_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);

        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("thread_2:");
        for (int i = 0; i < 16; i++) {
            printf("%u ", rd_dest_ip_addr[i]);
        }
        printf("\n");

        // for(int i = 0; i < 16; i++){
        // 	EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        // }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

TEST_F(Lpm6Index_test, DML_049_LPM6_057)
{
    // 创建 vertexLabel_1
    char labelName[128] = "lpm6";
    char *schema_json = NULL;
    void *vertexLabel = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt, labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex1 num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建 vertexLabel_2
    char *schema_json1 = NULL;
    void *vertexLabel1 = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_02.gmjson", &schema_json1);
    ASSERT_NE((void *)NULL, schema_json1);
    ret = GmcCreateVertexLabel(stmt, schema_json1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json1);
    // 写入数据
    ret = test_insert_vertex_lpm6(stmt, labelName2, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex2 num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, labelName2, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    printf("---------------------");

    // 多线程并发唯一lpm6索引查询
    pthread_t thr_arr[2];
    void *thr_ret[2];
    int index[2] = {0, 1};

    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    res = pthread_create(&thr_arr[0], NULL, thread_Lpm6IndexRead_test, (void *)&index[0]);
    EXPECT_EQ(GMERR_OK, res);
    res = pthread_create(&thr_arr[1], NULL, thread_Lpm6IndexRead02_test, (void *)&index[1]);
    EXPECT_EQ(GMERR_OK, res);

    pthread_join(thr_arr[0], &thr_ret[0]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    EXPECT_EQ(GMERR_OK, ret);

    // drop vertexLabel1 and vertexLabel2
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 058
并发场景，与pk、localhash索引交互：T1：根据pk、localhash索引混合更新，删除；T2：根据lpm6索引循环查询； Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
void *thread_Lpm6IndexRead03_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 通过lpm6索引查询
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t wr_fixed_qury_02[16] = {
        0xb9, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    uint8_t expect_mask_len = 127;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed_qury_02, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed_02[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
    }

    // 增加reset
    GmcResetStmt(stmt);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *thread_pk_localhash_update_delete_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // localhash 更新，非唯一hash索引更新
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_FIXED, wr_fixed, sizeof(wr_fixed));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned short up_qos_profile_id = 2222;
    ret = GmcSetVertexProperty(
        g_stmt_tht[conn_id], "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update,
    // sizeof(wr_fixed_update)); EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    int affectRows = 0;
    uint32_t len = 0;
    uint8_t oper_nums_01 = 64;
    ret = GmcGetStmtAttr(g_stmt_tht[conn_id], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums_01, affectRows);
    printf("\n[INFO] [ non-uniq hash update ] affect row: %d \n\n", affectRows);

    // 增加reset
    GmcResetStmt(stmt);

    // 根据pk删除
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT8, &oper_nums_01, sizeof(oper_nums_01));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT8, &oper_nums_01, sizeof(oper_nums_01));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(g_stmt_tht[conn_id], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n[INFO] [ pk delete ] affect row: %d \n\n", affectRows);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

TEST_F(Lpm6Index_test, DML_049_LPM6_058)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_03.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据1
    // ip 为 {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x20}，masklen从1-64
    int isPrint = 0;
    uint8_t oper_nums_01 = 64;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums_01);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums_01);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums_01, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    printf("after read 64 \n");

    // 写入数据2
    // ip 为 {0xb9, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x20}，masklen从65-128
    uint8_t oper_nums_02 = 65;
    ret = test_insert_vertex_lpm6_02(stmt, g_labelName, oper_nums_02, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    printf("after read 128 \n");

    // 多线程并发更新、删除  和  查询
    pthread_t thr_arr[2];
    void *thr_ret[2];
    int index[2] = {0, 1};
    // memset(g_conn_tht, 0, sizeof(void*) * THR_NUM * 2);

    res = pthread_create(&thr_arr[0], NULL, thread_pk_localhash_update_delete_test, (void *)&index[0]);
    EXPECT_EQ(GMERR_OK, res);
    res = pthread_create(&thr_arr[1], NULL, thread_Lpm6IndexRead03_test, (void *)&index[1]);
    EXPECT_EQ(GMERR_OK, res);

    pthread_join(thr_arr[0], &thr_ret[0]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 059
并发场景，与hashcluster、local索引交互：T1：根据hashcluster混合更新，删除，local索引区间查询；T2：根据lpm6索引循环查询；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.04.29]
*****************************************************************************/
void *thread_hashcluster_local_update_delete_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 非唯一hashcluster索引 更新
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_FIXED, wr_fixed, sizeof(wr_fixed));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned short up_qos_profile_id = 2222;
    ret = GmcSetVertexProperty(
        g_stmt_tht[conn_id], "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update,
    // sizeof(wr_fixed_update)); EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    uint8_t oper_nums_01 = 64;
    ret = GmcGetStmtAttr(g_stmt_tht[conn_id], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums_01, affectRows);
    printf("\n[INFO] [ non-uniq hashcluster update ] affect row: %d \n\n", affectRows);

    // local 区间删除[20, 25]
    uint8_t l_val_del = 20;
    uint8_t r_val_del = 25;
    unsigned int arrLen = 1;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT8;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT8;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    // 设置删除区间  [20, 25]
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt_tht[conn_id], items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(g_stmt_tht[conn_id], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6, affectRows);
    printf("\n[INFO] [ local delete ] affect row: %d \n\n", affectRows);

    free(leftKeyProps_del);
    free(rightKeyProps_del);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

TEST_F(Lpm6Index_test, DML_049_LPM6_059)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_04.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据1
    // ip 为 {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x20}，masklen从1-64
    int isPrint = 0;
    uint8_t oper_nums_01 = 64;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums_01);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums_01);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums_01, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    printf("after read 64 \n");

    // 写入数据2
    // ip 为 {0xb9, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x20}，masklen从65-128
    uint8_t oper_nums_02 = 65;
    ret = test_insert_vertex_lpm6_02(stmt, g_labelName, oper_nums_02, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    printf("after read 128 \n");

    // 多线程并发更新、删除  和  查询
    pthread_t thr_arr[2];
    void *thr_ret[2];
    int index[2] = {0, 1};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    res = pthread_create(&thr_arr[0], NULL, thread_hashcluster_local_update_delete_test, (void *)&index[0]);
    EXPECT_EQ(GMERR_OK, res);
    res = pthread_create(&thr_arr[1], NULL, thread_Lpm6IndexRead03_test, (void *)&index[1]);
    EXPECT_EQ(GMERR_OK, res);

    pthread_join(thr_arr[0], &thr_ret[0]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 060 支持GmcGetVertexCount接口，lpm6索引查询不支持，这里使用的是主键，相当于全表记录数；
 Notes        :
【迭代五】支持GmcGetVertexCount接口使用lpm6索引查询,查询的结果是匹配vrid和vrfid的结果，ip和masklen不做匹配校验 History :
 Author       : jiangshan/j30011431
 Modification : [2021.05.06]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_060)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    // 插入128条数据，全表查询，预期128
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, result);

    // lpm6索引查询，预期128条
    const char *KeyName = "lpm6_key";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(128, result);

    // lpm6索引查询，输入不存在的vrid和vrfindex,不报错，查询结果为0
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, result);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据库中不存在、dest_ip_addr与mask_len在数据库中已存在，预期成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n========== insert num: %d ===============\n", affectRows);

    // 主键读，插入成功，从原来的128条数据变成129条，所以此处用loop_2
    printf("\n========== insert one vertex, pk read, expect: %d ===============\n", loop_2);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, loop_2, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6索引查询，输入最新插入的vrid和vrfindex,预期1
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, result);

    // lpm6索引查询，输入不存在的ip和masklen,预期1，因为不做这两个字段的匹配校验
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_notexist, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, result);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 061 使用lpm6索引查询的时，给出的masklen格式非法的校验；
 Notes        : 用例032-035，只校验了索引表中ip和masklen格式非法的校验，所以补充061用例；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.05.09]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_061)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 通过lpm6索引查询，给出的masklen格式非法
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 通过lpm6索引查询，给出的ip格式非法
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_err_01, 18);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 062 ip和masklen组合唯一校验；
 Notes        : 用例022-030，只校验了索引表中ip和masklen相等情况的组合非唯一，没有校验，例如，ip和masklen分别为 1100 2
和1110 2，这两个是不能同时插入的，所以新增用例； History      : Author       : jiangshan/j30011431 Modification :
[2021.05.09]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_062)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 已经写入索引表的数据是：ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    // 再次写入：ip为"0xcdcd910a222254988475111139002021", mask_len为16
    // 此时索引表中的
    // ip:0xcdcd910a222254988475111139002020,masklen:16和ip:0xcdcd910a222254988475111139002021,masklen:16是冲突的，预期写入失败
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    uint8_t oper_nums_tmp = 16;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // 读128
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t rd_dest_ip_addr[16] = {0};
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    // 主键更新索引表中最后一条数据为冲突数据：ip:0xcdcd910a222254988475111139002021,masklen:16，预期更新失败
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 读16行
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    memset(rd_dest_ip_addr, 0, sizeof(rd_dest_ip_addr));
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    rd_mask_len = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums_tmp, rd_mask_len);

    // 读128
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    memset(rd_dest_ip_addr, 0, sizeof(rd_dest_ip_addr));
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    rd_mask_len = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // 扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint8_t rd_primary_label = 0;
        uint8_t rd_attribute_id = 0;
        ret = GmcGetVertexPropertyByName(stmt, "primary_label", &rd_primary_label, sizeof(oper_nums), &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(oper_nums), &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(oper_nums), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        cnt++;
    }
    ASSERT_EQ(oper_nums, cnt);

    uint64_t result;
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, result);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 063 同步建表，唯一lpm6索引多线程读取同一条数据及扫描同张表数据  【验收用例场景】
 Notes        : T1：主键读数据，T2：lpm6索引扫描数据；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.05.18]
*****************************************************************************/
void *thread_Lpm6IndexScan_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，匹配前16位，查询应该得到masklen为16
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip6forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);

        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("lpm scan result:");
        for (int i = 0; i < 16; i++) {
            printf("%u ", rd_dest_ip_addr[i]);
        }
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(expect_mask_len, rd_mask_len);
        printf(" masklen:%d\n", rd_mask_len);
    }

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *thread_PK_read_lpm6_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";

    for (uint8_t loop = 1; loop <= oper_nums; loop++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(GMERR_OK, ret);

        // 2021/12/01 直连读需要先fetch再获取数据
        bool isFinish = false;
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        unsigned int rd_vr_id;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "vr_id", &rd_vr_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        unsigned int rd_vrf_index;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "vrf_index", &rd_vrf_index, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        uint32_t rd_dest_ip_addr[4];
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(16, valueSize);

        uint8_t rd_mask_len;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);

        unsigned char rd_nhp_group_flag;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "nhp_group_flag", &rd_nhp_group_flag, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(wr_uint8, rd_nhp_group_flag);
        EXPECT_EQ(1, valueSize);

        unsigned short rd_uint16;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "qos_profile_id", &rd_uint16, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, valueSize);

        uint32_t rd_primary_label;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "primary_label", &rd_primary_label, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        uint32_t rd_attribute_id;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "attribute_id", &rd_attribute_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        uint32_t rd_nhp_group_id;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "nhp_group_id", &rd_nhp_group_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        unsigned int rd_path_flags;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "path_flags", &rd_path_flags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        unsigned int rd_flags;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "flags", &rd_flags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        unsigned char rd_status_high_prio;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "status_high_prio", &rd_status_high_prio, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);

        unsigned char rd_status_normal_prio;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "status_normal_prio", &rd_status_normal_prio, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);

        unsigned int rd_app_source_id;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "app_source_id", &rd_app_source_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        unsigned int rd_app_version;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "app_version", &rd_app_version, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);

        unsigned long long rd_app_obj_id;
        ret = queryFieldValueAndCompare(g_stmt_tht[conn_id], "app_obj_id", &rd_app_obj_id, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(wr_uint8, rd_app_obj_id);
        EXPECT_EQ(8, valueSize);

        // char *rd_svc_ctx_high_prio_fixed = (char *)malloc(sizeof(char) * 36);
        char rd_svc_ctx_high_prio_fixed[36] = {0};
        ret =
            queryFieldValueAndCompare(g_stmt_tht[conn_id], "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(36, valueSize);
    }

    GmcFreeIndexKey(g_stmt_tht[conn_id]);
    // GmcFreeStmt(g_stmt_tht[conn_id]);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

TEST_F(Lpm6Index_test, DML_049_LPM6_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertexLabel
    char *schema_json = NULL;
    char *configJson = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1
        })";

    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";
    readJanssonFile("./scheme_file_02/ip4forward.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    readJanssonFile("./scheme_file_02/ip4forward.gmconfig", &configJson);
    ASSERT_NE((void *)NULL, configJson);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    free(configJson);

    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6_new(stmt, labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6_new(conn, labelName, "primary_key", 1, oper_nums, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 并发场景，T1：主键读数据，T2：lpm6索引扫描数据；
    pthread_t thr_arr[2];
    void *thr_ret[2];
    int index[2] = {0, 1};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    res = pthread_create(&thr_arr[0], NULL, thread_PK_read_lpm6_test, (void *)&index[0]);
    EXPECT_EQ(GMERR_OK, res);
    res = pthread_create(&thr_arr[1], NULL, thread_Lpm6IndexScan_test, (void *)&index[1]);
    EXPECT_EQ(GMERR_OK, res);

    pthread_join(thr_arr[0], &thr_ret[0]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 064 同步建表，主键修改一张表的记录，唯一lpm6索引多线程扫描同张表数据  【验收用例场景】
 Notes        : 主键更新的数据和另一线程扫描的数据冲突;T1：主键更新数据，T2：lpm6索引扫描数据;
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.05.18]
*****************************************************************************/
void *thread_Lpm6IndexScan_test02(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"，
    // 更新数据前匹配16位，查询应该得到ip为wr_fixed,masklen为16
    // 更新数据后匹配128位，查询应该得到ip为wr_fixed_qury,masklen为128
    int ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t expect_mask_len = 16;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &expect_mask_len, sizeof(expect_mask_len));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip6forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);

        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("lpm scan result:");
        for (int i = 0; i < 16; i++) {
            printf("%u ", rd_dest_ip_addr[i]);
        }
        // 更新前,查询到的数据应该为：rd_dest_ip_addr[2] == '0x91', rd_dest_ip_addr[15] == '0x20',masklen= 16
        uint32_t tmp = 0x91;
        uint32_t tmp_02 = 0x11;
        if (rd_dest_ip_addr[2] == tmp) {
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
            }
            // check masklen
            uint8_t rd_mask_len;
            ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            // 更新中，查询到的数据应该为：rd_dest_ip_addr[2] == '0x91', rd_dest_ip_addr[15] == '0x20',masklen= 15
            uint8_t mask_len_15 = 15;
            if (rd_mask_len == expect_mask_len) {
                EXPECT_EQ(expect_mask_len, rd_mask_len);
            } else if (rd_mask_len == mask_len_15) {
                EXPECT_EQ(mask_len_15, rd_mask_len);
            }
            printf(" masklen:%d\n", rd_mask_len);
        }
        // 更新后,查询到的数据应该为：rd_dest_ip_addr[2] == '0x11', rd_dest_ip_addr[15] == '0x21',masklen= 128
        else if (rd_dest_ip_addr[2] == tmp_02) {
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(wr_fixed_qury[i], rd_dest_ip_addr[i]);
            }
            // check masklen
            uint8_t rd_mask_len;
            ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            EXPECT_EQ(oper_nums, rd_mask_len);
            printf(" masklen:%d\n", rd_mask_len);
        }
    }

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *thread_PK_update_lpm6_test02(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";

    // before update
    int ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t oper_nums_16 = 16;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &oper_nums_16, sizeof(oper_nums_16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    bool isFinish = false;
    ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(g_stmt_tht[conn_id], "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--- before update --- dest_ip_addr:");
    for (int i = 0; i < 16; i++) {
        printf("%u  ", rd_dest_ip_addr[i]);
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(g_stmt_tht[conn_id], "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    printf("  masklen: %d\n", rd_mask_len);
    EXPECT_EQ(oper_nums_16, rd_mask_len);

    // pk 更新，更新第16条数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &oper_nums_16, sizeof(oper_nums_16));
    EXPECT_EQ(GMERR_OK, ret);

    // 更新 dest_ip_addr字段为 wr_fixed_qury[16] = {0xcd, 0xcd, 0x11, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11,
    // 0x11, 0x39, 0x00, 0x20, 0x21},masklen为128； 更新的此条数据是和另一线程的查询冲突的数据 2021/11/24 主键不支持更新
    // ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    int affectRows = 0;
    uint32_t len = 0;
    ret = GmcGetStmtAttr(g_stmt_tht[conn_id], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n[INFO] [ pk update ] affect row: %d \n\n", affectRows);

    // check update
    // ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], labelName, GMC_OPERATION_SCAN);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    // EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "primary_key");
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcExecute(g_stmt_tht[conn_id]);
    // EXPECT_EQ(GMERR_OK, ret);

    // rd_dest_ip_addr[16] = {0};
    // ret = GmcGetVertexPropertySizeByName(g_stmt_tht[conn_id], "dest_ip_addr", &valueSize);
    // EXPECT_EQ(GMERR_OK,ret);
    // ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    // EXPECT_EQ(GMERR_OK, ret);
    // printf("--- after update --- dest_ip_addr:");
    // for(int i = 0;i< 16;i++){
    // 	printf("%u  ", rd_dest_ip_addr[i]);
    // 	EXPECT_EQ(wr_fixed_qury[i], rd_dest_ip_addr[i]);
    // }

    // rd_mask_len = 0;
    // ret = GmcGetVertexPropertySizeByName(g_stmt_tht[conn_id], "mask_len", &valueSize);
    // EXPECT_EQ(GMERR_OK,ret);
    // ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, valueSize, &isNull);
    // EXPECT_EQ(GMERR_OK, ret);
    // printf("  masklen: %d\n", rd_mask_len);
    // EXPECT_EQ(oper_nums, rd_mask_len);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

TEST_F(Lpm6Index_test, DML_049_LPM6_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertexLabel
    char *schema_json = NULL;
    char *configJson = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1
        })";

    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";
    readJanssonFile("./scheme_file_02/ip4forward.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    readJanssonFile("./scheme_file_02/ip4forward.gmconfig", &configJson);
    ASSERT_NE((void *)NULL, configJson);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    free(configJson);

    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6_new(stmt, labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6_new(conn, labelName, "primary_key", 1, oper_nums, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t thr_arr[4];
    void *thr_ret[4];
    int index[4] = {0, 1, 2, 3};

    res = pthread_create(&thr_arr[0], NULL, thread_PK_update_lpm6_test02, (void *)&index[0]);
    EXPECT_EQ(GMERR_OK, res);
    // sleep(1);
    res = pthread_create(&thr_arr[1], NULL, thread_Lpm6IndexScan_test02, (void *)&index[1]);
    EXPECT_EQ(GMERR_OK, res);
    res = pthread_create(&thr_arr[2], NULL, thread_Lpm6IndexScan_test02, (void *)&index[2]);
    EXPECT_EQ(GMERR_OK, res);
    res = pthread_create(&thr_arr[3], NULL, thread_Lpm6IndexScan_test02, (void *)&index[3]);
    EXPECT_EQ(GMERR_OK, res);

    pthread_join(thr_arr[0], &thr_ret[0]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[2], &thr_ret[2]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[3], &thr_ret[3]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 065
replace非冲突数据，其中lpm6索引引用的字段vr_id、vrf_index在数据中已存在，dest_ip_addr与mask_len的组合在数据库中不存在，预期成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/

TEST_F(Lpm6Index_test, DML_049_LPM6_065)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    // 1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据中已存在，dest_ip_addr与mask_len的组合在数据库中不存在，预期成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // pk 替换第128条数据
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新,但是支持replace（对齐v3）
    // 只replace lpm6 index引用的字段
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, affectRows);

    // 主键读
    printf("\n========== replace one vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "replace one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 使用主键check replace的数据，lpm6索引引用的字段
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 066
replace为冲突数据，其中lpm6索引引用的字段vr_id、vrf_index在数据中不存在，预期失败；【迭代五】预期成功； Notes        :
【迭代五】修改为：vr_id、vrf_index在一个索引表中可以不同，vr_id、vrf_index、dest_ip_addr与mask_len四个字段组合一定是唯一的，适配用例；
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/

TEST_F(Lpm6Index_test, DML_049_LPM6_066)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // replace 1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据不存在，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // pk 替换第128条数据
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新，但是支持replace（对齐v3）
    // 只replace lpm6 index引用的字段
    // lpm6 index
    unsigned int wr_unit32_update = 2;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_update, sizeof(wr_unit32_update));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_update, sizeof(wr_unit32_update));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引，replace数据，affect row为2
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, affectRows);

    // 使用主键check replace的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32_update, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32_update, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed_update[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 067
replace为冲突数据，其中lpm6索引引用的字段dest_ip_addr与mask_len的组合和索引表中的数据冲突，预期失败； Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_067)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // replace 1条数据，其中lpm6索引引用的字段dest_ip_addr与mask_len的组合和索引表中的数据冲突，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // pk 替换第128条数据
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新，但是支持replace（对齐v3）
    // 只replace lpm6 index引用的字段
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    uint8_t oper_nums_tmp = 16;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums_tmp, sizeof(oper_nums_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // 使用主键check replace的数据，replace失败，预期replace的那条数据没有变化
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_unit32, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    // drop
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 068 与namespace交互，不同namespace下创建lpm6索引；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/

TEST_F(Lpm6Index_test, DML_049_LPM6_068)
{
    // 创建两个namespace
    const char *namespace_01 = (const char *)"namespace01";
    ret = GmcDropNamespace(stmt, namespace_01);
    ret = GmcCreateNamespace(stmt, namespace_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *namespace_02 = (const char *)"namespace02";
    ret = GmcDropNamespace(stmt, namespace_02);
    ret = GmcCreateNamespace(stmt, namespace_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 两个namespace下create 同名 vertexLabel
    char *schema_json = NULL;

    ret = GmcUseNamespace(stmt, namespace_01);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    char *schema_json_02 = NULL;
    ret = GmcUseNamespace(stmt, namespace_02);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json_02);
    ASSERT_NE((void *)NULL, schema_json_02);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json_02, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json_02);

    // open namespace01 里的 vertexLabel
    ret = GmcUseNamespace(stmt, namespace_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    // ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, 1);
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    // 主键读
	int isPrint = 0;
	printf("\n==========namespace01 insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(conn, g_labelName, "primary_key", 1, oper_nums , GMC_DATATYPE_UINT8,  conn_id, "insert vertex", isPrint);
	EXPECT_EQ(GMERR_OK, ret);
#endif
    // open namespace02 里的 vertexLabel
    // GmcStmtT *stmt_tmp;
    // ret = GmcAllocStmt(conn, &stmt_tmp);
    // EXPECT_EQ(GMERR_OK,ret);

    ret = GmcUseNamespace(stmt, namespace_02);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    // ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, 1);
    EXPECT_EQ(GMERR_OK, ret);
#if 0
    // 主键读
	printf("\n==========namespace02 insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(conn, g_labelName, "primary_key", 1, oper_nums , GMC_DATATYPE_UINT8,  conn_id, "insert vertex", isPrint);
	EXPECT_EQ(GMERR_OK, ret);

#endif
    // drop
    ret = GmcUseNamespace(stmt, namespace_01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, namespace_02);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropNamespace(stmt, namespace_01);
    GmcDropNamespace(stmt, namespace_02);
}
/*****************************************************************************
 Description  : 069
lpm6索引字段nullable为true，已与开发确认，与主键处理逻辑保持相同，nullable属性底层会处理成nullable=false，建表不会报错；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/

TEST_F(Lpm6Index_test, DML_049_LPM6_069)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_nullable_true_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 070 lpm6索引字段nullable为flase，预期建表成功；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/

TEST_F(Lpm6Index_test, DML_049_LPM6_070)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_nullable_false_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 071 插入1条数据，其中lpm6索引引用的字段不设置，预期失败；
 Notes        : lpm6索引字段不可为空校验
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_071)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 插入1条数据，其中lpm6索引引用的字段不设置，预期失败；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
#if 0
/*****************************************************************************
 Description  : 072 更新1条数据，其中lpm6索引引用的字段更新为NULL，预期失败；
 Notes        : lpm6索引字段不可为空校验,不支持update为null字段，本用例执行的更新其实是随机分配的
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07] 
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_072)
{
    // create vertexLabel
	char *schema_json = NULL;
    
	readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
	ASSERT_NE((void *)NULL, schema_json);
	ret = GmcDropVertexLabel(stmt, g_labelName);
	ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
	ASSERT_EQ(GMERR_OK, ret);	
	free(schema_json);

	// 写入数据,总共写入128条数据；
	// ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
	ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
	EXPECT_EQ(GMERR_OK, ret);

    // 主键读
	int isPrint = 0;
	printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(conn, g_labelName, "primary_key", 1, oper_nums , GMC_DATATYPE_UINT8,  conn_id, "insert vertex", isPrint);
	EXPECT_EQ(GMERR_OK, ret);

    // 根据pk更新
	ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
	EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
	EXPECT_EQ(GMERR_OK, ret);
	
	// lpm6 index
	unsigned int wr_unit32_null;
	ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_null, sizeof(wr_unit32_null));
	EXPECT_EQ(GMERR_OK, ret);
	// lpm6 index
	ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_null, sizeof(wr_unit32_null));
	EXPECT_EQ(GMERR_OK, ret);
	// lpm6 index
	uint8_t wr_fixed_null[16];
	ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_null, 16);
	EXPECT_EQ(GMERR_OK, ret);
	// lpm6 index 
	uint8_t nums_null;
	ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &nums_null, sizeof(nums_null));
	EXPECT_EQ(GMERR_OK, ret);

	ret = GmcSetIndexKeyName(stmt, "primary_key");
	EXPECT_EQ(GMERR_OK, ret);
	ret = GmcExecute(stmt);
	EXPECT_EQ(GMERR_SYNTAX_ERROR,ret);
	res=testGmcGetLastError(NULL);
	EXPECT_EQ(GMERR_OK, res);

	// get affect row，pk唯一索引
	ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
	EXPECT_EQ(GMERR_OK, ret);
	EXPECT_EQ(0, affectRows);
	printf("\n========== pk index update num: %d ===============\n", affectRows);

	// 使用主键check更新数据，lpm6索引引用的字段,预期未更新
	ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
	EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
	EXPECT_EQ(GMERR_OK, ret);
	ret = GmcSetIndexKeyName(stmt, "primary_key");
	EXPECT_EQ(GMERR_OK, ret);
	ret = GmcExecute(stmt);
	EXPECT_EQ(GMERR_OK, ret);

	uint32_t rd_vr_id;
	ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK,ret);   
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
	EXPECT_EQ(0, rd_vr_id);
	printf("\n---rd_vr_id:%d----\n", rd_vr_id);

	uint32_t rd_vrf_index;
	ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK,ret);   
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
	EXPECT_EQ(0, rd_vrf_index);
	printf("\n---rd_vrf_index:%d----\n", rd_vrf_index);

	uint8_t rd_dest_ip_addr[16];
	ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK,ret);   
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
	printf("\n");
	for(int i = 0;i< 16;i++){
		EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
		printf("---wr_fixed[i]:%d, rd_dest_ip_addr[i]:%d----\n", wr_fixed[i], rd_dest_ip_addr[i]);
		
	}
	printf("\n");
	
	uint8_t rd_mask_len;
	ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK,ret);   
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
	EXPECT_EQ(oper_nums, rd_mask_len);
	printf("\n---rd_mask_len:%d----\n", rd_mask_len);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);	
}
#endif
/*****************************************************************************
 Description  : 073 replace 1条数据，其中lpm6索引引用的字段替换为为NULL，预期失败；
 Notes        : lpm6索引字段不可为空校验，replace部分字段，replace的属性为最新值，未replace的属性为NLL
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_073)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // replace 部分字段，不replace lpm6索引的四个字段
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char wr_uint8_replace = '2';
    ret = GmcSetVertexProperty(
        stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8_replace, sizeof(wr_uint8_replace));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(
        stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8_replace, sizeof(wr_uint8_replace));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n==========  replace num: %d ===============\n", affectRows);

    // 使用主键check替换的数据，lpm6索引引用的字段,预期未更新
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2021/12/01 直连读需要先fetch再获取数据
    isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t rd_vr_id;
    ret = GmcGetVertexPropertySizeByName(stmt, "vr_id", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_vr_id, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, rd_vr_id);

    uint32_t rd_vrf_index;
    ret = GmcGetVertexPropertySizeByName(stmt, "vrf_index", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, rd_vrf_index);

    uint8_t rd_dest_ip_addr[16];
    ret = GmcGetVertexPropertySizeByName(stmt, "dest_ip_addr", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 16; i++) {
        EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
    }

    uint8_t rd_mask_len;
    ret = GmcGetVertexPropertySizeByName(stmt, "mask_len", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, rd_mask_len);

    uint8_t rd_errcode_high_prio;
    ret = GmcGetVertexPropertySizeByName(stmt, "errcode_high_prio", &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "errcode_high_prio", &rd_errcode_high_prio, valueSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(wr_uint8, rd_errcode_high_prio);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 074
增量查询验证最长匹配：插入10条数据，根据lpm6索引查询；再次插入1条数据，查询验证是否是增量查询最长匹配；删除该条数据，再次查询；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_074)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入10条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...10,总共10条数据；
    int nums = 10;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // wr_fixed[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    // 0xcdcd91:11001101 11001101 10010001
    // wr_fixed_qury[16] = {0xcd, 0xcd, 0x11, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x21}; 0xcdcd11:11001101 11001101 00010001 wr_fixed_update[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98,
    // 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    // 1
    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020",查询应该得到索引表中的最后一条数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(nums, rd_mask_len);
    }

    // 2
    // 插入1条数据,ip为"0xcdcd110a222254988475111139002020", mask_len为16；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    uint8_t masklen_update = 16;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_update, sizeof(masklen_update));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020",查询应该得到索引表中新插入的那条数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed_qury[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(masklen_update, rd_mask_len);
    }
    GmcResetStmt(stmt);
    // 3
    // 删除新插入的那条数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020",查询应该得到未插入数据前索引表的最后一条数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(nums, rd_mask_len);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 075 根据lpm6索引查询,查询的vr_id和vrf_index不存在，预期报错；
 Notes        :
 History      :
 Author       : jiangshan/j30011431
 Modification : [2021.07.07]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_075)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入10条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...10,总共10条数据；
    int nums = 10;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 通过lpm6索引查询 "0xcdcd110a222254988475111139002020"
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t wr_unit32_query = 222;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32_query, sizeof(wr_unit32_query));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32_query, sizeof(wr_unit32_query));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_qury, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 076
lpm6索引查询，所要查询的ip的masklen为0，在索引表中没有匹配的数据，验证最长前缀匹配,预期无数据(改为找到最长匹配数据为128)；
 Notes : 1.允许masklen为0 2.可以支持返回默认路由，最长前缀匹配没找到，就去找默认路由，如果存在默认路由，会返回默认路由
 History      :
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14] [2021.8.19]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_076)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据,ip为"0xcdcd910a222254988475111139002021", mask_len为128；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    // wr_fixed[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    // wr_fixed_qury[16] = {0xcd, 0xcd, 0x11, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x21}; wr_fixed_update[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00,
    // 0x20, 0x21};

    // 1
    // mask_len为0，通过lpm6索引查询 "0xcdcd910a222254988475111139002020",匹配0位，查询应该无数据
    uint8_t mask_zero = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        for (int i = 0; i < 16; i++) {
            printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        printf("masklen is:%d\n", rd_mask_len);
        EXPECT_EQ(false, isNull);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 077
lpm6索引查询，所要查询的ip的masklen为0，在索引表中有匹配的数据，验证最长前缀匹配,预期匹配成功(改为找到最长匹配数据，长度为128)；
 Notes        : 允许masklen为0
 History      :
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_077)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入1条数据,ip为"0xcdcd910a222254988475111139002021", mask_len为128；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    // wr_fixed[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    // wr_fixed_qury[16] = {0xcd, 0xcd, 0x11, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x21}; wr_fixed_update[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00,
    // 0x20, 0x21};

    // 1
    // mask_len为0，通过lpm6索引查询 "0xcdcd910a222254988475111139002020",匹配0位，查询应该无数据
    uint8_t mask_zero = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
            printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("mask_len:%d\n", rd_mask_len);
        EXPECT_EQ(128, rd_mask_len);  //最新应该找到最长匹配数据
    }
    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 078 first use the key of index which is "lpmv6" to lookup the data,the data's masklen was seted to
0,insert a new data and the old one ip&masklen is same as the new data.expect failed because of 'insert duplicated lpm
keys'; Notes        : masklen is allowed  set  0 History      : 创建lpm6索引表，插入不同vr_id、vrf_index组合,
mask=0/mask>0的数据；新插入masklen和ip与表中存在的数据冲突，预期插入失败 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_078)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 已经写入索引表的数据是：ip为"0xcdcd910a222254988475111139002020", mask_len从0、2、3...128,总共129条数据；
    // 再次写入：ip为"0xcdcd910a222254988475111139002021", mask_len为0
    // 此时索引表中的
    // ip:0xcdcd910a222254988475111139002020,masklen:0和ip:0xcdcd910a222254988475111139002021,masklen:0是冲突的，预期写入失败
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    uint8_t mask_zero = 0;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 079 lpm6索引查询,masklen设置为任意数值不影响使用GetVertexCount接口查询，可以查到数据；
 Notes        : 允许masklen为0
 History      : [2021.08.14]后两个字段不设置查询过滤条件
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_079)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums + 1);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    // 插入129条数据，全表查询，预期129
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d\n", result);
    ASSERT_EQ(129, result);

    // lpm6索引查询masklen为0，预期129条
    const char *KeyName = "lpm6_key";
    uint8_t mask_zero = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    printf("0:%d\n", result);
    ASSERT_EQ(129, result);

    // lpm6索引查询，预期129条
    mask_zero = 128;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    printf("1:%d\n", result);
    ASSERT_EQ(129, result);

    // lpm6索引查询，输入不存在的vrid和vrfindex,不报错，查询结果为0
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, result);

    // 插入1条数据，其中lpm6索引引用的字段vr_id、vrf_index在数据库中不存在、dest_ip_addr与mask_len在数据库中已存在，预期成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n========== insert num: %d ===============\n", affectRows);

    // 主键读，插入成功，从原来的129条数据变成130条，所以此处用loop_2
    printf("\n========== insert one vertex, pk read, expect: %d ===============\n", loop_2 + 1);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, loop_2, GMC_DATATYPE_UINT8, conn_id, "insert one vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6索引查询，输入最新插入的vrid和vrfindex,预期1
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, result);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 080 lpm6索引查询,使用GetVertexCount接口查询，不设置后两个字段来查询，可以查到数据；
 Notes        : 允许masklen为0
 History      : [2021.08.14]DTS2021081416068
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_080)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", oper_nums + 1);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    // 插入129条数据，全表查询，预期129
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%d\n", result);
    EXPECT_EQ(129, result);

    // lpm6索引查询masklen为0，预期129条
    const char *KeyName = "lpm6_key";
    uint8_t mask_zero = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);

    result = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(129, result);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 081 含lpmv6索引和主键的表，其中masklen=0,主键对其值删除、查询，以及使用GetVertexCount接口查询
 Notes        : 允许masklen为0
 History      : [2021.08.16]
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_081)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", 4);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, 3, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    uint8_t pri_delete = 0;
    //删除masklen=0的数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &pri_delete, sizeof(pri_delete));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &pri_delete, sizeof(pri_delete));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t mask_zero = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
    }
    // couldn't scan the data which masklen is 0,because it was deleted
    const char *KeyName = "lpm6_key";
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);
    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 082
含lpmv6索引和主键的表，其中masklen=0,主键对其值更新、使用新值、旧值分别查询，以及使用GetVertexCount接口查询 Notes :
允许masklen为0 History      : [2021.08.16] Author       : yangningwei/ywx1037054 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_082)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    const char *KeyName = "lpm6_key";
    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", 4);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, 3, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    uint8_t pri_update = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &pri_update, sizeof(pri_update));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &pri_update, sizeof(pri_update));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t masklen_update = 12;  //变
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_update, sizeof(masklen_update));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    uint8_t mask_zero = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新失败,lpm索引查询找到的最长匹配依然是3
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(3, rd_mask_len);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 4);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &masklen_update, sizeof(masklen_update));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新失败,lpm索引查询找到的最长匹配依然是3
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(3, rd_mask_len);
    }
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 4);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 083
含lpmv6索引和主键的表，其中masklen=0,主键对其值替换、使用新值、旧值分别查询，以及使用GetVertexCount接口查询 Notes :
允许masklen为0 History      : [2021.08.16] Author       : yangningwei/ywx1037054 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_083)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", 4);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, 3, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    uint8_t pri_upbefore = 0;
    uint8_t pri_upafter = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &pri_upafter, sizeof(pri_upafter));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &pri_upafter, sizeof(pri_upafter));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新，但是支持replace（对齐v3）
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t masklen_replace = 12;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_replace, sizeof(masklen_replace));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //
    uint8_t mask_zero = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新成功,lpm索引查询找到的最长匹配是12
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(12, rd_mask_len);
    }
    const char *KeyName = "lpm6_key";
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 4);
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 4);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &masklen_replace, sizeof(masklen_replace));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新成功,lpm索引查询找到的最长匹配是12
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(12, rd_mask_len);
    }
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 4);
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 4);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 084
含lpmv6索引和主键的表，其中masklen不等于0,主键对其值更新为masklen=0、使用新值、旧值分别查询，以及使用GetVertexCount接口查询
 Notes        : 允许masklen为0
 History      : [2021.08.19]最新masklen应该匹配为3
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_084)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    const char *KeyName = "lpm6_key";
    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, 3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", 3);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, 3, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    uint8_t pri_update = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &pri_update, sizeof(pri_update));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &pri_update, sizeof(pri_update));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t masklen_update = 0;  //变
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_update, sizeof(masklen_update));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    //
    uint8_t mask_one = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_one, sizeof(mask_one));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新失败,lpm索引查询找到的最长匹配依然是3
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(3, rd_mask_len);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &masklen_update, sizeof(masklen_update));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新失败,lpm索引查询找到的最长匹配依然是3
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(3, rd_mask_len);  //最新应该为匹配到长度为3
    }
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 085
含lpmv6索引和主键的表，其中masklen不等于0,主键对其值替换为masklen=0、使用新值、旧值分别查询，以及使用GetVertexCount接口查询
 Notes        : 允许masklen为0
 History      : [2021.08.19]最新masklen应该匹配为3
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_085)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, 3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", 4);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, 3, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    uint8_t pri_upbefore = 1;
    uint8_t pri_upafter = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &pri_upafter, sizeof(pri_upafter));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &pri_upafter, sizeof(pri_upafter));
    EXPECT_EQ(GMERR_OK, ret);
    // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新，但是支持replace（对齐v3）
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t masklen_replace = 0;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_replace, sizeof(masklen_replace));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //
    uint8_t mask_one = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_one, sizeof(mask_one));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新成功,lpm索引查询找到的最长匹配依然是3
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(3, rd_mask_len);
    }
    const char *KeyName = "lpm6_key";
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &masklen_replace, sizeof(masklen_replace));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);  //更新成功,lpm索引查询找到的最长匹配依然是3
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(3, rd_mask_len);  //最新应该为最长匹配
    }
    ret = GmcGetVertexCount(stmt, g_labelName, KeyName, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 3);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 086 创建lpmv6索引表，插入不同vr_id、vrf_index组合,
mask=0/mask>0的数据；设置Lpm索引字段，其中vr_id、vrf_index表中有匹配，ip表中没有匹配，mask_len=0，查询； Notes        :
允许masklen为0 History      : [2021.08.16] Author       : yangningwei/ywx1037054 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_086)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    unsigned int wr_unit32_tmp = 1;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 3);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== insert vertex num: %d, pk read ===============\n", 4);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, 3, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t result;
    uint8_t wr_fixed2[16] = {
        0xcd, 0xab, 0xee, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    uint8_t mask_zero = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed2, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("mask_len:%d\n", rd_mask_len);
    }
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 087 创建lpm6索引表，插入不同vr_id、vrf_index组合,
,mask=0/mask>0的数据；设置Lpm索引字段，其中vr_id、vrf_index表中没有匹配，ip表中没有匹配，mask_len=0，查询 Notes        :
允许masklen为0 History      : [2021.08.16] Author       : yangningwei/ywx1037054 Modification : [2021.08.16]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_087)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    uint32_t oper_new = 10;
    // 写入数据,总共写入10条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...9,10总共10条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_new);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_new);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, oper_new, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t mask_new = 0;
    uint32_t vr_new = 11;
    uint8_t wr_fixed2[16] = {
        0xcd, 0xab, 0xee, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    // 插入1条数据,ip为"0xcdcd910a222254988475111139002021", mask_len为128；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_new, sizeof(vr_new));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vr_new, sizeof(vr_new));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed2, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_new, sizeof(mask_new));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    // 初始插入数据时，主键从1-128，主键唯一，所以这里插入129
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_new, sizeof(vr_new));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vr_new, sizeof(vr_new));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_new, sizeof(mask_new));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
            EXPECT_EQ(wr_fixed2[i], rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        EXPECT_EQ(0, rd_mask_len);
    }

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 088 1个进程进行DmL(插入，删除，更新)操作，1个进程不断的进行lpm索引查询，预期数据库服务正常，
DDL/DML操作成功 Notes        : 允许masklen为0 History      : [2021.08.16] Author       : yangningwei/ywx1037054
 Modification : [2021.08.16]
*****************************************************************************/
void *ThreadStartClient1(void *args)
{
    int ret = system("./Lpm6Index_test --gtest_also_run_disabled_tests "
                     "--gtest_filter=Lpm6Index_test.DISABLED_Client1 >Client1.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
//1个进程进行DmL(插入，删除，更新)操作
TEST_F(Lpm6Index_test, DISABLED_Client1)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client1 start.");
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    ret = testGmcConnect(&conn_t, &stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写入数据
    int isPrint = 0;
    ret = test_insert_vertex_lpm6(stmt_t, g_labelName, 0, 80);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 20; i++) {
        uint8_t priDelete = i;
        // 删除masklen=0的数据
        ret = testGmcPrepareStmtByLabelName(stmt_t, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT8, &priDelete, sizeof(priDelete));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 1, GMC_DATATYPE_UINT8, &priDelete, sizeof(priDelete));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "primary_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 20; i < 40; i++) {
        uint8_t priUpdate = i;
        // 更新masklen=0的数据，不支持update lpm索引字段
        ret = testGmcPrepareStmtByLabelName(stmt_t, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT8, &priUpdate, sizeof(priUpdate));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_t, 1, GMC_DATATYPE_UINT8, &priUpdate, sizeof(priUpdate));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t masklenReplace = i + 80;
        ret = GmcSetVertexProperty(stmt_t, "mask_len", GMC_DATATYPE_UINT8, &masklenReplace, sizeof(masklenReplace));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "primary_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    for (int i = 40; i < 60; i++) {
        uint8_t priUpdate = i;
        uint8_t priUpafter = i + 40;
        // 更新masklen=0的数据
        ret = testGmcPrepareStmtByLabelName(stmt_t, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "primary_label", GMC_DATATYPE_UINT8, &priUpafter, sizeof(priUpafter));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "attribute_id", GMC_DATATYPE_UINT8, &priUpafter, sizeof(priUpafter));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_t, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_02, 16);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t masklenReplace = i + 40;
        ret = GmcSetVertexProperty(stmt_t, "mask_len", GMC_DATATYPE_UINT8, &masklenReplace, sizeof(masklenReplace));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
}
//088 1个进程进行DmL(插入，删除，更新)操作，1个进程不断的进行lpm索引查询，预期数据库服务正常，DDL/DML操作成功
TEST_F(Lpm6Index_test, DML_049_LPM6_088)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);
    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartClient1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < 1000; i++) {
            uint8_t mask_zero = 0;
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
            EXPECT_EQ(GMERR_OK, ret);

            // qry vertex
            ret = GmcSetIndexKeyName(stmt, "lpm6_key");
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            for (unsigned int cnt = 0;; ++cnt) {
                bool isFinish;
                ret = GmcFetch(stmt, &isFinish);  //旧值扫不到内容
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                uint8_t rd_dest_ip_addr[16];
                ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(0, isNull);
                for (int i = 0; i < 16; i++) {
                    printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
                }
                // check masklen
                uint8_t rd_mask_len;
                ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(0, isNull);
                // printf("mask_len:%d\n",rd_mask_len);
                EXPECT_LE(rd_mask_len, 80);
            }
        }
    // 等待子进程结束
    ret = pthread_join(thr_arr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 089
3个线程进行DmL(插入，删除，更新,替换)操作，2个线程不断的进行lpmv6索引查询，dml统计视图查询，预期数据库服务正常，
DDL/DML操作成功；线程结束完，主线程查询，可以有预期信息； Notes        : 允许masklen为0 History      : [2021.08.16]
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.16]
*****************************************************************************/
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
void *lpm6thread_001(void *args)
{
    int conn_id = *((int *)args);
    if (conn_id == 0) {
        for (int i = 0; i < 30; i++) {
            uint8_t pri_delete = i;
            //删除masklen=0的数据
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &pri_delete, sizeof(pri_delete));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &pri_delete, sizeof(pri_delete));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (conn_id == 1) {
        for (int j = 0; j < 10; j++) {
            uint8_t pri_update = j + 30;
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &pri_update, sizeof(pri_update));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &pri_update, sizeof(pri_update));
            EXPECT_EQ(GMERR_OK, ret);

            uint8_t masklen_update = j + 90;  //变
            // 2021/12/02 lpm索引增加约束：不支持lpm索引字段更新
            // ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_update,
            // sizeof(masklen_update)); EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "primary_key");
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (conn_id == 2) {
        for (int k = 0; k < 10; k++) {
            uint8_t pri_upbefore = 50 + k;
            uint8_t pri_upafter = 101 + k;
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &pri_upafter, sizeof(pri_upafter));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &pri_upafter, sizeof(pri_upafter));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
            EXPECT_EQ(GMERR_OK, ret);
            uint8_t masklen_replace = k + 101;
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &masklen_replace, sizeof(masklen_replace));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (conn_id == 3) {
        for (int i = 0; i < 10; i++) {
            uint8_t mask_zero = 0;
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_zero, sizeof(mask_zero));
            EXPECT_EQ(GMERR_OK, ret);

            // qry vertex
            ret = GmcSetIndexKeyName(stmt, "lpm6_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            for (unsigned int cnt = 0;; ++cnt) {
                bool isFinish;
                ret = GmcFetch(stmt, &isFinish);  //
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                uint8_t rd_dest_ip_addr[16];
                ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(0, isNull);
                for (int i = 0; i < 16; i++) {
                    EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
                }
                // check masklen
                uint8_t rd_mask_len;
                ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(0, isNull);
                EXPECT_EQ(rd_mask_len, 110);
            }
        }
    }
    if (conn_id == 4) {
        for (int i = 0; i < 10; i++) {
            char const *Sys_name = "V\\$QRY_DML_OPER_STATIS";
            snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s\n", g_toolPath, g_userName,
                g_connServer, g_passwd, Sys_name);
            system(g_command);
            memset(g_command, 0, sizeof(g_command));
        }
    }
    return NULL;
}
TEST_F(Lpm6Index_test, DML_049_LPM6_089)
{
    GmcConnT *s_conn[MAX_CONN_SIZE];
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 80);
    EXPECT_EQ(GMERR_OK, ret);
    const int num_of_thread = 5;
    pthread_t tid[num_of_thread];
    GmcConnT *conn_t[num_of_thread] = {0};
    int index[num_of_thread] = {0};
    memset(s_conn, 0, sizeof(void *) * num_of_thread);
    for (int i = 0; i < num_of_thread; i++) {
        index[i] = i;
        pthread_create(&tid[i], NULL, lpm6thread_001, (void *)&index[i]);
        pthread_join(tid[i], NULL);
    }
    for (int i = 0; i < 10; i++) {
        uint8_t mask_scan = 128;
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_scan, sizeof(mask_scan));
        EXPECT_EQ(GMERR_OK, ret);

        // qry vertex
        ret = GmcSetIndexKeyName(stmt, "lpm6_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (unsigned int cnt = 0;; ++cnt) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);  //
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            uint8_t rd_dest_ip_addr[16];
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
            }
            // check masklen
            uint8_t rd_mask_len;
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            EXPECT_EQ(rd_mask_len, 110);
        }
    }
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 090 lpm6索引查询，所要查询的ip的masklen为0，在索引表中有匹配的数据，验证最长前缀匹配,预期匹配成功；
 Notes        : 允许masklen为0,默认路由检验
 History      : 0~128个相同的ip，maskelen不同，新建一个完全不同的ip,查找新ip，预期找到默认的
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_090)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 128);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读
    int isPrint = 0;
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    // wr_fixed[16] = {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    //最新查询："0x22, 0xc1, 0x84, 0x9a, 0x17, 0x66, 0x34, 0x91, 0x13, 0x56, 0xee, 0xff, 0x11, 0xed, 0xee, 0xff"
    // 1
    // mask_len为0，通过lpm6索引查询 "{0x22, 0xc1, 0x84, 0x9a, 0x17, 0x66, 0x34, 0x91, 0x13, 0x56, 0xee, 0xff, 0x11,
    // 0xed, 0xee, 0xff};",匹配0位，查询应该无数据
    uint8_t mask_diff = 128;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_diff, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_diff, sizeof(mask_diff));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
            printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("mask_len:%d\n", rd_mask_len);
        EXPECT_EQ(0, rd_mask_len);  //返回默认路由，即masklen为0的那条记录
    }
    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 091 lpm6索引查询，所要查询的ip的masklen为0，在索引表中有匹配的数据，验证最长前缀匹配,预期匹配成功；
 Notes        : 允许masklen为0,默认路由检验
 History      : 1个ip，maskelen为0，新建一个完全不同的ip,查找新ip，预期找到默认路由ip
 Author       : yangningwei/ywx1037054
 Modification : [2021.08.14]
*****************************************************************************/
TEST_F(Lpm6Index_test, DML_049_LPM6_091)
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = test_insert_vertex_lpm6(stmt, g_labelName, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 写入数据2；
    // ip为{0x08, 0x1c, 0x04, 0x10, 0xe1, 0x22, 0x1e, 0x27, 0xf1, 0xb1, 0xf0, 0x01, 0x77, 0x14, 0x11, 0x00};,
    // mask_len为100，总共1条数据；

    unsigned int wr_unit32_tmp = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wr_unit32_tmp, sizeof(wr_unit32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_diff2, 16);
    EXPECT_EQ(GMERR_OK, ret);
    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &oper_nums, sizeof(oper_nums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);
    // pk
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT8, &loop_2, sizeof(loop_2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed_tmp, 34);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row，pk唯一索引
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 1
    // mask_len为128，通过lpm6索引查询 "0x22, 0xc1, 0x84, 0x9a, 0x17, 0x66, 0x34, 0x91, 0x13, 0x56, 0xee, 0xff, 0x11,
    // 0xed, 0xee, 0xff"
    uint8_t mask_diff = 128;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_unit32, sizeof(wr_unit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, wr_fixed_diff, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_diff, sizeof(mask_diff));
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // check ip
        uint8_t rd_dest_ip_addr[16];
        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        for (int i = 0; i < 16; i++) {
            EXPECT_EQ(wr_fixed[i], rd_dest_ip_addr[i]);
            printf("rd_dest_ip_addr[%d]:%x\n", i, rd_dest_ip_addr[i]);
        }
        // check masklen
        uint8_t rd_mask_len;
        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isNull);
        printf("mask_len:%d\n", rd_mask_len);
        EXPECT_EQ(0, rd_mask_len);  //返回默认路由，即masklen为0的那条记录
    }
    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// DTS2024113023538问题单补充用例
TEST_F(Lpm6Index_test, DML_049_LPM6_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int conn_id = 0;
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test_05.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据1
    // ip 为 {0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20,
    // 0x20}，masklen从1-64
    uint8_t oper_nums_01 = 64;
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, oper_nums_01);
    EXPECT_EQ(GMERR_OK, ret);

    // local区间扫描所有数据
    uint8_t l_val[16] = {0xcc, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11,
        0x11, 0x39, 0x00, 0x20, 0x20};
    uint8_t r_val[16] = {0xce, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11,
        0x11, 0x39, 0x00, 0x20, 0x20};
    unsigned int arrLen = 1;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_FIXED;
    leftKeyProps_del[0].value = l_val;
    leftKeyProps_del[0].size = 16;

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_FIXED;
    rightKeyProps_del[0].value = r_val;
    rightKeyProps_del[0].size = 16;

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt_tht[conn_id], items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    // 在执行之前修改扫描的值，DTS2024113023538修改之前是扫描不到数据的，之后能扫描到数据
    l_val[0] = 0xce;
    r_val[0] = 0xcf;
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t count = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || isFinish == true) {
            break;
        }
        count++;
    }
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums_01, count);

    free(leftKeyProps_del);
    free(rightKeyProps_del);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
