/*****************************************************************************
 Description  : 用户管理异常测试
 Notes        :
 History      :
 Author       : 覃乾宝 qwx995465
 Modification :
 Date         : 2021/07/10
*****************************************************************************/
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
const char *userName;
int ret;
class UserManageAb : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 拉起server
        system("sh $TEST_HOME/tools/start.sh -f");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void UserManageAb::SetUp()
{
    printf("[INFO] UserManageAb Start.\n");
    AW_CHECK_LOG_BEGIN();
}

void UserManageAb::TearDown()
{
    AW_CHECK_LOG_END();
    printf("[INFO] UserManageAb End.\n");
}

/*******************************************************************************
Description : allow_list.cfg为空，gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_001)
{
    // 用户白名单配置user和group为空
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_001.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg缺少user_naem字段,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_002)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单缺少user_name字段
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_002.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_002.gmuser unsuccessful.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里user_naem字段值为空,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_003)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004005");
    // 用户白名单user_name字段值为空
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_003.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "mistake: Null value is not allowed. uname", "ret = 1004005");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里group_naem字段值为空,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_004)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单group_name字段值为空
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_004.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里process_naem字段值为空,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_005)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004005");
    // 用户白名单process_name字段值为空
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_005.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "005.gmuser unsuccessful. ret = 1004005");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里user_naem字段值为整数,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_006)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单user_name为整数
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_006.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    // int ret = executeCommand(g_command, "user", "is not string type.");
    int ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_006.gmuser unsuccessful.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里group_naem字段值为整数,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_007)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单group_name为整数
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_007.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_007.gmuser unsuccessful.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里process_naem字段值为整数,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_008)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单process_name为整数          // 注user和group的process_name为整数时，有不一样的结果
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_008.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_008.gmuser unsuccessful.");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里user_naem字段值为非法字符(分隔符),gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_009)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009000");
    // 用户白名单user_name为非法字符
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_009.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "009.gmuser unsuccessful. ret = 1009000");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里group_naem含有非法字符(分隔符),gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_010)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009000");
    // 用户白名单user_name为非法字符
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_010.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "010.gmuser unsuccessful. ret = 1009000");
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里process_naem含有非法字符(分隔符),gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_011)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009000");
    // 用户白名单user_name为非法字符
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *user_config = NULL;
    char *group_config = NULL;
    char const *user = "./allow_list/abnormal_user_011.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "create db users unsuccessful. mistake: Syntax unsucc. The process name : contains : .",
        "GMDBV5/test/sdv/testcases/10_Security/006_UserManagement/allow_list/"
        "abnormal_user_011.gmuser unsuccessful. ret = 1009000");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    char *user1_config = NULL;
    char *group1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_011_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_011_01.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : gmrule导入1024次allow_list.cfg
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_012)
{
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1005001");
    // 用户白名单重复导入128次
    char *group_config = NULL;
    char const *group = "./allow_list/abnormal_user_012.gmuser";
    snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, group, g_connServer);
    ret = executeCommand(g_command, "import allowlist, create db group. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 128; i++) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, group, g_connServer);
        printf("%s\n", g_command);
        ret = executeCommand(g_command, "import allowlist, create db group. success: 0, warning: 1.");
        EXPECT_EQ(GMERR_OK, ret);
    }
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, group, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : aallow_list.cfg只有user_name或group_name,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_013)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单只配置user_name或group_name
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_013.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_013.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_013_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_013_01.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里包含与superuser同名且proce相同的的普通group
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_014)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009000");
    // 用户白名单只配置user_name或group_name
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_014.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_014_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "014_01.gmuser unsuccessful. ret = 1009000");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里users、groups、process_name/group_name/user_name命名更改,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_015)
{
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单配置users、groups、process_name/group_name/user_name命名更改
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_015.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_015.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_015_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_015_01.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user2_config = NULL;
    char const *user2 = "./allow_list/abnormal_user_015_02.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user2, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_015_02.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user2, g_connServer);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : gmrule前几次导入异常的allow_list.cfg，最后导入正确的allow_list.cfg
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_016)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004005");
    // 前几次导入异常的allow_list.cfg
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_016.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "mistake: Null value is not allowed. processName", "ret = 1004005");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_016_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "abnormal_user_016_01.gmuser nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user2_config = NULL;
    char const *user2 = "./allow_list/abnormal_user_016_02.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user2, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user2, g_connServer);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里users中有group_name字段,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_017)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单配置users中有group_name字段
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_017.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_017.gmuser unsuccessful. ret = 1004006");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_017_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里groups中有user_name字段,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_018)
{
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // 用户白名单配置groups中有user_name字段
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_018.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_018.gmuser unsuccessful.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_018_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "invalid group or process field. parse group tuple unsuccessful. ret = 1004006",
        "abnormal_user_018_01.gmuser unsuccessful.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里user_name或group_name和process_name字段位置调换,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_019)
{
    // user_name或group_name和process_name字段位置调换
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_019.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    // 这里有了user_name和process_name，工具就认为这是一条有效的记录
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));
    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_019_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里user_name或group_name和process_name字段位置调换,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_020)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // user_name或group_name和process_name字段位置调换
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_020.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(
        g_command, "invalid user, process or reservedConnNum field. parse user tuple unsuccessful. ret = 1004006",
        "abnormal_user_020.gmuser unsuccessful.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_020_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : allow_list.cfg里缺少：,gmrule导入
*******************************************************************************/
TEST_F(UserManageAb, SEC_006_UserManagementAbnormalTest_021)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    // user_name或group_name和process_name字段位置调换
    char *user_config = NULL;
    char const *user = "./allow_list/abnormal_user_021.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "abnormal_user_021.gmuser nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    char *user1_config = NULL;
    char const *user1 = "./allow_list/abnormal_user_021_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user1, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "abnormal_user_021_01.gmuser nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    char *user2_config = NULL;
    char const *user2 = "./allow_list/abnormal_user_021_02.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user2, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(
        g_command, "abnormal_user_021_02.gmuser nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}
