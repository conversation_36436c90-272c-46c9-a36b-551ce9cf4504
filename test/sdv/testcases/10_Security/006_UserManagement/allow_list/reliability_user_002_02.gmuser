{"users": [{"user": "root1", "process": "st_client"}, {"user": "root2", "process": "st_common"}, {"user": "root3", "process": "st_datamodel"}, {"user": "root4", "process": "st_dbserver"}, {"user": "root5", "process": "st_deltastore"}, {"user": "1root6", "process": "st_graph4v1"}, {"user": "1root7", "process": "st_performance"}, {"user": "1root8", "process": "st_query"}, {"user": "1root9", "process": "st_runtime"}, {"user": "1root10", "process": "st_storage"}, {"user": "1root11", "process": "st_tools"}, {"user": "1root12", "process": "gmimport"}, {"user": "1root13", "process": "gmexport"}], "groups": [{"group": "grou_p00", "process": "group_0"}, {"group": "grou_p01", "process": "group_1"}, {"group": "grou_p02", "process": "group_2"}, {"group": "group_03", "process": "group_3"}, {"group": "grou_p04", "process": "group_4"}, {"group": "1group_03", "process": "group_3"}, {"group": "1grou_p04", "process": "group_4"}, {"group": "1grou_p05", "process": "group_5"}, {"group": "1grou_p06", "process": "group_6"}, {"group": "1grou_p07", "process": "group_7"}, {"group": "1grou_p08", "process": "group_8"}, {"group": "1grou_p09", "process": "group_9"}, {"group": "1grou_p10", "process": "group_10"}, {"group": "1grou_p11", "process": "1"}]}