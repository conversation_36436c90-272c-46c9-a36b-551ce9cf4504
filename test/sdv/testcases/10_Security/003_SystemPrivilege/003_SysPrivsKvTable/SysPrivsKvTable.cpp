/*****************************************************************************
 Description  : KVTABLE系统权限的校验
 Notes        :
 History      :
 Author       : madongdong m00502160
 Created      : 2021.07.07
 Modification :
*****************************************************************************/

extern "C" {
}

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"
#include <string>

int ret = 0;
GmcConnT *conn;
GmcStmtT *stmt;
GmcConnT *g_conn_async;
GmcStmtT *g_stmt_async;
#define MAX_CMD_SIZE 1024
char g_command[1024];
char g_tableName[128] = "KV_SYSPRIVS";
const char *g_configJson = R"({"max_record_count":100,"max_record_count_check":true})";

class SysPrivs_KvTable : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SysPrivs_KvTable::SetUpTestCase()
{
    //权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
}

void SysPrivs_KvTable::TearDownTestCase()
{
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
void SysPrivs_KvTable::SetUp()
{
    //启server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //导入白名单
    const char *allow_list_file = "schema/allow_list/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_CHECK_LOG_BEGIN();
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
}
void SysPrivs_KvTable::TearDown()
{
    AW_CHECK_LOG_END();
    // 添加删除白名单操作
    const char *allow_list_file = "schema/allow_list/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove db user. success: 1");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 000.对用户赋予KvTable的所有权限，用户对KvTable的所有合理操作都可成功，其他表类型的操作都失败
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_000)
{
    //导入系统权限 KvTable的所有权限
    const char *sys_policy_file = "gmpolicy_file/KvTableAllPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对KV表相关操作
    char kvTableName[128];
    for (int i = 0; i < 1; i++) {
        sprintf(kvTableName, "KvTable_%d", i);
        // create kv
        ret = GmcKvCreateTable(stmt, kvTableName, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        // open kv
        ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
        EXPECT_EQ(GMERR_OK, ret);
        // set
        GmcKvTupleT kvInfo = {0};
        char key[] = "zhangsan1";
        int32_t value = 10;
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // get
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(10, *(uint32_t *)output);
        EXPECT_EQ(4, outputLen);
        // isExist
        bool isExist = false;
        ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, isExist);
        // recordCount
        uint32_t count = 0;
        ret = GmcKvTableRecordCount(stmt, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, count);
        // delete
        ret = GmcKvRemove(stmt, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
        // set
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ScanKv
        uint64_t limitCount = 10;
        ret = GmcKvScan(stmt, limitCount);
        EXPECT_EQ(ret, GMERR_OK);
        uint32_t j = 0;
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (isFinish == true) {
                break;
            }
            j++;
            char *fetchKey = NULL;
            uint32_t fetchKeyLen = 0;
            char *fetchValue = NULL;
            uint32_t fetchValueLen = 0;
            ret = GmcKvGetFromStmt(stmt, (void **)&fetchKey, &fetchKeyLen, (void **)&fetchValue, &fetchValueLen);
            EXPECT_EQ(ret, GMERR_OK);
            // EXPECT_STREQ(key, fetchKey);
            ret = strncmp(key, fetchKey, strlen(key));
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_EQ((uint32_t)10, *(uint32_t *)fetchValue);
            EXPECT_EQ((uint32_t)9, fetchKeyLen);
            EXPECT_EQ((uint32_t)4, fetchValueLen);
        }
        // close kv
        // ret = GmcCloseKvTable(stmt);
        // EXPECT_EQ(GMERR_OK,ret);
        // truncate
        ret = GmcKvTruncateTable(stmt, kvTableName);
        EXPECT_EQ(GMERR_OK, ret);
        // drop
        ret = GmcKvDropTable(stmt, kvTableName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.依次对kvtable叠加create、get、set，select、delete、truncate、drop的权限
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_001)
{
    //对用户赋予KvTable的create
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_001.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv 无权限
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的GET
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_002.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 无权限
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan1";
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    bool isExist = false;
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的SET权限
    const char *sys_policy_file2 = "gmpolicy_file/KvTablePartPolicy_003.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // recordCount 无权限
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的SELECT权限
    const char *sys_policy_file3 = "gmpolicy_file/KvTablePartPolicy_004.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file3,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ///对用户赋予KvTable的DELETE权限
    const char *sys_policy_file4 = "gmpolicy_file/KvTablePartPolicy_005.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file4,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的TRUNCATE权限
    const char *sys_policy_file5 = "gmpolicy_file/KvTablePartPolicy_006.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file5,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, count);
    // delete
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的DROP权限
    const char *sys_policy_file6 = "gmpolicy_file/KvTablePartPolicy_007.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file6,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist

    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // drop
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.只对用户赋予kvtable的create权限, create 单表，批量创建表
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_002)
{
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv 无权限
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的create
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_001.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //用户对kv表执行操作
    // create kv sucess
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // Batch create kv
    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    char TableName[128];
    for (int i = 0; i < 1023; i++) {
        sprintf(TableName, "KvTable_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, TableName, NULL, NULL);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, 1023);
    EXPECT_EQ(successNum, 1023);
    // Batch drop kv failed
    for (int i = 0; i < 1023; i++) {
        sprintf(TableName, "KvTable_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, TableName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop kv failed
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的drop
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_008.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // Batch drop kv success
    for (int i = 0; i < 1023; i++) {
        sprintf(TableName, "KvTable_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, TableName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, 1023);
    EXPECT_EQ(successNum, 1023);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    // drop kv success
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.只对用户赋予kvtable的drop权限，用户删除kv表可操作成功
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_003)
{
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // drop kv 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的create
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_001.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv 有权限
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // drop kv 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的drop
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_008.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // drop kv 有权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.对用户赋权create、drop、get、replace权限,用户对kv的数据set可操作成功
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_004)
{
    //对用户赋予KvTable的create、drop、get
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_009.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 无权限
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan1";
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    //追加对用户赋予KvTable的SET
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_003.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    char output[128] = {0};
    uint32_t outputLen = 0;
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    bool isExist = false;
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // recordCount 无权限
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 有权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.对用户赋权create、drop、delete权限，用户对kv的数据delete可操作成功，对其它操作失败，追加赋权replace权限，数据插入成功，后再delete可操作成功
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_005)
{
    //对用户赋予KvTable的create、drop、get
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_009.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    char key[] = "zhangsan1";
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    //追加对用户赋予KvTable的delete
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_010.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 有权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    //追加对用户赋予KvTable的SET
    const char *sys_policy_file2 = "gmpolicy_file/KvTablePartPolicy_003.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    GmcKvTupleT kvInfo = {0};
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // delete 有权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    char output[128] = {0};
    uint32_t outputLen = 0;
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    bool isExist = false;
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // recordCount 无权限
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 有权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.对用户赋权create、drop、replace、select权限，用户建表插入数据后，对kv表进行查询操作都可成功，scankv表也可成功，其他kv表操作失败
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_006)
{
    //对用户赋予KvTable的create、drop、get
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_009.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    char output[128] = {0};
    char key[] = "zhangsan1";
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    bool isExist = false;
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // recordCount 无权限
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // ScanKv 无权限
    uint64_t limitCount = 10;
    ret = GmcKvScan(stmt, limitCount);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // //对用户追加赋予KvTable的select replace
    const char *sys_policy_file2 = "gmpolicy_file/KvTablePartPolicy_004.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    GmcKvTupleT kvInfo = {0};
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get 有权限
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist 有权限
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount 有权限
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // ScanKv 有权限
    ret = GmcKvScan(stmt, limitCount);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t j = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true) {
            break;
        }
        j++;
        char *fetchKey = NULL;
        uint32_t fetchKeyLen = 0;
        char *fetchValue = NULL;
        uint32_t fetchValueLen = 0;
        ret = GmcKvGetFromStmt(stmt, (void **)&fetchKey, &fetchKeyLen, (void **)&fetchValue, &fetchValueLen);
        EXPECT_EQ(ret, GMERR_OK);
        // EXPECT_STREQ(key, fetchKey);
        ret = strncmp(key, fetchKey, strlen(key));
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ((uint32_t)10, *(uint32_t *)fetchValue);
        EXPECT_EQ((uint32_t)9, fetchKeyLen);
        EXPECT_EQ((uint32_t)4, fetchValueLen);
    }
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 有权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.对用户赋权create、drop、truncate权限，用户可对kv表truncate成功；再次追加权限replace，对kv表插入数据后，truncate操作成功
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_007)
{
    //对用户赋予KvTable的create、drop、get
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_009.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // //对用户追加赋予KvTable的truncate
    const char *sys_policy_file2 = "gmpolicy_file/KvTablePartPolicy_011.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 有权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // //对用户追加赋予KvTable的replace
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_003.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv 有权限
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan1";
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    bool isExist = false;
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // recordCount 无权限
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 有权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 有权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.依次对kvtable追加create、get、set，select、delete、truncate、drop的权限，调用异步接口操作kv表
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_008)
{
    AsyncUserDataT data = {0};
    void *kvtable = NULL;
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv failed
    ret = GmcKvCreateTableAsync(g_stmt_async, g_tableName, g_configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的create
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_001.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTableAsync(g_stmt_async, g_tableName, g_configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // open kv 无权限
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的GET
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_002.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // disconnect
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 无权限
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan1";
    int32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    // truncate 无权限
    ret = GmcKvTruncateTableAsync(g_stmt_async, g_tableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // //对用户赋予KvTable的SET权限
    const char *sys_policy_file2 = "gmpolicy_file/KvTablePartPolicy_003.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // disconnect
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // delete 无权限
    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    // truncate 无权限
    ret = GmcKvTruncateTableAsync(g_stmt_async, g_tableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的delete权限
    const char *sys_policy_file3 = "gmpolicy_file/KvTablePartPolicy_005.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file3,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // disconnect
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // delete 无权限
    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // close kv 有权限
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    // truncate 无权限
    ret = GmcKvTruncateTableAsync(g_stmt_async, g_tableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的truncate权限
    const char *sys_policy_file4 = "gmpolicy_file/KvTablePartPolicy_006.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file4,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // disconnect
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // delete 无权限
    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // close kv 有权限
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    // truncate 无权限
    ret = GmcKvTruncateTableAsync(g_stmt_async, g_tableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // drop 无权限
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的drop权限
    const char *sys_policy_file5 = "gmpolicy_file/KvTablePartPolicy_008.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file5,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // disconnect
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 有权限
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // delete 无权限
    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // close kv 有权限
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    // truncate 无权限
    ret = GmcKvTruncateTableAsync(g_stmt_async, g_tableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // drop 无权限
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.依次对kvtable追加create、get、set，select、delete、truncate、drop的权限，对全局kv表进行操作
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_009)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    char Global_KvTableName[128] = "T_GMDB";
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv
    ret = GmcKvCreateTable(stmt, Global_KvTableName, g_configJson);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop
    ret = GmcKvDropTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的create
    const char *sys_policy_file = "gmpolicy_file/KvTablePartPolicy_001.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, Global_KvTableName, g_configJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    // open kv 无权限
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //对用户赋予KvTable的GET
    const char *sys_policy_file1 = "gmpolicy_file/KvTablePartPolicy_002.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set 无权限
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan1";
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    bool isExist = false;
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的SET权限
    const char *sys_policy_file2 = "gmpolicy_file/KvTablePartPolicy_003.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get 无权限
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // isExist 无权限
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // recordCount 无权限
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的SELECT权限
    const char *sys_policy_file3 = "gmpolicy_file/KvTablePartPolicy_004.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file3,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete 无权限
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // close kv 有权限
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ///对用户赋予KvTable的DELETE权限
    const char *sys_policy_file4 = "gmpolicy_file/KvTablePartPolicy_005.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file4,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的TRUNCATE权限
    const char *sys_policy_file5 = "gmpolicy_file/KvTablePartPolicy_006.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file5,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist
    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    //对用户赋予KvTable的DROP权限
    const char *sys_policy_file6 = "gmpolicy_file/KvTablePartPolicy_007.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file6,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // isExist

    ret = GmcKvIsExist(stmt, key, strlen(key), &isExist);

    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, isExist);
    // recordCount
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // delete
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // drop
    ret = GmcKvDropTable(stmt, Global_KvTableName);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_grant_privs_01(void *arg)
{
    const char *sys_policy_file_01 = "gmpolicy_file/KvTablePartPolicy_001.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file_01,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    // EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    return NULL;
}
void *thread_grant_privs_02(void *arg)
{
    char g_command1[1024];
    const char *sys_policy_file_02 = "gmpolicy_file/KvTablePartPolicy_007.gmpolicy";
    snprintf(g_command1, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file_02,
        g_connServer);
    printf("[INFO]%s\n", g_command1);
    ret = executeCommand(g_command1, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command1, 0, sizeof(g_command1));
    return NULL;
}

// 010.并发赋予kv表的不同权限
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_010)
{
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t kvTable[2];
    ret = pthread_create(&kvTable[0], NULL, thread_grant_privs_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&kvTable[1], NULL, thread_grant_privs_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(kvTable[0], NULL);
    pthread_join(kvTable[1], NULL);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // set
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan1";
    int32_t value = 10;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // get
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // drop
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_grant_privs_03(void *arg)
{
    char g_command3[1024];
    const char *sys_policy_file_03 = "gmpolicy_file/KvTablePartPolicy_008.gmpolicy";
    snprintf(g_command3, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file_03,
        g_connServer);
    printf("[INFO]%s\n", g_command3);
    ret = executeCommand(g_command3, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command3, 0, sizeof(g_command3));
    return NULL;
}

// 011.并发赋予kv表的不同权限，一个赋予create 一个赋予drop
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_011)
{
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t kvTable[2];
    ret = pthread_create(&kvTable[0], NULL, thread_grant_privs_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&kvTable[1], NULL, thread_grant_privs_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(kvTable[0], NULL);
    pthread_join(kvTable[1], NULL);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_grant_privs_04(void *arg)
{
    char g_command4[1024];
    const char *sys_policy_file_04 = "gmpolicy_file/KvTablePartPolicy_002.gmpolicy";
    snprintf(g_command4, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file_04,
        g_connServer);
    printf("[INFO]%s\n", g_command4);
    ret = executeCommand(g_command4, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command4, 0, sizeof(g_command4));
    return NULL;
}
// 012.并发赋予kv表的不同权限，一个赋予create 一个赋予drop，一个get
TEST_F(SysPrivs_KvTable, SEC_003_003_SysPrivs_KvTable_012)
{
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //用户对kv表执行操作
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate 无权限
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop 无权限
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //多线程赋权
    pthread_t kvTable[3];
    ret = pthread_create(&kvTable[0], NULL, thread_grant_privs_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&kvTable[1], NULL, thread_grant_privs_03, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&kvTable[2], NULL, thread_grant_privs_04, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(kvTable[0], NULL);
    pthread_join(kvTable[1], NULL);
    pthread_join(kvTable[2], NULL);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create kv
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // open kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    //断连
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
