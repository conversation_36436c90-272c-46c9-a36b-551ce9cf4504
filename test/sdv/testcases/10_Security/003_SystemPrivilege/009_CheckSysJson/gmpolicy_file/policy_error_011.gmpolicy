{"system_privilege_config": [{"users": [{"user": "roott", "process": "CheckSysJson"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY"]}, {"obj_type": "EDGE_LABEL", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "INSERT_ANY", "SELECT_ANY"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "USE", "DROP"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}]}]}