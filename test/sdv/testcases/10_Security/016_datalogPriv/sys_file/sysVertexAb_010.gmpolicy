{"system_privilege_config": [{"users": [{"user": "root", "process": "datalogSysPriv"}, {"user": "root", "process": "gmimport"}, {"user": "root", "process": "datalogPrivAb"}, {"user": "root", "process": "datalogObjPriv"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "GET", "ALTER", "DROP"]}, {"obj_type": "DATALOG_UDF", "privs_type": ["CREATE"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "GET", "ALTER", "DROP"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "USE", "DROP"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "BINARY_FILE", "privs_type": ["OPEN", "CLOSE"]}]}]}