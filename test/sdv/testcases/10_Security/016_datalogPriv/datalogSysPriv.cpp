/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalogSysPriv.cpp
 * Description: datalog系统权限验证
 * Author: qinqianbao 995465
 * Create: 2022-10-22
 */
#include "datalogPriv.h"
#include "t_datacom_lite.h"

class datalogSysPriv : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh ${TEST_HOME}/tools/stop.sh -f");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void datalogSysPriv::SetUp()
{
    int ret;
    g_conn = NULL;
    g_stmt = NULL;
    char allowListFile[128] = "d_file/allow_list.gmuser";
    char SysPrivFile[128] = "sys_file/sysVertex.gmpolicy";
    AllowListImport(allowListFile); // 导入白名单
    SystemPrivImport(SysPrivFile); //导入系统权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void datalogSysPriv::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    char allowListFile[128] = "d_file/allow_list.gmuser";
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AllowListRemove(allowListFile); // 白名单删除
}

// 001. 授予gmimport用户vertex权限create/drop, 进行常规表的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_001";
    LoadPrepare(fileName); // datalog程序加载
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char SysPrivFile[128] = "sys_file/sysVertexAb_007.gmpolicy";
    ret = TestUninstallDatalog(fileName); // datalog程序卸载
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 授予gmimport用户resource权限create/drop, 进行资源表的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_002";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 授予gmimport用户vertex权限create/drop, 进行可更新表的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_003";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. 授予gmimport用户vertex权限create/drop, 进行null表的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_005";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007. 授予gmimport用户udf权限create/drop, 进行udf_function的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_007";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char SysPrivFile[128] = "sys_file/sysVertexAb_007.gmpolicy";
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. 授予gmimport用户udf权限create/drop, 进行udf_aggregate的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_008";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009. 授予gmimport用户udf权限create/drop, 进行udf_timeout的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_009";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010. 授予gmimport用户udf权限create/drop, 进行udf_updateCompare的创建/删除
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_010";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013. 授予gmimport用户namespace权限create/use/drop, 规则文件中包含ns
TEST_F(datalogSysPriv, SEC_016_datalogSysPriv_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[128] = "datalogSysPriv_013";
    LoadPrepare(fileName);
    int ret = TestLoadDatalog(g_soNameTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view1 = "V\\$CATA_VERTEX_LABEL_INFO";
    char const *view2 = "V\\$STORAGE_VERTEX_COUNT";
    char const *view3 = "V\\$QRY_DML_OPER_STATIS";
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q %s", view1);
    AW_FUN_Log(LOG_DEBUG, "[INFO]LOAD_CMD: %s", cmd);
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);

    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q %s", view2);
    AW_FUN_Log(LOG_DEBUG, "[INFO]LOAD_CMD: %s", cmd);
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);

    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q %s", view3);
    AW_FUN_Log(LOG_DEBUG, "[INFO]LOAD_CMD: %s", cmd);
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);

    ret = TestUninstallDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
