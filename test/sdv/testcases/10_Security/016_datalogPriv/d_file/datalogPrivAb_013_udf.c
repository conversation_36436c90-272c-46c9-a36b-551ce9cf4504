/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
    int32_t dtlReservedCount;
} A;

#pragma pack(0)

int32_t dtl_timeout_callback_A(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
    GmUdfCtxT *ctx)
{
    int ret;
    // 不返回记录

    return GMERR_OK;
}
