/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "gm_udf.h"

#pragma pack(1)
typedef struct B {
    int32_t a;
    int32_t b;
    int32_t c;
} B;
#pragma pack(0)
int32_t dtl_serialize_hpr_tbl_outB(void *tuple, GmcHprKeyT *key, GmcShmemPtrT *value, GmUdfShmCtxT *memCtx)
{
    B *origin = (B *)tuple;
    key->linearKey.index = origin->a;
    B *dest = (B *)GmUdfShmMemAlloc(memCtx, sizeof(B), value);
    if (dest == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dest->a = origin->a;
    dest->b = origin->b;
    dest->c = origin->c;
    return GMERR_OK;
}
