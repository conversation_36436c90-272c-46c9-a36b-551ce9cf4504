/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "gm_udf.h"
#pragma pack(1)

typedef struct A2 {
    int64_t a;
    int64_t b;
    int64_t t;
    int32_t dtlReservedCount;
} A2;

#pragma pack(0)

int32_t dtl_timeout_callback_A2(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    A2 *srcTuple = (A2 *)timeoutTuple;

    A2 *dstTuple = GmUdfMemAlloc(ctx, sizeof(A2));
    if (dstTuple == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *dstTuple = (A2){0};
    dstTuple->a = srcTuple->a;
    dstTuple->b = srcTuple->b;
    dstTuple->t = srcTuple->t;

    dstTuple->dtlReservedCount = -srcTuple->dtlReservedCount;

    *extraTupleLen = sizeof(A2);
    *extraTuple = dstTuple;
    return GMERR_OK;
}
