/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"

#pragma pack(1)
// 输入字段--聚合字段
typedef struct A {
    int32_t a;
} A;
// 输出字段---min和max
typedef struct B {
    int32_t a;
    int32_t b;
} B;

#pragma pack(0)

int32_t dtl_agg_compare_agg(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_agg(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct = GmUdfMemAlloc(ctx, sizeof(B));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
