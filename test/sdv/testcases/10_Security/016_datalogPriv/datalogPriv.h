/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalogPriv.h
 * Description: datalog权限
 * Author: qinqianbao 995465
 * Create: 2022-10-22
 */
#ifndef DATALOGPRIV_H
#define DATALOGPRIV_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define FILE_PATH 512
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_hFile[FILE_PATH] = "../../../../../pub/include/";
char g_sysPolicyfile[128] = "sys_file/sysVertex.gmpolicy";
char const *g_storageDataView = "V\\$STORAGE_VERTEX_COUNT";

typedef struct {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
} DatalogObjTestBufsT;
DatalogObjTestBufsT g_bufObjTest[10] = {0};

typedef struct {
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t dtlReservedCount;
} DatalogObjBufsT;
DatalogObjBufsT g_bufObjTest003[10] = {0};

/*********************************************************************
datalog权限检查：
1、权限检查流程在计划执行前面
2、首先检查执行计划中表拥有的最小权限(输入、输出、中间表的写入权限)
3、权限不足, 数据不会写，规则也就不触发
4、先校验最小权限、后加载或卸载，缺少其一，全局失败
***********************************************************************/
char g_soNameTest[MAX_CMD_SIZE] = {0};

void LoadPrepare(char *fileName)
{
    (void)snprintf(g_soNameTest, MAX_CMD_SIZE, "./d_file/%s.so", fileName);
}

void AllowListImport(char *fileName)
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, fileName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]ALLOWLIST_CMD: %s", cmd);
    int ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);
}

void SystemPrivImport(char *fileName)
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, fileName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]SYSPRIV_CMD: %s", cmd);
    int ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);
}

void ObjectPrivImport(char *fileName)
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, fileName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]OBJPRIV_CMD: %s", cmd);
    int ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);
}

void ObjectPrivRevoke(char *fileName)
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, fileName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]OBJPRIV_REVOKE_CMD: %s", cmd);
    int ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);
}

void AllowListRemove(char *fileName)
{
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, fileName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]REMOVE_ALLOWLIST_CMD: %s", cmd);
    int ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, MAX_CMD_SIZE, 0);
}

int CheckDataMatch(int record, int32_t result[][3])
{
    int cnt = 0;  // 记录匹配次数
    char check[128] = "checkCorrect";
    int i, j;
    for (i = 0; i < record; i++) {
        for (j = 0; j < record; j++) {
            if (result[i][0] == g_bufObjTest[j].a && result[i][1] == g_bufObjTest[j].b &&
                result[i][2] == g_bufObjTest[j].dtlReservedCount) {
                cnt++;
                break;
            }
        }
    }
    if (cnt != record) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]result: %d %d %d", result[i][0], result[i][1], result[i][2]);
        AW_FUN_Log(LOG_DEBUG, "[INFO]g_bufObjTest: %d %d %d", g_bufObjTest[j].a, g_bufObjTest[j].b,
            g_bufObjTest[j].dtlReservedCount);
        EXPECT_EQ(0, 1);
    } else {
        AW_FUN_Log(LOG_DEBUG, "[INFO]: %s count = %d", check, cnt);
    }
    return cnt != record;
}

int CheckDataMatch003(int record, int32_t result[][4])
{
    int cnt = 0;  // 记录匹配次数
    char check[128] = "checkCorrect";
    int i, j;
    for (i = 0; i < record; i++) {
        for (j = 0; j < record; j++) {
            if (result[i][0] == g_bufObjTest003[j].a && result[i][1] == g_bufObjTest003[j].b &&
                result[i][2] == g_bufObjTest003[j].c && result[i][3] == g_bufObjTest003[j].dtlReservedCount) {
                cnt++;
                break;
            }
        }
    }
    if (cnt != record) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]result: %d %d %d", result[i][0], result[i][1], result[i][2], result[i][3]);
        AW_FUN_Log(LOG_DEBUG, "[INFO]g_bufObjTest003: %d %d %d", g_bufObjTest003[j].a, g_bufObjTest003[j].b,
            g_bufObjTest003[j].c, g_bufObjTest003[j].dtlReservedCount);
        EXPECT_EQ(0, 1);
    } else {
        AW_FUN_Log(LOG_DEBUG, "[INFO]: %s count = %d", check, cnt);
    }
    return cnt != record;
}

void ReadOutFunTest003(GmcStmtT *stmt, char *tableOut, int record, int32_t result[][4])
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int cnt = 0;
    bool isNull;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int32_t a, b, c, dtlReservedCount;
        ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest003[cnt].a = a;
        ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest003[cnt].b = b;
        ret = GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest003[cnt].c = c;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest003[cnt].dtlReservedCount = dtlReservedCount;
        AW_FUN_Log(LOG_DEBUG, "[INFO]result_test: %d %d %d %d", a, b, c, dtlReservedCount);
        cnt++;
    }
    EXPECT_EQ(record, cnt);
}

void ReadOutFunTest(GmcStmtT *stmt, char *tableOut, int record, int32_t result[][3])
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int cnt = 0;
    bool isNull;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int32_t a, b, dtlReservedCount;
        ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest[cnt].a = a;
        ret = GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest[cnt].b = b;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        g_bufObjTest[cnt].dtlReservedCount = dtlReservedCount;
        AW_FUN_Log(LOG_DEBUG, "[INFO]result_test: %d %d %d", a, b, dtlReservedCount);
        cnt++;
    }
    EXPECT_EQ(record, cnt);
}

void ReadOutFunTestAb022(GmcStmtT *stmt, char *tableOut, int record, int32_t result[][3])
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
}

int DmlFunTest(GmcStmtT *stmt, char *labelName, int32_t count[][3], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int DmlFunTestAbNormal(GmcStmtT *stmt, char *labelName, int32_t count[][3], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    return ret;
}

int BatchDmlFunTest(GmcStmtT *stmt, char *labelName, int32_t count[][3], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int BatchDmlFunTest005(GmcStmtT *stmt, char *labelName, int32_t count[][5], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value4 = count[i][3];
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &value4, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value5 = count[i][4];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int BatchDmlFunTest026(GmcStmtT *stmt, char *labelName, int32_t count[][4], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value4 = count[i][3];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value4, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int BatchDmlFunTestAb026(GmcStmtT *stmt, char *labelName, int32_t count[][4], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value4 = count[i][3];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value4, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int BatchDmlFunTest000(GmcStmtT *stmt, char *labelName, int32_t count[][4], int32_t dataNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < dataNum; i++) {
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t value4 = count[i][3];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value4, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

#endif
