/*****************************************************************************
 Description  : 对象权限可靠性测试
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/07/13
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "ObjPrivsTest.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class SEC_004_ObjPrivsRel : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 权限校验改为强制模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        readJanssonFile("schemaFile/config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        // 恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_004_ObjPrivsRel::SetUp()
{
    printf("[INFO] ObjPrivsRel Start.\n");
    AW_CHECK_LOG_BEGIN();
}

void SEC_004_ObjPrivsRel::TearDown()
{
    AW_CHECK_LOG_END();
    printf("[INFO] ObjPrivsRel End.\n");
}

// 001. 授权前同一用户的多个连接均无法操作，授权后均可以操作
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_001)
{
    int ret = 0;
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 导入白名单
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/rel_policy/objNoInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // before grant
    int connCount = 3;
    do {
        void *vertexLabel = NULL;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < count; i++) {
            set_VertexProperty_F7(stmt, i);
            set_VertexProperty_F9(stmt, i);
            set_VertexProperty(stmt, i);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
            AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
            ret = testGmcGetLastError(g_relPrivError);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        connCount--;
    } while (connCount);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/rel_policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    connCount = 3;
    do {
        void *vertexLabel = NULL;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < count; i++) {
            set_VertexProperty_F7(stmt, i);
            set_VertexProperty_F9(stmt, i);
            set_VertexProperty(stmt, i);
            ret = GmcExecute(stmt);
            EXPECT_NE(GMERR_INSUFFICIENT_PRIVILEGE, ret);
            AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        }
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        connCount--;
    } while (connCount);

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void *ThreadStartClient(void *args)
{
    int ret = system("./ObjPrivsRel --gtest_also_run_disabled_tests "
                     "--gtest_filter=*DISABLED_Client2 >Client2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 子进程
TEST_F(SEC_004_ObjPrivsRel, DISABLED_Client2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_needCheckWhenSucc = false;
    GmcConnT *conn02;
    GmcStmtT *stmt02;
    int ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn02, &stmt02);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    ret = testGmcPrepareStmtByLabelName(stmt02, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 50; i < 100; i++) {
        set_VertexProperty_F7(stmt02, i);
        set_VertexProperty_F9(stmt02, i);
        set_VertexProperty(stmt02, i);
        ret = GmcExecute(stmt02);
        EXPECT_EQ(GMERR_OK, ret);
    }

    const char *obj_policy_file1 = "schemaFile/rel_policy/objInsertVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt02, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn02, stmt02);
    EXPECT_EQ(GMERR_OK, ret);

    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO]pid2 connect success\r\n");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002. 客户端异常退出，重连后权限依旧存在且可用
// 备注：用例不重启server,不授权，跑完001之后执行002成功，以此验证权限在客户端退出后重连依旧存在
// 构造客户端异常退出----自动化  不自动化执行可根据以下方式
// 该用例无法自动化，执行前将001中的droplabel注释
// 因为删表之后重建就不是同一个对象了,此时对象权限就需要重新赋予，否则插入会报错
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_002)
{
    int ret = 0;
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);

    // 客户端异常退出, 重新写入数据

    GmcConnT *conn01;
    GmcStmtT *stmt01;
    char killPid[20] = {0};

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    // 导入白名单
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&conn01, &stmt01);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO]pid1 connect success\r\n");
    int count = 50;
    ret = GmcCreateVertexLabel(stmt01, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/rel_policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // insert vertex
    ret = testGmcPrepareStmtByLabelName(stmt01, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(stmt01, i);
        set_VertexProperty_F9(stmt01, i);
        set_VertexProperty(stmt01, i);
        ret = GmcExecute(stmt01);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn01, stmt01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t thrArr[1];
    ret = pthread_create(&thrArr[0], NULL, ThreadStartClient, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);
    system("rm -rf Client2.txt");

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 003. 授权group+process对象权限后，通过group下的user+process操作相关表成功
// 备注：C10B050无法给group+process导入权限，用例保留但不执行，详见：DTS20210716085XN1P0D00
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_003)
{
    int ret = 0;
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 导入白名单只导入group+process
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list_003.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex_003.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/rel_policy/objInsertVertex_003.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // before grant
    int connCount = 1;
    do {
        void *vertexLabel = NULL;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < count; i++) {
            set_VertexProperty_F7(stmt, i);
            set_VertexProperty_F9(stmt, i);
            set_VertexProperty(stmt, i);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        connCount--;
    } while (connCount);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 004.
// group+process权力大于user+process，校验user+process登录后权限只具备user的权限，不具备group用户拥有但user未拥有的权限
/*
1.用例执行前先建一个OS用户
    useradd abc -g root
2.把相关文件权限都给abc
    chown -R abc:root /home/<USER>/   #别直接把/目录所有权限给一个普通用户，风险极大
    chown -R abc:root /usr/local/file/
    chown -R abc:root /run/verona/
3.切换到abc,source环境变量
4.执行本用例  #本用例无法自动化
可能遇到的问题：
1.ipcrm
-a的时候报没有清理某个id共享内存的权限，可能是root用户启的server还在，所以执行用例前先把root用户的server停掉，并ipcrm -a
2.ulimit -nS 20480的时候报没权限，用root用户在/etc/security/limits.conf里下面这两行
abc hard nofile 20480
abc soft nproc 20480
*/
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_004)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=abc:gmrule\"");
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 导入白名单root:ObjPrivsRel  abc:ObjPrivsRel
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list_004_005.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop(root:root abc:root)
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex_004_005.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant obj privs(root:root<INSERT/SELECT> abc:root<INSERT>)
    const char *obj_policy_file2 = "schemaFile/rel_policy/obj_004.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // check insert ok
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan failed
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0, GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 005. group+process权力小于user+process，校验user+process登录后权限只具备user的权限 无法自动化测试方法同004
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_005)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=abc:gmrule\"");
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 导入白名单root:ObjPrivsRel  abc:ObjPrivsRel
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list_004_005.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop(root:root abc:root)
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex_004_005.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant obj privs(root:root<INSERT/SELECT> abc:root<INSERT>)
    const char *obj_policy_file2 = "schemaFile/rel_policy/obj_005.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // check insert ok
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan ok
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 006. 授权后断链，重新连接后权限依旧存在且可用
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_006)
{
    int ret = 0;
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 导入白名单
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 100;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/rel_policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int connCount = 2;
    do {
        void *vertexLabel = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < count; i++) {
            set_VertexProperty_F7(stmt, i);
            set_VertexProperty_F9(stmt, i);
            set_VertexProperty(stmt, i);
            ret = GmcExecute(stmt);
            EXPECT_NE(GMERR_INSUFFICIENT_PRIVILEGE, ret);
            AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        }
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        connCount--;
    } while (connCount);

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void *dml_vertex(void *args)
{
    int ret = 0;
    int count = 100;
    void *vertexLabel = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(stmt, i);
        set_VertexProperty_F9(stmt, i);
        set_VertexProperty(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ((ret == GMERR_OK) ? GMERR_OK : GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(stmt, i);
        set_VertexProperty_F9(stmt, i);
        set_VertexProperty(stmt, NewVal);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(stmt, i);
        set_VertexProperty_F9(stmt, i);
        set_VertexProperty(stmt, NewVal);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(stmt, NewVal);
        ret = GmcSetIndexKeyName(stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 007. 授权之后，多线程DML操作
TEST_F(SEC_004_ObjPrivsRel, SEC_004_ObjPrivsRel_007)
{
    int ret = 0;
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 导入白名单
    const char *allow_list_file = "schemaFile/allow_list/rel_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/rel_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant insert
    const char *obj_policy_file2 = "schemaFile/rel_policy/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // multi threads merge
    int tdNum = 8;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, dml_vertex, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
