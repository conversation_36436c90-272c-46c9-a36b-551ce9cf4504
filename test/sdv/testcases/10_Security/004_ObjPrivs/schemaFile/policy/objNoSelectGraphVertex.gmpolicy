{"object_privilege_config": [{"obj_name": "vsys", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "ObjPrivsVertex", "privs_type": ["UPDATE", "INSERT", "MERGE", "REPLACE", "DELETE"]}]}, {"obj_name": "vsys::rule", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "ObjPrivsVertex", "privs_type": ["UPDATE", "INSERT", "MERGE", "REPLACE", "DELETE"]}]}, {"obj_name": "vsys::rule::source_ip", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "ObjPrivsVertex", "privs_type": ["UPDATE", "INSERT", "MERGE", "REPLACE", "DELETE"]}]}]}