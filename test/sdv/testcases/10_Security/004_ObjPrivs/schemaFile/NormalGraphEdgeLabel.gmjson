[{"name": "vsys_rule", "source_vertex_label": "vsys", "comment": "haha", "dest_vertex_label": "vsys::rule", "constraint": {"operator_type": "and", "conditions": [{"source_property": "id", "dest_property": "vsys::id"}]}}, {"name": "ruleAndsource_ip", "source_vertex_label": "vsys::rule", "comment": "hahaha", "dest_vertex_label": "vsys::rule::source_ip", "constraint": {"operator_type": "and", "conditions": [{"source_property": "vsys::id", "dest_property": "rule::vsys::id"}, {"source_property": "id", "dest_property": "rule::id"}]}}]