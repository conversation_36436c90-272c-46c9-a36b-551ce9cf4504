/*****************************************************************************
 Description  : gmrule导入对象权限check
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/07/08
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "ObjPrivsTest.h"

int ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class SEC_004_ObjPrivsGmrule : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        printf("[INFO] ObjPrivsGmrule Start.\n");
        system("sh $TEST_HOME/tools/stop.sh -f");
        // 权限校验改为强制模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        // 拉起server
        system("sh $TEST_HOME/tools/start.sh -f");
        // 环境初始化
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schemaFile/config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        // 恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_004_ObjPrivsGmrule::SetUp()
{
    // 导入白名单
    const char *allow_list_file = "schemaFile/allow_list/gmrule_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/gmrule_test_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void SEC_004_ObjPrivsGmrule::TearDown()
{
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *allow_list_file = "schemaFile/allow_list/gmrule_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_CHECK_LOG_END();
    printf("[INFO] ObjPrivsGmrule End.\n");
}

// 001.导入空policy.gmjson
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_001)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    // gmrule
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/NullPolicy.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    // 现在不再直接调用第三方库接口，修改后json接口统一不传错误码和具体错误信息
    // 因为错误码没意义，是人为指定的，固定是GMERR_INVALID_JSON
    ret = executeCommand(g_command, "NullPolicy.gmpolicy nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 002.导入的policy.gmjson文件只有{}
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_002)
{
    int ret = 0;
    // gmrule
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/NonePolicy.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 003.重复导入同一用户相关对象权限
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_003)
{
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // 首次导入
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    // 再次导入
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 004.导入policy.gmjson包含无效字段
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_004)
{
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_004.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018002");
    // obj_type为视图名时(系统表)：gmrule在导对象权限的时候产生crash，导致server挂掉
    const char *obj_policy_file2 = "schemaFile/gmrule_test_policy/policy_dst_DTS2022041608876.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    ret = executeCommand(g_command, "grant object priv to \"root:ObjPrivsGmrule\" unsuccessful, "
                                    "objName: V$CATA_VERTEX_LABEL_INFO. ret = 1018002.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 005.导入policy.gmjson缺失obj_name
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_005)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_005.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, "field \"obj_name\" is inv in json file", expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 006.导入policy.gmjson缺失obj_type
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_006)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_006.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    // ret = executeCommand(g_command, "field \"obj_type\" is inv in json file", expectErrorCode);
    // EXPECT_EQ(GMERR_OK, ret);  暂时遗留
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

// 007.导入policy.gmjson缺失namespace
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_007)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_007.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, "field \"namespace\" inv in json file", expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 008.导入policy.gmjson缺失privs
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_008)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_008.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, "field \"privs\" is invalid in \"object_privilege_config\".", expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 009.导入policy.gmjson缺失user和group
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_009)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_009.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, "field \"user\" and \"group\" is invalid.", expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 010.导入policy.gmjson缺失privs_type
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_010)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_010.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, "field \"privs_type\" is invalid in \"object_privilege_config\"", expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 011.导入policy.gmjson中obj_name未创建  #未创建只告警不报错 DTS2021092820205
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_011)
{
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_011.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 012.导入policy.gmjson中obj_type不在枚举中
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_012)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_012.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 013.导入policy.gmjson中namespace未创建
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_013)
{
    int ret = 0;
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_013.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 014.导入policy.gmjson中user不存在的用户
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_014)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018002");
    int ret = 0;
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_014.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s -d", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_UNDEFINED_OBJECT);
    ret = executeCommand(g_command, expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 015.导入policy.gmjson中process不在白名单中的process，权限check时，返回指定错误码
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_015)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018002");
    int ret = 0;
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_015.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s -d", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_UNDEFINED_OBJECT);
    ret = executeCommand(g_command, expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 016.导入policy.gmjson中privs_type包含无效枚举
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_016)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_016.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(g_command, expectErrorCode);
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 017.导入policy.gmjson中name设置128字符及超过128字符    备注：超过128建表就失败
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_017)
{
    int ret = 0;
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);

    // 日志截断
    AW_ADD_TRUNCATION_WHITE_LIST(1, "len e");

    char *labelJson1 = NULL;
    const char *labelName1 = "T39_all_"
                             "typexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    const char *labelName2 = "T39_all_"
                             "typexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "T39_all_"
                             "typexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "T39_all_"
                             "typexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "T39_all_"
                             "typexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                             "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    readJanssonFile("schemaFile/VertexLabel_128.gmjson", &labelJson1);
    ASSERT_NE((void *)NULL, labelJson1);
    ret = GmcCreateVertexLabel(g_stmt, labelJson1, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson1);
    char *labelJson2 = NULL;
    readJanssonFile("schemaFile/VertexLabel_129.gmjson", &labelJson2);
    ASSERT_NE((void *)NULL, labelJson2);
    ret = GmcCreateVertexLabel(g_stmt, labelJson2, config_json);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    free(labelJson2);

    // len=128
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_017_128.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // len=129
    const char *obj_policy_file2 = "schemaFile/gmrule_test_policy/policy_017_129.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.导入policy.gmjson中user重名user，process不同   注：与友健对齐，rows与object_privilege_config  len一致
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_018)
{
    int ret = 0;
    // 导入一个root.pro1
    const char *allow_list_file = "schemaFile/allow_list/gmrule_allow_list_018.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 导入root.ObjPrivsGmrule和root.pro1的对象权限
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_018.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 019.导入policy.gmjson中user重名group，process不同
// 备注:C10B050不支持给group+process导权限，详见：DTS20210716085XN1P0D00
// 501.3 新增需求特性支持创建group+process赋权
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_019)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    // 导入root.ObjPrivsGmrule和root.pro0的对象权限
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_019.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    const char *obj_policy_file2 = "schemaFile/gmrule_test_policy/policy_019.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 020. 多次导入同一用户对象权限，导入的对象权限个数不一致
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_020)
{
    int ret = 0;
    //["INSERT", "UPDATE", "REPLACE", "SELECT"]
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_020_01.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //["MERGE", "DELETE"]
    const char *obj_policy_file2 = "schemaFile/gmrule_test_policy/policy_020_02.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //["INSERT", "UPDATE", "DELETE", "REPLACE", "MERGE", "SELECT"]
    const char *obj_policy_file3 = "schemaFile/gmrule_test_policy/policy_020_03.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file3,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file3,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
// 021 对象权限配置文件缺少","
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_021)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_021.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "policy_021.gmpolicy nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);
}
// 022 对象权限配置文件缺少"“"
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_022)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_022.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "policy_022.gmpolicy nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);
}
// 023 对象权限配置文件不存在
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_023)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1013000");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_023.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "no such file or directory: schemaFile/gmrule_test_policy/policy_023.gmpolicy, ret "
                                    "= 1013000");  // 错误码适配，更准确文件还是目录的缺失
    EXPECT_EQ(GMERR_OK, ret);
}
// 024 对象权限配置文件格式错误
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_024)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1013000");
    int ret = 0;
    const char *obj_policy_file1 = "./ObjPrivsGmrule";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    // ret = executeCommand(g_command, "GMDBV5/test/sdv/testcases/10_Security/004_ObjPrivs/ObjPrivsGmrule is not a
    // standard json file",expectErrorCode); EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "policy suffix from file ./ObjPrivsGmrule must be .gmpolicy, ret = 1013000");
    EXPECT_EQ(GMERR_OK, ret);
}
// 025 白名单配置文件缺少","
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_025)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_025.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "policy_025.gmuser nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);
}
// 026 白名单配置文件缺少","
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_026)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004006");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_026.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "policy_026.gmuser nonstandard json file");
    EXPECT_EQ(GMERR_OK, ret);
}
// 027 白名单配置文件不存在
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_027)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1013000");
    int ret = 0;
    const char *obj_policy_file1 = "schemaFile/gmrule_test_policy/policy_027.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // ret = executeCommand(g_command, "schemaFile/gmrule_test_policy/policy_027.config does not exist or you have no
    // operation permission. ret = 13000"); EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(
        g_command, "no such file or directory", "schemaFile/gmrule_test_policy/policy_027.gmuser, ret = 1013000");
    EXPECT_EQ(GMERR_OK, ret);  // 导入函数的逻辑复用policy的
}
// 028 白名单配置文件格式错误
TEST_F(SEC_004_ObjPrivsGmrule, SEC_004_ObjPrivsGmrule_028)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1013000");
    int ret = 0;
    const char *obj_policy_file1 = "./ObjPrivsGmrule";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    char expectErrorCode[64] = {0};
    sprintf(expectErrorCode, "%d", GMERR_INVALID_JSON_CONTENT);
    // ret = executeCommand(g_command, "GMDBV5/test/sdv/testcases/10_Security/004_ObjPrivs/ObjPrivsGmrule is not a
    // standard json file",expectErrorCode); EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "white list suffix from file ./ObjPrivsGmrule must be .gmuser, ret = 1013000");
    EXPECT_EQ(GMERR_OK, ret);
}
