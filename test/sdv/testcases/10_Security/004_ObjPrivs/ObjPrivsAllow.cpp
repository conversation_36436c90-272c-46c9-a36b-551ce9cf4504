/*****************************************************************************
 Description  : 宽容模式下对象权限check
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/07/13
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "ObjPrivsTest.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};
/***************订阅相关**********************/
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;
char *g_sub_info = NULL;
int g_data_num = 100;
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

class SEC_004_ObjPrivsAllow : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        printf("[INFO] ObjPrivsAllow Start.\n");
        system("sh $TEST_HOME/tools/stop.sh -f");
        //权限校验改为宽容模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=1\"");
        //拉起server
        system("sh $TEST_HOME/tools/start.sh");
        //环境初始化
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schemaFile/config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        
    }
    static void TearDownTestCase()
    {
        //恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_004_ObjPrivsAllow::SetUp()
{
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    // 断连报错
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void SEC_004_ObjPrivsAllow::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] ObjPrivsAllow End.\n");
}

// 001.宽容模式下，未赋对象权限，对vertex进行DML操作预期成功，有日志打印
TEST_F(SEC_004_ObjPrivsAllow, SEC_004_ObjPrivsAllow_001)
{
    int ret = 0;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    for (int i = 0; i < g_data_num; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < g_data_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < g_data_num; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < g_data_num; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_data_num; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.宽容模式下，未赋对象权限，下发订阅关系，操作预期成功，有日志打印
TEST_F(SEC_004_ObjPrivsAllow, SEC_004_ObjPrivsAllow_002)
{
    int ret = 0;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;

    int userDataIdx = 0;
    g_sub_info = NULL;
    readJanssonFile("schemaFile/NormalSubinfo.gmjson", &g_sub_info);
    ASSERT_NE((void *)NULL, g_sub_info);
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //建表
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // open
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // create sub
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    for (int i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    free(g_sub_info);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.宽容模式下，未赋对象权限，对kv进行DML操作预期成功，有日志打印
TEST_F(SEC_004_ObjPrivsAllow, SEC_004_ObjPrivsAllow_003)
{
    int ret = 0;
    char key[128] = "zhangsan";
    const char *g_kv_table_name = "kv_01";
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    //建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    // set
    for (int i = 0; i < g_data_num; i++) {
        sprintf(key, "zhangsan_%d", i);
        int value = i;
        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_kv(g_kv_table_name, g_data_num);

    // delete
    for (int i = 0; i < g_data_num; i++) {
        sprintf(key, "zhangsan_%d", i);
        ret = GmcKvRemove(g_stmt, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan
    scan_kv(g_kv_table_name, 0);

    // ret = GmcCloseKvTable(g_stmt);
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
}

// 004.宽容模式下，未赋对象权限，开始对账操作预期成功，有日志打印
TEST_F(SEC_004_ObjPrivsAllow, SEC_004_ObjPrivsAllow_004)
{
    int ret = 0;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_data_num; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // check account
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, GMC_FULL_TABLE);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.宽容模式下，未赋insert对象权限，调用gmimport工具导入数据,预期有日志打印
TEST_F(SEC_004_ObjPrivsAllow, SEC_004_ObjPrivsAllow_005)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    //导入
    printf("****************gmimport vertex:%d****************\n", g_data_num);
    const char *filePath = "schemaFile/T39_all_type.vertexdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -s %s", g_toolPath,
        g_normal_vertexlabel_name, filePath, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // scan
    // scan
    // scan_verex(g_stmt, g_normal_vertexlabel_name, g_data_num); // 暂时使用以下方式查询
    scan_verex_test(g_stmt, g_normal_vertexlabel_name, g_data_num);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.宽容模式下，未赋select对象权限，调用gmexport工具导入数据，预期有日志打印
TEST_F(SEC_004_ObjPrivsAllow, SEC_004_ObjPrivsAllow_006)
{
    int ret = 0;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_data_num; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("****************gmexport vertex:%d****************\n", g_data_num);
    const char *filePath = "schemaFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -s %s", g_toolPath,
        g_normal_vertexlabel_name, filePath, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "export file successfully");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}
