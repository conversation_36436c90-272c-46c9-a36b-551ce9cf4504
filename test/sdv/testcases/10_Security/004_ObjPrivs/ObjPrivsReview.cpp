/*****************************************************************************
 Description  : Vertex对象权限check
 Notes        : 补充deltaStorePolicyMode参数用例及异步批量操作
                deltaStorePolicyMode参数在服务端生效，即merge时的权限校验开关，实际客户端是否返回错误码取决于userPolicyMode
                --2021/08/26 确认人：王杰/汤璐
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/08/26
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "ObjPrivsTest.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";
int g_count = 100;
const char *g_kv_table_name = "kv_01";
#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class SEC_004_ObjPrivsReview : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        readJanssonFile("schemaFile/config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        free(config_json);
        free(normal_vertexlabel_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_004_ObjPrivsReview::SetUp()
{
    printf("[INFO] ObjPrivsReview Start.\n");
    AW_CHECK_LOG_BEGIN();
}

void SEC_004_ObjPrivsReview::TearDown()
{
    AW_CHECK_LOG_END();
    printf("[INFO] ObjPrivsReview End.\n");
}

// 009.授权select权限前，进行哈希扫描，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_009)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int expectAffectRows = 1;
    int count = 100;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant scan by localhash
    for (int i = 0; i < count; i++) {
        int64_t sk = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &sk, sizeof(int64_t));
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
        ret = GmcSetIndexKeyName(g_stmt, g_normal_sk_name);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    }

    // grant select
    const char *obj_policy_file2 = "schemaFile/review_policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // after grant scan by localhash
    for (int i = 0; i < count; i++) {
        int64_t sk = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_sk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &sk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 010.授权select权限前，进行localkey范围扫描，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_010)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int expectAffectRows = 1;
    int count = 100;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/LocalKeyVertexLabel.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant scan by localkey
    const char *localKeyName = "local_key";
    uint64_t l_val = 10;
    uint64_t r_val = 20;
    unsigned int ValsNum = 1;

    GmcPropValueT leftKeyProps[ValsNum];
    leftKeyProps[0].type = GMC_DATATYPE_UINT64;
    leftKeyProps[0].value = &l_val;
    leftKeyProps[0].size = sizeof(l_val);
    GmcPropValueT rightKeyProps[ValsNum];
    rightKeyProps[0].type = GMC_DATATYPE_UINT64;
    rightKeyProps[0].value = &r_val;
    rightKeyProps[0].size = sizeof(r_val);
    GmcRangeItemT items[ValsNum];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcSetKeyRange(g_stmt, items, ValsNum);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    ret = GmcSetIndexKeyName(g_stmt, localKeyName);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");

    // grant select
    const char *obj_policy_file2 = "schemaFile/review_policy/objSelectVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // after grant scan by localkey
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt, items, ValsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, localKeyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(r_val - l_val, cnt);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 011.授权insert权限前，进行异步批量insert操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_011)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // async batch insert
    AsyncUserDataT data = {0};
    data.lastError = g_reviewPrivError;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    // grant insert
    const char *obj_policy_file2 = "schemaFile/review_policy/objInsertVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // async batch insert
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // batch insert Vertex
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch insert vertex:%d\n", count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.授权update权限前，进行异步批量update操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_012)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // async batch update
    AsyncUserDataT data = {0};
    data.lastError = g_reviewPrivError;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    // grant update
    const char *obj_policy_file2 = "schemaFile/review_policy/objUpdateVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // async batch update
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch update Vertex
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch update vertex:%d\n", count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 013.授权merge权限前，进行异步批量merge操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_013)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // async batch merge
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    data.lastError = g_reviewPrivError;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        int pk = i + count;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    // grant merge
    const char *obj_policy_file2 = "schemaFile/review_policy/objMergeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // async batch merge
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch merge Vertex
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        int pk = i + count;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_F7(g_stmt_async, i+count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch merge vertex:%d\n", count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 014.授权replace权限前，进行异步批量replace操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_014)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // async batch replace
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    data.lastError = g_reviewPrivError;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i + count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    // grant replace
    const char *obj_policy_file2 = "schemaFile/review_policy/objReplaceVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // async batch repalce
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch replace Vertex
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i + count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch replace vertex:%d\n", count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 015.授权delete权限前，进行异步批量delete操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_015)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // async batch delete
    AsyncUserDataT data = {0};
    data.lastError = g_reviewPrivError;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    // grant delete
    const char *obj_policy_file2 = "schemaFile/review_policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // async batch delete
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    // batch delete Vertex
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_F7(g_stmt_async, i + count);
        set_VertexProperty_F9(g_stmt_async, i + count);
        set_VertexProperty(g_stmt_async, i + count);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(count, data.totalNum);
    EXPECT_EQ(count, data.succNum);
    printf("[INFO] batch delete vertex:%d\n", count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 016.KV,授权replace权限前，进行异步批量set操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_016)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysKV.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    char key[128] = "zhangsan";

    //建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_kv_table_name);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoReplaceKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // set
    AsyncUserDataT data = {0};
    data.lastError = g_kvReviewPrivError;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_count; i++) {
        sprintf(key, "zhangsan_%d", i);
        int value = i;
        ret = GmcKvInputToStmt(g_stmt_async, key, strlen(key), &value, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ASSERT_EQ(g_count, data.totalNum);
    ASSERT_EQ(0, data.succNum);

    // scan
    scan_kv(g_kv_table_name, 0);

    // grant replace
    const char *obj_policy_file2 = "schemaFile/review_policy/objReplaceKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // set
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_count; i++) {
        sprintf(key, "zhangsan_%d", i);
        int value = i;
        ret = GmcKvInputToStmt(g_stmt_async, key, strlen(key), &value, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(g_count, data.totalNum);
    ASSERT_EQ(g_count, data.succNum);

    // scan
    scan_kv(g_kv_table_name, g_count);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.KV,授权delete权限前，进行异步批量delete操作，预期失败，授权后，预期成功
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_017)
{
    int ret = 0;
    //修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    //拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    //初始化
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/review_policy/sysKV.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //建链
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    //建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_kv_table_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/review_policy/objNoDeleteKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // set
    char key[128] = "zhangsan";
    for (int i = 0; i < g_count; i++) {
        sprintf(key, "zhangsan_%d", i);
        int value = i;
        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    data.lastError = g_kvReviewPrivError;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_count; i++) {
        sprintf(key, "zhangsan_%d", i);
        ret = GmcKvInputToStmt(g_stmt_async, key, strlen(key), NULL, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ASSERT_EQ(g_count, data.totalNum);
    ASSERT_EQ(0, data.succNum);

    // scan
    scan_kv(g_kv_table_name, g_count);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/review_policy/objDeleteKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // delete
    data = {0};
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < g_count; i++) {
        sprintf(key, "zhangsan_%d", i);
        int value = i;
        ret = GmcKvInputToStmt(g_stmt_async, key, strlen(key), NULL, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(g_count, data.totalNum);
    ASSERT_EQ(g_count, data.succNum);

    // scan
    scan_kv(g_kv_table_name, 0);
    GmcBatchDestroy(batch);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcDetachAllShmSeg();
    testEnvClean();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// sys
void *thread_privs_01(void *arg)
{
    int count_test = *(int *)arg;
    int data_num = 1;

    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);  // 重新建连才可获取系统权限的变更
    EXPECT_EQ(GMERR_OK, ret);

    int expectAffectRows = 1;
    for (int i = count_test * data_num; i < count_test * data_num + data_num; i++) {
        // insert
        ret = testGmcPrepareStmtByLabelName(stmt1, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_F7(stmt1, i);
        set_VertexProperty_F9(stmt1, i);
        set_VertexProperty(stmt1, i);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);

        // update
        ret = testGmcPrepareStmtByLabelName(stmt1, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t pk = i;
        int NewVal = i + count_test * data_num + data_num;
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(stmt1, NewVal);
        ret = GmcSetIndexKeyName(stmt1, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);

        // delete
        ret = testGmcPrepareStmtByLabelName(stmt1, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        pk = i;
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// obj
void *thread_privs_02(void *arg)
{
    int count_test = *(int *)arg + 1;
    int data_num = 1;
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int expectAffectRows;
    for (int i = count_test * data_num; i < count_test * data_num + data_num; i++) {
        // insert
        ret = testGmcPrepareStmtByLabelName(stmt1, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_F7(stmt1, i);
        set_VertexProperty_F9(stmt1, i);
        set_VertexProperty(stmt1, i);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        expectAffectRows = 1;
        ret = testGmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);

        // replace
        ret = testGmcPrepareStmtByLabelName(stmt1, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_F7(stmt1, i);
        set_VertexProperty_F9(stmt1, i + count_test * data_num + data_num);
        set_VertexProperty(stmt1, i + count_test * data_num + data_num);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        expectAffectRows = 2;
        ret = testGmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);

        // delete
        ret = testGmcPrepareStmtByLabelName(stmt1, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        expectAffectRows = 1;
        ret = testGmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 018.多线程分别授予系统权限和对象权限且DML操作(写入、更新、删除))
TEST_F(SEC_004_ObjPrivsReview, SEC_004_ObjPrivsReview_018)
{
    // 修改启动参数
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    // 拉起server
    system("sh $TEST_HOME/tools/start.sh -f");
    // 恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 初始化
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    char *test_schema = NULL;
    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    // 导入白名单
    const char *allow_list_file = "schemaFile/allow_list/review_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/test_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 对同一张表：一条线程授予系统权限且DML操作、另一条现场授予对象权限且DML操作
    // T39_all_type
    ret = GmcCreateVertexLabel(g_stmt, test_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);

    // 导入vertex的系统权限update_any/insert_any/delete_any
    const char *sys_policy_file2 = "schemaFile/test_policy/sysVertex_001.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file2,
        g_connServer);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入vertex的对象权限replace/insert/delete
    const char *obj_policy_file3 = "schemaFile/test_policy/allObjVertex_test.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file3,
        g_connServer);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    int tdNum = 1;
    int index[32] = {0};
    pthread_t vertexlabel_01[tdNum];
    pthread_t vertexlabel_02[tdNum];
    for (int i = 0; i < tdNum; i++) {
        index[i] = i;
        ret = pthread_create(&vertexlabel_01[i], NULL, thread_privs_01, (void *)&index[i]);  // 系统权限写入更新删除
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&vertexlabel_02[i], NULL, thread_privs_02, (void *)&index[i]);  // 对象权限写入更新删除
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(vertexlabel_01[i], NULL);
        pthread_join(vertexlabel_02[i], NULL);
    }

    const char *obj_policy_file1 = "schemaFile/test_policy/allObjVertex_test.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    
    GmcDetachAllShmSeg();
    testEnvClean();
}
