[{"name": "root_to_list_2_1", "source_vertex_label": "root", "dest_vertex_label": "list_2_1", "source_node_path": "/con_2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_6", "source_vertex_label": "root", "dest_vertex_label": "list_6", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_7", "source_vertex_label": "root", "dest_vertex_label": "list_7", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_8", "source_vertex_label": "root", "dest_vertex_label": "list_8", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "list_8_to_list_8_1", "source_vertex_label": "list_8", "dest_vertex_label": "list_8_1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_9", "source_vertex_label": "root", "dest_vertex_label": "list_9", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_10", "source_vertex_label": "root", "dest_vertex_label": "list_10", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_13_1_1", "source_vertex_label": "root", "dest_vertex_label": "list_13_1_1", "source_node_path": "/choice_13/case_13_1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_16", "source_vertex_label": "root", "dest_vertex_label": "list_16", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "list_16_to_list_16_1", "source_vertex_label": "list_16", "dest_vertex_label": "list_16_1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]