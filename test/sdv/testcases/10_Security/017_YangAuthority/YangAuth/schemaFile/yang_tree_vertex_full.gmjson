[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"name": "P1", "type": "uint32", "nullable": true}, {"name": "P2", "type": "uint32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "uint32", "nullable": true}, {"name": "P5", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "con_1_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}, {"type": "container", "name": "con_2", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}, {"type": "container", "name": "con_3", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "choice", "name": "choice_3_1", "fields": [{"type": "case", "name": "case_3_1_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}]}, {"type": "container", "name": "con_4", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}, {"type": "container", "name": "con_5", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}, {"type": "choice", "name": "choice_11", "fields": [{"type": "case", "name": "case_11_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}, {"type": "case", "name": "case_11_2", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}, {"type": "choice", "name": "choice_12", "fields": [{"type": "case", "name": "case_12_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "con_12_1_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}]}, {"type": "choice", "name": "choice_13", "fields": [{"type": "case", "name": "case_13_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}, {"type": "choice", "name": "choice_14", "fields": [{"type": "case", "name": "case_14_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}, {"type": "choice", "name": "choice_15", "fields": [{"type": "case", "name": "case_15_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}], "keys": [{"node": "root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_2_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "list_2_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_6", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "con_6_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"fields": ["PID", "F0"], "node": "list_6", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_7", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "choice", "name": "choice_7_1", "fields": [{"type": "case", "name": "case_7_1_1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}]}], "keys": [{"fields": ["PID", "F0"], "node": "list_7", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_8", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "list_8", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_8_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "list_8_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_9", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "list_9", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_10", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "list_10", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_13_1_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "list_13_1_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_16", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "F0", "F1"], "node": "list_16", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_16_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "F0", "F1"], "node": "list_16_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]