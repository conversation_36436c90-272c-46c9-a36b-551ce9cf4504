/* ****************************************************************************
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: EdgeAuth.cpp
 * Description: 支持Yang权限控制特性测试
 * Author: wuxch
 * Create: 2023-02-09
* *****************************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "../authTest.h"
#include "OtherAuthTest.h"
char *g_namespace = (char *)"nspEdge";
char g_labelName2[64] = "simpleLabel2";
char *g_edgeLabel = (char *)"edgeLabel";
char g_edgeLabelConfig[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
class EdgeAuth : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" ");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = 0;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = 0;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void EdgeAuth::SetUp()
{
    int ret = 0;
    g_conn = NULL;
    g_stmt = NULL;
    char command[256];
    char *expectValue = (char *)"successfully";
    char *allowListFile = (char *)"./schemaFile/allow_list/edgeAuthAllowList.gmuser";
    char *policyFile = (char *)"./schemaFile/gmpolicy_list/edgePolicyNeed.gmpolicy";
    (void)snprintf(command, 256, "%s/gmrule -c import_allowlist -f %s -s %s", g_toolPath, allowListFile, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]ALLOWLIST_CMD: %s", command);
    ret = executeCommand(command, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PolicyGmruleImport(policyFile, expectValue); // 导入必须的系统权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespace(g_stmt, g_namespace, g_nsUserName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void EdgeAuth::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    char command[256];
    char *expectValue = (char *)"successfully";
    char *fileName = (char *)"./schemaFile/allow_list/edgeAuthAllowList.gmuser";
    char *policyFile = (char *)"./schemaFile/gmpolicy_list/edgePolicyNeed.gmpolicy";

    ret = GmcDropNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PolicyGmruleRevoke(policyFile, expectValue); // 移除系统权限
    (void)snprintf(command, 256, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, fileName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "[INFO]REMOVE_ALLOWLIST_CMD: %s", command);
    ret = executeCommand(command, "successfully"); // 移除白名单
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.nsp下创建edgeLabel权限check
TEST_F(EdgeAuth, SEC_017_EdgeAuth_001)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *expectValue1 = (char *)"successfully";
    char *expectValue2 = (char *)"success: 1, warning: 0";
    char fileName1[128] = "./schemaFile/gmpolicy_list/edgePolicy001_1.gmpolicy";
    char fileName2[128] = "./schemaFile/gmpolicy_list/edgePolicy001_2.gmpolicy";
    char fileName3[128] = "./schemaFile/gmpolicy_list/edgePolicy001_3.gmpolicy";
    char fileName4[128] = "./schemaFile/gmpolicy_list/edgePolicy001_4.gmpolicy";
    char schemaPath[128] = "./schemaFile/lableJson/vertexLabel1.gmjson";
    char schemaPath2[128] = "./schemaFile/lableJson/vertexLabel2.gmjson";
    char schemaPath3[128] = "./schemaFile/lableJson/edgeSchema.gmjson";
    // 导入nsp权限除create权限
    PolicyGmruleImport(fileName1, expectValue1, expectValue2);
    PolicyGmruleImport(fileName2, expectValue1, expectValue2);
    // 使用ns
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath, g_labelName, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath2, g_labelName2, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建edge表
    ret = TestCreateEdgeLabel(g_stmt, schemaPath3, g_edgeLabel, g_edgeLabelConfig, GMERR_INSUFFICIENT_PRIVILEGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 导入nsp的create权限
    PolicyGmruleImport(fileName4, expectValue1, expectValue2);
    // 创建edge表
    ret = TestCreateEdgeLabel(g_stmt, schemaPath3, g_edgeLabel, g_edgeLabelConfig, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropEdgeLabel(g_stmt, g_edgeLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 移除nsp所有对象权限
    PolicyGmruleRevoke(fileName3, expectValue1, expectValue2);
    // 移除系统vertexlabel create权限
    PolicyGmruleRevoke(fileName2, expectValue1, expectValue2);
}

// 002.nsp下删除edgeLabel权限check
TEST_F(EdgeAuth, SEC_017_EdgeAuth_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    char *expectValue1 = (char *)"successfully";
    char *expectValue2 = (char *)"success: 1, warning: 0";
    char fileName1[128] = "./schemaFile/gmpolicy_list/edgePolicy002_1.gmpolicy";
    char fileName3[128] = "./schemaFile/gmpolicy_list/edgePolicy001_3.gmpolicy";
    char fileName4[128] = "./schemaFile/gmpolicy_list/edgePolicy002_4.gmpolicy";
    char schemaPath[128] = "./schemaFile/lableJson/vertexLabel1.gmjson";
    char schemaPath2[128] = "./schemaFile/lableJson/vertexLabel2.gmjson";
    char schemaPath3[128] = "./schemaFile/lableJson/edgeSchema.gmjson";
    // 导入nsp权限除drop权限
    PolicyGmruleImport(fileName1, expectValue1, expectValue2);
    // 使用ns
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath, g_labelName, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath2, g_labelName2, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建edge表
    ret = TestCreateEdgeLabel(g_stmt, schemaPath3, g_edgeLabel, g_edgeLabelConfig, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, g_edgeLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 导入nsp的drop权限
    PolicyGmruleImport(fileName4, expectValue1, expectValue2);
    // 删除edge表
    ret = GmcDropEdgeLabel(g_stmt, g_edgeLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 移除nsp所有对象权限
    PolicyGmruleRevoke(fileName3, expectValue1, expectValue2);
}

// 003.nsp下edgeLabel表get权限check
TEST_F(EdgeAuth, SEC_017_EdgeAuth_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    char *expectValue1 = (char *)"successfully";
    char *expectValue2 = (char *)"success: 1, warning: 0";
    char fileName1[128] = "./schemaFile/gmpolicy_list/edgePolicy003_1.gmpolicy";
    char fileName2[128] = "./schemaFile/gmpolicy_list/edgePolicy003_2.gmpolicy";
    char fileName3[128] = "./schemaFile/gmpolicy_list/edgePolicy001_3.gmpolicy";
    char schemaPath[128] = "./schemaFile/lableJson/vertexLabel1.gmjson";
    char schemaPath2[128] = "./schemaFile/lableJson/vertexLabel2.gmjson";
    char schemaPath3[128] = "./schemaFile/lableJson/edgeSchema.gmjson";
    // 导入nsp权限除get权限
    PolicyGmruleImport(fileName1, expectValue1, expectValue2);
    // 使用ns
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath, g_labelName, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath2, g_labelName2, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *edgelabe = NULL;
    // 创建edge表
    ret = TestCreateEdgeLabel(g_stmt, schemaPath3, g_edgeLabel, g_edgeLabelConfig, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //构造src和dst点
    ret = GmcOpenEdgeLabelByName(g_stmt, g_edgeLabel, &edgelabe);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 导入nsp的get权限
    PolicyGmruleImport(fileName2, expectValue1, expectValue2);
    // 创建edge表
    ret = GmcOpenEdgeLabelByName(g_stmt, g_edgeLabel, &edgelabe);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(g_stmt, edgelabe);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropEdgeLabel(g_stmt, g_edgeLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 移除nsp所有对象权限
    PolicyGmruleRevoke(fileName3, expectValue1, expectValue2);
}

// 004.nsp下select权限check，邻点查询
TEST_F(EdgeAuth, SEC_017_EdgeAuth_004)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 10;
    char *expectValue1 = (char *)"successfully";
    char *expectValue2 = (char *)"success: 1, warning: 0";
    char fileName1[128] = "./schemaFile/gmpolicy_list/edgePolicy004_1.gmpolicy";
    char fileName2[128] = "./schemaFile/gmpolicy_list/edgePolicy004_2.gmpolicy";
    char fileName3[128] = "./schemaFile/gmpolicy_list/edgePolicy001_3.gmpolicy";
    char schemaPath[128] = "./schemaFile/lableJson/vertexLabel1.gmjson";
    char schemaPath2[128] = "./schemaFile/lableJson/vertexLabel2.gmjson";
    char schemaPath3[128] = "./schemaFile/lableJson/edgeSchema.gmjson";
    // 导入nsp权限除select权限
    PolicyGmruleImport(fileName1, expectValue1, expectValue2);
    // 使用ns
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath, g_labelName, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCreateVertexLabel(g_stmt, schemaPath2, g_labelName2, g_edgeLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *edgelabe = NULL;
    // 创建edge表
    ret = TestCreateEdgeLabel(g_stmt, schemaPath3, g_edgeLabel, g_edgeLabelConfig, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    TestSimpleT1InsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true);
    TestSimpleT1InsertOrReplace(g_stmt, g_labelName2, startValue, endValue, GMC_OPERATION_INSERT, true);
    // 查询邻点
    GmcStmtT *stmt1;
    ret = GmcAllocStmt(g_conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // fetch Neighbor
    ret = testGmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcSetIndexKeyName(stmt1, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcDirectFetchNeighborBegin(stmt1, g_edgeLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    // 导入nsp的select权限
    PolicyGmruleImport(fileName2, expectValue1, expectValue2);
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt1, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSimpleT1PkIndexSet(stmt1, 1);
    ret = GmcDirectFetchNeighborBegin(stmt1, g_edgeLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(stmt1, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSimpleT1UpdateGetPropertyByName(stmt1, 1, true);
    TestSimpleT1GetLpmProperty(stmt1, 1);
    ret = GmcFetch(stmt1, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    ret = GmcDirectFetchNeighborEnd(stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(stmt1);
    // 删表
    ret = GmcDropGraphLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 移除nsp所有对象权限
    PolicyGmruleRevoke(fileName3, expectValue1, expectValue2);
}
