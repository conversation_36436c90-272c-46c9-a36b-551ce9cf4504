/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :支持Yang权限控制其他认证测试头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/02/03
**************************************************************************** */
#ifndef OTHERAUTH_TEST_H
#define OTHERAUTH_TEST_H
#include <errno.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <stdarg.h>
#include <signal.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
const char *g_nsUserName = (const char *)"root";
char *g_labelName = (char *)"simpleLabel";
char *g_labelConfig = (char *)R"({"supportUndeterminedLength":true})";
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define SIMPLE_LABEL_FIXED_SIZE   9


int TestCreateVertexLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = g_labelConfig)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestCreateEdgeLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson, int32_t expectValue)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropEdgeLabel(stmt, labelName);
    ret = GmcCreateEdgeLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(expectValue, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create edgeLabel %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, char *labelName = NULL, char *uWay = (char *)"online",
                          char *nsName = g_testNameSpace)
{
    char *schema = NULL;
    readJanssonFile(schemaPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay,
            nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    free(schema);
    schema = NULL;
    return ret;
}

void TestSimpleT1SetPk(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1HashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1SetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1UpdateSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

void TestSimpleT1InsertOrReplace(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                 GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1SetProperty(stmt, i, isDefaultValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT1MergeOrUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                               GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0,
                               int32_t expectValue = GMERR_OK, int32_t expectAffactRows = 1)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        for (int i = startValue; i < endValue; i++) {
            TestSimpleT1PkIndexSet(stmt, i);
            TestSimpleT1SetProperty(stmt, i, isDefaultValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(expectValue, ret);
            ret = TestGetAffactRows(stmt, expectAffactRows);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    } else {
            for (int i = startValue; i < endValue; i++) {
            TestSimpleT1HashclusterIndexSet(stmt, i);
            TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(expectValue, ret);
            if (ret != GMERR_OK) {
                ret = testGmcGetLastError();
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            ret = TestGetAffactRows(stmt, expectAffactRows);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestSimpleT1UpdateBatch(GmcConnT *conn, GmcStmtT *stmt, int64_t startValue, int64_t endValue,
                             bool isDefaultValue = true, int64_t updateValue = 0, uint32_t expectNum = 0,
                             int32_t expectValue = GMERR_OK)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = expectNum;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        for (int i = startValue; i < endValue; i++) {
        TestSimpleT1HashclusterIndexSet(stmt, i);
        TestSimpleT1UpdateSetProperty(stmt, i, isDefaultValue);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(expectValue, ret);
    if (ret != GMERR_OK) {
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, totalNum);
    AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
    AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
    AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
    GmcBatchDestroy(batch);
}

void TestSimpleT1GetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestSimpleT1UpdateGetPropertyByName(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}


void TestSimpleTWholeRead(GmcStmtT *stmt, int32_t expAffectRows, bool isDefaultValue, int32_t expectValue,
                          int64_t updateValue = 0)
{
    int ret = 0;
    bool isFinish = false;
    uint32_t fetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(expectValue, ret);
    if (ret == GMERR_OK) {
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            fetchNum++;
            int64_t f0Value = 0;
            bool isNull = false;
            ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestSimpleT1UpdateGetPropertyByName(stmt, f0Value + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, f0Value);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
}

void SubLabelCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                AW_FUN_Log(LOG_INFO, "<---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "<---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

#endif
