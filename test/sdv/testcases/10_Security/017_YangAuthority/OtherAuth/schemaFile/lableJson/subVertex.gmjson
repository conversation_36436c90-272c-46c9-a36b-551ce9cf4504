{"name": "vertexLabelSub", "label_name": "simpleLabel", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["key", "new object"]}, {"type": "update", "msgTypes": ["key", "old object", "new object"]}, {"type": "merge insert", "msgTypes": ["key", "old object", "new object"]}, {"type": "merge update", "msgTypes": ["key", "old object", "new object"]}, {"type": "delete", "msgTypes": ["key", "old object"]}, {"type": "replace insert", "msgTypes": ["key", "old object", "new object"]}, {"type": "replace update", "msgTypes": ["key", "old object", "new object"]}, {"type": "age", "msgTypes": ["key", "old object"]}], "is_path": false, "retry": true}