#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define GMLOG_MAX_PATH 256
#define GMLOG_MAX_CMD 512
#define TIME_MAX_CMD 80
char const *cfglogName = "logLocalFoldThreshold";
int recoverSvalue = 1;
int changeSvalue = 10;

char const *cfglogName2 = "logGlobalFoldThreshold";
int recoverSvalue2 = 50;
int changeSvalue2 = 198;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char g_time_start[80];
char g_time_end[80];
char time_start[80];
char time_end[80];
int ret;
char log_cmd_start[100] = {0};
char log_cmd_end[GMLOG_MAX_PATH] = {0};
//适配新的异步框架
//客户端异步
GmcConnT *g_conn_async = NULL;
GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;
void getTime_Test();
int check_value(const char *cmd, const char *processName, const char *localSwitch, const char *localLogLevel,
    const char *stlmSwitch);
//定义Audit_gmlog_001类 继承自testing
class Audit_gmlog_001 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"logFoldMode=3\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"longProcTimeThreshold=0\""); // logLengthMax
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"logLocalFoldPeriod=1\"");
#ifdef ENV_RTOSV2X
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath,
                 g_connServer, cfglogName, changeSvalue);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath,
                 g_connServer, cfglogName2, changeSvalue2);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 4 mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
#endif
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        getTime_Test();
        snprintf(time_start, 80, "%s", g_time_start);
        if (g_runMode == 0) {
            printf("start .");
        } else {
            snprintf(log_cmd_start, 100, "gmlog -on hpk");
            system(log_cmd_start);
        }
        //适配新的异步框架
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        char logstPath[GMLOG_MAX_PATH] = {0};
        if (g_runMode == 0) {
            if (getcwd(logstPath, GMLOG_MAX_PATH) != NULL) {
                // truncate -s 0
                snprintf(log_cmd_end, GMLOG_MAX_PATH,
                    " echo -n "
                    " > %s/log/secure/audit.1.log",
                    logstPath);
                ret = system(log_cmd_end);
                // EXPECT_EQ(GMERR_OK, ret);
            }
        } else {
            snprintf(log_cmd_end, GMLOG_MAX_PATH,
                " echo -n "
                " >   /var/log/GMDBV5/log/secure/audit.1.log");
            ret = system(log_cmd_end);
            // EXPECT_EQ(GMERR_OK, ret);
        }
        //适配新的异步框架
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        // 还原配置
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
#if defined(ENV_RTOSV2X)
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath,
                 g_connServer, cfglogName, recoverSvalue);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath,
                 g_connServer, cfglogName2, recoverSvalue2);
        system(g_command);
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
#else
#endif
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};
//  SetUp函数实现
void Audit_gmlog_001::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
}
// TearDown函数实现
void Audit_gmlog_001::TearDown()
{
    AW_CHECK_LOG_END();
}
//  check_value函数实现
int check_value(const char *cmd, const char *processName = NULL, const char *localSwitch = NULL,
    const char *localLogLevel = NULL, const char *stlmSwitch = NULL)
{
    //读取命令打屏信息
    char buf_ps[2048];
    FILE *ptr;
    char final[2048] = "Execute this command successfully.";  //成功执行此命令
    char line[22][2048];
    char buffer[2048];
    int index = 0;
    // popen 函数  comand 参数  必须为shell 命令 且  type 参数 必须为 r 或 w
    if ((ptr = popen(cmd, "r")) != NULL) {
        // 将shell命令执行得到的信息读取到buf_ps中
        while (fgets(buf_ps, 2048, ptr) != NULL) {
            //判断final 是否为 buf_ps的子串 若是返回首地址  否则返回null
            if (strstr(buf_ps, final)) {
                break;
            }
            buf_ps[strlen(buf_ps) - 1] = 0;
            //将buf_ps内容复制到buffer
            strcpy(buffer, buf_ps);
        }

        pclose(ptr);
        ptr = NULL;
    } else {
        printf("popen error/n");
        return 0;
    }
    // split buffer放到array
    char array[4][200] = {"NULL", "NULL", "NULL", "NULL"};
    char *temp;
    int column = 0;
    printf("Test  buffer  =%s\n", buffer);
    temp = strtok(buffer, "|");
    while (temp != NULL) {
        sprintf(array[column++], "%s ", temp);
        temp = strtok(NULL, "|");
    }
    printf("array is    %s\n", array[0]);
    // compare message
    if (strstr(array[0], processName) || strstr(array[0], localSwitch) || strstr(array[0], localLogLevel) ||
        strstr(array[0], stlmSwitch)) {
        return 0;
    } else {
        return 1;
    }
}
// 获取当前系统时间
void getTime_Test()
{
    if (g_runMode == 0) {
        time_t rawtime;
        struct tm *info;
        time(&rawtime);
        info = localtime(&rawtime);
        strftime(g_time_start, 80, "%Y-%m-%d-%H:%M:%S", info);
        sleep(1);
        time(&rawtime);
        info = localtime(&rawtime);
        strftime(g_time_end, 80, "%Y-%m-%d-%H:%M:%S", info);
        return;
    } else {
        time_t rawtime;
        struct tm *info;
        time(&rawtime);
        info = localtime(&rawtime);
        strftime(g_time_start, 80, "%Y-%m-%d %H:%M:%S", info);
        sleep(1);
        time(&rawtime);
        info = localtime(&rawtime);
        strftime(g_time_end, 80, "%Y-%m-%d %H:%M:%S", info);
        return;
    }
}
//函数内实现键连断链操作
void *log_thread_client_connect(void *args)
{
    getTime_Test();
    GmcConnT *conn_thr = (GmcConnT *)args;
    //同步键连
    int ret = testGmcConnect(&conn_thr);
    EXPECT_EQ(GMERR_OK, ret);
    //同步断连
    ret = testGmcDisconnect(conn_thr);
    EXPECT_EQ(GMERR_OK, ret);
    char cmd[GMLOG_MAX_CMD] = {0};
    FILE *fp;
    char buffer[2048];
    char final[2048] = "successfully";
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  root %s", stPath, g_time_end);
            fp = popen(cmd, "r");
            fgets(buffer, sizeof(buffer), fp);
            // printf("%s\n", buffer);
            if (strstr(buffer, final)) {
                ret = 0;
            } else {
                ret = 1;
            }
            pclose(fp);
            fp = NULL;
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
        }
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
    }
    return ((void *)0);
}
// 函数内实现日志查询功能
void *thread_Log_query(void *args)
{
    getTime_Test();
    GmcConnT *conn_thr = (GmcConnT *)args;
    //同步键连
    int ret = testGmcConnect(&conn_thr);
    EXPECT_EQ(GMERR_OK, ret);
    //同步断连
    ret = testGmcDisconnect(conn_thr);
    EXPECT_EQ(GMERR_OK, ret);
    char cmd[GMLOG_MAX_CMD] = {0};
    FILE *fp;
    char buffer[2048] = {0};
    char final[2048] = "successfully";
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log   root %s", stPath, g_time_end);
            printf("%s\n", cmd);
            EXPECT_TRUE(ret > 0);
        }
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s\n", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
        return ((void *)0);
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        return NULL;
    }
}
// 函数内开启ddl打印开关关闭
void *thread_DDL_gmlog_off(void *args)
{
    pthread_mutex_lock(&g_connLock);
    char const *g_typeName = "audit ddl off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDDL");
    EXPECT_EQ(GMERR_OK, ret);

    char const *g_typeName1 = "audit ddl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDDL");
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&g_connLock);
    return ((void *)0);
}
// 函数内开启ddl打印开关打开
void *thread_DDL_gmlog_on(void *args)
{
    pthread_mutex_lock(&g_connLock);
    char const *g_typeName = "audit ddl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDDL");
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&g_connLock);
    return ((void *)0);
}
// 函数内开启dcl打印开关
void *thread_DCL_gmlog_off(void *args)
{
    pthread_mutex_lock(&g_connLock);
    char const *g_typeName = "audit dcl off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);

    char const *g_typeName1 = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&g_connLock);
    return ((void *)0);
}
// 函数内开启dcl打印开关
void *thread_DCL_gmlog_on(void *args)
{
    pthread_mutex_lock(&g_connLock);
    char const *g_typeName = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&g_connLock);
    return ((void *)0);
}
// 函数内开启dml打印开关打开
void *thread_DML_gmlog_on(void *args)
{
    pthread_mutex_lock(&g_connLock);
    char const *g_typeName = "audit dml on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDML");
    EXPECT_EQ(GMERR_OK, ret);

    char const *g_typeName1 = "audit dml off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDML");
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&g_connLock);
    return ((void *)0);
}
// 函数内开启dml打印开关关闭
void *thread_DML_gmlog_off(void *args)
{
    pthread_mutex_lock(&g_connLock);
    char const *g_typeName = "audit dml off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDML");
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_unlock(&g_connLock);
    return ((void *)0);
}

/*****************************************************************************
 Description  : gmlog-s audit NUll
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_001)
{
    printf("**********************Audit_gmlog_001_001 begin********************\n");
    char const *g_typeName = "NULL";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s ", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s)");
    printf("ret= %d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s adddit ddl on
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_002)
{
    printf("**********************Audit_gmlog_001_002 begin********************\n");
    char const *g_typeName = "adddit ddl on";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s  ", g_toolPath, g_typeName);
        printf("%s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "Execute this command failed");
    printf("ret=%d\n", ret);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
}
/*****************************************************************************
 Description  : gmlog-s audit ddl oll
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_003)
{
    printf("**********************Audit_gmlog_001_003 begin********************\n");
    char const *g_typeName = "audit ddl oll";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "Execute this command failed");
    printf("ret=%d\n", ret);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
}
/*****************************************************************************
 Description  : gmlog-s audit ddl on dml on dcl on
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_004)
{
    printf("**********************Audit_gmlog_001_004 begin********************\n");
    char const *g_typeName = "audit ddl on dml on dcl on";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s", g_toolPath, g_typeName);
        printf("%s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s)");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit ddl on dml off
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_005)
{
    printf("**********************Audit_gmlog_001_005 begin********************\n");
    char const *g_typeName = "audit ddl on dml off";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s)");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-q audit 不存在的绝对路径    例如  /root/def
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_006)
{
    printf("**********************Audit_gmlog_001_006 begin********************\n");

    getTime_Test();
    char const *g_typeName = "audit /root/def";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -q %s  root %s", g_toolPath, g_typeName, g_time_end);
    ret = executeCommand(g_command, "unsuccessful.");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-q audit 存在的绝对路径  开始时间晚于结束时间
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_007)
{
    printf("**********************Audit_gmlog_001_007 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log   root %s  %s  ",
                stPath, g_time_end, g_time_start);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ret = snprintf(g_command, GMLOG_MAX_CMD,
            "gmlog -q audit /var/log/GMDBV5/log/secure/sgmserver/sgmserver.log   root %s  %s  ", g_time_end, g_time_start);
    }
}
/*****************************************************************************
 Description  : gmlog-q audit 相对路径
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_008)
{
    printf("**********************Audit_gmlog_001_008 begin********************\n");

    char const *g_typeName = "audit /abc/def";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -q %s", g_toolPath, g_typeName);
    printf("%s\n", g_command);
    ret = executeCommand(
        g_command, "The number of parameters for the option(\"-q\") is in the range((3, 5)). please check your input");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit aaa on
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_009)
{
    printf("**********************Audit_gmlog_001_009 begin********************\n");
    char const *g_typeName = "aaa on";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    }
    ret = executeCommand(g_command, "Execute this command failed.");
    printf("ret= %d\n", ret);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
}
/*****************************************************************************
 Description  : gmlog-s audit aaa off
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_010)
{
    printf("**********************Audit_gmlog_001_010 begin********************\n");
    char const *g_typeName = "aaa off";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s ", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    }
    ret = executeCommand(g_command, "Execute this command failed.");
    printf("ret= %d\n", ret);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
}
/*****************************************************************************
 Description  : gmlog-q audit 不存在的绝对路径  开始时间
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_011)
{
    printf("**********************Audit_gmlog_001_011 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/audit.2.log   root %s    ", stPath,
                g_time_start);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : gmlog-q audit 不存在存在的绝对路径  时间段
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_012)
{
    printf("**********************Audit_gmlog_001_012 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/audit.2.log   root %s  %s  ",
                stPath, g_time_start, g_time_end);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : gmlog-q audit 不存在存在的绝对路径  开始时间晚于结束时间
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_013)
{
    printf("**********************Audit_gmlog_001_013 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/audit.2.log   root %s  %s  ",
                stPath, g_time_end, g_time_start);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : gmlog-q  绝对路径
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_014)
{
    printf("**********************Audit_gmlog_001_014 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q  %s/log/secure/audit.2.log   root   ", stPath);
        }
        ret = executeCommand(g_command, "please check your input.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q  /var/log/GMDBV5/log/secure/audit.1.log   root  ");
    }
}
/*****************************************************************************
 Description  : gmlog-q  绝对路径  开始时间
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_015)
{
    printf("**********************Audit_gmlog_001_015 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(
                g_command, GMLOG_MAX_CMD, "gmlog -q  %s/log/secure/audit.2.log   root  %s ", stPath, g_time_start);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ret = snprintf(
            g_command, GMLOG_MAX_CMD, "gmlog -q  /var/log/GMDBV5/log/secure/audit.1.log   root %s ", g_time_start);
    }
}
/*****************************************************************************
 Description  : gmlog-q  绝对路径  开始时间晚于结束时间
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_016)
{
    printf("**********************Audit_gmlog_001_016 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q  %s/log/secure/audit.2.log   root  %s %s", stPath,
                g_time_end, g_time_start);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q  /var/log/GMDBV5/log/secure/audit.1.log   root %s %s",
            g_time_end, g_time_start);
    }
}
/*****************************************************************************
 Description  : gmlog-q  绝对路径  时间段
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_017)
{
    printf("**********************Audit_gmlog_001_017 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q  %s/log/secure/audit.2.log   root  %s %s", stPath,
                g_time_start, g_time_end);
        }
        ret = executeCommand(g_command, "unsuccessful.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    } else {
        ret = snprintf(g_command, GMLOG_MAX_CMD, "gmlog -q  /var/log/GMDBV5/log/secure/audit.1.log   root %s %s",
            g_time_start, g_time_end);
    }
}
/*****************************************************************************
 Description  : gmlog-s  dml on
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_018)
{
    printf("**********************Audit_gmlog_001_018 begin********************\n");
    char const *g_typeName = " dml on ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  dml off
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_019)
{
    printf("**********************Audit_gmlog_001_019 begin********************\n");
    char const *g_typeName = " dml off ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  dcl off
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_020)
{
    printf("**********************Audit_gmlog_001_020 begin********************\n");
    char const *g_typeName = " dcl off ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  dcl on
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_021)
{
    printf("**********************Audit_gmlog_001_021 begin********************\n");
    char const *g_typeName = " dcl on ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  ddl on
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_022)
{
    printf("**********************Audit_gmlog_001_022 begin********************\n");
    char const *g_typeName = " ddl on ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  ddl off
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_023)
{
    printf("**********************Audit_gmlog_001_023 begin********************\n");
    char const *g_typeName = " ddl off ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  aaa on
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_024)
{
    printf("**********************Audit_gmlog_001_024 begin********************\n");
    char const *g_typeName = " aaa on ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s  aaa off
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_025)
{
    printf("**********************Audit_gmlog_001_025 begin********************\n");
    char const *g_typeName = " aaa off ";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s %s ", g_toolPath, g_typeName);
        printf("%s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s).");
    printf("ret=%d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit ddl off
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_026)
{
    if (g_runMode == 0) {
        char const *g_typeName = "audit ddl off ";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 0", g_toolPath);

    } else {
        char const *g_typeName = "audit ddl off ";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 0", g_toolPath);
    }
    int ret = executeCommand(g_command, "auditLogEnableDDL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_runMode == 0) {
        char const *g_typeName = "audit ddl on ";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);

    } else {
        char const *g_typeName = "audit ddl on ";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDDL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit ddl on
 Notes        :  ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_027)
{
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDDL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dml on
 Notes        : ddl  dcl 默认开启   dml默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_028)
{
    char const *g_typeName = "audit dml on ";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDML");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char const *g_typeName1 = "audit dml off ";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDML");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dml off
 Notes        : ddl  dcl 默认开启  dml 默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别
 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_029)
{
    char const *g_typeName = "audit dml off ";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);

    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDML -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDML");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dcl Off
 Notes        : ddl  dcl 默认开启  dml 默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别


 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_030)
{
    char const *g_typeName = "audit dcl off ";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char const *g_typeName1 = "audit dcl on ";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dcl on
 Notes        : ddl  dcl 默认开启  dml 默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_031)
{
    char const *g_typeName = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : gmlog-s audit dcl on  客户端同步键连
 Notes        :  ddl  dcl 默认开启  dml 默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_032)
{
    printf("**********************Audit_gmlog_001_032 begin********************\n");
    // 获取当前系统时间
    getTime_Test();
    //客户端同步键连
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    ASSERT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    char final[2048] = "successfully";
    char final2[2048] = "unsuccessful";
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    FILE *fp;
    char buffer[2048];
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  DCL %s", stPath, g_time_end);
            printf("%s\n", cmd);
            fp = popen(cmd, "r");
            fgets(buffer, sizeof(buffer), fp);
            printf("%s", buffer);
    // hpe环境不支持gmlog -q功能
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
            if (strstr(buffer, final2)) {
                ret = 0;
            } else {
                ret = 1;
            }
#else
            if (strstr(buffer, final)) {
                ret = 0;
            } else {
                ret = 1;
            }
#endif
            pclose(fp);
            fp = NULL;
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        char final[2048] = "DCL";
        system("hpecli log dump");
        snprintf(cmd, GMLOG_MAX_PATH, "grep DCL  /opt/vrpv8/home/<USER>/hpe_log.csv ");
        printf("%s \n", cmd);
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
        EXPECT_EQ(GMERR_OK, ret);
    }
    //客户端同步断链
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dcl on  客户端同步断连
 Notes        :  ddl  dcl 默认开启  dml 默认关闭
                -s 指令 欧拉环境与HPE环境格式有区别
 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_033)
{
    printf("**********************Audit_gmlog_001_033 begin********************\n");

    //客户端键连
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);
    // 获取当前系统时间
    getTime_Test();
    //客户端断链
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    // 欧拉环境   客户端拉起的路径/log/日志类型/进程名
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    FILE *fp;
    char buffer[2048];
    char final[2048] = "successfully";
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  DCL %s", stPath, g_time_end);
            printf("%s\n", cmd);
        }
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
        
    } else {
        char final[2048] = "DCL";
        system("hpecli log dump");
        snprintf(cmd, GMLOG_MAX_PATH, "grep DCL  /opt/vrpv8/home/<USER>/hpe_log.csv ");
        printf("%s \n", cmd);
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : gmlog-s audit dcl on  客户端异步键连
 Notes        : ddl  dcl 默认开启  dml 默认关闭
 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/

TEST_F(Audit_gmlog_001, SEC_001_034)
{
    printf("**********************Audit_gmlog_001_034 begin********************\n");
    // 获取当前系统时间
    getTime_Test();
    //适配新的异步框架
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    FILE *fp;
    char buffer[2048];
    char final[2048] = "successfully";
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  DCL %s", stPath, g_time_end);
            printf("%s\n", cmd);
        }
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        char final[2048] = "DCL";
            system("hpecli log dump");
        snprintf(cmd, GMLOG_MAX_PATH, "grep DCL  /opt/vrpv8/home/<USER>/hpe_log.csv ");
        printf("%s \n", cmd);
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : gmlog-s audit dcl on  客户端异步断连
 Notes        : ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_035)
{
    printf("**********************Audit_gmlog_001_035 begin********************\n");
    //客户端异步键连
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    // 获取当前系统时间
    getTime_Test();
    //客户端异步断链
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    FILE *fp;
    char buffer[2048];
    char final[2048] = "successfully";
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  DCL %s", stPath, g_time_end);
            printf("%s\n", cmd);
        }
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf(" BUFFER IS %s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        char final[2048] = "DCL";
        system("hpecli log dump");
        snprintf(cmd, GMLOG_MAX_PATH, "grep DCL  /opt/vrpv8/home/<USER>/hpe_log.csv");
        printf("%s \n", cmd);
        fp = popen(cmd, "r");
        fgets(buffer, sizeof(buffer), fp);
        printf("%s", buffer);
        if (strstr(buffer, final)) {
            ret = 0;
        } else {
            ret = 1;
        }
        pclose(fp);
        fp = NULL;
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : gmlog-s audit dcl off  客户端同步键连
 Notes        : ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_036)
{
    printf("**********************Audit_gmlog_001_036 begin********************\n");
    // 获取当前系统时间
    getTime_Test();
    char const *g_typeName = "audit dcl off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);
    //客户端同步键连
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    ASSERT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  DCL %s", stPath, g_time_end);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        char final[2048] = "root";
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep DCL ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }

    //客户端同步断链
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    // dcl  on
    g_typeName = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dcl off  客户端同步断连
 Notes        : ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_037)
{
    printf("**********************Audit_gmlog_001_037 begin********************\n");
    //客户端键连
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    ASSERT_EQ(GMERR_OK, ret);
    char const *g_typeName = "audit dcl off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);
    // 获取当前系统时间
    getTime_Test();
    //客户端断链
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  root %s", stPath, g_time_end);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {

        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }

    // dcl  on
    g_typeName = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    EXPECT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dcl off  客户端异步键连
 Notes        :  ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_038)
{
    printf("**********************Audit_gmlog_001_038 begin********************\n");
    // 获取当前系统时间
    getTime_Test();
    char const *g_typeName = "audit dcl off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //客户端异步键连
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  root %s", stPath, g_time_end);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }

    //客户端异步断链
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // dcl  on
    g_typeName = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit dcl off  客户端异步断连
 Notes        : ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_039)
{
    char const *g_typeName = "audit dcl off";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 0", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //客户端异步键连
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取当前系统时间
    getTime_Test();
    //客户端异步断链
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  root %s", stPath, g_time_end);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }

    // dcl  on
    g_typeName = "audit dcl on";
    if (g_runMode == 0) {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    } else {
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDCL -cfgVal 1", g_toolPath);
    }
    ret = executeCommand(g_command, "auditLogEnableDCL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-q audit 合理的路径
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_040)
{
    printf("**********************Audit_gmlog_001_040 begin********************\n");
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret = snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  root ", stPath);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }
}
/*****************************************************************************
 Description  : gmlog-q audit 合理的绝对路径 + 起始时间
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_041)
{
    printf("**********************Audit_gmlog_001_041 begin********************\n");
    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath=getenv("TEST_HOME");
        if (stPath) {
            ret =
                snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log  root %s", stPath, time_start);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }
}
/*****************************************************************************
 Description  : gmlog-q audit 合理的绝对路径 + 时间段
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_042)
{
    printf("**********************Audit_gmlog_001_042 begin********************\n");

    //  需要加 对应的log查询 并判断是否写入日志
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        char *stPath = getenv("TEST_HOME");
        if (stPath) {
            ret = snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/sgmserver/sgmserver.log   root %s %s", stPath,
                time_start, g_time_end);
        }
        printf("%s\n", cmd);
        EXPECT_TRUE(ret > 0);
        ret = system(cmd);
#if (!RUN_INDEPENDENT) || (RUN_SIMULATE)
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    } else {
        snprintf(cmd, GMLOG_MAX_PATH, "logctrl -S \"%s\" -U \"%s\" |grep root ", g_time_start, g_time_end);
        printf("%s \n", cmd);
        system(cmd);
    }
}
/*****************************************************************************
 Description  :
 Notes        : 多线程 进行键连断链操作

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_043)
{
    printf("**********************Audit_gmlog_001_043 begin********************\n");
    getTime_Test();
#define THR_NUM 3
    GmcConnT *conn_t[3] = {0};
    pthread_t thr_arr[3];
    void *thr_ret[3];
    int i;
    for (i = 0; i < THR_NUM; i++) {
        pthread_create(&thr_arr[i], NULL, log_thread_client_connect, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行查询日志操作
 Notes        :

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_044)
{
    printf("**********************Audit_gmlog_001_044 begin********************\n");

#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;

    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_Log_query, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行开关ddl off操作
 Notes        :  ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_045)
{
    printf("**********************Audit_gmlog_001_045 begin********************\n");

#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;

    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_DDL_gmlog_off, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行开关ddl on操作
 Notes        :  ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_046)
{
    printf("**********************Audit_gmlog_001_046 begin********************\n");

#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_DDL_gmlog_on, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行开关dcl off操作
 Notes        :  ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_047)
{
    printf("**********************Audit_gmlog_001_047 begin********************\n");

#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_DCL_gmlog_off, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行开关dcl on操作
 Notes        :  ddl  dcl 默认开启  dml 默认关闭


 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_048)
{
    printf("**********************Audit_gmlog_001_048 begin********************\n");

#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_DCL_gmlog_on, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行开关dml on操作
 Notes        :  ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_049)
{
    printf("**********************Audit_gmlog_001_049 begin********************\n");

#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_DML_gmlog_on, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : 并发进行开关dml off操作
 Notes        :  ddl  dcl 默认开启  dml 默认关闭

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_050)
{
    printf("**********************Audit_gmlog_001_050 begin********************\n");
#define THR_NUM_QUERY 5
    GmcConnT *conn_t[5] = {0};
    pthread_t thr_arr[5];
    void *thr_ret[5];
    int i;
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_create(&thr_arr[i], NULL, thread_DML_gmlog_off, (void *)&conn_t[i]);
    }
    for (i = 0; i < THR_NUM_QUERY; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}
/*****************************************************************************
 Description  : gmlog-s audit 特殊字符
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_051)
{
    printf("**********************Audit_gmlog_001_051 begin********************\n");
    char const *g_typeName = "@@@@@";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s ", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s)");
    printf("ret= %d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : gmlog-s audit 中文
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_052)
{
    printf("**********************Audit_gmlog_001_052 begin********************\n");
    char const *g_typeName = "软件测试";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s ", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    }
    ret = executeCommand(g_command, "unsuccessful, ret = 1004004");
    printf("ret= %d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
/*****************************************************************************
 Description  : gmlog-s audit ddl 1
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_053)
{
    char const *g_typeName = "ddl 1";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 1", g_toolPath);
    }
    int ret = executeCommand(g_command, "auditLogEnableDDL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
/*****************************************************************************
 Description  : gmlog-s audit ddl 0
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_054)
{
    char const *g_typeName = "ddl 0";
    if (g_runMode == 0) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 0", g_toolPath);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName auditLogEnableDDL -cfgVal 0", g_toolPath);
    }
    int ret = executeCommand(g_command, "auditLogEnableDDL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
/*****************************************************************************
 Description  : gmlog-s audit ddl on ddl of
 Notes        : -s 指令 欧拉环境与HPE环境格式有区别

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_055)
{
    printf("**********************Audit_gmlog_001_055 begin********************\n");
    char const *g_typeName = "ddl on ddl off";
    if (g_runMode == 0) {

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s ", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);

    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -s audit %s", g_toolPath, g_typeName);
        printf("g_command is %s\n", g_command);
    }
    ret = executeCommand(g_command, "The option(\"-s\") must input 1 parameter(s)");
    printf("ret= %d\n", ret);
    ASSERT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
/*****************************************************************************
 Description  : gmlog-q  中文路径
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_056)
{
    printf("**********************Audit_gmlog_001_056 begin********************\n");
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(cmd, GMLOG_MAX_CMD, "gmlog -q  %s/我的文档   root   ", stPath);
        }
        printf("cmd  is   %s\n", cmd);
        ret = executeCommand(cmd, "please check your input.");
        printf("ret=%d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : gmlog-q  查询时间为25：00 时间格式非法
 Notes        : 凡是涉及路径查询log日志文件为测试人员手动造的文件  （该方案已和开发沟通）
                -q 指令 欧拉环境与HPE环境 格式相同 不需要做的区分

 History      :
 Author       : wangkun wwx1038088
 Modification :
*****************************************************************************/
TEST_F(Audit_gmlog_001, SEC_001_057)
{
    printf("**********************Audit_gmlog_001_057 begin********************\n");

    //  需要加 对应的log查询 并判断是否写入日志
    char stPath[GMLOG_MAX_PATH] = {0};
    char cmd[GMLOG_MAX_CMD] = {0};
    //  获取当前路径  当前路径下log文件中的secure模块      详情参考GMDBV5日志使用说明书
    if (g_runMode == 0) {
        if (getcwd(stPath, GMLOG_MAX_PATH) != NULL) {
            ret = snprintf(
                cmd, GMLOG_MAX_CMD, "gmlog -q audit %s/log/secure/audit.1.log   root 2021-04-01 25:15:30 ", stPath);
        }
        printf("cmd %s\n", cmd);
        ret = executeCommand(cmd, "unsuccessful.");
        printf("ret= %d\n", ret);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
