[{"type": "record", "name": "T50", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint64", "nullable": true}, {"name": "F4", "type": "uint64", "nullable": false}], "keys": [{"node": "T50", "name": "T50_K1", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]