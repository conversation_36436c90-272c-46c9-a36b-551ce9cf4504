{"system_privilege_config": [{"groups": [{"group": "root", "process": "GrantGroup"}, {"group": "group2", "process": "process2"}, {"group": "group3", "process": "process3"}, {"group": "group4", "process": "process4"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "INSERT_ANY", "UPDATE_ANY", "SELECT_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "TRUNCATE"]}, {"obj_type": "DATALOG_UDF", "privs_type": ["CREATE", "DROP", "ALTER", "INVOKE_ANY"]}, {"obj_type": "EDGE_LABEL", "privs_type": ["CREATE", "DROP"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "REPLACE_ANY", "SELECT_ANY", "DELETE_ANY", "TRUNCATE"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "TABLESPACE", "privs_type": ["CREATE", "DROP", "BIND"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND"]}, {"obj_type": "BINARY_FILE", "privs_type": ["OPEN"]}]}]}