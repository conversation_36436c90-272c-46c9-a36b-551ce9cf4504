/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : DB支持Group导入权限
 Notes        : 
 001.导入白名单用户组
 002.导入白名单同名用户和用户组
 003.白名单用户组赋予/撤销系统权限
 004.白名单用户组赋予/撤销对象权限
 005.白名单用户组批量赋予/撤销系统权限
 006.白名单用户组批量赋予/撤销对象权限
 007.白名单用户组赋予/撤销RESOURCE系统权限（*用户组，*process）
 008.白名单用户组赋予RESOURCE系统权限（*用户组，process不为*）
 009.白名单用户组赋予RESOURCE系统权限（用户组不为*，*process）
 010.白名单用户组赋予/撤销SELECT对象权限（*用户组，*process）
 011.白名单*用户组赋予INSERT/UPDATE/DELETE对象权限（*用户组，*process）
 012.白名单用户组赋予/撤销INSERT/UPDATE/DELETE对象权限（*用户组，process不为*）
 013.白名单用户组赋予INSERT/UPDATE/DELETE对象权限（用户组不为*，*process）
 014.给不存在白名单的用户组赋予/撤销系统权限（isAtomic）
 015.给不存在白名单的用户组赋予/撤销对象权限（isAtomic）
 016.给不存在白名单的用户组赋予/撤销系统权限（!isAtomic）
 017.给不存在白名单的用户组赋予/撤销对象权限（!isAtomic）
 018.白名单用户组重复赋予系统权限（isAtomic）
 019.白名单用户组重复赋予对象权限（isAtomic）
 020.白名单用户组重复赋予系统权限（!isAtomic）
 021.白名单用户组重复赋予对象权限（!isAtomic）
 022.白名单用户组重复撤销系统权限
 023.白名单用户组重复撤销对象权限
 024.白名单用户组赋予系统权限，其中一个权限对象类型为VERTEX_LABEL，系统权限类型为USE
 025.白名单用户组赋予对象权限，其中一个权限对象类型为VERTEX_LABEL，对象权限类型为USE_NSP
 026.白名单用户组撤销系统权限，其中一个权限对象类型为VERTEX_LABEL，系统权限类型为USE
 027.白名单用户组赋予对象权限，其中一个权限对象类型为VERTEX_LABEL，对象权限类型为USE_NSP
 028.导入用户和用户组，任意删除用户或者用户组，再去建连
 Author       : duhu wx1257740
 Modification :
 create       : 2024/01/09
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"
#include <string>

int ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE] = {0};
char const *g_viewName = "V\\$PRIVILEGE_ROLE_STAT";
const char *g_configJson = R"({"max_record_num":1000, "max_record_num_check":true, "isFastReadUncommitted":0})";
class GrantGroup : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GrantGroup::SetUpTestCase()
{
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
}

void GrantGroup::TearDownTestCase()
{
    // 恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
void GrantGroup::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void GrantGroup::TearDown()
{
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    printf("\n=====================TEST:END=====================\n");
}

static void importAllowlist(
    const char *path, const char *expect1 = NULL, const char *expect2 = NULL, const char *expect3 = NULL)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, path, g_connServer);
    ret = executeCommand(g_command, expect1, expect2, expect3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {  // 校验不对时system直接打印结果
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
static void importPolicy(
    const char *path, const char *expect1 = NULL, const char *expect2 = NULL, const char *expect3 = NULL)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, path, g_connServer);
    ret = executeCommand(g_command, expect1, expect2, expect3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {  // 结果不对时system直接打印结果
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

static void removePolicy(const char* file, const char *expect1 = NULL, const char *expect2 = NULL)
{
    ret = sprintf_s(g_command, MAX_CMD_SIZE, "gmrule -c revoke_policy -f %s", file);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = executeCommand(g_command, expect1, expect2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

static void removeAllowlist(const char*file)
{
    ret = sprintf_s(g_command, MAX_CMD_SIZE, "gmrule -c remove_allowlist -f %s", file);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

static void queryPrivs(
    const char *name, const char *expect1 = NULL, const char *expect2 = NULL, const char *expect3 = NULL)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, name, g_connServer);
    ret = executeCommand(g_command, expect1, expect2, expect3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
}

static void importPolicy1(
    const char *path, const char *expect1 = NULL, const char *expect2 = NULL, const char *expect3 = NULL)
{
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule atomic -c import_policy -f %s -s %s ", g_toolPath, path, g_connServer);
    ret = executeCommand(g_command, expect1, expect2, expect3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {  // 结果不对时system直接打印结果
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 001.导入白名单用户组
TEST_F(GrantGroup, Security_024_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入白名单
    importAllowlist("./allowlist/sec24_001.gmuser", "Import single allow list file", "successfully");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除白名单
    removeAllowlist("./allowlist/sec24_001.gmuser");
}

// 002.导入白名单同名用户和用户组
TEST_F(GrantGroup, Security_024_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入白名单
    importAllowlist("./allowlist/sec24_002.gmuser", "Import single allow list file", "successfully");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除白名单
    removeAllowlist("./allowlist/sec24_002.gmuser");
}

// 003.白名单用户组赋予/撤销系统权限
TEST_F(GrantGroup, Security_024_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_003.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_003.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_003.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 验证有create,insert_any,truncate,drop权限
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    char const *labelName = "T24_type";
    uint32_t value = 10;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_003.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_003.gmuser");
}

// 004.白名单用户组赋予/撤销对象权限
TEST_F(GrantGroup, Security_024_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_004.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_004.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_004.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_004.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 验证T25_type表的insert，select，update, delete权限
    uint32_t value = 10;
    uint32_t value1 = 0;
    uint32_t value2 = 20;
    bool isFinished = false;
    bool isNull = false;
    char const *labelName = "T25_type";
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinished);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &value1, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, value1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T25_K1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value2,sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T25_K1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_004.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_004.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_004.gmuser");
}

// 005.白名单用户组批量赋予/撤销系统权限
TEST_F(GrantGroup, Security_024_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable、EdgeLable
    char *testSchema = NULL;
    char *testSchema1 = NULL;
    char *testSchema2 = NULL;
    char *testSchema3 = NULL;
    char *testSchema4 = NULL;
    readJanssonFile("schema_file/sec24_005.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    readJanssonFile("schema_file/sec24_005a.gmjson", &testSchema1);
    ASSERT_NE((void *)NULL, testSchema1);
    readJanssonFile("schema_file/sec24_005b.gmjson", &testSchema2);
    ASSERT_NE((void *)NULL, testSchema2);
    readJanssonFile("schema_file/sec24_005edge.gmjson", &testSchema3);
    ASSERT_NE((void *)NULL, testSchema3);
    readJanssonFile("schema_file/sec24_005resource.gmjson", &testSchema4);
    ASSERT_NE((void *)NULL, testSchema4);

    // 导入白名单
    importAllowlist("./allowlist/sec24_005.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_005.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证对VertexLabel有create，drop，insert_any,truncate权限
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    uint32_t value = 20;
    uint64_t value1 = 50;
    char const *labelName = "T50";
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT64, &value1, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证对EdgeLable有create，drop权限
    char const *labelName1 = "T50_01";
    char const *labelName2 = "T50_02";
    char const *labelName3 = "T50_03";
    ret = GmcCreateVertexLabel(g_stmt, testSchema1, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema2, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, testSchema3, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema1);
    free(testSchema2);
    free(testSchema3);

    ret = GmcDropEdgeLabel(g_stmt, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 验证对KvTable有create，replace_any，select_any, truncate，drop权限
    char const *labelName4 = "T50_KV";
    ret = GmcKvCreateTable(g_stmt, labelName4, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char key[32] = "zhangsan";
    int32_t value2 = 30;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value2;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value2, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(g_stmt, key, strlen(key), output, &outputLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(30, *(uint32_t *)output);
    AW_MACRO_EXPECT_EQ_INT(4, outputLen);

    ret = GmcKvTruncateTable(g_stmt, labelName4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证对namespace有create，drop，use权限
    const char *nameSpace = (const char *)"use";
    const char *userName = (const char *)"abc";
    ret = GmcCreateNamespace(g_stmt, nameSpace, userName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //验证对tablespace有create，drop权限
    const char *tspName = (const char *)"tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tspName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 0;
    ret = GmcCreateTablespace(g_stmt, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmt, tspName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证对ResourcePool有create，drop，bind，unbind权限
    char const *labelName5 = "T50_R";
    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test";
    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema4, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema4);

    ret = GmcBindResPoolToLabel(g_stmt, ResPoolName, labelName5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(g_stmt, labelName5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_005.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_005.gmuser");
}

// 006.白名单用户组批量赋予/撤销对象权限
TEST_F(GrantGroup, Security_024_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_006.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_006.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_006.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_006.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 验证T60_01表的insert，select，update, delete，merge，replace权限
    uint32_t value = 10;
    uint32_t value1 = 0;
    uint32_t value2 = 20;
    uint32_t value3 = 30;
    uint32_t value4 = 40;
    bool isFinished = false;
    bool isNull = false;
    char const *labelName = "T60_01";
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinished);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &value1, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10, value1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T60_K1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value2,sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T60_K1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "T60_K1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_006.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_006.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_006.gmuser");
}

// 007.白名单用户组赋予/撤销RESOURCE系统权限（*用户组，*process）
TEST_F(GrantGroup, Security_024_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入白名单
    importAllowlist("./allowlist/sec24_007.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_007.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_007.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_007.gmuser");
}

// 008.白名单用户组赋予/撤销RESOURCE系统权限（*用户组，process不为*）
TEST_F(GrantGroup, Security_024_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_NAME);
    // 导入白名单
    importAllowlist("./allowlist/sec24_008.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_008.gmpolicy", "import policy. system privilege success: 0, warning: 0.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_008.gmpolicy", "revoke policy. system privilege success: 0, warning: 1.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_008.gmuser");
}

// 009.白名单用户组赋予/撤销RESOURCE系统权限（用户组不为*，*process）
TEST_F(GrantGroup, Security_024_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_NAME);
    // 导入白名单
    importAllowlist("./allowlist/sec24_009.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_009.gmpolicy", "import policy. system privilege success: 0, warning: 0.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_009.gmpolicy", "revoke policy. system privilege success: 0, warning: 1.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_009.gmuser");
}

// 010.白名单用户组赋予/撤销SELECT对象权限（*用户组，*process）
TEST_F(GrantGroup, Security_024_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_010.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_010.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T100";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_010.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_010.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_010.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_010.gmuser"); 
}

// 011.白名单*用户组赋予/撤销INSERT/UPDATE/DELETE对象权限（*用户组，*process）
TEST_F(GrantGroup, Security_024_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_NAME);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_011.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_011.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_011.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T110";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_011.gmpolicy", "import policy. object privilege success: 0, warning: 0.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_011.gmpolicy", "revoke policy. object privilege success: 0, warning: 1.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_011.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_011.gmuser");
}

// 012.白名单用户组赋予/撤销INSERT/UPDATE/DELETE对象权限（*用户组，process不为*）
TEST_F(GrantGroup, Security_024_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_012.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_012.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_012.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T120";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_012.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_012.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_012.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_012.gmuser"); 
}

// 013.白名单用户组赋予INSERT/UPDATE/DELETE对象权限（用户组不为*，*process） 
TEST_F(GrantGroup, Security_024_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_NAME);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_013.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_013.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_013.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T130";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_013.gmpolicy", "import policy. object privilege success: 0, warning: 0.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_013.gmpolicy", "revoke policy. object privilege success: 0, warning: 1.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_013.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_013.gmuser");
}

// 014.给不存在白名单的用户组赋予/撤销系统权限（isAtomic）
TEST_F(GrantGroup, Security_024_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_INSUFFICIENT_PRIVILEGE);

    // 赋予系统权限
    importPolicy1("./policyfiles/systemPolicy/sec24_014.gmpolicy", "import policy. system privilege success: 0, warning: 0, rollback: 0.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_014.gmpolicy", "revoke policy. system privilege success: 0, warning: 24.");
    // 查询权限视图
    queryPrivs(g_viewName);
}

// 015.给不存在白名单的用户组赋予/撤销对象权限（isAtomic）
TEST_F(GrantGroup, Security_024_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_015.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_015.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_015.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T150";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy1("./policyfiles/objectPolicy/sec24_015.gmpolicy", "import policy. object privilege success: 0, warning: 0, rollback: 0.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_015.gmpolicy", "revoke policy. object privilege success: 0, warning: 4.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_015.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_015.gmuser");
}

// 016.给不存在白名单的用户组赋予/撤销系统权限（!isAtomic）
TEST_F(GrantGroup, Security_024_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_INSUFFICIENT_PRIVILEGE);

    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_016.gmpolicy", "import policy. system privilege success: 0, warning: 24.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_016.gmpolicy", "revoke policy. system privilege success: 0, warning: 24.");
    // 查询权限视图
    queryPrivs(g_viewName);
}


// 017.给不存在白名单的用户组赋予/撤销对象权限（!isAtomic）
TEST_F(GrantGroup, Security_024_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_017.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_017.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_017.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T170";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_017.gmpolicy", "import policy. object privilege success: 0, warning: 4.");
    // 查询权限视图
    queryPrivs(g_viewName);

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_017.gmpolicy", "revoke policy. object privilege success: 0, warning: 4.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_017.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_017.gmuser");
}

// 018.白名单用户组重复赋予系统权限（isAtomic）
TEST_F(GrantGroup, Security_024_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PRIVILEGE_NOT_GRANTED);
    // 导入白名单
    importAllowlist("./allowlist/sec24_018.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_018.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);
    // 重复赋予系统权限
    importPolicy1("./policyfiles/systemPolicy/sec24_018.gmpolicy", "import policy. system privilege success: 0, warning: 0, rollback: 0.");

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_018.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_018.gmuser");
}
// 019.白名单用户组重复赋予对象权限（isAtomic）
TEST_F(GrantGroup, Security_024_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_GRANT_OPERATION);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_019.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_019.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_019.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T190";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_019.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);
    // 重复赋予对象权限
    importPolicy1("./policyfiles/objectPolicy/sec24_019.gmpolicy", "import policy. object privilege success: 0, warning: 0, rollback: 0.");

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_019.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_019.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_019.gmuser");
}
// 020.白名单用户组重复赋予系统权限（!isAtomic）
TEST_F(GrantGroup, Security_024_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_PRIVILEGE_NOT_GRANTED);
    // 导入白名单
    importAllowlist("./allowlist/sec24_020.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_020.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);
    // 重复赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_020.gmpolicy", "Import single policy file", "successfully.");

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_020.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_020.gmuser");
}
// 021.白名单用户组重复赋予对象权限（!isAtomic）
TEST_F(GrantGroup, Security_024_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_GRANT_OPERATION);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_021.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_021.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_021.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T210";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_021.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);
    // 重复赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_021.gmpolicy", "Import single policy file", "successfully.");

    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_021.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_021.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_021.gmuser");
}

// 022.白名单用户组重复撤销系统权限
TEST_F(GrantGroup, Security_024_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INSUFFICIENT_PRIVILEGE);
    // 导入白名单
    importAllowlist("./allowlist/sec24_022.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_022.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_022.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 重复撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_022.gmpolicy", "revoke policy. system privilege success: 0, warning: 4.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_022.gmuser");
}
// 023.白名单用户组重复撤销对象权限
TEST_F(GrantGroup, Security_024_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_023.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_023.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_023.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T230";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_023.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);
    
    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_023.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");
    // 重复撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_023.gmpolicy", "revoke policy. object privilege success: 0, warning: 4.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_023.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_023.gmuser");
}

// 024.白名单用户组赋予系统权限，其中一个权限对象类型为VERTEX_LABEL，系统权限类型为USE
TEST_F(GrantGroup, Security_024_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入白名单
    importAllowlist("./allowlist/sec24_024.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_024.gmpolicy", "import policy. system privilege success: 0, warning: 4.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_024a.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_024.gmuser");
}
// 025.白名单用户组赋予对象权限，其中一个权限对象类型为VERTEX_LABEL，对象权限类型为USE_NSP
TEST_F(GrantGroup, Security_024_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_025.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_025.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_025.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T250";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_025.gmpolicy", "import policy. object privilege success: 0, warning: 4.");
    // 查询权限视图
    queryPrivs(g_viewName);
    
    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_025a.gmpolicy", "revoke policy. object privilege success: 1, warning: 0.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_025.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_025.gmuser");
}
// 026.白名单用户组撤销系统权限，其中一个权限对象类型为VERTEX_LABEL，系统权限类型为USE
TEST_F(GrantGroup, Security_024_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入白名单
    importAllowlist("./allowlist/sec24_026.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_026.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_026a.gmpolicy", "revoke policy. system privilege success: 0, warning: 4.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_026.gmuser");
}
// 027.白名单用户组撤销对象权限，其中一个权限对象类型为VERTEX_LABEL，对象权限类型为USE_NSP
TEST_F(GrantGroup, Security_024_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入VertexLable
    char *testSchema = NULL;
    readJanssonFile("schema_file/sec24_027.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);

    // 导入白名单
    importAllowlist("./allowlist/sec24_027.gmuser", "Import single allow list file", "successfully");
    // 赋予系统权限
    importPolicy("./policyfiles/systemPolicy/sec24_027.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);

    char const *labelName = "T270";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    // 赋予对象权限
    importPolicy("./policyfiles/objectPolicy/sec24_027.gmpolicy", "Import single policy file", "successfully.");
    // 查询权限视图
    queryPrivs(g_viewName);
    
    // 撤销对象权限
    removePolicy("./policyfiles/objectPolicy/sec24_027a.gmpolicy", "revoke policy. object privilege success: 0, warning: 4.");

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限
    removePolicy("./policyfiles/systemPolicy/sec24_027.gmpolicy", "revoke policy. system privilege success: 1, warning: 0.");
    // 删除白名单
    removeAllowlist("./allowlist/sec24_027.gmuser");
}

// 028.导入用户和用户组，任意删除用户或者用户组，再去建连
TEST_F(GrantGroup, Security_024_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INSUFFICIENT_PRIVILEGE);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
     // 导入白名单用户和用户组
    importAllowlist("./allowlist/sec24_028.gmuser", "Import single allow list file", "successfully");
    // 删除用户
    removeAllowlist("./allowlist/sec24_028a.gmuser");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新导入用户
    importAllowlist("./allowlist/sec24_028a.gmuser", "Import single allow list file", "successfully");
    // 删除用户组
    removeAllowlist("./allowlist/sec24_028b.gmuser");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新导入用户组
    importAllowlist("./allowlist/sec24_028b.gmuser", "Import single allow list file", "successfully");
    // 删除白名单用户和用户组
    removeAllowlist("./allowlist/sec24_028.gmuser");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
}
