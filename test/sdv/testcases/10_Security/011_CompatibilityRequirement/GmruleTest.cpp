/*****************************************************************************
 Description  :
 Notes        :
 History      :
 Author       :
 Modification :
 Date         :
*****************************************************************************/
#include "function.h"

class GmruleTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
#ifdef FEATURE_PERSISTENCE
        system("rm -rf /data/gmdb/*");
#endif
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
#ifdef FEATURE_PERSISTENCE
        system("rm -rf /data/gmdb/*");
#endif
        // 还原配置
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/start.sh -f");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmruleTest::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0},
        errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
}

void GmruleTest::TearDown()
{
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    printf("\n======================TEST:END========================\n");
}

// 1.导入白名单中包含users和groups，users元组中间有重复，起服务后建连
TEST_F(GmruleTest, SEC_011_001)
{
    importAllowlist("./allowlist/sec_001.gmuser", "dup user tup found", "ret = 1009012");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    if (g_envType == 2) {  // iot不重启服务
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(conn);
        EXPECT_EQ(GMERR_OK, ret);
        removeAllowlist("./allowlist/sec_001.gmuser");
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
}

// 2.导入白名单仅包含groups，用户组元组中间有重复，起服务后建连
TEST_F(GmruleTest, SEC_011_002)
{
    importAllowlist("./allowlist/sec_002.gmuser", "dup role tup found", "ret = 1009012");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    if (g_envType == 2) {  // iot不重启服务
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(conn);
        EXPECT_EQ(GMERR_OK, ret);
        removeAllowlist("./allowlist/sec_002.gmuser");
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
}
// 3.导入白名单包含users和groups，users元组无重复，goups元组中间有重复，起服务后建连
TEST_F(GmruleTest, SEC_011_003)
{
    importAllowlist("./allowlist/sec_003.gmuser", "dup role tup found", "ret = 1009012");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    if (g_envType == 2) {  // iot不重启服务
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(conn);
        EXPECT_EQ(GMERR_OK, ret);
        removeAllowlist("./allowlist/sec_003.gmuser");
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
}
// 4.Users和group有重复，导入成功
TEST_F(GmruleTest, SEC_011_004)
{
    importAllowlist("./allowlist/sec_004.gmuser", "Import single allow list file",
        "import allowlist, create db user. success: 5, warning: 0.",
        "import allowlist, create db group. success: 4, warning: 0.");  // user和group都已经支持创建元数据
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_004.gmuser");
}
// 5.User+process，较长，33位用户名+15位进程名重复，导入报错
TEST_F(GmruleTest, SEC_011_005)
{
    importAllowlist("./allowlist/sec_005.gmuser", "dup user tup found", "ret = 1009012");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    if (g_envType == 2) {  // iot不重启服务
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(conn);
        EXPECT_EQ(GMERR_OK, ret);
        removeAllowlist("./allowlist/sec_005.gmuser");
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
}
// 6.User+process，较长，33位用户名+15位进程名不重复，导入成功
TEST_F(GmruleTest, SEC_011_006)
{
    importAllowlist(
        "./allowlist/sec_006.gmuser", "Import single allow list file", "successfully");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_006.gmuser");
}
// 7.不同文件元组重复，导入成功
TEST_F(GmruleTest, SEC_011_007)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully");
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 8.构造文件名，2ab、_ab、ab：非法文件名，导入白名单后，建连
TEST_F(GmruleTest, SEC_011_008)
{
    const char *fileName[3] = {"2ab.gmuser", "_ab.gmuser", "ab:.gmuser"};
    for (int i = 0; i < 3; i++) {
        FILE *fp;
        if ((fp = fopen(fileName[i], "w")) == NULL) {
            printf("fopen fail\n");
            exit(1);
        }
        char *policy = NULL;
        readJanssonFile("./allowlist/sec_normal.gmuser", &policy);
        for (int i = 0; i < strlen(policy); i++) {
            fputc(policy[i], fp);
        }
        fclose(fp);
        free(policy);
        importAllowlist(fileName[i], "inv file name prefix", fileName[i], "ret = 1009001");
        GmcConnT *conn = NULL;
        ret = testGmcConnect(&conn);
        if (g_envType == 2) {  // iot不重启服务
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcDisconnect(conn);
            EXPECT_EQ(GMERR_OK, ret);
            removeAllowlist("./allowlist/sec_normal.gmuser");
        } else {
            EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        }
#if TEST_DEBUG
#else
        sprintf(g_command, "rm -rf %s", fileName[i]);
        system(g_command);
        memset(g_command, 0, MAX_CMD_SIZE);
#endif
    }
}
// 9.构造文件名长度255包括’\0’,导入白名单后建连
TEST_F(GmruleTest, SEC_011_009)
{
    uint32_t length = 255;
    char *fileName = (char *)malloc(sizeof(char) * length);
    int i = 0;
    for (; i < length - 1 - strlen(".gmuser"); i++) {
        fileName[i] = 'a';
    }
    strcpy(fileName + i, ".gmuser");
    EXPECT_EQ(length - 1, strlen(fileName));
    copyFile("./allowlist/sec_normal.gmuser", fileName);
    importAllowlist(fileName, "Import single allow list file", "successfully");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
#if TEST_DEBUG
#else
    sprintf(g_command, "rm -rf %s", fileName);
    system(g_command);
    memset(g_command, 0, MAX_CMD_SIZE);
#endif
    free(fileName);
}
// 10.构造绝对路径长度512包括'\0'的白名单文件，导入后建连
TEST_F(GmruleTest, SEC_011_010)
{
    int length = 512;
    char fileName[MAX_CMD_SIZE];
    makeAbsolutePath(".gmuser", length, fileName);
    copyFile("./allowlist/sec_normal.gmuser", fileName);
    importAllowlist(fileName, "Import single allow list file", "successfully");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
#if TEST_DEBUG
#else
    rmPathFile();
#endif
}
// 11.构造绝对路径长度513的白名单文件，导入后建连
TEST_F(GmruleTest, SEC_011_011)
{
    int length = 513;
    char fileName[MAX_CMD_SIZE];
    makeAbsolutePath(".gmuser", length, fileName);
    copyFile("./allowlist/sec_normal.gmuser", fileName);
    importAllowlist(fileName, "unsucc", "ret = 1009000");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(conn);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
#if TEST_DEBUG
#else
    rmPathFile();
#endif
}
// 12.构造文件名非.gmuser结尾，导入白名单后建连
TEST_F(GmruleTest, SEC_011_012)
{
    const char *fileName[3] = {"allowlist.gmuse", "allowlist.gmjson", "allowlist.gmconfig"};
    for (int i = 0; i < 3; i++) {
        FILE *fp;
        if ((fp = fopen(fileName[i], "w")) == NULL) {
            printf("fopen fail\n");
            exit(1);
        }
        char *policy = NULL;
        readJanssonFile("./allowlist/sec_normal.gmuser", &policy);
        for (int i = 0; i < strlen(policy); i++) {
            fputc(policy[i], fp);
        }
        fclose(fp);
        free(policy);
        importAllowlist(fileName[i], fileName[i], "must be .gmuser", "ret = 1013000");
        GmcConnT *conn = NULL;
        ret = testGmcConnect(&conn);
        if (g_envType == 2) {  // iot不重启服务
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcDisconnect(conn);
            EXPECT_EQ(GMERR_OK, ret);
            removeAllowlist("./allowlist/sec_normal.gmuser");
        } else {
            EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        }
#if TEST_DEBUG
#else
        sprintf(g_command, "rm -rf %s", fileName[i]);
        system(g_command);
        memset(g_command, 0, MAX_CMD_SIZE);
#endif
    }
}
// 13.一个目录下包含多个policy文件，批量导入，验证权限
TEST_F(GmruleTest, SEC_011_013)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully.");
    // 批量导入系统权限
    importPolicy("./policy/systemPolicy/batchImport", "3 files OK, 0 files unsuccessful");
    // 建连时会获取系统权限
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 批量导入对象权限
    importPolicy("./policy/objectPolicy/batchImport", "3 files OK, 0 files unsuccessful");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyName(stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // gmsysview的系统权限
    sprintf(g_command, "%s/gmsysview -q V\\$DRT_CONN_STAT", g_toolPath);
    ret = executeCommand(g_command, "CONN_ID");
    EXPECT_EQ(GMERR_OK, ret);
    removePolicy("./policy/objectPolicy/batchImport/label0delete.gmpolicy");
    removePolicy("./policy/objectPolicy/batchImport/label0insert.gmpolicy");
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 14.构造深度为16的目录（15个子目录），深度2，5，9，16都有文件，批量导入，验证权限
TEST_F(GmruleTest, SEC_011_014)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully.");
    // 批量导入系统权限
    importPolicy("./policy/systemPolicy/batchImport", "3 files OK, 0 files unsuccessful");
    // 建连时会获取系统权限
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName[2] = {"label0", "label1"};
    char schema[MAX_CMD_SIZE];
    for (int i = 0; i < 2; i++) {
        sprintf(schema, vertex, labelName[i], labelName[i]);
        GmcDropVertexLabel(stmt, labelName[i]);
        ret = GmcCreateVertexLabel(stmt, schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    importPolicy("./policy/objectPolicy/depth_16", "4 files OK, 0 files unsuccessful");
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName[i], GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < 1; j++) {
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName[i], GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < 1; j++) {
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcDropVertexLabel(stmt, labelName[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removePolicy("./policy/objectPolicy/depth_16/depth2.gmpolicy");
    removePolicy("./policy/objectPolicy/depth_16/depth3/depth4/depth5/depth5.gmpolicy");
    removePolicy("./policy/objectPolicy/depth_16/depth3/depth4/depth5/depth6/depth7/depth8/depth9/depth9.gmpolicy");
    removePolicy("./policy/objectPolicy/depth_16/depth3/depth4/depth5/depth6/depth7/depth8/depth9/depth10/depth11/"
                 "depth12/depth13/depth14/"
                 "depth15/depth16/depth16.gmpolicy");
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 15.构造深度为17的目录下一个policy文件，导入目录
TEST_F(GmruleTest, SEC_011_015)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully.");
    // 批量导入系统权限
    importPolicy("./policy/systemPolicy/batchImport", "3 files OK, 0 files unsuccessful");
    // 建连时会获取系统权限
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 批量导入对象权限
    importPolicy("./policy/objectPolicy/depth_17", "ret = 1010001", "Total 0 policy files were found");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 16.建立1024张表，一个目录下包含1025个文件，批量导入，校验权限
TEST_F(GmruleTest, SEC_011_016)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully");
    // 批量导入系统权限
    importPolicy("./policy/systemPolicy/batchImport", "3 files OK, 0 files unsuccessful");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelName[1024][20];
    char buff[1024];
    // 创建1024张表
    for (int i = 0; i < 1024; i++) {
        sprintf(labelName[i], "label%d", i);
        sprintf(buff, vertex, labelName[i], labelName[i]);
        GmcDropVertexLabel(stmt, labelName[i]);
        ret = GmcCreateVertexLabel(stmt, buff, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 创建1024个文件
    char fileName[40];
    system("mkdir tmp_policy");
    for (int i = 0; i < 1024; i++) {
        sprintf(fileName, "./tmp_policy/policy%d.gmpolicy", i);
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            printf("fopen fail\n");
            break;
        } else {
            fprintf(fp, objPolicy, labelName[i]);
            fclose(fp);
        }
    }
    // 批量导入
    importPolicy("./tmp_policy", "1024 files OK, 0 files unsuccessful");
    // 校验权限
    for (int i = 0; i < 1024; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName[i], GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, labelName[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removePolicy("./tmp_policy/policy0.gmpolicy");
    removeAllowlist("./allowlist/sec_normal.gmuser");
#if TEST_DEBUG
#else
    system("rm -rf tmp_policy");
#endif
}
// 17.目录中有非gmpolicy后缀的文件、内容错误，文件名错误的文件，批量导入，校验权限
TEST_F(GmruleTest, SEC_011_017)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully");
    // 批量导入目录，包含非gmpolicy后缀文件、下划线开头文件、json格式错误文件、json格式正确的白名单文件
    // 9001和4006报错来自不同的文件，出现顺序是乱序的
    importPolicy("./policy/systemPolicy/batchImport_error",
        "inv file name prefix", "ret = 1009001", "1 files OK, 3 files unsuccessful");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 18.从空目录、不存在的目录导入policy
TEST_F(GmruleTest, SEC_011_018)
{
    system("mkdir tmp_policy");
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully");
    // 批量导入目录，空目录和不存在目录
    importPolicy("./tmp_policy", "0 files OK, 0 files unsuccessful");
    importPolicy("./tmpPolicy", "no such file or directory", "ret = 1013000");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    system("rm -rf tmp_policy");
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 19构造后缀名非.gmpolicy的文件进行policy导入。如.gmpoli .gmjson
TEST_F(GmruleTest, SEC_011_019)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully");
    const char *fileName[3] = {"policy.gmpoli", "policy.gmjson", "policy.gmuser"};
    for (int i = 0; i < 3; i++) {
        FILE *fp;
        if ((fp = fopen(fileName[i], "w")) == NULL) {
            printf("fopen fail\n");
            exit(1);
        }
        char *policy = NULL;
        readJanssonFile("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy", &policy);
        for (int i = 0; i < strlen(policy); i++) {
            fputc(policy[i], fp);
        }
        fclose(fp);
        free(policy);
        importPolicy(fileName[i], fileName[i], "must be .gmpolicy", "ret = 1013000");
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        const char *labelName = "label0";
        char schema[MAX_CMD_SIZE];
        sprintf(schema, vertex, labelName, labelName);
        GmcDropVertexLabel(stmt, labelName);
        ret = GmcCreateVertexLabel(stmt, schema, NULL);
        if (g_envType == 2) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcDropVertexLabel(stmt, labelName);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        }
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
#if TEST_DEBUG
#else
        sprintf(g_command, "rm -rf %s", fileName[i]);
        system(g_command);
        memset(g_command, 0, MAX_CMD_SIZE);
#endif
    }
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 20.构造文件名，2ab.gmpolicy、_ab.gmpolicy、ab:.gmpolicy，校验权限
TEST_F(GmruleTest, SEC_011_020)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    const char *fileName[3] = {"2ab.gmpolicy", "_ab.gmpolicy", "ab:.gmpolicy"};
    for (int i = 0; i < 3; i++) {
        FILE *fp;
        if ((fp = fopen(fileName[i], "w")) == NULL) {
            printf("fopen fail\n");
            exit(1);
        }
        char *policy = NULL;
        readJanssonFile("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy", &policy);
        for (int i = 0; i < strlen(policy); i++) {
            fputc(policy[i], fp);
        }
        fclose(fp);
        free(policy);
        importPolicy(fileName[i], "inv file name prefix", fileName[i], "ret = 1009001");
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        const char *labelName = "label0";
        char schema[MAX_CMD_SIZE];
        sprintf(schema, vertex, labelName, labelName);
        GmcDropVertexLabel(stmt, labelName);
        ret = GmcCreateVertexLabel(stmt, schema, NULL);
        if (g_envType == 2) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcDropVertexLabel(stmt, labelName);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        }
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
#if TEST_DEBUG
#else
        sprintf(g_command, "rm -rf %s", fileName[i]);
        system(g_command);
        memset(g_command, 0, MAX_CMD_SIZE);
#endif
    }
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 21.导入policy文件，系统权限中有重复元组，建连建表
TEST_F(GmruleTest, SEC_011_021)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully.");
    importPolicy("./policy/systemPolicy/sec_021.gmpolicy", "dup user tup found", "ret = 1009012");
    importPolicy("./policy/systemPolicy/sec2_021.gmpolicy", "dup user tup found", "ret = 1009012");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 22.导入policy文件，对象权限中有重复元组，操作表
TEST_F(GmruleTest, SEC_011_022)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list file", "successfully.");
    importPolicy("./policy/systemPolicy/batchImport", "3 files OK, 0 files unsuccessful");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    importPolicy("./policy/objectPolicy/sec_022.gmpolicy", "dup user tup found", "ret = 1009012");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 23.导入policy文件，系统权限有重复用户组，对象权限不重复，操作表
TEST_F(GmruleTest, SEC_011_023)
{
    // 白名单、建表、删表权限
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    importPolicy("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy",
        "CreateDeleteVertexLabel.gmpolicy successfully");
    importPolicy("./policy/systemPolicy/batchImport/PrepareStmt.gmpolicy", "PrepareStmt.gmpolicy successfully");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 导入对象无重复，系统有重复元组的policy文件。验证对象权限先导入
    importPolicy("./policy/sec_023.gmpolicy", "dup user tup found", "ret = 1009012");
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 验证系统UPDATE权限未导入
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 24.导入policy文件，系统权限和对象权限中元组重复，导入policy文件，校验权限
TEST_F(GmruleTest, SEC_011_024)
{
    // 白名单、建表、删表权限
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    importPolicy("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy",
        "CreateDeleteVertexLabel.gmpolicy successfully");
    importPolicy("./policy/systemPolicy/batchImport/PrepareStmt.gmpolicy", "PrepareStmt.gmpolicy successfully");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 导入文件，对象权限insert，系统权限update
    importPolicy("./policy/sec_024.gmpolicy", "/sec_024.gmpolicy successfully");
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    i = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 验证系统UPDATE权限
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removePolicy("./policy/sec_024.gmpolicy");
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
// 25.构造最大长度255的policy文件导入权限，校验权限
TEST_F(GmruleTest, SEC_011_025)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    uint32_t length = 255;
    char *fileName = (char *)malloc(sizeof(char) * length);
    int i = 0;
    for (; i < length - 1 - strlen(".gmpolicy"); i++) {
        fileName[i] = 'a';
    }
    strcpy(fileName + i, ".gmpolicy");
    EXPECT_EQ(length - 1, strlen(fileName));
    // 建表删表权限
    copyFile("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy", fileName);
    importPolicy(fileName, "successfully");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
#if TEST_DEBUG
#else
    sprintf(g_command, "rm -rf %s", fileName);
    system(g_command);
    memset(g_command, 0, MAX_CMD_SIZE);
#endif
    free(fileName);
}
// 26.构建长512的绝对路径policy文件，导入权限，校验权限
TEST_F(GmruleTest, SEC_011_026)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    int length = 512;
    char fileName[MAX_CMD_SIZE];
    makeAbsolutePath(".gmpolicy", length, fileName);
    copyFile("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy", fileName);
    importPolicy(fileName, "successfully");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    removeAllowlist("./allowlist/sec_normal.gmuser");
#if TEST_DEBUG
#else
    rmPathFile();
#endif
}
// 27.构造长513的绝对路径的policy文件，导入权限，校验权限
TEST_F(GmruleTest, SEC_011_027)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    int length = 513;
    char fileName[MAX_CMD_SIZE];
    makeAbsolutePath(".gmpolicy", length, fileName);
    copyFile("./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy", fileName);
    importPolicy(fileName, "unsucc", "ret = 1009000");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    if (g_envType == 2) {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    }
    ret = testGmcDisconnect(conn);
    removeAllowlist("./allowlist/sec_normal.gmuser");
    EXPECT_EQ(GMERR_OK, ret);
#if TEST_DEBUG
#else
    rmPathFile();
#endif
}

// 28.导入不同的policy文件，系统权限和对象权限元组重复，验证权限叠加
TEST_F(GmruleTest, SEC_011_028)
{
    importAllowlist(
        "./allowlist/sec_normal.gmuser", "Import single allow list", "successfully");
    importPolicy(
        "./policy/systemPolicy/batchImport/CreateDeleteVertexLabel.gmpolicy", "successfully");
    importPolicy("./policy/systemPolicy/batchImport/PrepareStmt.gmpolicy", "successfully");
    // 建连时会获取系统权限
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "label0";
    char schema[MAX_CMD_SIZE];
    sprintf(schema, vertex, labelName, labelName);
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    importPolicy("./policy/objectPolicy/batchImport/label0insert.gmpolicy", "successfully");
    importPolicy("./policy/objectPolicy/batchImport/label0delete.gmpolicy", "successfully");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    for (int i = 0; i < 10; i++) {
        ret = GmcSetIndexKeyName(stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    removePolicy("./policy/objectPolicy/batchImport/label0insert.gmpolicy");
    removePolicy("./policy/objectPolicy/batchImport/label0delete.gmpolicy");
    removeAllowlist("./allowlist/sec_normal.gmuser");
}
