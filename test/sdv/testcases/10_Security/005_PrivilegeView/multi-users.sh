#!/bin/bash
# for gmimport multi-object_privilege

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi


## 数据清除及准备  $CUR_DIR/multi-gmimport文件夹
cd $CUR_DIR
rm -rf multi_user
mkdir multi_user
cp $TEST_HOME/testcases/10_Security/005_PrivilegeView/allow_list/whitelist_01.gmuser ./multi_user
sleep 2

# 构造多个 multi-object_privilege
cd $CUR_DIR/multi_user
echo $1
# for ((i=1; i<=$1; i++))
for i in $(seq 1 $1) 
do
	#sed -i "s/\"obj_name\": \"T39_all_type\"/\"obj_name\": \"T39_all_type"$i"\"/g" ./objVertex$i.gmpolicy
    sed -i "/\"root00\"/i\{\"user\": \"root$i\", \"process\": \"abc$i\"}," ./whitelist_01.gmuser
done

