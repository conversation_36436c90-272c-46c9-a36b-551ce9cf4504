/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: clientIllegal_test.cpp
 * Description: 非法用户尝试连接
 * Author: qinqianbao 995465
 * Create: 2022-03-30
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define CMD_SIZE 10000
#if !defined(RUN_DATACOM_HPE)
int main(void)
{
    int ret;
    int i = 0;
    GmcConnT *conn_t[CMD_SIZE+1] = {0};
    GmcStmtT *stmt_t[CMD_SIZE+1] = {0};
    // 非法用户进行大量尝试建连
    do {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i], 0, 0); // 客户端接口 GmcConnect
        printf("[INFO] conn_id: %d\n", i);
        i++;
    } while (i < CMD_SIZE);

    return ret;
}
#endif

