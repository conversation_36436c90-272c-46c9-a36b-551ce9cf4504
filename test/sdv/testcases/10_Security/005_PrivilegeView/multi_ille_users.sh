#!/bin/bash
# for gmimport multi-ille_user

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi


## 数据清除及准备  $CUR_DIR/multi-gmimport文件夹
cd $CUR_DIR
rm -rf multi_illg_users
mkdir multi_illg_users
cp $TEST_HOME/testcases/10_Security/005_PrivilegeView/clientIllegal_test.cpp ./multi_illg_users
sleep 2

# 构造多个 multi-client
cd $CUR_DIR/multi_illg_users
echo $1
# for ((i=1; i<=$1; i++))
for i in $(seq 1 $1) 
do
	#cp objVertex.gmpolicy objVertex$i.gmpolicy

    #构造多个客户端进程
	cp clientIllegal_test.cpp clientIllegal_test$i.cpp
	#sed -i "s/\"obj_name\": \"T39_all_type\"/\"obj_name\": \"T39_all_type"$i"\"/g" ./objVertex$i.gmpolicy
done

