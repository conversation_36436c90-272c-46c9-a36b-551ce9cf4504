/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: clientIllegal_test.cpp
 * Description: 合法用户尝试连接
 * Author: qinqianbao 995465
 * Create: 2022-04-12
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define CMD_SIZE 100000
#if !defined(RUN_DATACOM_HPE)
int main(int argc, char *argv[])
{
    int ret;
    int str_trun_num = atoi(argv[1]); // 字符串转整型
    int i = 0;
    do {
        GmcConnT *conn_t = NULL;
        GmcStmtT *stmt_t = NULL;
        // 导入白名单
        char command_user[CMD_SIZE] = "";
        char userFile_path[128] = "";
        sprintf(userFile_path, "multi_user_128/whitelist_01_test_%d.gmuser", str_trun_num);
        snprintf(command_user, CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, userFile_path,
            g_connServer);
        ret = executeCommand(command_user, "create db user success: 1, repeat: 0");
        EXPECT_EQ(GMERR_OK, ret);
        memset(command_user, 0, sizeof(command_user));

        // 合法用户创建同步连接
        ret = testGmcConnect(&conn_t, &stmt_t, 0, 0);
        EXPECT_EQ(GMERR_OK, ret);

        // 授予系统权限
        char command_sys[CMD_SIZE] = "";
        char file_path[128] = "";
        sprintf(file_path, "multi_128_sys/sysAll_%d.gmpolicy", str_trun_num);
        const char *obj_policy_file = file_path;
        snprintf(command_sys, CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file,
            g_connServer);
        ret = executeCommand(command_sys, "grant system priv to db user 1 rows ok");
        EXPECT_EQ(GMERR_OK, ret);
        memset(command_sys, 0, sizeof(command_sys));

        // 权限撤销
        snprintf(command_sys, CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file,
            g_connServer);
        ret = executeCommand(command_sys, "revoke system priv from db user 1 rows ok, 0 warning.");
        EXPECT_EQ(GMERR_OK, ret);
        memset(command_sys, 0, sizeof(command_sys));

        // 断连
        ret = testGmcDisconnect(conn_t, stmt_t);
        EXPECT_EQ(GMERR_OK, ret);

        // 删除白名单
        snprintf(command_user, CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, userFile_path,
            g_connServer);
        ret = executeCommand(command_user, "drop db user 1 rows ok");
        EXPECT_EQ(GMERR_OK, ret);
        memset(command_user, 0, sizeof(command_user));
        i++;
    } while (i < 8);

    return ret;
}
#endif

