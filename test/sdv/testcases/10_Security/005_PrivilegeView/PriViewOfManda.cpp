/*****************************************************************************
 Description  : 权限视图测试
 Notes        :
 History      :
 Author       : 覃乾宝 qwx995465
 Modification :
 Date         : 2021/07/23
*****************************************************************************/

#include "view.h"

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;
const char *g_normal_vertexlabel_name = "T39_all_type";
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
const char *cfgJson = R"({"max_record_count":1000000})";
const char *g_kv_table_name = "kv_01";
class PriViewOfManda : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 权限校验改为强制模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\" ");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schemaFile/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        // 恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void PriViewOfManda::SetUp()
{
    printf("[INFO] PriViewOfManda Start.\n");
    AW_CHECK_LOG_BEGIN();
}

void PriViewOfManda::TearDown()
{
    AW_CHECK_LOG_END();
    printf("[INFO] PriViewOfManda End.\n");
}

/*******************************************************************************
Description : 服务拉起，导入白名单127个user, 查询catalog视图、用户视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_001)
{
    // 导入白名单
    int ret;
    const char *allow_list_file = "allow_list/allow_list_128.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    user_view_128(g_command);  // 仅校验root126、root用户
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    catalog_view_user128(g_command);  // 校验userNum、roleNum
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 127, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，导入白名单126个group, 查询catalog视图、角色视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_002)
{
    // 导入白名单
    int ret;
    const char *allow_list_file = "allow_list/group_list_128.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_roleview);
    printf("%s\n", g_command);
    // system(g_command);
    role_view_128(g_command);  // 校验role_name
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    catalog_view_role128(g_command);  // 校验userNum、roleNum
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、catalog视图 all  sys_privilege
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_003)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysAll.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_usersys_view(g_command);  // 校验user系统权限
    memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_roleview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_usersys_view(g_command);  // 校验role系统权限
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_catalog_view(g_command);  // 校验userNum、roleNum
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    // system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、catalog视图 重复的系统权限
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_004)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysDuplicate.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_usersys_view(g_command);  // 校验user系统权限
    memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_roleview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_usersys_view(g_command);  // 校验role系统权限
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_catalog_view(g_command);  // roleNum、userNum
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图、catalog视图(vertex对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_005)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_userobj_view(g_command);  // 校验user对象权限
    memset(g_command, 0, sizeof(g_command));

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
    printf("%s\n", g_command);
    // system(g_command);
    execute_vertexobj_view(g_command);  // 校验vertex对象权限
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_catalog_view(g_command);  // roelNum、userNum
    memset(g_command, 0, sizeof(g_command));

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图、catalog视图(kvTable对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_006)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/kv_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objKvTable.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_userobj_view(g_command);  // 校验用户视图里Kv的对象权限
    memset(g_command, 0, sizeof(g_command));

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_kvTabel);
    printf("%s\n", g_command);
    // system(g_command);
    execute_kvTableobj_view(g_command);
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_catalog_view(g_command);
    memset(g_command, 0, sizeof(g_command));

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图、catalog视图：重复的对象权限
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_007)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // 连跑时屏蔽对group的校验，并不影响整体场景
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objDpVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_userobj_view(g_command);  // 校验系统权限
    memset(g_command, 0, sizeof(g_command));

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
    printf("%s\n", g_command);
    // system(g_command);
    execute_vertexobj_view(g_command);
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_catalog_view(g_command);
    memset(g_command, 0, sizeof(g_command));

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图、catalog视图(创建多个Vertex授予对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_008)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[20] = "";
    char label_schema[1024] = "";
    int vetexlabel_count = 10;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T_VERTEX%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T_VERTEX%d\", \"name\":\"T_VERTEX%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T_VERTEX%d", num);
        ret = GmcCreateVertexLabel(g_stmt, label_schema, config_json);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 赋予对象权限
    grant_vertexLabelObj(g_command);

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
    printf("%s\n", g_command);
    system(g_command);
    int count = 0;
    int label_num = execute_multiVertexobj_view(g_command, count);  // 只校验label_name个数
    EXPECT_EQ(label_num, 60);  // 赋予对象权限label的数目 改动：非Yang表labeljson和configjson的删除
    // catalog在vertexlabel视图中增加了索引信息，用例在校验数目时增加了十个(因为初期设计时labelname与indexname相同)
    // CATA_VERTEX_LABEL_INFO视图字段新增，导致用例中字符串匹配数量需要加倍
    memset(g_command, 0, sizeof(g_command));

    memset(labelName, 0, sizeof(labelName));

    const char *obj_policy_file0 = "label_name/sysVertexT0.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file0, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 1
    const char *obj_policy_file1 = "label_name/sysVertexT1.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 2
    const char *obj_policy_file2 = "label_name/sysVertexT2.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 3
    const char *obj_policy_file3 = "label_name/sysVertexT3.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file3, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 4
    const char *obj_policy_file4 = "label_name/sysVertexT4.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file4, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 5
    const char *obj_policy_file5 = "label_name/sysVertexT5.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file5, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 6
    const char *obj_policy_file6 = "label_name/sysVertexT6.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file6, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 7
    const char *obj_policy_file7 = "label_name/sysVertexT7.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file7, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 8
    const char *obj_policy_file8 = "label_name/sysVertexT8.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file8, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 9
    const char *obj_policy_file9 = "label_name/sysVertexT9.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file9, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(labelName, 20, "T_VERTEX%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图、catalog视图(创建多个KvTable授予对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_009)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/kv_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char label_name[20] = "";
    // 建表
    for (int i = 0; i < 10; i++) {
        snprintf(label_name, 20, "T_KV%d", i);
        ret = GmcKvCreateTable(g_stmt, label_name, config_json);
        ASSERT_EQ(GMERR_OK, ret);
    }

    grant_multiKvTableObj(g_command);

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_kvTabel);
    printf("%s\n", g_command);
    // system(g_command);
    int count = 0;
    int label_num = execute_multikvTableobj_view(g_command, count);  // 只校验KvTble个数
    EXPECT_EQ(label_num, 20);
    memset(g_command, 0, sizeof(g_command));

    memset(label_name, 0, sizeof(label_name));

    // grant 0
    const char *obj_policy_file0 = "label_name/sysKvTableT0.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file0, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 1
    const char *obj_policy_file1 = "label_name/sysKvTableT1.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file1, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 2
    const char *obj_policy_file2 = "label_name/sysKvTableT2.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 3
    const char *obj_policy_file3 = "label_name/sysKvTableT3.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file3, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 4
    const char *obj_policy_file4 = "label_name/sysKvTableT4.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file4, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 5
    const char *obj_policy_file5 = "label_name/sysKvTableT5.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file5, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 6
    const char *obj_policy_file6 = "label_name/sysKvTableT6.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file6, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 7
    const char *obj_policy_file7 = "label_name/sysKvTableT7.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file7, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 8
    const char *obj_policy_file8 = "label_name/sysKvTableT8.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file8, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // grant 9
    const char *obj_policy_file9 = "label_name/sysKvTableT9.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file9, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < 10; i++) {
        snprintf(label_name, 20, "T_KV%d", i);
        ret = GmcKvDropTable(g_stmt, label_name);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，校验user视图新增系统权限字段、vertex权限视图新增字段
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_010)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_userobj_filed(g_command);  // 校验user视图新增的系统权限字段
    memset(g_command, 0, sizeof(g_command));

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
    printf("%s\n", g_command);
    // system(g_command);
    execute_vertexobj_filed(g_command);  // 校验对象权限视图新增的对象权限字段
    memset(g_command, 0, sizeof(g_command));

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图、catalog视图(kvTable对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_011)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/kv_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objKvTable.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 对象权限视图 KvTable
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_kvTabel);
    printf("%s\n", g_command);
    // system(g_command);
    execute_kvTableobj_filed(g_command);  // 校验Kv对象权限视图新增字段
    memset(g_command, 0, sizeof(g_command));

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，循环1000次查询用户视图、vertex权限视图、catalog视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_012)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < 100; i++) {
        // 用户视图
        // printf("the value of i %d\n", i);
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
        // printf("%s\n", g_command);
        execute_userobj_view(g_command);  // 校验user对象权限
        memset(g_command, 0, sizeof(g_command));

        // 对象权限视图 vertex
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
        // printf("%s\n", g_command);
        execute_vertexobj_view(g_command);  // 校验vertex对象权限
        memset(g_command, 0, sizeof(g_command));

        // catalog info 视图
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
        // printf("%s\n", g_command);
        execute_catalog_view(g_command);  // roelNum、userNum
        memset(g_command, 0, sizeof(g_command));
    }

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，循环1000次查询用户视图、Kvble权限视图、catalog视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_013)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/kv_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objKvTable.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < 1000; i++) {
        // 用户视图
        // printf("the value of i %d\n", i);
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
        // printf("%s\n", g_command);
        // system(g_command);
        execute_userobj_view(g_command);  // 校验用户视图里Kv的对象权限
        memset(g_command, 0, sizeof(g_command));

        // 对象权限视图 vertex
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_kvTabel);
        // printf("%s\n", g_command);
        // system(g_command);
        execute_kvTableobj_view(g_command);
        memset(g_command, 0, sizeof(g_command));

        // catalog info 视图
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
        // printf("%s\n", g_command);
        // system(g_command);
        execute_catalog_view(g_command);
        memset(g_command, 0, sizeof(g_command));
    }

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

void *vertex_view_test(void *arg)
{
    char command_test[MAX_CMD_SIZE] = {0};  // 线程中使用局部变量替换全局变量
    // 用户视图
    snprintf(command_test, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    system(command_test);
    memset(command_test, 0, sizeof(command_test));

    // 对象权限视图 vertex
    snprintf(command_test, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
    system(command_test);
    memset(command_test, 0, sizeof(command_test));

    // catalog info 视图
    snprintf(command_test, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    system(command_test);
    memset(command_test, 0, sizeof(command_test));
    return NULL;
}

/*******************************************************************************
Description : 服务拉起，多线程查询用户视图、vertex权限视图、catalog视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_014)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    int tdNum = 49;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, vertex_view_test, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

void *kvTable_view_test(void *arg)
{
    char command_test[MAX_CMD_SIZE] = {0};  // 线程中使用局部变量替换全局变量
    // 用户视图
    snprintf(command_test, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userview);
    system(command_test);
    memset(command_test, 0, sizeof(command_test));

    // 对象权限视图 vertex
    snprintf(command_test, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_kvTabel);
    system(command_test);
    memset(command_test, 0, sizeof(command_test));

    // catalog info 视图
    snprintf(command_test, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_catalogview);
    system(command_test);
    memset(command_test, 0, sizeof(command_test));
    return NULL;
}

/*******************************************************************************
Description : 服务拉起，多线程查询用户视图、Kvble权限视图、catalog视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_015)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/kv_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);

    // grant
    const char *obj_policy_file2 = "object_privilege/objKvTable.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    int tdNum = 49;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, kvTable_view_test, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy -f %s -s %s ", g_toolPath, obj_policy_file2, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询权限视图(创建1024个Vertex授予对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_016)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[20] = "";
    char label_schema[1024] = "";
    int vetexlabel_count = 1024;                              // 创建1024张表
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {  // 可修改
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T39_all_type%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T39_all_type%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcCreateVertexLabel(g_stmt, label_schema, config_json);
        EXPECT_EQ(GMERR_OK, ret);
    }

    system("sh multi-obj.sh 1024");  // 创建1024个不同表名的obj-json文件
                                     // char schema_path[128] = "./multi-gmimport";
                                     // char cmd[1024];
    // snprintf(cmd, 1024, "%s/gmimport -c cache -f %s  -s %s", g_toolPath, schema_path,    userName,
    // passwd,g_connServer,); printf("\ncmd is: %s\n\n", cmd); sleep(1); system(cmd); getchar(); grant 1024张表obj
    for (int i = 1; i <= 1024; i++) {  // 可修改
        char file_path[128] = "";
        sprintf(file_path, "multi_1024obj/objVertex%d.gmpolicy", i);
        const char *obj_policy_file = file_path;
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file,
            g_connServer);
        // printf("[INFO]%s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "Import single policy file", "successfully.");
        EXPECT_EQ(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
    }

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_vertex);
    printf("%s\n", g_command);
    // system(g_command);
    execute_multiVertexobj_1024(g_command);  // 只校验label_name
    memset(g_command, 0, sizeof(g_command));

    memset(labelName, 0, sizeof(labelName));
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("rm -rf multi_1024obj");
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图(创建1030个Vertex授予对象权限)，只显示1024个对象权限视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_017)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[20] = "";
    char label_schema[1024] = "";
    int vetexlabel_count = 1030;                              // 创建超过1024张不同名的表
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {  // 可修改
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T39_all_type%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T39_all_type%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcCreateVertexLabel(g_stmt, label_schema, config_json);
        if (num <= 1024) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    system("sh multi-obj.sh 1024");  // 创建超过1024个不同表名的obj-json文件
                                     // char schema_path[128] = "./multi-gmimport";
                                     // char cmd[1024];
    // snprintf(cmd, 1024, "%s/gmimport -c cache -f %s  -s %s", g_toolPath, schema_path,    userName,
    // passwd,g_connServer,); printf("\ncmd is: %s\n\n", cmd); sleep(1); system(cmd); getchar(); grant 1024张表obj
    for (int i = 1; i <= 1024; i++) {  // 授予超过1024个对象权限
        char file_path[128] = "";
        sprintf(file_path, "multi_1024obj/objVertex%d.gmpolicy", i);
        const char *obj_policy_file = file_path;
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file,
            g_connServer);
        // printf("[INFO]%s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "Import single policy file", "successfully.");
        EXPECT_EQ(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath,
        g_userview);  // 用户视图
    printf("%s\n", g_command);
    // system(g_command); // 仅显示1024个对象权限的视图
    int count = 0;
    ret = execute_multiVertexobj_1030(g_command, count);  //
    EXPECT_EQ(1024, ret);                                 //
    memset(g_command, 0, sizeof(g_command));

    memset(labelName, 0, sizeof(labelName));  // 删除超过1024个不同名的表
    for (uint32_t num = 1; num <= 1024; num++) {
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("rm -rf multi_1024obj");
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询角色视图(创建1030个Vertex授予对象权限)，只显示1024个对象权限视图
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_018)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[20] = "";
    char label_schema[1024] = "";
    int vetexlabel_count = 1030;                              // 创建超过1024张不同名的表
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {  // 可修改
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T39_all_type%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T39_all_type%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcCreateVertexLabel(g_stmt, label_schema, config_json);
        if (num <= 1024) {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    system("sh multi-obj.sh 1024");  // 创建超过1024个不同表名的obj-json文件
                                     // char schema_path[128] = "./multi-gmimport";
                                     // char cmd[1024];
    // snprintf(cmd, 1024, "%s/gmimport -c cache -f %s  -s %s", g_toolPath, schema_path,    userName,
    // passwd,g_connServer,); printf("\ncmd is: %s\n\n", cmd); sleep(1); system(cmd); getchar(); grant 1024张表obj
    for (int i = 1; i <= 1024; i++) {  // 授予超过1024个对象权限
        char file_path[128] = "";
        sprintf(file_path, "multi_1024obj/objVertex%d.gmpolicy", i);
        const char *obj_policy_file = file_path;
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file,
            g_connServer);
        // printf("[INFO]%s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "Import single policy file", "successfully.");
        EXPECT_EQ(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
    }

    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userName, g_connServer,
    // g_passwd,    g_userview); // 用户视图 printf("%s\n", g_command); system(g_command); // 仅显示1024个对象权限的视图
    // memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_roleview);
    printf("%s\n", g_command);
    // system(g_command); // 仅显示1024个对象权限的视图
    int count = 0;
    ret = execute_multiVertexobj_1030(g_command, count);  //
    EXPECT_EQ(1024, ret);                                 //
    memset(g_command, 0, sizeof(g_command));

    memset(labelName, 0, sizeof(labelName));  // 删除超过1024个不同名的表
    for (uint32_t num = 1; num <= 1024; num++) {
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("rm -rf multi_1024obj");
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图、权限视图(创建1024个KvTable授予对象权限)
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_019)
{
    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 导入必备系统权限 create get drop
    const char *sys_policy_file = "system_privilege/sysVertex014.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char labelName[20] = "";
    char label_schema[1024] = "";
    int vetexlabel_count = 1024;                              // 创建1024张Kv表
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {  // 可修改
        sprintf(labelName, "T39_all_type%d", num);
        ret = GmcKvCreateTable(g_stmt, labelName, cfgJson);
        EXPECT_EQ(GMERR_OK, ret);
    }

    system("sh multi-objKv.sh 1024");  // 创建1024个不同表名的obj-json文件
                                       // char schema_path[128] = "./multi-gmimport";
                                       // char cmd[1024];
    // snprintf(cmd, 1024, "%s/gmimport -c cache -f %s  -s %s", g_toolPath, schema_path,    userName,
    // passwd,g_connServer,); printf("\ncmd is: %s\n\n", cmd); sleep(1); system(cmd); getchar(); grant 1024张表obj
    for (int i = 1; i <= 1024; i++) {  // 可修改
        char file_path[128] = "";
        sprintf(file_path, "multi_1024obj/objKvTable_%d.gmpolicy", i);
        const char *obj_policy_file = file_path;
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file,
            g_connServer);
        // printf("[INFO]%s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "Import single policy file", "successfully.");
        EXPECT_EQ(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath,
        g_userview);  // 用户视图
    printf("%s\n", g_command);
    // system(g_command); // 仅显示1024个对象权限的视图
    execute_multiKvObj_1024_users(g_command);  // 只校验label_name
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_roleview);
    printf("%s\n", g_command);
    // system(g_command); // 角色视图显示1024个对象权限
    execute_multiKvObj_1024_users(g_command);  // 只校验label_name
    memset(g_command, 0, sizeof(g_command));

    // 对象权限视图 vertex
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_objview_kvTabel);
    printf("%s\n", g_command);
    // system(g_command);
    int count = 0;
    ret = execute_multiVertexobj_1030(g_command, count);  //
    EXPECT_EQ(2048, ret);
    execute_multiKvObj_1024(g_command);  // 只校验label_name
    memset(g_command, 0, sizeof(g_command));

    memset(labelName, 0, sizeof(labelName));
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcKvDropTable(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("rm -rf multi_1024obj");
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

#if 0
/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图(创建超过1500个KvTable授予对象权限)，只显示1024
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_020)
{
    // 导入白名单 
    const char *allow_list_file="allow_list/vertex_user.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file, g_connServer  );
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.", "create db role 1 rows ok");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command,0,sizeof(g_command));
    
    // 导入必备系统权限 create get drop
    const char *sys_policy_file="system_privilege/sysVertex014.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer  );
    printf("[INFO]%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command,0,sizeof(g_command));

    // 建连 用户登录权限
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK,ret);

    char labelName[20] = "";
    char label_schema[1024]="";
    int vetexlabel_count = 1500; // 创建1024张Kv表
    for (uint32_t num = 1; num <= vetexlabel_count; num++) { // 可修改
        sprintf(labelName, "T39_all_type%d", num); 
        ret = GmcKvCreateTable(g_stmt, labelName, cfgJson);
        EXPECT_EQ(GMERR_OK, ret);
    }

	system("sh multi-objKv.sh 1500");   // 创建1024个不同表名的obj-json文件  
	// char schema_path[128] = "./multi-gmimport";	
	// char cmd[1024]; 
    // snprintf(cmd, 1024, "%s/gmimport -c cache -f %s  -s %s", g_toolPath, schema_path,    userName, passwd,g_connServer);
    // printf("\ncmd is: %s\n\n", cmd);
    // sleep(1);   
    // system(cmd);
    // getchar();
    // grant 1024张表obj
    for(int i = 1; i <= 1500; i++) {  // 可修改
        char file_path[128] = "";
        sprintf(file_path, "multi_1024obj/objKvTable_%d.gmpolicy", i);
        const char *obj_policy_file = file_path;
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file, g_connServer  );
        printf("[INFO]%s\n", g_command);
        // system(g_command);
        ret = executeCommand(g_command, "Import single policy file", "successfully.");
        EXPECT_EQ(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
    }

    // 用户视图 KvTale
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath,    g_userview);
    printf("%s\n", g_command);
    system(g_command); 
    // execute_multiVertexobj_1024(g_command); // 只校验label_name
    memset(g_command, 0, sizeof(g_command));

    memset(labelName, 0, sizeof(labelName));
    for (uint32_t num = 1; num <= vetexlabel_count; num++) {
        snprintf(labelName, 20, "T39_all_type%d", num);
        ret = GmcKvDropTable(g_stmt, labelName); 
        EXPECT_EQ(GMERR_OK, ret);
    }
	system("rm -rf multi_1024obj"); 
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

// 备注 系统权限和对象权限暂不支持group 已有需求单：DTS20210716085XN1P0D00 ---->所以仅针对root (改动sudoers或usermod
// sudo 暂不考虑) 只查看角色视图 (非root), group没有授予系统权限、对应的角色也没有
/*******************************************************************************
Description : 服务拉起，gmsysview查询权限视图(创建1024个Vertex授予对象权限) 查看角色视图，用例仅针对group
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_020)
{
    // 创建os group且把root用户添加到group中
    int count = 127;
    // 删除os group
    // for(int i = 1; i <= count; i++) {
    //     snprintf(g_command, MAX_CMD_SIZE, "groupdel db_group%d", i);
    //     // printf("%s\n", g_command);
    //     system(g_command);
    //     memset(g_command,0,sizeof(g_command));
    // }
    for (int i = 1; i <= count; i++) {
        snprintf(g_command, MAX_CMD_SIZE, "groupadd db_group%d", i);  // 增加组
        // printf("%s\n", g_command);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "usermod -a -G db_group%d root", i);  // 追加用户的附加组
        // printf("%s\n", g_command);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
    }

    // 导入白名单
    const char *allow_list_file = "allow_list/vertex_group_test.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    // int ret = executeCommand(g_command, "create db role 30 rows ok");
    // EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // // yonghu视图
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userName, g_connServer,
    // g_passwd,    g_userview); printf("%s\n", g_command); system(g_command); memset(g_command,0,sizeof(g_command));

    // 建连 用户登录权限
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // // yonghu视图
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userName, g_connServer,
    // g_passwd,    g_userview); printf("%s\n", g_command); system(g_command); memset(g_command,0,sizeof(g_command));
    // 角色视图
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, g_userName, g_connServer,
    // g_passwd,    g_roleview); printf("%s\n", g_command); system(g_command);

    // 删除os group
    for (int i = 1; i <= count; i++) {
        snprintf(g_command, MAX_CMD_SIZE, "groupdel db_group%d", i);
        // printf("%s\n", g_command);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 非法用户建连
void *thread_illegal_user(void *args)
{
    // 120 多个非法用户多个客户端
    char string[100] = "";
    int thr_id = *(int *)args + 1;  // 根据脚本可知
    sprintf(string, "./multi_illg_users/clientIllegal_test%d", thr_id);
    int ret = system(string);
    return NULL;
}

#ifdef ENV_RTOSV2X
#define TREAD_NUM 64
#else
#define TREAD_NUM 120
#endif
/*******************************************************************************
Description : 会话攻击：非法与合法用户同时进行建连
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_021)
{
    int ret;
    pthread_t vertexlabel_01[TREAD_NUM];
    int index[TREAD_NUM] = {0};
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret =
            pthread_create(&vertexlabel_01[i], NULL, thread_illegal_user, (void *)&index[i]);  // illegal user 非法用户
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(vertexlabel_01[i], NULL);
    }
}

// 合法用户建连且授予系统权限
void *thread_legal_user(void *args)
{
    // 多个合法用户多个客户端
    char string[100] = "";
    char num_turn_str[10] = "";
    int thr_id = *(int *)args + 1;        // 根据脚本可知
    sprintf(num_turn_str, "%d", thr_id);  // 整型转字符串
    sprintf(string, "./multi_le_users/clientLegitimate_test%d %s", thr_id, num_turn_str);
    int ret = system(string);
    return NULL;
}

/*******************************************************************************
Description : 并发多个合法用户进行建连授予权限
*******************************************************************************/
TEST_F(PriViewOfManda, SEC_005_PrivilegeViewOfMandatoryMode_022)
{
    int ret;
    // 128个白名单用户
    system("sh multi_users_128.sh 120");
    // 128个系统权限配置
    system("sh multi_sys_128.sh 120");
    pthread_t legal_user_01[TREAD_NUM];
    int index[TREAD_NUM] = {0};
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&legal_user_01[i], NULL, thread_legal_user, (void *)&index[i]);  // legal user 合法用户
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(legal_user_01[i], NULL);
    }
}
