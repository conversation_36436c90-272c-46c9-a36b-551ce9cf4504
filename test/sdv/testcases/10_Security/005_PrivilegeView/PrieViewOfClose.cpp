/*****************************************************************************
 Description  : 权限视图测试
 Notes        :
 History      :
 Author       : 覃乾宝 qwx995465
 Modification :
 Date         : 2021/07/23
*****************************************************************************/

#include "view.h"

class PrieViewOfClose : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void PrieViewOfClose::SetUp()
{
    printf("[INFO] PrieViewOfClose Start.\n");
    AW_CHECK_LOG_BEGIN();
}

void PrieViewOfClose::TearDown()
{
    AW_CHECK_LOG_END();
    printf("[INFO] PrieViewOfClose End.\n");
}

/*******************************************************************************
Description : 服务拉起，校验用户视图、角色视图、catalog视图新增字段
*******************************************************************************/
TEST_F(PrieViewOfClose, SEC_005_PrivilegeViewOfCloseMode_001)
{
    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_user_view(g_command);  // 校验用户视图新增字段
    memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_roleview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_role_view(g_command);  // 校验角色视图新增字段
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    execute_catalog_view(g_command);  // 校验roleNum、userNum字段
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，导入128个user, gmsysview查询用户视图、catalog视图
*******************************************************************************/
TEST_F(PrieViewOfClose, SEC_005_PrivilegeViewOfCloseMode_002)
{
    system("sh multi-users.sh 999");
    int ret;
    char const *user = "multi_user/whitelist_01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    close_user_128_view(g_command);  // 校验user: root9999, process: abc9999
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    close_catalog_128users(g_command);  // 校验roleNum、userNum
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "drop db user 1000 rows ok");
    memset(g_command, 0, sizeof(g_command));

    system("rm -rf multi_user");  // 调试是否生成期望的文件, 可注释该行
}

/*******************************************************************************
Description : 服务拉起，导入128个group, gmsysview查询角色视图、catalog info视图
*******************************************************************************/
TEST_F(PrieViewOfClose, SEC_005_PrivilegeViewOfCloseMode_003)
{
    char const *user = "allow_list/close_group_128.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_roleview);
    printf("%s\n", g_command);
    // system(g_command);
    close_group_128_view(g_command);  // 校验group_127、abc_127
    memset(g_command, 0, sizeof(g_command));

    // catalog info 视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_catalogview);
    printf("%s\n", g_command);
    // system(g_command);
    close_catalog_128groups(g_command);  // 校验roleNum、userNum
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db group. success: 128, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/*******************************************************************************
Description : 服务拉起，gmsysview查询用户视图、角色视图(len=128)
*******************************************************************************/
TEST_F(PrieViewOfClose, SEC_005_PrivilegeViewOfCloseMode_004)
{
    char const *user = "./allow_list/fun_username_128.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // 用户视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_userview);
    printf("%s\n", g_command);
    // system(g_command);
    close_username_128_userView(g_command);
    memset(g_command, 0, sizeof(g_command));

    // 角色视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,
        g_roleview);
    printf("%s\n", g_command);
    // system(g_command);
    close_username_128_roleView(g_command);  //
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist  -f %s  -s %s", g_toolPath, user, g_connServer);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
