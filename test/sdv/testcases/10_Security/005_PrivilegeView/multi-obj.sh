#!/bin/bash
# for gmimport multi-object_privilege

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi


## 数据清除及准备  $CUR_DIR/multi-gmimport文件夹
cd $CUR_DIR
rm -rf multi_1024obj
mkdir multi_1024obj
cp $TEST_HOME/testcases/10_Security/005_PrivilegeView/object_privilege/objVertex.gmpolicy ./multi_1024obj
sleep 2

# 构造多个 multi-object_privilege
cd $CUR_DIR/multi_1024obj
echo $1
# for ((i=1; i<=$1; i++))
for i in $(seq 1 $1) 
do
	cp objVertex.gmpolicy objVertex$i.gmpolicy
	sed -i "s/\"obj_name\": \"T39_all_type\"/\"obj_name\": \"T39_all_type"$i"\"/g" ./objVertex$i.gmpolicy
done

