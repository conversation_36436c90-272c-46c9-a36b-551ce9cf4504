/*****************************************************************************
 Description  : 系统权限、对象权限 支持批量配置
 Notes        :
 参数测试:
        001.在policy文件对象权限配置中,obj_name为array类型,policy文件格式内容正确,批量导入对象权限成功
        002.在policy文件对象权限配置中,obj_name为array类型,其中一个元素为空值,批量导入对象权限成功,打印告警信息
        003.在policy文件对象权限配置中,obj_name为array类型,其中一个元素int类型,批量导入对象权限失败
        004.在policy文件对象权限配置中,obj_name为array类型,其中一个元素为float类型,批量导入对象权限失败
        005.在policy文件对象权限配置中,obj_name为array类型,其中一对元素为重复值,批量导入对象权限失败
        006.在policy文件对象权限配置中,obj_name为array类型,其中一个元素为array类型,批量导入对象权限失败
        007.在policy文件对象权限配置中,obj_tape为VERTEX_LABEL,policy文件格式内容正确,批量导入对象权限成功
        008.在policy文件对象权限配置中,obj_tape为VERTEX_LABEL,obj_name中一个元素为KV_TABLE,批量导入对象权限成功
        009.在policy文件对象权限配置中,obj_tape为KV_TABLE,policy文件格式内容正确,批量导入对象权限成功
        010.在policy文件对象权限配置中,obj_tape为KV_TABLE,obj_name中一个元素为VERTEX_LABEL,批量导入对象权限成功
 基本功能:
        001.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,预期成功
        002.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,预期成功
        003.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限和KV_TABLE的所有系统权限,预期成功
        004.在同一个policy文件中,授予多个用户所有系统权限,预期成功
        005.在同一个policy文件中,授予多个用户KV_TABLE所有VERTEX_LABEL系统权限,预期失败
        006.在同一个policy文件中,授予多个用户相同的系统权限,预期成功
        007.在同一个policy文件中,授予多个用户EDGE_LABEL所有系统权限,预期成功
        008.在同一个policy文件中,授予多个用户NAMESPACE所有系统权限,预期成功
        009.在同一个policy文件中,授予多个用户RESOURCE所有系统权限,预期成功
        010.在同一个policy文件中,授予多个用户LABEL_SUBS所有系统权限,预期成功
        011.在同一个policy文件中,授予多个用户所有系统权限,预期成功
        012.在同一个policy文件中,授予多个用户多个对象的所有对象权限,预期成功
        013.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,预期成功
        014.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,预期成功
        015.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限和KV_TABLE所有对象权限,预期成功
        016.在同一个policy文件中,授予多个用户VERTEX_LABEL所有KV_TABLE对象权限,预期成功
        017.在同一个policy文件中,授予多个用户KV_TABLE所有VERTEX_LABEL对象权限,预期失败
        018.在同一个policy文件中,授予多个用户VERTEX_LABEL和KV_TABLE所有VERTEX_LABEL对象权限,预期成功
        019.在同一个policy文件中,授予多个用户VERTEX_LABEL和KV_TABLE所有的对象权限,预期成功
 异常场景:
        001.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个权限重复,预期失败
        002.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个权限错误,预期失败
        003.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个权限为空,预期失败
        004.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个权限重复,预期失败
        005.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个权限错误,预期失败
        006.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个权限为空,预期失败
        007.在同一个policy文件中,授予多个用户相同的系统权限,同一批用户存在重复,预期失败
        008.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个用户不存在,预期失败
        009.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个用户为空,预期失败
        010.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一对用户重复,预期失败
        011.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个用户不存在,预期失败
        012.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个用户为空,预期失败
        013.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一对用户重复,预期失败
        014.在同一个policy文件中,授予多个用户多个对象的所有对象权限,其中一个权限重复,预期失败
        015.在同一个policy文件中,授予多个用户多个对象的所有对象权限,其中一个权限错误,预期失败
        016.在同一个policy文件中,授予多个用户多个对象的所有对象权限,其中一个权限为空,预期失败
        017.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个权限重复,预期失败
        018.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个权限错误,预期失败
        019.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个权限为空,预期失败
        020.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个权限重复,预期失败
        021.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个权限错误,预期失败
        022.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个权限为空,预期失败
        023.在同一个policy文件中,授予多个用户相同的对象权限,同一批用户存在重复,预期失败
        024.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个用户不存在,预期失败
        025.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个用户为空,预期失败
        026.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一对用户存在重复,预期失败
        027.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个用户不存在,预期失败
        028.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个用户为空,预期失败
        029.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一对用户存在重复,预期失败
        030.在policy文件对象权限配置中,obj_name为array类型,其中一个元素不存在,批量导入对象权限成功
        031.在policy文件对象权限配置中,obj_name为array类型,其中一个元素不存在,批量导入对象权限成功
        032.在policy文件对象权限配置中,obj_name为array类型,obj_tape为VERTEX_LABEL,其中一个元素不存在,批量导入对象权限成功
        033.在policy文件对象权限配置中,obj_name为array类型,obj_tape为KV_TABLE,其中一个元素不存在,批量导入对象权限成功
 Author       : 文思奇 wwx1060458
 Created      : 2021.10.29
 Modification :
*****************************************************************************/

extern "C" {
}

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"
#include <string>

#define FULLTABLE 0xff
int ret = 0;
GmcConnT *conn;
GmcStmtT *stmt;
GmcConnT *g_conn_async;
GmcStmtT *g_stmt_async;

#define MAX_CMD_SIZE 1024
char g_command[1024];
char const *label_name = "T39_all_type";
char const *label_name1 = "T39_all_type1";
char const *label_name2 = "T39_all_type2";
char tableName[128] = "kv";
char tableName1[128] = "kv1";
char tableName2[128] = "kv2";
int data_num = 1000;
int subIndex = 0;
const char *g_configJson = R"({"max_record_num":1000,"max_record_num_check":true})";
char *test_schema = NULL;
char *test_schema1 = NULL;
char *test_schema2 = NULL;
const char *userName = "root";
const char *passwd = "XXpwd";
const char *g_normal_pk_name = "T39_K0";

class SysObjBatche_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SysObjBatche_test::SetUpTestCase()
{
    //权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
}

void SysObjBatche_test::TearDownTestCase()
{
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
void SysObjBatche_test::SetUp()
{
    //启server
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //导入白名单
    const char *allow_list_file = "./allow_list/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    readJanssonFile("schema/VertexLabel.gmjson", &test_schema);
    EXPECT_NE((void *)NULL, test_schema);
    readJanssonFile("schema/VertexLabel_01.gmjson", &test_schema1);
    EXPECT_NE((void *)NULL, test_schema1);
    readJanssonFile("schema/VertexLabel_02.gmjson", &test_schema2);
    EXPECT_NE((void *)NULL, test_schema2);
}
void SysObjBatche_test::TearDown()
{
    const char *allow_list_file = "./allow_list/allow_list.gmuser";
    // 删除白名单
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    free(test_schema);
    free(test_schema1);
    free(test_schema2);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 001.在policy文件对象权限配置中,obj_name为array类型,policy文件格式内容正确,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_001)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.在policy文件对象权限配置中,obj_name为array类型,其中一个元素为空值,批量导入对象权限成功,打印告警信息
TEST_F(SysObjBatche_test, Security_009_002)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_02.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.在policy文件对象权限配置中,obj_name为array类型,其中一个元素int类型,批量导入对象权限失败
TEST_F(SysObjBatche_test, Security_009_003)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_03.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 004.在policy文件对象权限配置中,obj_name为array类型,其中一个元素为float类型,批量导入对象权限失败
TEST_F(SysObjBatche_test, Security_009_004)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_04.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 005.在policy文件对象权限配置中,obj_name为array类型,其中一对元素为重复值,批量导入对象权限失败
TEST_F(SysObjBatche_test, Security_009_005)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_05.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
// 006.在policy文件对象权限配置中,obj_name为array类型,其中一个元素为array类型,批量导入对象权限失败
TEST_F(SysObjBatche_test, Security_009_006)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_06.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.在policy文件对象权限配置中,obj_tape为VERTEX_LABEL,policy文件格式内容正确,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_007)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_07.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.在policy文件对象权限配置中,obj_tape为VERTEX_LABEL,obj_name中一个元素为KV_TABLE,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_008)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_08.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);  // 因后续检测，开发修改代码忽略了不存在的表，类型不正确被判断为表不存在
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.在policy文件对象权限配置中,obj_tape为KV_TABLE,policy文件格式内容正确,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_009)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_09.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.在policy文件对象权限配置中,obj_tape为KV_TABLE,obj_name中一个元素为VERTEX_LABEL,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_010)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_10.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);  // 因后续检测，开发修改代码忽略了不存在的表，类型不正确被判断为表不存在
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 基本功能:
// 001.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_011)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_13.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_012)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_12.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限和KV_TABLE的所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_013)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.在同一个policy文件中,授予多个用户所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_014)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_14.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 005.在同一个policy文件中,授予多个用户KV_TABLE所有VERTEX_LABEL系统权限,预期失败
TEST_F(SysObjBatche_test, Security_009_015)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_15.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 006.在同一个policy文件中,授予多个用户相同的系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_016)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_17.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.在同一个policy文件中,授予多个用户EDGE_LABEL所有系统权限,预期失败
TEST_F(SysObjBatche_test, Security_009_017)
{
    char *edge_schema = NULL;
    char *delta_json = NULL;
    char edge_labelName[] = "from_T20_to_T21";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_18.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

// 008.在同一个policy文件中,授予多个用户NAMESPACE所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_018)
{

    const char *nameSpace = (const char *)"use";
    const char *userName1 = (const char *)"abc";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_19.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateNamespace(stmt, nameSpace, userName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.在同一个policy文件中,授予多个用户RESOURCE所有系统权限,预期失败
TEST_F(SysObjBatche_test, Security_009_019)
{
    const char *userName = "root";
    const char *passwd = "XXpwd";
    const char *gExternalName = "resource_pool_extended";
    static const char *gResPoolName = "resource_pool_test";
    static const char *gResPoolTest =
        R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 20000,
        "order" : 0,
        "alloc_type" : 0
    })";
    static const char *gResPoolExternal =
        R"({
        "name" : "resource_pool_extended",
        "pool_id" : 1,
        "start_id" : 1,
        "capacity" : 20000,
        "order" : 0,
        "alloc_type" : 0
    })";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_20.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create ResourcePool
    ret = GmcCreateResPool(stmt, gResPoolTest);
    EXPECT_EQ(GMERR_OK, ret);
    // create ResPoolExternal
    ret = GmcCreateResPool(stmt, gResPoolExternal);
    EXPECT_EQ(GMERR_OK, ret);
    // 绑定到扩展资源池
    ret = GmcBindExtResPool(stmt, gResPoolName, gExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    //解绑扩展资源池
    ret = GmcUnbindExtResPool(stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除资源池
    ret = GmcDestroyResPool(stmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(stmt, gExternalName);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

#define MAX_NAME_LENGTH 128
void kv_sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, i, index;
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char *outKey = NULL, *outValue = NULL;
    uint32_t outKeyLen = 512, outValueLen = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    void *tableLabel = 0;
    char key[128] = "zhangsan";
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->msgType) {
                case 1:  //推送old object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            index = ((int *)user_data->new_value)[subIndex];
                            sprintf(key, "zhangsan_%d", index);
                            printf("[OLD OBJECT] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            //读old
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_NO_DATA);
                            } else {
                                index = ((int *)user_data->old_value)[subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                printf("[OLD OBJECT] GMC_SUB_EVENT_KV_SET old_value is %d\r\n", index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            //读old
                            index = ((int *)user_data->old_value)[subIndex];
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                            }
                            printf("[OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 2:  //推送new object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            index = ((int *)user_data->new_value)[subIndex];
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                            }
                            printf("[NEW OBJECT] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));

                            //读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 3:  //推送new object + old object
                {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            index = ((int *)user_data->new_value)[subIndex];
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index);
                            }
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));

                            //读old
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET old insert\r\n");
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                index = ((int *)user_data->old_value)[subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET old update old_value is %d\r\n",
                                // index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 4:  //推送key
                {
                    //创建同步连接
                    ret = testGmcConnect(&conn, &stmt);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new
                            index = ((int *)user_data->new_value)[subIndex];
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                                printf("[KEY] GMC_SUB_EVENT_KV_SET insert new_value is %d\r\n", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                                printf("[KEY] GMC_SUB_EVENT_KV_SET update new_value is %d\r\n", index);
                            }
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(index, *(uint32_t *)outValue);
                            EXPECT_EQ(4, outValueLen);

                            //读old
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                                EXPECT_EQ(GMERR_NO_DATA, ret);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                                ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(outKey[i], key[i]);
                                }
                                ret = GmcKvGet(stmt, key, strlen(key), outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                EXPECT_EQ(index, *(uint32_t *)outValue);
                                EXPECT_EQ(4, outValueLen);
                            }
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            index = ((int *)user_data->old_value)[subIndex];
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            outKeyLen = 512;
                            //读old
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                            }
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
                            ret = testGmcGetLastError(NULL);
                            EXPECT_EQ(GMERR_OK, ret);

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    ret = testGmcDisconnect(conn, stmt);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case 5:  //推送key 和 old object
                {
                    //创建同步连接
                    ret = testGmcConnect(&conn, &stmt);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            //读new
                            index = ((int *)user_data->old_value)[subIndex];
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            //读old
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                            }
                            outKeyLen = 512;
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
                            ret = testGmcGetLastError(NULL);
                            EXPECT_EQ(GMERR_OK, ret);

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    ret = testGmcDisconnect(conn, stmt);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case 7:  //推送new object + old object + key
                {
                    //创建同步连接
                    ret = testGmcConnect(&conn, &stmt);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            //读new key
                            index = ((int *)user_data->new_value)[subIndex];
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                            }
                            printf("[NEW/OLD OBJECT/KEY] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubGetKey(subStmt, (const void **)outKey, &outKeyLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(outKey[i], key[i]);
                            }
                            ret = GmcKvGet(stmt, key, strlen(key), outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(index, *(uint32_t *)outValue);
                            EXPECT_EQ(4, outValueLen);

                            //读old key
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET old insert\r\n");
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_NO_DATA);
                            } else {
                                index = ((int *)user_data->old_value)[subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET old update old_value is %d\r\n",
                                // index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }

                            //读new kv
                            index = ((int *)user_data->new_value)[subIndex];
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - data_num);
                            }
                            printf("[NEW/OLD OBJECT/KEY] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            //读old kv
                            if (((bool *)user_data->isReplace_insert)[subIndex]) {
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(true, ret == GMERR_UNEXPECTED_NULL_VALUE || ret == GMERR_NO_DATA);
                            } else {
                                index = ((int *)user_data->old_value)[subIndex];
                                sprintf(key, "zhangsan_%d", index);
                                printf("[NEW/OLD OBJECT/KEY] GMC_SUB_EVENT_KV_SET old_value is %d\r\n", index);
                                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                                EXPECT_EQ(GMERR_OK, ret);
                                ret = GmcKvGetFromStmt(
                                    subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                                EXPECT_EQ(GMERR_OK, ret);
                                for (uint32_t i = 0; i < outKeyLen; i++) {
                                    EXPECT_EQ(key[i], outKey[i]);
                                }
                                EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));
                            }
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    ret = testGmcDisconnect(conn, stmt);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid msgType %d\r\n", info->msgType);
                    break;
                }
            }
        }
        subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

// 010.在同一个policy文件中,授予多个用户LABEL_SUBS所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_020)
{
    int data_num = 1000;
    int subIndex = 0;
    SnUserDataT *user_data;
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    char g_tableName[128] = "KV6";
    char configJson[128] = "{\"max_record_num\":100000}";
    char *sub_info = NULL;
    const char *subConnName = "subConnName";
    const char *subName = "subVertexLabel";

    //导入SN的系统权限create drop
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_21.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    subIndex = 0;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * data_num * 10);
    user_data->old_value = (int *)malloc(sizeof(int) * data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * data_num * 10);
    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * data_num * 10);

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    //创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    //打开kv表
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t i, userDataIdx = 0;
    readJanssonFile("schema/KV_subInfo_001.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    // 创建订阅关系有权限
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, g_conn_sub, kv_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    char key[128] = "zhangsan";

    //写
    for (i = 0; i < data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        sprintf(key, "zhangsan_%d", i);
        int value = i;

        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_KV_SET, data_num);
    EXPECT_EQ(GMERR_OK, ret);
    // drop success
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    // drop kv
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    ASSERT_EQ(GMERR_OK, ret);
    free(sub_info);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
    ret = close_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

// 011.在同一个policy文件中,授予多个用户所有系统权限,预期成功
TEST_F(SysObjBatche_test, Security_009_021)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.在同一个policy文件中,授予多个用户多个对象的所有对象权限,预期成功
TEST_F(SysObjBatche_test, Security_009_022)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_23.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,预期成功
TEST_F(SysObjBatche_test, Security_009_023)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_24.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,预期成功
TEST_F(SysObjBatche_test, Security_009_024)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_25.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 015.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限和KV_TABLE所有对象权限,预期成功
TEST_F(SysObjBatche_test, Security_009_025)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_26.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 016.在同一个policy文件中,授予多个用户VERTEX_LABEL所有KV_TABLE对象权限,预期成功(预期错误,KV_TABLE所有对象权限被VERTEX_LABEL包含,因此授权成功2022/9/8)
TEST_F(SysObjBatche_test, Security_009_026)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_27.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.在同一个policy文件中,授予多个用户KV_TABLE所有VERTEX_LABEL对象权限,预期失败
TEST_F(SysObjBatche_test, Security_009_027)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_28.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret); // gmrule导入系统权限返回信息缩短适配2022/9/14
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.在同一个policy文件中,授予多个用户VERTEX_LABEL和KV_TABLE所有VERTEX_LABEL对象权限,预期成功
TEST_F(SysObjBatche_test, Security_009_028)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_29.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);  // 因后续检测，开发修改代码忽略了不存在的表，类型不正确被判断为表不存在
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.在同一个policy文件中,授予多个用户相同的对象权限,预期成功
TEST_F(SysObjBatche_test, Security_009_029)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_30.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 异常场景:
// 001.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个权限重复,预期失败
TEST_F(SysObjBatche_test, Security_009_030)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_31.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 002.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个权限错误,预期失败
TEST_F(SysObjBatche_test, Security_009_031)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_32.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command);
    // EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 003.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个权限为空,预期失败
TEST_F(SysObjBatche_test, Security_009_032)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_33.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 004.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个权限重复,预期失败
TEST_F(SysObjBatche_test, Security_009_033)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_34.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 005.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个权限错误,预期失败
TEST_F(SysObjBatche_test, Security_009_034)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_35.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 006.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个权限为空,预期失败
TEST_F(SysObjBatche_test, Security_009_035)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_36.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 007.在同一个policy文件中,授予多个用户相同的系统权限,同一批用户存在重复,预期失败
TEST_F(SysObjBatche_test, Security_009_036)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_37.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 008.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个用户不存在,预期成功
TEST_F(SysObjBatche_test, Security_009_037)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_39.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 009.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个用户为空,预期失败
TEST_F(SysObjBatche_test, Security_009_038)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_40.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 010.在同一个policy文件中,授予多个用户VERTEX_LABEL所有系统权限,其中一个用户重复,预期失败
TEST_F(SysObjBatche_test, Security_009_039)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_38.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 011.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个用户不存在,预期成功
TEST_F(SysObjBatche_test, Security_009_040)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_41.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 012.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一个用户为空,预期失败
TEST_F(SysObjBatche_test, Security_009_041)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_42.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 013.在同一个policy文件中,授予多个用户KV_TABLE所有系统权限,其中一对用户重复,预期失败
TEST_F(SysObjBatche_test, Security_009_042)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_43.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 014.在同一个policy文件中,授予多个用户多个对象的所有对象权限,其中一个权限重复,预期失败
TEST_F(SysObjBatche_test, Security_009_043)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_44.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
}

// 015.在同一个policy文件中,授予多个用户多个对象的所有对象权限,其中一个权限错误,预期失败
TEST_F(SysObjBatche_test, Security_009_044)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_45.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 016.在同一个policy文件中,授予多个用户多个对象的所有对象权限,其中一个权限为空,预期失败
TEST_F(SysObjBatche_test, Security_009_045)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_46.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 017.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个权限重复,预期失败
TEST_F(SysObjBatche_test, Security_009_046)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_47.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个权限错误,预期失败
TEST_F(SysObjBatche_test, Security_009_047)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_48.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个权限为空,预期失败
TEST_F(SysObjBatche_test, Security_009_048)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_49.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 020.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个权限重复,预期失败
TEST_F(SysObjBatche_test, Security_009_049)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_50.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 021.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个权限错误,预期失败
TEST_F(SysObjBatche_test, Security_009_050)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_51.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 022.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个权限为空,预期失败
TEST_F(SysObjBatche_test, Security_009_051)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_52.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023.在同一个policy文件中,授予多个用户相同的对象权限,同一批用户存在重复,预期失败
TEST_F(SysObjBatche_test, Security_009_052)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_53.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);// gmrule导入系统权限返回信息缩短适配2022/9/14
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个用户不存在,预期成功
TEST_F(SysObjBatche_test, Security_009_053)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_55.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一个用户为空,预期失败
TEST_F(SysObjBatche_test, Security_009_054)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_56.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 026.在同一个policy文件中,授予多个用户VERTEX_LABEL所有对象权限,其中一对用户存在重复,预期失败
TEST_F(SysObjBatche_test, Security_009_055)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_57.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);// gmrule导入系统权限返回信息缩短适配2022/9/14
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 027.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个用户不存在,预期成功
TEST_F(SysObjBatche_test, Security_009_056)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_58.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 028.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一个用户为空,预期失败
TEST_F(SysObjBatche_test, Security_009_057)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_59.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_NE(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 029.在同一个policy文件中,授予多个用户KV_TABLE所有对象权限,其中一对用户存在重复,预期失败
TEST_F(SysObjBatche_test, Security_009_058)
{
    char tableName[128] = "kv";
    char tableName1[128] = "kv1";
    char tableName2[128] = "kv2";
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy_22.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_60.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);// gmrule导入系统权限返回信息缩短适配2022/9/14
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 030.在policy文件对象权限配置中,obj_name为array类型,其中一个元素不存在,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_059)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_61.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 031.在policy文件对象权限配置中,obj_name为array类型,其中一个元素不存在,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_060)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_62.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 032.在policy文件对象权限配置中,obj_name为array类型,obj_tape为VERTEX_LABEL,其中一个元素不存在,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_061)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_63.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 033.在policy文件对象权限配置中,obj_name为array类型,obj_tape为KV_TABLE,其中一个元素不存在,批量导入对象权限成功
TEST_F(SysObjBatche_test, Security_009_062)
{
    //导入系统权限
    const char *sys_policy_file = "./SysPolicy_file/allSysPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_schema2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName2, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 导入对象权限
    const char *obj_policy_file2 = "./ObjPolicy_file/allObjPolicy_64.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName2);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
