{"object_privilege_config": [{"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "SysObjBatche", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_client", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_datamodel", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_dbserver", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_deltastore", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_graph4v1", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_performance", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_query", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_runtime", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_storage", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "st_tools", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "gmimport", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}, {"obj_name": ["T39_all_type", "T39_all_type1", "T39_all_type2", "kv", "kv1", "kv2"], "obj_type": "VERTEX_LABEL|KV_TABLE", "namespace": "public", "privs": [{"user": "root", "process": "gmexport", "privs_type": ["DELETE", "REPLACE", "SELECT"]}]}]}