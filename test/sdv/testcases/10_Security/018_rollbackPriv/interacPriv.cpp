/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "rollbackPriv.h"

class interacPriv : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" ");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void interacPriv::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    char errorMsg4[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_GRANT_OPERATION);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIVILEGE_NOT_GRANTED);
    (void)snprintf(errorMsg4, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
}
void interacPriv::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001. 验证所有的系统权限类型是否都可以回滚
TEST_F(interacPriv, SEC_018_interacPriv_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexAll.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s -d", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile1 = "./sysPolciy/sysVertexAll_001.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "grant system priv to user root:interacPriv unsuccessful, objType: KV_TABLE",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 验证所系统权限回滚后是否还可以用
TEST_F(interacPriv, SEC_018_interacPriv_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexInterRac_002.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 1;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *policyFile1 = "./sysPolciy/sysVertexInterRac_002_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "grant system priv to user root:interacPriv unsuccessful, objType: VERTEX_LABEL",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 回滚系统已有用户拥有的权限
TEST_F(interacPriv, SEC_018_interacPriv_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexInterRac_003.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "root:gmadmin unsuccessful, objType: VERTEX_LABEL. ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 回滚通配符授予的权限
TEST_F(interacPriv, SEC_018_interacPriv_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexInterRac_004.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile1 = "./sysPolciy/sysVertexInterRac_004_001.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "root:interacPriv unsuccessful, objType: RESOURCE. ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. 回滚模式下回滚nsp对象权限
TEST_F(interacPriv, SEC_018_interacPriv_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexInterRac_005.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    const char *nameSpace = (const char *)"useTest";
    const char *userName1 = (const char *)"abcTest";

    ret = GmcCreateNamespace(stmt, nameSpace, userName1);
    EXPECT_EQ(GMERR_OK, ret);

    const char *objFile = "./objPolicy/ObjVertexInterRac_005.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile1 = "./objPolicy/ObjVertexInterRac_005_001.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile1);
    ret = executeCommand(cmd, "unsuccessful, objName: useTest. ret = 1018002.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile2 = "./objPolicy/ObjVertexInterRac_005_002.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006. 回滚模式下白名单进程配置异常
TEST_F(interacPriv, SEC_018_interacPriv_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *userAllow1 = "./allowList/allow_list_InterRac_006.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow1);
    ret = executeCommand(cmd, "create db user. success: 4, warning: 0, rollback: 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007. 回滚模式下白名单用户配置异常
TEST_F(interacPriv, SEC_018_interacPriv_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *userAllow1 = "./allowList/allow_list_InterRac_007.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow1);
    ret = executeCommand(cmd, "parse user tuple unsuccessful. ret = 1004006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. 回滚模式下系统权限user配置异常
TEST_F(interacPriv, SEC_018_interacPriv_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexInterRac_008.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile1 = "./sysPolciy/sysVertexInterRac_008_001.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "users[0] non-str type.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009. 回滚模式下循环导入白名单导入权限
TEST_F(interacPriv, SEC_018_interacPriv_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    for (int i = 0; i < 100; i++) {
        const char *userAllow = "./allowList/allow_list.gmuser";
        (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow);
        int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, 1024);

        const char *policyFile = "./sysPolciy/sysVertexInterRac_005.gmpolicy";
        (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile);
        ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, 1024);

        const char *userAllow1 = "./allowList/allow_list_InterRac_009.gmuser";
        (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow1);
        ret = executeCommand(cmd, "mistake: Duplicate object. user or group object, name: root:interacPriv.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, 1024);

        const char *userAllow2 = "./allowList/allow_list_InterRac_009_001.gmuser";
        (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow2);
        ret = executeCommand(cmd, "create db user. success: 4, warning: 0, rollback: 4.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, 1024);

        const char *policyFile1 = "./sysPolciy/sysVertexInterRac_009_001.gmpolicy";
        (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile1);
        ret = executeCommand(cmd, "root:closePriv unsuccessful, objType: VERTEX_LABEL. ret = 1018002");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, 1024);

        (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
        ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(cmd, 0, 1024);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010. 回滚kv的对象权限
TEST_F(interacPriv, SEC_018_interacPriv_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertexInterRac_010.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *kvTableName = "kv_01";
    ret = GmcKvCreateTable(stmt, kvTableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *kvTableName1 = "kv_02";
    ret = GmcKvCreateTable(stmt, kvTableName1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *objFile = "./objPolicy/ObjVertexInterRac_010.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy atomic -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile1 = "./objPolicy/ObjVertexInterRac_010_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy atomic -f %s", g_toolPath, objFile1);
    ret = executeCommand(cmd, "object privilege success: 0, warning: 0, rollback: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcKvDropTable(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, kvTableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}
