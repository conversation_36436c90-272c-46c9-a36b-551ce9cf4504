/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "rollbackPriv.h"

class closePriv : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void closePriv::SetUp()
{
    g_conn = NULL;
    g_stmt = NULL;
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void closePriv::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001. 回滚模式下导入白名单时指定目录
TEST_F(closePriv, SEC_018_closePriv_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *dirAllow = "./allowList/dirAllow";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, dirAllow);
    int ret = executeCommand(cmd, "import allowlist, create db user. success: 4, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *dirAllow1 = "./allowList/dirAllow/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, dirAllow1);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 回滚模式下导入权限文件时指定目录
TEST_F(closePriv, SEC_018_closePriv_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_PRIVILEGE_NOT_GRANTED);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    char cmd[1024] = {0};
    const char *dirAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, dirAllow);
    int ret = executeCommand(cmd, "import allowlist, create db user. success: 4, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *dirPolicy = "./sysPolciy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, dirPolicy);
    system(cmd);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, dirAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 回滚模式下删除白名单
TEST_F(closePriv, SEC_018_closePriv_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *allowUsers = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c remove_allowlist -f %s", g_toolPath, allowUsers);
    int ret = executeCommand(cmd, "unsuccessful, parameter atomic is not supported. ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 回滚模式下撤销权限
TEST_F(closePriv, SEC_018_closePriv_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c revoke_policy -f %s", g_toolPath, policyFile);
    int ret = executeCommand(cmd, "unsuccessful, parameter atomic is not supported. ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. 回滚模式下对gmrule的参数异常验证
TEST_F(closePriv, SEC_018_closePriv_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *dirAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist atomic -f %s", g_toolPath, dirAllow);
    int ret = executeCommand(cmd, "import allowlist, create db user. success: 4, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, dirAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006. 回滚模式下gmrule重复导入白名单
TEST_F(closePriv, SEC_018_closePriv_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *allowUsers = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist atomic -f %s", g_toolPath, allowUsers);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *allowUsers1 = "./allowList/allow_list_close_006.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist atomic -f %s", g_toolPath, allowUsers1);
    ret = executeCommand(cmd, "tuple unsuccessful. ret = 1004006", "create db user. success: 4, warning: 0, rollback: 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007. 回滚模式下gmrule重复导入系统权限
TEST_F(closePriv, SEC_018_closePriv_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_PRIVILEGE_NOT_GRANTED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char cmd[1024] = {0};
    const char *allowUsers = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy atomic -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile1 = "./sysPolciy/sysVertex_close_007.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy atomic -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "unsuccessful, objType: VERTEX_LABEL. ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}
