/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "rollbackPriv.h"
#include <iostream>
#include <cstdio>
#include <cstring>

class MandaPriv : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" ");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void MandaPriv::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    char errorMsg4[128] = {};
    char errorMsg5[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_TABLE_IN_CHECKING);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_GRANT_OPERATION);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIVILEGE_NOT_GRANTED);
    (void)snprintf(errorMsg4, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(errorMsg5, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
}
void MandaPriv::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001. 回滚模式下导入白名单时指定目录
TEST_F(MandaPriv, SEC_018_MandaPriv_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *dirAllow = "./allowList/dirAllow";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, dirAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *dirAllow1 = "./allowList/dirAllow/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, dirAllow1);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 回滚模式下导入权限文件时指定目录
TEST_F(MandaPriv, SEC_018_MandaPriv_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *dirAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, dirAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *dirPolicy = "./sysPolciy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, dirPolicy);
    system(cmd);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, dirAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 回滚模式下删除白名单
TEST_F(MandaPriv, SEC_018_MandaPriv_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *allowUsers = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c remove_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "allow_list.gmuser unsuccessful", "parameter atomic is not supported. ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 回滚模式下撤销系统权限
TEST_F(MandaPriv, SEC_018_MandaPriv_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c revoke_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "sysVertex.gmpolicy unsuccessful" , "parameter atomic is not supported. ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. 回滚模式下撤销对象权限
TEST_F(MandaPriv, SEC_018_MandaPriv_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 1;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *objFile = "./objPolicy/ObjVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "ObjVertex.gmpolicy unsuccessful" , "parameter atomic is not supported. ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006. 回滚模式下对gmrule的参数异常验证
TEST_F(MandaPriv, SEC_018_MandaPriv_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *aollwFile = "allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist atomic -f %s", g_toolPath, aollwFile);
    int ret = executeCommand(cmd, "Gmrule init args unsucc. ret = 1009006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c atomic import_allowlist -f %s", g_toolPath, aollwFile);
    ret = executeCommand(cmd, "parse gmrule option unsucc. ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

bool check_command_output(const std::string& command, const std::string& search_str) {
    char buffer[256];
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        std::cerr << "popen() failed!" << std::endl;
        return false;
    }
    // 逐行读取命令输出
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        if (strstr(buffer, search_str.c_str()) != nullptr) {
            pclose(pipe);  // 关闭 pipe
            return true;   // 找到字符串
        }
    }
    pclose(pipe);  // 关闭 pipe
    return false;  // 没有找到字符串
}

// 007. 回滚模式下gmrule重复导入白名单
TEST_F(MandaPriv, SEC_018_MandaPriv_007)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    
    std::string cmd1 = std::string(g_toolPath) + "/gmrule atomic -c import_allowlist -f ./allowList/allow_list.gmuser";
    std::string cmd2 = std::string(g_toolPath) + "/gmrule -c remove_allowlist -f ./allowList/allow_list.gmuser";
    bool ret = true;
    ret = check_command_output(cmd1, "[DONE] import allowlist, create db user. success: 4, warning: 0, rollback: 0");
    AW_MACRO_EXPECT_EQ_INT(true, ret);
    ret = check_command_output(cmd1, "unsuccessful. ret = 1009012");
    AW_MACRO_EXPECT_EQ_INT(true, ret);
    ret = check_command_output(cmd2, "[DONE] remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(true, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. 回滚模式下gmrule重复导入系统权限
TEST_F(MandaPriv, SEC_018_MandaPriv_008)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIVILEGE_NOT_GRANTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "grant system priv to user root:closePriv unsuccessful, objType: VERTEX_LABEL",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009. 回滚模式下gmrule重复导入对象权限
TEST_F(MandaPriv, SEC_018_MandaPriv_009)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_GRANT_OPERATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 1;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *objFile = "./objPolicy/ObjVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "grant object priv to \"root:MandaPriv\" unsuccessful, objName: Tx1.",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010. 第一次导入白名单成功, 第二次白名单文件配置新用户+存在用户, 第三次导入白名单配置全新用户
TEST_F(MandaPriv, SEC_018_MandaPriv_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    int ret = 0;
    const char *allowUsers = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *allowUsers1 = "./allowList/allow_list_010.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers1);
    ret = executeCommand(cmd, "mistake: Duplicate object. user or group object, name: root:gmimport.");	                           
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	ret = executeCommand(cmd, "gmuser unsuccessful. ret = 1009012", "create db user. success: 0, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *allowUsers2 = "./allowList/allow_list_010_01.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers2);
    ret = executeCommand(cmd, "create db user. success: 3, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowUsers2);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 3, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011. 回滚模式下第一次导入系统权限成功, 第二次系统权限文件配置用户存在权限+新权限, 第三次导入权限配置全新用户
TEST_F(MandaPriv, SEC_018_MandaPriv_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile1 = "./sysPolciy/sysVertex_011.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c  import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "grant system priv to user root:closePriv unsuccessful, objType: VERTEX_LABEL",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile2 = "./sysPolciy/sysVertex_011_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c  import_policy -f %s", g_toolPath, policyFile2);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012. 回滚模式下第一次导入对象权限成功, 第二次对象权限文件配置用户存在权限+新权限, 第三次导入白权限配置全新用户
TEST_F(MandaPriv, SEC_018_MandaPriv_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 1;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *objFile = "./objPolicy/ObjVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile1 = "./objPolicy/ObjVertex_012.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile1);
    ret = executeCommand(cmd, "grant object priv to \"root:MandaPriv\" unsuccessful, objName: Tx1.",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile2 = "./objPolicy/ObjVertex_012_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013. 回滚模式下第一次导入系统权限成功, 第二次系统权限文件配置多用户存在权限+新权限, 第三次导入权限配置全新用户
TEST_F(MandaPriv, SEC_018_MandaPriv_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex_013.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile1 = "./sysPolciy/sysVertex_013_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "grant system priv to user root:MandaPriv unsuccessful, objType: VERTEX_LABEL",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile2 = "./sysPolciy/sysVertex_013_02.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, policyFile2);
    ret = executeCommand(cmd, "system privilege success: 2, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014. 第一次导入对象权限成功, 第二次对象权限文件配置多用户存在权限+新权限, 第三次导入权限配置全新用户的权限或者老用户的权限
TEST_F(MandaPriv, SEC_018_MandaPriv_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 1;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *objFile = "./objPolicy/ObjVertex_014.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 3, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile1 = "./objPolicy/ObjVertex_014_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile1);
    ret = executeCommand(cmd, "grant object priv to \"root:closePriv\" unsuccessful, objName: Tx1.",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile2 = "./objPolicy/ObjVertex_014_02.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "object privilege success: 3, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 3, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 3, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015. 第一次导入对象权限成功, 第二次对象权限文件配置多表存在权限+新权限, 第三次导入权限配置全新用户的权限或者老用户的权限
TEST_F(MandaPriv, SEC_018_MandaPriv_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    for (int num = 1; num <= 2; num++) {
        snprintf(labelSchema, 1024,
            "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
            "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num, num, num);
        ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    const char *objFile = "./objPolicy/ObjVertex_015.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile1 = "./objPolicy/ObjVertex_015_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile1);
    ret = executeCommand(cmd, "grant object priv to \"root:MandaPriv\" unsuccessful, objName: Tx1.",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile2 = "./objPolicy/ObjVertex_015_02.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "object privilege success: 2, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    for (int num = 1; num <= 2; num++) {
        (void)snprintf(labelName, 20, "Tx%d", num);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016. 正常导入白名单, 回滚模式下导入重复白名单, 正常导入白名单
TEST_F(MandaPriv, SEC_018_MandaPriv_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    int ret = 0;
    const char *allowUsers = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *allowUsers1 = "./allowList/allow_list_016.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers1);
	ret = executeCommand(cmd, "mistake: Duplicate object. user or group object, name: root:gmimport.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "gmuser unsuccessful. ret = 1009012", "create db user. success: 0, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *allowUsers2 = "./allowList/allow_list_016_01.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_allowlist -f %s", g_toolPath, allowUsers2);
    ret = executeCommand(cmd, "create db user. success: 3, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowUsers2);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 3, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, allowUsers);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017. 正常导入系统权限, 回滚模式下导入重复系统权限, 正常导入系统权限
TEST_F(MandaPriv, SEC_018_MandaPriv_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile1 = "./sysPolciy/sysVertex_011.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c  import_policy -f %s", g_toolPath, policyFile1);
    ret = executeCommand(cmd, "grant system priv to user root:closePriv unsuccessful, objType: VERTEX_LABEL",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile2 = "./sysPolciy/sysVertex_011_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy atomic -f %s", g_toolPath, policyFile2);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0, rollback: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018. 正常导入对象权限, 回滚模式下导入重复对象权限, 正常导入导入对象权限
TEST_F(MandaPriv, SEC_018_MandaPriv_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    int ret = executeCommand(cmd, "create db user. success: 4, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 1;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"T%d_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_name%d\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *objFile = "./objPolicy/ObjVertex.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile1 = "./objPolicy/ObjVertex_012.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule atomic -c import_policy -f %s", g_toolPath, objFile1);
    ret = executeCommand(cmd, "grant object priv to \"root:MandaPriv\" unsuccessful, objName: Tx1.",
        "ret = 1018002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *objFile2 = "./objPolicy/ObjVertex_012_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile2);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 4, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *threadClienUser(void *args)
{
    char string[100] = "";
    int thrId = *(int *)args + 1;
    (void)sprintf(string, "./userPro%d", thrId);
    system(string);
    return NULL;
}

// 019. 多进程鉴权: 客户端进程1操作vertex/客户端进程2操作kv
TEST_F(MandaPriv, SEC_018_MandaPriv_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list_019.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "create db user. success: 5, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policySys = "./sysPolciy/sysVertex_019.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policySys);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char labelSchema[1024] = "";
    char labelName[20] = "";
    int num = 3;
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"Tx_PK\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"hash_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *objFile01 = "./objPolicy/ObjVertex_019_01.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile01);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *kvTableName = "Tx4";
    ret = GmcKvCreateTable(stmt, kvTableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *objFile02 = "./objPolicy/ObjVertex_019_02.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_policy -f %s", g_toolPath, objFile02);
    ret = executeCommand(cmd, "object privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    constexpr int thrNum = 2;
    int index[thrNum] = {0};
    pthread_t thr_arr_01[thrNum];
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, threadClienUser, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile01);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    (void)snprintf(cmd, 1024, "%s/gmrule -c revoke_policy -f %s", g_toolPath, objFile02);
    ret = executeCommand(cmd, "revoke policy. object privilege success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 5, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void SetVertexPropertyTestInsert(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_CHAR, &F2Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UCHAR, &F3Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    bool F4Value = false;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BOOL, &F4Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    float F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_FLOAT, &F5Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_DOUBLE, &F6Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    char F7Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_FIXED, F7Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F8Value = i;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_TIME, &F8Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    // bitmap
    GmcBitMapT bitMap = {0, 7, NULL};
    uint8_t bits[8 / 8];
    memset(bits, 0xff, 8 / 8);
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    // partintion
    uint8_t F10Value = 10;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_PARTITION, &F10Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 位域
    uint32_t F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_BITFIELD32, &F11Value, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    char F12Value[] = "12";
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_BYTES, F12Value, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    char F13Value[8] = "testver";
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, F13Value, (strlen(F13Value)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F14Value = i;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_UINT32, &F14Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F15Value = i;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &F15Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm4
    uint32_t vr_id_Value = 13;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id_Value, sizeof(vr_id_Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t vrf_index_Value = 1004;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index_Value, sizeof(vrf_index_Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t dest_ip_addr_Value = 0xffffff00;
    ret = GmcSetVertexProperty(
        stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr_Value, sizeof(dest_ip_addr_Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t mask_len_Value = 5;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len_Value, sizeof(mask_len_Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void SetVertexPropertyTestUpdate(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // 屏蔽 uint32_t F0Value = i;
    // 屏蔽 ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    // 屏蔽 EXPECT_EQ(GMERR_OK, ret);
    int32_t F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_CHAR, &F2Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UCHAR, &F3Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    bool F4Value = false;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BOOL, &F4Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    float F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_FLOAT, &F5Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_DOUBLE, &F6Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    char F7Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_FIXED, F7Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F8Value = i;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_TIME, &F8Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    // bitmap
    GmcBitMapT bitMap = {0, 7, NULL};
    uint8_t bits[8 / 8];
    memset(bits, 0xff, 8 / 8);
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    // partintion
    uint8_t F10Value = 10;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_PARTITION, &F10Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 位域
    uint32_t F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_BITFIELD32, &F11Value, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    char F12Value[] = "12";
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_BYTES, F12Value, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    char F13Value[8] = "testver";
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, F13Value, (strlen(F13Value)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F14Value = i;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_UINT32, &F14Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F15Value = i;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &F15Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm4
    uint32_t vr_id_Value = 13;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id_Value, sizeof(vr_id_Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t vrf_index_Value = 1004;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index_Value, sizeof(vrf_index_Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t dest_ip_addr_Value = 0xffffff00;
    ret = GmcSetVertexProperty(
        stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr_Value, sizeof(dest_ip_addr_Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t mask_len_Value = 5;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len_Value, sizeof(mask_len_Value));
    EXPECT_EQ(GMERR_OK, ret);
}

#define FULLTABLE 0xff
// 020. fuzz-demo
TEST_F(MandaPriv, SEC_018_MandaPriv_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 1;
    char cmd[1024] = {0};
    const char *userAllow = "./allowList/allow_list_demo.gmuser";
    (void)snprintf(cmd, 1024, "%s/gmrule -c import_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "create db user. success: 2, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    const char *policyFile = "./sysPolciy/sysVertex_demo.gmpolicy";
    (void)snprintf(cmd, 1024, "%s/gmrule -c  import_policy -f %s", g_toolPath, policyFile);
    ret = executeCommand(cmd, "system privilege success: 1, warning: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *labelName = "vertex_01";
    char *vertexSchema = NULL;
    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &vertexSchema);
    ASSERT_NE((void *)NULL, vertexSchema);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, vertexSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertexSchema);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t insert_value = 10;
    for (int i = 0; i < count; i++) {
        SetVertexPropertyTestInsert(stmt, insert_value);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE); // hash删除
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t update_value = 20;
    for (int i = 0; i < count; i++) {
        uint32_t sk = insert_value;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &sk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hash_key");
        EXPECT_EQ(GMERR_OK, ret);
        // 屏蔽 SetVertexPropertyTestUpdate(stmt, update_value); // hash更新
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 屏蔽ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    // 屏蔽EXPECT_EQ(GMERR_OK, ret);
    // 屏蔽ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION); // 版本更新
    // 屏蔽EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, 10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t updateVersion_value = 20;
    for (int i = 0; i < count; i++) {
        uint32_t pk = insert_value;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexPropertyTestUpdate(stmt, updateVersion_value);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN); // 过滤查询
    EXPECT_EQ(GMERR_OK, ret);
    const char *condStr = "F1<100";
    ret = GmcSetFilter(stmt, condStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, 10, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(cmd, 1024, "%s/gmrule -c remove_allowlist -f %s", g_toolPath, userAllow);
    ret = executeCommand(cmd, "remove allowlist, remove db user. success: 2, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, 1024);

    AW_FUN_Log(LOG_STEP, "test end.");
}
