set(compile_list "")
if(FEATURE_FASTPATH)
list(APPEND compile_list 001_SupportAuditFunction)
list(APPEND compile_list 002_AuditLogEnhancemet)
list(APPEND compile_list 003_SystemPrivilege)
list(APPEND compile_list 004_ObjPrivs)
list(APPEND compile_list 005_PrivilegeView)
list(APPEND compile_list 006_UserManagement)
list(APPEND compile_list 007_UserLogin)
list(APPEND compile_list 008_PrivilegeWildcard)
list(APPEND compile_list 009_SysObjBatche)
list(APPEND compile_list 010_GmruleCompatibleV3)
list(APPEND compile_list 011_CompatibilityRequirement)
list(APPEND compile_list 012_GmruleRemoveAllowlist)
list(APPEND compile_list 013_RevokePrivs)
list(APPEND compile_list 014_PrivilegeUpdate)
list(APPEND compile_list 015_PrivsCompatibleV3)
if(FEATURE_DATALOG)
    list(APPEND compile_list 016_datalogPriv)
    list(APPEND compile_list 020_DatalogLoadUnloadPriv)
endif()
if(FEATURE_YANG_VALIDATION)
    list(APPEND compile_list 017_YangAuthority)
endif()
list(APPEND compile_list 018_rollbackPriv)
list(APPEND compile_list 019_TspPolicy)
list(APPEND compile_list 021_fileMergeTest)
if(FEATURE_CLT_SERVER_SAME_PROCESS)
    list(APPEND compile_list 022_cliServerProcessPriv)
endif()
list(APPEND compile_list 023_RevokePrivsWild)
list(APPEND compile_list 024_GrantGroup)
list(APPEND compile_list 025_SensitiveEnhance)
list(APPEND compile_list 026_ToolAuthentication)
endif()

verify_and_add_directory(${compile_list})
