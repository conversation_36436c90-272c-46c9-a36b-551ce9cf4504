/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 * Create Date : 2023
 */

#ifndef PRIVILEGE_VIEW_H
#define PRIVILEGE_VIEW_H

/* ****************************************************************************
 Description  : 视图查询过滤
 Notes        :
 Author       : z00619264
 Created      : 2023.11.09
 Modification :
**************************************************************************** */

extern "C" {}

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <string>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define FULLTABLE 0xff
#define MAX_CMD_SIZE 1024
int g_ret = 0;
int g_count = 0;
char g_command[1024];
char const * g_labelName = "T39_all_type";
char const * g_labelName1 = "T39_all_type1";
char const * g_labelName2 = "T39_all_type2";
char g_tableName[128] = "kv";
char g_tableName1[128] = "kv1";
char g_tableName2[128] = "kv2";
char g_namespace1[128] = "nsp";
char g_namespace2[128] = "nsp1";
char g_namespace3[128] = "nsp2";
const char *g_configJson = R"({"max_record_num":1000,"max_record_num_check":true})";
char const * g_viewName = "V\\$PRIVILEGE_ROLE_STAT";
char *g_testSchema = NULL;
char *g_testSchema1 = NULL;
char *g_testSchema2 = NULL;


// 视图过滤查找
int PrivView(char *command, const char *metchPattern)
{
    int count = 0;
    char buffer[1024] = {0};
    int maxsize = 512;
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        return -1;
    }
    char matchbuffer[maxsize];
    while (fgets(matchbuffer, sizeof(matchbuffer), pf) != NULL) {
        // 查找匹配的字符串
        if (strstr(matchbuffer, metchPattern)) {
            count++;
            memset(matchbuffer, 0, maxsize);
            continue;
        }
        memset(matchbuffer, 0, maxsize);
    }
    pclose(pf);
    return count;
}

#endif

