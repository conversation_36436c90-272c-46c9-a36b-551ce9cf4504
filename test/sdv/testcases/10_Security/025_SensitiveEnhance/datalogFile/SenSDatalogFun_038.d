%table inp1(filedy1:int4, filedy2:int4, filedy3:int4, filedy4:int4, filedy5:int4, filedy6:int4, filedy7:int4, filedy8:int4, filedy9:int4, filedy10:int4, filedy11:int4, filedy12:int4, filedy13:int4, filedy14:int4, filedy15:int4, filedy16:int4, filedy17:int4, filedy18:int4, filedy19:int4, filedy20:int4, filedy21:int4, filedy22:int4, filedy23:int4, filedy24:int4, filedy25:int4, filedy26:int4, filedy27:int4, filedy28:int4, filedy29:int4, filedy30:int4, filedy31:int4, filedy32:int4, filedy33:int4, filedy34:int4, filedy35:int4, filedy36:int4, filedy37:int4, filedy38:int4, filedy39:int4, filedy40:int4, filedy41:int4, filedy42:int4, filedy43:int4, filedy44:int4, filedy45:int4, filedy46:int4, filedy47:int4, filedy48:int4, filedy49:int4, filedy50:int4, filedy51:int4, filedy52:int4, filedy53:int4, filedy54:int4, filedy55:int4, filedy56:int4, filedy57:int4, filedy58:int4, filedy59:int4, filedy60:int4, filedy61:int4, filedy62:int4, filedy63:int4) {
    index(0(filedy1)),
    sensitive_fields(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63)
}

%table mid1(filedy1:int4, filedy2:int4, filedy3:int4, filedy4:int4, filedy5:int4, filedy6:int4, filedy7:int4, filedy8:int4, filedy9:int4, filedy10:int4, filedy11:int4, filedy12:int4, filedy13:int4, filedy14:int4, filedy15:int4, filedy16:int4, filedy17:int4, filedy18:int4, filedy19:int4, filedy20:int4, filedy21:int4, filedy22:int4, filedy23:int4, filedy24:int4, filedy25:int4, filedy26:int4, filedy27:int4, filedy28:int4, filedy29:int4, filedy30:int4, filedy31:int4, filedy32:int4, filedy33:int4, filedy34:int4, filedy35:int4, filedy36:int4, filedy37:int4, filedy38:int4, filedy39:int4, filedy40:int4, filedy41:int4, filedy42:int4, filedy43:int4, filedy44:int4, filedy45:int4, filedy46:int4, filedy47:int4, filedy48:int4, filedy49:int4, filedy50:int4, filedy51:int4, filedy52:int4, filedy53:int4, filedy54:int4, filedy55:int4, filedy56:int4, filedy57:int4, filedy58:int4, filedy59:int4, filedy60:int4, filedy61:int4, filedy62:int4, filedy63:int4) {
    index(0(filedy1)),
    sensitive_fields(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63)
}

%table out1(filedy1:int4, filedy2:int4, filedy3:int4, filedy4:int4, filedy5:int4, filedy6:int4, filedy7:int4, filedy8:int4, filedy9:int4, filedy10:int4, filedy11:int4, filedy12:int4, filedy13:int4, filedy14:int4, filedy15:int4, filedy16:int4, filedy17:int4, filedy18:int4, filedy19:int4, filedy20:int4, filedy21:int4, filedy22:int4, filedy23:int4, filedy24:int4, filedy25:int4, filedy26:int4, filedy27:int4, filedy28:int4, filedy29:int4, filedy30:int4, filedy31:int4, filedy32:int4, filedy33:int4, filedy34:int4, filedy35:int4, filedy36:int4, filedy37:int4, filedy38:int4, filedy39:int4, filedy40:int4, filedy41:int4, filedy42:int4, filedy43:int4, filedy44:int4, filedy45:int4, filedy46:int4, filedy47:int4, filedy48:int4, filedy49:int4, filedy50:int4, filedy51:int4, filedy52:int4, filedy53:int4, filedy54:int4, filedy55:int4, filedy56:int4, filedy57:int4, filedy58:int4, filedy59:int4, filedy60:int4, filedy61:int4, filedy62:int4, filedy63:int4) {
    index(0(filedy1)),
    sensitive_fields(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63)
}

mid1(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63) :- inp1(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63).
out1(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63) :- mid1(filedy1, filedy2, filedy3, filedy4, filedy5, filedy6, filedy7, filedy8, filedy9, filedy10, filedy11, filedy12, filedy13, filedy14, filedy15, filedy16, filedy17, filedy18, filedy19, filedy20, filedy21, filedy22, filedy23, filedy24, filedy25, filedy26, filedy27, filedy28, filedy29, filedy30, filedy31, filedy32, filedy33, filedy34, filedy35, filedy36, filedy37, filedy38, filedy39, filedy40, filedy41, filedy42, filedy43, filedy44, filedy45, filedy46, filedy47, filedy48, filedy49, filedy50, filedy51, filedy52, filedy53, filedy54, filedy55, filedy56, filedy57, filedy58, filedy59, filedy60, filedy61, filedy62, filedy63).
