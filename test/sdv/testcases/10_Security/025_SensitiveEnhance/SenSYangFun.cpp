/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SenSYang.cpp
 * Description: sensitive能力增强(<PERSON>测试)
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-08-10
 */

#include "SenSYangFun.h"

class SenSYangFun : public testing::Test {
public:
    static void SetUpTestCase()
    {
#ifdef FEATURE_PERSISTENCE
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
#endif
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void SenSYangFun::SetUp()
{
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    g_mSTrxConfig.readOnly = false;

    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_leaflist);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void SenSYangFun::TearDown()
{
    AW_CHECK_LOG_END();
    AsyncUserDataT userData = {0};
    int ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_list);
    GmcFreeStmt(g_stmt_leaflist);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.同一个字段定义两次sensitive，创建Yang表失败
TEST_F(SenSYangFun, SEC_025_002_001)
{
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);

    AW_FUN_Log(LOG_STEP, "同一个字段定义两次sensitive，创建Yang表失败");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_001.gmjson";

    readJanssonFile(vertexPath, &schemaFile);
    AW_MACRO_EXPECT_NOTNULL(schemaFile);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schemaFile, g_config, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, data.status);
    free(schemaFile);
}

// 002.sensitive定义在root节点上，插入数据，查看数据
TEST_F(SenSYangFun, SEC_025_002_002)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_002.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("abc"), strlen("abc"), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"F1\": \"abc\"", "\"F2\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
}

// 003.container下的设置sensitive字段，插入数据，gmsysview subtree查询
TEST_F(SenSYangFun, SEC_025_002_003)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_003.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int8_t f1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT8, &f1Value, sizeof(int8_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f2Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_UINT16, &f2Value, sizeof(uint16_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f3Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f4Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_UINT64, &f4Value, sizeof(uint64_t), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("string"), strlen("string"), "F5",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = TestGmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t a1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_INT8, &a1Value, sizeof(int8_t), "A1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t a2Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_UINT16, &a2Value, sizeof(uint16_t), "A2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t a3Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_INT32, &a3Value, sizeof(int32_t), "A3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t a4Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_UINT64, &a4Value, sizeof(uint64_t), "A4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_childNode[0], GMC_DATATYPE_STRING, (char *)("string"), strlen("string"), "A5",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"F1\": \"******\"", "\"F2\": \"******\"", "\"F3\": \"******\"",
        "\"F4\": \"******\"", "\"F5\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = executeCommand(g_command, "\"A1\": \"******\"", "\"A2\": \"******\"", "\"A3\": \"******\"",
        "\"A4\": \"******\"", "\"A5\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
}

// 004.case下的设置sensitive字段，插入数据，gmsysview subtree查询
TEST_F(SenSYangFun, SEC_025_002_004)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_004.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // Case_2
    ret = TestGmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangEditChildNode(g_childNode[0], "Case_2", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t b1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_INT8, &b1Value, sizeof(int8_t), "B1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t b2Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_UINT16, &b2Value, sizeof(uint16_t), "B2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t b3Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_INT32, &b3Value, sizeof(int32_t), "B3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t b4Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_UINT64, &b4Value, sizeof(uint64_t), "B4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_childNode[1], GMC_DATATYPE_STRING, (char *)("string"), strlen("string"), "B5",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"B1\": \"******\"", "\"B2\": \"******\"", "\"B3\": \"******\"",
        "\"B4\": \"******\"", "\"B5\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
}

// 005.list下的设置sensitive字段，插入数据，gmsysview subtree查询
TEST_F(SenSYangFun, SEC_025_002_005)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_005.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // Con_root
    int8_t f1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT8, &f1Value, sizeof(int8_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = TestGmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t a1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_INT8, &a1Value, sizeof(int8_t), "A1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelNameList, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t lpValue = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_UINT32, &lpValue, sizeof(uint32_t), "LP", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t l1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_INT8, &l1Value, sizeof(int8_t), "L1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t l2Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_UINT16, &l2Value, sizeof(uint16_t), "L2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t l3Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_INT32, &l3Value, sizeof(int32_t), "L3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t l4Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[1], GMC_DATATYPE_UINT64, &l4Value, sizeof(uint64_t), "L4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_childNode[1], GMC_DATATYPE_STRING, (char *)("string"), strlen("string"), "L5",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"LP\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, "\"L1\": \"******\"", "\"L2\": \"******\"", "\"L3\": \"******\"",
        "\"L4\": \"******\"", "\"L5\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
}

// 006.leaf_list下的设置sensitive字段，插入数据，gmsysview subtree查询
TEST_F(SenSYangFun, SEC_025_002_006)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_006.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel2.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // Con_root
    int8_t f1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT8, &f1Value, sizeof(int8_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = TestGmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t a1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_INT8, &a1Value, sizeof(int8_t), "A1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaflist, g_labelNameLeafist, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_leaflist);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaflist, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t lepValue = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[2], GMC_DATATYPE_INT8, &lepValue, sizeof(int8_t), "LeP", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_leaflist);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"LeP\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
}

// 007.sensitive设为false，插入数据，gmsysview subtree查询
TEST_F(SenSYangFun, SEC_025_002_007)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_007.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int8_t f1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT8, &f1Value, sizeof(int8_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f2Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_UINT16, &f2Value, sizeof(uint16_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f3Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f4Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_UINT64, &f4Value, sizeof(uint64_t), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("string"), strlen("string"), "F5",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = TestGmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t a1Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_INT8, &a1Value, sizeof(int8_t), "A1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t a2Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_UINT16, &a2Value, sizeof(uint16_t), "A2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t a3Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_INT32, &a3Value, sizeof(int32_t), "A3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t a4Value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_childNode[0], GMC_DATATYPE_UINT64, &a4Value, sizeof(uint64_t), "A4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_childNode[0], GMC_DATATYPE_STRING, (char *)("string"), strlen("string"), "A5",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"F1\": 1", "\"F2\": 1", "\"F3\": 1", "\"F4\": \"1\"", "\"F5\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"A1\": 1", "\"A2\": 1", "\"A3\": 1", "\"A4\": \"1\"", "\"A5\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.覆盖所有字段类型设置sensitive字段，插入数据，gmsysview subtree查询
TEST_F(SenSYangFun, SEC_025_002_008)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_008.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t value = 1;
    TestYangSetNodePropertyAllType(g_rootNode, value, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview subtree -ns SenSYangFun -rn Con_root -defaultMode REPORT_ALL");
    ret = executeCommand(g_command, "\"F1\": \"******\"", "\"F2\": \"******\"", "\"F3\": \"******\"",
        "\"F4\": \"******\"", "\"F5\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F6\": \"******\"", "\"F7\": \"******\"", "\"F8\": \"******\"",
        "\"F9\": \"******\"", "\"F10\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F11\": \"******\"", "\"F12\": \"******\"", "\"F13\": \"******\"",
        "\"F14\": \"******\"", "\"F15\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F16\": \"******\"", "\"F17\": \"******\"", "\"F18\": \"******\"",
        "\"ID1\": \"******\"", "\"ID2\": \"******\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.设置sensitive字段，插入数据，subtree查询，查询日志（debug编译下会有，release编译没有）
TEST_F(SenSYangFun, SEC_025_002_009)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char *schemaFile = NULL;
    char vertexPath[] = "./schemaFileYang/Yang_025_009.gmjson";
    char edgePath[] = "./schemaFileYang/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int8_t value = 1;
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT8, &value, sizeof(int8_t), "yangsensitive1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_INT8, &value, sizeof(int8_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "subtree查询");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SubtreeFilterCbParam subData = {0};
    subData.expectReplyJson = "~";
    subData.expectStatus = GMERR_OK;
    subData.step = 0;
    GmcSubtreeFilterItemT filterState = {.rootName = "Con_root",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filterState,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &subData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&subData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, subData.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "校验数据，校验日志");
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectJson, result));
    free(result);

#if defined(ENV_RTOSV2X)
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r 'yangsensitive1' %s", g_serverlogPath);
    ret = executeCommand(g_command, "yangsensitive1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE
    if (ret == GMERR_OK) {
        system(g_command);
    }
}
