[{"type": "container", "name": "Container_root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": true}, {"name": "con_node", "type": "container", "fields": [{"name": "F0", "type": "int32", "nullable": true}]}], "keys": [{"node": "Container_root", "name": "root.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_child", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": true}], "keys": [{"node": "list_child", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]