/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

#ifndef PRIVSCOMPATIBILEV3_H
#define PRIVSCOMPATIBILEV3_H

extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

#define MAX_NAME_LENGTH 128

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret;
    char F0Value = (char)i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = (unsigned char)i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = (int8_t)i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = (int16_t)i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i, const char *labelname, const char *pk_name)
{
    int ret = 0;

    // scan
    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t pk_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // query
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        // Get F0
        char F0Value = i;
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F1
        unsigned char F1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F2
        int8_t F2Value = i;
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F3
        uint8_t F3Value = i;
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F4
        int16_t F4Value = i;
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F5
        uint16_t F5Value = i;
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F6
        int32_t F6Value = i;
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F8
        bool F8Value = false;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void test_checkVertexProperty_sub(GmcStmtT *stmt, int index)
{
    int ret;

    // Gef0Value
    char F0Value = (char)index;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = (unsigned char)index;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = (int8_t)index;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = index;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = (int16_t)index;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = index;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = index;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback_vertex(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                        test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT:
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE_INSERT new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_MERGE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_MERGE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                        test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE:
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE_UPDATE new_value is %d\r\n", index);
                    test_checkVertexProperty_sub(subStmt, index);

                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
        }
    }
}

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    int ret;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                if (0 == user_data->insertNum % 10) {
                    printf("[info]--user_data->insertNum-->%d\n", user_data->insertNum);
                }
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                if (0 == user_data->deleteNum % 20) {
                    printf("[info]--user_data->deleteNum-->%d\n", user_data->deleteNum);
                }
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                if (0 == user_data->updateNum % 20) {
                    printf("[info]--user_data->updateNum-->%d\n", user_data->updateNum);
                }
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                if (ret == GMERR_NO_DATA) {
                    break;
                } else {
                    uint32_t F7Value;
                    bool isNull;
                    ret = GmcGetVertexPropertyByName(subStmt, "F7", &F7Value, sizeof(uint32_t), &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (!isNull) {
                        printf("F7Value  :  %d\n", F7Value);
                    }
                    if (0 == user_data->replaceNum % 20) {
                        printf("[info]--user_data->replaceNum-->%d\n", user_data->replaceNum);
                    }
                }
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                if (0 == user_data->agedNum % 10) {
                    printf("[info]--user_data->agedNum-->%d\n", user_data->agedNum);
                }
                break;
            }
        }
    }
}

void kv_sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, i, index;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char *outKey = NULL, *outValue = NULL;
    uint32_t outKeyLen = 512, outValueLen = 0;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    void *tableLabel = 0;
    int g_data_num = 1000;
    char key[128] = "zhangsan";

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->msgType) {
                // 推送new object
                case 2: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            // 读new
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                                sprintf(key, "zhangsan_%d", index);
                            } else {
                                sprintf(key, "zhangsan_%d", index - g_data_num);
                            }
                            printf("[NEW OBJECT] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                // 推送new object + old object
                case 3: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_KV_SET: {
                            // 读new
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            sprintf(key, "zhangsan_%d", index);
                            printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_KV_SET new_value is %d\r\n", index);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcKvGetFromStmt(
                                subStmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
                            EXPECT_EQ(GMERR_OK, ret);
                            for (uint32_t i = 0; i < outKeyLen; i++) {
                                EXPECT_EQ(key[i], outKey[i]);
                            }
                            EXPECT_EQ((uint32_t)index, *((uint32_t *)outValue));

                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("default: invalid msgType %d\r\n", info->msgType);
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            default: {
                printf("default: invalid eventType\r\n");
                break;
            }
        }
    }
}


// 对账过程中刷新数据
int RefreshDataFull(GmcStmtT *stmt, const char *labelName, const char *pkName, GmcConnT *conn)
{
    int ret = 0;
    int count = 100;
    // 对账过程中,使用replace接口刷新前10条数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 20; i++) {
        int64_t F9Value = i;
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(stmt, i);
        set_VertexProperty(stmt, i + count);
        ret += GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账过程中,使用更新接口刷新第10-20数据的版本号
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 10; i < 20; i++) {
        // refresh version
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret += GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int RefreshDataPartiton(GmcStmtT *stmt, const char *labelName, const char *pkName, GmcConnT *conn)
{
    int ret = 0;
    int count = 15;
    // 对账过程中,使用replace接口刷新前5条数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        set_VertexProperty_PK(stmt, i);
        uint8_t partition_id = 0;
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_PARTITION, &partition_id, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(stmt, i + count);
        ret += GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账过程中,使用更新接口刷新第5-10数据的版本号
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 5; i < 10; i++) {
        // refresh version
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret += GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

// 对账
void AccountCheck(const char *labelName, const char *pkName, uint8_t partitionId, int expect_ret = GMERR_OK)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    do {
        // 开启对账
        ret = GmcBeginCheck(stmt, labelName, partitionId);
        EXPECT_EQ(expect_ret, ret);
        if (ret != GMERR_OK) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }

        // 刷新数据
        if (partitionId == GMC_FULL_TABLE) {
            ret = RefreshDataFull(stmt, labelName, pkName, conn);
        } else {
            ret = RefreshDataPartiton(stmt, labelName, pkName, conn);
        }
        bool isAbornormal = (ret != GMERR_OK);

        // 查询对账的状态
        GmcCheckInfoT *checkInfo;
        GmcCheckStatusE checkStatus;
        ret = GmcGetCheckInfo(stmt, labelName, partitionId, &checkInfo);
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcGetCheckStatus(checkInfo, &checkStatus);
        if (ret != GMERR_OK) {
            break;
        }
        if (checkStatus != GMC_CHECK_STATUS_CHECKING) {
            break;
        }
        // 结束对账
        (void)GmcEndCheck(stmt, labelName, partitionId, isAbornormal);
    } while (0);
}

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile, int expectRet)
{
    int ret = 0;
    json_t *data_json;
    json_error_t data_json_error;
    int expectAffectRows = 1;
    if (expectRet != GMERR_OK) {
        expectAffectRows = 0;
    }
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(expectRet, ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(expectRet, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
    }
    json_decref(data_json);
}

#endif
