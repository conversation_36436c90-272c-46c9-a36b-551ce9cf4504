/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: MergeTest.cpp
 * Description: 导表导权限支持文件合并
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2023-07-13test
 */
#include <stdio.h>
#include <time.h>
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "gtest/gtest.h"
#include "t_rd_sn.h"
#include "t_datacom_lite.h"
using namespace std;

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 128

char g_command[1024];

#if defined ENV_RTOSV2X
    char envCheck[] = {'/','\n'};
    char log_path[200] = "opt/vrpv8/home/<USER>";
    char log_file[20] = "diag.current.log";
#else
    char envCheck[5] = "./";
    char log_path[200] = "log/run/rgmimport";
    char log_file[20] = "rgmimport.log";
#endif
char errNum[20]; 
int count_num = 0;
int g_start_num = 0, g_end_num = 100, affectRows = 0, countss = 0;



uint32_t ret = 0;
GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;
AsyncUserDataT data = {0};
SnUserDataT *user_data;

class fileMergeTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {    
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        g_isOneThreadEpoll = true;
        ret = createEpollOneThread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    }
    static void TearDownTestCase()
    {
        
        closeEpollOneThread();
    }
};

void fileMergeTest::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    g_conn_async, g_stmt_async = NULL;
    g_conn_sync, g_stmt_sync = NULL;

    ret = testGmcConnect(
    &g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(
    &g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync,"public");
    EXPECT_EQ(GMERR_OK,ret);
}
void fileMergeTest::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
void test_setVertexProperty(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
}
int func_create_sub_relation(GmcStmtT *stmt, GmcConnT *sub_conn, char *file_path, GmcSubCallbackT callback,
    SnUserDataT *user_data, const char *sub_name)
{
    int ret = 0;
    // 创建3种订阅关系类型，property_value=1111 or property_value=2222
    char *sub_info = NULL;
    readJanssonFile(file_path, &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = sub_name;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, sub_conn, callback, user_data);
    free(sub_info);
    return ret;
}
void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    count_num++;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
int test_insert_vertex_partition_test(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    int ret = 0;
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "MergeTest1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int loop = oper_begin; loop < oper_end; loop++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t partition_value = (uint8_t)loop % 256 % 16;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_PARTITION, &partition_value, sizeof(partition_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}
int test_read_partition_test_by_pk(
    GmcStmtT *stmt, const char *keyName, int read_begin, int read_end, bool expect_exist = false)
{
    int ret = 0;
    void *vertexLabel = NULL;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, "MergeTest1", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (isFinish) {
                break;
            }
            uint8_t partition_value = (uint8_t)loop % 256 % 16;
            ret = GmcGetVertexPropertyByName(stmt, "F1", &partition_value, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((uint8_t)loop % 256 % 16, partition_value);
            EXPECT_EQ(0, isNull);
        }
        GmcFreeIndexKey(stmt);
    }
    return ret;
}
/* ***********************************************************************************
 Description  : 001.通过GmcCreateVertexLabelAsync接口异步建表，并且config字段生效
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    char *schema = NULL;

    AsyncUserDataT data = {0};
    
    readJanssonFile("./schema_files/001/schema_normal.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // 删除可能表
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName1, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_UNDEFINED_TABLE) {
        data.status = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName2, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_UNDEFINED_TABLE) {
        data.status = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema);

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 删表
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName1, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName2, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 002.通过GmcCreateVertexLabel接口同步建表，并且config字段生效
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    char *schema = NULL;
    char *config = NULL;

   
    readJanssonFile("./schema_files/002/schema_noneconfig.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    readJanssonFile("./schema_files/002/schema_noneconfig.gmconfig", &config);
    EXPECT_NE((void *)NULL, schema);

    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabel(g_stmt_sync,schema,config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    free(config);

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 2000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 2000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 003.两个文件合并成一个文件，并填写max_record_count_check字段
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 导入表
    system("gmimport -c vschema -f ./schema_files/003/schema_normal.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT_CHECK: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT_CHECK: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 004.1024文件合并，并填写isFastReadUncommitted字段
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *nsn = "mergetestns";
    const char *nsuser = "user";
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 删除可能的命名空间
    ret = GmcDropNamespace(g_stmt_sync,nsn);
    if(ret == GMERR_UNDEFINED_OBJECT){
        ret = GMERR_OK;
    }
    // 创建并使用命名空间

    AsyncUserDataT data = {0};
    GmcTxConfigT g_config;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.readOnly = false;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.trxType = GMC_PESSIMISITIC_TRX;

    ret = GmcCreateNamespace(g_stmt_sync,nsn,nsuser);
    EXPECT_EQ(GMERR_OK,ret);
    ret = GmcUseNamespace(g_stmt_sync,nsn);
    EXPECT_EQ(GMERR_OK,ret);

    // 导入表
    system("gmimport -c vschema -ns mergetestns -f ./schema_files/004/schema_1024.gmjson");
    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 表操作
    ret = GmcTransStart(g_conn_sync, &g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt_sync, labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    test_setVertexProperty(g_stmt_sync, 1);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 清除命名空间的表
    ret = GmcClearNamespaceAsync(g_stmt_async,nsn,drop_namespace_callback,&data);
    EXPECT_EQ(GMERR_OK,ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 删除命名空间
    ret = GmcDropNamespace(g_stmt_sync,nsn);
    EXPECT_EQ(GMERR_OK,ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 005.1024文件合并，并填写isFastReadUncommitted字段
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *nsn = "mergetestns";
    const char *nsuser = "user";
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 删除可能的命名空间
    ret = GmcDropNamespace(g_stmt_sync,nsn);
    if(ret == GMERR_UNDEFINED_OBJECT){
        ret = GMERR_OK;
    }
    // 创建并使用命名空间
    AsyncUserDataT data = {0};
    ret = GmcCreateNamespace(g_stmt_sync,nsn,nsuser);
    EXPECT_EQ(GMERR_OK,ret);
    ret = GmcUseNamespace(g_stmt_sync,nsn);
    EXPECT_EQ(GMERR_OK,ret);

    // 导入表
    system("gmimport -c vschema -ns mergetestns -f ./schema_files/005/schema_1025.gmjson");

    sleep(2);
    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -ns mergetestns");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1025");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 清除命名空间的表
    ret = GmcClearNamespaceAsync(g_stmt_async,nsn,drop_namespace_callback,&data);
    EXPECT_EQ(GMERR_OK,ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 删除命名空间
    ret = GmcDropNamespace(g_stmt_sync,nsn);
    EXPECT_EQ(GMERR_OK,ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 006.gmjson中未填写config字段，同文件夹也未包含gmconfig文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/006/schema_noneconfig1.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 007.gmjson中未填写config字段，同文件夹包含gmconfig文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 导入表
    system("gmimport -c vschema -f ./schema_files/007/schema_noneconfig2.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 3000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 3000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 008.gmjson中填写config字段，同文件夹包含gmconfig文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d",GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *g_subConnName = "subConnName";
    const char *g_subName = "subVertexLabel";
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 导入表
    system("gmimport -c vschema -f ./schema_files/008/schema_normal_DSBP.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 表操作
    int chanRingLen = 256;
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    connOptions.connName = g_subConnName;
    ret = TestYangGmcConnect(&g_conn_sub, NULL, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);

    ret = func_create_sub_relation(g_stmt_sync, g_conn_sub, (char *)"./schema_files/008/TreeModel_subinfo_TestT0.gmjson",
        sn_callback_not_cmp, user_data, g_subName);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 009.gmjson中部分表填写defragmentation字段，同文件夹也包含gmconfig文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#if defined ENV_EULER    
    system("stop.sh -f");
    GmcUnInit();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");
    system("start.sh");
    GmcInit();
    g_conn_async, g_stmt_async = NULL;
    g_conn_sync, g_stmt_sync = NULL;

    ret = testGmcConnect(
    &g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(
    &g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    char teststr[1024] = "testver0";
    countss = 0;
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/009/schema_normal_defr.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

#if defined ENV_EULER
    // 表操作
    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    while (countss < g_end_num * g_end_num) {
        snprintf(teststr, 16, "testver%d", countss);
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &countss, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_STRING, &teststr, (strlen(teststr)));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        countss++;
    }

    // delete（必须删除数据才可以触发缩容）
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    countss = 0;
    while (countss < g_end_num * g_end_num) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &countss, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "MergeTest1_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        countss++;
    }

    // check V$MEM_COMPACT_TASKS_STAT
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s",
        g_toolPath, g_connServer, "V\\$MEM_COMPACT_TASKS_STAT");
    ret = executeCommand(g_command, "TASK_TYPE", "TASK_STATUS", "SUB_TASK_STATUS");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "CREATE_COUNT", "DESTROY_COUNT", "SCHEDULE_COUNT");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DURATION", "EXEC_TIME_AVG", "EXEC_TIME_MIN", "EXEC_TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
#endif

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt_sync, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName2);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 010.gmjson中不同的表填写不同的字段（data_sync_label和
                push_age_record_batch），同文件夹未包含gmconfig文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *g_subConnName = "subConnName";
    const char *g_subName = "subVertexLabel";
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/010/schema_normal_DSLPARB.gmjson");
    
    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "IS_DATA_SYNC_LABEL: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 011.gmjson中包含check_validity字段和enable_clusterhash字段
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
#if defined ENV_EULER
    system("stop.sh -f");
    GmcUnInit();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
    system("start.sh");
    GmcInit();
    g_conn_async, g_stmt_async = NULL;
    g_conn_sync, g_stmt_sync = NULL;

    ret = testGmcConnect(
    &g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(
    &g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/011/schema_normal_CVEC.gmjson");

#if defined ENV_EULER
    // 表操作
    ret = test_insert_vertex_partition_test(g_stmt_sync, 0, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_read_partition_test_by_pk(g_stmt_sync, "MergeTest1_PK", 0, 16);
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));

#if defined ENV_EULER
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    ret = executeCommand(g_command, "LABEL_NAME: MergeTest1"); 
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "LABEL_NAME: MergeTest2"); 
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));
#endif

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 012.gmjson文件内config字段内有不支持的配置项(max_recard_count)
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    const char *labelName3 = "MergeTest3";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/012/schema_error_RECARD.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 4000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 4000");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest3");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 4000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 013.gmjson文件内config字段配置项为不支持的值(max_record_count配置为yes)
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    const char *labelName3 = "MergeTest3";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/013/schema_error_MRC_YES.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest3");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    
    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 014.gmjson中有格式配置错误的表（JSON格式配置错误）
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    const char *labelName3 = "MergeTest3";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/014/schema_error_JSON.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest3");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 015.gmjson中有表超出最大长度(字符串长度超过1M)
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FILE_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    const char *labelName3 = "MergeTest3";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/015/schema_error_long.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_FILE_OPERATE_FAILED);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 016.gmjson中有重复的表(兼容V3，预期成功)
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    const char *labelName3 = "MergeTest3";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/016/schema_error_jsonrepeat.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 8000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_DUPLICATE_TABLE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 017.gmjson文件中config字段有重复项（max_record_count）
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    const char *labelName3 = "MergeTest3";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName3);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/017/schema_error_configrepeat.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest3");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 018.gmjson文件中有vertex表也有kv表
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/018/schema_normal_KV.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 019.gmjson文件中config字段定义max_record_count字段，
                gmconfig文件也定义同字段，数值冲突且gmjson表内不包含此字段
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/019/schema_normal_MRC.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 8000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 8000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 020.gmjson文件中config字段定义max_record_count字段，
                gmjson文件中表内也定义此字段，数值冲突且不包含gmconfig文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/020/schema_normal_MRC.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 7000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 8000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 021.gmjson中有表配置也有白名单配置
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/021/schema_error_gmuser.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PRIVILEGE_USER_STAT -f USER_NAME=root1");
    int ret = executeCommand(g_command, "PROCESS_NAME: MergeTest");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_UNDEFINE_COLUMN);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 022.gmjson中有表配置也有权限配置
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入白名单
    system("gmrule -c import_allowlist -f ./schema_files/022/allow_list.gmuser");

    // 导入表
    system("gmimport -c vschema -f ./schema_files/022/schema_error_gmpolicy.gmjson");
    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PRIVILEGE_USER_STAT -f USER_NAME=root1");
    int ret = executeCommand(g_command, "PROCESS_NAME: LoaderTest");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_UNDEFINE_COLUMN);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 清除白名单
    system("gmrule -c remove_allowlist -f ./schema_files/022/allow_list.gmuser");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 023.gmjson文件中有表定义auto_increment，
                同时对应config字段内也定义了自增配置，
                同文件夹内不含有gmconfig文件进行相关配置
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/023/schema_normal_autoincrement.gmjson");
    
    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest2");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 024.gmjson文件中有表定义auto_increment，
                同时对应config字段内未定义自增配置，
                同文件夹内不含有gmconfig文件进行相关配置
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    // 删除可能表
    ret = GmcDropVertexLabel(g_stmt_sync,labelName1);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync,labelName2);
    if(ret == GMERR_UNDEFINED_TABLE){
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入表
    system("gmimport -c vschema -f ./schema_files/024/schema_error_autoincrement.gmjson");

    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=MergeTest1");
    ret = executeCommand(g_command, "MAX_RECORD_COUNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 查日志
    memset(g_command, 0, sizeof(g_command));
    memset(errNum, 0, sizeof(errNum));
    (void)snprintf(errNum, MAX_CMD_SIZE, "GMERR-%d",GMERR_DATA_EXCEPTION);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s/%s | grep %s", &envCheck, &log_path , &log_file, &errNum);
    ret = executeCommand(g_command, errNum);
#if defined ENV_RTOSV2X // SOHO环境暂不支持检测工具日志，暂时跳过
    ret = 0;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ***********************************************************************************
 Description  : 025.自动导入合并后的gmjson文件
*********************************************************************************** */
TEST_F(fileMergeTest, SEC_021_MergeTest_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"schemaLoader=1\"");
    char t_file_path[200] = "./testcases/10_Security/021_fileMergeTest/schema_files";
    const char *labelName1 = "MergeTest1";
    const char *labelName2 = "MergeTest2";
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 改配置
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s/001;%s/001;%s/001;%s/001;\"", 
        &t_file_path, &t_file_path, &t_file_path, &t_file_path);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaDataPath=%s/001;%s/001;\"", 
        &t_file_path, &t_file_path);
    system(g_command);
    
    // 起服务
    system("sh $TEST_HOME/tools/start.sh");
    // 查视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: MergeTest1","VERTEX_LABEL_NAME: MergeTest2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    
    // 删表
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmddl -c drop -t %s",&labelName1);
    ret = executeCommand(g_command, "successfully dropped table num is 1");
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmddl -c drop -t %s",&labelName2);
    ret = executeCommand(g_command, "successfully dropped table num is 1");
    memset(g_command, 0, sizeof(g_command));

    AW_FUN_Log(LOG_STEP, "test end.");
}
