[{"name": "MergeTest1", "version": "2.0", "type": "record", "config": {"max_record_count": 1000}, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "string", "size": 128, "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint16", "nullable": true}], "keys": [{"name": "MergeTest1_PK", "node": "MergeTest1", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "MergeTest2", "version": "2.0", "type": "record", "config": {"max_record_count": 1000}, "fields": [{"name": "F0", "type": "uint16", "nullable": false}, {"name": "F1", "type": "string", "size": 128, "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint16", "nullable": true}], "keys": [{"name": "MergeTest2_PK", "node": "MergeTest2", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"users": [{"user": "root1", "process": "MergeTest"}]}]