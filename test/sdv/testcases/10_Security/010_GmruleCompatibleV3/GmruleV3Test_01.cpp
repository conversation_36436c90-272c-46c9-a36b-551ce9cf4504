
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;
char *normal_vertexlabel_schema_01 = NULL;
char *normal_vertexlabel_schema_02 = NULL;
char *normal_vertexlabel_schema_03 = NULL;
char *normal_vertexlabel_schema_04 = NULL;
const char *g_kv_table_name = "kv_01";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

const char *userName = "root";
const char *passwd = "XXpwd";

class SEC_010_GmruleCompatibleV3 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        printf("[INFO] GmruleCompatibleV3 Start.\n");
        system("sh $TEST_HOME/tools/stop.sh -f");
        //权限校验改为强制模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");

        readJanssonFile("schema_file/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        readJanssonFile("schema_file/NormalVertexLabel_01.gmjson", &normal_vertexlabel_schema_01);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema_01);
        readJanssonFile("schema_file/NormalVertexLabel_02.gmjson", &normal_vertexlabel_schema_02);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema_02);
        readJanssonFile("schema_file/NormalVertexLabel_03.gmjson", &normal_vertexlabel_schema_03);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema_03);
        readJanssonFile("schema_file/NormalVertexLabel_04.gmjson", &normal_vertexlabel_schema_04);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema_04);
        //拉起server
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schema_file/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
    }
    static void TearDownTestCase()
    {
        //恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
        free(normal_vertexlabel_schema_01);
        free(normal_vertexlabel_schema_02);
        free(normal_vertexlabel_schema_03);
        free(normal_vertexlabel_schema_04);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_010_GmruleCompatibleV3::SetUp()
{
    int ret = 0;

    //导入白名单
    const char *allow_list_file = "./schema_file/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "./schema_file/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void SEC_010_GmruleCompatibleV3::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除白名单
    const char *allow_list_file = "./schema_file/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command,
        "GMDBV5/test/sdv/testcases/10_Security/010_GmruleCompatibleV3/schema_file/allow_list.gmuser successfully.",
        "remove db user. success: 1, warning: 0.", "remove db group. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] GmruleCompatibleV3 End.\n");
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[8] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}
// 008 不创建vertexlabel，导入对象权限文件
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_008)
{
    int ret = 0;
    const char *obj_policy_file1 = "schema_file/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}
// 009 不创建vertexlabel，导入对象权限文件，再创建vertexlabel，插入数据，校验insert权限
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_009)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    int ret = 0;
    // grant other
    const char *obj_policy_file1 = "schema_file/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty(g_stmt, 0);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcDropVertexLabel(g_stmt, "T39_all_type");
    EXPECT_EQ(GMERR_OK, ret);
}
// 010 不创建kv表，导入对象权限文件
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_010)
{
    int ret = 0;
    // grant other
    const char *obj_policy_file1 = "schema_file/allObjKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    // before grant
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_table_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}
// 011 不创建kv表，导入对象权限文件，再创建kv表，插入数据，校验insert权限
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_011)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    int ret = 0;
    // grant other
    const char *obj_policy_file1 = "schema_file/allObjKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //建表
    ret = GmcKvCreateTable(g_stmt, g_kv_table_name, config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_table_name);
    ASSERT_EQ(GMERR_OK, ret);
    char key[128] = "zhangsan";
    int value = 10;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_table_name);
    EXPECT_EQ(GMERR_OK, ret);
}
// 012 创建部分vertexlabel ，导入全部表的对象权限文件，再创建vertexlabel
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_012)
{
    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema_01, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schema_file/allObjVertex_01.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 2, warning: 3.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema_02, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema_03, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema_04, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 撤销对象权限
    const char *obj_policy_file2 = "./schema_file/revokeVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 2, warning: 0.",
        "revoke policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "T39_all_type");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "T40_all_type");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "T41_all_type");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "T42_all_type");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "T43_all_type");
    EXPECT_EQ(GMERR_OK, ret);
}
// 013 创建部分kv表 ，导入全部表的对象权限文件，再创建kv表
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_013)
{
    int ret = 0;
    // grant other
    ret = GmcKvCreateTable(g_stmt, "kv_00", config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, "kv_01", config_json);
    ASSERT_EQ(GMERR_OK, ret);
    const char *obj_policy_file1 = "schema_file/allObjKV_01.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 2, warning: 3.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //建表
    ret = GmcKvCreateTable(g_stmt, "kv_02", config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, "kv_03", config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, "kv_04", config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, "kv_00");
    ASSERT_EQ(GMERR_OK, ret);
    // 撤销对象权限
    const char *obj_policy_file2 = "./schema_file/revokeKv.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 2, warning: 0.",
        "revoke policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, "kv_00");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, "kv_01");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, "kv_02");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, "kv_03");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, "kv_04");
    EXPECT_EQ(GMERR_OK, ret);
}
// 014 namespaceA创建vertexlabel，导入public下同名vertexlabel对象权限，插入数据，校验insert权限
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_014)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    int ret = 0;
    // grant other
    const char *nameSpace = (const char *)"namespaceA";
    const char *userName = (const char *)"abc";
    ret = GmcCreateNamespace(g_stmt, nameSpace, userName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    const char *obj_policy_file1 = "schema_file/allObjVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);system(g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //建表
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty(g_stmt, 0);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcDropVertexLabel(g_stmt, "T39_all_type");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, "public");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //建表
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty(g_stmt, 0);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcDropVertexLabel(g_stmt, "T39_all_type");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}
// 015 namespaceA创建kv表，导入public下同名kv表对象权限，插入数据，校验insert权限
TEST_F(SEC_010_GmruleCompatibleV3, SEC_010_GmruleCompatibleV3_015)
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    int ret = 0;
    // grant other
    const char *nameSpace = (const char *)"namespaceA";
    const char *userName = (const char *)"abc";
    ret = GmcCreateNamespace(g_stmt, nameSpace, userName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, "kv_01", config_json);
    ASSERT_EQ(GMERR_OK, ret);
    const char *obj_policy_file1 = "schema_file/allObjKV.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 1.",
        "import policy. system privilege success: 0, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    //建表
    ret = GmcKvPrepareStmtByLabelName(g_stmt, "kv_01");
    EXPECT_EQ(GMERR_OK, ret);
    char key[128] = "zhangsan";
    int value = 10;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcKvDropTable(g_stmt, "kv_01");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}
