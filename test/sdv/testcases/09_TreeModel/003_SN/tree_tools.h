extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
AsyncUserDataT asynData = {0};
void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[strlen(f14_value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"F15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value));

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &string_value, 10, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[strlen(f14_value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"P15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value));

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[strlen(f14_value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"A15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value));

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_16, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_8, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[strlen(f14_value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"V15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value));

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcInsertVertexBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14_value,
    int start_num, int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
}

void TestGmcInsertVertexAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asynData;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asynData);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, asynData.status);
    }
}

void TestGmcInsertVertexAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14_value,
    int start_num, int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ;
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);
}

void TestGmcInsertVertexSuperfiled(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // free
    free(sp_1);
}

void TestGmcInsertVertexSuperfiledBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14_value,
    int start_num, int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    // free
    free(sp_1);
}

void TestGmcInsertVertexSuperfiledAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asynData;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asynData);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, asynData.status);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // free
    free(sp_1);
}

void TestGmcInsertVertexSuperfiledAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14_value, int start_num, int end_num, int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ;
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);
    // free
    free(sp_1);
}

void TestGmcUpdateVertexByIndexKey(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcUpdateVertexByIndexKeyBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value, char *f14_value,
    int start_num, int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
}

void TestGmcUpdateVertexByIndexKeyAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asynData;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asynData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asynData.status);
    }
}

void TestGmcUpdateVertexByIndexKeyAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14_value, int start_num, int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ;
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);
}

void TestGmcUpdateVertexByIndexKeySuperfiled(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // free
    free(sp_1);
}

void TestGmcUpdateVertexByIndexKeySuperfiledBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14_value, int start_num, int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    // free
    free(sp_1);
}

void TestGmcUpdateVertexByIndexKeySuperfiledAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14_value,
    int start_num, int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asynData;
        ret = GmcExecuteAsync(stmt, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asynData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asynData.status);
    }

    // free
    free(sp_1);
}

void TestGmcUpdateVertexByIndexKeySuperfiledAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, int index, bool bool_value,
    char *f14_value, int start_num, int end_num, int array_num, int vector_num, const char *labelName, char *keyName)
{
    int32_t ret = 0;

    char *sp_1 = (char *)malloc(26);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
        // set superfiled by name
        char *temp = sp_1;
        *(int64_t *)(temp) = 1 * i * index;
        *(uint64_t *)(temp + 8) = 1 * i * index;
        *(int32_t *)(temp + 16) = 2 * i * index;
        *(uint32_t *)(temp + 20) = 3 * i * index;
        *(int16_t *)(temp + 24) = 4 * i * index;

        ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeGetNextElement(t2, &t2);
        }

        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
            ASSERT_EQ(GMERR_OK, ret);
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ;
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);

    // free
    free(sp_1);
}

void TestGmcDirectFetchVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num)
{
    int32_t ret = 0;

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isFinish);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_R(root, i * index, bool_value, f14_value);
            TestGmcNodeGetPropertyByName_p(t1, i * index, bool_value, f14_value);
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, i * index, bool_value, f14_value);
            }
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, i * index, bool_value, f14_value);
            }
        }
    }
}

void TestGmcDirectFetchVertexSuperfiled(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num)
{
    int32_t ret = 0;

    char *sp_1_get = (char *)malloc(26);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isFinish);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeGetPropertyByName_R(root, i * index, bool_value, f14_value);

            // get superfiled by name
            uint32_t length;
            ret = GmcNodeGetSuperfieldSizeByName(t1, (char *)"superfiled0", &length);
            EXPECT_EQ((unsigned int)26, length);
            ret = GmcNodeGetSuperfieldByName(t1, (char *)"superfiled0", sp_1_get, 26);
            EXPECT_EQ(GMERR_OK, ret);
            char *temp = sp_1_get;
            EXPECT_EQ(1 * i * index, *(int64_t *)(temp));
            EXPECT_EQ(1 * i * index, *(uint64_t *)(temp + 8));
            EXPECT_EQ(2 * i * index, *(uint32_t *)(temp + 16));
            EXPECT_EQ(3 * i * index, *(int32_t *)(temp + 20));
            EXPECT_EQ(4 * i * index, *(int16_t *)(temp + 24));

            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                EXPECT_EQ(GMERR_OK, ret);
                uint32_t length;
                ret = GmcNodeGetSuperfieldSizeByName(t2, (char *)"superfiled1", &length);
                EXPECT_EQ((unsigned int)26, length);
                ret = GmcNodeGetSuperfieldByName(t1, (char *)"superfiled0", sp_1_get, 26);
                char *temp = sp_1_get;
                EXPECT_EQ(1 * i * index, *(int64_t *)(temp));
                EXPECT_EQ(1 * i * index, *(uint64_t *)(temp + 8));
                EXPECT_EQ(2 * i * index, *(uint32_t *)(temp + 16));
                EXPECT_EQ(3 * i * index, *(int32_t *)(temp + 20));
                EXPECT_EQ(4 * i * index, *(int16_t *)(temp + 24));
            }

            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                EXPECT_EQ(GMERR_OK, ret);
                uint32_t length;
                ret = GmcNodeGetSuperfieldSizeByName(t3, (char *)"superfiled2", &length);
                EXPECT_EQ((unsigned int)26, length);
                ret = GmcNodeGetSuperfieldByName(t3, (char *)"superfiled2", sp_1_get, 26);
                char *temp = sp_1_get;
                EXPECT_EQ(1 * i * index, *(int64_t *)(temp));
                EXPECT_EQ(1 * i * index, *(uint64_t *)(temp + 8));
                EXPECT_EQ(2 * i * index, *(uint32_t *)(temp + 16));
                EXPECT_EQ(3 * i * index, *(int32_t *)(temp + 20));
                EXPECT_EQ(4 * i * index, *(int16_t *)(temp + 24));
            }
        }
    }

    free(sp_1_get);
}
