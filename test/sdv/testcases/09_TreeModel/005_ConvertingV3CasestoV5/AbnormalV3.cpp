extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tree_tools.h"

GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL;
char *g_schema = NULL, *g_schema_2 = NULL;
GmcStmtT *g_stmt_sub = NULL;
GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
GmcConnT *connectionAsync = NULL;
char label_name1[] = "OP_T0";
char lalable_name_PK1[] = "OP_PK";
char label_name2[] = "DST_labelName";
char lalable_name_PK2[] = "DST_PK";
char label_name3[] = "edgelabel_testEdge";
char g_label_config_test[] = "{\"max_record_num\":10000}";
#define MAX_VERTEX_NUM 10000
#define MAX_NAME_LENGTH 128
int g_subIndex = 0;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";

char *test_schema1 = NULL;
char *test_schema2 = NULL;
char *test_schema3 = NULL;
const char *edgeLabelName = "edgelabel_testEdge";

int start_num = 0;
int end_num = 100;
int array_num = 3;
int vector_num = 3;

void sn_callback1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //读new
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[g_subIndex];
            printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
            bool isNull;
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(subStmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t f0_value;
            ret = GmcNodeGetPropertyByName(t1, (char *)"T2.P0", &f0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
        }
    }
}

void sn_callback2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //读new
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[g_subIndex];
            printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(subStmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t f0_value = index;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);

            uint64_t f1_value = 2 * index;
            ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(subStmt);
            EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
        }
    }
}

class Abnormal_V3_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void Abnormal_V3_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = 0;
    g_stmt_async = NULL;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void Abnormal_V3_test::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    ASSERT_EQ(0, ret);
    testEnvClean();
}

void Abnormal_V3_test::SetUp()
{
    int ret = 0;
    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/ConvertingV3CasestoV5_op.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0},
        errorMsg4[errCodeLen] = {0}, errorMsg5[errCodeLen] = {0}, errorMsg6[errCodeLen] = {0},
        errorMsg7[errCodeLen] = {0}, errorMsg8[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_CONNECTION_FAILURE);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg6, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg7, errCodeLen, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(errorMsg8, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(8, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6,
        errorMsg7, errorMsg8);
}

void Abnormal_V3_test::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    free(test_schema1);
    GmcDropVertexLabel(stmt, label_name1);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 反复重置stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_001)
{
    int32_t ret = 0;
    for (int i = 0; i < end_num; i++) {
        GmcResetStmt(stmt);
    }

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/*****************************************************************************
 Description  : 未创建stmt，stmt为NULL，直接重置stmt
 Input        : None
 Output       : None
 Return Value : DTS2021070704ZV1AP1I00
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_002)
{
    int32_t ret = 0;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2;
    // 1.未创建stmt，stmt为NULL，直接重置stmt,插入数据
    GmcResetStmt(stmt1);

    ret = testGmcPrepareStmtByLabelName(stmt1, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcResetVertex(stmt1, false);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 2.未创建stmt，stmt不赋值NULL，直接重置stmt
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcResetStmt(stmt);

    // GmcResetStmt(stmt2);
}

/*****************************************************************************
 Description  : 重复释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        : 资料已有说明stmt一旦释放就禁止使用，建议将其设置为NULL，以避免野指针或重复释放
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_003)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    /*     for (int i = 0; i < end_num; i++) {
            GmcFreeStmt(stmt);
        } */
}

/*****************************************************************************
 Description  : 建连接前，反复创建stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_004)
{
    int32_t ret = 0;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2;

    /*   ret = testGmcDisconnect(conn, stmt);
    /*   EXPECT_EQ(GMERR_OK, ret);

       // 1.建连接前，反复创建stmt  100次，stmt初始值为NULL的结果
       for (int i = 0; i < end_num; i++) {
           ret = GmcAllocStmt(conn, &stmt1);
           ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
       }

       // 2.建连接前，反复创建stmt  100次，stmt无初始值的结果
       for (int i = 0; i < end_num; i++) {
           ret = GmcAllocStmt(conn, &stmt2);
           ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
       }

       //创建同步连接
       ret = testGmcConnect(&conn, &stmt);
       ASSERT_EQ(GMERR_OK, ret); */
}

/*****************************************************************************
 Description  : 建连接前，反复释放stmt
 Input        : None
 Output       : None
 Return Value : DTS2021070704ZV1AP1I00
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_005)
{
    int32_t ret = 0;
    GmcStmtT *stmt2;
    /*
        // 2.建连接前，反复释放stmt  100次，stmt无初始值的结果
        for (int i = 0; i < end_num; i++) {
            GmcFreeStmt(stmt2);
        } */

    GmcStmtT *stmt1 = NULL;
    // 1.建连接前，反复释放stmt  100次，stmt初始值为NULL的结果
    GmcFreeStmt(stmt1);
}

/*****************************************************************************
 Description  : 建连接后，反复创建stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_006)
{
    int32_t ret = 0;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2;

    // 1.建连接后，反复创建stmt  100次，stmt初始值为NULL的结果
    for (int i = 0; i < end_num; i++) {
        ret = GmcAllocStmt(conn, &stmt1);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 2.建连接后，反复创建stmt  100次，stmt无初始值的结果
    for (int i = 0; i < end_num; i++) {
        ret = GmcAllocStmt(conn, &stmt2);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 建连接后，反复释放stmt
 Input        : None
 Output       : None
 Return Value : DTS2021070704ZV1AP1I00
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_007)
{
    int32_t ret = 0;
    GmcStmtT *stmt2;
    /*
     // 2.建连接后，反复释放stmt  100次，stmt无初始值的结果
     for (int i = 0; i < end_num; i++) {
         GmcFreeStmt(stmt2);
     } */

    GmcStmtT *stmt1 = NULL;
    // 1.建连接后，反复释放stmt  100次，stmt初始值为NULL的结果
    GmcFreeStmt(stmt1);
}

/*****************************************************************************
 Description  : 释放stmt，继续使用stmt
 Input        : None
 Output       : None
 Return Value : DTS2021070704ZV1AP1I00
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_008)
{
    int32_t ret = 0;
    GmcStmtT *stmt1 = NULL;

    ret = GmcAllocStmt(conn, &stmt1);
    ASSERT_EQ(GMERR_OK, ret);
    // 普通同步插入数据
    TestGmcInsertVertex(stmt1, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    GmcFreeStmt(stmt1);
    stmt1 = NULL;

    ret = testGmcPrepareStmtByLabelName(stmt1, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    //插入顶点
    GmcNodeT *root = NULL, *t1 = NULL, *t2 = NULL, *t3 = NULL;
    ret = GmcGetRootNode(stmt1, &root);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    int64_t f0_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    uint32_t f3_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

/*****************************************************************************
 Description  : 创建stmt后写数据不释放stmt，直接关闭连接池,继续建表
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_009)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    /*   ret = testGmcDisconnect(conn);
    /*   EXPECT_EQ(GMERR_OK, ret);

        ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
       ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
       ret = testGmcConnect(&conn, &stmt);
       ASSERT_EQ(GMERR_OK, ret); */
}

/*****************************************************************************
 Description  : 重复建表、写数据、删表操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_010)
{
    int32_t ret = 0;

    for (int i = start_num; i < end_num; i++) {
        // 普通同步插入数据
        TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

        // 普通同步删除数据
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        // 读取数据
        TestGmcDirectFetchVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1,
            lalable_name_PK1, false);
    }
}

/*****************************************************************************
 Description  : 无连接/关闭连接池后在做删表操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_011)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    /*  ret = testGmcDisconnect(conn);
    /*  EXPECT_EQ(GMERR_OK, ret);

      ret = GmcDropVertexLabel(stmt, label_name1);
      ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

       ret = testGmcConnect(&conn, &stmt);
      ASSERT_EQ(GMERR_OK, ret); */
}

/*****************************************************************************
 Description  : 表存在，关闭连接池后重新建立连接池继续在做读数据、写数据删表操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_012)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);

    /*  ret = testGmcDisconnect(conn);
    /*  EXPECT_EQ(GMERR_OK, ret);

      ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
      ASSERT_EQ(GMERR_OK, ret);
      GmcNodeT *root = NULL;
      ret = GmcGetRootNode(stmt, &root);
      EXPECT_EQ(GMERR_MEMORY_OPERATE_FAILED, ret);
       ret = testGmcGetLastError(NULL);
      EXPECT_EQ(GMERR_OK, ret);
      // 插入顶点
      int64_t f0_value = 0;
      ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
      ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

      ret = GmcExecute(stmt);
      ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

       ret = testGmcConnect(&conn, &stmt);
      ASSERT_EQ(GMERR_OK, ret); */
}

/*****************************************************************************
 Description  : 订阅回调里，对数据进行读操作，父节点不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_013)
{
    int32_t ret = 0;

    int end_num = 10;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * end_num * 3);
    memset(user_data->new_value, 0, sizeof(int) * end_num * 3);

    //创建订阅连接
    GmcConnT *g_subChan = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    int userDataIdx = 0;
    char *sub_info = NULL;
    readJanssonFile("schema_file/simple_schema_subinfo.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, g_subChan, sn_callback1, user_data);
    ASSERT_EQ(GMERR_OK, ret);
    free(sub_info);

    for (int i = start_num; i < end_num; i++) {

        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, end_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_subChan, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data);
}

/*****************************************************************************
 Description  : 订阅回调里,用回调里的stmt进行更新操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_014)
{
    int32_t ret = 0;

    int end_num = 10;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * end_num * 3);
    memset(user_data->new_value, 0, sizeof(int) * end_num * 3);

    //创建订阅连接
    GmcConnT *g_subChan = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    int userDataIdx = 0;
    char *sub_info = NULL;
    readJanssonFile("schema_file/simple_schema_subinfo.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, g_subChan, sn_callback2, user_data);
    ASSERT_EQ(GMERR_OK, ret);
    free(sub_info);

    for (int i = start_num; i < end_num; i++) {

        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, end_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_subChan, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data);
}

/*****************************************************************************
 Description  : 表不存在，进行全表扫描
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_015)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 表不存在，进行索引扫描
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_016)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 表中无数据，进行全表扫描
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_017)
{
    int32_t ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(true, eof);
    }
}

/*****************************************************************************
 Description  : 扫描出数据后，对数据进行读操作，父节点不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_018)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(false, eof);
        int64_t f0_value;
        bool isNull;
        GmcNodeT *t4 = NULL;
        ret = GmcNodeGetPropertyByName(t4, (char *)"P20", &f0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    }
}

/*****************************************************************************
 Description  : 扫描出数据后，对数据进行读操作，子节点与父节点不在同一处
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_019)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        ASSERT_EQ(false, eof);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value;
        bool isNull;
        ret = GmcNodeGetPropertyByName(t1, (char *)"P20", &f0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    }
}

/*****************************************************************************
 Description  : 扫描出数据后，对数据进行更新操作，更新完后释放stmt，继续扫描
 Input        : None
 Output       : None
 Return Value : DTS2021073112282
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_020)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        if (ret != 0) {
            ASSERT_EQ(GMERR_INTERNAL_ERROR, ret);
            break;
        };
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            printf("----\n");
            break;
        }
        ASSERT_EQ(false, eof);
        /*  GmcNodeT *root, *t1, *t2, *t3;
         ret = GmcGetRootNode(stmt, &root);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T1", &t1);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcNodeGetChild(root, "T3", &t3);
         EXPECT_EQ(GMERR_OK, ret);
         int64_t f0_value = i;
         ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
         ASSERT_EQ(GMERR_OK, ret);
         uint64_t f1_value = 2 * i;
         ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
         EXPECT_EQ(GMERR_OK, ret);

         ret = GmcSetIndexKeyName(stmt, NULL);
         EXPECT_EQ(GMERR_OK, ret);
         ret = GmcExecute(stmt);
         EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret); */
    }
}

/*****************************************************************************
 Description  : 扫描出数据后，对数据进行删除操作，数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_021)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        if (ret != 0) {
            ASSERT_EQ(GMERR_MEMORY_OPERATE_FAILED, ret);
            break;
        };
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            printf("----\n");
            break;
        }
        ASSERT_EQ(false, eof);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 索引扫描出数据后，对数据进行读操作，父节点不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_022)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, "OP_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        ASSERT_EQ(false, eof);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value;
        bool isNull;
        GmcNodeT *t4 = NULL;
        ret = GmcNodeGetPropertyByName(t4, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    }
}

/*****************************************************************************
 Description  : 索引扫描数据后，对数据进行读操作，子节点与父节点不在同一处
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_023)
{
    int32_t ret = 0;
    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, "OP_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcFetch(stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        ASSERT_EQ(false, eof);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value;
        bool isNull;
        ret = GmcNodeGetPropertyByName(t1, (char *)"P20", &f0_value, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    }
}

/*****************************************************************************
 Description  : 批量写数据，父节点不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_024)
{
    int32_t ret = 0;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *t4 = NULL;
        TestGmcNodeSetPropertyByName_PK(root, i);
        int64_t f0_value = i;
        ret = GmcNodeSetPropertyByName(t4, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // compatibleV3为0时，会报二级索引冲突（localhash）
    // compatibleV3为1时，如果索引类型为local或hashcluster并且为唯一索引，则会将唯一索引强制改为非唯一索引
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);  // 主键和localhash字段都为F0，所以当compatibleV3为0时会冲突，为1时正常
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
}

/*****************************************************************************
 Description  : 批量写数据，子节点与父节点不在同一处
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_025)
{
    int32_t ret = 0;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        int64_t f0_value = i;
        ret = GmcNodeSetPropertyByName(root, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // compatibleV3为0时，会报二级索引冲突（localhash）
    // compatibleV3为1时，如果索引类型为local或hashcluster并且为唯一索引，则会将唯一索引强制改为非唯一索引
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);  // 主键和localhash字段都为F0，所以当compatibleV3为0时会冲突，为1时正常
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
}

/*****************************************************************************
 Description  : 批量更新数据，父节点不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_026)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *t4 = NULL;
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        int64_t p0_value = 2 * i;
        ret = GmcNodeSetPropertyByName(t4, (char *)"P0", GMC_DATATYPE_INT64, &p0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        uint64_t p1_value = i;
        ret = GmcNodeSetPropertyByName(t1, (char *)"P1", GMC_DATATYPE_UINT64, &p1_value, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/*****************************************************************************
 Description  : 批量更新数据，子节点与父节点不在同一处
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_027)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        int64_t p0_value = 2 * i;
        ret = GmcNodeSetPropertyByName(root, (char *)"P0", GMC_DATATYPE_INT64, &p0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
        uint64_t p1_value = i;
        ret = GmcNodeSetPropertyByName(t1, (char *)"P1", GMC_DATATYPE_UINT64, &p1_value, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/*****************************************************************************
 Description  : 批量删除操作，数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_028)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 删除顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = end_num + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    EXPECT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/*****************************************************************************
 Description  : 重复删除数据，数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_029)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 普通同步删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (int k = start_num; k < 100 * end_num; k++) {
        int64_t f0_value = end_num + 1;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        int affectRows = 0;
        unsigned int len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/*****************************************************************************
 Description  : 重复更新数据,数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_030)
{
    int32_t ret = 0;

    // 普通同步插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    // 普通同步更新数据
    for (int k = start_num; k < 100 * end_num; k++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = end_num + 1;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t f1_value = 2;
        ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows = 0;
        unsigned int len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1, lalable_name_PK1, true);
}

/*****************************************************************************
 Description  : 重复读数据,数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_031)
{
    int32_t ret = 0;

    int end_num = 1;
    for (int k = start_num; k < 1000 * end_num; k++) {
        // 读取数据
        TestGmcDirectFetchVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1,
            lalable_name_PK1, false);
    }
}

/*****************************************************************************
 Description  : 重复索引扫描数据，数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_032)
{
    int32_t ret = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (int k = start_num; k < 100 * end_num; k++) {
        int64_t f0_value = end_num + 1;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < end_num; i++) {
            bool eof;
            ret = GmcSetIndexKeyName(stmt, "localhash_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcFetch(stmt, &eof);
            ASSERT_EQ(GMERR_OK, ret);
            if (eof == true) {
                break;
            }
            ASSERT_EQ(false, eof);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t p0_value;
            bool isNull;
            ret = GmcNodeGetPropertyByName(t1, (char *)"P0", &p0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
        }
    }
}

/*****************************************************************************
 Description  : 重复全表扫描数据，数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_033)
{
    int32_t ret = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    for (int k = start_num; k < 100 * end_num; k++) {
        for (int i = start_num; i < end_num; i++) {
            bool eof;
            ret = GmcSetIndexKeyName(stmt, NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcFetch(stmt, &eof);
            ASSERT_EQ(GMERR_OK, ret);
            if (eof == true) {
                break;
            }
            ASSERT_EQ(false, eof);
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t f0_value;
            bool isNull;
            ret = GmcNodeGetPropertyByName(t1, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
        }
    }
}

/*****************************************************************************
 Description  : 重复条件扫描数据，数据不存在
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_034)
{
    int32_t ret = 0;

    const char *cond1 = (const char *)"100<=OP_T0.F0";

    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int k = start_num; k < 100 * end_num; k++) {

        ret = GmcSetFilter(stmt, cond1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetOutputFormat(stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // check
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);

            if (isFinish == true) {
                break;
            }
            fetchtimes++;
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t f0_query;
            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_query, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
        }
        ASSERT_EQ(0, fetchtimes);
    }
    GmcResetStmt(stmt);
}

/*****************************************************************************
 Description  : 连接满后，写数据失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_035)
{
    int32_t ret = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
// 适配光启连接数，光启环境每个进程都会创建一个心跳连接
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    // 补充逃生通道2个
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt_t[0], &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 重复插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt_t[0], &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 连接满后，更新失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_036)
{
    int32_t ret = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt_t[0], &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 普通同步更新数据
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt_t[0], &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t f0_value = end_num + 1;
        ret = GmcSetIndexKeyValue(stmt_t[0], 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t f1_value = 2 * i;
        ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t[0], lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t[0]);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows;
        unsigned int len = 0;
        ret = GmcGetStmtAttr(stmt_t[0], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 连接满后，读失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_037)
{
    int32_t ret = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt_t[0], &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 读数据
    ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = end_num + 1;
        ret = GmcSetIndexKeyValue(stmt_t[0], 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t[0], lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt_t[0], &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
        int affectRows;
        unsigned int len = 0;
        ret = GmcGetStmtAttr(stmt_t[0], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 连接满后，删除失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_038)
{
    int32_t ret = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
            system("gmsysview -q V\\$DRT_CONN_STAT | grep CONN_ID | wc -l");
            connCnt = i;
            break;
        }
    }

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt_t[0], &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 普通同步删除数据
    ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = end_num + 1;
        ret = GmcSetIndexKeyValue(stmt_t[0], 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t[0], lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_OK, ret);
        int affectRows;
        unsigned int len = 0;
        ret = GmcGetStmtAttr(stmt_t[0], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 表不存在，truncate失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_039)
{
    int32_t ret = 0;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcTruncateVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

/*****************************************************************************
 Description  : 连接满后，全表扫描失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_040)
{
    int32_t ret = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    for (int i = start_num; i < end_num; i++) {
        bool eof;
        ret = GmcSetIndexKeyName(stmt_t[0], NULL);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    }

    // 关闭查询
    GmcResetStmt(stmt_t[0]);

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 连接满后，索引扫描失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_041)
{
    int32_t ret = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    int64_t f0_value = 0;
    ret = GmcSetIndexKeyValue(stmt_t[0], 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt_t[0], "localhash_key");
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt_t[0]);

    for (int i = start_num; i < end_num; i++) {

        ret = GmcSetIndexKeyName(stmt_t[0], NULL);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = GmcExecute(stmt_t[0]);
        ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    }

    // 关闭查询
    GmcResetStmt(stmt_t[0]);

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 连接满后，非条件索引扫描失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_042)
{
    int32_t ret = 0;
    const char *cond1 = (const char *)"100<=OP_T0.F0";
    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
#ifdef ENV_SUSE
    uint32_t connCnt = MAX_CONN_SIZE - connNum - 1;
#else
    uint32_t connCnt = MAX_CONN_SIZE - connNum;
#endif
    for (int i = 0; i < connCnt; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_t[0], label_name2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = GmcSetFilter(stmt_t[0], cond1);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
        ret = GmcSetOutputFormat(stmt_t[0], NULL);
        EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    }

    // 关闭查询
    GmcResetStmt(stmt_t[0]);

    for (int i = 0; i < connCnt; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 连接满后，订阅失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_043)
{
    int32_t ret = 0;
    const char *cond1 = (const char *)"100<=OP_T0.F0";
    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE] = {0};
    for (int i = 0; i < MAX_CONN_SIZE - 5 - connNum; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //创建订阅连接
    GmcConnT *g_subChan = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile("schema_file/simple_schema_subinfo.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    //无效的stmt
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt_sub, &tmp_sub_info, g_subChan, sn_callback1, user_data);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(sub_info);

    ret = testSubDisConnect(g_subChan, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < MAX_CONN_SIZE - 5 - connNum; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 表不存在，删表失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_044)
{
    int32_t ret = 0;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, label_name2);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

/*****************************************************************************
 Description  : 连接满后，关闭连接失败后释放stmt
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_045)
{
    int32_t ret = 0;
    const char *cond1 = (const char *)"100<=OP_T0.F0";
    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t connNum = 0;
    ret = testGetConnNum(&connNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_t[MAX_CONN_SIZE + 1] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE + 1] = {0};
    for (int i = 0; i < MAX_CONN_SIZE - 5 - connNum; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn_t[MAX_CONN_SIZE]);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < MAX_CONN_SIZE - 5 - connNum; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : DB未启动，直接执行操作，如连接，建表，dml，ddl，扫描、删表等操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_046)
{
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    GmcDetachAllShmSeg();

    conn = NULL;
    stmt = NULL;
    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_CONNECTION_FAILURE, ret);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    //插入顶点
    GmcNodeT *root = NULL, *t1 = NULL, *t2 = NULL, *t3 = NULL;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    int64_t f0_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    //更新数据
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    uint64_t f1_value = 1;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    //删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    //恢复环境
    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    GmcDetachAllShmSeg();
    system("sh $TEST_HOME/tools/start.sh -f ");
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  :
 DB启动后操作到一半，关闭连接，中途kill掉server，不重新拉起，继续操作，如连接，建表，dml，ddl，扫描、删表等操作
 Input :
 None Output       : None Return
 Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_047)
{
    int32_t ret = 0;
    // 普通同步插入数据
    // TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    system("sh $TEST_HOME/tools/stop.sh -f");
    // GmcDetachAllShmSeg();
    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_CONNECTION_FAILURE, ret);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);

    //插入顶点
    GmcNodeT *root = NULL, *t1 = NULL, *t2 = NULL, *t3 = NULL;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    int64_t f0_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    uint64_t f1_value = 1;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    //删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);
    //恢复环境
    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    GmcDetachAllShmSeg();
    system("sh $TEST_HOME/tools/start.sh -f ");
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  :
DB启动后操作到一半，不关闭连接，中途kill掉server，不重新拉起，继续操作，如连接，建表，dml，ddl，扫描、删表等操作 Input
: None Output       : None Return Value : Notes        : History      : Author       : qinjianhua wx620469 Modification
:
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_048)
{
    int32_t ret = 0;
    // 普通同步插入数据
    // TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);

    system("sh $TEST_HOME/tools/stop.sh -f");
    // GmcDetachAllShmSeg();
    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_CONNECTION_FAILURE, ret);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);

    //插入顶点
    GmcNodeT *root = NULL, *t1 = NULL, *t2 = NULL, *t3 = NULL;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    int64_t f0_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    uint64_t f1_value = 1;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    //删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);
    //恢复环境
    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    GmcDetachAllShmSeg();
    system("sh $TEST_HOME/tools/start.sh -f ");
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 加-f重启server之后不建连接(重启之前关闭连接)，继续操作，如连接，建表，dml，ddl，扫描、删表等操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_049)
{
    int32_t ret = 0;
    // 普通同步插入数据
    // TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -f");
    GmcDetachAllShmSeg();
    system("sh $TEST_HOME/tools/start.sh -f ");

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name1);
    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //插入顶点
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t f0_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_R(root, 0, 0, (char *)"string");
    ret = GmcSetIndexKeyName(stmt, "OP_PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f1_value = 1;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 加-f重启server之后不建连接(重启之前不关闭连接)，继续操作，如连接，建表，dml，ddl，扫描、删表等操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_050)
{
    int32_t ret = 0;
    // 普通同步插入数据
    // TestGmcInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, label_name1);
    system("sh $TEST_HOME/tools/stop.sh -f");  //修改配置，先停服务
    GmcDetachAllShmSeg();
    system("sh $TEST_HOME/tools/start.sh -f ");

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name1);
    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //插入顶点
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t f0_value = 0;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_R(root, 0, 0, (char *)"string");
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f1_value = 1;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(stmt, label_name1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : local key字段没赋值，lasterror，后台日志显示正确
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Abnormal_V3_test, Abnormal_V3_test_051)
{
    int32_t ret = 0;
    // 插入顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_PK(root, 0);

    char *f14_value = (char *)"string";
    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, (char *)"string", (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
