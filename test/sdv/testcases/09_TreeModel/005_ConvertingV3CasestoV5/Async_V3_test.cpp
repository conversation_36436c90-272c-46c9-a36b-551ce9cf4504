extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tree_tools.h"

GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL;
char *g_schema = NULL, *g_schema_2 = NULL;
#define MAX_CONN 1024
GmcStmtT *g_stmt_sub = NULL;
GmcConnT *conn;
GmcStmtT *stmt;
GmcConnT *connectionAsync = NULL;
char label_name1[] = "OP_T0";
char lalable_name_PK1[] = "OP_PK";
char label_name2[] = "DST_T0";
char lalable_name_PK2[] = "DST_PK";
char label_name3[] = "edgelabel_testEdge";
char g_label_config_test[] = "{\"max_record_num\":10000}";
#define MAX_VERTEX_NUM 10000
#define MAX_NAME_LENGTH 128
int g_subIndex = 0;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";

char *test_schema1 = NULL;
char *test_schema2 = NULL;
char *test_schema3 = NULL;
const char *edgeLabelName = "edgelabel_testEdge";

int start_num = 0;
int end_num = 100;
int array_num = 3;
int vector_num = 3;

void sn_callback1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //读new
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[g_subIndex];
            printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
            bool isNull;
            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);
            int64_t f0_value;
            ret = GmcNodeGetPropertyByName(t1, (char *)"T2/P0", &f0_value, sizeof(int64_t), &isNull);
            ASSERT_EQ(GMERR_INVALID_NAME, ret);
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
        }
    }
}

void sn_callback2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //读new
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[g_subIndex];
            printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);

            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
            ASSERT_EQ(GMERR_OK, ret);

            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            EXPECT_EQ(GMERR_OK, ret);

            int64_t f0_value = index;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            ASSERT_EQ(GMERR_OK, ret);

            uint64_t f1_value = 2 * index;
            ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(subStmt, NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(subStmt);
            ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, ret);

            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
        }
    }
}

class Async_V3_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void Async_V3_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = 0;
    g_stmt_async = NULL;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void Async_V3_test::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    ASSERT_EQ(0, ret);
    testEnvClean();
}

void Async_V3_test::SetUp()
{
    int ret = 0;
    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, label_name1);
    readJanssonFile("schema_file/ConvertingV3CasestoV5_op.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void Async_V3_test::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    free(test_schema1);
    GmcDropVertexLabel(stmt, label_name1);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 异步批量操作中同时添加add set、delete操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_001)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < end_num; i++) {
        // batch set
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch;
        GmcBatchRetT batchRet;
        ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        GmcKvTupleT kvInfo = {0};
        char key_set[1024];
        for (int32_t i = 0; i < end_num; i++) {
            sprintf(key_set, "zhangsan%d", i);
            int32_t value1 = i;
            kvInfo.key = key_set;
            kvInfo.keyLen = strlen(key_set);
            kvInfo.value = &value1;
            kvInfo.valueLen = sizeof(int32_t);

            ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, sizeof(int32_t));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
            ASSERT_EQ(GMERR_OK, ret);
        }

        char key_delete[1024];
        for (int32_t i = 0; i < end_num; i++) {
            sprintf(key_delete, "zhangsan%d", i);
            kvInfo.key = key_delete;
            kvInfo.keyLen = strlen(key_delete);
            kvInfo.valueLen = 0;
            ret = GmcKvInputToStmt(g_stmt_async, key_delete, strlen(key_delete), NULL, 0);
            ASSERT_EQ(GMERR_OK, ret);

            ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_DELETE);
            ASSERT_EQ(GMERR_OK, ret);
        }

        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asynData);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, asynData.status);
        ASSERT_EQ(2 * end_num, asynData.totalNum);
        ASSERT_EQ(2 * end_num, asynData.succNum);
    }

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数3 strlen(key_set)为NULL
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_002)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value1;
        kvInfo.valueLen = sizeof(int32_t);
        // ret = GmcKvInputToStmt(g_stmt_async, key_set, NULL, &value1, sizeof(int32_t));
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), NULL, 0);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    
    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : 表被删除，执行批量操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_003)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value1;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, asynData.status);
    printf("async set asynData num: %d\n", successNum);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数2 key_set的key为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_004)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = NULL;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value1;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async, NULL, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    
    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数2 kvInfo的key_buff长度过长
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_005)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = 51300000;
        kvInfo.value = &value1;
        kvInfo.valueLen = sizeof(int32_t);

        ret = GmcKvInputToStmt(g_stmt_async, key_set, 51300000, &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_INVALID_VALUE, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    
    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数2 kvInfo的value_buff为空
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_006)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = NULL;
        kvInfo.valueLen = sizeof(int32_t);

        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), NULL, sizeof(int32_t));
        ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    
    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数2 kvInfo的value_buff长度过长
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_007)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        ;
        kvInfo.value = &value1;
        kvInfo.valueLen = 5130000;
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, 5130000);
        ASSERT_EQ(GMERR_INVALID_VALUE, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    
    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数2 kvInfo的value_buff为double
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_008)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        double value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        ;
        kvInfo.value = &value1;

        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : GmcKvInputToStmt参数2 kvInfo的value_buff为float
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_009)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        float value1 = 1.23f;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        ;
        kvInfo.value = &value1;

        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

void batch_execute_callback1(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    int ret = 0;
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        ret = GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
        ASSERT_EQ(GMERR_OK, ret);
        user_data->status = status;
        user_data->historyRecvNum++;
        user_data->recvNum++;
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*****************************************************************************
 Description  : kv批量操作中添加断连操作
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_010)
{
    void *tableLabel = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        ;
        kvInfo.value = &value1;

        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback1, &asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(end_num, asynData.totalNum);
    ASSERT_EQ(end_num, asynData.succNum);

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}

/*****************************************************************************
 Description  : kv批量操作中同时有成功和失败场景
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : qinjianhua wx620469
 Modification :
*****************************************************************************/
TEST_F(Async_V3_test, Async_V3_test_011)
{
    void *tableLabel = NULL;
    void *tableLabel2 = NULL;
    AsyncUserDataT asynData = {0};
    char kvTableName[] = "student";
    GmcKvDropTable(stmt, kvTableName);
    // create Tabel
    int ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, NULL, create_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // batch set
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value1;

        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 异步delete  、删除不存在的数据
    char key_delete[1024];
    for (int32_t i = 0; i < end_num; i++) {
        sprintf(key_delete, "zhangsan%d", i + end_num);
        kvInfo.key = key_delete;
        kvInfo.keyLen = strlen(key_delete);
        kvInfo.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt_async, key_delete, strlen(key_delete), NULL, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asynData.status);
    ASSERT_EQ(2 * end_num, asynData.totalNum);
    ASSERT_EQ(2 * end_num, asynData.succNum);

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asynData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asynData.status);
}
