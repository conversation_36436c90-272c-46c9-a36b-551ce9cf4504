/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 推理加速1020版本特性2：支持基于倒排类向量索引支持高性能ANN查询    功能用例范围1-96个  共97个
 * Author:
 * Create: 2024-10-19
 */
#include "t_rd_vectorDB.h"
using namespace std;

const size_t TRAIN_NUM = 28353;
const size_t QUERY_NUM = 30;
const size_t CENTER_NUM = 500;
const int DIM = 8192;
static float *g_trainDataSet = NULL;
static float *g_queryDataSet = NULL;
static float *g_centerDataSet = NULL;
static const char *g_ori_labelName = "QuantTable_ori";
static const char *g_ori_quant_labelName = "QuantTable_ori_quant";
static const char *g_quant_labelName = "QuantTable_quant";
const char *insertFile = "${TEST_HOME}/data/10m_dataset/10comb_8192_split_1.fbin";
const char *qureyFile = "${TEST_HOME}/data/10m_dataset/10comb_query_split_1.fbin";
const char *centerFile = "${TEST_HOME}/data/10m_dataset/cluster_centers_500_split_1.fbin";
InsertMsgs *ins = NULL;
IvfIndexMsgs *idx = NULL;
LoadCenterMsgs *lod = NULL;
GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
char *common_quant_lableJson = NULL;
char *common_ori_quant_lableJson = NULL;
FileInfo file_info_128d = {
    .dataFile = "${TEST_HOME}/data/128k_dataset/req0_bin_128d/req0_l10_k.bin",
    .queryFile = "${TEST_HOME}/data/128k_dataset/req0_bin_128d/req0_l10_q.bin",
    .centFile = "${TEST_HOME}/data/128k_dataset/req0_bin_128d/128d_500cluster.fbin",
    .trainNum = 4 * 1024,
    .queryNum = 30,
    .centerNum = 500,
    .dim = 128
};
float *lod_data_128d = NULL;
float *ins_data_128d = NULL;
float *idx_data_128d = NULL;
DataInit data_init_128d;
MsgsPrep msgsPrep;
void msgInit(){
    //Init Insert Message
    ins = (InsertMsgs *)malloc(sizeof(InsertMsgs));
    ins->stmt = stmt;
    ins->conn = conn;
    ins->data = g_trainDataSet;
    ins->start = 0;
    ins->end = TRAIN_NUM;
    ins->dim = DIM;
    ins->statusCode = 0;
    ins->latency = 0;
    ins->printInfo = false;
    //Init Query Message
    idx = (IvfIndexMsgs *)malloc(sizeof(IvfIndexMsgs));
    idx->stmt = stmt;
    idx->conn = conn;
    idx->data = g_queryDataSet;
    idx->dataNum = QUERY_NUM; 
    idx->indexId = 1; 
    idx->dim = DIM;
    idx->topK = 100;
    idx->statusCode = 0;
    idx->latency = 0;
    idx->printInfo = false;
    //Init Center Point Message
    lod = (LoadCenterMsgs *)malloc(sizeof(LoadCenterMsgs));
    lod->stmt = stmt;
    lod->conn = conn;
    lod->loadData = g_centerDataSet;
    lod->start = 0;
    lod->end = CENTER_NUM;
    lod->dim = DIM;
    lod->statusCode = 0;
    lod->latency = 0;
    lod->printInfo = false;
}
void freeMem(){
    //Free Memory
    free(ins);
    free(idx);
    free(lod);
}

class Vector_vlivf_index : public ::testing::Test {
protected:
    static void SetUpTestCase() 
    {
        LLAEnvInit();
        StartServer(); 
        LLAReadVector(&g_trainDataSet, insertFile, DIM, TRAIN_NUM, 0);
        uint32_t num = LLAReadVector(&g_queryDataSet, qureyFile, DIM, QUERY_NUM, 0);
        LLAReadVector(&g_centerDataSet, centerFile, DIM, CENTER_NUM, 0);
    }
    static void TearDownTestCase()
    {   
        free(g_trainDataSet);
        free(g_queryDataSet);
        free(g_centerDataSet);
        LLAEnvUninit();
        system("$TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }
    void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        AW_CHECK_LOG_BEGIN();
        int ret = LLAConnect(&conn, &stmt);
        ASSERT_EQ(GMERR_OK, ret);
        data_init_128d.Init(file_info_128d, &ins_data_128d, &idx_data_128d, &lod_data_128d);
        msgInit();
        
    }
    void TearDown()
    {   
        printf("\n======================TEST:END========================\n");
        freeMem();
        data_init_128d.Release();
        AW_CHECK_LOG_END();
        int ret = LLADisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
};
// DML示例
TEST_F(Vector_vlivf_index, vlivf_index_test_xx1){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    char *tmp_table_json = NULL;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &tmp_table_json);
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(tmp_table_json);
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，仅存量化编码，metric为ip，source为F1，filed为F1，索引filelds为["F1"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_001){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为300，仅存量化编码，metric为ip，source为F1，filed为F1，索引filelds为["F1"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_002){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[300]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，仅存量化编码，metric为l2，source为F1，filed为F1，索引filelds为["F1"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_003){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为300，仅存量化编码，metric为l2，source为F1，filed为F1，索引filelds为["F1"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_004){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[300]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，仅存量化编码，metric为cosine，source为F1，filed为F1，索引filelds为["F1"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_005){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为300，仅存量化编码，metric为cosine，source为F1，filed为F1，索引filelds为["F1"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_006){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[300]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为200，存储原向量+量化编码，metric为ip，source为F1，filed为F2，索引filelds为["F2"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_007){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema7.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(tmp_table_json);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F2\"]", ARRAY_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为200，存储原向量+量化编码，metric为l2，source为F1，filed为F2，索引filelds为["F2"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_008){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema7.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(tmp_table_json);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F2\"]", ARRAY_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为200，存储原向量+量化编码，metric为cosine，source为F1，filed为F2，索引filelds为["F2"]，预期创建成功
TEST_F(Vector_vlivf_index, VectorDB_002_009){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema7.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(tmp_table_json);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F2\"]", ARRAY_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，ood_threshold阈值为2000，metic为ip，scan_ratio为0.1，candidata_ratio为1.5，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_010){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "2000.0", REAL_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为128，ood_threshold阈值为5000，metic为l2，scan_ratio为0.1，candidata_ratio为2，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_011){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为200，ood_threshold阈值为6000，metic为cosine，scan_ratio为0.2，candidata_ratio为2，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_012){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}


//索引类型为vlivf，簇中心ncentroids值为360，ood_threshold阈值为8000，metic为cosine，scan_ratio为0.15，candidata_ratio为1.5，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_013){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[360]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "8000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为420，ood_threshold阈值为3600，metic为ip，scan_ratio为0.15，candidata_ratio为1.5，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_014){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[420]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "3600.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为500，ood_threshold阈值为10000，metic为ip，scan_ratio为0.15，candidata_ratio为3，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_015){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[500]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "10000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "3.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，metic维ip，scan_ratio为0.1，field字段的量化类型为大写LVQ，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_016){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "LVQ", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，metic维ip，scan_ratio为0.1，field字段的量化类型为“1”，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_017){
    AW_FUN_Log(LOG_INFO,"start");
    char *common_quant_lableJson;
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(common_quant_lableJson);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "1", STR_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，ood_threshold阈值为5000，设置F0\F1\F2三个字段，F2 type为fixed，F1 type为byte，仅存量化编码，source为F1，filed为F1，索引filelds为["F1"]，预期创建失败
TEST_F(Vector_vlivf_index, VectorDB_002_018){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema6.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    free(tmp_table_json);
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，ood_threshold阈值为5000，增加字段F2，type为fixed，source为F1，filed为F2，索引filelds为两层['F1","F1"]，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_019){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema3.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(tmp_table_json);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\",\"F1\"]", ARRAY_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_PROGRAM_LIMIT_EXCEEDED);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//索引类型为vlivf，簇中心ncentroids值为100，ood_threshold阈值为5000，设置F0\F1\F2三个字段，type为fixed，存储原向量+量化编码，source为F1，filed为F2，索引filelds为三层["F0","F1","F2"]，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_020){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema3.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(tmp_table_json);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F0\",\"F1\",\"F2\"]", ARRAY_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}


// ncentroids值为100，ood_threshold阈值为5000.0，索引字段为子表F3，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_021){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema1.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    //init schema
    OprTableJson oprTable(tmp_table_json);
    //modify schema
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F3\"]", ARRAY_TYPE);
    //print schema
    oprTable.PrintfTableJson();
    //get schema
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_PROPERTY);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009002, Illegal property name when schema get propId by name, property name is F3");
    EXPECT_TRUE(retOfLog); 
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为100，ood_threshold阈值为5000.0，索引字段为F3，type为string，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_022){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema2.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, The field F3 is vlivf index field, its datatype should be fixed.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    free(tmp_table_json);
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为128，ood_threshold阈值为5000.0，metic为l2，索引类型设置为none，，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_023){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "index", "{\"type\":\"none\"}", OBJ_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_NAME);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009001, Node name is illegal");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为128，ood_threshold阈值为5000.0，metic为l2，索引类型设置为localhash，，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_024){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "index", "{\"type\":\"localhash\"}", OBJ_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009003, The quantization field F1 is not support index.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为128，ood_threshold阈值为5000.0，metic为l2，索引类型设置为ivfflat，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_025){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "index", "{\"type\":\"ivfflat\"}", OBJ_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, Type of index vlivf is ivfflat");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为ip，量化编码metic为l2，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_026){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为ip，量化编码metic为cosine，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_027){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为l2，量化编码metic为ip，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_028){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为l2，量化编码metic为cosine，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_029){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为cosine，量化编码metic为ip，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_030){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为cosine，量化编码metic为l2，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_031){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引metic为small，量化编码metic为ip，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_032){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "small", STR_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 200;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，原向量+量化编码，F2对应size为2235，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_033){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "2235", INT32_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 100;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，原向量+量化编码，F2对应size为1268，type为bool，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_034){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "1268", INT32_TYPE);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "type", "bool", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, The type of F2 is incorrect.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F1，F2，原向量+量化编码，F2对应type为string，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_035){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "type", "string", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\",\"F2\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, The field F2 is auto quantization field, its datatype");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F0，F1，原向量+量化编码，F2对应type为string，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_036){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\",\"F0\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, The field F0 is vlivf index field, its datatype should be fixed");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为1，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_037){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[1]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, VLIVF index level 0 cluster size 1 is out of range");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为1234，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_038){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[1234]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为9999，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_039){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[9999]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为10000，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_040){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10000]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_041){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_ARRAY_SUBSCRIPT_ERROR);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1004001, The size of array ncentroids should be in the range : 1 to 1.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[100,200]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_042){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100,200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_ARRAY_SUBSCRIPT_ERROR);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[9]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_043){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[9]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[100001]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_044){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100001]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[0]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_045){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[0]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[128.5]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_046){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128.5]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ncentroids值为[-1]，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_047){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[-1]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为0.06，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_048){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    // oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "16388",REAL_TYPE);     // Segmentation fault
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.06", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为0.36，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_049){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.36", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为0.5，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_050){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.52", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为0.65，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_051){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.65", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为0.99，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_052){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.99", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为1，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_053){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "1.0", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为0（低于下限），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_054){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.0", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009003, VLIVF index scan ratio 0.000000 is out of range");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为1.1（超过上限），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_055){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "1.1", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，查询桶比例scan_ratio为-1（无效值），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_056){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "-1.0", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为1.8，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_057){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.8", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为2.25，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_058){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.25", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为3.2，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_059){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "3.2", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为5.0，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_060){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "10240", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "3.2", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为9.9，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_061){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "9.9", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为10.0，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_062){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "10.0", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为0.5（低于下限），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_063){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "0.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009003, VLIVF index candidate ratio 0.500000 is out of range");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为10.5（大于上限），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_064){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "10.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，选集limit倍数candidate_ratio为-1（无效值），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_065){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "-1.0", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，ood_threshold向量阈值为-1（无效值），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_066){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "-1.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "-1.0", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_TABLE_DEFINITION);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，ood_threshold向量阈值为0（无效值），预期失败(实际结果成功)
TEST_F(Vector_vlivf_index, VectorDB_002_067){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "0.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，ood_threshold向量阈值为整形6000，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_068){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "6000", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, ood_threshold value type is mismatch");
    EXPECT_TRUE(retOfLog);

    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，ood_threshold向量阈值为字符串（无效值），预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_069){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "min", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，索引属性metric为空值，量化编码属性为l2，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_070){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，索引属性metric为无效值，量化编码属性为l2，优先使用量化编码metric，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_071){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "LARGE", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，索引属性metric为ip，量化编码属性为small，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_072){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "small", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, The field value of \"metric\" is not supported.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// （日志打印有问题）索引类型为vlivf，索引属性metric为l2，量化编码属性为枚举10，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_073){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "10", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，索引属性metric为cosine，量化编码属性为manhattan，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_074){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "manhattan", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    GmcDropVertexLabel(stmt,g_quant_labelName);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，簇中心ncentroids值为100，metic维ip，scan_ratio为0.1，candidata_ratio为0.05，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_075){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "10240", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "field", "F1", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "source", "F2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "2000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，簇中心ncentroids值为128，ood_threshold阈值为5000，metic维l2，scan_ratio为0.05，candidata_ratio为1.8，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_076){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "10240", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "field", "F1", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "source", "F2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.05", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.8", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，簇中心ncentroids值为300，ood_threshold阈值为10000，metic维cosine，scan_ratio为0.2，candidata_ratio为2，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_077){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "10240", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "field", "F1", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "source", "F2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[300]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "10000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.2", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，在非量化编码字段上创建索引，簇中心360，metric为l2，导入字段配置原向量字段，会自动导入量化编码字段，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_078){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "field", "F1", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "source", "F2", STR_TYPE);
    oprTable.ModifyTableObj(FIELDS_OBJ, 2, "size", "10240", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[360]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "10000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.15", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引字段为F1，仅量化编码，先进行ivf索引查询，然后通过filter过滤查询对应的索引字段，预期查询失败 
TEST_F(Vector_vlivf_index, VectorDB_002_079){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 100;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //ivf index query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    //filter scan
    FilterParaConfig filterCig;
    filterCig = {
    .fieldIdConfig = 1,
    .nodeNameConfig = NULL,
    .compOpConfig = GMC_OP_IP_VECTOR_SIMILARITY,
    .valueConfig = NULL,
    .valueLenConfig = DIM * sizeof(float)};
    QueryMsgs *queCur =NULL;
    queCur = (QueryMsgs *)malloc(sizeof(QueryMsgs));
    queCur->stmt = stmt;
    queCur->conn = conn;
    queCur->qLabelName= g_quant_labelName;
    queCur->data = g_queryDataSet;
    queCur->dataNum = 30; 
    queCur->dim = DIM;
    queCur->topK = 100;
    queCur->statusCode = 0;
    queCur->latency = 0;
    queCur->filterPara = filterCig;
    AnnQueryData(queCur);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, queCur->statusCode);
    free(queCur);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    bool retOfLog = AW_CHECK_LOG_EXIST(CLIENT, 1, "GMERR-1003000, Vlivf index does not support set key range.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，原向量+量化编码，ivf索引设置唯一索引，预期创表失败  
TEST_F(Vector_vlivf_index, VectorDB_002_vlilvf_quant_080){
    AW_FUN_Log(LOG_INFO,"start");
    char *tmp_table_json;
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema3.gmjson", &tmp_table_json);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(tmp_table_json);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F2\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    tmp_table_json = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, tmp_table_json, NULL);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1003000, Vlivf index not support unique index");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F1，F2，candidate_ratio为1.25，原向量+量化编码，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_081){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\",\"F2\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.25", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_PROGRAM_LIMIT_EXCEEDED);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1011000, The properties total size 36912 of");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，candidate_ratio为1.75，量化metric为l2，ncentroids为300，原向量+量化编码，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_082){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[300]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.75", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 300;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，candidate_ratio为2.25，量化metric为cosine，ncentroids为500，原向量+量化编码，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_083){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[500]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "2.25", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 500;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，在长度为12800字段上创建索引，簇中心100，metric为ip，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_084){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "12800", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.15", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，在长度为16384字段上创建索引，簇中心200，metric为ip，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_085){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "16384", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.15", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，在长度为16388字段上创建索引，簇中心200，metric为l2，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_086){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "16388", INT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.15", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_PROGRAM_LIMIT_EXCEEDED);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1011000, The properties total size 16388 of index vlivf exceeds the upper limit 16384.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，ivf索引字段为主键F0，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_087){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F0\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.15", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1009007, The field F0 is vlivf index field, its datatype should be fixed.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引类型为vlivf，非量化编码fixed类型，索引filelds为两段["F1","F1"]，预期创建失败
TEST_F(Vector_vlivf_index, VectorDB_002_088){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "ncodebits", "8", UINT32_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "fields", "[\"F1\",\"F1\"]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "16", UINT32_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.15", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1003000, Label QuantTable_quant vlivf index only supports building on one field.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 在量化编码字段上创建索引，索引类型为vlivf，簇中心100，导入100中心后插入200数据，执行查询操作，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_089){

    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "ip", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
 
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 100;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 200;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 在量化编码字段上创建索引，索引类型为vlivf，簇中心128，导入128中心后插入512数据，执行查询操作，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_090){

    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[128]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
 
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 128;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 512;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //query
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 在非量化编码字段上创建索引，vlivf索引导入500中心后插入数据，预期失败（8192维情况，原向量编码创建索引，8192*4>16384,无法建表核插入数据)
TEST_F(Vector_vlivf_index, VectorDB_002_091){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "field", "F1", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "source", "F2", STR_TYPE);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "16384", UINT32_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "cosine", STR_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[500]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "scan_ratio", "0.1", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "candidate_ratio", "1.5", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();

    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_PROGRAM_LIMIT_EXCEEDED);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "GMERR-1011000, The properties total size 32768 of index vlivf exceeds the upper limit 16384.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
 
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//在量化编码字段上创建索引，索引类型为vlivf，簇中心400，导入400中心后插入160K数据，执行查询操作，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_092){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[400]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 400;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 160;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    IvfIndexMsgs *idxCur = idx;
    idxCur->idxLabelName= g_quant_labelName;
    IvfIndexQuery(idxCur);
    EXPECT_EQ(GMERR_OK, idxCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//在量化编码字段上创建索引，索引类型为vlivf，簇中心100，导入10中心后插入200数据，预期失败
TEST_F(Vector_vlivf_index, VectorDB_002_093){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 10;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 200;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, insCur->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 索引字段为F2，ncentroids为200，导入128中心后，插入200条数据，原向量+量化编码，预期插入失败
TEST_F(Vector_vlivf_index, VectorDB_002_094){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 128;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 200;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, insCur->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，导入100中心后，插入1000条数据，GmcSetKeyRange范围查询，预期查询失败
TEST_F(Vector_vlivf_index, VectorDB_002_095){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 100;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 1000;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //set key range scan
    GmcResetStmt(stmt);
    ret = GmcPrepareStmtByLabelName(stmt, g_quant_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t prefetchNum = 200;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetScanLimit(stmt, 100);
    EXPECT_EQ(GMERR_OK, ret);
    float *l_vec = g_queryDataSet + 1 * DIM;
    float *r_vec = g_queryDataSet + 10 * DIM;
    uint32_t arrLen = 1;
    const char *keyName = "vlivf";
    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_FIXED;
    leftKeyProps[0].value = l_vec;
    leftKeyProps[0].size = sizeof(float) * DIM;
    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_FIXED;
    rightKeyProps[0].value = r_vec;
    rightKeyProps[0].size = sizeof(float) * DIM;
    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = &leftKeyProps[0];
    items_sc[0].rValue = &rightKeyProps[0];
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_ASC;
    ret = GmcSetIndexKeyId(stmt, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items_sc, arrLen);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    bool retOfLog = AW_CHECK_LOG_EXIST(CLIENT, 1, "GMERR-1003000, Vlivf index does not support set key range.");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    free(leftKeyProps);
    free(rightKeyProps);
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，导入100中心后，插入1000条数据，GmcSetKeyRangeStructure范围查询，预期查询失败
TEST_F(Vector_vlivf_index, VectorDB_002_096){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.ModifyTableObj(CONFIG_OBJ, 1, "metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    LoadCenterMsgs *lodCur = lod;
    lodCur->end = 100;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    //insert data 
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 1000;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    //GmcSetKeyRangeStructure测试
    uint32_t arrLen = 1;
    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    GmcRangeKeySeriT rangeKeyInfo;
    rangeKeyInfo.keyId = 1;
    rangeKeyInfo.leftKeySeri = NULL;
    rangeKeyInfo.rightKeySeri = NULL;
    ret = GmcSetKeyRangeStructure(stmt, items, arrLen, &rangeKeyInfo);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引创建在非量化编码的fixed字段，同时不设置vlivf索引的metric参数，预期创表报错
TEST_F(Vector_vlivf_index, VectorDB_002_098){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema4.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[100]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_UNDEFINE_COLUMN);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 创建多个vlivf索引，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_099){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_index_10m_schema5.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 对vlivf索引的字段进行update预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_100){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    LoadCenterMsgs *lodCur = lod;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 1000;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    int indexValue = 0;
    UpdateIndexConfig IndexConfig{
    .indexName = "PK",
    .indexId = 0,
    .indexValue = &indexValue
    };
    UpdateMsgs *updCur;
    updCur = (UpdateMsgs *)malloc(sizeof(UpdateMsgs));
    updCur->stmt = stmt;
    updCur->labelName = g_quant_labelName;
    updCur->fieldName = "F1";
    updCur->value = g_trainDataSet;
    updCur->dataType = GMC_DATATYPE_FIXED;
    updCur->dataLen = 32768;
    updCur->updateIndexPara = IndexConfig;
    updCur->statusCode = 0;
    UpdateData(updCur);
    ASSERT_EQ(GMERR_OK, updCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    free(updCur);
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ood_metric值为l2，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_101){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_metric", "l2", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ood_metric值为ip，预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_102){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_metric", "ip", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// ood_metric值为cosine，预期创表失败
TEST_F(Vector_vlivf_index, VectorDB_002_103){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_metric", "cosine", STR_TYPE);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 1, "The field value of ood_metric is not supported");
    EXPECT_TRUE(retOfLog);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 补充用例85 表结构和Config配置文件都配置量化信息时，在量化信息正确的前提下，以配置文件为主，配置文件bits改为5预期创表成功
TEST_F(Vector_vlivf_index, VectorDB_002_104){
    AW_FUN_Log(LOG_INFO,"start");
    const char *configJson = R"({
                                "auto_vector_quantization":
                                [
                                    {"type":"lvq", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":5}
                                ]
                                })";
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 补充用例86 表结构和Config配置文件都配置量化信息时，任意一方信息错误，预期创建失败
TEST_F(Vector_vlivf_index, VectorDB_002_105){
    AW_FUN_Log(LOG_INFO,"start");
    const char *configJson = R"({
                                "auto_vector_quantization":
                                [
                                    {"type":"LVQ", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":4}
                                ]
                                })";
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, configJson);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_DATATYPE_MISMATCH);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 补充用例87 表结构配置量化信息，Config配置文件中不配置时，使用表结构量化信息
TEST_F(Vector_vlivf_index, VectorDB_002_106){
    AW_FUN_Log(LOG_INFO,"start");
    const char *configJson = R"({
                                "max_record_count":10000
                                })";
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema.gmjson", &common_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.PrintfTableJson();
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    LoadCenterMsgs *lodCur = lod;
    lodCur->lLabelName= g_quant_labelName;
    BatchLoadCenter(lodCur);
    EXPECT_EQ(GMERR_OK, lodCur->statusCode);
    InsertMsgs *insCur = ins;
    insCur->iLabelName = g_quant_labelName;
    insCur->end = 4 * 1024;
    BatchInsertData(insCur);
    EXPECT_EQ(GMERR_OK, insCur->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
//在量化编码字段创建vlivf索引，簇中心400，插入4K的128维数据，执行查询，预期成功
TEST_F(Vector_vlivf_index, VectorDB_002_107){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[400]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "512", INT32_TYPE);
    common_ori_quant_lableJson = oprTable.GetTableJson();
    oprTable.PrintfTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    msgsPrep.Init(stmt, conn, data_init_128d, file_info_128d);
    msgsPrep.lod->end = 400;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK, msgsPrep.ins->statusCode);
    IvfIndexQuery(msgsPrep.idx);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    msgsPrep.Release();
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
// 索引字段为F2，ncentroids为200，导入128中心后，插入200条128维数据，原向量+量化编码，预期插入失败
TEST_F(Vector_vlivf_index, VectorDB_002_108){
    AW_FUN_Log(LOG_INFO,"start");
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_ori_quant_lableJson);
    GmcDropVertexLabel(stmt, g_quant_labelName);
    OprTableJson oprTable(common_ori_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[200]", ARRAY_TYPE);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ood_threshold", "5000.0", REAL_TYPE);
    oprTable.ModifyTableObj(FIELDS_OBJ, 1, "size", "512", INT32_TYPE);
    oprTable.PrintfTableJson();
    common_ori_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_ori_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //  load center point
    msgsPrep.Init(stmt, conn, data_init_128d, file_info_128d);
    msgsPrep.lod->end = 128;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    //insert data 
    msgsPrep.ins->end = 200;
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, msgsPrep.ins->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    if (ret == GMERR_OK){
        ret = GmcDropVertexLabel(stmt,g_quant_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
    msgsPrep.Release();
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}
