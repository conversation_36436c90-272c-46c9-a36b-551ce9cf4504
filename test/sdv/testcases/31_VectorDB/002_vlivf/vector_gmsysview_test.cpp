/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 推理加速1120版本特性1：向量查询相关可维可测能力提升    
 * Author:qinhaoran
 * Create: 2024-10-19
 */
#include "t_rd_vectorDB.h"
#include <chrono>
#include <math.h>
#include "db_lvq.h"
using namespace std;
using namespace std::chrono;

FileInfo fileInfo = {
    .dataFile = "${TEST_HOME}/data/10m_dataset/10comb_8192_split_1.fbin",
    .queryFile = "${TEST_HOME}/data/10m_dataset/10comb_query_split_1.fbin",
    .centFile = "${TEST_HOME}/data/10m_dataset/cluster_centers_500_split_1.fbin",
    .trainNum = 4096,
    .queryNum = 30,
    .centerNum = 500,
    .dim = 8192
};
DataInit dataInit;
MsgsPrep msgsPrep;
float *g_trainDataSet = NULL;
float *g_queryDataSet = NULL;
float *g_centerDataSet = NULL;
const char *g_ori_quant_labelName = "QuantTable_ori_quant";
const char *g_quant_labelName = "QuantTable_quant";
const char *g_quant_labelName1 = "QuantTable_quant1";
const char *g_quant_labelName2 = "Quant_Table2";
const char *g_quant_labelName_2 = "QuantTable_quant_2";
const char *g_quant_labelName3 = "Quant_table2";
GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
char *common_quant_lableJson = NULL;
char *common_quant_lableJson1 = NULL;
char *common_quant_lableJson2 = NULL;
char *common_quant_lableJson3 = NULL;
char *common_ori_quant_lableJson = NULL;
char result00[256],result01[256],result02[256],result03[256],result04[256],result05[256],result06[256],result07[256],result08[256],
result09[256], result010[256],result011[256],result012[256],result013[256],result014[256],result10[256],result11[256],result12[256],
result13[256],result14[256],result15[256],result16[256],result17[256],result18[256],result19[256],result110[256],result111[256],result112[256],
result113[256],result114[256],result_1[256];
float i = 0.2, j = 0.3;
float *param = &j;
float para = 2.22;
float param1 = 3.33;
int ret_1;
float param2 = 4.44;
float query[4] = {5.0f, 1.0f, 6.0f, 2.0f};
float prop[4] = {4.0f, 2.0f, 5.0f, 6.0f};
struct ApiPara {
    GmcStmtT *stmtP;
    const char *propName;
    float *qryData;
    float disResult;
    GmcVectorMetricE metric;
    uint32_t qryNum;
};
float dotProduct(float *vector1, float *vector2, unsigned int dimension){
    float sum = 0.0;
    for (int i = 0; i < dimension; ++i){
        sum = sum + vector1[i] * vector2[i];
    }
    return sum;
}
float computel2distance(float *vector1, float *vector2, int dimension){
    float sum = 0.0;
    for (int i = 0; i < dimension; ++i){
        sum = sum + (vector1[i] - vector2[i]) * (vector1[i] - vector2[i]);
    }
    return sum;
}
float computeMagnitude(float *vector, int dimension){
    float sum = 0.0;
    for (int i = 0; i < dimension; ++i){
        sum = sum + vector[i] * vector[i];
    }
    return sqrt(sum);
}
float computecosinedistance(float *vector1, float *vector2){
    float dotresult = dotProduct(vector1, vector2, 4);
    float mag1 = computeMagnitude(vector1, 4);
    float mag2 = computeMagnitude(vector2, 4);
    float cosinesimilarity = dotresult / (mag1 * mag2);
    float cosinedistance = 1.0 - cosinesimilarity;
    return cosinedistance;
}
void QryLoadCenter(void *arg)
{   
    LoadCenterMsgs *loadCen = (LoadCenterMsgs *)arg;
    GmcBatchT *batch = nullptr;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(loadCen->conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(loadCen->stmt, loadCen->lLabelName, GMC_OPERATION_LOAD_INDEX);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        printf("[GmcPrepareStmtByLabelName] LoadCenter failed\n");
        loadCen->statusCode = ret;
        return ;
    }
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    uint32_t tmpNum = loadCen->end - loadCen->start;
    auto startExecute = steady_clock::now();
    for (uint32_t i = loadCen->start; i < loadCen->end && ret == GMERR_OK; ++i) {
        ret = GmcSetVertexProperty(
        loadCen->stmt, "F1", GMC_DATATYPE_FIXED, prop, 16);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            printf("[GmcSetVertexProperty] LoadCenter failed\n");
            loadCen->statusCode = ret;
            return;
        }
        ret = GmcBatchAddDML(batch, loadCen->stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            loadCen->statusCode = ret;
            return;
        }
        if ((i - loadCen->start) % 50 == 0) {
            if (i != loadCen->start) {
                tmpNum = tmpNum - 50;
            } else if (i == loadCen->start) {
                tmpNum = tmpNum - 1;
            }
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                printf("[GmcExecute] LoadCenter failed\n");
                loadCen->statusCode = ret;
                return;
            }
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchReset(batch);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (tmpNum > 0) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret != GMERR_OK) {
        loadCen->statusCode = ret;
        return;
    }
    loadCen->latency = duration_cast<microseconds>(steady_clock::now() - startExecute).count();
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    loadCen->latency = loadCen->latency / 1000 / (loadCen->end - loadCen->start);
    if (loadCen->printInfo) {
       printf("loadCen success\n");
    }
}
// Inserting Data in Batches
void InsertQryData(void *arg)
{   
    InsertMsgs *ins = (InsertMsgs *)arg;
    GmcBatchT *batch = nullptr;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(ins->conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(ins->stmt, ins->iLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        ins->statusCode = ret;
        printf("[GmcPrepareStmtByLabelName] insert failed\n");
        return ;
    }
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    uint32_t tmpNum = ins->end - ins->start;
    ins->start = 0;
    ins->end = 1;
    auto startExecute = steady_clock::now();
    for (uint32_t i = ins->start; i < ins->end; ++i) {
        ret = GmcSetVertexProperty(ins->stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(ins->stmt, "F1", GMC_DATATYPE_FIXED, prop, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, ins->stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            ins->statusCode = ret;
            printf("[GmcBatchAddDML] insert failed\n");
            return;
        }
    }
    if (tmpNum > 0) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        ins->statusCode = ret;
        return;
    }
    ins->latency = duration_cast<microseconds>(steady_clock::now() - startExecute).count();
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ins->latency = ins->latency / 1000 / (ins->end - ins->start);
    if (ins->printInfo) {
        printf("insert success\n");
    }
}
void IvfQueryForGmsysview(void *arg, ApiPara *apiPara, bool apiTest = false){
    IvfIndexMsgs *idx = (IvfIndexMsgs *)arg;
    uint32_t prefetchNum = idx->topK * 2;
    int ret;
    auto startExecute = steady_clock::now();
    for (uint32_t i = 0; i < idx->dataNum; i++) {
        GmcResetStmt(idx->stmt);
        ret = GmcPrepareStmtByLabelName(idx->stmt, idx->idxLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStmtAttr(idx->stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetStmtAttr] query failed\n");
            return;
        }
        ret = GmcSetScanLimit(idx->stmt, idx->topK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(idx->stmt, idx->indexId);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyId] ivfIndex query failed\n");
            return;
        }
        ret = GmcSetIndexKeyValue(idx->stmt, 0, GMC_DATATYPE_FIXED, idx->data + i * idx->dim, sizeof(float) * idx->dim);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyValue] ivfIndex query failed\n");
            return;
        }
        ret = GmcExecute(idx->stmt);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcExecute] ivfIndex query failed\n");
            return;
        }
        bool eof;
        uint32_t cnt = 0;
        while (true) {
            ret = GmcFetch(idx->stmt, &eof);
            if (ret != GMERR_OK) {
                idx->statusCode = ret;
                printf("[GmcFetch] ivfIndex query failed\n");
                return;
            }
            if (eof) {
                break;
            }
            
            if (apiTest) {
                if (apiPara->qryData == param){
                    apiPara->qryData = idx->data + i * idx->dim;
                }
                if (apiPara->disResult == para){
                    ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, NULL, apiPara->metric);
                    if (ret != GMERR_OK) {
                    idx->statusCode = ret;
                    printf("[GmcGetVecSimilarity] dis is null\n");
                    return;
                    }
                }
                if (apiPara->propName =="F2" && apiPara->qryData == query && apiPara->metric == GMC_VECTOR_METRIC_IP){
                    unsigned int sizevalue = 0;
                    bool isNull = 0;
                    ret = GmcGetVertexPropertySizeByName(idx->stmt, "F2", &sizevalue);
                    EXPECT_EQ(GMERR_OK, ret);
                    float *quevalue = (float *)malloc(sizevalue);
                    ret = GmcGetVertexPropertyByName(idx->stmt, "F2", quevalue, sizevalue, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    float *querydata = apiPara->qryData;
                    float result = dotProduct(quevalue, querydata, 4);
                    ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                    EXPECT_EQ(apiPara->disResult, 1 - result);
                    free(quevalue);
                }else if (apiPara->propName =="F2" && apiPara->qryData == query && apiPara->metric == GMC_VECTOR_METRIC_L2){
                    unsigned int sizevalue = 0;
                    bool isNull = 0;
                    ret = GmcGetVertexPropertySizeByName(idx->stmt, "F2", &sizevalue);
                    EXPECT_EQ(GMERR_OK, ret);
                    float *quevalue = (float *)malloc(sizevalue);
                    ret = GmcGetVertexPropertyByName(idx->stmt, "F2", quevalue, sizevalue, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    float *querydata = apiPara->qryData;
                    float result = computel2distance(quevalue, querydata, 4);
                    ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                    EXPECT_EQ(apiPara->disResult, result);
                    free(quevalue);
                }else if (apiPara->propName =="F2" && apiPara->qryData == query && apiPara->metric == GMC_VECTOR_METRIC_COSINE){
                    unsigned int sizevalue = 0;
                    bool isNull = 0;
                    ret = GmcGetVertexPropertySizeByName(idx->stmt, "F2", &sizevalue);
                    EXPECT_EQ(GMERR_OK, ret);
                    float *quevalue = (float *)malloc(sizevalue);
                    ret = GmcGetVertexPropertyByName(idx->stmt, "F2", quevalue, sizevalue, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    float *querydata = apiPara->qryData;
                    float result = computecosinedistance(quevalue, querydata);
                    ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                    EXPECT_EQ(apiPara->disResult, result);
                    free(quevalue);
                }else{
                    ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                }
                if (ret == GMERR_OK) {
                    (apiPara->qryNum)++;
                }
            }
            if (ret != GMERR_OK) {
                idx->statusCode = ret;
                printf("[GmcGetVecSimilarity] ivfIndex query failed\n");
                return;
            }
            cnt++;
        }
        if(cnt != idx->topK) {
            idx->statusCode = cnt ;
            printf("[GmcFetch] fetch data is abnormal\n");
            return;
        }
    }
    idx->latency = duration_cast<microseconds>(steady_clock::now() - startExecute).count();
    idx->latency = idx->latency/ 1000 / idx->dataNum;
    printf("ivfIndex query success\n");
}

void IvfQueryForGmsysview1(void *arg, ApiPara *apiPara, bool apiTest = false){
    IvfIndexMsgs *idx = (IvfIndexMsgs *)arg;
    uint32_t prefetchNum = idx->topK * 2;
    int ret;
    auto startExecute = steady_clock::now();
    for (uint32_t i = 0; i < idx->dataNum; i++) {
        GmcResetStmt(idx->stmt);
        ret = GmcPrepareStmtByLabelName(idx->stmt, idx->idxLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStmtAttr(idx->stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetStmtAttr] query failed\n");
            return;
        }
        ret = GmcSetScanLimit(idx->stmt, idx->topK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(idx->stmt);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcExecute] ivfIndex query failed\n");
            return;
        }
        bool eof;
        uint32_t cnt = 0;
        while (true) {
            ret = GmcFetch(idx->stmt, &eof);
            if (ret != GMERR_OK) {
                idx->statusCode = ret;
                printf("[GmcFetch] ivfIndex query failed\n");
                return;
            }
            if (eof) {
                break;
            }
            
            if (apiTest) {
                if (apiPara->qryData == param){
                    apiPara->qryData = idx->data + i * idx->dim;
                }
                if (apiPara->disResult == para){
                    ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, NULL, apiPara->metric);
                    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
                    if (ret != GMERR_OK) {
                    idx->statusCode = ret;
                    printf("[GmcGetVecSimilarity] ivfIndex query failed\n");
                    return;
                    }
                }

                ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                if (ret == GMERR_OK) {
                    (apiPara->qryNum)++;
                }
            }
            if (ret != GMERR_OK) {
                idx->statusCode = ret;
                printf("[GmcGetVecSimilarity] ivfIndex query failed\n");
                return;
            }
            cnt++;
        }
        if(cnt != idx->topK) {
            idx->statusCode = cnt ;
            printf("[GmcFetch] fetch data is abnormal\n");
            return;
        }
    }
    idx->latency = duration_cast<microseconds>(steady_clock::now() - startExecute).count();
    idx->latency = idx->latency/ 1000 / idx->dataNum;
    printf("ivfIndex query success\n");
}
uint8_t* IvfQueryForQryVec(void *arg){
    IvfIndexMsgs *idx = (IvfIndexMsgs *)arg;
    idx->dataNum = 1;
    uint32_t prefetchNum = 1;
    int ret;
    auto startExecute = steady_clock::now();
    for (uint32_t i = 0; i < idx->dataNum; i++) {
        GmcResetStmt(idx->stmt);
        ret = GmcPrepareStmtByLabelName(idx->stmt, idx->idxLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStmtAttr(idx->stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetStmtAttr] query failed\n");
        }
        ret = GmcSetScanLimit(idx->stmt, idx->topK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(idx->stmt, idx->indexId);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyId] ivfIndex query failed\n");
        }
        ret = GmcSetIndexKeyValue(idx->stmt, 0, GMC_DATATYPE_FIXED, prop, 16);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyValue] ivfIndex query failed\n");
        }
        ret = GmcExecute(idx->stmt);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcExecute] ivfIndex query failed\n");
        }
        bool eof;
        uint32_t cnt = 0;
        ret = GmcFetch(idx->stmt, &eof);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcFetch] ivfIndex query failed\n");
        }
        unsigned int sizevalue = 0;
        bool isNull = 0;
        ret = GmcGetVertexPropertySizeByName(idx->stmt, "F1", &sizevalue);
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t *originvec = (uint8_t *)malloc(sizevalue);
        ret = GmcGetVertexPropertyByName(idx->stmt, "F1", originvec, sizevalue, &isNull);
        EXPECT_EQ(GMERR_OK, ret);  
        return originvec;
    }
}
void IvfQueryForGetSimilarity(uint8_t *vector1, uint8_t *vector2, void *arg, ApiPara *apiPara, bool apiTest = false){
    IvfIndexMsgs *idx = (IvfIndexMsgs *)arg;
    idx->dataNum = 1;
    uint32_t prefetchNum = 1;
    int ret;
    auto startExecute = steady_clock::now();
    for (uint32_t i = 0; i < idx->dataNum; i++) {
        GmcResetStmt(idx->stmt);
        ret = GmcPrepareStmtByLabelName(idx->stmt, idx->idxLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStmtAttr(idx->stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetStmtAttr] query failed\n");
            return;
        }
        ret = GmcSetScanLimit(idx->stmt, idx->topK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(idx->stmt, idx->indexId);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyId] ivfIndex query failed\n");
            return;
        }
        ret = GmcSetIndexKeyValue(idx->stmt, 0, GMC_DATATYPE_FIXED, prop, 16);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyValue] ivfIndex query failed\n");
            return;
        }
        ret = GmcExecute(idx->stmt);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcExecute] ivfIndex query failed\n");
            return;
        }
        bool eof;
        uint32_t cnt = 0;
        ret = GmcFetch(idx->stmt, &eof);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcFetch] ivfIndex query failed\n");
        }   
        if (apiTest) {
            if (apiPara->qryData == param){
                apiPara->qryData = prop;
            }
            if (apiPara->metric == GMC_VECTOR_METRIC_IP){
                float dotresut = DbLVQComputeDistance(DbLVQCodeAttach(vector1), DbLVQCodeAttach(vector2), DB_VECTOR_METRIC_IP);
                ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                EXPECT_EQ(dotresut, apiPara->disResult);
            }else if (apiPara->metric == GMC_VECTOR_METRIC_L2){
                float dotresut = DbLVQComputeDistance(DbLVQCodeAttach(vector1), DbLVQCodeAttach(vector2), DB_VECTOR_METRIC_L2);
                ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                EXPECT_EQ(dotresut, apiPara->disResult);
            }else{
                float dotresut = DbLVQComputeDistance(DbLVQCodeAttach(vector1), DbLVQCodeAttach(vector2), DB_VECTOR_METRIC_COSINE);
                ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                EXPECT_EQ(dotresut, apiPara->disResult);
            }          
        }
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcGetVecSimilarity] ivfIndex query failed\n");
            return;
        }
    }
    idx->latency = duration_cast<microseconds>(steady_clock::now() - startExecute).count();
    idx->latency = idx->latency/ 1000 / idx->dataNum;
    printf("ivfIndex query success\n");
}
void BeforeFetchIvfQueryForGmsysview(void *arg, ApiPara *apiPara, bool apiTest = false){
    IvfIndexMsgs *idx = (IvfIndexMsgs *)arg;
    uint32_t prefetchNum = idx->topK * 2;
    int ret;
    auto startExecute = steady_clock::now();
    for (uint32_t i = 0; i < idx->dataNum; i++) {
        GmcResetStmt(idx->stmt);
        ret = GmcPrepareStmtByLabelName(idx->stmt, idx->idxLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStmtAttr(idx->stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prefetchNum, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetStmtAttr] query failed\n");
            return;
        }
        ret = GmcSetScanLimit(idx->stmt, idx->topK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(idx->stmt, idx->indexId);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyId] ivfIndex query failed\n");
            return;
        }
        ret = GmcSetIndexKeyValue(idx->stmt, 0, GMC_DATATYPE_FIXED, idx->data + i * idx->dim, sizeof(float) * idx->dim);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcSetIndexKeyValue] ivfIndex query failed\n");
            return;
        }
        ret = GmcExecute(idx->stmt);
        if (ret != GMERR_OK) {
            idx->statusCode = ret;
            printf("[GmcExecute] ivfIndex query failed\n");
            return;
        }
        bool eof;
        uint32_t cnt = 0;
        while (true) {
            if (apiTest) {
                if (apiPara->qryData == &param1){
                    apiPara->qryData = idx->data + i * idx->dim;
                }
                ret = GmcGetVecSimilarity(apiPara->stmtP, apiPara->propName, apiPara->qryData, &(apiPara->disResult), apiPara->metric);
                ret_1 = ret;
                return;
            }
            ret = GmcFetch(idx->stmt, &eof);
            if (ret != GMERR_OK) {
                idx->statusCode = ret;
                printf("[GmcFetch] ivfIndex query failed\n");
                return;
            }
            if (eof) {
                break;
            }
            if (ret != GMERR_OK) {
                idx->statusCode = ret;
                printf("[GmcGetVecSimilarity] ivfIndex query failed\n");
                return;
            }
            cnt++;
        }
        if(cnt != idx->topK) {
            idx->statusCode = cnt ;
            printf("[GmcFetch] fetch data is abnormal\n");
            return;
        }
    }
    idx->latency = duration_cast<microseconds>(steady_clock::now() - startExecute).count();
    idx->latency = idx->latency/ 1000 / idx->dataNum;
    printf("ivfIndex query success\n");
}

class Vector_Gmsysview_Test : public ::testing::Test {
protected:
    static void SetUpTestCase() 
    {
        LLAEnvInit();
        StartServer();
        dataInit.Init(fileInfo, &g_trainDataSet, &g_queryDataSet, &g_centerDataSet);
    }
    static void TearDownTestCase()
    {   
        dataInit.Release();
        LLAEnvUninit();
        ShutdownServer();
    }
    void SetUp()
    {
        printf("\n======================TEST:BEGIN======================\n");
        int ret = LLAConnect(&conn, &stmt);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
        msgsPrep.printInfo = true;
        msgsPrep.Init(stmt, conn, dataInit, fileInfo);
        
    }
    void TearDown()
    {   
        printf("\n======================TEST:END========================\n");
        msgsPrep.Release();
        AW_CHECK_LOG_END();
        int ret = LLADisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
};
// 创建非ivf索引的表结构，查询视图，预期不显示任何内容
TEST_F(Vector_Gmsysview_Test, VectorDB_003_001){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema_none.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result00);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result01);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result02);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result03);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result04);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result05);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result06);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result07);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result08);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result09);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result010);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result011);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result012);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result013);
    EXPECT_NE(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result014);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

// 创建一个ivf索引表,查询视图,预期显示ivf索引信息正确
TEST_F(Vector_Gmsysview_Test, VectorDB_003_002){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result00);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result02);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result03);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result04);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result05);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result06);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result07);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result08);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result09);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result010);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result011);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result012);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result013);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result014);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//创建表名不同的两个ivf表，查询视图,预期两个表格除表名和索引ID不同以外，其他信息大小一致
TEST_F(Vector_Gmsysview_Test, VectorDB_003_003){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson1);
    OprTableJson oprTable(common_quant_lableJson1);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson1 = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result00);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result02);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result03);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result04);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result05);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result06);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result07);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result08);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result09);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result010);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result011);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result012);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result013);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result014);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson2);
    OprTableJson oprTable2(common_quant_lableJson2);
    oprTable2.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    oprTable2.ModifyTableObj(NODE_OBJ, 1, "node", "Quant_table2", STR_TYPE);
    common_quant_lableJson2 = oprTable2.GetTableJson();
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result_1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(22, atoi(result_1));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result_1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(220, atoi(result_1));
    char cmd2[512];
    char result2[256];
    (void)snprintf(cmd2, sizeof(cmd2), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd2, "INDEX_NAME: vlivf", result10);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(*result00,*result10);
    ret = GetValueOfCmd(cmd2, "INDEX_ID", result11);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(*result01,*result11);
    ret = GetValueOfCmd(cmd2, "LABEL_NAME: Quant_table2", result12);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "PAGE_COUNT", result13);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(*result03,*result13);
    ret = GetValueOfCmd(cmd2, "TOTAL_MEMORY_SIZE", result14);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(*result04,*result14);
    ret = GetValueOfCmd(cmd2, "USED_MEM_SIZE", result15);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(*result05,*result15);
    ret = GetValueOfCmd(cmd2, "CENTROIDS_LIST", result16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "PAGE_NUM", result17);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "SCAN_NUM", result18);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "LEVEL", result19);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CLUSTER_PATH", result110);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "VECTOR_NUM", result111);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "IS_OOD", result112);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CLUSTER_RADIUS", result113);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "DIS_DISTRIBUTION", result114);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson1);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName3);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    AW_FUN_Log(LOG_INFO,"end");    
}


//创建一个ivf索引表和一个非ivf索引表,预期显示ivf索引相关信息，非ivf索引表不显示任何ivf索引相关信息
TEST_F(Vector_Gmsysview_Test, VectorDB_003_004){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson1);
    OprTableJson oprTable(common_quant_lableJson1);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson1 = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema_none.gmjson", &common_quant_lableJson2);
    OprTableJson oprTable2(common_quant_lableJson2);
    oprTable2.ModifyTableObj(NODE_OBJ, 1, "node", "Quant_Table2", STR_TYPE);
    common_quant_lableJson2 = oprTable2.GetTableJson();
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char cmd2[512];
    char result2[256];
    (void)snprintf(cmd2, sizeof(cmd2), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd2, "PAGE_COUNT", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson1);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName2);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    AW_FUN_Log(LOG_INFO,"end");    
}

//创建ivf表、插入1024条数据、查询视图、删表，再创建ivf表、插入1024条数据、查询视图，预期两次相同的操作视图内容不会存在差异、同时插入数据前后簇半径由0变为有值
TEST_F(Vector_Gmsysview_Test, VectorDB_003_005){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    msgsPrep.ins->end = 1024;
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK, msgsPrep.ins->statusCode);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson2);
    OprTableJson oprTable2(common_quant_lableJson2);
    oprTable2.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson2 = oprTable2.GetTableJson();
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    msgsPrep.ins->end = 1024;
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK, msgsPrep.ins->statusCode);
    char result2[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result2));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result2));
    char cmd2[512];
    (void)snprintf(cmd2, sizeof(cmd2), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd2, "INDEX_NAME: vlivf", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "INDEX_ID", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "LABEL_NAME: QuantTable_quant", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "PAGE_COUNT", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "TOTAL_MEMORY_SIZE", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "USED_MEM_SIZE", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CENTROIDS_LIST", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "PAGE_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "SCAN_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "LEVEL", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CLUSTER_PATH", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "VECTOR_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "IS_OOD", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CLUSTER_RADIUS", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "DIS_DISTRIBUTION", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    AW_FUN_Log(LOG_INFO,"end");
}

//创建ivf表、插入1024条数据、查询视图、删表，再创建ivf表、插入2048条数据、查询视图，预期两次操作后的视图内容，后者的内存占比变大，总VECTOR_NUM变大等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_006){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    msgsPrep.ins->end = 1024;
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK, msgsPrep.ins->statusCode);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable2(common_quant_lableJson);
    oprTable2.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable2.GetTableJson();
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    msgsPrep.ins->start = 1024;
    msgsPrep.ins->end = 3 * 1024;
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK, msgsPrep.ins->statusCode);
    char result2[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result2));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result2));
    char cmd2[512];
    (void)snprintf(cmd2, sizeof(cmd2), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd2, "INDEX_NAME: vlivf", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "INDEX_ID", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "LABEL_NAME: QuantTable_quant", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "PAGE_COUNT", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "TOTAL_MEMORY_SIZE", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "USED_MEM_SIZE", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CENTROIDS_LIST", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "PAGE_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "SCAN_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "LEVEL", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CLUSTER_PATH", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "VECTOR_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "IS_OOD", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "CLUSTER_RADIUS", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd2, "DIS_DISTRIBUTION", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    oprTable2.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//创建两个ivf索引表，均导入簇中心，其中一个插入数据后，查询视图,预期两个表结构内存相关参数和簇信息由一致变为不同
TEST_F(Vector_Gmsysview_Test, VectorDB_003_007){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson1);
    OprTableJson oprTable(common_quant_lableJson1);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson1 = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson1);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson2);
    OprTableJson oprTable2(common_quant_lableJson2);
    oprTable2.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson2 = oprTable2.GetTableJson();
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    char result2[256];
    char cmd2[512];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result2));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result2);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result2));
    (void)snprintf(cmd2, sizeof(cmd2), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    AW_FUN_Log(LOG_INFO,"end");
}

// 簇中心数量为10时视图中参数变化,预期视图内簇的个数为11
TEST_F(Vector_Gmsysview_Test, VectorDB_003_008){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 10;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    IvfQueryForGmsysview(msgsPrep.idx, NULL);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(11, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep 'IS_OOD: 1' |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM|awk -F': ' '       \
        /^[ \t]*PAGE_NUM/ {sum += $2} END {print sum}'", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(283, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(110, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

// 簇中心数量为10时视图中参数变化,预期视图内簇的个数为500、簇的分布为按簇含有向量数量的降序分布、且不同簇的DIS_TANGE范围不一样，且范围对应的值之和为总的插入向量数
TEST_F(Vector_Gmsysview_Test, VectorDB_003_009){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[500]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep.lod->end = 500;
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    IvfQueryForGmsysview(msgsPrep.idx, NULL);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(501, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep 'IS_OOD: 1' |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM|awk -F': ' '       \
        /^[ \t]*PAGE_NUM/ {sum += $2} END {print sum}'", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(726, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5010, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    system("gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//簇中心数量为1001时视图中参数变化,预期视图内簇的个数仅显示为1000个
TEST_F(Vector_Gmsysview_Test, VectorDB_003_010){
    AW_FUN_Log(LOG_INFO,"start");
    DataInit dataInit1;
    dataInit1.Init(fileInfo, &g_trainDataSet, &g_queryDataSet, &g_trainDataSet);
    MsgsPrep msgsPrep1;
    msgsPrep1.Init(stmt, conn, dataInit1, fileInfo);
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[1001]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    msgsPrep1.lod->end = 1001;
    BatchLoadCenter(msgsPrep1.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep1.lod->statusCode);
    BatchInsertData(msgsPrep1.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep1.ins->statusCode);
    IvfQueryForGmsysview(msgsPrep1.idx, NULL);
    EXPECT_EQ(GMERR_OK, msgsPrep1.idx->statusCode);
    char result[256];
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1000, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep 'IS_OOD: 1' |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep PAGE_NUM|awk -F': ' '       \
        /^[ \t]*PAGE_NUM/ {sum += $2} END {print sum}'", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1225, atoi(result));
    ret = GetResultOfCmd("gmsysview -q V\\$STORAGE_VL_IVF_STAT|grep RANGE |wc -l", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10000, atoi(result));
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$STORAGE_VL_IVF_STAT");
    ret = GetValueOfCmd(cmd, "INDEX_NAME: vlivf", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "INDEX_ID", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LABEL_NAME: QuantTable_quant", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_COUNT", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "TOTAL_MEMORY_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "USED_MEM_SIZE", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CENTROIDS_LIST", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "PAGE_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "SCAN_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "LEVEL", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_PATH", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "VECTOR_NUM", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "IS_OOD", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "CLUSTER_RADIUS", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "DIS_DISTRIBUTION", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    dataInit1.Release();
    msgsPrep1.Release();
    oprTable.Release();
    AW_FUN_Log(LOG_INFO,"end");
}

//创建带有ivf索引全量信息的表结构、查询视图,预期显示全部簇相关的全部静态信息
TEST_F(Vector_Gmsysview_Test, VectorDB_003_011){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char result[256];
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = GetValueOfCmd(cmd, "centroids_level_num", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "metric", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "ood_metric", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "ood_threshold", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "scan_ratio", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "candidate_ratio", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");  
}


//创建带有ivf索引缺省信息的表结构、查询视图,预期显示全部簇相关的全部静态信息
TEST_F(Vector_Gmsysview_Test, VectorDB_003_012){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema_default.gmjson", &common_quant_lableJson);
    OprTableJson oprTable(common_quant_lableJson);
    oprTable.ModifyTableObj(KEYS_OBJ, 1, "ncentroids", "[10]", ARRAY_TYPE);
    common_quant_lableJson = oprTable.GetTableJson();
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char result[256];
    char cmd[512];
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = GetValueOfCmd(cmd, "centroids_level_num", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "metric", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "ood_metric", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "ood_threshold", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GetValueOfCmd(cmd, "candidate_ratio", result);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");  
}

//GmcFetch前调用向量相似度计算接口,预期查询失败，会报错
TEST_F(Vector_Gmsysview_Test, VectorDB_003_013){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", &param1, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    BeforeFetchIvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(GMERR_NO_DATA, ret_1);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_NO_DATA);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//stmt为NULL其他参数正确,创表、导入、插入、查询时调用向量相似度计算接口,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_014){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {NULL, "F1", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, msgsPrep.idx->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_NULL_VALUE_NOT_ALLOWED);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//dis为NULL其他参数正确,创表、导入、插入、查询时调用向量相似度计算接口,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_015){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, para, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, msgsPrep.idx->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_NULL_VALUE_NOT_ALLOWED);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//propName为NULL其他参数正确,创表、导入、插入、查询时调用向量相似度计算接口,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_016){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, NULL, param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, msgsPrep.idx->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_NULL_VALUE_NOT_ALLOWED);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//propName为“F10”无效字段，其他参数正确,创表、导入、插入、查询时调用向量相似度计算接口,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_017){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F10", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, msgsPrep.idx->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_INVALID_PROPERTY);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//metric为无效字段，其他参数正确,创表、导入、插入、查询时调用向量相似度计算接口,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_018){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_BUTT, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, msgsPrep.idx->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//propName为仅量化编码字段、传入向量维度等于8192维，预期查询成功，dis为有效值，所有查询结果大于等于0
TEST_F(Vector_Gmsysview_Test, VectorDB_003_019){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

// propName为仅量化编码字段、传入向量为NULL,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_020){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", NULL, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, msgsPrep.idx->statusCode);
    AW_ADD_ERRNUM_WHITE_LIST(1,GMERR_NULL_VALUE_NOT_ALLOWED);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//propName为仅量化编码字段、传入向量为8192维、值全部是1,预期查询成功、dis为有效值，所有查询结果大于等于0
TEST_F(Vector_Gmsysview_Test, VectorDB_003_021){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    float query_8192d_1[8192] = {1.0};
    ApiPara apiPara = {stmt, "F1", query_8192d_1, 0.0f, GMC_VECTOR_METRIC_COSINE, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO,"end");
}

//存储原向量+量化编码,propName为原向量字段、传入向量为等于8192维,metric为ip，预期查询成功、dis为有效值
TEST_F(Vector_Gmsysview_Test, VectorDB_003_022){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//存储原向量+量化编码,propName为量化编码字段、传入向量为8192维,预期查询成功、dis为有效值
TEST_F(Vector_Gmsysview_Test, VectorDB_003_023){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F2", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//propName为量化编码字段、传入向量维度等于8192维,创表、导入、插入、查询时调用向量相似度计算接口、metric为cosine,预期查询失败，报错访问越界，dis为无效值
TEST_F(Vector_Gmsysview_Test, VectorDB_003_024){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_COSINE, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

void CallBackSetPropertyFixed(GmcStmtT *stmtC, void *num){
    char tmp[16] = {'a'};
    int ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, &tmp, 16);
    ASSERT_EQ(GMERR_OK, ret);
}

//propName为非量化相关字段,propName为非量化的fixed字段、传入向量为等于8192维,预期查询成功、dis为有效值
TEST_F(Vector_Gmsysview_Test, VectorDB_003_025){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema1.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins, CallBackSetPropertyFixed);
    system("gmsysview record  QuantTable_quant 2 ");
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F3", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

void CallBackSetPropertyint64(GmcStmtT *stmtC, void *num){
    int64_t tmpData = 1;
    int ret = GmcSetVertexProperty(stmtC, "F4", GMC_DATATYPE_INT64, &tmpData, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

//propName为非量化相关字段,propName为非量化的int字段、传入向量为等于8192维,预期查询失败
TEST_F(Vector_Gmsysview_Test, VectorDB_003_026){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_ori_quant_schema2.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins, CallBackSetPropertyint64);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    system("gmsysview record  QuantTable_quant 2 ");
    ApiPara apiPara = {stmt, "F4", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATATYPE_MISMATCH);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

//非ivf索引查询时，调用GmcVecSimilarityGet接口,预期查询成功
TEST_F(Vector_Gmsysview_Test, VectorDB_003_027){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_gmsysview_quant_schema_none.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchInsertData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview1(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}

void CallBackSetPropertyFixed1(GmcStmtT *stmtC, void *num){
    float tmp[4] = {1.0f, 4.0f, 2.0f, 3.0f};
    int ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_FIXED, &tmp, 16);
    ASSERT_EQ(GMERR_OK, ret);
}
//propName为非量化fixed字段，metric为IP，传入向量为小于8192维,预期查询成功、dis值与两向量内积值相等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_028){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema1.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins, CallBackSetPropertyFixed1);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F2", query, 0.0f, GMC_VECTOR_METRIC_IP, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_LE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}
//propName为非量化fixed字段、metric为L2,传入向量为小于8192维,预期查询成功、dis值与两向量L2距离相等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_029){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema1.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins, CallBackSetPropertyFixed1);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F2", query, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}
//propName为非量化fixed字段、metric为cosine,传入向量为小于8192维,预期查询成功、dis为有效值，且与两向量cosine距离相等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_030){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema1.gmjson", &common_quant_lableJson);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    BatchLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    BatchInsertData(msgsPrep.ins, CallBackSetPropertyFixed1);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F2", query, 0.0f, GMC_VECTOR_METRIC_COSINE, 0};
    IvfQueryForGmsysview(msgsPrep.idx, &apiPara, true);
    ASSERT_GE(apiPara.disResult, GMERR_OK);
    EXPECT_EQ(msgsPrep.idx->dataNum * msgsPrep.idx->topK, apiPara.qryNum);
    EXPECT_EQ(GMERR_OK, msgsPrep.idx->statusCode);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}
//propName为量化fixed字段，metric为IP，传入向量小于8192维,预期查询成功、dis值与两向量内积值相等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_031){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson1);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    uint8_t *quevalue = IvfQueryForQryVec(msgsPrep.idx);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson1);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson2);
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    uint8_t *qryvalue = IvfQueryForQryVec(msgsPrep.idx);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson);
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_IP, 0};
    IvfQueryForGetSimilarity(quevalue, qryvalue, msgsPrep.idx, &apiPara, true);
    free(quevalue);
    free(qryvalue);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}
//propName为量化fixed字段，metric为IP，传入向量小于8192维,预期查询成功、dis值与两向量L2距离相等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_032){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson1);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    uint8_t *quevalue = IvfQueryForQryVec(msgsPrep.idx);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson1);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson2);
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    uint8_t *qryvalue = IvfQueryForQryVec(msgsPrep.idx);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson);
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_L2, 0};
    IvfQueryForGetSimilarity(quevalue, qryvalue, msgsPrep.idx, &apiPara, true);
    free(quevalue);
    free(qryvalue);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}
//propName为量化fixed字段，metric为IP，传入向量小于8192维,预期查询成功、dis与两向量cosine距离相等
TEST_F(Vector_Gmsysview_Test, VectorDB_003_033){
    AW_FUN_Log(LOG_INFO,"start");
    GmcDropVertexLabel(stmt,g_quant_labelName);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson1);
    int ret = GmcCreateVertexLabel(stmt, common_quant_lableJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    uint8_t *quevalue = IvfQueryForQryVec(msgsPrep.idx);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson1);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson2);
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    uint8_t *qryvalue = IvfQueryForQryVec(msgsPrep.idx);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson2);
    LLAReadJsonFile("./schema_file/vlivf_10m_quant_schema2.gmjson", &common_quant_lableJson);
    ret = GmcCreateVertexLabel(stmt, common_quant_lableJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    QryLoadCenter(msgsPrep.lod);
    EXPECT_EQ(GMERR_OK, msgsPrep.lod->statusCode);
    InsertQryData(msgsPrep.ins);
    EXPECT_EQ(GMERR_OK,  msgsPrep.ins->statusCode);
    ApiPara apiPara = {stmt, "F1", param, 0.0f, GMC_VECTOR_METRIC_COSINE, 0};
    IvfQueryForGetSimilarity(quevalue, qryvalue, msgsPrep.idx, &apiPara, true);
    free(quevalue);
    free(qryvalue);
    ret = GmcDropVertexLabel(stmt,g_quant_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(common_quant_lableJson);
    AW_FUN_Log(LOG_INFO,"end");
}
