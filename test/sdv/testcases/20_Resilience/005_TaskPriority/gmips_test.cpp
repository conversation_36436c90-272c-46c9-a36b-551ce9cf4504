#include "tools.h"
#include "SceneTest.h"

class gmips_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
public:
    virtual void SetUp();
    virtual void TearDown();
};

void gmips_test::SetUpTestCase()
{}

void gmips_test::TearDownTestCase()
{}

void gmips_test::SetUp()
{}

void gmips_test::TearDown()
{
    GmcDetachAllShmSeg();
}

using namespace std;

// 001. 非鉴权模式，设置服务端时间间隔内任务量
TEST_F(gmips_test, Resilience_005_001_001)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 5 -unitTime 5");
    system(g_command);
    ret = executeCommand(g_command, "ret = 1003000.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 002. 鉴权模式，设置服务端时间间隔内任务量小于等于0, 1
TEST_F(gmips_test, Resilience_005_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 0 -unitTime 5");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload -1 -unitTime 5");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 1 -unitTime 5");
    ret = executeCommand(g_command, "set Workload successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 003. 鉴权模式，设置服务端时间间隔内任务量为21000001
TEST_F(gmips_test, Resilience_005_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 21000001 -unitTime 5");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 004. 鉴权模式，设置服务端时间间隔内任务量为21000000
TEST_F(gmips_test, Resilience_005_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 21000000 -unitTime 5");
    ret = executeCommand(g_command, "[gmips] set Workload successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 005. 鉴权模式，设置服务端时间间隔小于0
TEST_F(gmips_test, Resilience_005_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 5 -unitTime -1");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 006. 鉴权模式，设置服务端时间间隔等于0
TEST_F(gmips_test, Resilience_005_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 5 -unitTime 0");
    ret = executeCommand(g_command, "[gmips] set Workload successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 007. 鉴权模式，设置服务端时间间隔为3601
TEST_F(gmips_test, Resilience_005_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 5 -unitTime 3601");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 008. 鉴权模式，设置服务端时间间隔等于3600
TEST_F(gmips_test, Resilience_005_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -workload 5 -unitTime 3600");
    ret = executeCommand(g_command, "[gmips] set Workload successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 009. 非鉴权模式，设置用户权重
TEST_F(gmips_test, Resilience_005_001_009)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName root -processName gmips_test -userWeight 10");
    ret = executeCommand(g_command, "ret = 1003000.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 010. 鉴权模式，设置用户权重小于等于0, 1
TEST_F(gmips_test, Resilience_005_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName root -processName gmips_test -userWeight -1");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName root -processName gmips_test -userWeight 0");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName root -processName gmips_test -userWeight 1");
    system(g_command);
    ret = executeCommand(g_command, "set user weight successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 011. 鉴权模式，设置用户权重为101, 100
TEST_F(gmips_test, Resilience_005_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName root -processName gmips_test -userWeight 101");
    ret = executeCommand(g_command, "[gmips] Init args unsucc. ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName root -processName gmips_test -userWeight 100");
    ret = executeCommand(g_command, "set user weight successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 012. 鉴权模式，为不存在的用户设置权重
TEST_F(gmips_test, Resilience_005_001_012)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "gmips -userName unexist_username -processName gmips_test -userWeight 5");
    ret = executeCommand(g_command, "ret = 1009010");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 013. 非鉴权模式，设置连接权重
TEST_F(gmips_test, Resilience_005_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建连并设置连接权重
    uint32_t weight = 50;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 014. 鉴权模式，设置连接权重大于用户权重
TEST_F(gmips_test, Resilience_005_001_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmips -userName root -processName gmips_test -userWeight 10");

    // 同步
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建连并设置连接权重
    uint32_t weight = 20;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    // 建连并设置连接权重
    uint32_t weight_async = 20;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 015. 鉴权模式，设置连接权重小于/等于0
TEST_F(gmips_test, Resilience_005_001_015)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmips -userName root -processName gmips_test -userWeight 10");

    // 同步
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建连并设置连接权重
    uint32_t weight = -1;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    // 建连并设置连接权重
    uint32_t weight_async = -1;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 016. 鉴权模式，设置连接权重等于用户权重
TEST_F(gmips_test, Resilience_005_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmips -userName root -processName gmips_test -userWeight 10");

    // 同步
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建连并设置连接权重
    uint32_t weight = 10;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    // 建连并设置连接权重
    uint32_t weight_async = 10;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 017. 鉴权模式，设置连接权重（正常），断连，重连，重新设置连接权重
TEST_F(gmips_test, Resilience_005_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmips -userName root -processName gmips_test -userWeight 10");

    // 同步
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建连并设置连接权重
    uint32_t weight = 10;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新建连并设置连接权重
    uint32_t new_weight = 5;
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &new_weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    // 建连并设置连接权重
    uint32_t weight_async = 10;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新建连并设置连接权重
    uint32_t new_weight_async = 5;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &new_weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 018. 鉴权模式，设置连接权重（异常），重新设置连接权重（正常）
TEST_F(gmips_test, Resilience_005_001_018)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmips -userName root -processName gmips_test -userWeight 10");

    // 同步
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 建连并设置连接权重
    uint32_t weight = 20;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新建连并设置权重
    uint32_t correct_weight = 5;
    ret = TestConnSetRequestWeight(&conn, &stmt, user_name, &correct_weight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    // 建连并设置连接权重
    uint32_t weight_async = 20;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新建连并设置权重
    uint32_t correct_weight_async = 20;
    ret = TestConnSetRequestWeight(&conn_async, &stmt_async, user_name, &correct_weight, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 019. 鉴权模式，为同一用户名设置多个连接，每个连接的连接权重超过/等于/小于（>0）用户权重
TEST_F(gmips_test, Resilience_005_001_019)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy_gmips.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmips -userName root -processName gmips_test -userWeight 10");

    // 同步
    GmcConnT *conn_1 = NULL;
    GmcConnT *conn_2 = NULL;
    GmcConnT *conn_3 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtT *stmt_2 = NULL;
    GmcStmtT *stmt_3 = NULL;
    // 建连并设置连接权重
    uint32_t weight_1 = 5;
    uint32_t weight_2 = 10;
    uint32_t weight_3 = 15;
    char user_name[MAX_CMD_SIZE];
    snprintf(user_name, MAX_CMD_SIZE, "root");
    ret = TestConnSetRequestWeight(&conn_1, &stmt_1, user_name, &weight_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestConnSetRequestWeight(&conn_2, &stmt_2, user_name, &weight_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestConnSetRequestWeight(&conn_3, &stmt_3, user_name, &weight_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步
    GmcConnT *conn_async_1 = NULL;
    GmcConnT *conn_async_2 = NULL;
    GmcConnT *conn_async_3 = NULL;
    GmcStmtT *stmt_async_1 = NULL;
    GmcStmtT *stmt_async_2 = NULL;
    GmcStmtT *stmt_async_3 = NULL;
    // 建连并设置连接权重
    uint32_t weight_async_1 = 5;
    uint32_t weight_async_2 = 10;
    uint32_t weight_async_3 = 15;
    ret = TestConnSetRequestWeight(&conn_async_1, &stmt_async_1, user_name, &weight_1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestConnSetRequestWeight(&conn_async_2, &stmt_async_2, user_name, &weight_2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestConnSetRequestWeight(&conn_async_3, &stmt_async_3, user_name, &weight_3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 断连
    ret = testGmcDisconnect(conn_async_1, stmt_async_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_async_2, stmt_async_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    ret = system("gmrule -c remove_allowlist -f ./allowlist/allowlist_gmips.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
