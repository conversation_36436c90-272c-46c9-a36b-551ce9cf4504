[{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "ip4forward", "fields": [{"name": "vr_id", "type": "uint32", "nullable": false, "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "nullable": false, "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "nullable": false, "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "nullable": false, "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "nullable": true, "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "nullable": true, "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "nullable": true, "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "nullable": true, "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "nullable": true, "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "nullable": true, "comment": "path标记"}, {"name": "flags", "type": "uint32", "nullable": true, "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8", "nullable": true}, {"name": "status_normal_prio", "type": "uint8", "nullable": true}, {"name": "errcode_high_prio", "type": "uint8", "nullable": true}, {"name": "errcode_normal_prio", "type": "uint8", "nullable": true, "default": 1}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "nullable": true, "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "nullable": true, "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32", "nullable": true}, {"name": "table_smooth_id", "type": "uint32", "nullable": true}, {"name": "app_obj_id", "type": "uint64", "nullable": true}, {"name": "app_version", "type": "uint32", "nullable": true}, {"name": "trace", "type": "uint64", "nullable": true}, {"name": "route_flags", "type": "uint16", "nullable": true, "comment": "路由标记"}, {"name": "reserved", "type": "uint16", "nullable": true, "comment": "预留"}, {"name": "test_str", "type": "string", "nullable": true, "size": 100}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}, "comment": "根据nhp_group_id + vrid索引"}, {"name": "localhash_key_2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["primary_label", "attribute_id"], "constraints": {"unique": true}}, {"name": "local_key", "index": {"type": "local"}, "node": "ip4forward", "fields": ["primary_label", "attribute_id"], "constraints": {"unique": true}, "comment": "根据nhp_group_id + vrid索引"}, {"name": "local_key_2", "index": {"type": "local"}, "node": "ip4forward", "fields": ["primary_label", "dest_ip_addr"], "constraints": {"unique": false}}]}]