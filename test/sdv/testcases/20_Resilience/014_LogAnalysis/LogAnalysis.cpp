/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :关闭用户连接功能场景测试
 Author       : wensiqi wx1060458
 Modification :
 Date         : 2022/09/09
**************************************************************************** */
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"
#include <string>

#define MAX_CMD_SIZE 1024
int ret;
char g_command[MAX_CMD_SIZE] = {0};
class LogAnalysis_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void LogAnalysis_test::SetUpTestCase()
{
}

void LogAnalysis_test::TearDownTestCase()
{
    
}

void LogAnalysis_test::SetUp()
{
    printf("===================gmips LogAnalysis_testCase begin===================\n");
}

void LogAnalysis_test::TearDown()
{
    printf("===================gmips LogAnalysis_testCase end===================\n");
}

/* *********************************************************************************
 Description  : 001.gmids打印目录
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s", Path);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 002.gmids打印全部日志
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s", Path);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 003.gmids按时间过滤日志
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s", Path, StDate, EtDate);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 004.gmids -st使用有效值
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s", Path, StDate);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 005.gmids -et使用有效值
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char EtDate[256] = "2022-10-13-10:00:00";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -et %s", Path, EtDate);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 006.gmids -st使用超过正常范围的有效值 -et使用超过正常范围的有效值
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-66:66:66";
    char EtDate[256] = "2022-10-14-66:66:66";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -et %s", Path, EtDate);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 007.gmids按关键字过滤
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char Match[256] = "ERROR";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -match %s", Path, Match);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 008.gmids按关键字反过滤
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char NoMatch[256] = "ERROR";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -nmatch %s", Path, NoMatch);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 009.gmids按特殊日志类型long筛选过滤
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char Special[256] = "long";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -sp %s", Path, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 010.gmids按特殊日志类型fold筛选过滤
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char Special[256] = "fold";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -sp %s", Path, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 011.gmids按特殊日志类型hung筛选过滤
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char Special[256] = "hung";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -sp %s", Path, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 012.gmids按特殊日志类型core筛选过滤
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char Special[256] = "hung";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -sp %s", Path, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 013.gmids统计目录
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -stats", Path);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 014.gmids统计文件日志
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -stats", Path);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 015.gmids各筛选条件混合使用-match val 
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Match[256] = "ERROR";
    char Special[256] = "long";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -stats", Path, StDate, 
    EtDate, Match);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 016.gmids各筛选条件混合使用-nmatch val
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char NoMatch[256] = "ERROR";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -nmatch %s -stats", Path, StDate, 
    EtDate, NoMatch);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 017.gmids各筛选条件混合使用-sp long
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Special[256] = "long";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -sp %s -stats", Path, StDate, 
    EtDate, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 018.gmids各筛选条件混合使用-sp fold
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Special[256] = "fold";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -sp %s -stats", Path, StDate, 
    EtDate, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 019.gmids各筛选条件混合使用-sp hung
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Special[256] = "hung";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -sp %s -stats", Path, StDate, 
    EtDate, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 020.gmids各筛选条件混合使用-sp core
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Special[256] = "core";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -sp %s -stats", Path, StDate, 
    EtDate, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 021.gmids各筛选条件混合使用-sp long hung
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Special[256] = "long hung";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -sp %s -stats", Path, StDate, 
    EtDate, Special);
    system(g_command);
    ret = executeCommand(g_command, "DB init option param unsucc, ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 022.gmids各筛选条件混合使用
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Match[256] = "GMERR";
    char NoMatch[256] = "Create";
    char Special[256] = "long";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -nmatch %s -sp %s -stats", 
    Path, StDate, EtDate, Match, NoMatch, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 023.修改配置项后生成的日志能正常匹配
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Match[256] = "GMERR";
    char NoMatch[256] = "Create";
    char Special[256] = "long";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -nmatch %s -sp %s -stats", 
    Path, StDate, EtDate, Match, NoMatch, Special);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

/* *********************************************************************************
 Description  : 024.匹配到关键字后修改日志文件中的关键字，再次匹配失败
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/test.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Match[256] = "Create";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -stats", 
    Path, StDate, EtDate, Match);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    system("sed -i 's/Create/create/g' test/log/test.log");
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -stats", 
    Path, StDate, EtDate, Match);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    system("sed -i 's/create/Create/g' test/log/test.log");
}

/* *********************************************************************************
 Description  : 025.验证审计日志所有选项的功能
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(LogAnalysis_test, Resilience_014_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char Path[256] = "test/log/audit.log";
    char StDate[256] = "2022-10-13-10:00:00";
    char EtDate[256] = "2022-10-13-10:00:00";
    char Match[256] = "AuditLogEnhance";
    char NoMatch[256] = "Disconnection";
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -nmatch %s", 
    Path, StDate, EtDate, Match, NoMatch);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "gmids -path %s -st %s -et %s -match %s -nmatch %s -stats", 
    Path, StDate, EtDate, Match, NoMatch);
    system(g_command);
    ret = executeCommand(g_command, "[gmids] query finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
