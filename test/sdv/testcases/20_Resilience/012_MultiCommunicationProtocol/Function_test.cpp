/*
多通信协议
--------------------------------  功能测试  --------------------------------
040 默认情况下进行主键直连读操作，预期成功
041 禁用用户A的直连读协议，进行主键读、全表扫描、二级索引等直连读操作，预期失败
042 禁用用户A的直连读协议，用户B 二级索引 直连读操作正常
043 禁用用户A的直连读协议，进行直连读操作，预期失败；启用用户A的直连读协议，进行直连读操作包括 主键读、二级索引、全表扫描，预期成功
044 用户A进行直连读，禁用后再开启直连读协议，再进行 主键读、二级索引、全表扫描 直连读，预期成功
045 禁用直连读协议，CS模式建连，预期成功
046 启用直连读协议，CS模式建连，预期成功
047 禁用超级用户的直连读协议，超级用户 主键读、二级索引、全表扫描 直连读失败
048 禁用超级用户的直连读协议，超级用户直连读失败；再启用超级用户的直连读协议，主键读、二级索引、全表扫描 直连读成功
049 禁用直连读协议，视图查询功能不受影响，record、count视图

Date ： 2022-10-24
Author : 唐广明
*/

#include "multi_communication_protocol.h"


class Function_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Function_test::SetUpTestCase()
{
    // 修改配置项，先停服务
    system("sh $TEST_HOME/tools/stop.sh");
    // 开启强鉴权模式
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Function_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void Function_test::SetUp()
{
    int ret;
    // 导入白名单
    const char *allow_list_file = "./configSchema/gmips_allowlist.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s", g_toolPath, allow_list_file,
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入系统权限
    const char *sys_policy_file = "./configSchema/system_privilege.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s", g_toolPath, sys_policy_file, g_connServer);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

void Function_test::TearDown()
{
    // 删除白名单
    int ret;
    const char *allow_list_file = "./configSchema/gmips_allowlist.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 增加超级用户
class Function_test02 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Function_test02::SetUpTestCase()
{
    // 修改配置项，先停服务
    system("sh $TEST_HOME/tools/stop.sh");
    // 开启强鉴权模式,同时增加Function_test为超级用户
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" \"DBA=root:gmrule;gmips;Function_test\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Function_test02::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void Function_test02::SetUp()
{
    int ret;
    // 导入白名单
    const char *allow_list_file = "./configSchema/gmips_allowlist.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s", g_toolPath, allow_list_file,
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入系统权限
    const char *sys_policy_file = "./configSchema/system_privilege.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s", g_toolPath, sys_policy_file, g_connServer);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}

void Function_test02::TearDown()
{
    // 删除白名单
    int ret;
    const char *allow_list_file = "./configSchema/gmips_allowlist.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 040 默认情况下进行主键、二级索引、全表扫描、条件过滤 直连读操作，预期成功
TEST_F(Function_test, Resilience_012_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    GmcConnOptionsT *connOptions;
    ret = GmcConnOptionsCreate(&connOptions);  // 覆盖率补充connOptions创建
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetBigObjThreshold(connOptions, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    testCreateLabel(g_stmt);

    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);

    // 主键读
    TestGmcScanByPk(g_stmt);

    // 全表扫描
    TestGmcFullScan(g_stmt);

    // 二级索引
    TestGmcScanByLocalHash(g_stmt);
    TestGmcScanByHashCluster(g_stmt);
    TestGmcScanByLocal(g_stmt);
    TestGmcScanByLpm4(g_stmt);

    // 删表
    testDropLabel(g_stmt, g_vertexName);

    // 断连
    ret = testGmcDisconnect(g_conn ,g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041 禁用用户A的直连读协议，进行主键读、全表扫描、二级索引等直连读操作，预期失败
TEST_F(Function_test, Resilience_012_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    testCreateLabel(g_stmt);
    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);

    // 禁用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读，预期失败、无权限
    TestGmcScanByPkFailure(g_stmt);

    // 全表扫描失败、无权限
    TestGmcFullScanFailure(g_stmt);

    // 二级索引、无权限
    TestGmcScanByLocalHashFailure(g_stmt);
    
    // 删表
    testDropLabel(g_stmt, g_vertexName);
    // 断连
    ret = testGmcDisconnect(g_conn ,g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class Function_test03 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Function_test03::SetUpTestCase()
{
    // 修改配置项，先停服务
    system("sh $TEST_HOME/tools/start.sh");
    // 开启强鉴权模式
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Function_test03::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void Function_test03::SetUp()
{}

void Function_test03::TearDown()
{}

// 042 禁用用户A的直连读协议，用户B 直连读操作正常
TEST_F(Function_test03, Resilience_012_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;

    // 使用gmrule导入白名单
    char schema_path[128] = "./configSchema/user01.gmuser";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, schema_path, g_connServer);
    AW_FUN_Log(LOG_INFO ,"%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);

    // 导入系统权限
    const char *sys_policy_file = "./configSchema/system_privilege01.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s", g_toolPath, sys_policy_file, g_connServer);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;

    // 禁用user01用户的直连读协议
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName abc -userName user01 -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连、建表、写数据
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testCreateLabel(stmt1);
    testInsertVertexLabel(stmt1, times, initValue, g_vertexName);

    // 直连读操作
    TestGmcScanByPk(stmt1);

    // 删表、断连
    testDropLabel(stmt1, g_vertexName);
    ret = testGmcDisconnect(conn1, stmt1);

    // 删除白名单
    const char *allow_list_file = "./configSchema/user01.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043 禁用用户A的直连读协议，进行直连读操作，预期失败；启用用户A的直连读协议，进行直连读操作包括 主键读、二级索引、全表扫描，预期成功
TEST_F(Function_test, Resilience_012_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    testCreateLabel(g_stmt);
    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);

    // 禁用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    // 用system(g_command),将失败的结果打印后，匹配executeCommand
    system(g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读，预期失败、无权限
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_vertexName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(g_stmt, g_vertexKeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
        if (ret == GMERR_OK) {
            succNum++;
        }
    }
    AW_FUN_Log(LOG_INFO, "============== PK Scan Succ Num(%d)==================\n\n", succNum);

    // 启用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -enable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    // 用system(g_command),将失败的结果打印后，匹配executeCommand
    system(g_command);
    ret = executeCommand(g_command, "enable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读操作
    // 主键读
    TestGmcScanByPk(g_stmt);

    // 全表扫描
    TestGmcFullScan(g_stmt);

    // 二级索引
    TestGmcScanByLocalHash(g_stmt);
    TestGmcScanByHashCluster(g_stmt);
    TestGmcScanByLocal(g_stmt);
    TestGmcScanByLpm4(g_stmt);

    // 删表
    testDropLabel(g_stmt, g_vertexName);
    // 断连
    ret = testGmcDisconnect(g_conn ,g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044 用户A进行直连读，禁用后再开启直连读协议，再进行 主键读、二级索引、全表扫描 直连读，预期成功
TEST_F(Function_test, Resilience_012_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    testCreateLabel(g_stmt);
    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);

    // 禁用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    // 用system(g_command),将失败的结果打印后，匹配executeCommand
    system(g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -enable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    // 用system(g_command),将失败的结果打印后，匹配executeCommand
    system(g_command);
    ret = executeCommand(g_command, "enable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 直连读操作
    // 主键读
    TestGmcScanByPk(g_stmt);

    // 全表扫描
    TestGmcFullScan(g_stmt);

    // 二级索引
    TestGmcScanByLocalHash(g_stmt);
    TestGmcScanByHashCluster(g_stmt);
    TestGmcScanByLocal(g_stmt);
    TestGmcScanByLpm4(g_stmt);

    // 删表
    testDropLabel(g_stmt, g_vertexName);
    // 断连
    ret = testGmcDisconnect(g_conn ,g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045 禁用直连读协议，CS模式建连，预期成功
TEST_F(Function_test, Resilience_012_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 禁用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    // 用system(g_command),将失败的结果打印后，匹配executeCommand
    system(g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt, 0, true, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046 启用直连读协议，CS模式建连，预期成功
TEST_F(Function_test, Resilience_012_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 启用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -enable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    // 用system(g_command),将失败的结果打印后，匹配executeCommand
    system(g_command);
    ret = executeCommand(g_command, "enable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt, 0, true, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047 禁用超级用户的直连读协议，超级用户 主键读、二级索引、全表扫描直连读失败
TEST_F(Function_test02, Resilience_012_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;

    // 查看DB当前存在的用户及相应权限,IS_SUPERUSER字段是1，表明是超级用户
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q \"V\\$PRIVILEGE_USER_STAT\"", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    system(g_command);

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    testCreateLabel(g_stmt);
    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);

    // 禁用超级用户的直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读，预期失败、无权限
    TestGmcScanByPkFailure(g_stmt);

    // 全表扫描失败、无权限
    TestGmcFullScanFailure(g_stmt);

    // 二级索引、无权限
    TestGmcScanByLocalHashFailure(g_stmt);

    // 删表
    testDropLabel(g_stmt, g_vertexName);
    // 断连
    ret = testGmcDisconnect(g_conn ,g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048 禁用超级用户的直连读协议，超级用户直连读失败；再启用超级用户的直连读协议，主键读、二级索引、全表扫描 直连读成功
TEST_F(Function_test02, Resilience_012_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;

    // 查看DB当前存在的用户及相应权限,IS_SUPERUSER字段是1，表明是超级用户
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q \"V\\$PRIVILEGE_USER_STAT\"", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    system(g_command);

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    testCreateLabel(g_stmt);
    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);

    AW_FUN_Log(LOG_INFO, "************************ step1：禁用超级用户直连读协议，直连读操作失败 ************************\n");
    // 禁用超级用户的直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读，预期失败、无权限
    TestGmcScanByPkFailure(g_stmt);

    // 全表扫描失败、无权限
    TestGmcFullScanFailure(g_stmt);

    // 二级索引、无权限
    TestGmcScanByLocalHashFailure(g_stmt);

    AW_FUN_Log(LOG_INFO, "************************ step2：启用超级用户直连读协议，直连读操作成功 ************************\n");
    // 禁用超级用户的直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -enable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "enable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读，预期成功
    TestGmcScanByPk(g_stmt);

    // 全表扫描成功
    TestGmcFullScan(g_stmt);

    // 二级索引 预期成功
    TestGmcScanByLocalHash(g_stmt);

    // 删表
    testDropLabel(g_stmt, g_vertexName);
    // 断连
    ret = testGmcDisconnect(g_conn ,g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049 禁用直连读协议，视图查询功能不受影响，record、count视图
TEST_F(Function_test, Resilience_012_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 2;
    uint32_t initValue = 0;

    // 禁用直连读协议，预期成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -processName Function_test -userName root -disable 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "disable user protocol 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = testGmcConnect(&g_conn ,&g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    testCreateLabel(g_stmt);

    // 写数据
    testInsertVertexLabel(g_stmt, times, initValue, g_vertexName);
    
    // 查看record视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record \"Vertex_01\"", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看count视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count \"Vertex_01\"", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    testDropLabel(g_stmt, g_vertexName);

    // 断连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
