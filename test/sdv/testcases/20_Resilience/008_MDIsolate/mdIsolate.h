/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :纵向隔离头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/09/13
**************************************************************************** */
#ifndef MDISOLATE_H
#define MDISOLATE_H

#include "t_datacom_lite.h"
// 没有找到build-in中EXPECT_GT的断言，自定义断言规避codeclean
#define AW_MACRO_EXPECT_GT_INT(a, b) EXPECT_GT(a, b)

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *g_labelName = (char *)"Label1";
char g_vertexLabelConfig[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
char g_edgeLabelConfig[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

void TestDropVertexLabel(GmcStmtT *stmtSync, const char *vertexLabelName, int32_t expectValue = GMERR_OK)
{
    int ret = 0;
    ret = GmcDropVertexLabel(stmtSync, vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(expectValue, ret);
}

int TestCreateLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = NULL)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

void TestLabel1GetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1V", &t1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    *root = Root;
    *t1V = t1;
}

void TestLabel1SetPk(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


#define LABEL_FIXED_SIZE1   9
#define MAX_MASK_LEN_16     1000
#define MAX_MASK_LEN_24     2501000
#define LABEL_BIG_OBJ_FIXED_SIZE   13312
#define LABEL_BIG_STRING_SIZE      13312
void TestLabel1SetRootProperty(GmcNodeT *node, int32_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t fixedValue[LABEL_FIXED_SIZE1] = {0};
        for (int j = 0; j < LABEL_FIXED_SIZE1; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, LABEL_FIXED_SIZE1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t valueUint64 = i;
    uint8_t stringValue[LABEL_BIG_OBJ_FIXED_SIZE] = {'\0'};
    (void)snprintf((char *)stringValue, sizeof(stringValue), "f%013310d", valueUint64);
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_FIXED, stringValue, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_STRING, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_BYTES, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


void TestLabel1UpdateProperty(GmcNodeT *node, int32_t i, bool isDefaultValue = true)
{
    int ret = 0;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    uint8_t fixedValue[LABEL_FIXED_SIZE1] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (!isDefaultValue) {
        for (int j = 0; j < LABEL_FIXED_SIZE1; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < LABEL_FIXED_SIZE1; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, LABEL_FIXED_SIZE1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t valueUint64 = i;
    uint8_t stringValue[LABEL_BIG_OBJ_FIXED_SIZE] = {'\0'};
    (void)snprintf((char *)stringValue, sizeof(stringValue), "f%013310d", valueUint64);
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_FIXED, stringValue, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_STRING, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_BYTES, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestLabel1VectorNode(GmcNodeT *node, int32_t i)
{
    int32_t ret = 0;
    uint32_t valueUin32 = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT32, &valueUin32, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_UINT32, &valueUin32, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t stringValue[LABEL_BIG_OBJ_FIXED_SIZE] = {'\0'};
    (void)snprintf((char *)stringValue, sizeof(stringValue), "f%013310d", valueUin32);
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_STRING, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_BYTES, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_STRING, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_BYTES, stringValue, strlen((char *)stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int32_t TestLabel1InsertOrReplace(GmcStmtT *stmt, char *labelName, int32_t index, GmcOperationTypeE operationType,
                                  int32_t vectorNum, bool isDefaultValue = true)
{
    GmcNodeT *root, *t1V;
    int64_t i = index;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
    if (ret == GMERR_OK) {
        TestLabel1GetNode(stmt, &root, &t1V);
        TestLabel1SetPk(root, i);
        TestLabel1SetRootProperty(root, i, isDefaultValue);
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t1V, &t1V);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestLabel1VectorNode(t1V, j);
        }
        ret = GmcExecute(stmt);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        return ret;
    }
}

int32_t TestLabel1InsertOrReplaceSyncBatch(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startValue,
    int32_t count, GmcOperationTypeE operationType, int32_t vectorNum, bool isDefaultValue = true)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = count;
    GmcNodeT *root, *t1V;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startValue; i < count + startValue; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
        if (ret == GMERR_OK) {
            TestLabel1GetNode(stmt, &root, &t1V);
            TestLabel1SetPk(root, i);
            TestLabel1SetRootProperty(root, i, isDefaultValue);
            // 插入vector节点
            for (uint32_t j = 0; j < vectorNum; j++) {
                ret = GmcNodeAppendElement(t1V, &t1V);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestLabel1VectorNode(t1V, j);
            }
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            return ret;
        }
    }

    // 批处理提交
    int32_t ret1 = GmcBatchExecute(batch, &batchRet);
    if (ret1 == GMERR_OK) {
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    return ret1;
}

void TestLabel1PkIndexSet(GmcStmtT *stmt, int32_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


int32_t TestLabel1InsertOrReplaceAsyncBatch(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t startValue,
    int32_t count, GmcOperationTypeE operationType, int32_t vectorNum, bool isDefaultValue = true)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = count;
    GmcNodeT *root, *t1V;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data = {0};
    memset(&data, 0, sizeof(data));

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = startValue; i < count + startValue; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
        if (ret == GMERR_OK) {
            TestLabel1GetNode(stmt, &root, &t1V);
            TestLabel1SetPk(root, i);
            TestLabel1SetRootProperty(root, i, isDefaultValue);
            // 插入vector节点
            for (uint32_t j = 0; j < vectorNum; j++) {
                ret = GmcNodeAppendElement(t1V, &t1V);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestLabel1VectorNode(t1V, j);
            }
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            return ret;
        }
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return data.status;
}

int CheckAccountStatus(GmcStmtT *stmt, const char *labelName, bool isNormal = true)
{
    int ret = 0;
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    if (isNormal) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcCheckStatusE checkStatus;
        ret = GmcGetCheckStatus(checkInfo, &checkStatus);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        return ret;
    }
}

int32_t TestLabel1UpdateOrMerge(GmcStmtT *stmt, char *labelName, int32_t index, GmcOperationTypeE operationType,
                                int32_t updateValue, bool isDefaultValue = true)
{
    GmcNodeT *root, *t1V;
    int64_t i = index;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
    if (ret == GMERR_OK) {
        TestLabel1PkIndexSet(stmt, i);
        TestLabel1GetNode(stmt, &root, &t1V);
        TestLabel1UpdateProperty(root, updateValue, isDefaultValue);
        ret = GmcExecute(stmt);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        return ret;
    }
}

void TestWholeLabelScan(GmcStmtT *stmt, char *labelName, uint32_t *fetchNum)
{
    bool isFinish = false;
    int32_t loop = 0;
    (*fetchNum) = 0;
    int32_t ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    if (ret == GMERR_OK) {
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        while (!isFinish) {
            (*fetchNum)++;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
        }
    } else {
        AW_FUN_Log(LOG_INFO, "ret = %d line = %d\n", ret, __LINE__);
    }
    AW_FUN_Log(LOG_INFO, "fetchNum = %d line = %d\n", *fetchNum, __LINE__);
}

int32_t CreateManyEdgeLabel(GmcStmtT *stmt, uint32_t EdgeLabelNum, uint32_t *succNum)
{
    int32_t ret = 0;
    char edgeLabelJson[1024] = {0};
    char edgeLabelName[64] = {0};
    uint32_t edgeNum = EdgeLabelNum;
    int32_t id = 0;
    uint32_t succCount = 0;
    for (id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"edgeT%u\",\"source_vertex_label\":\"T80\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"T90\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F2\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"}]}}]",
            id);
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcCreateEdgeLabel(stmt, edgeLabelJson, g_edgeLabelConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyEdgeLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

int32_t CreateManyEdgeLabel2(GmcStmtT *stmt, uint32_t EdgeLabelNum, uint32_t *succNum)
{
    int32_t ret = 0;
    char edgeLabelJson[1024] = {0};
    char edgeLabelName[64] = {0};
    uint32_t edgeNum = EdgeLabelNum;
    int32_t id = 0;
    uint32_t succCount = 0;
    for (id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"edge2T%u\",\"source_vertex_label\":\"T20\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"T10\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F2\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"}]}}]",
            id);
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcCreateEdgeLabel(stmt, edgeLabelJson, g_edgeLabelConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyEdgeLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

int32_t CreateManyEdgeLabel3(GmcStmtT *stmt, uint32_t EdgeLabelNum, uint32_t *succNum)
{
    int32_t ret = 0;
    char edgeLabelJson[1024] = {0};
    char edgeLabelName[64] = {0};
    uint32_t edgeNum = EdgeLabelNum;
    int32_t id = 0;
    uint32_t succCount = 0;
    for (id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"edge3T%u\",\"source_vertex_label\":\"T30\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"T40\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F2\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"}]}}]",
            id);
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcCreateEdgeLabel(stmt, edgeLabelJson, g_edgeLabelConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyEdgeLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

void DropManyEdgeLabel(GmcStmtT *stmt, uint32_t EdgeLabelNum)
{
    int32_t ret = 0;
    char srcVertexLabelName[] = "T80";
    char dstVertexLabelName[] = "T90";
    uint32_t edgeNum = EdgeLabelNum;
    char edgeLabelName[64] = {0};
    // 删edgeLabel
    for (int32_t id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcDropEdgeLabel(stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 删vertexLabel
    ret = GmcDropVertexLabel(stmt, srcVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, dstVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DropManyEdgeLabel2(GmcStmtT *stmt, uint32_t EdgeLabelNum)
{
    int32_t ret = 0;
    char srcVertexLabelName[] = "T20";
    char dstVertexLabelName[] = "T10";
    uint32_t edgeNum = EdgeLabelNum;
    char edgeLabelName[64] = {0};
    // 删edgeLabel
    for (int32_t id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelName, "edge2T%u", id);
        ret = GmcDropEdgeLabel(stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 删vertexLabel
    ret = GmcDropVertexLabel(stmt, srcVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, dstVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DropManyEdgeLabel3(GmcStmtT *stmt, uint32_t EdgeLabelNum)
{
    int32_t ret = 0;
    char srcVertexLabelName[] = "T30";
    char dstVertexLabelName[] = "T40";
    uint32_t edgeNum = EdgeLabelNum;
    char edgeLabelName[64] = {0};
    // 删edgeLabel
    for (int32_t id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelName, "edge3T%u", id);
        ret = GmcDropEdgeLabel(stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 删vertexLabel
    ret = GmcDropVertexLabel(stmt, srcVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, dstVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int32_t CreateManyVertexLabel(GmcStmtT *stmt, uint32_t labelNum, uint32_t *succNum)
{
    int ret = 0;
    uint32_t createLabelNum = labelNum;
    char *testSchema = NULL;
    char labelName[128] = {0};
    char schemaPath[256] = {0};
    char createLabelSchema[256] = {0};
    int id = 0;
    uint32_t succCount = 0;

    for (id = 1; id <= createLabelNum; id++) {
        (void)sprintf(createLabelSchema, "sed -i 's/\"tree1.*\"/\"tree1%d\"/g' ./schemaFile/treeLabel.gmjson", id);
        system(createLabelSchema);
        (void)sprintf(schemaPath, "./schemaFile/treeLabel.gmjson");
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
        (void)sprintf(labelName, "tree1%d", id);
        ret = GmcDropVertexLabel(stmt, labelName);
        ret = GmcCreateVertexLabel(stmt, testSchema, NULL);
        if (ret != GMERR_OK) {
            free(testSchema);
            AW_FUN_Log(LOG_INFO, "CreateManyVertexLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
        free(testSchema);
    }
    (*succNum) = succCount;
    return ret;
}

int32_t CreateManyVertexLabelGmimport(GmcStmtT *stmt, uint32_t labelNum, uint32_t *succNum)
{
    int ret = 0;
    uint32_t createLabelNum = labelNum;
    char *testSchema = NULL;
    char labelName[128] = {0};
    char schemaPath[256] = {0};
    char createLabelSchema[256] = {0};
    int id = 0;
    char cmd[512] = {0};
    uint32_t succCount = 0;

    for (id = 1; id <= createLabelNum; id++) {
        (void)sprintf(createLabelSchema, "sed -i 's/\"tree1.*\"/\"tree1%d\"/g' ./schemaFile/treeLabel.gmjson", id);
        system(createLabelSchema);
        (void)sprintf(schemaPath, "./schemaFile/treeLabel.gmjson");
        (void)sprintf(labelName, "tree1%d", id);
        ret = GmcDropVertexLabel(stmt, labelName);
        memset(cmd, 0, 512);
        (void)snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -s %s -ns %s", g_toolPath, schemaPath, g_connServer,
                       g_testNameSpace);
        ret = executeCommand(cmd, "successfully");
        if (ret != GMERR_OK) {
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

// 删表
void DropManyVertexLabel(GmcStmtT *stmt, uint32_t labelNum)
{
    int ret = 0;
    char labelName[128] = {0};
    uint32_t createLabelNum = labelNum;
    // 删表
    for (int id = 1; id <= createLabelNum; id++) {
        (void)sprintf(labelName, "tree1%d", id);
        ret = GmcDropVertexLabel(stmt, labelName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "labelName: %s\n", labelName);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}
const char *g_msConfigTrans = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";


// 建yang表
int32_t CreateManyYangLabel(GmcStmtT *stmt, uint32_t labelNum, uint32_t *succNum)
{
    int ret = 0;
    uint32_t createLabelNum = labelNum;
    char *testSchema = NULL;
    char labelName[128] = {0};
    char schemaPath[256] = {0};
    char createLabelSchema[256] = {0};
    int id = 0;
    uint32_t succCount = 0;
    AsyncUserDataT data = {0};
    AsyncUserDataT data2 = {0};

    for (id = 1; id <= createLabelNum; id++) {
        (void)sprintf(createLabelSchema, "sed -i 's/\"yang1.*\"/\"yang1%d\"/g' ./schemaFile/Yang_AllType.gmjson", id);
        system(createLabelSchema);
        (void)sprintf(schemaPath, "./schemaFile/Yang_AllType.gmjson");
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
        (void)sprintf(labelName, "yang1%d", id);
        ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data2, 0, sizeof(data2));
        ret = GmcCreateVertexLabelAsync(stmt, testSchema, g_msConfigTrans, create_vertex_label_callback, &data2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data2.status != GMERR_OK) {
            free(testSchema);
            break;
        }
        succCount++;
        free(testSchema);
    }
    (*succNum) = succCount;
    return ret;
}

void DropManyYangLabel(GmcStmtT *stmt, uint32_t labelNum)
{
    int ret = 0;
    char labelName[128] = {0};
    AsyncUserDataT data = {0};
    for (int32_t id = 1; id <= labelNum; id++) {
        (void)sprintf(labelName, "yang1%d", id);
        ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    }
}

void ResLabelSetProperty(
    GmcStmtT *stmt, int32_t i, bool boolValue, char *strValue, void *fixedValue, char *bytesValue)
{
    int ret = 0;
    uint32_t valueUint32 = i;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = (i) % 32768;
    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = (i) % 65536;
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = (i) % 128;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = (i) % 256;
    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_BOOL, &boolValue, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    float f9_value = i + 0.5;
    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = i + 0.55;
    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t valueU64 = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_TIME, &valueU64, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = 'a';
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 'b';
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_FIXED, fixedValue, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F20", GMC_DATATYPE_STRING, strValue, strlen(strValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_BYTES, (char *)bytesValue, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F22", GMC_DATATYPE_FIXED, fixedValue, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F23", GMC_DATATYPE_TIME, &valueU64, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F24", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F25", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F26", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F27", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F28", GMC_DATATYPE_INT64, &f1_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F29", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F30", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F31", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F32", GMC_DATATYPE_UINT32, &valueUint32, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ResLabelIndexLpm4Set(GmcStmtT *stmt, int32_t uiVrIndex)
{
    int ret = 0;
    uint32_t vrid = 0;
    uint32_t valueU32 = uiVrIndex;
    uint32_t dest_ip_addr = 0;
    uint8_t mask_len = 0;
    if (uiVrIndex <= MAX_MASK_LEN_16) {
        dest_ip_addr = ((uiVrIndex + 2) << 16);
        mask_len = ((16) & 0xff);
    } else if (uiVrIndex > MAX_MASK_LEN_16 && uiVrIndex <= MAX_MASK_LEN_24) {
        dest_ip_addr = ((uiVrIndex + 2) << 8);
        mask_len = ((24) & 0xff);
    } else {
        dest_ip_addr = ((uiVrIndex + 2));
        mask_len = ((32) & 0xff);
    }

    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F17", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F18", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F19", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ResLabelSetPk(
    GmcStmtT *stmt, int64_t *f0Value, char *strValue, char *bytesValue, int32_t bytesLen = 7)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_STRING, strValue, strlen(strValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_BYTES, bytesValue, strlen(bytesValue));
    if (bytesLen == 0) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int32_t TestLabelInsertVertex(GmcStmtT *stmt, char *labelName, int32_t i, bool boolValue,
    char *strValue, void *fixedValue, char *bytesValue, bool isResLabel = 0)
{
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t f0Value = i;
    ResLabelSetProperty(stmt, i, boolValue, strValue, (uint8_t *)fixedValue, bytesValue);
    ResLabelIndexLpm4Set(stmt, i);
    ResLabelSetPk(stmt, &f0Value, strValue, (char *)bytesValue);
    if (isResLabel) {
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(2, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "res", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    return ret;
}

void SubLabel1CallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 300;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_AGED: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            break;
                        }
                        default: {
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;

                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            break;
                        }
                        default: {
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            break;
                        }
                        default: {
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

// 使用命令查动态内存视图
void sysviewReadDynMemory(char *fileName)
{
    char command[512] = {'\0'};
    char const *viewname = "V\\$COM_DYN_CTX";
    (void)snprintf(command, 512, "%s/gmsysview -q %s > %s", g_toolPath, viewname, fileName);
    int ret = executeCommand(command, "SysDynContext", "AppDynContext");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 使用命令查共享内存视图
void sysviewReadShMemory(char *fileName)
{
    char command[512] = {'\0'};
    char const *viewname = "V\\$COM_SHMEM_CTX";
    (void)snprintf(command, 512, "%s/gmsysview -q %s > %s", g_toolPath, viewname, fileName);
    int ret = executeCommand(command, "SysShmContext", "AppShmContext");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
GmcStmtT *stmt[MAX_CONN_SIZE] = {NULL};
void TestConnFullSync(uint32_t existConnNum, uint32_t *successNum)
{
    int32_t ret = 0;

    // 建满规格的连接
    for (int j = 1; j < MAX_CONN_SIZE - existConnNum; j++) {
        ret = testGmcConnect(&conn[j], &stmt[j]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "connect syncConnect failed, ret = %d, j = %d, existConnNum = %d\n",
                       ret, j, existConnNum);
            (*successNum) = j;
            if (ret != GMERR_OUT_OF_MEMORY && ret != GMERR_MEMORY_OPERATE_FAILED) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_MEMORY_OPERATE_FAILED, ret);
            }
            break;
        }
    }
}

void TestDisConnectSync(uint32_t successNum)
{
    // 断链
    for (int j = 1; j < successNum; j++) {
        int32_t ret = testGmcDisconnect(conn[j], stmt[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        conn[j] = NULL;
        stmt[j] = NULL;
    }
}

void TestCreateNspaceFull(GmcStmtT *stmt, uint32_t *succNum)
{
    int32_t ret = 0;
    char nameS[64] = {0};
    int nameSNum = 64;
    uint32_t nsSuccNum = 0;

    for (int id = 1; id <= nameSNum; id++) {
        (void)sprintf((char *)nameS, "nS%d", id);
        GmcDropNamespace(stmt, nameS);
        ret = GmcCreateNamespace(stmt, nameS, "gmdbv5");
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        } else if (ret == GMERR_OUT_OF_MEMORY || ret == GMERR_MEMORY_OPERATE_FAILED) {
            break;
        } else if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nsSuccNum++;
    }
    (*succNum) = nsSuccNum;
}

void TestDropNspaceFull(GmcStmtT *stmt, uint32_t Num)
{
    int32_t ret = 0;
    char nameS[64] = {0};
    // 删 namespace
    for (int i = 1; i <= Num; i++) {
        (void)sprintf((char *)nameS, "nS%d", i);
        ret = GmcDropNamespace(stmt, nameS);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "TestDropNspaceFull nameS= %s ret =%d i =%d\n", nameS, ret, i);
            testGmcGetLastError(NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestCreateManyResPool(GmcStmtT *stmt, uint32_t *succNum)
{
    int32_t ret = 0;
    char createPool[1024] = {0};
    char poolName[128] = {0};
    uint32_t count = 0;

    for (uint32_t i = 1; i <= 0xffffffff; i++) {
        (void)sprintf(createPool, "{\"name\" : \"poolName%d\",\"pool_id\" : %d,\"start_id\" : 0,\"capacity\" : 65535,"
                      "\"order\" : 0,\"alloc_type\" : 0}", i, i);

        (void)sprintf(poolName, "poolName%d", i);
        ret = GmcDestroyResPool(stmt, poolName);
        ret = GmcCreateResPool(stmt, createPool);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, i = %d line = %d\n", ret, i, __LINE__);
            break;
        }
        count++;
    }
    (*succNum) = count;
}

void TestDropManyResPool(GmcStmtT *stmt, uint32_t Num)
{
    int32_t ret = 0;
    char poolName[128] = {0};
    for (uint32_t i = 1; i <= Num; i++) {
        (void)sprintf(poolName, "poolName%d", i);
        ret = GmcDestroyResPool(stmt, poolName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmimmportManyAllowlist(uint32_t *succNum, uint32_t gmruleNum = 0xffffffff)
{
    int32_t ret = 0;
    char allowlist[1024] = {0};
    uint32_t count = 0;
    char cmd[1024] = {0};
    char allowPath[128] = "./schemaFile/allowtest.gmuser";

    for (uint32_t i = 1; i <= gmruleNum; i++) {
        (void)sprintf(allowlist, "sed -i 's/\"allow.*\"/\"allow%d\"/g' ./schemaFile/allowtest.gmuser", i);
        system(allowlist);
        (void)snprintf(
            cmd, 1024, "%s/gmrule -c import_allowlist -f %s -s %s", g_toolPath, allowPath, g_connServer);
        ret = executeCommand(cmd, "successfully");
        if (ret != GMERR_OK) {
            system(cmd);
            AW_FUN_Log(LOG_DEBUG, "ret = %d, i = %d line = %d\n", ret, i, __LINE__);
            break;
        }
        if (i % 100 == 0) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, i = %d line = %d\n", ret, i, __LINE__);
        }
        count++;
    }
    (*succNum) = count;
}

void TestRemoveManyAllowlist(uint32_t succNum)
{
    int32_t ret = 0;
    char allowlist[1024] = {0};
    uint32_t count = 0;
    char cmd[1024] = {0};
    char allowPath[128] = "./schemaFile/allowtest.gmuser";

    for (uint32_t i = 1; i <= succNum; i++) {
        (void)sprintf(allowlist, "sed -i 's/\"allow.*\"/\"allow%d\"/g' ./schemaFile/allowtest.gmuser", i);
        system(allowlist);
        (void)snprintf(
            cmd, 1024, "%s/gmrule -c remove_allowlist -f %s -s %s", g_toolPath, allowPath, g_connServer);
        ret = executeCommand(cmd, "successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, i = %d line = %d\n", ret, i, __LINE__);
            break;
        }
        if (i % 100 == 0) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, i = %d line = %d\n", ret, i, __LINE__);
        }
        count++;
    }
}


void TestCreateManySub(GmcStmtT *stmt, GmcConnT *connSub, GmcStmtT *stmtSub, uint32_t *subNum)
{
    int ret = 0;
    char labelName[128] = {0};
    uint32_t createNum = 1025;
    const char *subConnName = "subConnName";
    SnUserDataT userData;
    char subName[128] = "subVertexLabel";
    char *subInfo = NULL;
    char subPath[256] = {0};
    uint32_t count = 0;
    // 创建订阅连接
    int chanRingLen = 256;
    (void)sprintf(subPath, "./schemaFile/sub.gmjson");
    readJanssonFile(subPath, &subInfo);
    EXPECT_NE((void *)NULL, subInfo);
    for (int id = 1; id <= createNum; id++) {
        // 创建订阅关系
        (void)sprintf(subName, "subVertexLabel_%d", id);
        // 订阅
        GmcSubConfigT tmpSubInfo;
        tmpSubInfo.subsName = subName;
        tmpSubInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmpSubInfo, connSub, SubLabel1CallBack, &userData);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, id = %d line = %d\n", ret, id, __LINE__);
            break;
        }
        count++;
    }
    free(subInfo);
    (*subNum) = count;
}

void TestRemoveManySub(GmcStmtT *stmt, GmcConnT *connSub, GmcStmtT *stmtSub, uint32_t subNum)
{
    int ret = 0;
    char subName[128] = "subVertexLabel";
    // 取消订阅和删表
    for (int id = 1; id <= subNum; id++) {
        (void)sprintf(subName, "subVertexLabel_%d", id);
        ret = GmcUnSubscribe(stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 释放订阅连接
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestCreateManyKV(GmcStmtT *stmt, uint32_t *succNum)
{
    int ret = 0;
    uint32_t count = 0;
    char kvName[32] = {0};
    int32_t kvLabelNum = 1025;
    for (int id = 1; id < kvLabelNum; id++) {
        (void)sprintf(kvName, "KV%d", id);
        ret = GmcKvDropTable(stmt, kvName);
        ret = GmcKvCreateTable(stmt, kvName, NULL);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, id = %d line = %d\n", ret, id, __LINE__);
            break;
        }
        count++;
    }
    (*succNum) = count;
}

void TestDropManyKV(GmcStmtT *stmt, uint32_t succNum)
{
    int ret = 0;
    char kvName[32] = {0};
    int32_t kvLabelNum = succNum;
    for (int id = 1; id < kvLabelNum; id++) {
        (void)sprintf(kvName, "KV%d", id);
        ret = GmcKvDropTable(stmt, kvName);
        ret = GmcKvCreateTable(stmt, kvName, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSnCallbackNotCmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[128] = {0};
    unsigned int labelNameLen = 128;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        } else if (eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                    __LINE__);
                break;
            }
        }
    }
}

int GetKeyWordCnt(const char *keyWord, int patience = 10)
{
    system("rm -rf startReply.txt");
    string valueStr;
    int value = 0;
    char cmdOutPut[1024] = {0};
    char cmd[256] = {0};
    (void)snprintf(cmd, 256, "%s/gmserver -b -p %s > startReply.txt", g_toolPath, g_sysGMDBCfg);
    system(cmd);
    
    (void)snprintf(cmd, 256, "grep -r \"%s\" startReply.txt|wc -l", keyWord);
    int tryTimes = 0;
    FILE *pf;
    while (value == 0 && tryTimes < patience) {
        pf = popen(cmd, "r");
        while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
            valueStr.assign(cmdOutPut);
            value = stoi(valueStr);
        }
        sleep(1);
        tryTimes++;
    }
    pclose(pf);
    return value;
}

#endif
