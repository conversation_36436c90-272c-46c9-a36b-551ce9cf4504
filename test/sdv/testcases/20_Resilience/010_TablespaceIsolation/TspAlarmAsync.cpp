/*
表空间隔离  迭代三
=================================== 内存告警 ===================================
115 校验内存告警，默认nsp、异步创建tsp1并创建表v1，写数据至内存满，获取告警数据，预期告警触发，查询tablespace视图；
    删除一半数据，再次获取告警数据，预期无告警切为告警消除状态，查询tablespace视图。
116 校验内存告警，默认nsp、异步创建多个tsp并创建表v1，2个写数据至内存满、2个不写满，获取告警数据，预期告警触发，查询tablespace视图；
    删除一半数据，再次获取告警数据，预期告警解除，查询tablespace视图。
117 校验内存告警，默认nsp、异步创建tsp1并创建表v1，写数据至内存满、获取告警数据，预期告警触发，查询tablespace视图；
    删除一半数据，再次获取告警数据，预期告警解除，查询tablespace视图，修改阈值为10%，预期告警且为告警激活；再次修改阈值为90%，预期无告警且为告警清除。
118 异步创建tsp1和tsp2，分别写满tsp1、tsp2和public，第一次获取告警数据，预期告警且为告警激活状态；
    删除tsp1，第二次获取告警数据，预期没有告警且为告警激活状态；删除tsp2，第三次获取告警数据，预期无告警且为告警激活；
    删除public的数据，第四次获取告警数据，预期没有告警且为告警清除状态。（非交互事务，public是否将undo的空间写满）
119 异步创建tsp1和tsp2，分别写满tsp1、tsp2和public，第一次获取告警数据，预期告警且为告警激活状态；
    删除tsp1，第二次获取告警数据，预期没有告警且为告警激活状态；删除tsp2，第三次获取告警数据，预期无告警且为告警激活；
    删除public的数据，第四次获取告警数据，预期没有告警且为告警清除状态。开事务（交互事务，public是否将undo的空间写满）直到提交时才回收undo空间
*/

#include "tablespace_isolation.h"

uint32_t g_getRaiseRatio = 10;
uint32_t g_raiseRatio = 90;
uint32_t g_clearRatio = 80;
uint32_t g_delay = 310;  // 时间需要大于300秒

class TspAlarmAsync_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TspAlarmAsync_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAlarmAsync_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("ipcrm -a");
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TspAlarmAsync_test::SetUp()
{
    int ret;
    // 同步连接、异步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //设置阈值
    ret = GmcChangeAlmThreshold(g_stmt, GMC_ALARM_SHM_USED_INFO, 90, 80);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAlarmAsync_test::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 115 校验内存告警，默认nsp、异步创建tsp1并创建表v1，写数据至内存满，获取告警数据，预期告警触发，查询tablespace视图；
//     删除一半数据，再次获取告警数据，预期无告警切为告警消除状态，查询tablespace视图。
TEST_F(TspAlarmAsync_test, Resilience_010_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};

    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;

    const char *tablespaceName = "tsp1";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    uint32_t num = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = num;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = num + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = num + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f3Value = num + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data in %s: %d\n", g_vertexLabelName2, num);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        num++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 写满tsp1后，查看告警信息 ============\n");
    TspInfo();

    // 获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();

    // 删除大半数据，（因索引页是散列的关系，数据存储分散，需删除大部分数据才可能达到一半）
    uint32_t cnt = 0;
    while (cnt < num * 9 / 10) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        int32_t keyValue = (uint32_t)cnt;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(g_stmt_async, "ip4_key"));
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        EXPECT_EQ(GMERR_OK, ret);
        testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        cnt++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 删除大半数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_CLEARED, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);

    // 删表空间
    TestDropTablespaceAsync(tablespaceName);
    AW_FUN_Log(LOG_INFO, "============ 表空间删除后，查看Tsp信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 116 校验内存告警，默认nsp、异步创建多个tsp并创建表，1个写数据至内存满、1个不写满，获取告警数据，预期告警触发，查询tablespace视图；
//     删除一半数据，再次获取告警数据，预期告警解除，查询tablespace视图。
TEST_F(TspAlarmAsync_test, Resilience_010_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};

    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;

    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName1);
    testCreateTablespaceAsync(tablespaceName2);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel3
    readJanssonFile("./schemaFile/003.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    uint32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        index++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 向simpleLabel3中写数据
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = cnt;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f1Value = cnt + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f2Value = cnt + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f3Value = cnt + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        cnt++;
        if (cnt == 10) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", cnt);
            break;
        }
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 写满tsp1、tsp2写部分数据，查看告警信息 ============\n");
    TspInfo();

    // 获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();

    // 删除大半数据，（因索引页是散列的关系，数据存储分散）
    uint32_t del = 0;
    while (del < index * 9 / 10) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        int32_t keyValue = (uint32_t)del;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(g_stmt_async, "ip4_key"));
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        EXPECT_EQ(GMERR_OK, ret);
        testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        del++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 删除大半数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为告警解除状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_CLEARED, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);
    TestDeleteVertexAsync(g_vertexLabelName3);

    // 删表空间
    TestDropTablespaceAsync(tablespaceName1);
    TestDropTablespaceAsync(tablespaceName2);

    AW_FUN_Log(LOG_INFO, "============ 表空间删除后，查看Tsp信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 117 校验内存告警，默认nsp、异步创建tsp1并创建表v1，写数据至内存满、获取告警数据，预期告警触发，查询tablespace视图；
//     删除一半数据，再次获取告警数据，预期告警解除，查询tablespace视图，修改阈值为10%，预期告警且为告警激活；再次修改阈值为90%，预期无告警且为告警清除。
TEST_F(TspAlarmAsync_test, Resilience_010_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tablespaceName = "tsp1";
    AsyncUserDataT userData = {0};

    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    uint32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        index++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 写满tsp1后，查看告警信息 ============\n");
    TspInfo();

    // 获取告警数据，预期告警且为告警激活状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();

    // 删除大半数据，（因索引页是散列的关系，数据存储分散）
    uint32_t cnt = 0;
    while (cnt < index * 9 / 10) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        int32_t keyValue = (uint32_t)cnt;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(g_stmt_async, "ip4_key"));
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &keyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        EXPECT_EQ(GMERR_OK, ret);
        testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        cnt++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 删除大半数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为告警清除状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_CLEARED, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 修改阈值为10%
    AW_FUN_Log(LOG_INFO, "============ 修改阈值为10%，查看告警 ============\n");
    g_raiseRatio = 10;
    g_clearRatio = 5;
    ret = GmcChangeAlmThreshold(g_stmt, AlarmId, g_raiseRatio, g_clearRatio);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "raiseRatio:%d  clearRatio:%d\n", g_raiseRatio, g_clearRatio);

    // 获取告警数据，预期告警且为告警激活状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改阈值为90%
    AW_FUN_Log(LOG_INFO, "============ 修改阈值为90%，查看告警 ============\n");
    g_raiseRatio = 90;
    g_clearRatio = 80;
    ret = GmcChangeAlmThreshold(g_stmt, AlarmId, 90, 80);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "raiseRatio:%d  clearRatio:%d\n", g_raiseRatio, g_clearRatio);

    // 获取告警数据，预期无告警且为告警清除状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_CLEARED, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);

    // 删表空间
    TestDropTablespaceAsync(tablespaceName);
    
    AW_FUN_Log(LOG_INFO, "============ 表空间删除后，查看Tsp信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

class TspAlarmAsync01_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TspAlarmAsync01_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=12\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAlarmAsync01_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("ipcrm -a");
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TspAlarmAsync01_test::SetUp()
{
    int ret;
    // 同步连接、异步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //设置阈值
    ret = GmcChangeAlmThreshold(g_stmt, GMC_ALARM_SHM_USED_INFO, 90, 80);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAlarmAsync01_test::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 118 异步创建tsp1和tsp2，分别写满tsp1、tsp2和public，第一次获取告警数据，预期告警且为告警激活状态；
//     删除tsp1，第二次获取告警数据，预期告警且为无告警状态；删除tsp2，第三次获取告警数据，预期无告警且为无告警状态；
//     删除public的数据，第四次获取告警数据，预期没有告警且为无告警状态。（非交互事务，public是否将undo的空间写满）
TEST_F(TspAlarmAsync01_test, Resilience_010_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";
    AsyncUserDataT userData = {0};

    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName1);
    testCreateTablespaceAsync(tablespaceName2);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel3
    readJanssonFile("./schemaFile/003.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 默认表空间 simpleLabel1
    readJanssonFile("./schemaFile/001_public.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    TestInsertFull(g_vertexLabelName2);
    // 向simpleLabel3中写数据
    TestInsertFull(g_vertexLabelName3);
    // 向simpleLabel1中写数据
    TestInsertFull(g_vertexLabelName1);

    AW_FUN_Log(LOG_INFO, "============ 写满tsp1、tsp2和默认public，查看告警信息 ============\n");
    TspInfo();

    // 获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();

    // 删除tsp1中的数据
    TestDeleteVertexAsync(g_vertexLabelName2);
    
    AW_FUN_Log(LOG_INFO, "============ 删除tsp1的数据后，查看告警信息 ============\n");
    // 获取告警数据，预期告警且为无告警状态；告警状态查询一次后，阈值不修改或者数据不减少，其后面的查询都会被置为空
    // tsp1虽然数据被清空，但是tsp2的数据和public的数据还是满的，所以告警还是触发，只是再次查询时、状态为空
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);  // 告警依然触发
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);  // 但是二次查询、状态被置空
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 删除tsp2的数据
    TestDeleteVertexAsync(g_vertexLabelName3);
    
    AW_FUN_Log(LOG_INFO, "============ 删除tsp2的数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为告警解除状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName1);

    AW_FUN_Log(LOG_INFO, "============ 删除public数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为告警解除状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_CLEARED, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表空间
    TestDropTablespaceAsync(tablespaceName1);
    TestDropTablespaceAsync(tablespaceName2);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    AW_FUN_Log(LOG_STEP, "test end.");
}

using namespace std;
class TspAlarmAsync02_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TspAlarmAsync02_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh");
    // 修改配置项将事务级别改为乐观事务
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\" ");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=12\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAlarmAsync02_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("ipcrm -a");
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TspAlarmAsync02_test::SetUp()
{
    int ret;
    AsyncUserDataT userData = {0};
    // 同步连接、异步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //设置阈值
    ret = GmcChangeAlmThreshold(g_stmt, GMC_ALARM_SHM_USED_INFO, 90, 80);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAlarmAsync02_test::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 119 异步创建tsp1和tsp2，分别写满tsp1、tsp2和public，第一次获取告警数据，预期告警且为告警激活状态；
//     删除tsp1的数据，第二次获取告警数据，预期告警且为无告警状态；删除tsp2的数据，第三次获取告警数据，预期无告警且为无告警状态；
//     删除public的数据，第四次获取告警数据，预期没有告警且为告警清除状态。开事务（交互事务，public是否将undo的空间写满）直到提交时才回收undo空间
TEST_F(TspAlarmAsync02_test, Resilience_010_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";
    AsyncUserDataT userData = {0};

    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName1);

    testCreateTablespaceAsync(tablespaceName2);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel3
    readJanssonFile("./schemaFile/003.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 默认表空间 simpleLabel1
    readJanssonFile("./schemaFile/001_public.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    uint32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data in table: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        index++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 向simpleLabel3中写数据
    index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data in table: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        index++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcTxConfigT g_trxConfig;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    g_trxConfig.readOnly = false;
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStartAsync(g_conn_async, &g_trxConfig, trans_start_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    // 向public中的simpleLabel1写数据
    uint32_t num = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = num;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = num + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f2Value = num + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f3Value = num + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data in table1: %d\n", num);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        num++;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    testGmcGetLastError(NULL);
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    AW_FUN_Log(LOG_INFO, "============ 写满tsp1、tsp2和默认public，查看告警信息 ============\n");
    TspInfo();

    // 获取告警数据，预期无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();

    // 删除tsp1中的数据
    ret = GmcDropVertexLabelAsync(g_stmt_async, "simpleLabel", drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_INFO, "============ 删除tsp1的数据后，查看告警信息 ============\n");
    // 获取告警数据，预期告警且为无告警状态；告警状态查询一次后，阈值不修改或者数据不减少，其后面的查询都会被置为空
    // tsp1虽然数据被清空，但是tsp2的数据和public的数据还是满的，所以告警还是触发，只是再次查询时、状态为空
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);  // 告警依然触发
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);  // 但是二次查询、状态被置空
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ 校验public的Tsp信息 ============\n");
    queryView("TABLESPACE_NAME: public", "INIT_SIZE: [4] MB", "MAX_SIZE: [4] MB",
        "CUR_USED_SIZE: [1] MB [480] KB [0] Byte", "USED_RATIO: 36.72%");
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 删除tsp2的数据
    ret = GmcDropVertexLabelAsync(g_stmt_async, "simpleLabel3", drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_INFO, "============ 删除tsp2的数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为告警解除状态；此时的public只有入少量数据，没有触发告警阈值
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_CLEARED, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    // 删表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "simpleLabel1", drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 删除public数据后，查看告警信息 ============\n");
    // 获取告警数据，预期无告警且为告警解除状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    sleep(g_delay);

    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);
    ret = CheckAlarmData(&test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表空间
    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName1, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName2, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ Tsp信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ HEAP信息查询 ============\n");
    HeapInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看alarmId及相关告警信息 ============\n");
    alarm();

    AW_FUN_Log(LOG_STEP, "test end.");
}
