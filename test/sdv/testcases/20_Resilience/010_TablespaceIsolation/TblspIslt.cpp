/*
--------------------------------------- 功能测试 25个 ---------------------------------------
1. 单namespace单tablespace
    049 默认namespace下创建tablespace1、通过视图查看表空间id，默认initSize、stepSize和maxSize的大小；建表写数据至大于4M，申请内存失败
    050 默认namespace下创建tablespace1、通过视图查看表空间id，默认initSize和stepSize，maxSize = 8；建表写数据至大于4M，申请内存成功 
    051 默认namespace下创建tablespace1、通过视图查看表空间id，并在里面创建1张表，删除tablespace1，预期删除失败 
    052 默认namespace下创建tablespace1、通过视图查看表空间id，并在里面创建1张表，删除表，删除tablespace1，预期删除成功 
    053 默认namespace下创建tablespace1、通过视图查看表空间id，并在里面创建1024张表，进行dml操作，删除表，删除tablespace1，预期删除成功
    054 默认namespace下创建tablespace1、通过视图查看表空间id，创建多种类型的表，yang表、kv表、vertex表和edge表，预期创建成功 
    055 默认namespace下创建tablespace1、通过视图查看表空间id，创建两张相同的vertex，预期第一次成功，第二次建表失败 
    056 默认namespace下创建tablespace、通过视图查看表空间id，创建vertex并写10000条数据，查看数据及内存；原地更新、不申请新的页，预期更新成功
    057 默认namespace下创建tablespace、通过视图查看表空间id，创建vertex并写满数据，查看数据及内存；大对象更新、申请新的页，预期更新失败
    058 默认namespace下创建tablespace、通过视图查看表空间id，创建vertex并写满，查看数据及内存状况；truncate数据，查看内存；再写满，再次查看数据及内存状况
    059 默认namespace下创建tablespace1、通过视图查看表空间id，创建两张相同的edge表，预期第一次成功，第二次建表失败
2. 多namespace单tablespace
    060 创建namespace1和namespace2，共同绑定tablespace1，删除namespace1，操作成功；删除namespace2，删除tablespace1，预期成功
    061 创建namespace1和namespace2，共同绑定tablespace1，删除namespace1，操作成功；删除tablespace1、失败，删除namespace2、成功
    062 创建namespace1和namespace2，共同绑定tablespace1，删除tablespace1，预期删除失败
    063 创建namespace1和namespace2，共同绑定tablespace1、通过视图查看表空间id，在tablespace1中建表、写数据、删表等，预期成功 
    064 创建namespace1和namespace2，分别绑定tablespace1、通过视图查看表空间id，创建两次两张相同的edge表属于不同namespace，预期第一次成功，第二次建表成功
    065 namespace1和namespace2都绑定tablespace1、通过视图查看表空间id，创建两次两张相同的vertex属于不同namespace，预期第一次成功，第二次建表成功
3. 单namespace多tablespace
    066 默认namespace下创建tablespace1和tablespace2、通过视图查看表空间id，分别创建vetex1和vertex2，在tablespace1中操作写满verex1、通过视图查看内存使用情况，tablespace2对vertex2进行写操作，互不影响
    067 默认namespace 下创建tsp1和tsp2，tsp1下使用yang表写满，在tsp2下操作yang表，互不影响
    068 默认namespace下创建tablespace1和tablespace2、通过视图查看表空间id，两个表空间分别创建edge表，edge表的源表和目的表不属于同一表空间，预期失败
    069 默认namespace下创建tablespace1和tablespace2、通过视图查看表空间id，两个表空间分别创建edge表，edge表的源表和目的表属于同一表空间，预期成功
    143 点表和边表不在同一表空间  2023-04-18  补充测试点  点和边不在同一表空间
    070 默认namespace下创建3个tsp，分别申请300，300，200；预期前两次成功，最后一次失败
4. 多namespace多tablespace
    071 默认namespace 下创建tsp1和tsp2，tsp1下使用kv表写满，在tsp2下操作kv表，互不影响
    072 namespace1绑定tablespace1、namespace2绑定tablespace2、通过视图查看表空间id，两个表空间分别创建相同的vertex1表，预期成功
    089 表升级，指定不存在的tsp，升级失败；不指定tsp，默认使用nsp绑定的tsp    
*/
#include "tablespace_isolation.h"

class TablespaceIsolation_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TablespaceIsolation_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TablespaceIsolation_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TablespaceIsolation_test::SetUp()
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "simpleLabel");
    GmcDropVertexLabel(g_stmt, "simpleLabel2");
    GmcDropVertexLabel(g_stmt, "simpleLabel3");
    
    GmcDropTablespace(g_stmt, "tsp1");
    GmcDropTablespace(g_stmt, "tsp2");
    GmcDropTablespace(g_stmt, "tsp3");
}

void TablespaceIsolation_test::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 049
// 默认namespace下创建tablespace1、通过视图查看表空间id，默认initSize、stepSize和maxSize的大小；建表写数据至大于4M，申请内存失败
TEST_F(TablespaceIsolation_test, Resilience_010_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 写数据
    int32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, simpleVertexLabelName, 0, 0, index - 1);

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);

    // 再写一条数据，预期失败
    ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f0Value = index;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f1Value = index + 1;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2Value = index + 2;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f3Value = index + 3;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050 默认namespace下创建tablespace1、通过视图查看表空间id，默认initSize和stepSize，maxSize = 8；建表写数据至大于4M，申请内存成功
TEST_F(TablespaceIsolation_test, Resilience_010_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 写数据
    int32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            break;
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, simpleVertexLabelName, 0, 0, index - 1);

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051 默认namespace下创建tablespace1、通过视图查看表空间id，并在里面创建1张表，删除tablespace1，预期删除失败
TEST_F(TablespaceIsolation_test, Resilience_010_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 未删表的情况下删tsp，预期删除失败，验证码？
    ret = GmcDropTablespace(g_stmt, tspName);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052 默认namespace下创建tablespace1、通过视图查看表空间id，并在里面创建1张表，删除表，删除tablespace1，预期删除成功
TEST_F(TablespaceIsolation_test, Resilience_010_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053
// 默认namespace下创建tablespace1、通过视图查看表空间id，并在里面创建1024张表，进行dml操作，删除表，删除tablespace1，预期删除成功
TEST_F(TablespaceIsolation_test, Resilience_010_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char vertexLabelName[80];
    uint32_t tableNum = 0;
    const char *tspName = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建1024张表
    ret = testGetTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    int vNum = 0;
    for (int i = 0; i < MAX_CMD_SIZE - tableNum; i++) {
        sprintf(vertexLabelName, "test%d", i);
        ret = GmcCreateVertexLabelWithName(g_stmt, g_vertexLabelJson, g_cfgJson, vertexLabelName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "print i:%d\n", i);
            break;
        }
        vNum++;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        free(g_vertexLabelJson);
        g_vertexLabelJson = NULL;
    }

    AW_FUN_Log(LOG_INFO , "=========================== Tsp Information ===========================\n");
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    for (int j = 0; j < vNum; j++) {
        sprintf(vertexLabelName, "test%d", j);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "print j:%d\n", j);
            break;
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认namespace下创建tablespace1、通过视图查看表空间id，创建多种类型的表，yang表、kv表、vertex表和edge表，预期创建成功
TEST_F(TablespaceIsolation_test, Resilience_010_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *kvtable = NULL;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    const char *namespace1 = "NamespaceA";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = tspName;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    // 1. yang表（vertex和edge）
    readJanssonFile("./schemaFile/listSameTsp.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    readJanssonFile("schemaFile/listEdgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);

    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    free(g_edgeLabelJson);
    g_vertexLabelJson = NULL;
    g_edgeLabelJson = NULL;

    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2. vertex表
    char *vertexLabelJson = NULL;
    readJanssonFile("./schemaFile/001.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertexLabelJson);
    vertexLabelJson = NULL;

    // 3. kv表
    ret = GmcKvCreateTable(g_stmt, g_kvTableName, g_kvConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    // 1. yang表
    ret = GmcDropEdgeLabel(g_stmt, g_edgeLabelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, (const char *)"public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2. vertex表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 3. kv表
    ret = GmcKvDropTable(g_stmt, g_kvTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055 默认namespace下创建tablespace1、通过视图查看表空间id，创建两张相同的vertex，预期第一次成功，第二次建表失败
TEST_F(TablespaceIsolation_test, Resilience_010_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056
// 默认namespace下创建tablespace、通过视图查看表空间id，创建vertex并写10000条数据，查看数据及内存；原地更新、不申请新的页，预期更新成功
TEST_F(TablespaceIsolation_test, Resilience_010_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName, 0, 0, 0);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 写数据
    int32_t index = 0;
    for (int32_t i = 0; i < 10000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, simpleVertexLabelName, 0, 0, index - 1);

    // 更新数据
    for (index; index > 0; index--) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "ip4_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键索引字段
        int32_t f0Value = 0;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置字段值
        f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to update data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_FUN_Log(LOG_INFO , "=========================== Tsp Information ===========================\n");
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057
// 默认namespace下创建tablespace、通过视图查看表空间id，创建vertex并写满数据，查看数据及内存；大对象更新、申请新的页，预期更新失败
TEST_F(TablespaceIsolation_test, Resilience_010_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName, 0, 0, 8);

    // 建表
    readJanssonFile("./schemaFile/largeObject.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 写数据
    int64_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "TestLargeObject", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint64_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT64, &f0Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint64_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint64_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT64, &f2Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }

    // 更新数据
    index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "TestLargeObject", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T35_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键索引字段
        uint64_t f0Value = 0;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT64, &f0Value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置字段值

        uint64_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint64_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT64, &f2Value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to update data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, "TestLargeObject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删tsp
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058
// 默认namespace下创建tablespace、通过视图查看表空间id，创建vertex并写满，查看数据及内存状况；truncate数据，查看内存；再写满，再次查看数据及内存状况
TEST_F(TablespaceIsolation_test, Resilience_010_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName, 4, 4, 4);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 写数据
    int32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, simpleVertexLabelName, 0, 0, index - 1);

    AW_FUN_Log(LOG_INFO , "=========================== Tsp Information ===========================\n");
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // truncate数据
    GmcResetStmt(g_stmt);

    ret = GmcTruncateVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO , "=========================== Tsp Information ===========================\n");
    TspInfo();
    char *curUsedSize2 = NULL, *useRatio2 = NULL, *maxSize2 = NULL;
    ret = GtExecSysviewCmd(&maxSize2, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize2, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio2, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize2, curUsedSize2, useRatio2);
    free(maxSize2);
    free(curUsedSize2);
    free(useRatio2);
    // 再次写满内存
    index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, simpleVertexLabelName, 0, 0, index - 1);

    AW_FUN_Log(LOG_INFO , "=========================== Tsp Information ===========================\n");
    TspInfo();
    char *curUsedSize3 = NULL, *useRatio3 = NULL, *maxSize3 = NULL;
    ret = GtExecSysviewCmd(&maxSize3, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize3, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio3, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize3, curUsedSize3, useRatio3);
    free(maxSize3);
    free(curUsedSize3);
    free(useRatio3);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059 默认namespace下创建tablespace1、通过视图查看表空间id，创建两张相同的edge表，预期第一次成功，第二次建边失败
TEST_F(TablespaceIsolation_test, Resilience_010_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";
    const char *simpleVertexLabelName2 = "simpleLabel2";

    // 创建表空间
    testCreateTablespace(tspName);

    // 建表
    // 表1 simpleLabel
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 表2 simpleLabel2
    readJanssonFile("./schemaFile/002.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 在simpleLabel和simpleLabel2之间建边
    readJanssonFile("./schemaFile/edgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 第二次建边失败
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    // 删表
    // 删边
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表 simpleLabel和simpleLabel2
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表空间tsp1
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 2. 多namespace单tablespace
// 060 创建namespace1和namespace2，共同绑定tablespace1、通过视图查看表空间id，删除namespace1，操作成功；删除namespace2，删除tablespace1，预期成功
TEST_F(TablespaceIsolation_test, Resilience_010_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *tspName = "tsp1";
    const char *namespaceUserName = (const char *)"abc";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = "tsp1";
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = "tsp1";
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp1和nsp2
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061
// 创建namespace1和namespace2，共同绑定tablespace1、通过视图查看表空间id，删除namespace1，操作成功；删除tablespace1、失败，删除namespace2、成功
TEST_F(TablespaceIsolation_test, Resilience_010_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp1
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除tsp失败，还被nsp2占用
    ret = GmcDropTablespace(g_stmt, tspName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);

    // 删除nsp2
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062 创建namespace1和namespace2，共同绑定tablespace1、通过视图查看表空间id，删除tablespace1，预期删除失败
TEST_F(TablespaceIsolation_test, Resilience_010_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删除tsp失败，tsp下还有表
    ret = GmcDropTablespace(g_stmt, tspName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp1
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除nsp2
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063
// 创建namespace1和namespace2，共同绑定tablespace1、通过视图查看表空间id，在tablespace1中建表、写数据、删表等，预期成功
TEST_F(TablespaceIsolation_test, Resilience_010_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 写数据
    int32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, simpleVertexLabelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, simpleVertexLabelName, 0, 0, index - 1);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp1
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除nsp2
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064
// 创建namespace1和namespace2，分别绑定tablespace1、通过视图查看表空间id，创建两次两张相同的edge表属于不同namespace，预期第一次成功，第二次建表成功
TEST_F(TablespaceIsolation_test, Resilience_010_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在namespace1下建edgeLabel
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表simpleLabel
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表simpleLabel2
    readJanssonFile("./schemaFile/002.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    readJanssonFile("./schemaFile/edgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    // 在namespace2下建edgeLabel
    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表simpleLabel
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表simpleLabel2
    readJanssonFile("./schemaFile/002.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    readJanssonFile("./schemaFile/edgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp1和nsp2
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065
// namespace1和namespace2都绑定tablespace1、通过视图查看表空间id，创建两次两张相同的vertex属于不同namespace，预期第一次成功，第二次建表成功
TEST_F(TablespaceIsolation_test, Resilience_010_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在namespace1下建表simpleLabel
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 在namespace2下建表simpleLabel
    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp1和nsp2
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 3. 单namespace多tablespace
// 066
// 默认namespace下创建tablespace1和tablespace2、通过视图查看表空间id，分别创建vetex1和vertex2，在tablespace1中操作写满verex1、通过视图查看内存使用情况，tablespace2对vertex2进行写操作，互不影响
TEST_F(TablespaceIsolation_test, Resilience_010_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";

    // 创建tsp1，tsp2
    testCreateTablespace(tspName1);
    testCreateTablespace(tspName2, 4, 4, 4);

    // 建表 simpleLabel
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel3
    readJanssonFile("./schemaFile/003.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    int32_t index = 0;
    while (true) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "simpleLabel", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        index++;
    }
    // 校验数据
    ReadData(g_stmt, "simpleLabel", 0, 0, index - 1);

    // 向simpleLabel3中写数据
    int32_t cnt = 0;
    for (int32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "simpleLabel3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = cnt;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f1Value = cnt + 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f2Value = cnt + 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f3Value = cnt + 3;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cnt++;
        if (cnt == 10) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", cnt);
            break;
        }
    }
    // 校验数据
    ReadData(g_stmt, "simpleLabel3", 0, 0, cnt - 1);
    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName1, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName1, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName1, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);

    char *curUsedSize2 = NULL, *useRatio2 = NULL, *maxSize2 = NULL;
    ret = GtExecSysviewCmd(&maxSize2, "V$CATA_TABLESPACE_INFO", tspName2, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize2, "V$CATA_TABLESPACE_INFO", tspName2, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio2, "V$CATA_TABLESPACE_INFO", tspName2, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp2", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize2, curUsedSize2, useRatio2);
    free(maxSize2);
    free(curUsedSize2);
    free(useRatio2);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067 默认namespace 下创建tsp1和tsp2，tsp1下使用yang表写满，在tsp2下操作yang表，互不影响
#ifdef FEATURE_YANG
TEST_F(TablespaceIsolation_test, Resilience_010_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    int ret;
    AsyncUserDataT data = {0};
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";
    
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn_async2 = NULL;
    GmcStmtT *stmt_async2 = NULL;
    ret = testGmcConnect(&conn_async2, &stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建表空间
    testCreateTablespace(tspName1, 4, 4, 4);
    testCreateTablespace(tspName2, 4, 4, 4);
    
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    TestBatchPrepare(conn_async, &batch);

    // 创建namespace1
    const char *namespace1 = "NamespaceA";
    const char *namespace2 = "NamespaceB";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = tspName1;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建namespace2
    GmcNspCfgT nspCfg2;
    nspCfg2.tablespaceName = tspName2;
    nspCfg2.namespaceName = namespace2;
    nspCfg2.userName = namespaceUserName;
    nspCfg2.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async2, &nspCfg2, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 建表
    readJanssonFile("./schemaFile/listSameTsp.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    readJanssonFile("schemaFile/listEdgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);

    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;
    insertRequestCtx.insertCb = insert_vertex_callback;

    ret = GmcCreateVertexLabelAsync(stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    ret = GmcCreateEdgeLabelAsync(stmt_async, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    ret = GmcUseNamespaceAsync(g_T0T2_stmt, namespace1, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // tsp1下的yang表写数据
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t num = 0;
    while (true) {
        // 插入T0，对应根节点
        ret = testGmcPrepareStmtByLabelName(stmt_async, "T0", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t f0 = 1;
        ret = testYangSetField(stmt_async, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_async, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &num, sizeof(num));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(
            g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "insert %d num\n", num);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, data.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        num++;
    }
    // 提交事务
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_async2, namespace2, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // tsp2下的yang表写数据
    // 建表
    readJanssonFile("./schemaFile/listSameTsp2.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    readJanssonFile("schemaFile/listEdgeLabel2.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);

    ret = GmcCreateVertexLabelAsync(stmt_async2, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    ret = GmcCreateEdgeLabelAsync(stmt_async2, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    ret = GmcUseNamespaceAsync(g_T0T2_stmt, namespace2, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    TestBatchPrepare(conn_async2, &batch);
    ret = TestTransStartAsync(conn_async2, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tsp2的yang表写10条数据
    for (int32_t i = 0; i < 10; i++) {
        // 插入T0，对应根节点
        ret = testGmcPrepareStmtByLabelName(stmt_async2, "T02", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_async2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t f0_value = 1;
        ret = testYangSetField(stmt_async2, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T02::T2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_async2, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
    // 提交事务
    ret = TestTransCommitAsync(conn_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 删表
    testClearNsp(stmt_async, namespace1);

    ret = GmcUseNamespaceAsync(stmt_async2, namespace2, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    testClearNsp(stmt_async2, namespace2);

    ret = GmcDropNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(stmt_async2, namespace2, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);

    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async2, stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
#endif

// 068
// 默认namespace下创建tablespace1和tablespace2、通过视图查看表空间id，两个表空间分别创建edge表，edge表的源表和目的表不属于同一表空间，预期失败
TEST_F(TablespaceIsolation_test, Resilience_010_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";
    char *vertexLabelJson = NULL;
    char *edgeLabelJson = NULL;

    // 创建表空间
    testCreateTablespace(tspName1, 4, 4, 4);
    testCreateTablespace(tspName2, 4, 4, 4);

    // 建表 simpleLabel  tsp1
    readJanssonFile("./schemaFile/001.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertexLabelJson);
    vertexLabelJson = NULL;
    
    // 建表 simpleLabel2  tsp1
    readJanssonFile("./schemaFile/002.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertexLabelJson);
    vertexLabelJson = NULL;

    // 建表 simpleLabel3  tsp2
    readJanssonFile("./schemaFile/003.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertexLabelJson);
    vertexLabelJson = NULL;

    // 建表 simpleLabel4  tsp2
    readJanssonFile("./schemaFile/004.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertexLabelJson);
    vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", "MAX_SIZE: [4] MB",
        "CUR_USED_SIZE: [0] MB [0] KB [0] Byte", "USED_RATIO: 0.00%");
    queryView("TABLESPACE_NAME: tsp2", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", "MAX_SIZE: [4] MB",
        "CUR_USED_SIZE: [0] MB [0] KB [0] Byte", "USED_RATIO: 0.00%");
        
    // tsp1的simpleLabel和tsp2的simpleLabel3建边edgeLabel3，预期失败
    readJanssonFile("./schemaFile/edgeLabel3.gmjson", &edgeLabelJson);
    ASSERT_NE((void *)NULL, edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    free(edgeLabelJson);
    edgeLabelJson = NULL;

    // tsp1的simpleLabel2和tsp2的simpleLabel4建边edgeLabel4，预期失败
    readJanssonFile("./schemaFile/edgeLabel4.gmjson", &edgeLabelJson);
    ASSERT_NE((void *)NULL, edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    free(edgeLabelJson);
    edgeLabelJson = NULL;

    // 删表
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel3");
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel4");

    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069
// 默认namespace下创建tablespace1和tablespace2、通过视图查看表空间id，两个表空间分别创建edge表，edge表的源表和目的表属于同一表空间，预期成功
TEST_F(TablespaceIsolation_test, Resilience_010_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";
    testCreateTablespace(tspName1, 4, 4, 4);
    testCreateTablespace(tspName2, 4, 4, 4);

    // 建表 simpleLabel  tsp1
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel2  tsp1
    readJanssonFile("./schemaFile/002.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel3  tsp2
    readJanssonFile("./schemaFile/003.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel4  tsp2
    readJanssonFile("./schemaFile/004.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // tsp1下建边edgeLabel
    readJanssonFile("./schemaFile/edgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    // tsp2下建边edgeLabel2
    readJanssonFile("./schemaFile/edgeLabel2.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    // 查看表空间
    TspInfo();
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", "MAX_SIZE: [4] MB",
        "CUR_USED_SIZE: [0] MB [0] KB [0] Byte", "USED_RATIO: 0.00%");
    queryView("TABLESPACE_NAME: tsp2", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", "MAX_SIZE: [4] MB",
        "CUR_USED_SIZE: [0] MB [0] KB [0] Byte", "USED_RATIO: 0.00%");

    // 删边、删表
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt, "edgelabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 143 点表和边表不在同一表空间
TEST_F(TablespaceIsolation_test, Resilience_010_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";
    testCreateTablespace(tspName1, 4, 4, 4);
    testCreateTablespace(tspName2, 4, 4, 4);

    // 建表 simpleLabel  tsp1
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel2  tsp1
    readJanssonFile("./schemaFile/002.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 电表在tsp1下  边表在tsp2  建边edgeLabel
    readJanssonFile("./schemaFile/edgeLabel5.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);
    ret = GmcCreateEdgeLabel(g_stmt, g_edgeLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;


    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 070 默认namespace下创建3个tsp，分别申请400，400，400；预期前两次成功，最后一次失败
TEST_F(TablespaceIsolation_test, Resilience_010_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";
    const char *tspName3 = "tsp3";
    // 系统增加ts-omu-yang的表空间，32M；

#if defined RUN_INDEPENDENT
    testCreateTablespace(tspName1, 0, 0, 400);
    testCreateTablespace(tspName2, 0, 0, 400);
#elif defined ENV_RTOSV2X
    // 前两次每个tsp各申请8M，iot只分配了32M共享内存，ap设备public-tsp占用约10M
    testCreateTablespace(tspName1, 0, 0, 8);
    testCreateTablespace(tspName2, 0, 0, 8);
#else
    testCreateTablespace(tspName1, 0, 0, 300);  // AC设备maxSeMem=2048
    testCreateTablespace(tspName2, 0, 0, 300);
#endif

    // 第三次申请400M失败，超出可用范围，1020M
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tspName3;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
#if defined RUN_INDEPENDENT
    tspCfg.maxSize = 400;
#elif defined ENV_RTOSV2X
    tspCfg.maxSize = 20;  // soho设备maxSeMem调整到68M
#else
    tspCfg.maxSize = 800;
#endif
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret = GmcCreateTablespace(g_stmt, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 4. 多namespace多tablespace
// 071 nsp1绑定tsp1，在nsp1下建kv表，写满；nsp2绑定tsp2，在nsp2下建kv表，操作kv表
TEST_F(TablespaceIsolation_test, Resilience_010_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *kvtable = NULL;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";

    // 批量初始化
    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建表空间
    testCreateTablespace(tspName1, 4, 4, 4);
    testCreateTablespace(tspName2);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName1;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName2;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建kv表1
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char kvTableName[] = "student";
    ret = GmcKvCreateTable(g_stmt, kvTableName, g_kvConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  写数据
    int32_t num = 0;
    GmcKvTupleT kvInfo1 = {0};
    char key_set[1024];
    while (true) {
        sprintf(key_set, "zhangsan%d", num);
        int32_t value1 = num;
        kvInfo1.key = key_set;
        kvInfo1.keyLen = strlen(key_set);
        kvInfo1.value = &value1;
        kvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", num);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        num++;
    }

    // 创建kv表2
    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char kvTableName2[] = "student2";
    ret = GmcKvCreateTable(g_stmt, kvTableName2, g_kvConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  写数据
    GmcKvTupleT kvInfo2 = {0};
    char key_set2[1024];
    int32_t num2 = 0;
    for (int32_t i = 0; i < 10; i++) {
        sprintf(key_set2, "lisi%d", i);
        int32_t value2 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value2;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value2, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        num2++;
    }

    // 删除kv表
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, "student");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, "student2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 072 namespace1绑定tablespace1、namespace2绑定tablespace2、通过视图查看表空间id，两个表空间分别创建相同的vertex1表，预期成功
TEST_F(TablespaceIsolation_test, Resilience_010_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";
    const char *tspName1 = "tsp1";
    const char *tspName2 = "tsp2";
    // 创建表空间
    testCreateTablespace(tspName1, 4, 4, 4);
    testCreateTablespace(tspName2);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName1;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建nsp2
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName2;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tsp1下建表simpleLabel
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // tsp2下建表simpleLabel
    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("./schemaFile/001_1.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 删表
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName1);
    TestDropTablespace(tspName2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 089 表升级，指定不存在的tsp，升级失败；不指定tsp，默认使用nsp绑定的tsp
TEST_F(TablespaceIsolation_test, Resilience_010_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *namespace1 = (const char *)"userA";
    const char *namespaceUserName = (const char *)"abc";
    const char *simpleVertexLabelName = "simpleLabel";
    const char *tspName1 = "tsp1";

    // 创建表空间
    testCreateTablespace(tspName1);

    // 创建nsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tspName1;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 读已提交+悲观
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tsp1下建表simpleLabel
    ret = GmcUseNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_cfgJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 升级一次
    readJanssonFile("./schemaFile/001_up1.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcAlterVertexLabelWithName(g_stmt, g_vertexLabelJson, true, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 升级一次，不指定tsp，默认使用nsp绑定的tsp，预期成功
    readJanssonFile("./schemaFile/001_up2.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcAlterVertexLabelWithName(g_stmt, g_vertexLabelJson, true, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 升级一次，指定不存在的tsp，预期失败
    readJanssonFile("./schemaFile/001_up3.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcAlterVertexLabelWithName(g_stmt, g_vertexLabelJson, true, simpleVertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 查看表空间
    TspInfo();
    char *curUsedSize = NULL, *useRatio = NULL, *maxSize = NULL;
    ret = GtExecSysviewCmd(&maxSize, "V$CATA_TABLESPACE_INFO", tspName1, "| grep MAX_SIZE | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&curUsedSize, "V$CATA_TABLESPACE_INFO", tspName1, "|grep CUR_USED_SIZE|awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSysviewCmd(&useRatio, "V$CATA_TABLESPACE_INFO", tspName1, "| grep USED_RATIO | awk '{print $0}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB", "STEP_SIZE: [4] MB", maxSize, curUsedSize, useRatio);
    free(maxSize);
    free(curUsedSize);
    free(useRatio);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, "simpleLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删nsp
    ret = GmcDropNamespace(g_stmt, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表空间
    TestDropTablespace(tspName1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
