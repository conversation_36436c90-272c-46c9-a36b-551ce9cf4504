/*
表空间隔离 迭代三
=================================== 异步功能场景 ===================================
125 默认namespace下异步创建tablespace1、查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，并在里面创建1张表，删除tablespace1，预期删除失败
126 默认namespace下异步创建tablespace1、查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，并在里面创建1张表，删除表，删除tablespace1，预期删除成功
127 yang表的json中，每个表都增加相同的tablespace字段，查看tablespace、STORAGE_EDGE_LABEL_STAT和STORAGE_HEAP_STAT视图并校验tsp中的字段值，预期创建成功

128 创建namespace1和namespace2，共同绑定异步创建的tsp1，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，删除namespace1，操作成功；删除namespace2，删除tsp1，预期成功
129 创建namespace1和namespace2，共同绑定异步创建的tsp1，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，删除namespace1，操作成功；删除tsp1、失败，删除namespace2、成功
130 创建namespace1和namespace2，共同绑定异步创建的tsp1，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，删除tsp1，预期删除失败
131 创建namespace1和namespace2，共同绑定异步创建的tsp1、查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，通过视图查看表空间id，在ts1中建表、写数据、删表等，预期成功

132 默认namespace下异步创建tablespace1和tablespace2、分别创建vetex1和vertex2，在tablespace1中操作写满verex1、通过视图查看内存使用情况，tablespace2对vertex2进行写操作，互不影响，
    查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值
133 默认namespace 下异步创建tsp1和tsp2，tsp1下使用yang表写满，在tsp2下操作yang表，互不影响，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值

134 默认namespace 下异步创建tsp1和tsp2，tsp1下使用kv表写满，在tsp2下操作kv表，互不影响，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值
135 namespace1绑定异步创建的tablespace1、namespace2绑定异步创建的tablespace2、查看tablespace视图，两个表空间分别创建相同的vertex1表，预期成功
*/
#include "tablespace_isolation.h"

class TspAsync_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TspAsync_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAsync_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TspAsync_test::SetUp()
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "simpleLabel");
    GmcDropVertexLabel(g_stmt, "simpleLabel2");
    GmcDropVertexLabel(g_stmt, "simpleLabel3");
    
    GmcDropTablespace(g_stmt, "tsp1");
    GmcDropTablespace(g_stmt, "tsp2");
    GmcDropTablespace(g_stmt, "tsp3");
}

void TspAsync_test::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 125 默认namespace下异步创建tablespace1、查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，并在里面创建1张表，删除tablespace1，预期删除失败
TEST_F(TspAsync_test, Resilience_010_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName, 4, 4, 4);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");

    // HEAP信息
    HeapInfo();

    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);
    TestDropTablespaceAsync(tablespaceName);
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 126 默认namespace下异步创建tablespace1、查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，并在里面创建1张表，删除表，删除tablespace1，预期删除成功
TEST_F(TspAsync_test, Resilience_010_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";

    testCreateTablespaceAsync(tablespaceName, 4, 4, 4);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");

    // HEAP信息
    HeapInfo();

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);

    // 删表空间
    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 127 yang表的json中，每个表都增加相同的tablespace字段，查看tablespace、STORAGE_EDGE_LABEL_STAT和STORAGE_HEAP_STAT视图并校验tsp中的字段值，预期创建成功
TEST_F(TspAsync_test, Resilience_010_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    void *kvtable = NULL;
    const char *tablespaceName = "tsp1";
    const char *simpleVertexLabelName = "simpleLabel";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName);
    
    const char *namespace1 = "NamespaceA";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 建表
    // 1. yang表（vertex和edge）
    readJanssonFile("./schemaFile/listSameTsp.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    readJanssonFile("schemaFile/listEdgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;
    
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    ret = GmcUseNamespaceAsync(g_stmt_async, (const char *)"public", use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 2. vertex表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 3. kv表
    ret = GmcKvCreateTableAsync(g_stmt_async, g_kvTableName, g_kvConfigJson, create_kv_table_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "=========== 查看STORAGE_EDGE_LABEL_STAT ===========\n");
    StorageEdgeLabelInfo();
    AW_FUN_Log(LOG_INFO, "=========== 查看TABLESPACE_INFO ===========\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=========== 查看HEAP_INFO ===========\n");
    HeapInfo();

    // 删表
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 1. yang表，先删除边、再删点
    ret = GmcDropEdgeLabelAsync(g_stmt_async, g_edgeLabelName1, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName2, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName3, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    ret = GmcUseNamespaceAsync(g_stmt_async, (const char *)"public", use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 2. vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, simpleVertexLabelName, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 3. kv表
    ret = GmcKvDropTableAsync(g_stmt_async, g_kvTableName, drop_kv_table_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 删表空间
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 128 创建namespace1和namespace2，共同绑定异步创建的tsp1，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，删除namespace1，操作成功；
// 删除namespace2，删除tsp1，预期成功;  流程： 创建表空间 --- 创建2个nanespace并绑定tsp1 -- 删除namespace1、2 -- 删除tsp1
TEST_F(TspAsync_test, Resilience_010_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName, 4, 4, 4);

    queryView("TABLESPACE_NAME: tsp1", "INIT_SIZE: [4] MB","USED_RATIO: 0.00%");

    // 创建namespace1绑定tsp1
    TestCreateNspAsync(namespace1, tablespaceName);

    // 创建namespace2绑定tsp1
    TestCreateNspAsync(namespace2, tablespaceName);

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");
    AW_FUN_Log(LOG_INFO, "=========== 查看HEAP_INFO ===========\n");
    HeapInfo();

    // 删namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 129 创建namespace1和namespace2，共同绑定异步创建的tsp1，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，删除namespace1，操作成功；删除tsp1、失败，删除namespace2、成功
TEST_F(TspAsync_test, Resilience_010_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName, 4, 4, 4);

    // 创建namespace1绑定tsp1
    TestCreateNspAsync(namespace1, tablespaceName);

    // 创建namespace2绑定tsp1
    TestCreateNspAsync(namespace2, tablespaceName);

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");
    AW_FUN_Log(LOG_INFO, "=========== 查看HEAP_INFO ===========\n");
    HeapInfo();

    // 删namespace
    TestDropNspAsync(namespace2);

    // 删tsp、失败，还有表空间绑定
    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删nsp2
    TestDropNspAsync(namespace1);

    // 删tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 130 创建namespace1和namespace2，共同绑定异步创建的tsp1，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，删除tsp1，预期删除失败
TEST_F(TspAsync_test, Resilience_010_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName, 4, 4, 4);

    // 创建namespace1绑定tsp1
    TestCreateNspAsync(namespace1, tablespaceName);

    // 创建namespace2绑定tsp1
    TestCreateNspAsync(namespace2, tablespaceName);

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");
    AW_FUN_Log(LOG_INFO, "=========== 查看HEAP_INFO ===========\n");
    HeapInfo();

    // 删tsp、失败，表空间未解除绑定
    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    // 删namespace
    TestDropNspAsync(namespace1);

    // 删nsp2
    TestDropNspAsync(namespace2);

    // 删tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 131 创建namespace1和namespace2，共同绑定异步创建的tsp1、查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值，通过视图查看表空间id，在ts1中建表、写数据、删表等，预期成功
TEST_F(TspAsync_test, Resilience_010_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *namespaceUserName = (const char *)"abc";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName, 4, 4, 4);

    // 创建namespace1绑定tsp1
    TestCreateNspAsync(namespace1, tablespaceName);

    // 创建namespace2绑定tsp1
    TestCreateNspAsync(namespace2, tablespaceName);

    // 建表
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 写数据
    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;
    int32_t cnt = 0;
    for (int32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelName2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f0Value = cnt;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f1Value = cnt + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f2Value = cnt + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f3Value = cnt + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        cnt++;
        if (cnt == 10) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", cnt);
            break;
        }
    }

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [160] KB [0] Byte",
        "USED_RATIO: 3.91%");
    AW_FUN_Log(LOG_INFO, "=========== 查看HEAP_INFO ===========\n");
    HeapInfo();

    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);

    // 删namespace
    TestDropNspAsync(namespace2);
    TestDropNspAsync(namespace1);

    // 删tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 132 默认namespace下异步创建tablespace1和tablespace2、分别创建vetex1和vertex2，在tablespace1中操作写满verex1、通过视图查看内存使用情况，
// tablespace2对vertex2进行写操作，互不影响，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值
TEST_F(TspAsync_test, Resilience_010_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    GmcAsyncRequestDoneContextT requestCxt;
    requestCxt.insertCb = insert_vertex_callback;
    requestCxt.userData = &userData;

    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";

    // 创建tsp1，tsp2
    testCreateTablespaceAsync(tablespaceName1, 4, 4, 4);
    testCreateTablespaceAsync(tablespaceName2, 4, 4, 16);

    // 建表 simpleLabel
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 建表 simpleLabel3
    readJanssonFile("./schemaFile/003.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // 向simpleLabel中写数据
    TestInsertFull(g_vertexLabelName2);

    // 向simpleLabel3中写数据
    uint32_t cnt = 0;
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "simpleLabel3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0Value = cnt;
        ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f1Value = cnt + 1;
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f2Value = cnt + 2;
        ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f3Value = cnt + 3;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCxt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        cnt++;
        if (cnt == 10) {
            AW_FUN_Log(LOG_INFO, "success to insert data: %d\n", cnt);
            break;
        }
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [4] MB [0] KB [0] Byte",
        "USED_RATIO: 100.00%");
    queryView("TABLESPACE_NAME: tsp2", "MAX_SIZE: [16] MB", "CUR_USED_SIZE: [0] MB [160] KB [0] Byte",
        "USED_RATIO: 0.98%");
    AW_FUN_Log(LOG_INFO, "=========== 查看HEAP_INFO ===========\n");
    HeapInfo();
    
    // 删表
    TestDeleteVertexAsync(g_vertexLabelName2);
    TestDeleteVertexAsync(g_vertexLabelName3);

    // 删表空间
    TestDropTablespaceAsync(tablespaceName1);
    TestDropTablespaceAsync(tablespaceName2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

#ifdef FEATURE_YANG
// 133 默认namespace 下异步创建tsp1和tsp2，tsp1下使用yang表写满，在tsp2下操作yang表，互不影响，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值
TEST_F(TspAsync_test, Resilience_010_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";

    GmcConnT *conn_async2 = NULL;
    GmcStmtT *stmt_async2 = NULL;
    ret = testGmcConnect(&conn_async2, &stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建tsp1，tsp2
    testCreateTablespaceAsync(tablespaceName1, 4, 4, 4);
    testCreateTablespaceAsync(tablespaceName2, 4, 4, 16);

    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_conn_async, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_conn_async, &batch);

    // 创建namespace1
    const char *namespace1 = "NamespaceA";
    const char *namespace2 = "NamespaceB";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = tablespaceName1;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建namespace2
    GmcNspCfgT nspCfg2;
    nspCfg2.tablespaceName = tablespaceName2;
    nspCfg2.namespaceName = namespace2;
    nspCfg2.userName = namespaceUserName;
    nspCfg2.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async2, &nspCfg2, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 建表
    readJanssonFile("./schemaFile/listSameTsp.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    readJanssonFile("schemaFile/listEdgeLabel.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    ret = GmcUseNamespaceAsync(g_T0T2_stmt, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // tsp1下的yang表写数据

    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t num = 0;
    while (true) {
        // 插入T0，对应根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "T0", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t f0 = 1;
        ret = testYangSetField(g_stmt_async, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &num, sizeof(num));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(
            g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &num, sizeof(num), GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "insert %d num\n", num);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        num++;
    }
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_async2, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // tsp2下的yang表写数据
    // 建表
    readJanssonFile("./schemaFile/listSameTsp2.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    readJanssonFile("schemaFile/listEdgeLabel2.gmjson", &g_edgeLabelJson);
    ASSERT_NE((void *)NULL, g_edgeLabelJson);

    ret = GmcCreateVertexLabelAsync(stmt_async2, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    ret = GmcCreateEdgeLabelAsync(stmt_async2, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeLabelJson);
    g_edgeLabelJson = NULL;

    ret = GmcUseNamespaceAsync(g_T0T2_stmt, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 启动事务
    TestBatchPrepare(conn_async2, &batch);
    ret = TestTransStartAsync(conn_async2, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tsp2的yang表写10条数据
    for (int32_t i = 0; i < 10; i++) {
        // 插入T0，对应根节点
        ret = testGmcPrepareStmtByLabelName(stmt_async2, "T02", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_async2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t f0_value = 1;
        ret = testYangSetField(stmt_async2, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T02::T2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_async2, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    }

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
    // 提交事务
    ret = TestTransCommitAsync(conn_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 删表
    testClearNsp(g_stmt_async, namespace1);
    
    ret = GmcUseNamespaceAsync(stmt_async2, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // tsp2的表
    testClearNsp(stmt_async2, namespace2);
    
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(stmt_async2, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 删除表空间
    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName1, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmt_async, tablespaceName2, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(conn_async2, stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

GmcConnT *g_conn_sync2 = NULL;
GmcConnT *g_conn_async2 = NULL; 
GmcStmtT *g_stmt_async2 = NULL;
GmcStmtT *g_stmt_sync2 = NULL;
class TspAsync_test02 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void TspAsync_test02::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);

    // 创建异步单线程epoll
    ret = createEpollOneThread();
    EXPECT_EQ(GMERR_OK, ret);
}

void TspAsync_test02::TearDownTestCase()
{
    closeEpollOneThread();
}

void TspAsync_test02::SetUp()
{
    int ret;
    // 创建异步单线程连接
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    ret = TestYangGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建单线程同步连接
    ret = TestYangGmcConnect(&g_conn_sync2, &g_stmt_sync2, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspAsync_test02::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync2, g_stmt_sync2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 134 默认namespace 下异步创建tsp1和tsp2，tsp1下使用kv表写满，在tsp2下操作kv表，互不影响，查看tablespace和STORAGE_HEAP_STAT视图并校验tsp中的字段值
TEST_F(TspAsync_test02, Resilience_010_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    void *kvtable = NULL;
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";

    // 批量初始化
    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async2, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建表空间tsp1，tsp2
    GmcTspCfgT tspCfg1;
    tspCfg1.tablespaceName = "tsp1";
    tspCfg1.initSize = 0;
    tspCfg1.stepSize = 0;
    tspCfg1.maxSize = 4;

    ret = GmcCreateTablespaceAsync(g_stmt_async2, &tspCfg1, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcTspCfgT tspCfg2;
    tspCfg2.tablespaceName = "tsp2";
    tspCfg2.initSize = 0;
    tspCfg2.stepSize = 0;
    tspCfg2.maxSize = 4;

    ret = GmcCreateTablespaceAsync(g_stmt_async2, &tspCfg2, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建namespace1绑定tsp1
    GmcNspCfgT nspCfg1;
    nspCfg1.namespaceName = namespace1;
    nspCfg1.userName = (const char *)"abc";
    nspCfg1.tablespaceName = tablespaceName1;
    nspCfg1.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 悲观+读已提交

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async2, &nspCfg1, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    // 创建namespace2绑定tsp2
    GmcNspCfgT nspCfg2;
    nspCfg2.namespaceName = namespace2;
    nspCfg2.userName = (const char *)"abc";
    nspCfg2.tablespaceName = tablespaceName2;
    nspCfg2.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 悲观+读已提交

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async2, &nspCfg2, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    // 创建kv表1
    ret = GmcUseNamespaceAsync(g_stmt_async2, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    char kvTableName[] = "student";
    ret = GmcKvCreateTableAsync(g_stmt_async2, kvTableName, g_kvConfigJson, create_kv_table_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async2, kvTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    //  写数据
    GmcKvTupleT kvInfo1 = {0};
    char key_set[1024];
    int dataNum = 1000;
    for (int32_t i = 0; i < dataNum; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value1 = i;
        kvInfo1.key = key_set;
        kvInfo1.keyLen = strlen(key_set);
        kvInfo1.value = &value1;
        kvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async2, key_set, strlen(key_set), &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(dataNum, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, userData.succNum);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建kv表2
    ret = GmcUseNamespaceAsync(g_stmt_async2, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    char kvTableName2[] = "student2";
    ret = GmcKvCreateTableAsync(g_stmt_async2, kvTableName2, g_kvConfigJson, create_kv_table_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async2, kvTableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  写数据
    GmcKvTupleT kvInfo2 = {0};
    char key_set2[1024];
    int32_t num2 = 0;
    for (int32_t i = 0; i < dataNum; i++) {
        sprintf(key_set2, "lisi%d", i);
        int32_t value2 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value2;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_async2, key_set2, strlen(key_set2), &value2, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        num2++;
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(dataNum, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, userData.succNum);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删除kv表
    ret = GmcUseNamespaceAsync(g_stmt_async2, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcKvDropTableAsync(g_stmt_async2, "student", drop_kv_table_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));    

    ret = GmcUseNamespaceAsync(g_stmt_async2, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcKvDropTableAsync(g_stmt_async2, "student2", drop_kv_table_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删namespace
    ret = GmcDropNamespaceAsync(g_stmt_async2, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    ret = GmcDropNamespaceAsync(g_stmt_async2, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删表空间
    ret = GmcDropTablespaceAsync(g_stmt_async2, tablespaceName2, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    ret = GmcDropTablespaceAsync(g_stmt_async2, tablespaceName1, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

// 135 namespace1绑定异步创建的tablespace1、namespace2绑定异步创建的tablespace2、查看tablespace视图，两个表空间分别创建相同的vertex1表，预期成功
TEST_F(TspAsync_test, Resilience_010_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *namespace1 = (const char *)"userA";
    const char *namespace2 = (const char *)"userB";
    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";
    
    // 创建表空间
    testCreateTablespaceAsync(tablespaceName1, 4, 4, 4);
    testCreateTablespaceAsync(tablespaceName2, 4, 4, 4);

    // 创建nsp1、nsp2
    TestCreateNspAsync(namespace1, tablespaceName1);
    TestCreateNspAsync(namespace2, tablespaceName2);

    // tsp1下建表simpleLabel
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    readJanssonFile("./schemaFile/001.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    // tsp2下建表simpleLabel
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    readJanssonFile("./schemaFile/001_1.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback,
        &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    AW_FUN_Log(LOG_INFO, "=================== 查看tsp信息 ===================\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "=================== 校验tsp信息 ===================\n");
    queryView("TABLESPACE_NAME: tsp1", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");
    queryView("TABLESPACE_NAME: tsp2", "MAX_SIZE: [4] MB", "CUR_USED_SIZE: [0] MB [0] KB [0] Byte",
        "USED_RATIO: 0.00%");

    // nsp1删表
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    TestDeleteVertexAsync(g_vertexLabelName2);

    // nsp2删表
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    TestDeleteVertexAsync(g_vertexLabelName2);

    // 删namespace
    TestDropNspAsync(namespace1);
    TestDropNspAsync(namespace2);

    // 删表空间
    TestDropTablespaceAsync(tablespaceName1);
    TestDropTablespaceAsync(tablespaceName2);

    AW_FUN_Log(LOG_STEP, "test end.");
}
#endif
