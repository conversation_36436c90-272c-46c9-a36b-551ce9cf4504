#!/bin/bash

function usage()
{
    echo "usage: startServer.sh -n 1 -s 'usocket:/run/verona/unix_emserver'"
    exit 1
}

socket=0
num=0
if [ $# -eq 4 ]; then
    if [ "x$1" = "x-n" ]; then
        num="$2"
    else
        usage
        exit 1
    fi
    if [ "x$3" = "x-s" ]; then
        socket=" -s $4"
    else
        usage
        exit 1
    fi
else
    usage
    exit 1
fi


pidOfServer=""
isQemu=0
fileName="${TEST_HOME}/cfg/sysconfig.ini"
cfgFile=`grep -w sysGmdbCfg ${fileName}| awk '{print $3}'`

EnvType=`grep -w EnvType ${fileName}| awk '{print $3}'`
if [ "x${EnvType}" != "x0" -a "x${EnvType}" != "x1" -a "x${EnvType}" != "x2" -a "x${EnvType}" != "x3" ]; then
    echo "invalid EnvType:${EnvType}, it must been in [0, 1, 2, 3]. Please Check ${TEST_HOME}/cfg/sysconfig.ini"
    exit 2
fi
RunMode=`grep -w RunMode ${fileName}| awk '{print $3}'`
if [ "x${RunMode}" != "x0" -a "x${RunMode}" != "x1" -a "x${RunMode}" != "x2" ]; then
    echo "invalid RunMode:${RunMode}, it must been in [0, 1, 2]. Please Check ${TEST_HOME}/cfg/sysconfig.ini"
    exit 3
fi
toolPath=`grep -w toolPath ${fileName} | awk -F "= " '{print $NF}'`
if [ "x" = "x${toolPath}" ]; then
    toolPath="/usr/local/bin"
fi
clientServerSameProcess=`grep -w clientServerSameProcess ${fileName} | awk -F "= " '{print $NF}'`
if [ "x${clientServerSameProcess}" != "x0" -a "x${clientServerSameProcess}" != "x1" ]; then
    echo "invalid clientServerSameProcess:${clientServerSameProcess}, it must been in [0, 1]. Please Check ${TEST_HOME}/cfg/sysconfig.ini"
    exit 4
fi

function getPIDOfServer
{
    pidNum=$(ps ux|grep gmserver|grep -v grep|awk '{print $2}'|xargs echo|awk '{print NF}')
    echo "gmserver count: ${pidNum}"
    if [ X"${pidNum}" == X"${num}" ];then
        isServerStarted=0
    else
        isServerStarted=1
    fi
}

function waitServerAvailable
{
    getPIDOfServer
    if [ ${num} -ne ${pidNum} ]; then
        echo "Error: start service failure"
        return 1
    fi
    # 主备包拉起服务未配置主备属性，无法连接查询
    sleep 2
    echo "Info: start service done"
    return 0
}

function dealWithXsanLog()
{
    xsanAbsolute=`echo $1 | awk -F":" '{print $NF}' | awk -F"=" '{print $NF}'`
    xsanPath=`dirname ${xsanAbsolute}`
    xsanName=`basename ${xsanAbsolute}`
    if [ ! -d ${xsanPath} ]; then
        return 0
    fi
    t=`date '+%Y%m%d%H%M%S.%N'`
    fileList=`ls ${xsanPath}/${xsanName}.* 2>/dev/null`
    if [ $? -ne 0 ]; then
        return 0
    fi
    f3=""
    for f1 in ${fileList}
    do
        f2=`basename ${f1}`
        filePid=`echo ${f2} | awk -F"." '{print $NF}'`
        fileSize=`ls -l ${f1} | awk '{printf $5}'`
        if [ -f /proc/${filePid}/exe ]; then
            if [ ${fileSize} -lt 10 ]; then
                 continue   
            fi
            cp ${f1} ${xsanPath}/${t}_${f2}
            echo "" > ${f1}
        else
            mv ${f1} ${xsanPath}/${t}_${f2}
        fi
        f3="${f3} ${t}_${f2}"
    done
    echo "${currDir}  ${currName}  ${f3}" >> ${xsanPath}/xsan.txt
}

function backupXsanLog()
{
    if [ "x" = "x${currDir}" -o "x" = "x${currName}" ]; then
        return 0
    fi
    if [ "x" != "x${UBSAN_OPTIONS}" ]; then
        dealWithXsanLog ${UBSAN_OPTIONS}
        return 0
    fi
    if [ "x" != "x${TSAN_OPTIONS}" ]; then
        dealWithXsanLog ${TSAN_OPTIONS}
        return 0
    fi
    if [ "x" != "x${ASAN_OPTIONS}" ]; then
        dealWithXsanLog ${ASAN_OPTIONS}
        return 0
    fi
    return 0
}


function grantGmsysviewPrivs()
{
    userPolicyMode=`grep -w "^userPolicyMode" ${cfgFile} | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    gmsysview_allow_list=${TEST_HOME}/testcases/10_Security/004_ObjPrivs/schemaFile/allow_list/gmsysview_allow_list.gmuser
    gmsysview_policy=${TEST_HOME}/testcases/10_Security/004_ObjPrivs/schemaFile/policy/gmsysviewPolicy.gmpolicy
    if [ ${userPolicyMode} -eq 2 ]; then
            ${toolPath}/gmrule -c import_allowlist -f ${gmsysview_allow_list}  ${socket}
            ${toolPath}/gmrule -c import_policy  -f ${gmsysview_policy}  ${socket}
    fi
}

function record_corefile()
{
    # 统计core文件
    local his_file="$1"
    if [ ! -f ${his_file} ];then
        return 0
    fi
    last_command_dir=$(grep "autotest" ${his_file} | awk '/./{line=$0} END{print line}')
    if [ "${last_command_dir}" != "" ];then
        dir_t1=$(echo ${last_command_dir} | awk -F'source' '{print $1}')
        dir_t2=$(echo ${dir_t1} | awk -F'&&' '{print $1}')
        dir_t3=$(echo ${dir_t2} | awk '{print $NF}')
        if [ ${EnvType} -eq 0 ];then
            server_core=$(find ${TEST_HOME} -maxdepth 1 -name "core-*")
            test_core=$(find ${dir_t3} -name "core-*")
        else
            server_core=$(find /opt/vrpv8/home/<USER>"hcoredump*.gz")
            test_core=$(find /opt/vrpv8/home/<USER>"core*.gz")
        fi
        for c in ${server_core}
        do
            c_num=$(cat ${TEST_HOME}/gmdb_sdv_log/__core__.txt | grep $c | wc -l)
            if [ ${c_num} -eq 0 ];then
                echo "${c}" >> ${TEST_HOME}/gmdb_sdv_log/__core__.txt
                echo "#corefile of testsuit: ${dir_t3}; ls ${c}" | tee -a ${his_file}
            fi
        done
        for c in ${test_core}
        do
            c_num=$(cat ${TEST_HOME}/gmdb_sdv_log/__core__.txt | grep $c | wc -l)
            if [ ${c_num} -eq 0 ];then
                echo "${c}" >> ${TEST_HOME}/gmdb_sdv_log/__core__.txt
                echo "#corefile of testsuit: ${dir_t3}; ls ${c}" | tee -a ${his_file}
            fi
        done
    fi
}

self_pid=$$
parent_pid=$PPID
function record_history()
{
    if [ ${EnvType} -eq 0 -o ${EnvType} -eq 1 ]; then
        # SELF=$(ps -ef | grep $self_pid | grep -v $parent_pid | grep -v 'grep' | grep -v "ps \-ef")
        PARENT_CMD=$(ps -ef | grep $parent_pid | grep -v 'grep'  | awk '{ for (i=8; i<=NF; i++) {printf"%s", $i}; printf"\n" }' | grep -v -w "sh" | grep -v -w "\/bin\/bash")
        PARENT_COMMAND="${PARENT_CMD//--/ --}"
        #pppid=$(ps -o ppid= $parent_pid)
        pppid=$(ps -ef | grep $parent_pid | grep -v $self_pid | grep -v 'grep' | grep -v "ps \-ef" | awk '{printf $3}')
        PARENT_OF_PARENT_CMD=$(ps -ef | grep $pppid | grep -v 'grep' | grep -v "ps \-ef" | grep autotest_env | grep "sh \-c" | awk '{ for (i=10; i<=NF; i++) {printf"%s ", $i}; printf"\n" }')
    else
        # hongmeng command 'ps' is different
        PARENT_CMD=$(ps -l -www | grep $parent_pid  | grep -v 'grep' | awk '{ for (i=11; i<=NF; i++) {printf"%s", $i}; printf"\n" }' | grep -v -w "sh" | grep -v -w "\/bin\/bash")
        PARENT_COMMAND="${PARENT_CMD//--/ --}"
        pppid=$(ps -l -www | grep $parent_pid | grep -v $self_pid | grep -v 'grep' | grep -v "ps \-www" | awk '{printf $4}')
        PARENT_OF_PARENT_CMD=$(ps -l -www | grep $pppid | grep -v 'grep' | grep -v "ps \-www" | grep autotest_env | grep "sh \-c" | awk '{ for (i=13; i<=NF; i++) {printf"%s ", $i}; printf"\n" }')
    fi

    his_file="${TEST_HOME}/gmdb_sdv_log/start_history.sh" && [ ! -f ${his_file} ] && mkdir -p "${TEST_HOME}/gmdb_sdv_log" && touch ${his_file}
    PARENT_OF_PARENT_CMD="${PARENT_OF_PARENT_CMD//--/ --}"
    if [[ "X${PARENT_COMMAND}" != "X" ]] && [[ "X${PARENT_OF_PARENT_CMD}" != "X" ]];then
        # 统计core文件
        record_corefile "${his_file}"
        test_full_command="echo CI@["$(date "+%F %T")"]; ${PARENT_OF_PARENT_CMD} ; "
        echo "$test_full_command" | tee -a ${his_file}
    else
        his_file="${TEST_HOME}/gmdb_sdv_log/start_history.sh"
        record_corefile "${his_file}"
    fi
}
record_history

currDir=`pwd`
currProc=`ls -l /proc/${PPID}/exe | awk '{print $NF}' | awk -F"/" '{print $NF}'`
currName=`cat /proc/${PPID}/cmdline | tr -d '\0'`
forceStop=""
backupXsanLog
if [ -d ${TEST_HOME}/log ]; then
    mkdir -p ${TEST_HOME}/logback/$currProc
    cp -r ${TEST_HOME}/log ${TEST_HOME}/logback/$currProc/
fi

if [ ${RunMode} -eq 0 ]; then
    runPath=`grep -w serverLocator ${fileName} | awk -F":" '{print $NF}'`
    lockFile="`dirname ${runPath}`/GMDBV5_INSTANCE.lock"
    rm -f ${lockFile}
    if [ -d ${TEST_HOME}/log ]; then
        rm -rf ${TEST_HOME}/log
    fi
    prevPath=`pwd`
    cd ${TEST_HOME}
    gmserver_path=`which gmserver`
    exec_file="gmserver"
    if [ $num -eq 2 ]; then
        rm ${gmserver_path}_${num} -rf
        ln -s $gmserver_path ${gmserver_path}_${num}
        exec_file="gmserver_${num}"
    fi
    
    if [ "x" = "x${cfgFile}" ]; then
        $exec_file -b > /dev/null 2>&1
    else
        $exec_file -p ${cfgFile} -b > /dev/null 2>&1
    fi
    cd ${prevPath}
    sleep 0.5
elif [ ${EnvType} -eq 1 ]; then
    echo "[start.sh] Info: ae9700 do nothing"
elif [ ${EnvType} -eq 2 ]; then
    echo "[start.sh] Info: IOT do nothing"
fi

grantGmsysviewPrivs
waitServerAvailable

