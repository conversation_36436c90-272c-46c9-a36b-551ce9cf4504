/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: DB主备基础数据实时同步复制 - 10、交互并发(同步复制)
 * Author: lushiguang
 * Create: 2025-01-08
 */

#include "RtBackup.h"

int g_beginIndex = 0;
int g_endIndex = 1000;

class RtBackup11 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void StartEnvAndConn()
{
    int ret;
    // 主节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -m");  
    // 备节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -s");  

    // 配置主备
    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建连接 主节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_masterConn[i], &g_masterStmt[i], g_connServer);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 建连接 备节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_slaveConn[i], &g_slaveStmt[i], g_connServerSlave);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void RtBackup11::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    g_ignoreConflict = true;
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    ret = ChangeGmserverCfg((char *)"enableClusterHash", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"pageSize", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i + 1);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    StartEnvAndConn();
    AW_CHECK_LOG_BEGIN();
}

void RtBackup11::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    // 断开连接
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_masterConn[i], g_masterStmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_slaveConn[i], g_slaveStmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 001、建表并发（同表），验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 同表并发ddl，可能存在只写入了资源表，还没有同步到系统表，导致并发删表失败
    g_ddlExceptionCheck = true;
    int ret;
    // 同一张表
    g_endIndex = 1;
    int operateTime = 20;
    ret = AsyncDDLOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, operateTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncDDLOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, operateTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncDDLOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, operateTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    uint32_t labelType = 0;
    char tableName[30] = {0};
    (void)snprintf(tableName, sizeof(tableName), "%s_%d", g_tableName, 0);
    ret = GmcGetLabelTypeByName(g_slaveStmt[0], g_tableName, &labelType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_DUPLICATE_TABLE, GMERR_UNDEFINED_TABLE, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、建表并发（不同表），验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableCount = 150;
    int ret;
    // 不同表
    int operateTime = 1;
    ret = AsyncDDLOperate(g_masterStmt[0], g_tableName, 0, 50, operateTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncDDLOperate(g_masterStmt[1], g_tableName, 50, 100, operateTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncDDLOperate(g_masterStmt[2], g_tableName, 100, tableCount, operateTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    uint32_t labelType = 0;
    char tableName[30] = {0};
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s_%d", g_tableName, i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], g_tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、vertex表(同表)并发insert，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int expectCount = g_endIndex - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PRIMARY_KEY_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、vertex表(同表)并发insert，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, 0, 1000, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, 1000, 2000, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, 2000, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int expectCount = g_endIndex - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、vertex表(同表)并发update，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckUpdateFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、vertex表(同表)并发update，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, 0, 1000, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, 1000, 2000, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, 2000, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckUpdateFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、vertex表(同表)并发replace，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int expectCount = g_endIndex - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、vertex表(同表)并发replace，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, 0, 1000, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, 1000, 2000, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, 2000, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int expectCount = g_endIndex - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、vertex表(同表)并发merge，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckMergeFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckMergeFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、vertex表(同表)并发merge，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, 0, 1000, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, 1000, 2000, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, 2000, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckMergeFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckMergeFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、vertex表(同表)并发delete，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 100;
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、vertex表(同表)并发delete，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 100;
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, 0, 1000, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, 1000, 2000, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, 2000, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、vertex表(同表)update和delete并发，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 100;
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex + checkExtCount, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、vertex表(同表)update和delete并发，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_endIndex / 2, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex / 2, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckUpdateFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = g_endIndex / 2 - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、vertex表(同表)replace和delete并发，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 100;
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex + checkExtCount, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、vertex表(同表)replace和delete并发，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_endIndex / 2, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex / 2, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = g_endIndex / 2 - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、vertex表(同表)merge和delete并发，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 100;
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex + checkExtCount, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    int expectCount = 0;
    ret = CheckRecordCount(g_masterStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_endIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、vertex表(同表)merge和delete并发，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 3000;

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName, g_endIndex / 2, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex / 2, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckMergeFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckMergeFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex / 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = g_endIndex / 2 - g_beginIndex;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、vertex表(不同表)并发insert/replace/update，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_slaveStmt[1], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[2], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PRIMARY_KEY_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、vertex表(不同表)并发merge，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckMergeFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckMergeFixedTb(g_slaveStmt[1], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckMergeFixedTb(g_slaveStmt[2], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、vertex表(不同表)并发delete，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    int expectCount = 0;
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName2, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckRecordCount(g_slaveStmt[0], g_tableName3, g_beginIndex, g_endIndex, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、KV表(同表)并发set，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    g_endIndex = 5000;
    char *configJson = (char *)R"({
    "name": "system_info",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[2], kvTableName, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023、KV表(同表)并发set，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    g_endIndex = 5000;
    char *configJson = (char *)R"({
    "name": "system_info",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, 1500, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName, 1500, 3000, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[2], kvTableName, 3000, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、KV表(同表)并发remove，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 1000;
    int ret;
    g_endIndex = 5000;
    char *configJson = (char *)R"({
    "name": "system_info",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[2], kvTableName, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckCount(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount, checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、KV表(同表)并发remove，并发操作的数据无冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int checkExtCount = 1000;
    g_endIndex = 5000;
    char *configJson = (char *)R"({
    "name": "system_info",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, 1500, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName, 1500, 3000, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[2], kvTableName, 3000, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckCount(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount, checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026、KV表(同表)set和remove并发，并发操作的数据有冲突，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int checkExtCount = 1000;
    int ret;
    g_endIndex = 5000;
    char *configJson = (char *)R"({
    "name": "system_info",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_endIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027、KV表(同表)set和remove并发，并发操作的数据无冲突，验证主备数据 -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int checkExtCount = 1000;
    g_endIndex = 5000;
    char *configJson = (char *)R"({
    "name": "system_info",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, 2500, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName, 2500, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName, 2500, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, 2500, g_endIndex + checkExtCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int expectCount = g_endIndex + checkExtCount - 2500;
    ret = KvCheckCount(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex + checkExtCount, expectCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、KV表(不同表)并发set，验证主备数据 -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    g_endIndex = 5000;
    char *configJson1 = (char *)R"({
    "name": "system_info1",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    char *configJson2 = (char *)R"({
    "name": "system_info2",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName1 = (char *)"system_info1";
    char *kvTableName2 = (char *)"system_info2";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName1);
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName2);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName1, configJson1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName2, configJson2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName1, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName2, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckGet(g_masterStmt[0], kvTableName1, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_masterStmt[0], kvTableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName1, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_masterStmt[0], kvTableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029、KV表(不同表)并发remove，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    g_endIndex = 5000;
    char *configJson1 = (char *)R"({
    "name": "system_info1",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    char *configJson2 = (char *)R"({
    "name": "system_info2",
    "max_record_count": 100000000,
    "replication":1,
    "configs":
    [
        {
        "name": "vs_id",
        "type": "uint32"
        }
    ]
    })";
    // 主板建表写数据
    char *kvTableName1 = (char *)"system_info1";
    char *kvTableName2 = (char *)"system_info2";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName1);
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName2);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName1, configJson1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName2, configJson2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName1, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName1, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncKvTbOperate(g_masterStmt[1], kvTableName2, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = KvCheckCount(g_masterStmt[0], kvTableName1, g_beginIndex, g_endIndex, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckCount(g_slaveStmt[0], kvTableName2, g_beginIndex, g_endIndex, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_masterStmt[0], kvTableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_masterStmt[0], kvTableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030、多个同表同类操作事务(大事务)，并发提交，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 大事务
    g_endIndex = 5000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[3], g_masterStmt[3], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_LOCK_NOT_AVAILABLE, GMERR_TRANSACTION_ROLLBACK);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031、多个同表同类操作事务(小事务)，并发提交，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 小事务
    g_endIndex = 300;

    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[3], g_masterStmt[3], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_LOCK_NOT_AVAILABLE, GMERR_TRANSACTION_ROLLBACK);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032、多个不同表同类操作事务(大事务)，并发提交，验证主备数据 -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 大事务
    g_endIndex = 5000;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033、多个不同表同类操作事务(小事务)，并发提交，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 大事务
    g_endIndex = 300;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务

    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[1], g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTrx(g_masterConn[2], g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034、数据同步和GmcFlushData并发，验证主备数据  -- 正常同步
TEST_F(RtBackup11, Compute_001_001_11_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_endIndex = 5000;
    int ret;
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName2, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CommonCreateTable(g_masterStmt[0], g_tableName3, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[1], g_tableName2, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = AsyncFixedTbOperate(g_masterStmt[2], g_tableName3, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_masterStmt[3], NULL, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_masterStmt[4], NULL, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主、备板验证数据
    ret = CheckInsertFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_slaveStmt[1], g_tableName2, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[2], g_tableName3, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_masterStmt[0], g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PRIMARY_KEY_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

