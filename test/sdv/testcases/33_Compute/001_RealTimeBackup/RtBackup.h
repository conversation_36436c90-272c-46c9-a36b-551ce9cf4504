/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: RtBackup.h
 * Author: lushiguang
 * Create: 2025-01-08
 */
#ifndef RTBACKUP_H
#define RTBACKUP_H


#include "gtest/gtest.h"
#include "t_light.h"
#include "test_ha.h"

#define MAX_INSTANCE_COUNT 5
#define CONN_COUNT 5

char g_serverLocation[MAX_INSTANCE_COUNT][200];
bool g_ddlExceptionCheck = false;

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync = NULL;
GmcConnT *g_connList[MAX_INSTANCE_COUNT];
GmcStmtT *g_stmtList[MAX_INSTANCE_COUNT];

GmcConnT *g_masterConn[CONN_COUNT];
GmcStmtT *g_masterStmt[CONN_COUNT];

GmcConnT *g_slaveConn[CONN_COUNT];
GmcStmtT *g_slaveStmt[CONN_COUNT];

int g_instance0 = 0;
int g_instance1 = 1;
int g_instance2 = 2;
GmcTxConfigT g_trxConfig;
const char *g_url = "tcp:host=127.0.0.1,port=2233";

bool g_ignoreConflict = false;
bool g_ignoreTimeout = false;

#ifdef __cplusplus
extern "C" {
#endif

char g_pIndexName[] = "PrimaryKey";
char g_cond[] = "A0(int32)=%i{0}";
char *g_tableName = (char *)"lable1";
char *g_tableName2 = (char *)"lable2";
char *g_tableName3 = (char *)"lable3";

char g_dbFilePath[MAX_INSTANCE_COUNT][1024] = {0};

const char *g_config2 = (char *)R"(
    {
        "max_record_count": 100000000,
        "isFastReadUncommitted":0,
        "replication":2
    }
)";

const char *g_config1 = (char *)R"(
    {
        "max_record_count": 100000000,
        "isFastReadUncommitted":0,
        "replication":1
    }
)";

const char *g_config0 = (char *)R"(
    {
        "max_record_count": 100000000,
        "isFastReadUncommitted":0,
        "replication":0
    }
)";

char *g_Schema1 = (char *)R"(
    [{
    "version": "2.0", "type": "record", "name": "lable1",
    "fields": [
        { "name": "A0", "type": "int32"},
        { "name": "vrf_index", "type": "uint32"},
        { "name": "dest_ip_addr", "type": "uint32"},
        { "name": "mask_len", "type": "uint8"},
        { "name": "other", "type": "string", "nullable": true}
    ],
    "keys": [
        { "name": "PrimaryKey", "index": { "type": "primary" },
            "node": "lable1",
            "fields": [ "A0" ],
            "constraints": { "unique": true }
        }
    ]
}]
)";

#define MAX_TEST_TABLE_NAME_LEN 50
typedef enum OperateType {
    T_INSERT,
    T_REPLACE,
    T_UPDATE,
    T_MERGE,
    T_DELETE,
    T_KVSET,
    T_KVREMOVE,
    T_DDL,
    T_REPLACE_TRX
} TOperateTypeE;

typedef struct {
    GmcConnT *conn;
    GmcStmtT *stmt;
    char tableName[MAX_TEST_TABLE_NAME_LEN];
    int bIndex;
    int eIndex;
    int operateTime;
    TOperateTypeE type;
    bool isFixedTable;
    int id;
} OperateArgsT;

#define MAX_THREAD_COUNT 20

pthread_t g_thread[MAX_THREAD_COUNT];
int g_curUsedThCount = 0;
OperateArgsT g_operateArg[MAX_THREAD_COUNT];
bool g_rollBackFlag[MAX_THREAD_COUNT];

bool IsRollBack(int threadCount)
{
    for (int i = 0; i < threadCount; i++) {
        if (g_rollBackFlag[i] == false) {
            return false;
        }
    }
    return true;
}

int WaitReplicateFinish(int timeout = 3)
{
    sleep(timeout);
    return T_OK;
}

void AddReplicationCfg()
{
    (void)GtExecSystemCmd("sed -i '/enableReplication/d' %s", g_sysGMDBCfg);
    (void)GtExecSystemCmd("echo 'enableReplication=0' >> %s", g_sysGMDBCfg);
    (void)ChangeGmserverCfg((char *)"enableReplication", (char *)"1");
}


int HaConfig(const char *masterLocation, const char *slaveLocation, const char *url, int waitTimeout)
{
    int ret = GmcSetDbRole(masterLocation, GMC_DB_ROLE_MASTER, url);
    RETURN_IFERR(ret);
    ret = GmcSetDbRole(slaveLocation, GMC_DB_ROLE_SLAVE, url);
    RETURN_IFERR(ret);
    // 等待备可访问
    ret = GmcWait(slaveLocation, GMC_DB_STATUS_ACCESSIBLE, waitTimeout);
    RETURN_IFERR(ret);
    ret = GmcSlaveOnline(masterLocation, url);
    RETURN_IFERR(ret);
    // 等待主可同步备份
    ret = GmcWait(masterLocation, GMC_DB_STATUS_BACKUP_ENABLED, waitTimeout);
    return ret;
}

int StartEnvWithConfig(int num, int count, ...)
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    va_list varList;
    char tempCmd[200] = {0};
    const char *strKey;
    va_start(varList, count);
    char socket[200] = {0};
    char tempStr[200] = {0};
    for (int i = 0; i < count; i++) {
        strKey = va_arg(varList, const char *);
        (void)strcpy(tempStr, strKey);
        if (strstr(tempStr, "localLocatorListened")) {
            char *temp = strstr(tempStr, "=");
            (void)strcpy(socket, temp + 1);
        }
        (void)sprintf(tempCmd, "sh $TEST_HOME/tools/modifyCfg.sh \"%s\"", strKey);
        printf("tempcmd: %s\n", tempCmd);
        system(tempCmd);
    }
    va_end(varList);
    int ret = GtExecSystemCmd("sh startServer.sh -n %d -s '%s'", num, socket);
    return ret;
}

int DoAccess(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    char strValue[maxLen] = {0};
    (void)memset(strValue, 'a', maxLen - 1);
    strValue[maxLen - 1] = '\0';
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        uint32_t vr_id = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &vr_id, sizeof(int32_t));
        RETURN_IFERR(ret);
        uint32_t vrf_index = i + 2;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint32_t dest_ip_addr = i + 3;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint8_t mask_len = i % 127;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "other", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int TestGmcConnectLocator(GmcConnT **connOut, GmcStmtT **stmt = NULL, const char *serverLocator = NULL,
    int syncMode = 0, bool needEpoll = 1, EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL,
    const void *chanRingLen = NULL)
{
    int ret;

    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, serverLocator);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testMallocConnOptions failed, ret = %d.\n", ret);
        return ret;
    }

    ret = testGmcConnect(connOut, stmt, syncMode, needEpoll, epollReg, connName, chanRingLen, connOption);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testGmcConnect failed, ret = %d.\n", ret);
        testFreeConnOptions(connOption);
        return ret;
    }
    testFreeConnOptions(connOption);
    return ret;
}

int DDLTable(GmcStmtT *stmt, const char *lableName, int suffixStart, int suffixEnd, int operateTime)
{
    char *schema = NULL;
    int ret;
    readJanssonFile((char *)"./schema/var_schema.gmjson", &schema);
    if (schema == NULL) {
        return T_FAILED;
    }
    char tableName[30] = {0};
    for (int i = 0; i < operateTime; i++) {
        for (int j = suffixStart; j <= suffixEnd; j++) {
            (void)snprintf(tableName, sizeof(tableName), "%s_%d", lableName, j);
            ret = GmcCreateVertexLabelWithName(stmt, schema, g_config2, tableName);
            AW_FUN_Log(LOG_INFO, "[GmcCreateVertexLabelWithName] ret = %d.", ret);
            AW_MACRO_EXPECT_EQ_BOOL(true, (ret == T_OK || ret == GMERR_DUPLICATE_TABLE));
            ret = GmcDropVertexLabel(stmt, tableName);
            AW_FUN_Log(LOG_INFO, "[GmcDropVertexLabel] ret = %d.", ret);
            if (g_ddlExceptionCheck) {
                AW_MACRO_EXPECT_EQ_BOOL(true,
                    (ret == T_OK || ret == GMERR_UNDEFINED_TABLE || ret == GMERR_DATA_EXCEPTION));
            } else {
                AW_MACRO_EXPECT_EQ_BOOL(true, (ret == T_OK || ret == GMERR_UNDEFINED_TABLE));
            }
        }
    }
    free(schema);
    schema = NULL;
    return ret;
}

int InsertFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        int8_t a9 = i % 16;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_PARTITION, &a9, sizeof(int8_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int ReplaceFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 100000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 100001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 100002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        int8_t a9 = i % 16;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_PARTITION, &a9, sizeof(int8_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int UpdateFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 200000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 200001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 200002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int MergeFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_MERGE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 300000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 300001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 300002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_DATA_EXCEPTION) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int InsertVarVertex36k(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{0},
    "A2": %i{0},
    "A3": %i{0},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}i",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{333}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{333}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{333}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0},
    "s1": "%f{5000}c",
    "s2": "%f{5000}c",
    "s3": "%f{5000}c",
    "s4": "%f{5000}c",
    "s5": "%f{5000}c",
    "s6": "%f{5000}c",
    "s7": "%f{5000}c"
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}


int InsertVarVertex18k(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{0},
    "A2": %i{0},
    "A3": %i{0},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}i",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{3}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{3}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{3}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0},
    "s1": "%f{2}c",
    "s2": "%f{250}c",
    "s3": "%f{250}c",
    "s4": "%f{250}c",
    "s5": "%f{250}c",
    "s6": "%f{250}c",
    "s7": "%f{250}c"
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int InsertVarVertex2k(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{0},
    "A2": %i{0},
    "A3": %i{0},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}i",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0},
    "s1": "%f{300}c",
    "s2": "%f{300}c",
    "s3": "%f{300}c",
    "s4": "%f{300}c",
    "s5": "%f{300}c",
    "s6": "%f{300}c",
    "s7": "%f{300}c"
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int InsertVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{0},
    "A2": %i{0},
    "A3": %i{0},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}i",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0}
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int ReplaceVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{100000},
    "A2": %i{100001},
    "A3": %i{100002},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}r",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0}
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int UpdateVarVertex42k(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    int maxLen2 = 6000;
    char *strValue = (char *)malloc(maxLen);
    if (strValue == NULL) {
        return T_FAILED;
    }
    (void)memset(strValue, 'u', maxLen - 1);
    strValue[maxLen - 1] = '\0';

    char *lob = (char *)malloc(maxLen2);
    if (lob == NULL) {
        return T_FAILED;
    }
    (void)memset(lob, 'u', maxLen2 - 1);
    lob[maxLen2 - 1] = '\0';

    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 200000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 200001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 200002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);

        ret = GmcSetVertexProperty(stmt, "s1", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "s2", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "s3", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "s4", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "s5", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "s6", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "s7", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    free(lob);
    free(strValue);
    return T_OK;
}

int UpdateVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    char strValue[maxLen] = {0};
    (void)memset(strValue, 'u', maxLen - 1);
    strValue[maxLen - 1] = '\0';
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 200000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 200001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 200002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int MergeVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    char strValue[maxLen] = {0};
    (void)memset(strValue, 'm', maxLen - 1);
    strValue[maxLen - 1] = '\0';
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_MERGE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 300000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 300001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 300002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_DATA_EXCEPTION) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int CommonDelete(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelName(stmt, lableName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_UNDEFINED_TABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int KvSet(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[20] = {0};
    int32_t setValue = 0;
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        setValue = i;
        ret = GmcKvSet(stmt, kvKey, strlen(kvKey), &setValue, sizeof(int32_t));
        if (g_ignoreConflict) {
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int KvRemove(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[20] = {0};
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        ret = GmcKvRemove(stmt, kvKey, strlen(kvKey));
        if (g_ignoreConflict) {
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int KvCheckGet(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[10] = {0};
    int32_t expectValue = 0;
    int32_t getValue = 0;
    uint32_t getValueLen = sizeof(int32_t);
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        expectValue = i;
        ret = GmcKvGet(stmt, kvKey, strlen(kvKey), &getValue, &getValueLen);
        RETURN_IFERR(ret);
        if (expectValue != getValue) {
            AW_FUN_Log(LOG_STEP, "expectValue: %d getValue: %d", expectValue, getValue);
            return T_FAILED;
        }
    }
    return ret;
}

int KvCheckCount(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex, int expectCount)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[10] = {0};
    int32_t expectValue = 0;
    int32_t getValue = 0;
    uint32_t getValueLen = sizeof(int32_t);
    int count = 0;
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        expectValue = i;
        ret = GmcKvGet(stmt, kvKey, strlen(kvKey), &getValue, &getValueLen);
        if (ret == T_OK) {
            count++;
        }
    }
    if (count != expectCount) {
        AW_FUN_Log(LOG_STEP, "expectCount: %d actualCount: %d", expectCount, count);
        return T_FAILED;
    }
    if (ret = GMERR_NO_DATA) {
        return T_OK;
    }
    return ret;
}

int DoReplaceFixedTrx(GmcConnT *conn, GmcStmtT *stmt, char *tableName, int bIndex, int eIndex, int id)
{
    int ret = GmcTransStart(conn, &g_trxConfig);
    RETURN_IFERR(ret);
    bool rollBackFlag = false;
    ret = ReplaceFixedVertex(stmt, tableName, bIndex, eIndex);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "ReplaceFixedVertex ret: %d", ret);
    }
    ret = GmcTransCommit(conn);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "GmcTransCommit ret: %d", ret);
        g_rollBackFlag[id] = true;
        ret = GmcTransRollBack(conn);
    }
    return ret;
}

int DoReplaceVarTrx(GmcConnT *conn, GmcStmtT *stmt, char *tableName, int bIndex, int eIndex, int id)
{
    int ret = GmcTransStart(conn, &g_trxConfig);
    RETURN_IFERR(ret);
    bool rollBackFlag = false;
    ret = ReplaceVarVertex(stmt, tableName, bIndex, eIndex);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "ReplaceFixedVertex ret: %d", ret);
    }
    ret = GmcTransCommit(conn);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "GmcTransCommit ret: %d", ret);
        g_rollBackFlag[id] = true;
        ret = GmcTransRollBack(conn);
    }
    return ret;
}

void *ThreadDml(void *arg)
{
    OperateArgsT *dmlArg = (OperateArgsT *)arg;
    int ret;
    AW_FUN_Log(LOG_STEP, "ThreadDml table: %s, bIndex: %d, eIndex: %d", dmlArg->tableName, dmlArg->bIndex,
        dmlArg->eIndex);
    switch (dmlArg->type) {
        case T_REPLACE: {
            if (dmlArg->isFixedTable) {
                ret = ReplaceFixedVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            } else {
                ret = ReplaceVarVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            }
            AW_FUN_Log(LOG_STEP, "Replace %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_INSERT: {
            if (dmlArg->isFixedTable) {
                ret = InsertFixedVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            } else {
                ret = InsertVarVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            }
            AW_FUN_Log(LOG_STEP, "Insert %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_UPDATE: {
            if (dmlArg->isFixedTable) {
                ret = UpdateFixedVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            } else {
                ret = UpdateVarVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            }
            AW_FUN_Log(LOG_STEP, "Update %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_MERGE: {
            if (dmlArg->isFixedTable) {
                ret = MergeFixedVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            } else {
                ret = MergeVarVertex(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            }
            AW_FUN_Log(LOG_STEP, "Merge %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_DELETE: {
            ret = CommonDelete(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            AW_FUN_Log(LOG_STEP, "Delete %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_KVSET: {
            ret = KvSet(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            AW_FUN_Log(LOG_STEP, "KV Set %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_KVREMOVE: {
            ret = KvRemove(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex);
            AW_FUN_Log(LOG_STEP, "KV Remove %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        case T_DDL: {
            ret = DDLTable(dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex, dmlArg->operateTime);
            AW_FUN_Log(LOG_STEP, "ddl %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_BOOL(true,
                (ret == T_OK || ret == GMERR_UNDEFINED_TABLE || ret == GMERR_DUPLICATE_TABLE));
            break;
        }
        case T_REPLACE_TRX: {
            ret = DoReplaceFixedTrx(dmlArg->conn, dmlArg->stmt, dmlArg->tableName, dmlArg->bIndex, dmlArg->eIndex,
                dmlArg->id);
            AW_FUN_Log(LOG_STEP, "replace trx %s status: %d", dmlArg->tableName, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        default: {
            break;
        }
    }
}

int AsyncVarTbOperate(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, TOperateTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_operateArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_operateArg[g_curUsedThCount - 1].stmt = stmt;
    g_operateArg[g_curUsedThCount - 1].conn = NULL;
    g_operateArg[g_curUsedThCount - 1].bIndex = bIndex;
    g_operateArg[g_curUsedThCount - 1].eIndex = eIndex;
    g_operateArg[g_curUsedThCount - 1].isFixedTable = false;
    g_operateArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_operateArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncFixedTbOperate(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, TOperateTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_operateArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_operateArg[g_curUsedThCount - 1].stmt = stmt;
    g_operateArg[g_curUsedThCount - 1].conn = NULL;
    g_operateArg[g_curUsedThCount - 1].bIndex = bIndex;
    g_operateArg[g_curUsedThCount - 1].eIndex = eIndex;
    g_operateArg[g_curUsedThCount - 1].isFixedTable = true;
    g_operateArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_operateArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncFixedTrx(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, TOperateTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_operateArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_operateArg[g_curUsedThCount - 1].stmt = stmt;
    g_operateArg[g_curUsedThCount - 1].conn = conn;
    g_operateArg[g_curUsedThCount - 1].bIndex = bIndex;
    g_operateArg[g_curUsedThCount - 1].eIndex = eIndex;
    g_operateArg[g_curUsedThCount - 1].isFixedTable = true;
    g_operateArg[g_curUsedThCount - 1].type = type;
    g_operateArg[g_curUsedThCount - 1].id = g_curUsedThCount - 1;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_operateArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncKvTbOperate(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, TOperateTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_operateArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_operateArg[g_curUsedThCount - 1].stmt = stmt;
    g_operateArg[g_curUsedThCount - 1].conn = NULL;
    g_operateArg[g_curUsedThCount - 1].bIndex = bIndex;
    g_operateArg[g_curUsedThCount - 1].eIndex = eIndex;
    g_operateArg[g_curUsedThCount - 1].isFixedTable = false;
    g_operateArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_operateArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncDDLOperate(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, int operateTime)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_operateArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_operateArg[g_curUsedThCount - 1].stmt = stmt;
    g_operateArg[g_curUsedThCount - 1].conn = NULL;
    g_operateArg[g_curUsedThCount - 1].bIndex = bIndex;
    g_operateArg[g_curUsedThCount - 1].eIndex = eIndex;
    g_operateArg[g_curUsedThCount - 1].operateTime = operateTime;
    g_operateArg[g_curUsedThCount - 1].isFixedTable = false;
    g_operateArg[g_curUsedThCount - 1].type = T_DDL;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_operateArg[g_curUsedThCount - 1]);
    return ret;
}

int JoinAsyncThread()
{
    int ret = T_OK;
    for (int i = 0; i < g_curUsedThCount; i++) {
        ret = pthread_join(g_thread[i], NULL);
        RETURN_IFERR(ret);
    }
    g_curUsedThCount = 0;
    return ret;
}

int CheckInsertFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format, "A0(int32)=%i{0};A1(int64)=%i{0};A2(uint32)=%i{0};A3(uint64)=%i{0};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckReplaceFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{100000};A2(uint32)=%i{100001};A3(uint64)=%i{100002};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckUpdateFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{200000};A2(uint32)=%i{200001};A3(uint64)=%i{200002};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckMergeFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{300000};A2(uint32)=%i{300001};A3(uint64)=%i{300002};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckInsertVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format, "A0(int32)=%i{0};A1(int64)=%i{0};A2(uint32)=%i{0};A3(uint64)=%i{0};A9(string)=%f{20}i");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckReplaceVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{100000};A2(uint32)=%i{100001};A3(uint64)=%i{100002};A9(string)=%f{20}r");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckUpdateVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{200000};A2(uint32)=%i{200001};A3(uint64)=%i{200002};A9(string)=%f{99}u");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckMergeVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{300000};A2(uint32)=%i{300001};A3(uint64)=%i{300002};A9(string)=%f{99}m");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckRecordCount(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, uint32_t expectCount)
{
    int ret = T_OK;
    ret = TestSelVertexCount(stmt, lableName, g_pIndexName, g_cond, expectCount, bIndex, eIndex, 0);
    return ret;
}

int BatchReplaceFixedVertex(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, 0, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    for (uint32_t i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 100000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 100001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 100002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        int8_t a9 = i % 16;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_PARTITION, &a9, sizeof(int8_t));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            i--;
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "Exit when Batch insert record, index:%u status: %d", i, ret);
                (void)GmcBatchDestroy(batch);
                return ret;
            }
            ret = GmcBatchDestroy(batch);
            RETURN_IFERR(ret);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            RETURN_IFERR(ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    return ret;
}

typedef struct {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint8_t mask_len;
} Ip4ForwardStructT;

void SetIp4ForwardStructValue(Ip4ForwardStructT *valueStruct, uint32_t index)
{
    valueStruct->vr_id = index;
    valueStruct->vrf_index = index + 100;
    valueStruct->dest_ip_addr = index + 1000;
    valueStruct->mask_len = (uint8_t)(index % 127);
}

int StructInsertFixedVertex(GmcStmtT *stmt, char *lableName, int bIndex, int eIndex, char *schema)
{
    Ip4ForwardStructT obj = (Ip4ForwardStructT){ 0 };
    TestLabelInfoT labelInfo = { lableName, 0, g_testNameSpace };
    int ret = GmcPrepareStmtByLabelName(stmt, lableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    ret = TestGetVertexLabelFromSchema(schema, g_testNameSpace);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        SetIp4ForwardStructValue(&obj, i);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }

    return T_OK;
}

int CommonCreateTable(GmcStmtT *stmt, const char *lableName, const char *schemaFilePath, const char *config)
{
    char *schema = NULL;
    // 简单表
    readJanssonFile(schemaFilePath, &schema);
    if (schema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabelWithName(stmt, schema, config, lableName);
    free(schema);
    schema = NULL;
    return ret;
}

#ifdef __cplusplus
}
#endif

#endif /* RTBACKUP_H */

