/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :【计算交付】对账老化支持持久化
 Author       :gaopanqiong 60053317
 Modification :
 Date         :2025/02/06
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include "gtest/gtest.h"
#include "PersSupRecoAge.h"

char cmd[512];
#define FULLTABLE 0xff

class GeneralTable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GeneralTable::SetUpTestCase()
{}

void GeneralTable::TearDownTestCase()
{}

void GeneralTable::SetUp()
{
    // 建连
    int ret = 0;
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 按需持久化
    system("${TEST_HOME}/tools/start.sh");

    // 删除历史表
    system("gmsysview count");
    for (int32_t i = 0; i < 5; i++) {
        char labelName[128];
        char labelName1[128];
        (void)sprintf(labelName, "generalpartition%d", i + 1);
        GmcDropVertexLabel(g_stmt, labelName);
        (void)sprintf(labelName1, "generalNopartition%d", i + 1);
        GmcDropVertexLabel(g_stmt, labelName1);
    }
    GmcDropVertexLabel(g_stmt, "general");

    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void GeneralTable::TearDown()
{
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_CHECK_LOG_END();
    int ret = 0;
    
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
}

/* ****************************************************************************
001.开启对账，insert数据1000条，结束对账（老化），再次开启对账，DB持久化落盘重启
步骤:
0、开启按需持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再insert数据1000条数据B；
4、结束对账（老化）；
5、再次开启对账；
6、DB持久化落盘重启
预期结果：
DB重启后数据集A不存在、数据集B存在
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_001)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 再次开启对账");
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1000条");
    int32_t record = 1000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
002.开启对账，insert数据1000条，结束对账（老化），再次开启对账，DB持久化落盘重启，再次结束对账（老化）预期报错
步骤:
0、开启按需持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再insert数据1000条数据B；
4、结束对账（老化）；
5、再次开启对账；
6、DB持久化落盘重启；
7、再次结束对账（老化）；
预期结果：
DB重启后数据集A不存在、数据集B存在
最后再结束对账预期报错，对账状态不会持久化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_002)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_NOT_IN_CHECKING);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 再次开启对账");
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再次结束对账（老化）;");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);  // 报错 1012004表未处于对账中

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1000条");
    int32_t record = 1000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
003.开启对账，insert数据1000条，结束对账（老化），再次开启对账，DB持久化落盘重启，再次异常结束对账（恢复）预期报错
步骤:
0、开启按需持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再insert数据1000条数据B；
4、结束对账（老化）；
5、再次开启对账；
6、DB持久化落盘重启；
7、异常结束对账（恢复）；
预期结果：
DB重启后数据集A不存在、数据集B存在
最后再结束对账预期报错，对账状态不会持久化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_003)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_NOT_IN_CHECKING);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 再次开启对账");
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再次结束对账（恢复）;");
    ret = GmcEndCheck(g_stmt, labelName, partition, true);
    EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);  // 报错 1012004表未处于对账中

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1000条");
    int32_t record = 1000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
004.开启对账，insert数据1000条，结束对账（老化），再次开启对账，replace数据100条， 结束对账（老化），DB持久化落盘重启
步骤:
0、开启按需持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再insert数据1000条数据B；
4、结束对账（老化）；
5、再次开启对账；
6、再replace 新旧各600条数据C；
7、结束对账（老化）；
8、DB持久化落盘重启
预期结果：
DB重启后数据集A不存在、数据集B不存在，数据C存在
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_004)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 再次开启对账");
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再replace 新旧各600条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10400, 11600, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);

    AW_FUN_Log(LOG_STEP, "[info] 再次结束对账（老化）;");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1200");
    uint64_t vertexCount1 = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1200, vertexCount1);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1200条");
    int32_t record = 1200;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1200");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
005.开启对账，insert数据1000条，结束对账（老化），再次开启对账，replace数据1200条，
异常结束对账（恢复），DB持久化落盘重启 步骤: 0、开启按需持久化模式； 1、预置插入10000条数据A； 2、开启对账；
3、再insert数据1000条数据B；
4、结束对账（老化）；
5、再次开启对账；
6、再replace 新旧各600条数据C；
7、结束对账（恢复）；
8、DB持久化落盘重启
预期结果：
DB重启后数据集A不存在、数据集B存在(旧数据不存在)，数据C存在
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_005)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 再次开启对账");
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再replace 新旧各600条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10400, 11600, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);

    AW_FUN_Log(LOG_STEP, "[info] 再次结束对账（恢复）;");
    ret = GmcEndCheck(g_stmt, labelName, partition, true);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1600");
    uint64_t vertexCount1 = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1600, vertexCount1);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1600条");
    int32_t record = 1600;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1600");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
006.大数据（50000条）开启对账，DML数据，结束对账完成（老化），DB持久化落盘重启，预期数据写入成功，预置数据被老化
步骤:
0、开启增量持久化模式；
1、预置插入50000条数据A；
2、开启对账；
3、DML数据；
4、结束对账（老化）；
5、DB持久化落盘重启
预期结果：
DB重启后数据集A不存在、数据集B不存在，数据C存在
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_006)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 50000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info]update更新1000条，replace1000条，merge1000条，删除1000条，插入1000条;");
    // update更新1000条
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 0, 10000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 20000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge1000条，
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 20000, 30000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除1000条
    TestGeneralDelete(g_stmt, labelName, 40000, 50000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 40000, 55000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为45000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(45000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数45000条");
    int32_t record = 45000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 45000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
007.大数据（50000条）开启对账，DML数据，异常结束对账（恢复），DB持久化落盘重启，预期数据写入成功，预置数据不会老化删掉
步骤:
0、开启增量持久化模式；
1、预置插入50000条数据A；
2、开启对账；
3、DML数据；
4、异常结束对账（恢复）；
5、DB持久化落盘重启
预期结果：
DB重启后数据集A未老化、DML数据成功
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_007)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 50000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info]update更新1000条，replace1000条，merge1000条，删除1000条，插入1000条;");
    // update更新1000条
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 0, 10000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 20000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge1000条，
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 20000, 30000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除1000条
    TestGeneralDelete(g_stmt, labelName, 40000, 50000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 40000, 55000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（恢复）");
    ret = GmcEndCheck(g_stmt, labelName, partition, true);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为55000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(55000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数55000条");
    int32_t record = 55000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 55000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
008.大数据（50000条）开启对账，DML数据，DB持久化落盘重启，预置数据不会老化删掉
步骤:
0、开启增量持久化模式；
1、预置插入50000条数据A；
2、开启对账；
3、DML数据；
4、未执行结束对账；DB持久化落盘重启
预期结果：
DB重启后数据集A未老化、DML数据成功
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_008)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 50000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info]update更新1000条，replace1000条，merge1000条，删除1000条，插入1000条;");
    // update更新1000条
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 0, 10000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 20000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge1000条，
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 20000, 30000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除1000条
    TestGeneralDelete(g_stmt, labelName, 40000, 50000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 40000, 55000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为55000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(55000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数55000条");
    int32_t record = 55000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 55000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
009.开启对账，写数据插入1000条数据，结束对账完成（老化）过程中并行删掉表，DB持久化落盘重启，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再插入1000条数据B；
4、结束对账（老化）；
5、并行删掉表（第2步-4之间）
6、DB持久化落盘重启
预期结果：
预期表正在对账中删表失败，对账能正常对账结束
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_009)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_IN_CHECKING);  // 1012003 表正在对账中。
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 创建信号量集，一个信号，用于子进程通知主进程，确保构造业务过程中故障
    AW_FUN_Log(LOG_STEP, "[info] 创建信号量集");
    ret = GetSem(&g_semSetId, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 并行删掉表 第2步-4之间");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];

    ret = pthread_create(&client_thr[0], NULL, ThreadNoPartDelFa, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对账过程中删表
    ret = pthread_create(&client_thr[1], NULL, DeleteLabel, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1000条");
    int32_t record = 1000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    // 清理信号集
    (void)RemoveSem(g_semSetId);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
010.开启对账，写数据插入1000条数据，异常结束对账（恢复）过程中并行删掉表，DB持久化落盘重启，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再插入1000条数据B；
4、异常结束对账（恢复）；
5、并行删掉表（第2步-4之间）
6、DB持久化落盘重启
预期结果：
预期表正在对账中删表失败，对账能正常对账结束
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_010)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_IN_CHECKING);  // 1012003 表正在对账中。
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 创建信号量集，一个信号，用于子进程通知主进程，确保构造业务过程中故障
    AW_FUN_Log(LOG_STEP, "[info] 创建信号量集");
    ret = GetSem(&g_semSetId, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 并行删掉表 第2步-4之间");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];

    ret = pthread_create(&client_thr[0], NULL, ThreadNoPartDelTr, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对账过程中删表
    ret = pthread_create(&client_thr[1], NULL, DeleteLabel, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数11000条");
    int32_t record = 11000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 11000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    // 清理信号集
    (void)RemoveSem(g_semSetId);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
011.建表，预置写入10000条数据，开启对账，进行DML操作，结束对账，服务重启，删除数据，循环50次
步骤:
0、开启增量持久化模式；
1、建表，预置插入10000条数据A；
2、开启对账；
3、进行DML操作；update更新100条，replace100条，merge100条，删除100条，插入100条
4、结束对账（老化）；
5、DB持久化落盘重启
6、重复开启对账 和结束对账（老化）（重复50次）
预期结果：
DB重启后数据集A不存在，数据更新成功，未更新数据老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_011)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 1000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    int cycle = 10;
    int ret = 0;
    for (int i = 0; i <= cycle; i++) {
        AW_FUN_Log(LOG_STEP, "[info] =============================第 %d 次循环 建表", i);
        readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);
        AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

        AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
        TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

        AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
        uint8_t partition = 0xff;
        ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info]update更新100条，replace100条，merge100条，删除100条，插入100条;");
        // update更新1000条
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 0, 100, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
        // replace1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 100, 200, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
        // merge1000条，
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 200, 300, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

        // 删除1000条
        TestGeneralDelete(g_stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

        // 插入1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 500, 600, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

        AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
        ret = GmcEndCheck(g_stmt, labelName, partition, false);
        EXPECT_EQ(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
        queryOldStatusView();

        AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为400");
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(400, vertexCount);

        AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
        ret = RestartAndConn(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 用于查询顶点标签的对账相关信息。system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
        // 等待恢复
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(2);

        system("gmsysview count");
        AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数400条");
        int32_t record = 400;
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
        queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 400");

        AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
012.建表，预置写入10000条数据，开启对账，进行DML操作， 异常结束对账（恢复），DB持久化落盘重启，删除数据，循环50次，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、进行DML操作；update更新100条，replace100条，merge100条，删除100条，插入100条;
4、异常结束对账（恢复）；
5、 DB持久化落盘重启；
6、重复开启对账 和异常结束对账（重复50次）
预期结果：
预期数据更新成功，预置数据不会老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_012)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int cycle = 10;
    int ret = 0;
    for (int i = 0; i <= cycle; i++) {
        AW_FUN_Log(LOG_STEP, "[info] =============================第 %d 次循环 建表", i);
        const char *labelName = "general";
        char *schema = NULL;
        int64_t startValue = 0;
        int64_t endValue = 10000;
        uint32_t schemaVersion = 0;
        GmcDropVertexLabel(g_stmt, labelName);
        readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);

        AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

        AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
        TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

        AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
        uint8_t partition = 0xff;
        ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info]update更新100条，replace100条，merge100条，删除100条，插入100条;");
        // update更新1000条
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
        // replace1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 1000, 2000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
        // merge1000条，
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 2000, 3000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

        // 删除1000条
        TestGeneralDelete(g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

        // 插入1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

        ret = GmcEndCheck(g_stmt, labelName, partition, true);
        EXPECT_EQ(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
        queryOldStatusView();
        AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为10000");
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10000, vertexCount);

        AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
        ret = RestartAndConn(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 用于查询顶点标签的对账相关信息。system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
        // 等待恢复
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数10000条");
        int32_t record = 10000;
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
        queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 10000");

        AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
013.建表，预置写入10000条数据，开启对账，进行DML操作，DB持久化落盘重启，循环50次，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、进行DML操作；update更新100条，replace100条，merge100条，删除100条，插入100条;
4、DB持久化落盘重启
（重复50次）
预期结果：
预期数据更新成功，预置数据不会老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_013)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int cycle = 10;
    int ret = 0;
    for (int i = 0; i <= cycle; i++) {
        AW_FUN_Log(LOG_STEP, "[info] =============================第 %d 次循环 建表", i);
        const char *labelName = "general";
        char *schema = NULL;
        int64_t startValue = 0;
        int64_t endValue = 10000;
        uint32_t schemaVersion = 0;
        GmcDropVertexLabel(g_stmt, labelName);
        readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);

        AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

        AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
        TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

        AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
        uint8_t partition = 0xff;
        ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info]update更新100条，replace100条，merge100条，删除100条，插入100条;");
        // update更新1000条
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
        // replace1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 1000, 2000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
        // merge1000条，
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 2000, 3000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

        // 删除1000条
        TestGeneralDelete(g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

        // 插入1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

        AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
        queryOldStatusView();
        AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为10000");
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10000, vertexCount);

        AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
        ret = RestartAndConn(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 用于查询顶点标签的对账相关信息。system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
        // 等待恢复
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数10000条");
        int32_t record = 10000;
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
        queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 10000");

        AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
014.建分区表，预置写入10000条数据，开启分区对账，进行DML操作，结束对账，服务重启，预期未更新数据老化，循环50次，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启分区对账；
3、进行DML操作；update更新100条，replace100条，merge100条，删除100条，插入100条;
4、结束对账（老化）；
5、DB持久化落盘重启
6、重复开启对账 和结束对账（老化）（重复50次）
预期结果：
DB重启后数据集A不存在，数据更新成功，未更新数据老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_014)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int cycle = 10;
    int ret = 0;
    for (int i = 0; i <= cycle; i++) {
        AW_FUN_Log(LOG_STEP, "[info] =============================第 %d 次循环 建表", i);
        const char *labelName = "general";
        char *schema = NULL;
        int64_t startValue = 0;
        int64_t endValue = 10000;
        uint32_t schemaVersion = 0;
        GmcDropVertexLabel(g_stmt, labelName);
        readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

        AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
        TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

        AW_FUN_Log(LOG_STEP, "[info] 开启分区对账");
        uint8_t par_count = 16;
        for (int partition = 0; partition < par_count; partition++) {
            ret = GmcBeginCheck(g_stmt, labelName, partition);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        AW_FUN_Log(LOG_STEP, "[info]update更新100条，replace100条，merge100条，删除100条，插入100条;");
        // update更新1000条
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
        // replace1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 1000, 2000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);
        // merge1000条，
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 2000, 3000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

        // 删除1000条
        TestGeneralDelete(
            g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

        // 插入1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

        AW_FUN_Log(LOG_STEP, "[info] 结束分区对账（老化）");
        for (int partition = 0; partition < par_count; partition++) {
            ret = GmcEndCheck(g_stmt, labelName, partition, false);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
        queryOldStatusView();
        AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为4000");
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4000, vertexCount);

        AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
        ret = RestartAndConn(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 用于查询顶点标签的对账相关信息。system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

        // 等待恢复
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数4000条");
        int32_t record = 4000;
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
        queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 250");
        queryOldInfoView(labelName, "PARTITION_ID: 15", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 250");

        AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
015.建分区表，预置写入10000条数据，开启分区对账，进行DML操作，
异常结束对账（恢复），DB持久化落盘重启，预期数据不会老化，循环50次 步骤: 0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启分区对账；
3、进行DML操作；update更新100条，replace100条，merge100条，删除100条，插入100条;
4、异常结束对账（恢复）；
5、 DB持久化落盘重启；
6、重复开启对账 和异常结束对账（重复50次）
预期结果：
预期数据更新成功，预置数据不会老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_015)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int cycle = 10;
    int ret = 0;

    for (int i = 0; i <= cycle; i++) {
        AW_FUN_Log(LOG_STEP, "[info] =============================第 %d 次循环 建表", i);
        const char *labelName = "general";
        char *schema = NULL;
        int64_t startValue = 0;
        int64_t endValue = 10000;
        uint32_t schemaVersion = 0;
        GmcDropVertexLabel(g_stmt, labelName);
        readJanssonFile("./schema_file/generalpartition.gmjson", &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);

        AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);

        AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
        TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

        AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
        uint8_t par_count = 16;
        for (int partition = 0; partition < par_count; partition++) {
            ret = GmcBeginCheck(g_stmt, labelName, partition);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        AW_FUN_Log(LOG_STEP, "[info]update更新100条，replace100条，merge100条，删除100条，插入100条;");
        // update更新1000条
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true, true);
        // replace1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 1000, 2000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true, true);
        // merge1000条，
        TestGeneralT1UpdateOrMerge(
            g_stmt, labelName, 2000, 3000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true, true);

        // 删除1000条
        TestGeneralDelete(
            g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true, true);

        // 插入1000条
        TestGeneralT1InsertOrReplace(
            g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true, true);
        AW_FUN_Log(LOG_STEP, "[info] 结束分区对账（老化）");
        for (int partition = 0; partition < par_count; partition++) {
            ret = GmcEndCheck(g_stmt, labelName, partition, true);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // query old queue status
        queryOldStatusView();
        AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为10000");
        uint64_t vertexCount = 0;
        ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10000, vertexCount);

        AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
        ret = RestartAndConn(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 用于查询顶点标签的对账相关信息。system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
        // 等待恢复
        ret = TestWaitRsmRecoverFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(2);
        AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数10000条");
        int32_t record = 10000;
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
        queryOldInfoView(labelName, "PARTITION_ID: 0", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 625");
        queryOldInfoView(labelName, "PARTITION_ID: 15", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 625");

        AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
016.开启对账，insert数据1000条，结束对账（老化），在对老化数据DML操作，DB持久化落盘重启，后台老化任务完成，预期DML成功，数据老化，DML操作数据存在，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再插入1000条数据B；
4、结束对账（老化）；
5、在对老化数据DML操作;
6、DB持久化落盘重启；
预期结果：
预期DML成功，数据老化，DML操作数据存在，
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_016)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（老化）");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为1000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 在对老化数据DML操作;");
    AW_FUN_Log(LOG_STEP, "[info]update更新1000条，replace1000条，merge1000条，删除1000条，插入1000条;");
    // update更新1000条
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 1000, 2000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge1000条，
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 2000, 3000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除1000条
    TestGeneralDelete(g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);
    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 11000, 12000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 1查询表逻辑计数为5000");
    uint64_t vertexCount1 = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, vertexCount1);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 2查询表逻辑计数为5000");
    uint64_t vertexCount2 = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5000, vertexCount2);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数5000条");
    int32_t record = 5000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 5000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
017.开启对账，insert数据1000条，结束对账（恢复），在对老化数据DML操作，DB持久化落盘重启，后台老化任务完成，预期数据不会老化，
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再插入1000条数据B；
4、结束对账（恢复）；
5、在对老化数据DML操作；
6、DB持久化落盘重启；
预期结果：
预期DML成功，数据不会老化，DML操作数据存在，
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_017)
{
    AW_FUN_Log(LOG_STEP, "[info] test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 结束全表对账（恢复）");
    ret = GmcEndCheck(g_stmt, labelName, partition, true);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为11000");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(11000, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] 在对老化数据DML操作;");
    AW_FUN_Log(LOG_STEP, "[info]update更新1000条，replace1000条，merge1000条，删除1000条，插入1000条;");
    // update更新1000条
    TestGeneralT1UpdateOrMerge(g_stmt, labelName, 0, 1000, (char *)"string", schemaVersion, GMC_OPERATION_UPDATE, true);
    // replace1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 1000, 2000, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);
    // merge1000条，
    TestGeneralT1UpdateOrMerge(
        g_stmt, labelName, 2000, 3000, (char *)"string", schemaVersion, GMC_OPERATION_MERGE, true);

    // 删除1000条
    TestGeneralDelete(g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_DELETE, true);

    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 5000, 6000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);
    // 插入1000条
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 11000, 12000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] 1查询表逻辑计数为12000");
    uint64_t vertexCount1 = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(12000, vertexCount1);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 2查询表逻辑计数为12000");
    uint64_t vertexCount2 = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(12000, vertexCount2);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数12000条");
    int32_t record = 12000;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 12000");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] test_end");
}

/* ****************************************************************************
018.开启对账，insert数据1000条，Truncate 快速老化操作（GmcDeleteAllFast），重启，预期数据全部老化
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、开启对账；
3、再插入1000条数据B；
4、Truncate 快速老化操作；
5、DB持久化落盘重启；
预期结果：
预期数据全部老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_018)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] 开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 再新插入1000条数据B;");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, 10000, 11000, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] Truncate 快速老化操作；");
    ret = GmcDeleteAllFast(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为0");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1000条");
    int32_t record = 0;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 0");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
019.预置数据，直接Truncate 快速老化操作（GmcDeleteAllFast），重启，数据全部老化
步骤:
0、开启增量持久化模式；
1、预置插入10000条数据A；
2、Truncate 快速老化操作；
3、DB持久化落盘重启；
预期结果：
预期数据全部老化
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_019)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建表");
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 10000;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 预置10000条数据A");
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    AW_FUN_Log(LOG_STEP, "[info] scan 查询一般复杂表");
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    AW_FUN_Log(LOG_STEP, "[info] Truncate 快速老化操作；");
    ret = GmcDeleteAllFast(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] query old queue status 查询旧队列状态，查询老化任务列表");
    queryOldStatusView();

    AW_FUN_Log(LOG_STEP, "[info] 查询表逻辑计数为0");
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");
    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1000条");
    int32_t record = 0;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "[info] 查询老化任务列表");  // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 0");

    AW_FUN_Log(LOG_STEP, "[info] 用例结束删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
020.建10张表，每张表写1000条数据，开启10个线程，5个线程对一张表开启全表对账，5个线程进行分区对账，进行dml操作，结束对账，服务重启，
步骤:
0、开启按需持久化模式；
1、建10张表，每张表写1000条数据A；
2、开启10个线程，5个线程对一张表开启全表对账，5个线程进行分区对账；
3、update更新100条，replace100条，merge100条，删除100条，插入100条，
4、结束对账（老化）；
5、DB持久化落盘重启
预期结果：
DB重启后数据集A不存在、数据(400条)存在
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_020)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建10个表");
    char labelName[128] = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 1000;
    uint32_t schemaVersion = 0;
    char partVertex[128] = "";
    char noPartVertex[128] = "";
    GmcDropVertexLabel(g_stmt, labelName);
    for (int32_t i = 1; i < 6; i++) {
        (void)sprintf(partVertex, "./schema_file/generalpartition%d.gmjson", i);
        (void)sprintf(noPartVertex, "./schema_file/generalNopartition%d.gmjson", i);
        readJanssonFile(partVertex, &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);
        readJanssonFile(noPartVertex, &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);
    }

    AW_FUN_Log(LOG_STEP, "[info] 开启多个线程");
    int32_t thread_num = 10;
    pthread_t client_thr[thread_num];
    int32_t index[5] = {1, 2, 3, 4, 5};
    for (int32_t i = 0; i < 5; i++) {
        sleep(1);
        ret = pthread_create(&client_thr[i], NULL, ThreadPart, &index[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 5; i < 10; i++) {
        ret = pthread_create(&client_thr[i], NULL, ThreadNoPart, &index[i % 5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish(100);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 查询非分区表老化任务列表");  // query old version
    for (int32_t m = 1; m < 6; m++) {
        const char *generalNopartition = "generalNopartition";
        char labName[50];
        (void)sprintf(labName, "%s%d", generalNopartition, m);
        queryOldInfoView(labName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 400");
    }

    AW_FUN_Log(LOG_STEP, "[info] 查询0-15分区老化任务列表");
    for (int32_t m = 1; m < 6; m++) {
        const char *generalpartition = "generalpartition";
        char labName[50];
        (void)sprintf(labName, "%s%d", generalpartition, m);
        AW_FUN_Log(LOG_STEP, "分区表: %s ,", labName);
        for (int partition = 0; partition < 16; partition++) {
            const char *partitionId = "PARTITION_ID: ";
            char partition_id[50];
            (void)sprintf(partition_id, "%s%d", partitionId, partition);
            if (partition < 4) {
                queryOldInfoView(labName, partition_id, "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 25");
            } else if (partition < 8) {
                queryOldInfoView(labName, partition_id, "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 26");
            } else if (partition < 12) {
                queryOldInfoView(labName, partition_id, "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 25");
            } else {
                queryOldInfoView(labName, partition_id, "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 24");
            }
        }
    }
    // scan
    int32_t record = 400;
    system("gmsysview count");
    for (int32_t i = 0; i < 5; i++) {
        (void)sprintf(labelName, "generalpartition%d", i + 1);
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < 5; i++) {
        (void)sprintf(labelName, "generalNopartition%d", i + 1);
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
021.建10张表，每张表写1000条数据，开启10个线程，5个线程对一张表开启全表对账，5个线程进行分区对账，进行dml操作，异常结束对账，DB持久化落盘重启
步骤:
0、开启按需持久化模式；
1、建10张表，每张表写1000条数据A；
2、开启10个线程，5个线程对一张表开启全表对账，5个线程进行分区对账；
3、旧update更新100条，新replace100条，旧merge100条，删除100条，新插入100条(共400条)；
4、异常结束对账（恢复）；
5、DB持久化落盘重启
预期结果：
DB重启后数据集A(1000条)存在、数据集B(200条)存在
**************************************************************************** */
TEST_F(GeneralTable, Compute_003_001_03_021)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "[info] 建10个表");
    char labelName[128] = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    char partVertex[128] = "";
    char noPartVertex[128] = "";
    GmcDropVertexLabel(g_stmt, labelName);
    for (int32_t i = 1; i < 6; i++) {
        (void)sprintf(partVertex, "./schema_file/generalpartition%d.gmjson", i);
        (void)sprintf(noPartVertex, "./schema_file/generalNopartition%d.gmjson", i);
        readJanssonFile(partVertex, &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);
        readJanssonFile(noPartVertex, &schema);  // 拥有partition字段的表是分区表
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);
    }
    AW_FUN_Log(LOG_STEP, "[info] 开启多个线程");
    int32_t thread_num = 10;
    pthread_t client_thr[thread_num];
    int32_t index[5] = {1, 2, 3, 4, 5};
    for (int32_t i = 0; i < 5; i++) {
        sleep(1);
        ret = pthread_create(&client_thr[i], NULL, ThreadPartTrue, &index[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 5; i < 10; i++) {
        ret = pthread_create(&client_thr[i], NULL, ThreadNoPartTrue, &index[i % 5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待恢复
    ret = TestWaitRsmRecoverFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO");

    AW_FUN_Log(LOG_STEP, "[info] 查询非分区表老化任务列表");  // query old version
    for (int32_t m = 1; m < 6; m++) {
        char labName[50];
        (void)sprintf(labName, "%s%d", "generalNopartition", m);
        AW_FUN_Log(LOG_STEP, "非分区表: %s,", labName);
        queryOldInfoView(labName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 1000");
    }

    AW_FUN_Log(LOG_STEP, "[info] 查询0-15分区老化任务列表");
    for (int32_t m = 1; m < 6; m++) {
        char labName[50];
        (void)sprintf(labName, "%s%d", "generalpartition", m);
        AW_FUN_Log(LOG_STEP, "分区表: %s,", labName);
        for (int partition = 0; partition < 16; partition++) {
            const char *partitionId = "PARTITION_ID: ";
            char partition_id[50];
            (void)sprintf(partition_id, "%s%d", partitionId, partition);
            if (partition < 8) {
                queryOldInfoView(labName, partition_id, "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 63");
            } else {
                queryOldInfoView(labName, partition_id, "CHECK_STATUS: 0", "CHECK_VERSION: 0", "RECORD_CNT: 62");
            }
        }
    }
    // scan
    int32_t record = 1000;
    system("gmsysview count");
    char labelName1[128];
    for (int32_t i = 0; i < 5; i++) {
        (void)sprintf(labelName, "generalpartition%d", i + 1);
        ret = readTable(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(record, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < 5; i++) {
        (void)sprintf(labelName1, "generalNopartition%d", i + 1);
        ret = readTable(g_stmt, labelName1);
        AW_MACRO_EXPECT_EQ_INT(record, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test_end");
}
