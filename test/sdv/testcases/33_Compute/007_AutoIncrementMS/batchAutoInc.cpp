/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 主备同步支持自增列 - 批量备份
 * Author: liwenhai
 * Create: 2025-02-06
 */

#include "tool.h"

int g_instanceCount = 2;

void StartEnvAndConn()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxLockTimeOut=10000\"");
    // 主节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -m");
    // 备节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -s");

    // 配置主备
    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

class batch_backup_auto_increment : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void batch_backup_auto_increment::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");

    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    StartEnvAndConn();

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建连接
    ret = TestGmcConnectLocator(&g_connListSync[0], &g_stmtListSync[0], g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void batch_backup_auto_increment::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    // 断开连接
    for (int i = 0; i < g_instanceCount; i++) {
        ret = testGmcDisconnect(g_connListSync[i], g_stmtListSync[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 017.自增列数据类型为uint32的主键字段，建表，写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i + 1;
        uint32_t f1Val = i;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i + 1;
        uint32_t f1Val = i;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.自增列数据类型为uint64的主键字段，建表，写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_2.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        // 自增列默认值从1开始
        uint64_t idVal = i + 1;
        uint32_t f1Val = i;
        ret = GetVertexProperty64PK(g_stmtListSync[g_instance0], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint64_t idVal = i + 1;
        uint32_t f1Val = i;
        ret = GetVertexProperty64PK(g_stmtListSync[g_instance1], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.自增列数据类型为uint32的非索引字段，建表，写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_3.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.自增列数据类型为uint64的非索引字段，建表，写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint64_t f2Val = i + 1;
        ret = GetVertexProperty64F(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint64_t f2Val = i + 1;
        ret = GetVertexProperty64F(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.含自增列的表，replace1000条旧数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 预置数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 重启备服务
    ReOnlineSlave();

    // replace这些数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int replaceCnt = insertCnt;
    for (int i = 0; i < replaceCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < replaceCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 1001;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < replaceCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 1001;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.含自增列的表，replace1000条新数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // replace1000条数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int replaceCnt = 1000;
    for (int i = 0; i < replaceCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < replaceCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < replaceCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.含自增列的表，merge1000条旧数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 预置数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 重启备服务
    ReOnlineSlave();

    // merge这些数据
    int mergeCnt = insertCnt;
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < mergeCnt; i++) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主服务查询
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < mergeCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < mergeCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.含自增列的表，merge1000条新数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // merge1000条数据
    int mergeCnt = 1000;
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < mergeCnt; i++) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主服务查询
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < mergeCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < mergeCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.含自增列的表，update1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 预置数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 重启备服务
    ReOnlineSlave();

    // update这些数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int updateCnt = insertCnt;
    for (int i = 0; i < updateCnt; i++) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主服务查询
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < updateCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < updateCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.含自增列的表，delete1000条数据后立即启动备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 预置数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 重启备服务
    ReOnlineSlave();

    // delete这些数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int deleteCnt = insertCnt;
    for (int i = 0; i < deleteCnt; i++) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < deleteCnt; i++) {
        uint32_t idVal = i;
        ret = GetVertexPropertyNoData(g_stmtListSync[g_instance0], idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < deleteCnt; i++) {
        uint32_t idVal = i;
        ret = GetVertexPropertyNoData(g_stmtListSync[g_instance1], idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.含自增列的表，设置自增列初始值，写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_configAI100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        // 自增列从100开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 100;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 100;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.含自增列的表，设置自增列初始值接近数据类型上限，写1000条数据使自增列超出类型上限后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_configAINearToMaxUINT32);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // 重启备服务
    ReOnlineSlave();

    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt / 2; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_DATA_EXCEPTION);
    for (int i = insertCnt / 2; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexPropertyFail(g_stmtListSync[g_instance0], "id", idVal, GMERR_DATA_EXCEPTION);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt / 2; i++) {
        // 自增列从4294966795开始
        uint32_t idVal = i;
        uint32_t f2Val = i + 4294966796;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = insertCnt / 2; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = GetVertexPropertyNoData(g_stmtListSync[g_instance0], idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt / 2; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 4294966796;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = insertCnt / 2; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = GetVertexPropertyNoData(g_stmtListSync[g_instance1], idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.含自增列的表，写1000条数据后删500条数据，再写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // delete500条数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int deleteCnt = insertCnt;
    for (int i = 0; i < deleteCnt; i += 2) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启备服务
    ReOnlineSlave();

    // 再写1000条数据
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i + 1000;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i + 1000;
        uint32_t f2Val = i + 1001;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i + 1000;
        uint32_t f2Val = i + 1001;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.设置自增列初始值为100，写1000条数据后truncate清空表数据，再写1000条数据后立即启备服务，主服务查，备服务查
TEST_F(batch_backup_auto_increment, Compute_007_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_configAI100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // truncate清空数据
    ret = GmcTruncateVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启备服务
    ReOnlineSlave();

    // 重新写1000条数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 100;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        uint32_t f2Val = i + 100;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.含自增列的表，先更新1000条数据，再写1000条数据后立即启备服务，主服务查，备服务查（自增列先不增加再增加）
TEST_F(batch_backup_auto_increment, Compute_007_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // 预置数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int insertCnt = 1000;
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // update这些数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int updateCnt = insertCnt;
    for (int i = 0; i < updateCnt; i++) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 重启备服务
    ReOnlineSlave();

    // 再写1000条数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt; i++) {
        uint32_t idVal = i + 1000;
        ret = GmcSetVertexProperty(g_stmtListSync[g_instance0], "id", GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f1Val = i + 1000;
        ret = GmcSetVertexProperty(g_stmtListSync[g_instance0], "F1", GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 主服务查
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt * 2; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f1Val = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < insertCnt * 2; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.含自增列的表，先replace1000条数据，再update这些条数据后立即启备服务，主服务查，备服务查（自增列先增加再不增加）
TEST_F(batch_backup_auto_increment, Compute_007_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    char *labelJson = NULL;
    readJanssonFile("schemaFile/vertex_lable_5.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmtListSync[g_instance0], labelJson, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    // replace1000条数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int replaceCnt = 1000;
    for (int i = 0; i < replaceCnt; i++) {
        uint32_t idVal = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "id", idVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 重启备服务
    ReOnlineSlave();

    // update这些数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int updateCnt = replaceCnt;
    for (int i = 0; i < updateCnt; i++) {
        uint32_t idVal = i;
        ret = GmcSetIndexKeyName(g_stmtListSync[g_instance0], "pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtListSync[g_instance0], 0, GMC_DATATYPE_UINT32, &idVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtListSync[g_instance0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        uint32_t f1Val = i;
        ret = SetVertexProperty(g_stmtListSync[g_instance0], "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcWait(g_connServer, GMC_DB_STATUS_BACKUP_ENABLED, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&g_connListSync[1], &g_stmtListSync[1], g_connServerSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 主服务查
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance0], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < updateCnt; i++) {
        // 自增列默认值从1开始
        uint32_t idVal = i;
        uint32_t f1Val = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GetVertexProperty(g_stmtListSync[g_instance0], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 备服务检查同步的数据
    ret = testGmcPrepareStmtByLabelName(g_stmtListSync[g_instance1], "vertexLable", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < updateCnt; i++) {
        uint32_t idVal = i;
        uint32_t f1Val = i;
        uint32_t f2Val = i + 1;
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F1", f1Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GetVertexProperty(g_stmtListSync[g_instance1], idVal, "F2", f2Val);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 清理环境
    ret = GmcDropVertexLabel(g_stmtListSync[g_instance0], "vertexLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
