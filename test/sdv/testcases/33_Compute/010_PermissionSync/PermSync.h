/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: PermSync.h
 * Author: lushiguang
 * Create: 2025-01-08
 */
#ifndef PERMSYNC_H
#define PERMSYNC_H


#include "gtest/gtest.h"
#include "t_light.h"
#include "test_ha.h"

#define MAX_INSTANCE_COUNT 5
#define CONN_COUNT 5

char g_serverLocation[MAX_INSTANCE_COUNT][200];
bool g_ddlExceptionCheck = false;
int g_repMode = 2;
int g_beginIndex = 0;
int g_endIndex = 1000;
int g_gmcWaitTimeout = 30;

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync = NULL;

GmcConnT *g_masterConn[CONN_COUNT];
GmcStmtT *g_masterStmt[CONN_COUNT];

GmcConnT *g_slaveConn[CONN_COUNT];
GmcStmtT *g_slaveStmt[CONN_COUNT];

int g_instance0 = 0;
int g_instance1 = 1;
int g_instance2 = 2;
GmcTxConfigT g_trxConfig;
const char *g_url = "tcp:host=127.0.0.1,port=2633";

bool g_ignoreConflict = false;
bool g_ignoreTimeout = false;

#ifdef __cplusplus
extern "C" {
#endif

char g_pIndexName[] = "PrimaryKey";
char g_cond[] = "A0(int32)=%i{0}";
char *g_tableName = (char *)"lable1";
char *g_tableName2 = (char *)"lable2";
char *g_tableName3 = (char *)"lable3";

char g_dbFilePath[MAX_INSTANCE_COUNT][1024] = {0};

const char *g_config2 = (char *)R"(
    {
        "max_record_count": 100000000,
        "isFastReadUncommitted":0,
        "replication":2
    }
)";

const char *g_config1 = (char *)R"(
    {
        "max_record_count": 100000000,
        "isFastReadUncommitted":0,
        "replication":1
    }
)";

const char *g_config0 = (char *)R"(
    {
        "max_record_count": 100000000,
        "isFastReadUncommitted":0,
        "replication":0
    }
)";

char *g_Schema1 = (char *)R"(
    [{
    "version": "2.0", "type": "record", "name": "lable1",
    "fields": [
        { "name": "A0", "type": "int32"},
        { "name": "vrf_index", "type": "uint32"},
        { "name": "dest_ip_addr", "type": "uint32"},
        { "name": "mask_len", "type": "uint8"},
        { "name": "other", "type": "string", "nullable": true}
    ],
    "keys": [
        { "name": "PrimaryKey", "index": { "type": "primary" },
            "node": "lable1",
            "fields": [ "A0" ],
            "constraints": { "unique": true }
        }
    ]
}]
)";

#define MAX_TEST_TABLE_NAME_LEN 50
typedef enum OperateType {
    T_INSERT,
    T_REPLACE,
    T_UPDATE,
    T_MERGE,
    T_DELETE,
    T_KVSET,
    T_KVREMOVE,
    T_DDL,
    T_REPLACE_TRX
} TOperateTypeE;

typedef struct {
    GmcConnT *conn;
    GmcStmtT *stmt;
    char tableName[MAX_TEST_TABLE_NAME_LEN];
    int bIndex;
    int eIndex;
    int operateTime;
    TOperateTypeE type;
    bool isFixedTable;
    int id;
} OperateArgsT;

#define MAX_THREAD_COUNT 20

int WaitReplicateFinish(int timeout = 3)
{
    sleep(timeout);
    return T_OK;
}

int WaitFullBackupFinish(const char *serverLocator, uint32_t timeout)
{
    int ret = GmcWait(serverLocator, GMC_DB_STATUS_BACKUP_ENABLED, timeout);
    // 增加失败DFX观测
    if (ret != GMERR_OK) {
        (void)GtExecSystemCmd("cp -r ../../../log ./server_log_back");
        (void)GtExecSystemCmd("gmsysview -q 'V$CATA_NAMESPACE_INFO' -s %s > CATA_NAMESPACE_INFO_master.log",
            g_connServer);
        (void)GtExecSystemCmd("gmsysview -q 'V$CATA_NAMESPACE_INFO' -s %s > CATA_NAMESPACE_INFO_slave.log",
            g_connServerSlave);
        (void)GtExecSystemCmd("gmsysview -q 'V$CATA_VERTEX_LABEL_INFO' -s %s > CATA_VERTEX_LABEL_INFO_master.log",
            g_connServer);
        (void)GtExecSystemCmd("gmsysview -q 'V$CATA_VERTEX_LABEL_INFO' -s %s > CATA_VERTEX_LABEL_INFO_slave.log",
            g_connServerSlave);
        (void)GtExecSystemCmd("netstat  -n  | grep tcp");
    }
    return ret;
}

void AddReplicationCfg()
{
    (void)GtExecSystemCmd("sed -i '/enableReplication/d' %s", g_sysGMDBCfg);
    (void)GtExecSystemCmd("echo 'enableReplication=0' >> %s", g_sysGMDBCfg);
    (void)ChangeGmserverCfg((char *)"enableReplication", (char *)"1");
}


int HaConfig(const char *masterLocation, const char *slaveLocation, const char *url, int waitTimeout)
{
    int ret = GmcSetDbRole(masterLocation, GMC_DB_ROLE_MASTER, url);
    RETURN_IFERR(ret);
    ret = GmcSetDbRole(slaveLocation, GMC_DB_ROLE_SLAVE, url);
    RETURN_IFERR(ret);
    // 等待备可访问
    ret = GmcWait(slaveLocation, GMC_DB_STATUS_ACCESSIBLE, waitTimeout);
    RETURN_IFERR(ret);
    ret = GmcSlaveOnline(masterLocation, url);
    RETURN_IFERR(ret);
    // 等待主可同步备份
    ret = GmcWait(masterLocation, GMC_DB_STATUS_BACKUP_ENABLED, waitTimeout);
    return ret;
}

int HaConfigOnlySlave(const char *masterLocation, const char *slaveLocation, const char *url, int waitTimeout,
    bool withWaitBackup = true)
{
    int ret = GmcSetDbRole(slaveLocation, GMC_DB_ROLE_SLAVE, url);
    RETURN_IFERR(ret);
    // 等待备可访问
    ret = GmcWait(slaveLocation, GMC_DB_STATUS_ACCESSIBLE, waitTimeout);
    RETURN_IFERR(ret);
    ret = GmcSlaveOnline(masterLocation, url);
    RETURN_IFERR(ret);
    // 等待主可同步备份
    if (withWaitBackup) {
        ret = WaitFullBackupFinish(masterLocation, waitTimeout);
    } else {
        usleep(30000);
    }
    return ret;
}

int StartEnvWithConfig(int num, int count, ...)
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    va_list varList;
    char tempCmd[200] = {0};
    const char *strKey;
    va_start(varList, count);
    char socket[200] = {0};
    char tempStr[200] = {0};
    for (int i = 0; i < count; i++) {
        strKey = va_arg(varList, const char *);
        (void)strcpy(tempStr, strKey);
        if (strstr(tempStr, "localLocatorListened")) {
            char *temp = strstr(tempStr, "=");
            (void)strcpy(socket, temp + 1);
        }
        (void)sprintf(tempCmd, "sh $TEST_HOME/tools/modifyCfg.sh \"%s\"", strKey);
        printf("tempcmd: %s\n", tempCmd);
        system(tempCmd);
    }
    va_end(varList);
    int ret = GtExecSystemCmd("sh startServer.sh -n %d -s '%s'", num, socket);
    return ret;
}

int TestGmcConnectLocator(GmcConnT **connOut, GmcStmtT **stmt = NULL, const char *serverLocator = NULL,
    int syncMode = 0, bool needEpoll = 1, EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL,
    const void *chanRingLen = NULL)
{
    int ret;

    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, serverLocator);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testMallocConnOptions failed, ret = %d.\n", ret);
        return ret;
    }

    ret = testGmcConnect(connOut, stmt, syncMode, needEpoll, epollReg, connName, chanRingLen, connOption);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testGmcConnect failed, ret = %d.\n", ret);
        testFreeConnOptions(connOption);
        return ret;
    }
    testFreeConnOptions(connOption);
    return ret;
}

int ConnMaster()
{
    AW_FUN_Log(LOG_STEP, "ConnMaster...");
    int ret;
    // 建连接 主节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_masterConn[i], &g_masterStmt[i], g_connServer);
        RETURN_IFERR(ret);
    }
    return ret;
}

int ConnSlave()
{
    AW_FUN_Log(LOG_STEP, "ConnSlave...");
    int ret;
    // 建连接 备节点
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = TestGmcConnectLocator(&g_slaveConn[i], &g_slaveStmt[i], g_connServerSlave);
        RETURN_IFERR(ret);
    }
    return ret;
}

int DisConnMaster()
{
    AW_FUN_Log(LOG_STEP, "DisConnMaster...");
    int ret;
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_masterConn[i], g_masterStmt[i]);
        RETURN_IFERR(ret);
    }
    return ret;
}

int DisConnSlave()
{
    AW_FUN_Log(LOG_STEP, "DisConnSlave...");
    int ret;
    for (int i = 0; i < CONN_COUNT; i++) {
        ret = testGmcDisconnect(g_slaveConn[i], g_slaveStmt[i]);
        RETURN_IFERR(ret);
    }
    return ret;
}

void CleanSlavePstFile()
{
    (void)Rmdir(g_dbFilePath[1]);
    int ret = mkdir(g_dbFilePath[1], S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void OffAndStartSlave()
{
    int ret;
    DisConnSlave();
    ret = GmcSlaveOffline(g_connServer);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -s");
    // 清空备板持久化文件
    CleanSlavePstFile();
    system("sh $TEST_HOME/tools/start.sh -s");
}

void ReOnlineSlave(int waitTimeout = 15)
{
    int ret;
    OffAndStartSlave();
    // 配置主备
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, waitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
}

int DDLTable(GmcStmtT *stmt, const char *lableName, int suffixStart, int suffixEnd, int operateTime)
{
    char *schema = NULL;
    int ret;
    readJanssonFile((char *)"./schema/var_schema.gmjson", &schema);
    if (schema == NULL) {
        return T_FAILED;
    }
    char tableName[30] = {0};
    for (int i = 0; i < operateTime; i++) {
        for (int j = suffixStart; j <= suffixEnd; j++) {
            (void)snprintf(tableName, sizeof(tableName), "%s_%d", lableName, j);
            ret = GmcCreateVertexLabelWithName(stmt, schema, g_config2, tableName);
            AW_FUN_Log(LOG_INFO, "[GmcCreateVertexLabelWithName] ret = %d.", ret);
            AW_MACRO_EXPECT_EQ_BOOL(true, (ret == T_OK || ret == GMERR_DUPLICATE_TABLE));
            ret = GmcDropVertexLabel(stmt, tableName);
            AW_FUN_Log(LOG_INFO, "[GmcDropVertexLabel] ret = %d.", ret);
            if (g_ddlExceptionCheck) {
                AW_MACRO_EXPECT_EQ_BOOL(true,
                    (ret == T_OK || ret == GMERR_UNDEFINED_TABLE || ret == GMERR_DATA_EXCEPTION));
            } else {
                AW_MACRO_EXPECT_EQ_BOOL(true, (ret == T_OK || ret == GMERR_UNDEFINED_TABLE));
            }
        }
    }
    free(schema);
    schema = NULL;
    return ret;
}

int InsertFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        int8_t a9 = i % 16;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_PARTITION, &a9, sizeof(int8_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int ReplaceFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 100000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 100001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 100002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        int8_t a9 = i % 16;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_PARTITION, &a9, sizeof(int8_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int UpdateFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 200000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 200001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 200002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int MergeFixedVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_MERGE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 300000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 300001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 300002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_DATA_EXCEPTION) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int InsertVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{0},
    "A2": %i{0},
    "A3": %i{0},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}i",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0}
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int ReplaceVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    char *complexSet = (char *)R"({
    "A0": %i{0},
    "A1": %i{100000},
    "A2": %i{100001},
    "A3": %i{100002},
    "A4": %i{1, 100000, 0.5},
    "A5": %i{10, 100000, 0.5},
    "A6": "%f{16}0",
    "A7": "0x%f{32}0",
    "A8": "0x%f{32}0",
    "A9": "%f{20}r",
    "M0": [
    { "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10}t1" },
    { "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10}t2" },
    { "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10}t3" }
    ],
    "h1": %i{0},
    "h2": %i{0}
    })";
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        ret = GenFormatDataByIndex(complexSet, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int UpdateVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    char strValue[maxLen] = {0};
    (void)memset(strValue, 'u', maxLen - 1);
    strValue[maxLen - 1] = '\0';
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 200000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 200001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 200002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int MergeVarVertex(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    char strValue[maxLen] = {0};
    (void)memset(strValue, 'm', maxLen - 1);
    strValue[maxLen - 1] = '\0';
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_MERGE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 300000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        uint32_t a2 = i + 300001;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint64_t a3 = i + 300002;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(uint64_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_DATA_EXCEPTION) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return T_OK;
}

int CommonDelete(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelName(stmt, lableName, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_pIndexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (g_ignoreConflict) {
            if (ret == GMERR_UNDEFINED_TABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int KvSet(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[20] = {0};
    int32_t setValue = 0;
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        setValue = i;
        ret = GmcKvSet(stmt, kvKey, strlen(kvKey), &setValue, sizeof(int32_t));
        if (g_ignoreConflict) {
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int KvRemove(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[20] = {0};
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        ret = GmcKvRemove(stmt, kvKey, strlen(kvKey));
        if (g_ignoreConflict) {
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = T_OK;
            }
        }
        RETURN_IFERR(ret);
    }
    return ret;
}

int KvCheckGet(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[10] = {0};
    int32_t expectValue = 0;
    int32_t getValue = 0;
    uint32_t getValueLen = sizeof(int32_t);
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        expectValue = i;
        ret = GmcKvGet(stmt, kvKey, strlen(kvKey), &getValue, &getValueLen);
        RETURN_IFERR(ret);
        if (expectValue != getValue) {
            AW_FUN_Log(LOG_STEP, "expectValue: %d getValue: %d", expectValue, getValue);
            return T_FAILED;
        }
    }
    return ret;
}

int KvCheckCount(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex, int expectCount)
{
    int ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    RETURN_IFERR(ret);
    char kvKey[10] = {0};
    int32_t expectValue = 0;
    int32_t getValue = 0;
    uint32_t getValueLen = sizeof(int32_t);
    int count = 0;
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvKey, sizeof(kvKey), "key%d", i);
        expectValue = i;
        ret = GmcKvGet(stmt, kvKey, strlen(kvKey), &getValue, &getValueLen);
        if (ret == T_OK) {
            count++;
        }
    }
    if (count != expectCount) {
        AW_FUN_Log(LOG_STEP, "expectCount: %d actualCount: %d", expectCount, count);
        return T_FAILED;
    }
    if (ret = GMERR_NO_DATA) {
        return T_OK;
    }
    return ret;
}

int CheckInsertFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format, "A0(int32)=%i{0};A1(int64)=%i{0};A2(uint32)=%i{0};A3(uint64)=%i{0};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckReplaceFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{100000};A2(uint32)=%i{100001};A3(uint64)=%i{100002};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckUpdateFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{200000};A2(uint32)=%i{200001};A3(uint64)=%i{200002};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckMergeFixedTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{300000};A2(uint32)=%i{300001};A3(uint64)=%i{300002};A7(uint16)=10");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckInsertVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format, "A0(int32)=%i{0};A1(int64)=%i{0};A2(uint32)=%i{0};A3(uint64)=%i{0};A9(string)=%f{20}i");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckReplaceVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{100000};A2(uint32)=%i{100001};A3(uint64)=%i{100002};A9(string)=%f{20}r");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckUpdateVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{200000};A2(uint32)=%i{200001};A3(uint64)=%i{200002};A9(string)=%f{99}u");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckMergeVarTb(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{300000};A2(uint32)=%i{300001};A3(uint64)=%i{300002};A9(string)=%f{99}m");
    ret = TestSelVertexRecord(stmt, lableName, g_pIndexName, g_cond, format, bIndex, eIndex, 0);
    return ret;
}

int CheckRecordCount(GmcStmtT *stmt, const char *lableName, int bIndex, int eIndex, uint32_t expectCount)
{
    int ret = T_OK;
    ret = TestSelVertexCount(stmt, lableName, g_pIndexName, g_cond, expectCount, bIndex, eIndex, 0);
    return ret;
}

int CommonCreateTable(GmcStmtT *stmt, const char *lableName, const char *schemaFilePath, const char *config)
{
    char *schema = NULL;
    // 简单表
    readJanssonFile(schemaFilePath, &schema);
    if (schema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabelWithName(stmt, schema, config, lableName);
    free(schema);
    schema = NULL;
    return ret;
}

int ImportAllowlist(const char *path, char *connLocator)
{
    char command[512] = {0};
    (void)snprintf(command, sizeof(command), "gmrule -c import_allowlist -f %s -s %s ", path, connLocator);
    int ret = executeCommand(command, "successfully.");
    return ret;
}

int RemoveAllowlist(const char *path, char *connLocator)
{
    char command[512] = {0};
    (void)snprintf(command, sizeof(command), "gmrule -c remove_allowlist -f %s -s %s ", path, connLocator);
    int ret = executeCommand(command, "successfully.");
    return ret;
}

int ImportPolicy(const char *path, char *connLocator)
{
    char command[512] = {0};
    (void)snprintf(command, sizeof(command), "gmrule -c import_policy -f %s -s %s ", path, connLocator);
    int ret = executeCommand(command, "successfully.");
    if (ret != GMERR_OK) {
        GtExecSystemCmd(command);
    }
    return ret;
}

int ImportPolicyAsync(const char *path, char *connLocator)
{
    char command[512] = {0};
    (void)snprintf(command, sizeof(command), "gmrule -c import_policy -f %s -s %s &", path, connLocator);
    int ret = GtExecSystemCmd(command);
    return ret;
}

int RevokePolicy(const char *path, char *connLocator)
{
    char command[512] = {0};
    (void)snprintf(command, sizeof(command), "gmrule -c revoke_policy -f %s -s %s ", path, connLocator);
    int ret = executeCommand(command, "successfully.");
    return ret;
}

int RevokePolicyAsync(const char *path, char *connLocator)
{
    char command[512] = {0};
    (void)snprintf(command, sizeof(command), "gmrule -c revoke_policy -f %s -s %s &", path, connLocator);
    int ret = GtExecSystemCmd(command);
    return ret;
}

int CheckAllowlist(const char *userName, const char *proName, char *connLocator, bool isUser = true)
{
    int ret;
    if (isUser) {
        ret = GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_USER_STAT' -f USER_NAME=%s -s %s |grep %s", userName,
            connLocator, proName);
        RETURN_IFERR(ret);
    }
    if (isUser) {
        ret = GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_ROLE_STAT' -f ROLE_NAME=%s -s %s |grep -A6 'PROCESS_NAME: %s' "
            "| grep 'IS_USER: 1'",
            userName, connLocator, proName);
    } else {
        ret = GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_ROLE_STAT' -f ROLE_NAME=%s -s %s |grep -A6 'PROCESS_NAME: %s' "
            "| grep 'IS_USER: 0'",
            userName, connLocator, proName);
    }
    return ret;
}

int CheckSysPolicy(const char *userName, const char *proName, const char *priv, const char *objType, char *connLocator)
{
    int ret;
    ret = GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_USER_STAT' -f PROCESS_NAME=%s -s %s |grep -A100 'USER_NAME: "
                          "%s'|grep -A100 SYS_PRIVILEGE "
        "|grep -A5 'OBJ_TYPE: %s'|grep 'PRIVILEGES: %s'",
        proName, connLocator, userName, objType, priv);
    if (ret != GMERR_OK) {
        GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_USER_STAT' -s %s", connLocator);
    }
    return ret;
}

int CheckObjPolicy(const char *userName, const char *proName, const char *priv, const char *objName, char *connLocator)
{
    int ret;
    ret = GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_ROLE_STAT' -f PROCESS_NAME=%s -s %s |grep -A100 'ROLE_NAME: "
                          "%s'|grep -A100 OBJ_PRIVILEGE  "
        "|grep -A5 'OBJ_NAME: %s'|grep 'PRIVILEGES: %s'",
        proName, connLocator, userName, objName, priv);
    if (ret != GMERR_OK) {
        GtExecSystemCmd("gmsysview -q 'V$PRIVILEGE_ROLE_STAT' -f PROCESS_NAME=%s -s %s", proName, connLocator);
    }
    return ret;
}

int CreatePbVertex(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix, int repMode)
{
    int ret;
    char tableName[50];
    char *format1 = (char *)"{\"max_record_count\": 100000000, \"isFastReadUncommitted\":0, \"replication\":%d}";
    char config[300] = {0};
    (void)snprintf(config, sizeof(config), format1, repMode);
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s_%d", tbNamePrefix, i);
        (void)GmcDropVertexLabel(stmt, tableName);
        ret = CommonCreateTable(stmt, tableName, (char *)"./schema/fixed_schema.gmjson", config);
        RETURN_IFERR(ret);
        ret = InsertFixedVertex(stmt, tableName, 0, 500);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int DropPbVertex(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix)
{
    int ret;
    char tableName[50];
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s_%d", tbNamePrefix, i);
        ret = GmcDropVertexLabel(stmt, tableName);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CheckPbVertex(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix)
{
    int ret;
    int beginIndex = 0;
    int endIndex = 500;
    int nsCount = 20;
    char tableName[50];
    char nameSpace[50] = {0};
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s_%d", tbNamePrefix, i);
        ret = CheckInsertFixedTb(stmt, tableName, beginIndex, endIndex);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CreatePbKv(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix, int repMode)
{
    int ret;
    int beginIndex = 0;
    int endIndex = 500;
    char *format2 = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": %d}";
    char kvConfig[300] = {0};
    (void)snprintf(kvConfig, sizeof(kvConfig), format2, repMode);
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    char kvTableName[50] = {0};
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "%s_%d", tbNamePrefix, i);
        (void)GmcKvDropTable(stmt, kvTableName);
        ret = GmcKvCreateTable(stmt, kvTableName, kvConfig);
        RETURN_IFERR(ret);
        ret = KvSet(stmt, kvTableName, beginIndex, endIndex);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int DropPbKv(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix)
{
    int ret;
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    char kvTableName[50] = {0};
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "%s_%d", tbNamePrefix, i);
        ret = GmcKvDropTable(stmt, kvTableName);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CheckPbKv(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix)
{
    int ret;
    int beginIndex = 0;
    int endIndex = 500;
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    char kvTableName[50] = {0};
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "%s_%d", tbNamePrefix, i);
        ret = KvCheckGet(stmt, kvTableName, beginIndex, endIndex);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CreateEmptyNs(GmcStmtT *stmt, int bIndex, int eIndex, char *nsNamePrefix)
{
    int ret;
    char nameSpace[50] = {0};
    const char *userName = "user";
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "%s_%d", nsNamePrefix, i);
        ret = GmcCreateNamespace(stmt, nameSpace, userName);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CheckEmptyNs(GmcStmtT *stmt, int bIndex, int eIndex, char *nsNamePrefix)
{
    int ret;
    int nsCount = 20;
    char nameSpace[50] = {0};
    const char *userName = "user";
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "%s_%d", nsNamePrefix, i);
        ret = GmcUseNamespace(stmt, nameSpace);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CreatePbEmptyVertex(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix, int repMode)
{
    int ret;
    char tableName[50];
    char *format1 = (char *)"{\"max_record_count\": 100000000, \"isFastReadUncommitted\":0, \"replication\":%d}";
    char config[300] = {0};
    (void)snprintf(config, sizeof(config), format1, repMode);
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s_%d", tbNamePrefix, i);
        (void)GmcDropVertexLabel(stmt, tableName);
        ret = CommonCreateTable(stmt, tableName, (char *)"./schema/fixed_schema.gmjson", config);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

int CheckPbEmptyVertex(GmcStmtT *stmt, int bIndex, int eIndex, char *tbNamePrefix)
{
    int ret;
    ret = GmcUseNamespace(stmt, (char *)"public");
    RETURN_IFERR(ret);
    uint32_t labelType = 0;
    char tableName[50];
    for (int i = bIndex; i < eIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s_%d", tbNamePrefix, i);
        ret = GmcGetLabelTypeByName(stmt, tableName, &labelType);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

void CheckReplica(const char *masterLocation, const char *slaveLocation, int replicationMode)
{
    int ret;
    char *format1 = (char *)"{\"max_record_count\": 100000000, \"isFastReadUncommitted\":0, \"replication\":%d}";
    char config[300] = {0};
    (void)snprintf(config, sizeof(config), format1, replicationMode);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *connSlave = NULL;
    GmcStmtT *stmtSlave = NULL;
    ret = TestGmcConnectLocator(&conn, &stmt, masterLocation);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcConnectLocator(&connSlave, &stmtSlave, slaveLocation);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateEmptyNs(stmt, 0, 2, (char *)"ns_rep");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreatePbEmptyVertex(stmt, 0, 2, (char *)"vertex_empty_rep", replicationMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreatePbVertex(stmt, 0, 2, (char *)"vertex_rep", replicationMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreatePbKv(stmt, 0, 2, (char *)"kv_rep", replicationMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (replicationMode == 1) {
        ret = WaitReplicateFinish();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = CheckEmptyNs(stmtSlave, 0, 2, (char *)"ns_rep");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(stmtSlave, 0, 2, (char *)"vertex_empty_rep");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(stmtSlave, 0, 2, (char *)"vertex_rep");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(stmtSlave, 0, 2, (char *)"kv_rep");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connSlave, stmtSlave);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


void Swap(char *location1, char *location2)
{
    char temp[64];
    (void)snprintf(temp, sizeof(temp), "%s", location1);
    (void)snprintf(location1, 64, "%s", location2);
    (void)snprintf(location2, 64, "%s", temp);
}

int AsSwitch(char *masterLocation, char *slaveLocation, bool disConnBeforeStop = true)
{
    AW_FUN_Log(LOG_STEP, "AsSwitch...");
    int ret;
    if (disConnBeforeStop) {
        ret = DisConnMaster();
        RETURN_IFERR(ret);
    }
    system("sh $TEST_HOME/tools/stop.sh -m");
    if (!disConnBeforeStop) {
        for (int i = 0; i < CONN_COUNT; i++) {
            (void)testGmcDisconnect(g_masterConn[i], g_masterStmt[i]);
        }
    }
    GmcUnInitByInstance(g_instance1);
    ret = GmcToMaster(slaveLocation);
    RETURN_IFERR(ret);
    system("sh $TEST_HOME/tools/start.sh -m");
    ret = HaConfigOnlySlave(slaveLocation, masterLocation, g_url, g_gmcWaitTimeout);
    RETURN_IFERR(ret);
    ret = DisConnSlave();
    RETURN_IFERR(ret);
    Swap(masterLocation, slaveLocation);
    ret = ConnMaster();
    RETURN_IFERR(ret);
    ret = ConnSlave();
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int ReAsSwitch(char *masterLocation, char *slaveLocation, bool disConnBeforeStop = true)
{
    int ret;
    if (disConnBeforeStop) {
        ret = DisConnMaster();
        RETURN_IFERR(ret);
    }
    // 因第一次倒换备已变主，所以此处-s
    system("sh $TEST_HOME/tools/stop.sh -s");
    if (!disConnBeforeStop) {
        for (int i = 0; i < CONN_COUNT; i++) {
            (void)testGmcDisconnect(g_masterConn[i], g_masterStmt[i]);
        }
    }
    GmcUnInitByInstance(g_instance2);
    ret = GmcToMaster(slaveLocation);
    RETURN_IFERR(ret);
    system("sh $TEST_HOME/tools/start.sh -s");
    ret = HaConfigOnlySlave(slaveLocation, masterLocation, g_url, g_gmcWaitTimeout);
    RETURN_IFERR(ret);
    ret = DisConnSlave();
    RETURN_IFERR(ret);
    swap(masterLocation, slaveLocation);
    ret = ConnMaster();
    RETURN_IFERR(ret);
    ret = ConnSlave();
    RETURN_IFERR(ret);
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif

#endif /* PERMSYNC_H */

