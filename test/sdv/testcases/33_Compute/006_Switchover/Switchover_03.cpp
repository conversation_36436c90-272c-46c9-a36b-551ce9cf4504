/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: DB支持主板和备板倒换 - 倒换交互实时备份（异步复制）
 * Author: lushiguang
 * Create: 2025-02-27
 */

#include "Switchover.h"


class Switchover03 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

int g_repMode = 1;
int g_bIndex = 0;
int g_eIndex = 10;

void StartEnvAndConn()
{
    int ret;
    // 主节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -m");
    // 备节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -s");

    // 配置主备
    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ConnMaster();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ConnSlave();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void Switchover03::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 并发业务中退出，可能返回断开错误码
    g_ignoreError = true;
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_RESET_BY_PEER, GMERR_MEMORY_OPERATE_FAILED,
        GMERR_NULL_VALUE_NOT_ALLOWED);
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i + 1);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    StartEnvAndConn();
    AW_CHECK_LOG_BEGIN();
}

void Switchover03::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    // 断开连接
    ret = DisConnMaster();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DisConnSlave();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 001、实备vertex表创建,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    char tablePrefix2[] = "rep2_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    int bIndex = 0;
    int eIndex = 50;
    ret = AsyncTbDDLOperate(g_masterStmt[0], tablePrefix2, bIndex, eIndex, T_CREATE_VERTEX, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、实备vertex表删除,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    char tablePrefix2[] = "rep2_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int bIndex = 0;
    int eIndex = 50;
    ret = CreatePbVertex(g_masterStmt[0], bIndex, eIndex, tablePrefix2, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncTbDDLOperate(g_masterStmt[0], tablePrefix2, bIndex, eIndex, T_DROP_VERTEX, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、实备vertex表truncate,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    char tablePrefix2[] = "rep2_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int bIndex = 0;
    int eIndex = 50;
    ret = CreatePbVertex(g_masterStmt[0], bIndex, eIndex, tablePrefix2, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 交互业务
    ret = AsyncTbDDLOperate(g_masterStmt[0], tablePrefix2, bIndex, eIndex, T_TRUNCATE_VERTEX, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、实备vertex表insert,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";

    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、实备vertex表replace,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = UpdateFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpdateFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、实备vertex表update,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、实备vertex表merge,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、实备vertex表delete,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncFixedTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、实备kv表创建,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    char tablePrefix2[] = "rep2_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    int bIndex = 0;
    int eIndex = 50;
    ret = AsyncTbDDLOperate(g_masterStmt[0], tablePrefix2, bIndex, eIndex, T_CREATE_KV, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、实备kv表删除,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    char tablePrefix2[] = "rep2_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int bIndex = 0;
    int eIndex = 100;
    ret = CreatePbKv(g_masterStmt[0], bIndex, eIndex, tablePrefix2, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncTbDDLOperate(g_masterStmt[0], tablePrefix2, bIndex, eIndex, T_DROP_KV, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、实备kv表truncate,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    char tablePrefix2[] = "rep2_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int bIndex = 0;
    int eIndex = 100;
    ret = CreatePbKv(g_masterStmt[0], bIndex, eIndex, tablePrefix2, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncTbDDLOperate(g_masterStmt[0], tablePrefix2, bIndex, eIndex, T_TRUNCATE_KV, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、实备kv表set,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *format2 = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": %d}";
    char kvConfig[300] = {0};
    (void)snprintf(kvConfig, sizeof(kvConfig), format2, g_repMode);
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, kvConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex, T_KVSET);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、实备kv表remove,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *format2 = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": %d}";
    char kvConfig[300] = {0};
    (void)snprintf(kvConfig, sizeof(kvConfig), format2, g_repMode);
    // 主板建表写数据
    char *kvTableName = (char *)"system_info";
    (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
    ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, kvConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 交互业务
    ret = AsyncKvTbOperate(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex, T_KVREMOVE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、实备交互式小事务,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务

    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    g_endIndex = 300;
    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、实备交互式大事务,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.readOnly = false;
    g_trxConfig.type = GMC_TX_ISOLATION_COMMITTED; // RC
    g_trxConfig.trxType = GMC_PESSIMISITIC_TRX;    // 悲观事务

    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/fixed_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    g_endIndex = 5000;
    ret = AsyncFixedTrx(g_masterConn[0], g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_TRX);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceFixedVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceFixedTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、实备非交互式大事务,交互主备倒换  -- 正常倒换,倒换后正常同步
TEST_F(Switchover03, Compute_006_001_03_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    char tablePrefix1[] = "rep1_table";
    // 十张表,写一定数据,用于后面存量校验
    ret = CreatePbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1, g_repMode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_masterStmt[0], g_tableName);
    // 主板建表写数据
    ret = CommonCreateTable(g_masterStmt[0], g_tableName, (char *)"./schema/var_schema.gmjson", g_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = InsertVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互业务
    g_endIndex = 100;
    ret = AsyncVarTbOperate(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex, T_REPLACE_LOB);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主备倒换
    ret = AsSwitch(g_connServer, g_connServerSlave, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = JoinAsyncThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证存量数据
    ret = CheckPbVertex(g_masterStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0], g_bIndex, g_eIndex, tablePrefix1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 倒换后重新验证验证新数据同步
    CheckReplica(g_connServer, g_connServerSlave, g_repMode);
    ret = ReplaceVarVertex(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WaitReplicateFinish();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceVarTb(g_masterStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckReplaceVarTb(g_slaveStmt[0], g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

