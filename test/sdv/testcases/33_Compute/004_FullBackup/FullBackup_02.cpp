/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 主备复制-支持批量数据复制DML - 批备过程中交互实备（同步复制）
 * Author: lushiguang
 * Create: 2025-02-05
 */

#include "FullBackup.h"

class FullBackup02 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void StartEnvAndConn()
{
    int ret;
    // 主节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -m");
    // 备节点
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -s");

    // 配置主备
    ret = HaConfig(g_connServer, g_connServerSlave, g_url, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnMaster();
    ConnSlave();
}

void FullBackup02::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    g_endIndex = 500;
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddReplicationCfg();
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"pageSize", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    for (int i = 0; i < MAX_INSTANCE_COUNT; i++) {
        (void)sprintf(g_dbFilePath[i], "%s/gmdb_instance_%d", pwdDir, i + 1);
        (void)Rmdir(g_dbFilePath[i]);
        ret = mkdir(g_dbFilePath[i], S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    StartEnvAndConn();
    // 并发可能有抢锁操作，错误加白名单
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_LOCK_NOT_AVAILABLE);
    AW_CHECK_LOG_BEGIN();
}

void FullBackup02::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret;
    // 断开连接
    DisConnMaster();
    DisConnSlave();
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

// 001、批备过程中，删除namespace  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int nsCount = 20;
    char nameSpace[50] = {0};
    const char *userName = "user";
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcDropNamespace(g_masterStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcUseNamespace(g_slaveStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    }
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcUseNamespace(g_masterStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    }
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_OBJECT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、批备过程中，删除vertex表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = GmcDropVertexLabel(g_masterStmt[0], tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t labelType = 0;
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = GmcGetLabelTypeByName(g_masterStmt[0], tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、批备过程中，删除kv表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char kvTableName[50] = {0};
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = GmcKvDropTable(g_masterStmt[0], kvTableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t labelType = 1;
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], kvTableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }

    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = GmcGetLabelTypeByName(g_masterStmt[0], kvTableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、批备过程中，删除不存在的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lableDDDD_%d", i);
        ret = GmcDropVertexLabel(g_masterStmt[0], tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、批备过程中，创建同名namespace  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    // 创建同名ns
    int nsCount = 20;
    char nameSpace[50] = {0};
    const char *userName = "user";
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep_%d", i);
        ret = GmcCreateNamespace(g_masterStmt[0], nameSpace, userName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_OBJECT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、批备过程中，创建不同名namespace  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    // 创建不同名ns
    int nsCount = 20;
    char nameSpace[50] = {0};
    const char *userName = "user";
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep2_%d", i);
        ret = GmcCreateNamespace(g_masterStmt[0], nameSpace, userName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep2_%d", i);
        ret = GmcUseNamespace(g_masterStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < nsCount; i++) {
        (void)snprintf(nameSpace, sizeof(nameSpace), "empty_db_rep2_%d", i);
        ret = GmcUseNamespace(g_slaveStmt[0], nameSpace);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、批备过程中，创建同名vertex表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    // public空间创建200张同名空表
    int tableCount = 200;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "empty_lable_%d", i);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/var_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、批备过程中，创建不同名vertex表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    // public空间创建200张同名空表
    int tableCount = 200;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "empty_lable2_%d", i);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/var_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t labelType = 0;
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "empty_lable2_%d", i);
        ret = GmcGetLabelTypeByName(g_masterStmt[0], tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "empty_lable2_%d", i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], tableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、批备过程中，创建同名kv表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char *kvConfig = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": 2}";
    char kvTableName[50] = {0};
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, kvConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、批备过程中，创建不同名kv表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char *kvConfig = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": 2}";
    char kvTableName[50] = {0};
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, kvConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t labelType = 1;
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = GmcGetLabelTypeByName(g_slaveStmt[0], kvTableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = GmcGetLabelTypeByName(g_masterStmt[0], kvTableName, &labelType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、批备过程中，insert操作批备中的表，存在数据冲突  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = InsertFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PRIMARY_KEY_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、批备过程中，insert操作批备中的表，不存在数据冲突  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = InsertFixedVertex(g_masterStmt[0], tableName, g_endIndex, g_endIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckInsertFixedTb(g_masterStmt[0], tableName, g_endIndex, g_endIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckInsertFixedTb(g_slaveStmt[0], tableName, g_endIndex, g_endIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、批备过程中，insert操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = InsertFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckInsertFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckInsertFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_beginIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、批备过程中，replace操作批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = ReplaceFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckReplaceFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckReplaceFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、批备过程中，replace操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ReplaceFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckReplaceFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckReplaceFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_beginIndex + 200);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、批备过程中，update操作批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = UpdateFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckUpdateFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckUpdateFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、批备过程中，update操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ReplaceFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = UpdateFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckUpdateFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckUpdateFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、批备过程中，merge操作批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = MergeFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckMergeFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckMergeFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、批备过程中，merge操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ReplaceFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = MergeFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckMergeFixedTb(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckMergeFixedTb(g_slaveStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、批备过程中，delete操作批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CommonDelete(g_masterStmt[0], tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckRecordCount(g_masterStmt[0], tableName, g_beginIndex, g_endIndex, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable_%d", i);
        ret = CheckRecordCount(g_slaveStmt[0], tableName, g_beginIndex, g_endIndex, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、批备过程中，delete操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char tableName[50];
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        (void)GmcDropVertexLabel(g_masterStmt[0], tableName);
        ret = CommonCreateTable(g_masterStmt[0], tableName, (char *)"./schema/fixed_schema.gmjson", g_config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ReplaceFixedVertex(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = CommonDelete(g_masterStmt[0], tableName, g_beginIndex, g_beginIndex + 100);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckRecordCount(g_masterStmt[0], tableName, g_beginIndex, g_endIndex, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "vertex_lable2_%d", i);
        ret = CheckRecordCount(g_slaveStmt[0], tableName, g_beginIndex, g_endIndex, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、批备过程中，set操作批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char kvTableName[50] = {0};
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex + 1000);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex + 1000);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex + 1000);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023、批备过程中，set操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char kvTableName[50] = {0};
    char *kvConfig = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": 2}";
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
        ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, kvConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、批备过程中，remove操作批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char kvTableName[50] = {0};
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = KvRemove(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable_%d", i);
        ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、批备过程中，remove操作非批备中的表  -- 正常同步
TEST_F(FullBackup02, Compute_004_001_02_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    DoOperateInMaster(2);
    OffAndStartSlave();
    // 配置主备，不等待备份完成
    ret = HaConfigOnlySlave(g_connServer, g_connServerSlave, g_url, 10, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 交互主板业务操作
    int tableCount = 20;
    char kvTableName[50] = {0};
    char *kvConfig = (char *)"{\"name\": \"system_info\", \"max_record_count\": 100000000, \"replication\": 2}";
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        (void)GmcKvDropTable(g_masterStmt[0], kvTableName);
        ret = GmcKvCreateTable(g_masterStmt[0], kvTableName, kvConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = KvSet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = KvRemove(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待全量备份完成
    ret = WaitFullBackupFinish(g_connServer, g_gmcWaitTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConnSlave();
    // 备板验证已同步
    ret = CheckEmptyNs(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckNsVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbEmptyVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbVertex(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckPbKv(g_slaveStmt[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = KvCheckGet(g_masterStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
    }
    for (int i = 0; i < tableCount; i++) {
        (void)snprintf(kvTableName, sizeof(kvTableName), "kv_lable2_%d", i);
        ret = KvCheckGet(g_slaveStmt[0], kvTableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_FUN_Log(LOG_STEP, "test end.");
}

