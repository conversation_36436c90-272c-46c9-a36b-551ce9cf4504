#include "../incre_pst_common.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
int g_beginIndex = 0;
int g_endIndex = 200;
char g_soPath[256];
char g_soBakPath[256];

class LightDemandPst2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase() {}
};

int DisAndClean()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    ret = GmcUnInit();
    RETURN_IFERR(ret);
    ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}


void LightDemandPst2::SetUp()
{
    AW_FUN_Log(LOG_STEP, "test set up.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret;
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm -rf gmdb*");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_FUN_Log(LOG_STEP, "mkdir %s ret = %d", g_dbFilePath, ret);
    Addini((char *)"persistentCompressMode", (char *)"0");
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofSoPath", (char *)"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"tamperProofEnable", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_soPath, "%s/../lib/libgmdb_digest.so", pwdDir);
    AW_FUN_Log(LOG_STEP, "so path %s.", g_soPath);
    ret = ChangeGmserverCfg((char *)"tamperProofSoPath", g_soPath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LightDemandPst2::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[BUFF_MAX_LEN] = {0};
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s -rf", g_dbFilePath);
    system(expectCmd);
    AW_FUN_Log(LOG_STEP, "test tared down.");
}

// 032 光启按需共进程 篡改持久化文件dbSystemSpace头，后重启
TEST_F(LightDemandPst2, Compute_012_032)
{
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbSystemSpace文件头
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbSystemSpace", pwdDir);
    DestoryDataFileHead(dataFilePath, 0);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033 光启按需共进程 篡改持久化文件dbSystemSpace尾，后重启
TEST_F(LightDemandPst2, Compute_012_033)
{
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbSystemSpace文件头
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbSystemSpace", pwdDir);
    DestoryDataFileEnd(dataFilePath, 0);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034 光启按需共进程 篡改持久化文件dbSystemSpace中间content，后重启
TEST_F(LightDemandPst2, Compute_012_034)
{
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 篡改持久化文件dbSystemSpace中间content
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbSystemSpace", pwdDir);
    uint32_t pageIndex = 1;
    uint32_t pageOffset = 20;
    DestoryDataFileContent(dataFilePath, pageIndex, pageOffset);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035 光启按需共进程 篡改持久化文件dbCtrlFile头，后重启
TEST_F(LightDemandPst2, Compute_012_035)
{
    // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");
    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbCtrlFile文件头
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char ctrFilePath[256] = {0};
    (void)sprintf(ctrFilePath, "%s/gmdb/dbCtrlFile", pwdDir);
    DestoryCtrFileHead(ctrFilePath, 0);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 光启按需共进程 篡改持久化文件dbCtrlFile尾，后重启
TEST_F(LightDemandPst2, Compute_012_036)
{
    // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbCtrlFile尾
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char ctrFilePath[256] = {0};
    (void)sprintf(ctrFilePath, "%s/gmdb/dbCtrlFile", pwdDir);
    DestoryCtrFileEnd(ctrFilePath, 0);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037 光启按需共进程 篡改持久化文件dbCtrlFile中间content，后重启
TEST_F(LightDemandPst2, Compute_012_037)
{
    // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    system("ls -la gmdb/");
    // 篡改持久化文件dbCtrlFile中间content
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char ctrFilePath[256] = {0};
    (void)sprintf(ctrFilePath, "%s/gmdb/dbCtrlFile", pwdDir);
    uint32_t pageIndex = 1;
    uint32_t pageOffset = 100;  // 为啥offset 为0的时候不报错， 为100的时候报错1001004
    DestoryCtrlFileContent(ctrFilePath, pageIndex, pageOffset);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038 光启按需共进程 交换数据文件dbSystemSpace中的页，后重启
TEST_F(LightDemandPst2, Compute_012_038)
{
    // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    system("ls -la gmdb/");
    // 交换数据文件dbSystemSpace中的页
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbSystemSpace", pwdDir);
    SwapDataFilePage(dataFilePath, 1, 2);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1004000", "Data exception occurs");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

class LightDemandPst3 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase() {}
};

void LightDemandPst3::SetUp()
{
    AW_FUN_Log(LOG_STEP, "test set up.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret;
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm -rf gmdb*");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_FUN_Log(LOG_STEP, "mkdir %s ret = %d", g_dbFilePath, ret);
    Addini((char *)"persistentCompressMode", (char *)"0");
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofSoPath", (char *)"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"tamperProofEnable", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_soPath, "%s/../lib/libgmdb_digest.so", pwdDir);
    AW_FUN_Log(LOG_STEP, "so path %s.", g_soPath);
    ret = ChangeGmserverCfg((char *)"tamperProofSoPath", g_soPath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LightDemandPst3::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[BUFF_MAX_LEN] = {0};
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s -rf", g_dbFilePath);
    system(expectCmd);
    AW_FUN_Log(LOG_STEP, "test tared down.");
}

// 039 光启按需共进程开启页压缩 篡改持久化文件数据文件dbSystemSpace，后重启
TEST_F(LightDemandPst3, Compute_012_039)
{
   // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK_AND_COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbSystemSpace文件
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbSystemSpace", pwdDir);
    FILE *file = fopen(dataFilePath, "r+");
    if (file == NULL) {
        AW_MACRO_ASSERT_EQ_INT(1, ret);
    }

    int offset = 4 * 1024 + 80;
    ret = fseek(file, offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);
    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK_AND_COMPRESS);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

class LightDemandPst4 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase() {}
};

void LightDemandPst4::SetUp()
{
    AW_FUN_Log(LOG_STEP, "test set up.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret;
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm -rf gmdb*");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_FUN_Log(LOG_STEP, "mkdir %s ret = %d", g_dbFilePath, ret);
    Addini((char *)"persistentCompressMode", (char *)"0");
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofSoPath", (char *)"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"tamperProofEnable", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_soPath, "%s/../lib/libgmdb_digest.so", pwdDir);
    AW_FUN_Log(LOG_STEP, "so path %s.", g_soPath);
    ret = ChangeGmserverCfg((char *)"tamperProofSoPath", g_soPath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LightDemandPst4::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[BUFF_MAX_LEN] = {0};
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s -rf", g_dbFilePath);
    system(expectCmd);
    AW_FUN_Log(LOG_STEP, "test tared down.");
}

// 040 光启按需共进程开启device压缩 篡改持久化文件数据文件dbSystemSpace，后重启
TEST_F(LightDemandPst4, Compute_012_040)
{
    // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK_AND_COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbSystemSpace文件
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbSystemSpace", pwdDir);
    FILE *file = fopen(dataFilePath, "r+");
    if (file == NULL) {
        AW_MACRO_ASSERT_EQ_INT(1, ret);
    }

    int offset = 4 * 1024 + 20;
    ret = fseek(file, offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);
    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK_AND_COMPRESS);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

class LightDemandPst5 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase() {}
};

void LightDemandPst5::SetUp()
{
    AW_FUN_Log(LOG_STEP, "test set up.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret;
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm -rf gmdb*");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_FUN_Log(LOG_STEP, "mkdir %s ret = %d", g_dbFilePath, ret);
    Addini((char *)"persistentCompressMode", (char *)"0");
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofSoPath", (char *)"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"tamperProofEnable", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LightDemandPst5::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[BUFF_MAX_LEN] = {0};
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s -rf", g_dbFilePath);
    system(expectCmd);
    AW_FUN_Log(LOG_STEP, "test tared down.");
}

// 041 共进程 tamperProofEnable开启，tamperProofSoPath配置的路径so不存在，用GmsRegAdaptFuncs的方式注册摘要计算函数
// 破坏数据文件dbUndoSpace 重启
TEST_F(LightDemandPst5, Compute_012_041)
{
   // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, TAMPER_PROOF_CHEACK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbUndoSpace
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbUndoSpace", pwdDir);
    uint32_t pageIndex = 1;
    uint32_t pageOffset = 20;
    DestoryDataFileContent(dataFilePath, pageIndex, pageOffset);

    // 重启server失败
    ret = AdapAndStartServer(TAMPER_PROOF_CHEACK_AND_COMPRESS);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}

class LightDemandPst : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase() {}
};

void LightDemandPst::SetUp()
{
    AW_FUN_Log(LOG_STEP, "test set up.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret;
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm -rf gmdb*");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_FUN_Log(LOG_STEP, "mkdir %s ret = %d", g_dbFilePath, ret);
    Addini((char *)"persistentCompressMode", (char *)"0");
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Addini((char *)"tamperProofSoPath", (char *)"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"tamperProofEnable", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_soPath, "%s/../lib/libgmdb_digest.so", pwdDir);
    AW_FUN_Log(LOG_STEP, "so path %s.", g_soPath);
    ret = ChangeGmserverCfg((char *)"tamperProofSoPath", g_soPath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LightDemandPst::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    printf("[INFO] Incremental Persistence cfg test End.\n");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[BUFF_MAX_LEN] = {0};
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s -rf", g_dbFilePath);
    system(expectCmd);
    AW_FUN_Log(LOG_STEP, "test tared down.");
}

// 042 共进程 tamperProofEnable开启，不用GmsRegAdaptFuncs的方式注册摘要计算函数
// 破坏数据文件dbUndoSpace 重启
TEST_F(LightDemandPst, Compute_012_042)
{
    // 清理客户端日志
    system("cat /dev/null > log/run/rLightDemandPst/rLightDemandPst.log");

    int ret = 0;
    ret = GtExecSystemCmd("./MainDml %d %d %d", g_beginIndex, g_endIndex, NOT_REG_GEN_DIG_FUNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int pid = 0;
    pid = GetProcessIdByName("MainDml"); 
    EXPECT_LE(pid, 0);

    // 破坏dbUndoSpace
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_ERROR, "get env PWD fail.");
    }
    char dataFilePath[256] = {0};
    (void)sprintf(dataFilePath, "%s/gmdb/dbUndoSpace", pwdDir);
    uint32_t pageIndex = 1;
    uint32_t pageOffset = 20;
    DestoryDataFileContent(dataFilePath, pageIndex, pageOffset);

    // 重启server失败
    ret = AdapAndStartServer(NOT_REG_GEN_DIG_FUNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 校验错误日志
    system("cat log/run/rLightDemandPst/rLightDemandPst.log");
    bool retOfLog = AW_CHECK_LOG_EXIST(
       CLIENT, 2, "GMERR-1019003", "Persistence data file is corrupted");
    EXPECT_TRUE(retOfLog);

    AW_FUN_Log(LOG_STEP, "test end.");
}


