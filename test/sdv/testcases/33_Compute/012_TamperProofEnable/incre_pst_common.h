/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: incre_pst_common.h
 * Author: lushiguang
 * Create: 2023-09-15
 */
#ifndef INCRE_PST_COMMON_H
#define INCRE_PST_COMMON_H

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/mman.h>
#include "gtest/gtest.h"
#include "t_light.h"
#include "gms.h"

#ifdef __cplusplus
extern "C" {
#endif

#define BUFF_MAX_LEN 1024
#define LOG_IFERR(ret)                                                                                        \
    do {                                                                                                      \
        if ((ret) != T_OK) {                                                                                  \
            fprintf(stdout, "Error: %s:%d func:%s " #ret " = %d\n", __FILE__, __LINE__, __FUNCTION__, (ret)); \
        }                                                                                                     \
    } while (0)


GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync = NULL;
const char *g_simpleLabel = "vl_simple";
const char *g_complexLabel = "vl_general_complex";
char *g_simpleSchema = NULL;
char *g_complexSchema = NULL;
char g_dbFilePath[BUFF_MAX_LEN] = {0};
char g_dbFileCtrlPath[BUFF_MAX_LEN] = {0};
char g_newDbFilePath[BUFF_MAX_LEN] = {0};
char g_dbFilePathNoExist[BUFF_MAX_LEN] = {0};
char g_dbFilePath1[BUFF_MAX_LEN] = {0};
char g_dbFilePath2[BUFF_MAX_LEN] = {0};

uint8_t *g_aptAddr = NULL;
uint32_t g_aptMemSize = 0;
pthread_t g_svThreadId = 0;
int g_shmid;
const char* g_shmName = "/my_shared_memory";

const char *g_config = R"(
    {
        "max_record_count":1000000
    }
)";

const char *g_config2 = R"(
    {
        "max_record_count":1000000,
        "auto_increment":1
    }
)";

char *g_simpleSet = (char *)R"({"A0": %i{1},
"A1": %i{10},
"A2": %i{100},
"A3": %i{1000},
"A4": %i{1,100000,0.5},
"A5": %i{10,100000,0.5}
})";

char *g_complexSet = (char *)R"({
"A0": %i{1},
"A1": %i{10},
"A2": %i{100},
"A3": %i{1000},
"A4": %i{1,100000,0.5},
"A5": %i{10,100000,0.5},
"A6": "%f{16}0",
"A7": "0x%f{32}0",
"A8": "0x%f{32}0",
"A9": "%f{2000}c",
"M0": [
{ "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{1000}t1" },
{ "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{1000}t2" },
{ "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{1000}t3" }
]
})";

int RestartGmserver()
{
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/stop.sh -f");
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -f");
    RETURN_IFERR(ret);
}

int CommonDropTable()
{
    int ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    RETURN_IFERR(ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonDDL()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("../schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    if (ret != T_OK) {
        free(g_simpleSchema);
    }
    RETURN_IFERR(ret);

    // 复杂表
    readJanssonFile("../schema/vl_general_complex.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config);
    if (ret != T_OK) {
        free(g_complexSchema);
    }
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonInsert(int beginIndex, int endIndex)
{
    int ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonUpdate(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5}";
    int ret = TestUpdVertexSync(g_stmt, g_simpleLabel, indexName, cond, updateFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, g_complexLabel, indexName, cond, updateFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonDelete(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    int ret = TestdelVertexSync(g_stmt, g_simpleLabel, indexName, cond, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestdelVertexSync(g_stmt, g_complexLabel, indexName, cond, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckInCount(int expectCount, int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    int ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, expectCount, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, expectCount, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckRecord(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *checkFormat = (char *)"A1(int64)=%i{10};A2(uint32)=%i{100};A5(double)=%i{10,100000,0.5}";
    int ret = TestSelVertexRecord(g_stmt, g_simpleLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    checkFormat =
        (char *)"A1(int64)=%i{10};A5(double)=%i{10,100000,0.5};M0[0].B0(int32)=%i{1};M0[2].B5(string)=%f{1000}t3";
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckUpdRecord(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *checkFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5}";
    int ret = TestSelVertexRecord(g_stmt, g_simpleLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    checkFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5};M0[0].B0(int32)=%i{10};M0[2]."
        "B5(string)=%f{20}f3";
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int32_t GetRedoTruncateBlockId(uint64_t *fieldVal)
{
    char command[BUFF_MAX_LEN];
    int ret = snprintf_s(command, BUFF_MAX_LEN, BUFF_MAX_LEN - 1,
        "gmsysview -q V\\$STORAGE_PERSISTENT_STAT | grep TRUNC_POINT | cut -d ',' -f 2");
    EXPECT_GT(ret, 0);

    system("gmsysview -q V\\$STORAGE_PERSISTENT_STAT");

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        return DB_ERROR;
    }
    EXPECT_NE((void *)NULL, pf);
    char result[BUFF_MAX_LEN] = {0};
    while (NULL != fgets(result, BUFF_MAX_LEN, pf)) {
    };
    (void)fclose(pf);

    *fieldVal = atoi(result);
    AW_FUN_Log(LOG_INFO, "truncate block id is %lu.", *fieldVal);
    return DB_SUCCESS;
}

static const uint32_t pageSize = 32 * BUFF_MAX_LEN;  // 当前配置项 pageSize/ctrlPageSize 默认为32KB
void DestoryDataFileContent(char *filePath, uint32_t pageIndex, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // 文件头，以4KB对齐，实际的数据内容从4KB偏移开始
    // 页头20B的DbLatchT不会纳入CRC的计算
    // PersFileHeadT 结构体4KB对齐
    static const uint32_t dataFileHeadSize = 4 * BUFF_MAX_LEN;
    uint32_t fileOffset = dataFileHeadSize + pageIndex * pageSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryDataFileHead(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_TRUE(file != NULL);

    int ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "before modify file size is %ld.", ftell(file));

    ret = fseek(file, offset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "after modify file size is %ld.", ftell(file));

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryDataFileEnd(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_TRUE(file != NULL);

    int ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "before modify file size is %ld.", ftell(file));

    ret = fseek(file, ftell(file) - 8 - 32, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkk", 1, 8, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "after modify file size is %ld.", ftell(file));

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void SwapDataFilePage(char *filePath, uint32_t pageIndex1, uint32_t pageIndex2)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_TRUE(file != NULL);

    static const uint32_t dataFileHeadSize = 4 * BUFF_MAX_LEN;
    uint32_t page1Offset = dataFileHeadSize + pageIndex1 * pageSize;
    int ret = fseek(file, page1Offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    uint8_t *page1 = (uint8_t *)malloc(pageSize);
    ret = fread(page1, 1, pageSize, file);
    ASSERT_GT(ret, 0);

    uint32_t page2Offset = dataFileHeadSize + pageIndex2 * pageSize;
    ret = fseek(file, page2Offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    uint8_t *page2 = (uint8_t *)malloc(pageSize);
    ret = fread(page2, 1, pageSize, file);
    ASSERT_GT(ret, 0);
    // 把page1写到page2的位置
    ret = fwrite(page1, 1, pageSize, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, page1Offset, SEEK_SET);
    ASSERT_EQ(ret, 0);
    // 把page2写到page1的位置
    ret = fwrite(page2, 1, pageSize, file);
    ASSERT_GT(ret, 0);

    free(page1);
    free(page2);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryCtrFileHead(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_TRUE(file != NULL);

    int ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "before modify file size is %ld.", ftell(file));

    ret = fseek(file, offset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "after modify file size is %ld.", ftell(file));

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryCtrFileEnd(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_TRUE(file != NULL);

    int ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "before modify file size is %ld.", ftell(file));

    ret = fseek(file, ftell(file) - 8 - 32, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkk", 1, 8, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "after modify file size is %ld.", ftell(file));

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryCtrlFileContent(char *filePath, uint32_t pageIndex, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // ctrl文件头大小（固定）
    static const uint32_t ctrlFileHeadSize = 8;  // PersFileHeadT 的大小
    // ctrl file 第一个页为预留页
    uint32_t fileOffset = ctrlFileHeadSize + pageIndex * pageSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryRedoFileHead(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    int ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "before modify file size is %ld.", ftell(file));

    ret = fseek(file, offset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    ret = fseek(file, 0, SEEK_END);
    ASSERT_EQ(ret, 0);
    AW_FUN_Log(LOG_INFO, "after modify file size is %ld.", ftell(file));

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestoryRedoFileContent(char *filePath, uint32_t blockId, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // redo 文件是以512字节对齐 每一个RedoLogBatchT的大小不定
    // 第一个512字节存放的是RedoLogFileHeadT
    // RedoLogFileHeadT 512字节对齐
    static const uint32_t redoDefaultBlockSize = 512;
    uint32_t fileOffset = blockId * redoDefaultBlockSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

void DestorySafeFileContent(char *filePath, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    ASSERT_EQ(true, file != NULL);

    // safeFile 文件头预留4KB
    static const uint32_t safeFileReserveSize = 4 * BUFF_MAX_LEN;
    uint32_t fileOffset = safeFileReserveSize + offset;
    int ret = fseek(file, fileOffset, SEEK_SET);
    ASSERT_EQ(ret, 0);

    // 破坏文件的内容
    ret = fwrite("kkkkkkkk", 1, 9, file);
    ASSERT_GT(ret, 0);

    // 关闭文件
    ret = fclose(file);
    ASSERT_EQ(ret, 0);
}

int BuildAndDml(int beginIndex, int endIndex)
{
    // 建表
    int ret = CommonDDL();
    RETURN_IFERR(ret);
    // 写入数据
    ret = CommonInsert(beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 更新
    ret = CommonUpdate(beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 删除部分
    ret = CommonDelete(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    // 查询数据量
    ret = CommonCheckInCount(endIndex / 2 - beginIndex, beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 查询更新结果
    ret = CommonCheckUpdRecord(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    return T_OK;
}

int ReCheckDml(int beginIndex, int endIndex)
{
    // 查询数据量
    int ret = CommonCheckInCount(endIndex / 2 - beginIndex, beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 查询结果
    ret = CommonCheckUpdRecord(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonPstCheck(int beginIndex, int endIndex)
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    // 建表跑业务操作
    ret = BuildAndDml(beginIndex, endIndex);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);

    // 重建表，验证数据
    ret = ReCheckDml(beginIndex, endIndex);
    RETURN_IFERR(ret);

    ret = CommonDropTable();
    RETURN_IFERR(ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

bool RedoIsExists(char *dataFilePath)
{
    char temp[512] = {0};
    (void)sprintf(temp, "%s/gmdb_redo_0", dataFilePath);
    return FileIsExist(temp);
}

int InitAndConn()
{
    int ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn()
{
    // 重启
    int ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn2()
{
    // 重启
    int ret;
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int DisConnAndClean()
{
    int ret = CommonDropTable();
    RETURN_IFERR(ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

/* ****** ipv4地址 转换为 uint32 ****** */
uint32_t TransIp(const char *ipStr)
{
    char strIpIndex[4] = {'\0'};
    uint32_t ipInt;
    uint32_t ipAdd = 0;
    int j = 0, a = 3;
    for (uint32_t i = 0; i <= strlen(ipStr); i++) { // 要用到'\0'
        if (ipStr[i] == '\0' || ipStr[i] == '.') {
            ipInt = atoi(strIpIndex);
            if (ipInt < 0 || ipInt > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ipAdd += (ipInt * ((uint32_t)pow(256.0, a)));
            a--;
            memset(strIpIndex, 0, sizeof(strIpIndex));
            j = 0;
            continue;
        }
        strIpIndex[j] = ipStr[i];
        j++;
    }
    return ipAdd;
}

uint32_t TruncateFile(char *fileName, float percentage, char direction)
{
    FILE *fp = fopen(fileName, "rb+");
    if (fp == NULL) {
        printf("Failed to open file %s", fileName);
        return T_FAILED;
    }

    // 获取文件大小
    (void)fseek(fp, 0L, SEEK_END);
    long int fileSize = ftell(fp);
    if (fileSize == 0) {
        return T_FAILED;
    }
    // 计算截断位置
    long int sizeCount;
    char *buffer = NULL;
    long int truncatePos = fileSize * percentage;

    if (direction == 'b') {
        (void)fseek(fp, truncatePos, SEEK_SET);
        sizeCount = fileSize - truncatePos;
        buffer = (char *)malloc(fileSize - truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return T_FAILED;
        }
        (void)fread(buffer, sizeof(char), fileSize - truncatePos, fp);
    } else if (direction == 'a') {
        (void)fseek(fp, 0, SEEK_SET);
        sizeCount = truncatePos;
        buffer = (char *)malloc(truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return T_FAILED;
        }
        (void)fread(buffer, sizeof(char), truncatePos, fp);
    } else {
        printf("Invalid direction %c", direction);
        (void)fclose(fp);
        return T_FAILED;
    }
    (void)fseek(fp, 0, SEEK_SET);
    (void)ftruncate(fileno(fp), 0);
    (void)fwrite(buffer, sizeof(char), sizeCount, fp);
    (void)fclose(fp);
    return T_OK;
}

int CreateShareMem()
{
    g_shmid = shm_open(g_shmName, O_CREAT | O_RDWR, 0666);
    if (g_shmid == -1) {
        perror("shm_open");
        return T_FAILED;
    }
    if (ftruncate(g_shmid, g_aptMemSize) == -1) {
        perror("ftruncate");
        return T_FAILED;
    }
    g_aptAddr = (uint8_t *)mmap(NULL, g_aptMemSize, PROT_READ | PROT_WRITE, MAP_SHARED, g_shmid, 0);
    if (g_aptAddr == MAP_FAILED) {
        perror("mmap");
        return T_FAILED;
    }
    (void)memset(g_aptAddr, 0, g_aptMemSize);

    return T_OK;
}

int GetShareMem()
{
    g_shmid = shm_open(g_shmName, O_RDWR, 0666);
    if (g_shmid == -1) {
        perror("shm_open");
        return T_FAILED;
    }
    g_aptAddr = (uint8_t *)mmap(NULL, g_aptMemSize, PROT_READ | PROT_WRITE, MAP_SHARED, g_shmid, 0);
    if (g_aptAddr == MAP_FAILED) {
        perror("mmap");
        return T_FAILED;
    }

    return T_OK;
}

int DetachShareMem()
{
    if (munmap(g_aptAddr, g_aptMemSize) == -1) {
        perror("munmap");
        return T_FAILED;
    }
    if (close(g_shmid) == -1) {
        perror("close");
        return T_FAILED;
    }
    return T_OK;
}

int CleanShareMem()
{
    int ret = DetachShareMem();
    if (ret == T_FAILED) {
        return ret;
    }
    if (shm_unlink(g_shmName) == -1) {
        perror("shm_unlink");
        return T_FAILED;
    }
    return T_OK;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
static uint32_t g_dbCrcShiftByte = 8;

// 后续改为带秘钥的算法
static const uint32_t CRC32_TABLE[256] = {0x0, 0x77073096, 0xee0e612c, 0x990951ba, 0x76dc419, 0x706af48f, 0xe963a535,
    0x9e6495a3, 0xedb8832, 0x79dcb8a4, 0xe0d5e91e, 0x97d2d988, 0x9b64c2b, 0x7eb17cbd, 0xe7b82d07, 0x90bf1d91,
    0x1db71064, 0x6ab020f2, 0xf3b97148, 0x84be41de, 0x1adad47d, 0x6ddde4eb, 0xf4d4b551, 0x83d385c7, 0x136c9856,
    0x646ba8c0, 0xfd62f97a, 0x8a65c9ec, 0x14015c4f, 0x63066cd9, 0xfa0f3d63, 0x8d080df5, 0x3b6e20c8, 0x4c69105e,
    0xd56041e4, 0xa2677172, 0x3c03e4d1, 0x4b04d447, 0xd20d85fd, 0xa50ab56b, 0x35b5a8fa, 0x42b2986c, 0xdbbbc9d6,
    0xacbcf940, 0x32d86ce3, 0x45df5c75, 0xdcd60dcf, 0xabd13d59, 0x26d930ac, 0x51de003a, 0xc8d75180, 0xbfd06116,
    0x21b4f4b5, 0x56b3c423, 0xcfba9599, 0xb8bda50f, 0x2802b89e, 0x5f058808, 0xc60cd9b2, 0xb10be924, 0x2f6f7c87,
    0x58684c11, 0xc1611dab, 0xb6662d3d, 0x76dc4190, 0x1db7106, 0x98d220bc, 0xefd5102a, 0x71b18589, 0x6b6b51f,
    0x9fbfe4a5, 0xe8b8d433, 0x7807c9a2, 0xf00f934, 0x9609a88e, 0xe10e9818, 0x7f6a0dbb, 0x86d3d2d, 0x91646c97,
    0xe6635c01, 0x6b6b51f4, 0x1c6c6162, 0x856530d8, 0xf262004e, 0x6c0695ed, 0x1b01a57b, 0x8208f4c1, 0xf50fc457,
    0x65b0d9c6, 0x12b7e950, 0x8bbeb8ea, 0xfcb9887c, 0x62dd1ddf, 0x15da2d49, 0x8cd37cf3, 0xfbd44c65, 0x4db26158,
    0x3ab551ce, 0xa3bc0074, 0xd4bb30e2, 0x4adfa541, 0x3dd895d7, 0xa4d1c46d, 0xd3d6f4fb, 0x4369e96a, 0x346ed9fc,
    0xad678846, 0xda60b8d0, 0x44042d73, 0x33031de5, 0xaa0a4c5f, 0xdd0d7cc9, 0x5005713c, 0x270241aa, 0xbe0b1010,
    0xc90c2086, 0x5768b525, 0x206f85b3, 0xb966d409, 0xce61e49f, 0x5edef90e, 0x29d9c998, 0xb0d09822, 0xc7d7a8b4,
    0x59b33d17, 0x2eb40d81, 0xb7bd5c3b, 0xc0ba6cad, 0xedb88320, 0x9abfb3b6, 0x3b6e20c, 0x74b1d29a, 0xead54739,
    0x9dd277af, 0x4db2615, 0x73dc1683, 0xe3630b12, 0x94643b84, 0xd6d6a3e, 0x7a6a5aa8, 0xe40ecf0b, 0x9309ff9d, 0xa00ae27,
    0x7d079eb1, 0xf00f9344, 0x8708a3d2, 0x1e01f268, 0x6906c2fe, 0xf762575d, 0x806567cb, 0x196c3671, 0x6e6b06e7,
    0xfed41b76, 0x89d32be0, 0x10da7a5a, 0x67dd4acc, 0xf9b9df6f, 0x8ebeeff9, 0x17b7be43, 0x60b08ed5, 0xd6d6a3e8,
    0xa1d1937e, 0x38d8c2c4, 0x4fdff252, 0xd1bb67f1, 0xa6bc5767, 0x3fb506dd, 0x48b2364b, 0xd80d2bda, 0xaf0a1b4c,
    0x36034af6, 0x41047a60, 0xdf60efc3, 0xa867df55, 0x316e8eef, 0x4669be79, 0xcb61b38c, 0xbc66831a, 0x256fd2a0,
    0x5268e236, 0xcc0c7795, 0xbb0b4703, 0x220216b9, 0x5505262f, 0xc5ba3bbe, 0xb2bd0b28, 0x2bb45a92, 0x5cb36a04,
    0xc2d7ffa7, 0xb5d0cf31, 0x2cd99e8b, 0x5bdeae1d, 0x9b64c2b0, 0xec63f226, 0x756aa39c, 0x26d930a, 0x9c0906a9,
    0xeb0e363f, 0x72076785, 0x5005713, 0x95bf4a82, 0xe2b87a14, 0x7bb12bae, 0xcb61b38, 0x92d28e9b, 0xe5d5be0d,
    0x7cdcefb7, 0xbdbdf21, 0x86d3d2d4, 0xf1d4e242, 0x68ddb3f8, 0x1fda836e, 0x81be16cd, 0xf6b9265b, 0x6fb077e1,
    0x18b74777, 0x88085ae6, 0xff0f6a70, 0x66063bca, 0x11010b5c, 0x8f659eff, 0xf862ae69, 0x616bffd3, 0x166ccf45,
    0xa00ae278, 0xd70dd2ee, 0x4e048354, 0x3903b3c2, 0xa7672661, 0xd06016f7, 0x4969474d, 0x3e6e77db, 0xaed16a4a,
    0xd9d65adc, 0x40df0b66, 0x37d83bf0, 0xa9bcae53, 0xdebb9ec5, 0x47b2cf7f, 0x30b5ffe9, 0xbdbdf21c, 0xcabac28a,
    0x53b39330, 0x24b4a3a6, 0xbad03605, 0xcdd70693, 0x54de5729, 0x23d967bf, 0xb3667a2e, 0xc4614ab8, 0x5d681b02,
    0x2a6f2b94, 0xb40bbe37, 0xc30c8ea1, 0x5a05df1b, 0x2d02ef8d};

uint32_t DBTableCRC32(char *data, uint32_t length)
{
    uint32_t crc = 0xffffffff;
    uint8_t *byteArray = (uint8_t *)data;
    for (uint32_t i = 0; i < length; i++) {
        crc = CRC32_TABLE[(crc ^ byteArray[i]) & 0xFF] ^ (crc >> g_dbCrcShiftByte);
    }
    return ~crc;
}

int32_t GmdbDigest32Generate(
    uint8_t *data, uint32_t dataLen, uint8_t *digest, uint32_t digestLen)
{
    // 摘要长度为32字节
    uint32_t digestLenExpect = 32;
    if (digestLen != digestLenExpect) {
        return -1;
    }
    uint32_t genDigest = DBTableCRC32((char *)data, dataLen);
    uint32_t crcByteNum = sizeof(uint32_t) / sizeof(uint8_t);
    for (uint32_t i = 0; crcByteNum * i < digestLen; i++) {
        *(uint32_t *)(digest + crcByteNum * i) = genDigest;
    }
    return 0;
}

int GetRedoBufferRsm(uint8_t **addr, uint32_t *size, bool *forRecovery)
{
    *addr = g_aptAddr;
    *size = g_aptMemSize;
    *forRecovery = true;
    return T_OK;
}

// 破坏共享内存
int GetRedoBufferRsmErr(uint8_t **addr, uint32_t *size, bool *forRecovery)
{
    *addr = g_aptAddr;
    *size = g_aptMemSize;
    *forRecovery = true;
    for (uint32_t i = 0; i < g_aptMemSize; i++) {
        *(*addr + i) = i;
    }
    return T_OK;
}

int PstCompress(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uint32_t dstLen = 0;
    uint32_t i = 0;
    while (i < srcSize) {
        uint8_t count = 1;
        while (i + count < srcSize && src[i] == src[i + count] && count < 255) {
            count++;
        }
        if (count > 1 || src[i] == 0xFF) {
            // 如果有重复的字节或者是0xFF，则需要进行压缩
            dest[dstLen++] = 0xFF;
            dest[dstLen++] = count;
            dest[dstLen++] = src[i];
            i += count;
        } else {
            // 否则直接复制
            dest[dstLen++] = src[i++];
        }
    }
    *destSize = dstLen;
    return T_OK;
}

int PstUnCompress(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uint32_t dstLen = 0;
    uint32_t i = 0;
    while (i < srcSize) {
        if (src[i] == 0xFF) {
            // 如果是0xFF，则需要进行解压
            uint8_t count = src[i + 1];
            uint8_t value = src[i + 2];
            for (int j = 0; j < count; j++) {
                dest[dstLen++] = value;
            }
            i += 3;
        } else {
            // 否则直接复制
            dest[dstLen++] = src[i++];
        }
    }
    *destSize = dstLen;
    return T_OK;
}
#endif

typedef enum AdaptFunc {
    NONE,
    RSM,
    COMPRESS,
    RSM_AND_COMPRESS,
    TAMPER_PROOF_CHEACK,
    TAMPER_PROOF_CHEACK_AND_COMPRESS,
    NOT_REG_GEN_DIG_FUNC,
} AdaptFuncE;

int AdapAndStartServer(AdaptFuncE func)
{
    int ret = 0;
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    if (g_isReadConfig == false) {
        getSysConfig();
    }

    system("ipcrm -a");
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    GmsAdptFuncsT adpt = { 0 };
    if (func == RSM || func == RSM_AND_COMPRESS) {
        adpt.getReservedMemFunc = (GmsGetRedoBufferReservedSegmentFuncT)GetRedoBufferRsm;
    }
    if (func == COMPRESS || func == RSM_AND_COMPRESS) {
        adpt.persistCompressFunc = PstCompress;
        adpt.persistDecompressFunc = PstUnCompress;
    }
    if (func == TAMPER_PROOF_CHEACK) {
        adpt.digest32GenerateFunc = GmdbDigest32Generate;
    }
    if (func == TAMPER_PROOF_CHEACK_AND_COMPRESS) {
        adpt.digest32GenerateFunc = GmdbDigest32Generate;
        adpt.persistCompressFunc = PstCompress;
        adpt.persistDecompressFunc = PstUnCompress;
    }
    if (func == NOT_REG_GEN_DIG_FUNC) {
    }

    ret = GmsRegAdaptFuncs(&adpt);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmsRegAdaptFuncs failed, ret = %d.\n", ret);
    }

    char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
    ret = GmsServerMain(3, cmdString);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testEnvInit] GmsServerMain failed, ret = %d.\n", ret);
        return ret;
    }
#endif
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    g_runStat = RUN_STAT_SUCC;
    return 0;
}

int AdapAndStartServerErr(AdaptFuncE func)
{
    int ret = 0;
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    if (g_isReadConfig == false) {
        getSysConfig();
    }

    system("ipcrm -a");
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    GmsAdptFuncsT adpt = { 0 };
    if (func == RSM || func == RSM_AND_COMPRESS) {
        adpt.getReservedMemFunc = (GmsGetRedoBufferReservedSegmentFuncT)GetRedoBufferRsmErr;
    }
    if (func == COMPRESS || func == RSM_AND_COMPRESS) {
        adpt.persistCompressFunc = PstCompress;
        adpt.persistDecompressFunc = PstUnCompress;
    }
    ret = GmsRegAdaptFuncs(&adpt);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmsRegAdaptFuncs failed, ret = %d.\n", ret);
    }

    char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
    ret = GmsServerMain(3, cmdString);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testEnvInit] GmsServerMain failed, ret = %d.\n", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
#endif
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    g_runStat = RUN_STAT_SUCC;
    return 0;
}

// 新增配置项不存在gmserver.ini里
int Addini(char *para, char *value)
{
    char gCommand[BUFF_MAX_LEN];
    memset(gCommand, 0, sizeof(gCommand));
    (void)snprintf(gCommand, BUFF_MAX_LEN, "grep -rn %s %s |wc -l", para, g_sysGMDBCfg);
    int ret = executeCommand(gCommand, "0");
    if (ret == GMERR_OK) {
        memset(gCommand, 0, sizeof(gCommand));
        // echo赋值必须是初始值
        (void)snprintf(gCommand, BUFF_MAX_LEN, "echo '%s = %s' >> %s", para, value, g_sysGMDBCfg);
        system(gCommand);
    }
    return T_OK;
}

int MultiZoneBk()
{
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    char tempDir[BUFF_MAX_LEN] = {0};
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)sprintf(g_newDbFilePath, "%s/new_gmdb", pwdDir);
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    RETURN_IFERR(ret);
    ret = ChangeGmserverCfg((char *)"multizonePersistNum", (char *)"2");
    RETURN_IFERR(ret);
    (void)sprintf(g_dbFilePath1, "%s1", g_dbFilePath);
    (void)sprintf(g_dbFilePath2, "%s2", g_dbFilePath);
    
    (void)Rmdir(g_dbFilePath1);
    (void)Rmdir(g_dbFilePath2);
    ret = mkdir(g_dbFilePath1, S_IRUSR | S_IWUSR);
    RETURN_IFERR(ret);
    ret = mkdir(g_dbFilePath2, S_IRUSR | S_IWUSR);
    RETURN_IFERR(ret);
    char temp[2048] = {0};
    (void)sprintf(temp, "%s,%s", g_dbFilePath1, g_dbFilePath2);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", temp);
    RETURN_IFERR(ret);
    return T_OK;
}

#ifdef __cplusplus
}
#endif

#endif /* INCRE_PST_COMMON_H */

