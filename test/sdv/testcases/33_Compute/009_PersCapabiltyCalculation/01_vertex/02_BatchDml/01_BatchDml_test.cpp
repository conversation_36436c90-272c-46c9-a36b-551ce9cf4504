/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
 //(持久化能力上车计算(03_DML\015_BatchDml\BatchDml_test.cpp)
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "vertex_tools.h"

GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL;
char *g_schema = NULL, *g_schema_2 = NULL;

GmcConnT *conn;
GmcStmtT *stmt;
GmcConnT *connectionAsync = NULL;
char label_name1[] = "OP_T0";
char lalable_name_PK1[] = "OP_PK";
char label_name2[] = "DST_T0";
char lalable_name_PK2[] = "DST_PK";
char label_name3[] = "edgelabel_testEdgeBatchOp";
char g_label_config_test[] = "{\"max_record_num\":10000}";
#define MAX_VERTEX_NUM 10000
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

char *test_schema1 = NULL;
char *test_schema2 = NULL;
char *test_schema3 = NULL;
const char *edgeLabelName = "edgelabel_testEdgeBatchOp";
AsyncUserDataT asyncData;


class BatchDml_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BatchDml_test::SetUpTestCase()
{}

void BatchDml_test::TearDownTestCase()
{}


void BatchDml_test::SetUp()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, label_name1);

    readJanssonFile("schema_file/BatchDml_op.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);


    AW_CHECK_LOG_BEGIN();
}

void BatchDml_test::TearDown()
{
    AW_CHECK_LOG_END();
    free(test_schema1);

    GmcDropVertexLabel(stmt, label_name1);
 
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    ASSERT_EQ(0, ret);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}


/* ****************************************************************************
 Description  : 同步批量插入100个图顶点、读数据
 Input        : None
 Output       : None
 Return Value : 批量不加数据的验证
 Notes        :DML_015_001
 History      :
 Author       :
 Modification :
**************************************************************************** */
TEST_F(BatchDml_test, Compute_009_001_01_013)
{
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " 批量插入顶点");
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    AW_FUN_Log(LOG_STEP, "  批量不加数据");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "  批量加数据");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt, &g_conn_async, &g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  批量插入顶点不加数据");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "  批量加数据");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < end_num + 100; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num + 100; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数200条");
    int32_t record1 = 200;
    ret = readTable(stmt, label_name1);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);
}


/* ****************************************************************************
 Description  : 同步批量插入图顶点操作（请求总数>=1024）
 Input        : None
 Output       : None
 Return Value :DML_015_013
 Notes        :
 History      :
 Author       :
 Modification :
**************************************************************************** */
TEST_F(BatchDml_test, Compute_009_001_01_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 1024;

    AW_FUN_Log(LOG_STEP, "  第一次插入顶点,请求个数等于1024");
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, "  第二次插入顶点,请求个数等于1024");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    AW_FUN_Log(LOG_STEP, "  删除顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, "  删除顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  插入顶点，请求个数等于1025");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num + 1; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        if (i == end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt, &g_conn_async, &g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  第一次插入顶点,请求个数等于1024");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  插入顶点，请求个数等于1025");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 2 * end_num; i < 3 * end_num + 1; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        if (i == 3 * end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < 3 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数3072条");
    int32_t record1 = 3 * end_num;
    ret = readTable(stmt, label_name1);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);
}


/* ****************************************************************************
 Description  : 同步批量更新图顶点操作（请求总数>=1024）
 Input        : None
 Output       : None
 Return Value :DML_015_014
 Notes        :
 History      :
 Author       :
 Modification :
**************************************************************************** */
TEST_F(BatchDml_test, Compute_009_001_01_015)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int start_num = 0;
    int end_num = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  插入顶点");
    for (int i = start_num; i < 2 * end_num; i++) {

        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, totalNum);
        ASSERT_EQ(1, successNum);
    }

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    AW_FUN_Log(LOG_STEP, "  更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);

        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
    }

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  更新顶点，请求个数等于1025");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num + 1; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (i == end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt, &g_conn_async, &g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  更新顶点，请求个数等于1024");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1 * end_num; i < 2 * end_num + 1; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 3 * i, 1, (char *)"string3");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (i == 2 * end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1 * end_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 3 * i, 1, (char *)"string3");
    }

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "  更新顶点，请求个数等于1025");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1 * end_num; i < 2 * end_num + 1; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 3 * i, 1, (char *)"string3");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (i == 2 * end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1 * end_num; i < 1 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 3 * i, 1, (char *)"string3");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数2048条");
    int32_t record1 = 2 * end_num;
    ret = readTable(stmt, label_name1);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);
}


/* ****************************************************************************
 Description  : 同步批量删除图顶点操作（请求总数>=1024）
 Input        : None
 Output       : None
 Return Value :DML_015_015
 Notes        :
 History      :
 Author       :
 Modification :
**************************************************************************** */
TEST_F(BatchDml_test, Compute_009_001_01_016)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 1024;

    AW_FUN_Log(LOG_STEP, "  插入顶点");
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, "  批量删除顶点1025");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num + 1; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (i == end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数0条");
    int32_t record1 = 0;
    ret = readTable(stmt, label_name1);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt, &g_conn_async, &g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  插入顶点");
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < 2 * end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
        totalNum = 0;
        successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, totalNum);
        ASSERT_EQ(1, successNum);
    }
    AW_FUN_Log(LOG_STEP, "  批量删除顶点1025");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num + 1; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (i == end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数1024条");
    int32_t record2 = end_num;
    ret = readTable(stmt, label_name1);
    AW_MACRO_EXPECT_EQ_INT(record2, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);
}


// 插入
void *client_thread_045_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;
    int32_t *insert = (int32_t *)args;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    printf(" insert  sss :%d %d \n", insert[0], insert[1]);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = insert[0]; i < insert[1]; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insert[1] - insert[0], totalNum);
    EXPECT_EQ(insert[1] - insert[0], successNum);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "-----GmcBatchExecute1=%d------\n");
    return NULL;
}

//删除
void *client_thread_045_02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;
    int32_t *deletee = (int32_t *)args;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" deletee  sss :%d %d \n", deletee[0], deletee[1]);
    for (int i = deletee[0]; i < deletee[1]; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(deletee[1] - deletee[0], totalNum);
    EXPECT_EQ(deletee[1] - deletee[0], successNum);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "-----GmcBatchExecute2=%d------\n");
    return NULL;
}



// 更新顶点
void *client_thread_046_01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;
    int32_t *update = (int32_t *)args;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    // 更新顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t repeatcount = 10;
    while (repeatcount > 0) {
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch;
        GmcBatchRetT batchRet;
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = update[0]; i < update[1]; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
        }

        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            repeatcount--;
        } else {
            AW_FUN_Log(LOG_STEP, "-----GmcBatchExecute3=%d------\n");
            break;
        }
    }
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(update[1] - update[0], totalNum);
    /*校验不准确  EXPECT_EQ(end_num, successNum); */

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}


/* ****************************************************************************
 Description  : 并发顶点dml操作，并发同步批量插入和批量删除数据
 Input        : None
 Output       : None
 Return Value :DML_015_045
 Notes        :
 History      :
 Author       :
 Modification :
**************************************************************************** */
TEST_F(BatchDml_test, Compute_009_001_01_017)
{
    int ret = 0;

    int start_num = 0;
    int end_num = 100;
    pthread_t client_thr_01, client_thr_02, client_thr_03;
    void *thr_ret[30] = {0};

    ret = GmcCreateVertexLabel(stmt, test_schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t insert[2] = {0, 200};
    int32_t deletee[2] = {0, 100};
    int32_t update[2] = {100, 200};
    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_045_01, &insert);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_045_02, &insert);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_03, NULL, client_thread_046_01, &update);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
        pthread_join(client_thr_02, &thr_ret[3]);
    }

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        } else {
            ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
            EXPECT_EQ(GMERR_OK, ret);
            query_VertexProperty(stmt, i, 0, (char *)"string");
        }
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt, &g_conn_async, &g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t insert1[2] = {200, 400};
    int32_t deletee1[2] = {0, 100};
    int32_t update1[2] = {200, 300};
    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&client_thr_01, NULL, client_thread_045_01, &insert1);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, client_thread_045_02, &deletee1);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_03, NULL, client_thread_046_01, &update1);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
        pthread_join(client_thr_02, &thr_ret[3]);
    }

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        } else {
            ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
            EXPECT_EQ(GMERR_OK, ret);
            query_VertexProperty(stmt, i, 0, (char *)"string");
        }
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);
}


/* ****************************************************************************
 Description  : 异步批量插入图顶点操作（请求总数>=1024）
 Input        : None
 Output       : None
 Return Value :DML_015_057
 Notes        :
 History      :
 Author       :
 Modification :
**************************************************************************** */
TEST_F(BatchDml_test, Compute_009_001_01_018)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int32_t ret = 0;

    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, NULL, create_vertex_label_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 1024;

    AW_FUN_Log(LOG_STEP, "  第一次插入顶点,请求个数等于1024");
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(g_stmt_async, i);
        Set_VertexProperty(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    AW_FUN_Log(LOG_STEP, "  第二次插入顶点,请求个数等于1024");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        Set_VertexProperty_PK(g_stmt_async, i);
        Set_VertexProperty(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    AW_FUN_Log(LOG_STEP, " 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    AW_FUN_Log(LOG_STEP, "  删除顶点1");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    AW_FUN_Log(LOG_STEP, "  删除顶点2");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, lalable_name_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    AW_FUN_Log(LOG_STEP, "  插入顶点，请求个数等于1025");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < end_num + 1; i++) {
        Set_VertexProperty_PK(g_stmt_async, i);
        Set_VertexProperty(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_async);
        if (i == end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt, &g_conn_async, &g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  插入顶点，请求个数等于1024");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < 2 * end_num; i++) {
        Set_VertexProperty_PK(g_stmt_async, i);
        Set_VertexProperty(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    AW_FUN_Log(LOG_STEP, "  插入顶点，请求个数等于1025");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, label_name1, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 2 * end_num; i < 3 * end_num + 1; i++) {
        Set_VertexProperty_PK(g_stmt_async, i);
        Set_VertexProperty(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_async);
        if (i == 3 * end_num) {
            ASSERT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, asyncData.totalNum);
    ASSERT_EQ(end_num, asyncData.succNum);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数3072条");
    int32_t record1 = 3 * end_num;
    ret = readTable(stmt, label_name1);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name1);
    ASSERT_EQ(GMERR_OK, ret);
}

