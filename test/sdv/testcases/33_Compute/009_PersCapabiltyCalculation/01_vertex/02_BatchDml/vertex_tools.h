/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef VERTEX_TOOLS_H
#define VERTEX_TOOLS_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"


void Set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}


void Set_VertexProperty(GmcStmtT *stmt, int i, bool boolValue, char *f14Value)
{
    int ret = 0;

    uint64_t f1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3Value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7Value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = boolValue;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11Value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);
}


void query_VertexProperty(GmcStmtT *stmt, int index, bool boolValue, char *f14Value)
{
    int ret = 0;
    uint64_t f1Value = 1 * index;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2Value = 2 * index;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &f2Value);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3Value = 3 * index;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &f3Value);
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4Value = 4 * index;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4Value);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5Value = 5 * index;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value);
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6Value = 6 * index;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &f6Value);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7Value = 7 * index;
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &f7Value);
    EXPECT_EQ(GMERR_OK, ret);

    bool f8Value = boolValue;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value);
    EXPECT_EQ(GMERR_OK, ret);

    float f9Value = 9 * index;
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &f9Value);
    EXPECT_EQ(GMERR_OK, ret);

    double f10Value = 10 * index;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10Value);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11Value = 11 * index;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &f11Value);
    EXPECT_EQ(GMERR_OK, ret);

    char f12Value = 12 * index;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &f12Value);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13Value = 13 * index;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &f13Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, f14Value);
    EXPECT_EQ(GMERR_OK, ret);
}


int RestartAndConn(GmcConnT **conn, GmcStmtT **stmt, GmcConnT **conn_async, GmcStmtT **stmt_async)
{
    // 持久化重启
    int ret = testGmcDisconnect(*conn, *stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(*conn_async, *stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/stop.sh -f");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "重启服务");
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "创建epoll");
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "Connect");
    ret = testGmcConnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(conn_async, stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return T_OK;
}

// 全表扫描查询
int readTable(GmcStmtT *stmt, const char *tableOut)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "全表扫描查询,校验表%s", tableOut);

    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while ((!isFinish) && (ret == GMERR_OK)) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    return cnt;
}

#endif /* VERTEX_TOOLS_H */
