[{"type": "record", "name": "clusterTestLabel", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "uint8", "nullable": false}, {"name": "F7", "type": "time", "nullable": true}, {"name": "F8", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F9", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F10", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F11", "type": "uint32", "nullable": false}, {"name": "F12", "type": "uint32", "nullable": false}, {"name": "F13", "type": "uint8: 4", "nullable": true}, {"name": "F14", "type": "char", "nullable": true}, {"name": "F15", "type": "uchar", "nullable": true}, {"name": "R1", "type": "record", "fields": [{"name": "P1", "type": "int64", "nullable": true}, {"name": "P2", "type": "uint64", "nullable": true}]}], "keys": [{"node": "clusterTestLabel", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]