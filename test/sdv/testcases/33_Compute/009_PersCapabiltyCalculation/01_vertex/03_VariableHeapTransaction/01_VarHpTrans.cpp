/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
 // 持久化能力上车计算--事务类型(悲观读已提交) ( 03_DML\019_VariableHeapTransaction\VarHpTrans.cpp)
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "vertex_tools.h"
#define MAX_VERTEX_NUM 10000

GmcConnT *conn;
GmcStmtT *stmt;
char label_name[] = "OP_T0";
char label_name2[] = "T39_all_type";
char lalable_name_PK[] = "OP_PK";
char lalable_name_PK2[] = "T39_K0";
char g_label_config_test[] = "{\"max_record_num\":10000, \"isFastReadUncommitted\":0}";
char *test_schema = NULL;
char *test_schema2 = NULL;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];


class VariableHeapTransaction_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void VariableHeapTransaction_test::SetUpTestCase()
{}

void VariableHeapTransaction_test::TearDownTestCase()
{}

void VariableHeapTransaction_test::SetUp()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    
    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");
    
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, label_name);
    readJanssonFile("schema_file/VariableHeapTransaction_op.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    ret = GmcCreateVertexLabel(stmt, test_schema, g_label_config_test);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void VariableHeapTransaction_test::TearDown()
{
    int ret = 0;
    AW_CHECK_LOG_END();
    free(test_schema);
    ret = GmcDropVertexLabel(stmt, label_name);
    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行普通插入数据，commit
 Input        : None
 Output       : None
 Return Value : 提交成功，数据变更正确
 Notes        :DML_019_001
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_019)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    //插入顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }
    ret = GmcFlushData(stmt, NULL, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //插入顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = end_num; i < end_num + 100; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < end_num + 100; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数200条");
    int32_t record1 = 200;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行普通更新数据，commit
 Input        : None
 Output       : None
 Return Value : 提交成功，数据变更正确
 Notes        :DML_019_002
 History      :
 Author       : 
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_020)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    //插入顶点
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启一个事务(cs模式)
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后 更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 3 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 3 * i, 1, (char *)"string2");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = 100;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行普通删除数据，commit
 Input        : None
 Output       : None
 Return Value : 提交成功，数据变更正确
 Notes        :DML_019_004
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_021)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " *****插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, " *****删除顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启一个事务(cs模式)
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，删除顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < 50; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (i < 50) {
            ASSERT_EQ(isFinish, true);
        }else{
            ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
            ASSERT_EQ(GMERR_OK, ret);
            query_VertexProperty(stmt, i, 0, (char *)"string");
        }
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数50条");
    int32_t record1 = 50;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行普通插入数据，RollBack
 Input        : None
 Output       : None
 Return Value :DML_019_005
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_022)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " *****插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, " ***** 回滚事务");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, " *****重启后， 回滚事务");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后， 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数0条");
    int32_t record1 = 0;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行普通更新数据，RollBack
 Input        : None
 Output       : None
 Return Value :DML_019_006
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_023)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " *****插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, " *****读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, " ***** 回滚事务");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，开启一个事务(cs模式)");
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, " *****重启后， 回滚事务");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = 100;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行批量插入数据，commit
 Input        : None
 Output       : None
 Return Value :DML_019_009
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_024)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;
    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " ***** 提交事务");
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，开启一个事务(cs模式)");
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
 
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < end_num + 100; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum1 = 0;
    unsigned int successNum1 = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum1, &successNum1);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum1);
    ASSERT_EQ(end_num, successNum1);

    AW_FUN_Log(LOG_STEP, " *****重启后，提交事务");
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " *****重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num + 100; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数200条");
    int32_t record1 = 200;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行批量更新数据，commit
 Input        : None
 Output       : None
 Return Value :DML_019_010
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_025)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " ***** 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " ***** 批量更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    AW_FUN_Log(LOG_STEP, " ***** 提交事务");
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
    }
    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，开启一个事务(cs模式)");
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " *****重启后，批量更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 3 * i, 0, (char *)"string3");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    AW_FUN_Log(LOG_STEP, " *****重启后，提交事务");
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, " *****重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, 3 * i, 0, (char *)"string3");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = end_num;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行批量删除数据，commit
 Input        : None
 Output       : None
 Return Value :DML_019_012
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_026)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " ***** 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****批量删除顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后， 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " *****重启后，开启一个事务(cs模式)");
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后，批量删除顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " *****/重启后，读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数0条");
    int32_t record1 = 0;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行批量插入数据，RollBack
 Input        : None
 Output       : None
 Return Value :DML_019_013
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_027)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " ***** 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    AW_FUN_Log(LOG_STEP, " ***** 事务回滚");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " *****重启后， 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    AW_FUN_Log(LOG_STEP, " ***** 事务回滚");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
    }
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数0条");
    int32_t record1 = 0;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 开启事务， 对顶点进行批量更新数据，RollBack
 Input        : None
 Output       : None
 Return Value :DML_019_014
 Notes        :
 History      :
 Author       :
 Modification :
*****************************************************************************/
TEST_F(VariableHeapTransaction_test, Compute_009_001_01_028)
{
    int32_t ret = 0;

    int start_num = 0;
    int end_num = 100;

    AW_FUN_Log(LOG_STEP, " ***** 批量插入顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        Set_VertexProperty_PK(stmt, i);
        Set_VertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " *****批量更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    AW_FUN_Log(LOG_STEP, " ***** 事务回滚");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 开启一个事务(cs模式)");
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, " *****批量更新顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        Set_VertexProperty(stmt, 3 * i, 0, (char *)"string3");
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);
    AW_FUN_Log(LOG_STEP, " ***** 事务回滚");
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " ***** 读取顶点");
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, lalable_name_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        query_VertexProperty(stmt, i, 0, (char *)"string");
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = 100;
    ret = readTable(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

