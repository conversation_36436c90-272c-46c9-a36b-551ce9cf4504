/*****************************************************************************
 Description  : 迭代二 支持Lpm6索引 测试用例
 Notes        : //(持久化能力上车计算(03_DML\049_Lpm6Index\Lpm6Index_test.cpp)
 History      :
 Author       :  
 Modification :  
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_configJson[128] = "{\"max_record_count\" : 999999}";
int res = 0;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
int g_data_num = 100;

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

// // 更新的字段值
uint8_t wr_fixed_update[16] = {
    0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
 

class Lpm6Index_test : public testing::Test {
public:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {};
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void Lpm6Index_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");
    res = testEnvInit();
    EXPECT_EQ(GMERR_OK, res);

    conn = NULL;
    stmt = NULL;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    res = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(1);
}

void Lpm6Index_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    res = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
    // 断开同步连接
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data);

    GmcDetachAllShmSeg();
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}


/*****************************************************************************
 Description  : 041 不支持按照lpm6索引更新，GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
 Notes        :DML_049_LPM6_041
 History      :
 Author       : 
 Modification : 
*****************************************************************************/
TEST_F(Lpm6Index_test, Compute_009_001_01_012) 
{
    // create vertexLabel
    char *schema_json = NULL;

    readJanssonFile("./schema_file/Lpm6Index_CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    AW_FUN_Log(LOG_STEP, "  写入数据,总共写入128条数据；");
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    ret = test_insert_vertex_lpm6(stmt, g_labelName, 1, g_operNums);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  主键读；");
    int isPrint = 0;
    AW_FUN_Log(LOG_STEP, "\n========== insert vertex, pk read, expect: %d ===============\n", g_operNums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, g_operNums, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " lpm6_key 扫描；");
    for (int i = 1; i < g_operNums; i++) {
        uint8_t mask_scan = 128;
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_wrFixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_scan, sizeof(mask_scan));
        EXPECT_EQ(GMERR_OK, ret);

        // qry vertex
        ret = GmcSetIndexKeyName(stmt, "lpm6_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (unsigned int cnt = 0;; ++cnt) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            uint8_t rd_dest_ip_addr[16];
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(g_wrFixed[i], rd_dest_ip_addr[i]);
            }
            // check masklen
            uint8_t rd_mask_len;
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            EXPECT_EQ(rd_mask_len, 128);
        }
    }

    AW_FUN_Log(LOG_STEP, "不支持按照lpm6索引更新；");  // GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_wrFixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &g_operNums, sizeof(g_operNums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, sizeof(wr_fixed_update));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);


    AW_FUN_Log(LOG_STEP, "不支持按照lpm6索引删除；"); // GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_wrFixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &g_operNums, sizeof(g_operNums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 删除失败后，扫描，数据没有减少
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(128, cnt);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  主键读；");
    isPrint = 0;
    AW_FUN_Log(LOG_STEP, "\n========== insert vertex, pk read, expect: %d ===============\n", g_operNums);
    ret = test_PK_read_lpm6(
        conn, g_labelName, "primary_key", 1, g_operNums + 100, GMC_DATATYPE_UINT8, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " lpm6_key 扫描；");
    for (int i = 1; i < g_operNums + 100; i++) {
        uint8_t mask_scan = 128;
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_wrFixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_scan, sizeof(mask_scan));
        EXPECT_EQ(GMERR_OK, ret);

        // qry vertex
        ret = GmcSetIndexKeyName(stmt, "lpm6_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        for (unsigned int cnt = 0;; ++cnt) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);  //
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            uint8_t rd_dest_ip_addr[16];
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", rd_dest_ip_addr, 16, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            for (int i = 0; i < 16; i++) {
                EXPECT_EQ(g_wrFixed[i], rd_dest_ip_addr[i]);
            }
            // check masklen
            uint8_t rd_mask_len;
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(0, isNull);
            EXPECT_EQ(rd_mask_len, 128);
        }
    }

    AW_FUN_Log(LOG_STEP, "不支持按照lpm6索引更新；");  // GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_wrFixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &g_operNums, sizeof(g_operNums));
    EXPECT_EQ(GMERR_OK, ret);

    // lpm6 index
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed_update, sizeof(wr_fixed_update));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);


    AW_FUN_Log(LOG_STEP, "不支持按照lpm6索引删除；"); // GmcUpdateVertexByIndexKey接口传入lpm6, 预期报错；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &g_wrUnit32, sizeof(g_wrUnit32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_wrFixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &g_operNums, sizeof(g_operNums));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // 删除失败后，扫描，数据没有减少
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(128, cnt);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数128条");
    int32_t record1 = 128;
    ret = readTable(stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

