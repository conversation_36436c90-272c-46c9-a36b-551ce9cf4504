#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include "gtest/gtest.h"
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <stdio.h>
#include <malloc.h>
#include <math.h>

using namespace std;

#define THR_NUM 2
#define MAX_NAME_LENGTH 128

#define LABELNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
GmcConnT *g_conn;
GmcStmtT *g_stmt;
char *schema_json = NULL;
int32_t ret;
int res = 0;
int conn_id = 0;
bool isNull;
unsigned int valueSize;
int total_num = 0;

unsigned int totalNum = 0;
unsigned int successNum = 0;
unsigned int len;
int affectRows;
int g_threadId = 0;
// test datatype label: write vertexLabel
char wr_int8 = 1;
unsigned char wr_uint8 = '1';
short wr_int16 = -1111;
unsigned short wr_uint16 = 1111;
int int32_tmp = -111111;
unsigned int uint32_tmp = 111111;
unsigned long long wr_uint64 = 11111111;
long long int64_tmp = -11111111;
int wr_int32 = -111111;
long long wr_long = -11111111;
float float_tmp = 1111.111;
double double_tmp = 1111.111;
bool bool_tmp = true;
char bytes_tmp[6] = "14";
char string_tmp[64] = "*************";
char fixed_tmp[10] = "16";
long long time_tmp = 369852147;
char char_tmp = -1;
unsigned char uchar_tmp = 11;
bool isFinish;
char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward";

void *vertexLabel1 = NULL, *vertexLabel2 = NULL;
void *vertexLabel = NULL;
void *g_vertexLabel = NULL;

#define NONE "\033[0m"
#define BLACK "\033[0;30m"
#define L_BLACK "\033[1;30m"
#define RED_S "\033[0;31m"
#define L_RED "\033[1;31m"
#define L_GREEN "\033[1;32m"
#define BROWN "\033[0;33m"
#define YELLOW_S "\033[1;33m"
#define L_BLUE "\033[1;34m"
#define PURPLE "\033[0;35m"
#define L_PURPLE "\033[1;35m"
#define CYAN "\033[0;36m"
#define L_CYAN "\033[1;36m"
#define GRAY "\033[0;37m"
#define WHITE "\033[1;37m"

#define TEST_INFO_1(testItem, fieldName, fieldvalue, threadId, isprint)                              \
    do {                                                                                             \
        if (isprint == 1) {                                                                          \
            printf("[INFO][Thread_%d][%7s][%15s] %d \n", threadId, testItem, fieldName, fieldvalue); \
        }                                                                                            \
        if () {                                                                                      \
        }                                                                                            \
    } while (0)

#define TEST_INFO(testItem, fieldName, fieldvalue, threadId, isprint, isUniquleHash)                             \
    do {                                                                                                         \
        if (isprint == 1) {                                                                                      \
            if ((isUniquleHash) == 0)                                                                            \
                printf("[INFO][Thread_%d][%7s][" YELLOW_S "%17s" NONE "] %lu \n", threadId, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else                                                                                                 \
                printf("[INFO][Thread_%d][%7s][%17s] %lu \n", threadId, testItem, fieldName, fieldvalue);        \
        }                                                                                            \
    } while (0)

#define TEST_INFO_STR(testItem, fieldName, fieldvalue, threadId, isprint, isUniquleHash)                        \
    do {                                                                                                        \
        if (isprint == 1) {                                                                                     \
            if ((isUniquleHash) == 1)                                                                           \
                printf("[INFO][Thread_%d][%7s][" L_PURPLE "%17s" NONE "] %s \n", threadId, testItem, fieldName, \
                    fieldvalue);                                                                                \
            else                                                                                                \
                printf("[INFO][Thread_%d][%7s][%17s] %s \n", threadId, testItem, fieldName, fieldvalue);        \
        }                                                                                            \
    } while (0)

#define TEST_SCAN_DEL_RES(expect_status, ret, loop)                                              \
    do {                                                                                         \
        if ((ret) != (expect_status)) {                                                          \
            fprintf(stderr,                                                                      \
                "[" CYAN "Test" NONE "][" RED_S "Error" NONE                                     \
                "][oper_num: %d][File: %s:%d Func: %s] expect %lu, real %lu\n",                  \
                loop, __FILE__, __LINE__, __func__, (uint64_t)(expect_status), (uint64_t)(ret)); \
            break;                                                                               \
        }                                                                                        \
    } while (0)

#define TEST_ASSERT_EQ(expect_status, ret)                                                                     \
    do {                                                                                                       \
        if ((ret) != (expect_status)) {                                                                        \
            fprintf(stderr,                                                                                    \
                "[" CYAN "Test" NONE "][" RED_S "Error" NONE "][File: %s:%d Func: %s] expect %lu, real %lu\n", \
                __FILE__, __LINE__, __func__, (uint64_t)(expect_status), (uint64_t)(ret));                     \
            continue;                                                                                          \
        }                                                                                                      \
    } while (0)

    int32_t queryFieldValueAndCompare(
        GmcStmtT *stmt, const char *fieldName, void *readValueOut, unsigned int *getValueSizeOut)
    {
        ret = GmcGetVertexPropertySizeByName(stmt, fieldName, getValueSizeOut);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetVertexPropertyByName(stmt, fieldName, readValueOut, *getValueSizeOut, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }

/******* ipv4地址 转换为 uint32 *******/
unsigned int TransIp(const char *ipStr)
{
    // 我当前的IP是: *************** (增加一个实例来判断)
    char *ipstr = NULL;
    char strIpIndex[4] = {'\0'};
    unsigned int ipInt, ipAdd = 0, ipIntIndex[4], ipTempNumbr = 24;
    int j = 0, a = 3;
    for (unsigned int i = 0; i <= strlen(ipStr); i++) {  // 要用到'\0'
        if (ipStr[i] == '\0' || ipStr[i] == '.') {
            ipInt = atoi(strIpIndex);
            if (ipInt < 0 || ipInt > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ipAdd += (ipInt * ((unsigned int)pow(256.0, a)));
            a--;
            memset(strIpIndex, 0, sizeof(strIpIndex));
            j = 0;
            continue;
        }
        strIpIndex[j] = ipStr[i];
        j++;
    }
    //*** printf("[INFO] IP str: %s, uint32 value: %u\n", ipStr, ipAdd);
    return ipAdd;
}

/******* lpm4写入: ip4forward *******/
int32_t test_insert_vertex_ip4forward_lpm4(GmcStmtT *stmt, const char *vtxLabelName, uint32_t vr_id, uint32_t vrf_index,
    uint32_t destIpAddrs, uint32_t destIpAddr, uint8_t maskLen)
{
    for (int loop = destIpAddrs; loop < destIpAddr; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        char ipAddrTmp[16];
        (void)sprintf(ipAddrTmp, "192.168.%d.0", loop);
        uint32_t transVal =
            TransIp(ipAddrTmp);  // ************  3232239108  192.地址，切割 --> 二进制（去掉.）--> 10进制

        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        EXPECT_EQ(GMERR_OK, ret);

        // fixed 36
        char wrFixed[34] = "write";
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
        EXPECT_EQ(GMERR_OK, ret);

        // fixed 16
        ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// /******* 索引 Scan *******/
int scan_end = 0;
int g_loopNum = 1000;
int32_t test_index_scan_ip4forward(
    GmcStmtT *stmt, const char *keyName, void *expValue, int threadId, const char *comment, int isPrint = 1)
{
    scan_end = 0;
    int cnt = 0;
    for (unsigned int loop = 0; loop < g_loopNum + 1; loop++) {
        cnt++;
        scan_end = cnt;
        ret = GmcFetch(stmt, &isFinish);
        TEST_SCAN_DEL_RES(0, ret, cnt);
        // 打印 printf("[INFO] LOOP_%d gmcfetch num: %d, GmcFetch status %d \n", cnt, scan_end, ret);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", cnt, ret);
            scan_end = cnt;
            return GMERR_OK;
        }
        unsigned int scVrId;
        ret = queryFieldValueAndCompare(stmt, "vr_id", &scVrId, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "vr_id", scVrId, threadId, isPrint, 0);

        unsigned int scVrfIndex;
        ret = queryFieldValueAndCompare(stmt, "vrf_index", &scVrfIndex, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "vrf_index", scVrfIndex, threadId, isPrint, 0);

        unsigned int scDestIpAddr;
        ret = queryFieldValueAndCompare(stmt, "dest_ip_addr", &scDestIpAddr, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "dest_ip_addr", scDestIpAddr, threadId, isPrint, 0);

        unsigned char scMaskLen;
        ret = queryFieldValueAndCompare(stmt, "mask_len", &scMaskLen, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);
        EXPECT_EQ(*(unsigned char *)expValue, scMaskLen);
        TEST_INFO(comment, "mask_len", scMaskLen, threadId, isPrint, 0);
        unsigned short scQosProfileId;
        ret = queryFieldValueAndCompare(stmt, "qos_profile_id", &scQosProfileId, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, valueSize);
        TEST_INFO(comment, "qos_profile_id", scQosProfileId, threadId, isPrint, 999);

        unsigned int scNhpGroupId;
        ret = queryFieldValueAndCompare(stmt, "nhp_group_id", &scNhpGroupId, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "nhp_group_id", scNhpGroupId, threadId, isPrint, 999);

        // batch update 3 fields
        unsigned int scPathFlags;
        ret = queryFieldValueAndCompare(stmt, "path_flags", &scPathFlags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "path_flags", scPathFlags, threadId, isPrint, 999);

        unsigned int scFlags;
        ret = queryFieldValueAndCompare(stmt, "flags", &scFlags, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "flags", scFlags, threadId, isPrint, 999);

        unsigned int scAppVersion;
        ret = queryFieldValueAndCompare(stmt, "app_version", &scAppVersion, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "app_version", scAppVersion, threadId, isPrint, 999);

        unsigned int scTableSmoothId;
        ret = queryFieldValueAndCompare(stmt, "table_smooth_id", &scTableSmoothId, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "table_smooth_id", scTableSmoothId, threadId, isPrint, 999);

        unsigned int scPrimaryLabel;
        ret = queryFieldValueAndCompare(stmt, "primary_label", &scPrimaryLabel, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "primary_label", scPrimaryLabel, threadId, isPrint, 999);

        unsigned int scAttributeId;
        ret = queryFieldValueAndCompare(stmt, "attribute_id", &scAttributeId, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        TEST_INFO(comment, "attribute_id", scAttributeId, threadId, isPrint, 999);

        unsigned char scNhpGroupFlag;
        ret = queryFieldValueAndCompare(stmt, "nhp_group_flag", &scNhpGroupFlag, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);
        TEST_INFO(comment, "nhp_group_flag", scNhpGroupFlag, threadId, isPrint, 999);

        char rdSvcCtxHighPrioFixed[37] = {};
        ret = queryFieldValueAndCompare(stmt, "svc_ctx_high_prio", rdSvcCtxHighPrioFixed, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(34, valueSize);
        TEST_INFO_STR(comment, "svc_ctx_high_prio", rdSvcCtxHighPrioFixed, threadId, isPrint, 999);
    }
    GmcResetStmt(stmt);
    return ret;
}

int RestartAndConn(GmcConnT **conn, GmcStmtT **stmt)
{
    // 持久化重启
    ret = testGmcDisconnect(*conn, *stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/stop.sh -f");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "重启服务");
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "创建epoll");
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "Connect");
    ret = testGmcConnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return T_OK;
}

// 全表扫描查询
int readTable(GmcStmtT *stmt, const char *tableOut)
{
    ret = 0;
    AW_FUN_Log(LOG_STEP, "全表扫描查询,校验表%s", tableOut);

    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while ((!isFinish) && (ret == GMERR_OK)) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    return cnt;
}
