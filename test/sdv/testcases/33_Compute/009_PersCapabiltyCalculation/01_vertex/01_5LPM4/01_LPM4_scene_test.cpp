/*****************************************************************************
 Description  : LPM4索引场景测试
 History      ://(持久化能力上车计算(03_DML\050_LPM4\LPM4_scene_test.cpp)
 Author       : 
 Modification :
 Date         : 
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_configJson[128] = "{\"max_record_count\" : 999999}";
GmcStmtT *stmt_sn_sync;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
GmcConnT *subChan;
char *vtxLabelJson7 = NULL, *vtxLabelJson8 = NULL, *vtxLabelJson9 = NULL;
char *vtxLabelJson10 = NULL, *vtxLabelJson42 = NULL;
char *edgeLabelJson78 = NULL, *edgeLabelJson79 = NULL, *edgeLabelJson89 = NULL, *edgeLabelJson910 = NULL,
     *edgeLabelJson1042 = NULL;
const char *vtxLabelName7 = "ip4forward";
const char *vtxLabelName8 = "nhp_group";
const char *vtxLabelName9 = "nhp_group_node";
const char *vtxLabelName10 = "nhp";
const char *vtxLabelName42 = "nhp_std";
const char *edgeLabelNameF78 = (char *)"from_7_to_8";
const char *edgeLabelNameF79 = (char *)"from_7_to_9";
const char *edgeLabelNameF89 = (char *)"from_8_to_9";
const char *edgeLabelNameF910 = (char *)"from_9_to_10";
const char *edgeLabelNameF1042 = (char *)"from_10_to_42";

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

class LPM4_scene_test : public testing::Test {
public:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {};

    virtual void SetUp();
    virtual void TearDown();
};

void LPM4_scene_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");
    res = testEnvInit();
    EXPECT_EQ(GMERR_OK, res);
    res = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);

    conn = NULL;
    stmt = NULL;
    vertexLabel = NULL;
    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(1);
}

void LPM4_scene_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    // 断开客户端连接
    GmcDropVertexLabel(stmt, "ip4forward");
    GmcDropVertexLabel(stmt, "ip4forward_1");
    GmcDropVertexLabel(stmt, "ip4forward_2");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    res = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}


// 05.根据lpm4索引同步更新
TEST_F(LPM4_scene_test, Compute_009_001_01_011) //DML_050_LPM4_scene_005
{
    int ThreadId = 0;
    int isPrint = 1, ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    AW_FUN_Log(LOG_STEP, "  写入数据"); // 写入数据  192.168.[loop].0  |  24
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, 0, oper_nums, 24);

    AW_FUN_Log(LOG_STEP, "  lpm4索引同步更新,提示lpm4索引不支持更新操作"); 
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    char ipValue[50];
    memset(ipValue, '\0', sizeof(ipValue));
    strcpy(ipValue, "192.168.0");  
    strcat(ipValue, ".0");
    uint32_t dest_ip_addr = TransIp(ipValue);  // 门禁报错硬编码dest_ip_addr = TransIp("***********");

    uint8_t mask_len = 18;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");  // lpm4索引不支持更新操作
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char up_uint8 = 22;
    unsigned short up_qos_profile_id = 2222;
    char up_fixed[35] = "update";
    unsigned char up_nhp_group_flag = 22;

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "ip4forward_lpm");  // lpm4索引不支持更新操作
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    AW_FUN_Log(LOG_STEP, "[INFO] update data with lpm4 index, status is %d \n", ret); 

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    AW_FUN_Log(LOG_STEP, "\n[INFO] [ lpm4 update ] affect row: %d \n\n", affectRows); 

    AW_FUN_Log(LOG_STEP, "  lpm4索引扫描"); 
    unsigned char exp_mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &up_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, ThreadId, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  根据lpm4索引删除，预期失败"); 
    vr_id = 0;
    vrf_index = 0;
    dest_ip_addr = TransIp(ipValue);
    mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    AW_FUN_Log(LOG_STEP, "\n[INFO] [ lpm4 delete ] affect row: %d \n\n", affectRows);

    // lpm4索引扫描
    exp_mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, ThreadId, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  写入数据"); // 写入数据  192.168.[loop].0  |  24
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, oper_nums + 100, 24);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  lpm4索引同步更新,提示lpm4索引不支持更新操作"); 
    vr_id = 0;
    vrf_index = 0;
    dest_ip_addr = TransIp(ipValue);

    mask_len = 18;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");  // lpm4索引不支持更新操作
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
    EXPECT_EQ(GMERR_OK, ret);

    up_uint8 = 22;
    up_qos_profile_id = 2222;
    char up_fixed1[35] = "update";
    up_nhp_group_flag = 22;

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed1, 34);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "ip4forward_lpm");  // lpm4索引不支持更新操作
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    AW_FUN_Log(LOG_STEP, "[INFO] update data with lpm4 index, status is %d \n", ret); 

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    AW_FUN_Log(LOG_STEP, "\n[INFO] [ lpm4 update ] affect row: %d \n\n", affectRows); 

    AW_FUN_Log(LOG_STEP, "  lpm4索引扫描"); 
    exp_mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &up_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, ThreadId, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  根据lpm4索引删除，预期失败"); 
    vr_id = 0;
    vrf_index = 0;

    dest_ip_addr = TransIp(ipValue);
    mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    AW_FUN_Log(LOG_STEP, "\n[INFO] [ lpm4 delete ] affect row: %d \n\n", affectRows);

    // lpm4索引扫描
    exp_mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, ThreadId, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数228条");
    int32_t record1 = 228;
    ret = readTable(stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


