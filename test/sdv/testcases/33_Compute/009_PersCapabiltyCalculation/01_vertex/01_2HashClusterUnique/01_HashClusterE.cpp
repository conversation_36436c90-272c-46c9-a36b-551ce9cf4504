/* ****************************************************************************
 Description  : HashCluster支持唯一属性验证
 Node      ://(持久化能力上车计算(03_DML\059_SecondIndexEnhance\HashClusterE.cpp)
    001 hashcluster为唯一索引，正常插入数据
    006 hashcluster为唯一索引，通过hashcluster索引更新数据
    007 hashcluster为唯一索引，通过hashcluster索引删除数据
    008 hashcluster为唯一索引，通过hashcluster索引fetch数据
 Author       : 
 Modification :
 Date         : 
 node :
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "SecondIndex.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

GmcConnT *g_conn = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt = NULL, *g_stmt_async = NULL;

const char *g_labelName = "T25";
const char *g_PKName = "T25_PK";
const char *g_localName = "local_key1";
const char *g_LocalhashName = "localhash_key1";
const char *g_HashclusterName = "hashcluster_key1";

class HashClusterUnique_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void HashClusterUnique_test::SetUp()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");

    int ret;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    //建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/UniqueIndex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    GmcDropVertexLabel(g_stmt, g_labelName);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schema);
    AW_CHECK_LOG_BEGIN();
}

void HashClusterUnique_test::TearDown()
{
    int ret = 0;
    AW_CHECK_LOG_END();
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    //断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}

// 001 hashcluster为唯一索引，正常插入数据
TEST_F(HashClusterUnique_test, Compute_009_001_01_004) //DML_059_025
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    AW_FUN_Log(LOG_STEP, " 插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " 插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 30; i < 100; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 30; i < 100; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = 100;
    ret = readTable(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


// 006 hashcluster为唯一索引，通过hashcluster索引更新数据
TEST_F(HashClusterUnique_test, Compute_009_001_01_005) //DML_059_030
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    AW_FUN_Log(LOG_STEP, " 插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 0; i < 100; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    hashcluster_value = 3;
    locahash_value = 102, local = 104, value = 104;
    AW_FUN_Log(LOG_STEP, "  hashcluster update");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, &locahash_value, NULL, &local, false);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 102, local = 104, value = 104;
    for (int i = 0; i < 10; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    hashcluster_value = 3;
    locahash_value = 202, local = 204, value = 204;
    AW_FUN_Log(LOG_STEP, " 重启后 hashcluster update 存在的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 20; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, &locahash_value, NULL, &local, false);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, " 重启后 fetch");
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 202, local = 204, value = 204;
    for (int i = 0; i < 20; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    hashcluster_value = 200;
    locahash_value = 202, local = 204, value = 204;
    AW_FUN_Log(LOG_STEP, " hashcluster update 不存在的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, &locahash_value, NULL, &local, false);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = 100;
    ret = readTable(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 007 hashcluster为唯一索引，通过hashcluster索引删除数据
TEST_F(HashClusterUnique_test, Compute_009_001_01_006) //DML_059_031
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    AW_FUN_Log(LOG_STEP, " 插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    hashcluster_value = 3;
    AW_FUN_Log(LOG_STEP, "  hashcluster delete");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        hashcluster_value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 101, locahash_value = 102, local = 104, value = 104;
    for (int i = 0; i < 10; i++) {
        ret = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(0, ret);
        f7_value++;
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    hashcluster_value = 14;
    AW_FUN_Log(LOG_STEP, "  hashcluster delete");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        hashcluster_value++;
    }

    // fetch
    hashcluster_value = 14;
    f7_value = 101, locahash_value = 102, local = 104, value = 104;
    for (int i = 0; i < 10; i++) {
        ret = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(0, ret);
        f7_value++;
    }
    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数80条");
    int32_t record1 = 80;
    ret = readTable(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


// 008 hashcluster为唯一索引，通过hashcluster索引fetch数据
TEST_F(HashClusterUnique_test, Compute_009_001_01_007) //DML_059_032
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    AW_FUN_Log(LOG_STEP, " 插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 2, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_HashclusterName, 1, false, &f7_value, &value,
            &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, " 插入数据");
    hashcluster_value = 33;
    f7_value = 31, locahash_value = 32, local = 34, value = 34;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 30; i < 100; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, " hashcluster fetch");
    hashcluster_value = 33;
    f7_value = 31, locahash_value = 32, local = 34, value = 34;
    for (int i = 30; i < 100; i++) {
        int cnt = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_HashclusterName, 1, false, &f7_value, &value,
            &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数100条");
    int32_t record1 = 100;
    ret = readTable(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}


