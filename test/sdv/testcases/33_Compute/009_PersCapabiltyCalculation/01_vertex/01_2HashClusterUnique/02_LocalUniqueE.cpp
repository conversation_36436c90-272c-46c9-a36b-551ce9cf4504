/* ****************************************************************************
 Description  : local 支持唯一属性验证
 Node      ://(持久化能力上车计算(03_DML\059_SecondIndexEnhance\LocalUniqueE.cpp)
    001 local为唯一索引，正常插入数据
    006 local为唯一索引，通过local索引更新数据(不支持，异常场景)
    007 local为唯一索引，通过local索引删除数据(区间删除)
    008 local为唯一索引，通过local索引fetch数据(区间扫描)

 V3转化用例补充：
    011 localkey索引删除与扫描并发
    012 localkey秒级索引删除与扫描并发
 Author       :  
 Modification :
 Date         :
 node :
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "SecondIndex.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char const *view_name = "V\\$DRT_LONG_OPERATION_STAT";

GmcConnT *g_conn = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt = NULL, *g_stmt_async = NULL;

const char *g_labelName = "T25";
const char *g_PKName = "T25_PK";
const char *g_localName = "local_key1";
const char *g_LocalhashName = "localhash_key1";
const char *g_HashclusterName = "hashcluster_key1";

class LocalUnique_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void LocalUnique_test::SetUp()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");

    int ret;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    char *schema = NULL;
    //建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/UniqueIndex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    GmcDropVertexLabel(g_stmt, g_labelName);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(schema);
}

void LocalUnique_test::TearDown()
{
    int ret = 0;
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    //断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}

// 001 local为唯一索引，正常插入数据, 1.建表，local索引，插入数据，local 查询、更新、删除；重启；再次执行重启前操作；
TEST_F(LocalUnique_test, Compute_009_001_01_010)  //DML_059_033
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 1, value = 4;

    AW_FUN_Log(LOG_STEP, " 插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 200; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, "  fetch");
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 1, value = 4;
    for (int i = 0; i < 200; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, "  local 区间扫描[21,120]");
    unsigned int arrLen = 1;
    uint16_t l_val_del = 21, r_val_del = 120;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT16;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(uint16_t);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT16;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(uint16_t);

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = &leftKeyProps_del[0];
    items_sc[0].rValue = &rightKeyProps_del[0];
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt, items_sc, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_localName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    for (int i = 0; i < 110; i++) {
        bool isFinish = false;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true)
            break;
        bool isNull;
        uint16_t f5_value;
        ret = GmcGetVertexPropertyByName(g_stmt, (char *)"F5", &f5_value, sizeof(f5_value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        if ((f5_value < l_val_del) || (f5_value > r_val_del)) {
            AW_FUN_Log(LOG_STEP, "[error] : the local value %d no in range\n", f5_value);
        }
        f7_value = f5_value, locahash_value = f5_value + 1, hashcluster_value = f5_value + 2, local = f5_value,
        value = f5_value + 3;
        get_VertexProperty(g_stmt, value, 0, (char *)"string", &f7_value, &locahash_value, &hashcluster_value, &local);
        cnt++;
    }
    EXPECT_EQ(100, cnt);
    free(leftKeyProps_del);
    free(rightKeyProps_del);

    AW_FUN_Log(LOG_STEP, " local 更新数据");
    local = 1;
    value = 104;
    // local update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        uint16_t f5_value = local;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_localName);
        EXPECT_EQ(GMERR_OK, ret);
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, NULL, NULL, NULL, false);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 1, value = 104;
    for (int i = 0; i < 2; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, "  local 区间删除[1,30]");
    arrLen = 1;
    l_val_del = 1, r_val_del = 30;

    GmcPropValueT *leftKeyProps_dell = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_dell[0].type = GMC_DATATYPE_UINT16;
    leftKeyProps_dell[0].value = &l_val_del;
    leftKeyProps_dell[0].size = sizeof(uint16_t);

    GmcPropValueT *rightKeyProps_dell = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_dell[0].type = GMC_DATATYPE_UINT16;
    rightKeyProps_dell[0].value = &r_val_del;
    rightKeyProps_dell[0].size = sizeof(uint16_t);

    GmcRangeItemT items_scl[arrLen];
    items_scl[0].lValue = &leftKeyProps_dell[0];
    items_scl[0].rValue = &rightKeyProps_dell[0];
    items_scl[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_scl[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_scl[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt, items_scl, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_localName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    free(leftKeyProps_dell);
    free(rightKeyProps_dell);

    AW_FUN_Log(LOG_STEP, "  fetch");
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 1, value = 4;
    for (int i = 0; i < 100; i++) {
        if (i < 30) {
            ret = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value,
                &hashcluster_value, &local);
            EXPECT_EQ(0, ret);
        } else {
            int cnt = ReadAndCheck_Vertexdata(
                g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
            EXPECT_EQ(1, cnt);
        }
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] DB持久化落盘重启");
    ret = RestartAndConn(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 插入数据");
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 1, value = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, "  fetch");
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "  local 区间删除[41,60]");
    arrLen = 1;
    l_val_del = 41, r_val_del = 60;

    GmcPropValueT *leftKeyProps_dell2 = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_dell2[0].type = GMC_DATATYPE_UINT16;
    leftKeyProps_dell2[0].value = &l_val_del;
    leftKeyProps_dell2[0].size = sizeof(uint16_t);

    GmcPropValueT *rightKeyProps_dell2 = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_dell2[0].type = GMC_DATATYPE_UINT16;
    rightKeyProps_dell2[0].value = &r_val_del;
    rightKeyProps_dell2[0].size = sizeof(uint16_t);

    GmcRangeItemT items_scl2[arrLen];
    items_scl2[0].lValue = &leftKeyProps_dell2[0];
    items_scl2[0].rValue = &rightKeyProps_dell2[0];
    items_scl2[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_scl2[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_scl2[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt, items_scl2, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_localName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    free(leftKeyProps_dell2);
    free(rightKeyProps_dell2);

    AW_FUN_Log(LOG_STEP, "  fetch");
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 1, value = 4;
    for (int i = 0; i < 200; i++) {
        if ((i >= 40 && i < 60)) {
            ret = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value,
                &hashcluster_value, &local);
            
            EXPECT_EQ(0, ret);
        } else {
            int cnt = ReadAndCheck_Vertexdata(
                g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
            
            EXPECT_EQ(1, cnt);
        }
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, " local 更新数据");
    local = 31;
    value = 204;
    // local update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        uint16_t f5_value = local;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_localName);
        EXPECT_EQ(GMERR_OK, ret);
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, NULL, NULL, NULL, false);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        local++;
        value++;
    }

    AW_FUN_Log(LOG_STEP, " local fetch");
    f7_value = 31, locahash_value = 32, hashcluster_value = 33, local = 31, value = 204;
    for (int i = 0; i < 2; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    (void)snprintf(g_command, 1024, "%s/gmsysview count -s %s", g_toolPath, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] 全表扫描查询数据条数180条");
    int32_t record1 = 180;
    ret = readTable(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(record1, ret);

    AW_FUN_Log(LOG_STEP, "用例结束后删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

}

