/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 事务支持带savepoint基本功能测试（Yang场景--树模型）
  持久化能力上车计算( 15_Yang\030_TransSavepoint\Savepoint_FunTr.cpp)
 Notes        : 001.乐观事务内创建savepoint，vertex操作类型为create，属性操作类型分别为五原语操作
                002.乐观事务内创建savepoint，vertex操作类型为merge，属性操作类型分别为五原语操作
                003.乐观事务内创建savepoint，vertex操作类型为replace，属性操作类型分别为五原语操作
                004.乐观事务内创建savepoint，vertex操作类型为delete，属性操作类型分别为五原语操作
                005.乐观事务内创建savepoint，vertex操作类型为remove，属性操作类型分别为五原语操作
                006.乐观事务内创建savepoint，vertex操作类型为none，属性操作类型分别为五原语操作
                
                013.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；replace、查询diff数据
                014.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；delete、查询diff数据
                015.乐观事务内创建两个不同的savepoint，分别执行delete、查询diff数据；remove、查询diff数据
                016.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；delete、查询diff数据
                017.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；remove、查询diff数据
                018.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；remove、查询diff数据
                
                029.一个乐观事务内创建3个不同的savepoint，回滚第二个savepoint，SubTree查询
                030.一个乐观事务内创建3个不同的savepoint，释放第三个savepoint，SubTree查询

                032.乐观事务内创建两个savepoint，第二个savepoint六元语操作失败，回滚第二个savepoint，重新创建savepoint，再次执行六元语操作
                033.创建两个乐观事务，每个事务分别创建一个savepoint，事务二提交失败后，事务二再次创建savepoint
                034.乐观事务内创建两个同名savepoint，分别执行六元语操作，连续回滚savepoint、SubTree查询
                035.乐观事务内创建两个匿名savepoint，分别执行六元语操作，连续释放savepoint、SubTree查询

 History      :
 Author       :  
 Modification :  
*****************************************************************************/

#include "TransSavepoint.h"

class Savepoint_FunTr : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void Savepoint_FunTr::SetUp()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");

    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcClearNamespace(g_stmt, NAMESPACE);
    GmcDropNamespace(g_stmt, NAMESPACE);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 异步创建namespace
    createNameSpace(g_stmt_async, NAMESPACE);

    // use namespace
    useNameSpace(g_stmt_async, NAMESPACE);
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, NAMESPACE, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_CHECK_LOG_BEGIN();

    // 添加日志白名单
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);
}

void Savepoint_FunTr::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    // drop namespace
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, NAMESPACE, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    dropNameSpace(g_stmt_async, NAMESPACE);

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    GmcFreeStmt(g_stmt_list);

    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    closeEpollOneThread();
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}


// 001.乐观事务内创建savepoint，vertex操作类型为create，属性操作类型分别为五原语操作 //Yang_030_FuncTree_001
TEST_F(Savepoint_FunTr, Compute_009_001_02_048)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 002.乐观事务内创建savepoint，vertex操作类型为merge，属性操作类型分别为五原语操作 //Yang_030_FuncTree_002
TEST_F(Savepoint_FunTr, Compute_009_001_02_049)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 merge 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 003.乐观事务内创建savepoint，vertex操作类型为replace，属性操作类型分别为五原语操作 //Yang_030_FuncTree_003
TEST_F(Savepoint_FunTr, Compute_009_001_02_050)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 replace 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 004.乐观事务内创建savepoint，vertex操作类型为delete，属性操作类型分别为五原语操作 //Yang_030_FuncTree_004
TEST_F(Savepoint_FunTr, Compute_009_001_02_051)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 delete 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ;

    // 对属性 P1 执行 create 操作
    GmcPropValueT propValue;
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_CREATE.\n\n");
    memcpy(propValue.propertyName, "P1", (strlen("P1") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P2 执行 merge 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_MERGE.\n\n");
    memcpy(propValue.propertyName, "P2", (strlen("P2") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P3 执行 replace 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REPLACE.\n\n");
    memcpy(propValue.propertyName, "P3", (strlen("P3") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P4 执行 delete 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REMOVE.\n\n");
    memcpy(propValue.propertyName, "P4", (strlen("P4") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P5 执行 remove 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_DELETE.\n\n");
    memcpy(propValue.propertyName, "P5", (strlen("P5") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 005.乐观事务内创建savepoint，vertex操作类型为remove，属性操作类型分别为五原语操作 //Yang_030_FuncTree_005
TEST_F(Savepoint_FunTr, Compute_009_001_02_052)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 remove 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对属性 P1 执行 create 操作
    GmcPropValueT propValue;
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_CREATE.\n\n");
    memcpy(propValue.propertyName, "P1", (strlen("P1") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P2 执行 merge 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_MERGE.\n\n");
    memcpy(propValue.propertyName, "P2", (strlen("P2") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P3 执行 replace 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REPLACE.\n\n");
    memcpy(propValue.propertyName, "P3", (strlen("P3") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P4 执行 delete 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REMOVE.\n\n");
    memcpy(propValue.propertyName, "P4", (strlen("P4") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P5 执行 remove 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_DELETE.\n\n");
    memcpy(propValue.propertyName, "P5", (strlen("P5") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 006.乐观事务内创建savepoint，vertex操作类型为none，属性操作类型分别为五原语操作//Yang_030_FuncTree_006
TEST_F(Savepoint_FunTr, Compute_009_001_02_053)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 none 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


// 013.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；replace、查询diff数据//Yang_030_FuncTree_013
TEST_F(Savepoint_FunTr, Compute_009_001_02_054)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_MERGE, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 replace 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    int newvalue = 200;
    testYangSetNodeProperty(g_root_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_REPLACE_GRAPH, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeReplaceUpdateBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 014.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；delete、查询diff数据//Yang_030_FuncTree_014
TEST_F(Savepoint_FunTr, Compute_009_001_02_055)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 replace_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_REPLACE_GRAPH, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 delete 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 015.乐观事务内创建两个不同的savepoint，分别执行delete、查询diff数据；remove、查询diff数据//Yang_030_FuncTree_015
TEST_F(Savepoint_FunTr, Compute_009_001_02_056)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** 预置数据  *******************/
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_INSERT, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    /*************** savepoint1 对container执行 delete 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 remove 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveNotDataBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 016.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；delete、查询diff数据//Yang_030_FuncTree_016
TEST_F(Savepoint_FunTr, Compute_009_001_02_057)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_MERGE, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 delete 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 017.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；remove、查询diff数据//Yang_030_FuncTree_017
TEST_F(Savepoint_FunTr, Compute_009_001_02_058)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 replace_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_REPLACE_GRAPH, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 remove 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 018.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；remove、查询diff数据//Yang_030_FuncTree_018
TEST_F(Savepoint_FunTr, Compute_009_001_02_059)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, TREE_CON_CON_CHILD_NAME, GMC_OPERATION_MERGE, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 remove 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


// 029.一个乐观事务内创建3个不同的savepoint，回滚第二个savepoint，SubTree查询//Yang_030_FuncTree_029
TEST_F(Savepoint_FunTr, Compute_009_001_02_060)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 replace 操作 *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint3 delete 操作 *******************/
    // craete savepoint3
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME3);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** 回滚savepoint2，SubTree查询 *******************/
    // 回滚savepoint2
    RollbackSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_001.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


// 030.一个乐观事务内创建3个不同的savepoint，释放第三个savepoint，SubTree查询//Yang_030_FuncTree_030
TEST_F(Savepoint_FunTr, Compute_009_001_02_061)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 replace 操作 *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint3 delete 操作 *******************/
    // craete savepoint3
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME3);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** 释放savepoint3，SubTree查询 *******************/
    // 释放savepoint3
    ReleaseSavepoint(g_conn_async, SAVEPOINT_NAME3);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_002.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


// 032.乐观事务内创建两个savepoint，第二个savepoint六元语操作失败，回滚第二个savepoint，再次执行六元语操作//Yang_030_FuncTree_032
TEST_F(Savepoint_FunTr, Compute_009_001_02_062)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container013
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    /********** savepoint1 create 操作 *********/
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /********** savepoint2 六元语操作失败 *********/
    // create savepoint2
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 0, GMERR_PRIMARY_KEY_VIOLATION);

    /********** 回滚savepoint2，再次执行六元语操作 *********/
    // 回滚savepoint2
    RollbackSavepoint(g_conn_async, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 replace 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    int newvalue = 200;
    testYangSetVertexProperty(g_stmt_root, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 033.创建两个乐观事务，每个事务分别创建一个savepoint，事务二提交失败后，事务二再次创建savepointsavepoint//Yang_030_FuncTree_033
TEST_F(Savepoint_FunTr, Compute_009_001_02_063)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    /*************** 事务1内创建savepoint1 *******************/
    // 创建 乐观事务1
    TransStart(g_conn_async);

    // 在乐观事务1 内创建 savepoint
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** 事务2内创建savepoint2 *******************/
    // use namespace
    useNameSpace(g_stmt_async1, NAMESPACE);

    // 创建 乐观事务2
    TransStart(g_conn_async1);

    // 在乐观事务2 内创建 savepoint2
    CreateSavepoint(g_conn_async1, SAVEPOINT_NAME2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务1
    TransCommit(g_conn_async);

    // 提交事务2
    TransCommit(g_conn_async1, GMERR_RESTRICT_VIOLATION);
    TransRollback(g_conn_async1);

    AW_FUN_Log(LOG_STEP, "END");
}

// 034.乐观事务内创建两个同名savepoint，分别执行六元语操作，连续回滚savepoint、SubTree查询//Yang_030_FuncTree_034
TEST_F(Savepoint_FunTr, Compute_009_001_02_064)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint1 replace 操作 *******************/
    // craete savepoint1 again
    CreateSavepoint(g_conn_async, SAVEPOINT_NAME);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** 回滚savepoint1，SubTree查询  *******************/
    // 回滚savepoint1
    RollbackSavepoint(g_conn_async, SAVEPOINT_NAME);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_003.json";

    /*************** 释放savepoint2，回滚savepoint1，SubTree查询  *******************/
    // 释放savepoint2
    ReleaseSavepoint(g_conn_async, SAVEPOINT_NAME);
    // 回滚savepoint1
    RollbackSavepoint(g_conn_async, SAVEPOINT_NAME);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath2 = "SubtreeReplyJson/containernode_Listreply_Null.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 035.乐观事务内创建两个匿名savepoint，分别执行六元语操作，连续释放savepoint、SubTree查询//Yang_030_FuncTree_035
TEST_F(Savepoint_FunTr, Compute_009_001_02_065)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, NULL);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint1 replace 操作 *******************/
    // craete savepoint1 again
    CreateSavepoint(g_conn_async, NULL);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, TREE_CON_CON_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, TREE_CON_LIST_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** 释放savepoint1，SubTree查询  *******************/
    // 释放savepoint1
    ReleaseSavepoint(g_conn_async, NULL);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_003.json";

    /*************** 释放savepoint1，SubTree查询  *******************/
    // 释放savepoint1
    ReleaseSavepoint(g_conn_async, NULL);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath2 = "SubtreeReplyJson/containernode_Listreply_Null.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


