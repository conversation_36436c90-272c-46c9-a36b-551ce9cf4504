/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
#ifndef MUSTCHECK_H
#define MUSTCHECK_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <typeinfo>
#include <fstream>
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
const char *LABEL_NAME_ROOT = "Con_0";
const char *LABEL_NAME_LIST = "list_1";
const char *LABEL_NAME_LEAFLIST = "Leaflist_1";


AsyncUserDataT g_data = {0};
GmcConnT *g_conn = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_list = NULL;
GmcStmtT *g_stmt_leaflist = NULL;
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[10] = {0};
GmcBatchT *g_batch = NULL;

GmcTxConfigT g_mSTrxConfig;

// userData结构
struct SubtreeFilterCbParam {
    int step;
    int32_t expectStatus;
    const char *expectReplyJson;
};

const char *CONFIG = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";

/******************************DML*******************************************/
int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int testYangSetNodeField(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);

    return ret;
}

void TestCreateLabel(GmcStmtT *stmtAsync, char *vertexPath, char *edgePath)
{
    int ret = 0;
    char *schemaFile = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile(vertexPath, &schemaFile);
    AW_MACRO_EXPECT_NOTNULL(schemaFile);
    ret = GmcCreateVertexLabelAsync(stmtAsync, schemaFile, CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(schemaFile);

    schemaFile = NULL;
    readJanssonFile(edgePath, &schemaFile);
    AW_MACRO_EXPECT_NOTNULL(schemaFile);
    ret = GmcCreateEdgeLabelAsync(stmtAsync, schemaFile, CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(schemaFile);
}

int testYangSetVertexProperty_Root(GmcNodeT *node, int value, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    int8_t f0Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_INT8, &f0Value, sizeof(int8_t), "F0", optype);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f1Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT8, &f1Value, sizeof(uint8_t), "F1", optype);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty_String_Root(GmcNodeT *node, char *string, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetNodeField(node, GMC_DATATYPE_STRING, string, (strlen(string)), "F2", optype);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty_Con_1(GmcNodeT *node, int value, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    int16_t a0Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_INT16, &a0Value, sizeof(int16_t), "A0", optype);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t a1Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT16, &a1Value, sizeof(uint16_t), "A1", optype);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty_List_1(GmcNodeT *node, int value, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    int64_t l0Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_INT64, &l0Value, sizeof(int64_t), "L0", optype);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t l1Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT64, &l1Value, sizeof(uint64_t), "L1", optype);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty_Leaflist_1(GmcNodeT *node, int value, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    uint32_t f0Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), "F0", optype);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

int testYangSetVertexProperty_Case_2(GmcNodeT *node, int value, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t b0Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_UINT32, &b0Value, sizeof(uint32_t), "B0", optype);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t b1Value = value;
    ret = testYangSetNodeField(node, GMC_DATATYPE_INT32, &b1Value, sizeof(int32_t), "B1", optype);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

void ModelCheck(GmcStmtT *stmt)
{
    int ret = 0;

    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    if (checkData.validateRes == false) {
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

struct ValidateParam {
    std::atomic_uint32_t *step;
    Status exceptStatus;
    GmcValidateResT validateRes;
    bool isValidateErrorPath;
    GmcErrorPathCodeE expectedErrCode;
    uint32_t expectedErrClauseIndex;
    const char *expectedErrMsg;
    const char *expectedErrPath;
    uint64_t startTime;
    bool printTime;
    bool printSize;
};

void AsyncValidateCb(void *userData, GmcValidateResT validateRes, Status status, const char *errMsg)
{
    ValidateParam *param = reinterpret_cast<ValidateParam *>(userData);
    ASSERT_EQ(param->exceptStatus, status) << errMsg;
    if (status == GMERR_OK) {
        ASSERT_EQ(param->validateRes.validateRes, validateRes.validateRes);
        ASSERT_EQ(param->validateRes.failCount, validateRes.failCount);
    }
    if (param->isValidateErrorPath) {
        GmcErrorPathInfoT msg;
        ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
        // 结果检查
        EXPECT_EQ(param->expectedErrCode, msg.errorCode);
        EXPECT_EQ(param->expectedErrClauseIndex, msg.errorClauseIndex);
        EXPECT_STREQ(param->expectedErrMsg, msg.errorMsg);
        EXPECT_STREQ(param->expectedErrPath, msg.errorPath);
        ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
    }
    (*(param->step))++;
}

int testWaitValidateAsyncRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true,
    int32_t epollFd = g_epollDataOneThread.userEpollFd)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    ValidateParam *userDatas = (ValidateParam *)userData;
    int num = *(userDatas->step);
    if (num != 0) {
        printf("%d\n", num);
    }
    while (*(userDatas->step) != expRecvNum) {
        int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n", (double)duration / 1000000,
                expRecvNum, userDatas->step);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userDatas->step = 0;
    }
    return 0;
}

#endif /* MUSTCheck_H */
