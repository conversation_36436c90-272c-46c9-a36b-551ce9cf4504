/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: MustCheckFun.cpp
 * Description:持久化能力上车计算(15_Yang\069_MustDataValidityCheck\MustCheckFun.cpp)
 * Author:
 * Create:
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "MustCheck.h"

class MustCheckFun : public testing::Test {
public:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
    virtual void SetUp();
    virtual void TearDown();
};

void MustCheckFun::SetUp()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("${TEST_HOME}/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    g_mSTrxConfig.readOnly = false;

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "Namespace069_001";
    const char *namespaceUserName = "abc";
    // 清理历史namespace
    GmcClearNamespace(g_stmt, namespace1);
    GmcDropNamespace(g_stmt, namespace1);

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void MustCheckFun::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;

    const char *namespace1 = "Namespace069_001";
    AsyncUserDataT userData = {0};

    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_list);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}


// 001.np-container节点定义must校验，写入不满足must条件的数据//Yang_069_001
TEST_F(MustCheckFun, Compute_009_001_02_078)
{
    AW_FUN_Log(LOG_STEP, "建表");
    int ret = 0;
    char vertexPath[] = "./schemaFile/np-container_001.gmjson";
    char edgePath[] = "./schemaFile/yang_edge.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);
    ModelCheck(g_stmt_async);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &g_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, LABEL_NAME_ROOT, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    int value = 1;
    ret = testYangSetVertexProperty_Root(g_rootNode, value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(g_batch, batch_execute_callback, &g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_data.status);
    AW_MACRO_EXPECT_EQ_INT(1, g_data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, g_data.succNum);
    GmcBatchDestroy(g_batch);

    // 数据校验
    bool isDataService = true;
    std::atomic_uint32_t step{0};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/Con_0/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MUST, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 003.p-container节点定义must校验，写入不满足must条件的数据//Yang_069_003
TEST_F(MustCheckFun, Compute_009_001_02_079)
{
    AW_FUN_Log(LOG_STEP, "建表");
    int ret = 0;
    char vertexPath[] = "./schemaFile/p-container_003.gmjson";
    char edgePath[] = "./schemaFile/yang_edge.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);
    ModelCheck(g_stmt_async);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &g_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, LABEL_NAME_ROOT, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    int value = 1;
    ret = testYangSetVertexProperty_Root(g_rootNode, value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexProperty_Con_1(g_childNode[0], value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(g_batch, batch_execute_callback, &g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_data.status);
    AW_MACRO_EXPECT_EQ_INT(1, g_data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, g_data.succNum);
    GmcBatchDestroy(g_batch);

    // 数据校验
    bool isDataService = true;
    std::atomic_uint32_t step{0};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/Con_0/Con_1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MUST, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 005.list节点定义must校验，写入不满足must条件的数据//Yang_069_005
TEST_F(MustCheckFun, Compute_009_001_02_080)
{
    AW_FUN_Log(LOG_STEP, "建表");
    int ret = 0;
    char vertexPath[] = "./schemaFile/list_005.gmjson";
    char edgePath[] = "./schemaFile/yang_edge.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);
    ModelCheck(g_stmt_async);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &g_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, LABEL_NAME_ROOT, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    int value = 1;
    ret = testYangSetVertexProperty_Root(g_rootNode, value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexProperty_Con_1(g_childNode[0], value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, LABEL_NAME_LIST, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(g_batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexProperty_List_1(g_childNode[1], value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(g_batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(g_batch, batch_execute_callback, &g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_data.status);
    AW_MACRO_EXPECT_EQ_INT(2, g_data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, g_data.succNum);
    GmcBatchDestroy(g_batch);

    // 数据校验
    bool isDataService = true;
    std::atomic_uint32_t step{0};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/Con_0/Con_1/list_1[L0=1]",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MUST, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 007.leaf-list节点定义must校验，写入不满足must条件的数据//Yang_069_007
TEST_F(MustCheckFun, Compute_009_001_02_081)
{
    AW_FUN_Log(LOG_STEP, "建表");
    int ret = 0;
    char vertexPath[] = "./schemaFile/leaf-list_007.gmjson";
    char edgePath[] = "./schemaFile/yang_edge.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);
    ModelCheck(g_stmt_async);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &g_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, LABEL_NAME_ROOT, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    int value = 1;
    ret = testYangSetVertexProperty_Root(g_rootNode, value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexProperty_Con_1(g_childNode[0], value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(g_batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, LABEL_NAME_LEAFLIST, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(g_batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexProperty_Leaflist_1(g_childNode[1], value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(g_batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(g_batch, batch_execute_callback, &g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&g_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_data.status);
    AW_MACRO_EXPECT_EQ_INT(2, g_data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, g_data.succNum);
    GmcBatchDestroy(g_batch);

    // 数据校验
    bool isDataService = true;
    std::atomic_uint32_t step{0};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = {.validateRes = false, .failCount = 1},
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/Con_0/Con_1/Leaflist_1[F0=1]",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MUST, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

