[{"type": "container", "name": "Con_0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int8"}, {"name": "F1", "type": "uint8"}, {"name": "F2", "type": "string", "nullable": true}, {"name": "Con_1", "type": "container", "presence": true, "clause": [{"type": "must", "formula": "/Con_0/Con_1/A0 > 5"}], "fields": [{"name": "A0", "type": "int16"}, {"name": "A1", "type": "uint16"}]}, {"name": "Choice_1", "type": "choice", "fields": [{"name": "Case_2", "type": "case", "fields": [{"name": "B0", "type": "uint32"}, {"name": "B1", "type": "int32"}]}]}], "keys": [{"node": "Con_0", "name": "CON_0_PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "L0", "type": "int64", "nullable": false}, {"name": "L1", "type": "uint64"}, {"name": "Con_2", "type": "container", "fields": [{"name": "A0", "type": "int8"}]}, {"name": "Choice_2", "type": "choice", "fields": [{"name": "Case_3", "type": "case", "fields": [{"name": "B0", "type": "int8"}]}]}], "keys": [{"node": "list_1", "name": "list_1_PK", "fields": ["PID", "L0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "Leaflist_1", "type": "leaf-list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "Leaflist_1", "name": "Leaflist_1_PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]