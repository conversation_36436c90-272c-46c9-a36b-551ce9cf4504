[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "int32", "nullable": true, "default": 1}, {"name": "root_F2", "type": "int32", "nullable": true, "default": 2}, {"type": "container", "name": "container_1", "clause": [{"type": "when", "formula": "/main_label/choice_1/case_1_1/case_1_1_F2 < 100"}], "fields": [{"name": "con_1_F1", "type": "int32", "nullable": true, "default": 3}, {"name": "con_1_F2", "type": "int32", "nullable": true, "default": 4}]}, {"type": "container", "name": "container_2", "fields": [{"name": "con_2_F1", "type": "int32", "nullable": true, "default": 5}, {"name": "con_2_F2", "type": "int32", "nullable": true, "default": 6}]}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1_1", "fields": [{"name": "case_1_1_F1", "type": "int32", "nullable": true, "default": 7}, {"name": "case_1_1_F2", "type": "int32", "nullable": true, "default": 8}]}, {"type": "case", "name": "case_1_2", "default": true, "fields": [{"name": "case_1_2_F1", "type": "int32", "nullable": true, "default": 9}, {"name": "case_1_2_F2", "type": "int32", "nullable": true, "default": 10}]}]}, {"type": "choice", "name": "choice_2", "fields": [{"type": "case", "name": "case_2_1", "fields": [{"name": "case_2_1_F1", "type": "int32", "nullable": true, "default": 11}, {"name": "case_2_1_F2", "type": "int32", "nullable": true, "default": 12}]}, {"type": "case", "name": "case_2_2", "default": true, "fields": [{"name": "case_2_2_F1", "type": "int32", "nullable": true, "default": 13}, {"name": "case_2_2_F2", "type": "int32", "nullable": true, "default": 14}]}]}], "keys": [{"node": "main_label", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "main_label", "name": "<PERSON><PERSON><PERSON>", "fields": ["root_F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}, {"type": "list", "name": "list_label_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "list_1_F1", "type": "uint32", "nullable": false}, {"name": "list_1_F2", "type": "int32", "nullable": true, "default": 16}, {"name": "list_1_F3", "type": "int32", "nullable": true, "default": 17}], "keys": [{"node": "list_label_1", "name": "pk", "fields": ["PID", "list_1_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_label_2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "list_2_F1", "type": "uint32", "nullable": false}, {"name": "list_2_F2", "type": "int32", "nullable": true, "default": 19}, {"name": "list_2_F3", "type": "int32", "nullable": true, "default": 20}], "keys": [{"node": "list_label_2", "name": "pk", "fields": ["PID", "list_2_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaf-list_label_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "leaf-list_1_F1", "type": "uint32", "nullable": false, "default": [21, 22, 23]}], "keys": [{"fields": ["PID", "leaf-list_1_F1"], "node": "leaf-list_label_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaf-list_label_2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "leaf-list_2_F1", "type": "uint32", "nullable": false, "default": [24, 25, 26]}], "keys": [{"fields": ["PID", "leaf-list_2_F1"], "node": "leaf-list_label_2", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]