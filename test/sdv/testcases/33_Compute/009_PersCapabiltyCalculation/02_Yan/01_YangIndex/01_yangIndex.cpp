
/*****************************************************************************
 Description  : yang自增字段和索引
 Notes        :  // 持久化能力上车计算( 06_Other\074_YangIndex\yangIndex.cpp)
            Other_074_001_003 leaflist包含1个自增字段，刷盘重启后，查询表模型，写入数据，subtree查询（id  PID（固定值） 第三个字段添加自增
            Other_074_001_006 container节点包含，local唯一索引
            Other_074_001_007 container节点包含 loacl非唯一索引
            Other_074_001_008 container节点包含 localhash唯一索引
            Other_074_001_009 container节点包含 localhash非唯一索引
备注：
自增列 类型必须是uint32 uint64
 History      :
 Author       : 
 Modification :
 Date         :
*****************************************************************************/
#include "tools.h"


char g_dbFilePath[1024] = {0};
char g_newDbFilePath[1024] = {0};

class Incremental_check : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};
void Incremental_check::SetUp()
{
    int ret;
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 异步建连
    AsyncUserDataT data = {0};
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, NAMESPACE);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = NAMESPACE;
    nspCfg.userName = NAMESPACE_USER_NAME;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, NAMESPACE, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcAllocStmt(g_connAsync, &g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtLeafList1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &g_stmtLeafList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 生成后天目录
    system("rm -rf dynFolder;mkdir dynFolder");
}
void Incremental_check::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};

    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, NAMESPACE);
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    system("rm -rf dynFolder;mkdir dynFolder");
}


// 003. leaflist包含1个自增字段，写入数据（不含自增之列），subtree查询自增列数据, 查询yang模型,再写入数据（不含自增之列），subtree查询自增列数据。
TEST_F(Incremental_check, Compute_009_001_02_001) //Other_074_001_003
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] // 模型校验");
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create savepoint
    const char *g_savepointName = "sp";
    CreateSavepoint(g_connAsync, g_savepointName);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] // 设置根节点");
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] // 设置container_2节点");
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[info] // 设置list节点和其属性值");
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "[info] // 批处理提交");
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    AW_FUN_Log(LOG_STEP, "[info] // subtree查询");
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


static const char *g_yangcfgJson006 = R"({"auto_increment": [100,100,100], "isFastReadUncommitted": 0, "yang_model": 1})";
// 006.container local唯一
TEST_F(Incremental_check, Compute_009_001_02_002)  //Other_074_001_006
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_6.gmjson", g_yangcfgJson006);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valuerootF2 = 105;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_INT32, &valuerootF2, sizeof(int32_t),"root_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
    }
    
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/6_reply.json");

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.container loacl非唯一
TEST_F(Incremental_check, Compute_009_001_02_003)  //Other_074_001_007
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_7.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valuerootF2 = 105;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_INT32, &valuerootF2, sizeof(int32_t),"root_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }


    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/7_reply.json");
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.container localhash唯一
TEST_F(Incremental_check, Compute_009_001_02_004)  //Other_074_001_008
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_8.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valuerootF2 = 105;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_INT32, &valuerootF2, sizeof(int32_t),"root_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2节点
    GmcNodeT *con2Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "container_2", GMC_OPERATION_INSERT, &con2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_2/con_2_F2的值
    int32_t valueCon2F2 = 105;
    ret = SetNodeProperty(con2Node, GMC_DATATYPE_INT32, &valueCon2F2, sizeof(int32_t),
                            "con_2_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply.json");
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.container localhash非唯一
TEST_F(Incremental_check, Compute_009_001_02_005)  //Other_074_001_009
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertex_9.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置choice_1节点
    GmcNodeT *choice1Node = NULL;
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &choice1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1节点
    GmcNodeT *case1N1Node = NULL;
    ret = GmcYangEditChildNode(choice1Node, "case_1_1", GMC_OPERATION_INSERT, &case1N1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case_1_1_F2的值
    int32_t valueCase1N1F2 = 105;
    ret = SetNodeProperty(case1N1Node, GMC_DATATYPE_INT32, &valueCase1N1F2, sizeof(int32_t),
                            "case_1_1_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList1, "list_label_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList1, &g_listNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList1F1 = i + 1;
        ret = SetNodeProperty(g_listNode1, GMC_DATATYPE_UINT32, &valueList1F1, sizeof(uint32_t),
                            "list_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmtList2, "list_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList2, &g_listNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t valueList2F1 = i + 2;
        ret = SetNodeProperty(g_listNode2, GMC_DATATYPE_UINT32, &valueList2F1, sizeof(uint32_t),
                            "list_2_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/9_reply.json");
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


