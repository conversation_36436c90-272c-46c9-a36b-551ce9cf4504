/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 持久化能力上车计算(15_Yang\066_WhenDataValidityCheck\WhenCheck.cpp)
 * Author:
 * Create:
 */
#include "WhenCheck.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class WhenValidityCheck_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void WhenValidityCheck_test::SetUpTestCase()
{
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化

    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void WhenValidityCheck_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
}

void WhenValidityCheck_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, NAMESPACE);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = NAMESPACE;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NAMESPACE_USER_NAME;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, NAMESPACE, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);

    AW_CHECK_LOG_BEGIN();
}

void WhenValidityCheck_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, NAMESPACE, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

/*
root--con_1
root--con_2
root--con_3
root--con_4
root--con_5

root--choice_1--case_1_1
root--choice_2--case_2_1
root--choice_3--case_3_1
root--choice_4--case_4_1
root--choice_5--case_5_1

root--list_1
root--list_2
root--list_3
root--list_4
root--list_5

root--leaflist_1
root--leaflist_2
root--leaflist_3
root--leaflist_4
root--leaflist_5
*/


/*****************************************************************************
 Description  : 001.写入数据，包括满足when条件和不满足when条件的数据，when数据校验，查询校验结果
 Author       : Yang_066_Func_001
*****************************************************************************/
TEST_F(WhenValidityCheck_test, Compute_009_001_02_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;

    // 模型校验
    ModelCheck(g_stmt_async);

    /***************************写入待校验数据***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    root_A/F2(/root_A/F0 = 100)          | root_A/F2(/root_A/F0 = 100)          | root_A/F2(属性可见)
    root_A/F8(/root_A/F0 = 100)          | root_A/F8(/root_A/F0 = 100)          | root_A/F8(属性默认值可见)
    root_A/F9(/root_A/F0 = 200)          | root_A/F9(/root_A/F0 = 100)          | root_A/F9(属性默认值不可见)
    con_1_A(./F0 = 200)                  | con_1_A(./F0 = 100)                  | con_1_A(节点不可见)
    con_2_A(/root_A/F1 = 100)            | con_2_A(/root_A/F1 = 100)            | con_2_A(节点可见)
    con_2_A/F1(/root_A/F0 > 0)           | con_2_A/F1(/root_A/F0 = 100)         | con_2_A/F1(属性可见)
    con_2_A/F2(/root_A/F0 > 0)           | con_2_A/F2(/root_A/F0 = 100)         | con_2_A/F2(属性可见)
    con_2_A/F6(/root_A/con_2_A/F5 = 555) | con_2_A/F6(/root_A/con_2_A/F5 = 100) | con_2_A/F6(属性默认值不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_A", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1_A", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2_A", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilter(g_stmt_async, "root_A", "Yang_066_Func_001");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 002.写入数据后when数据校验，再次insert数据，包含不符合when条件的数据，
                再次when数据校验，查询校验结果
 Author       : Yang_066_Func_002
*****************************************************************************/
TEST_F(WhenValidityCheck_test, Compute_009_001_02_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;

    // 模型校验
    ModelCheck(g_stmt_async);

    /***************************写入待校验数据***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    root_A/F2(/root_A/F0 = 100)          | root_A/F2(/root_A/F0 = 100)          | root_A/F2(属性可见)
    root_A/F8(/root_A/F0 = 100)          | root_A/F8(/root_A/F0 = 100)          | root_A/F8(属性默认值可见)
    root_A/F9(/root_A/F0 = 200)          | root_A/F9(/root_A/F0 = 100)          | root_A/F9(属性默认值不可见)
    con_1_A(./F0 = 200)                  | con_1_A(./F0 = 100)                  | con_1_A(节点不可见)
    con_2_A(/root_A/F1 = 100)            | con_2_A(/root_A/F1 = 100)            | con_2_A(节点可见)
    con_2_A/F1(/root_A/F0 > 0)           | con_2_A/F1(/root_A/F0 = 100)         | con_2_A/F1(属性可见)
    con_2_A/F2(/root_A/F0 > 0)           | con_2_A/F2(/root_A/F0 = 100)         | con_2_A/F2(属性可见)
    con_2_A/F6(/root_A/con_2_A/F5 = 555) | con_2_A/F6(/root_A/con_2_A/F5 = 100) | con_2_A/F6(属性默认值不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_A", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1_A", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2_A", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilter(g_stmt_async, "root_A", "Yang_066_Func_002_01");

    // 再次insert数据
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_1_A(./F0 = 200)                  | con_1_A(./F0 = 200)                  | con_1_A(节点可见)
    */
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_A", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1_A", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilter(g_stmt_async, "root_A", "Yang_066_Func_002_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 003.写入数据后when数据校验，再次merge数据，包含不符合when条件的数据，
                再次when数据校验，查询校验结果
 Author       : Yang_066_Func_003
*****************************************************************************/
TEST_F(WhenValidityCheck_test, Compute_009_001_02_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;

    // 模型校验
    ModelCheck(g_stmt_async);

    /***************************写入待校验数据***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    root_A/F2(/root_A/F0 = 100)          | root_A/F2(/root_A/F0 = 100)          | root_A/F2(属性可见)
    root_A/F8(/root_A/F0 = 100)          | root_A/F8(/root_A/F0 = 100)          | root_A/F8(属性默认值可见)
    root_A/F9(/root_A/F0 = 200)          | root_A/F9(/root_A/F0 = 100)          | root_A/F9(属性默认值不可见)
    con_1_A(./F0 = 200)                  | con_1_A(./F0 = 100)                  | con_1_A(节点不可见)
    con_2_A(/root_A/F1 = 100)            | con_2_A(/root_A/F1 = 100)            | con_2_A(节点可见)
    con_2_A/F1(/root_A/F0 > 0)           | con_2_A/F1(/root_A/F0 = 100)         | con_2_A/F1(属性可见)
    con_2_A/F2(/root_A/F0 > 0)           | con_2_A/F2(/root_A/F0 = 100)         | con_2_A/F2(属性可见)
    con_2_A/F6(/root_A/con_2_A/F5 = 555) | con_2_A/F6(/root_A/con_2_A/F5 = 100) | con_2_A/F6(属性默认值不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_A", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1_A", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2_A", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilter(g_stmt_async, "root_A", "Yang_066_Func_003_01");

    // 再次merge数据
    /*
    依赖关系                              | 写入数据                             | 预期结果
    root_A/F2(/root_A/F0 = 100)          | root_A/F2(/root_A/F0 = 200)          | root_A/F2(属性不可见)
    root_A/F8(/root_A/F0 = 100)          | root_A/F8(/root_A/F0 = 200)          | root_A/F8(属性默认值不可见)
    root_A/F9(/root_A/F0 = 200)          | root_A/F9(/root_A/F0 = 200)          | root_A/F9(属性默认值可见)
    con_1_A(./F0 = 200)                  | con_1_A(./F0 = 200)                  | con_1_A(节点可见)
    con_2_A(/root_A/F1 = 100)            | con_2_A(/root_A/F1 = 200)            | con_2_A(节点不可见)
    */
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_A", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1_A", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilter(g_stmt_async, "root_A", "Yang_066_Func_003_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

