/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: MustCheckFun.cpp
 * Description:持久化能力上车计算(15_Yang\036_tree_model_support_big_object\SupBigObj.cpp)
 * Author:
 * Create:
 */
#include "tools.h"

class support_big_object : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void support_big_object::SetUpTestCase()
{}

void support_big_object::TearDownTestCase()
{}

void support_big_object::SetUp()
{
    system("rm -rf reply/*");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("rm -rf gmdb");
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=2048\"");
    system("sh $TEST_HOME/tools/start.sh");
    
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AsyncUserDataT data = {0};
    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info,
        NULL, NULL, NULL, NULL, -1, 0, &g_epollData.userEpollFd, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    GmcClearNamespace(stmt, NAMESPACE);
    GmcDropNamespace(stmt, NAMESPACE);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = NAMESPACE;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NAMESPACE_USER_NAME;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmtAsync, NAMESPACE, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, NAMESPACE, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
}

void support_big_object::TearDown()
{
    AW_ADD_ERR_WHITE_LIST(1, "DynCtx reaches mem peak. CtxName: sessionMemCtx");
    AW_CHECK_LOG_END();
    AsyncUserDataT data = {0};
    int ret;

    ret = GmcClearNamespaceAsync(g_stmtAsync, NAMESPACE, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmtAsync, NAMESPACE, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	GmcDetachAllShmSeg();
	testEnvClean();
    system("rm -rf reply/*");
    system("rm -rf gmdb");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 001.insert数据达到10M+10k //Yang_036_001
TEST_F(support_big_object, Compute_009_001_02_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_0字段值
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] // 校验数据");
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        string expect_str_mark_con_0 = "con_0_F" + to_string(i + 1) + ":";
        string expect_str_con_0 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_0);
        ret = expect_value(check_field_con_0, expect_str_con_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        string expect_str_mark_con_1 = "con_1_F" + to_string(i + 1) + ":";
        string expect_str_con_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_1);
        ret = expect_value(check_field_con_1, expect_str_con_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        string expect_str_mark_case_1_1 = "case_1_1_F" + to_string(i + 1) + ":";
        string expect_str_case_1_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_1_1);
        ret = expect_value(check_field_case_1_1, expect_str_case_1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        string expect_str_mark_case_2_1 = "case_2_1_F" + to_string(i + 1) + ":";
        string expect_str_case_2_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_2_1);
        ret = expect_value(check_field_case_2_1, expect_str_case_2_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        string expect_str_mark_list_1 = "list_1_F" + to_string(i + 1) + ":";
        string expect_str_list_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_1);
        ret = expect_value(check_field_list_1, expect_str_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        string expect_str_mark_con_X = "con_X_F" + to_string(i + 1) + ":";
        string expect_str_con_X = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_X);
        ret = expect_value(check_field_con_X, expect_str_con_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        string expect_str_mark_list_2 = "list_2_F" + to_string(i + 1) + ":";
        string expect_str_list_2 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_2);
        ret = expect_value(check_field_list_2, expect_str_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        string expect_str_mark_case_X_1 = "case_X_1_F" + to_string(i + 1) + ":";
        string expect_str_case_X_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_X_1);
        ret = expect_value(check_field_case_X_1, expect_str_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.insert数据超过10M+10k //Yang_036_002
TEST_F(support_big_object, Compute_009_001_02_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(12 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    AW_FUN_Log(LOG_STEP, "[info] 开启事务");
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] vertexlabel2"); // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "[info] vertexlabel3"); // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "[info] 提交事务");
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    AW_FUN_Log(LOG_STEP, "[info]  回滚事务"); // 回滚事务
    ret = GmcTransRollBackAsync(g_connAsync, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    AW_FUN_Log(LOG_STEP, "[info]  开启事务"); // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        ret = expect_value(check_field_con_0, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        ret = expect_value(check_field_con_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_1_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_2_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        ret = expect_value(check_field_list_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        ret = expect_value(check_field_con_X, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        ret = expect_value(check_field_list_2, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_X_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.merge数据达到10M+10k //Yang_036_003
TEST_F(support_big_object, Compute_009_001_02_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    AW_FUN_Log(LOG_STEP, "[info]  开启事务"); // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_MERGE, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_MERGE, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_MERGE, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_MERGE, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_MERGE, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_MERGE, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = list_set_key(stmt_list_1, list_1_F0_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_MERGE, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_MERGE, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = list_set_key(stmt_list_2, list_2_F0_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        string expect_str_mark_con_0 = "con_0_F" + to_string(i + 1) + ":";
        string expect_str_con_0 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_0);
        ret = expect_value(check_field_con_0, expect_str_con_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        string expect_str_mark_con_1 = "con_1_F" + to_string(i + 1) + ":";
        string expect_str_con_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_1);
        ret = expect_value(check_field_con_1, expect_str_con_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        string expect_str_mark_case_1_1 = "case_1_1_F" + to_string(i + 1) + ":";
        string expect_str_case_1_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_1_1);
        ret = expect_value(check_field_case_1_1, expect_str_case_1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        string expect_str_mark_case_2_1 = "case_2_1_F" + to_string(i + 1) + ":";
        string expect_str_case_2_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_2_1);
        ret = expect_value(check_field_case_2_1, expect_str_case_2_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        string expect_str_mark_list_1 = "list_1_F" + to_string(i + 1) + ":";
        string expect_str_list_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_1);
        ret = expect_value(check_field_list_1, expect_str_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        string expect_str_mark_con_X = "con_X_F" + to_string(i + 1) + ":";
        string expect_str_con_X = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_X);
        ret = expect_value(check_field_con_X, expect_str_con_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        string expect_str_mark_list_2 = "list_2_F" + to_string(i + 1) + ":";
        string expect_str_list_2 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_2);
        ret = expect_value(check_field_list_2, expect_str_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        string expect_str_mark_case_X_1 = "case_X_1_F" + to_string(i + 1) + ":";
        string expect_str_case_X_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_X_1);
        ret = expect_value(check_field_case_X_1, expect_str_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.merge数据超过10M+10k //Yang_036_004
TEST_F(support_big_object, Compute_009_001_02_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(12 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_MERGE, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_MERGE, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_MERGE, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_MERGE, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_MERGE, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_MERGE, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = list_set_key(stmt_list_1, list_1_F0_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_MERGE, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_MERGE, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = list_set_key(stmt_list_2, list_2_F0_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    // 回滚事务
    ret = GmcTransRollBackAsync(g_connAsync, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        ret = expect_value(check_field_con_0, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        ret = expect_value(check_field_con_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_1_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_2_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        ret = expect_value(check_field_list_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        ret = expect_value(check_field_con_X, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        ret = expect_value(check_field_list_2, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_X_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
    }
    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.replace数据达到10M+10k //Yang_036_005
TEST_F(support_big_object, Compute_009_001_02_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ===============================================================replace==============================================================
    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option_3;
    GmcBatchT *batch_3 = NULL;
    ret = GmcBatchOptionInit(&batch_option_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_3, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_3, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_3, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_3, &batch_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_4;
    GmcBatchT *batch_4 = NULL;
    ret = GmcBatchOptionInit(&batch_option_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_4, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_4, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_4, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_4, &batch_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t new_con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &new_con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t new_last_str_size = g_fSize;
    string new_last_mark = "new_case_2_1_F" + to_string(g_fNum + 1) + ":";
    string new_last_str = WriteStringToSpecialSize(new_last_str_size, new_last_mark);
    char *new_last_str_value = (char *)new_last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        new_last_str_value,
        new_last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch_3, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t new_list_1_F0_value = i + 100;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &new_list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "new_list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "new_con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch_3, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_4, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_4, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_4, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t new_list_2_F0_value = i + 100;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &new_list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "new_list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "new_case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_4, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch_3, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_3);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_4, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_4);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        string expect_str_mark_con_0 = "new_con_0_F" + to_string(i + 1) + ":";
        string expect_str_con_0 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_0);
        ret = expect_value(check_field_con_0, expect_str_con_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        string expect_str_mark_con_1 = "new_con_1_F" + to_string(i + 1) + ":";
        string expect_str_con_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_1);
        ret = expect_value(check_field_con_1, expect_str_con_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        string expect_str_mark_case_1_1 = "new_case_1_1_F" + to_string(i + 1) + ":";
        string expect_str_case_1_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_1_1);
        ret = expect_value(check_field_case_1_1, expect_str_case_1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        string expect_str_mark_case_2_1 = "new_case_2_1_F" + to_string(i + 1) + ":";
        string expect_str_case_2_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_2_1);
        ret = expect_value(check_field_case_2_1, expect_str_case_2_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        string expect_str_mark_list_1 = "new_list_1_F" + to_string(i + 1) + ":";
        string expect_str_list_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_1);
        ret = expect_value(check_field_list_1, expect_str_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        string expect_str_mark_con_X = "new_con_X_F" + to_string(i + 1) + ":";
        string expect_str_con_X = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_X);
        ret = expect_value(check_field_con_X, expect_str_con_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        string expect_str_mark_list_2 = "new_list_2_F" + to_string(i + 1) + ":";
        string expect_str_list_2 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_2);
        ret = expect_value(check_field_list_2, expect_str_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        string expect_str_mark_case_X_1 = "new_case_X_1_F" + to_string(i + 1) + ":";
        string expect_str_case_X_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_X_1);
        ret = expect_value(check_field_case_X_1, expect_str_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.replace数据超过10M+10k //Yang_036_006
TEST_F(support_big_object, Compute_009_001_02_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ===============================================================replace==============================================================
    SetLabelSize(12 * 1024 * 1024, 256);
    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option_3;
    GmcBatchT *batch_3 = NULL;
    ret = GmcBatchOptionInit(&batch_option_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_3, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_3, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_3, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_3, &batch_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_4;
    GmcBatchT *batch_4 = NULL;
    ret = GmcBatchOptionInit(&batch_option_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_4, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_4, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_4, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_4, &batch_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t new_con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &new_con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "new_case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t new_last_str_size = g_fSize;
    string new_last_mark = "new_case_2_1_F" + to_string(g_fNum + 1) + ":";
    string new_last_str = WriteStringToSpecialSize(new_last_str_size, new_last_mark);
    char *new_last_str_value = (char *)new_last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        new_last_str_value,
        new_last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch_3, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t new_list_1_F0_value = i + 100;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &new_list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "new_list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "new_con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch_3, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_4, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_4, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_4, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t new_list_2_F0_value = i + 100;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &new_list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "new_list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "new_case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_4, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch_3, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
    GmcBatchDestroy(batch_3);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_4, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch_4);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    // 回滚事务
    ret = GmcTransRollBackAsync(g_connAsync, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    SetLabelSize(9 * 1024 * 1024, 256);
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        string expect_str_mark_con_0 = "con_0_F" + to_string(i + 1) + ":";
        string expect_str_con_0 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_0);
        ret = expect_value(check_field_con_0, expect_str_con_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        string expect_str_mark_con_1 = "con_1_F" + to_string(i + 1) + ":";
        string expect_str_con_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_1);
        ret = expect_value(check_field_con_1, expect_str_con_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        string expect_str_mark_case_1_1 = "case_1_1_F" + to_string(i + 1) + ":";
        string expect_str_case_1_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_1_1);
        ret = expect_value(check_field_case_1_1, expect_str_case_1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        string expect_str_mark_case_2_1 = "case_2_1_F" + to_string(i + 1) + ":";
        string expect_str_case_2_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_2_1);
        ret = expect_value(check_field_case_2_1, expect_str_case_2_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        string expect_str_mark_list_1 = "list_1_F" + to_string(i + 1) + ":";
        string expect_str_list_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_1);
        ret = expect_value(check_field_list_1, expect_str_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        string expect_str_mark_con_X = "con_X_F" + to_string(i + 1) + ":";
        string expect_str_con_X = WriteStringToSpecialSize(g_fSize, expect_str_mark_con_X);
        ret = expect_value(check_field_con_X, expect_str_con_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        string expect_str_mark_list_2 = "list_2_F" + to_string(i + 1) + ":";
        string expect_str_list_2 = WriteStringToSpecialSize(g_fSize, expect_str_mark_list_2);
        ret = expect_value(check_field_list_2, expect_str_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        string expect_str_mark_case_X_1 = "case_X_1_F" + to_string(i + 1) + ":";
        string expect_str_case_X_1 = WriteStringToSpecialSize(g_fSize, expect_str_mark_case_X_1);
        ret = expect_value(check_field_case_X_1, expect_str_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.insert数据达到10M+10k 然后delete所有数据 //Yang_036_007
TEST_F(support_big_object, Compute_009_001_02_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option_3;
    GmcBatchT *batch_3 = NULL;
    ret = GmcBatchOptionInit(&batch_option_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_3, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_3, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_3, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_3, &batch_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch_3, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_3);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        ret = expect_value(check_field_con_0, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        ret = expect_value(check_field_con_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_1_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_2_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        ret = expect_value(check_field_list_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        ret = expect_value(check_field_con_X, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        ret = expect_value(check_field_list_2, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_X_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.insert数据达到10M+10k 然后remove所有数据 //Yang_036_008
TEST_F(support_big_object, Compute_009_001_02_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batch_option_2;
    GmcBatchT *batch_2 = NULL;
    ret = GmcBatchOptionInit(&batch_option_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_2, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_2, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_2, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_2, &batch_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(stmt_container_0, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *container_1_node = NULL;
    ret = GmcYangEditChildNode(root, "container_1", GMC_OPERATION_NONE, &container_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *choice_1_node = NULL;
    ret = GmcYangEditChildNode(container_1_node, "choice_1", GMC_OPERATION_NONE, &choice_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *case_1_1_node = NULL;
    ret = GmcYangEditChildNode(choice_1_node, "case_1_1", GMC_OPERATION_NONE, &case_1_1_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch_2, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch_2, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch_2, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcBatchExecuteAsync(batch_2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option_3;
    GmcBatchT *batch_3 = NULL;
    ret = GmcBatchOptionInit(&batch_option_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option_3, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option_3, 30000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option_3, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option_3, &batch_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch_3, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch_3, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch_3);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    for (int i = 0; i < g_fNum; i++) {
        if (g_envType != 0) {
            break;
        }
        // con_0_F*
        string check_field_con_0 = "con_0_F" + to_string(i + 1);
        ret = expect_value(check_field_con_0, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_1_F*
        string check_field_con_1 = "con_1_F" + to_string(i + 1);
        ret = expect_value(check_field_con_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_1_1_F*
        string check_field_case_1_1 = "case_1_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_1_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_2_1_F*
        string check_field_case_2_1 = "case_2_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_2_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_1_F*
        string check_field_list_1 = "list_1_F" + to_string(i + 1);
        ret = expect_value(check_field_list_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // con_X_F*
        string check_field_con_X = "con_X_F" + to_string(i + 1);
        ret = expect_value(check_field_con_X, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // list_2_F*
        string check_field_list_2 = "list_2_F" + to_string(i + 1);
        ret = expect_value(check_field_list_2, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
        // case_X_1_F*
        string check_field_case_X_1 = "case_X_1_F" + to_string(i + 1);
        ret = expect_value(check_field_case_X_1, "");
        AW_MACRO_EXPECT_EQ_INT(-2, ret);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 010.批操作实际值大于batch上限 //Yang_036_010
TEST_F(support_big_object, Compute_009_001_02_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    SetLabelSize(9 * 1024 * 1024, 256);
    AsyncUserDataT data = {0};
    // 建表
    string vertex_label_1_str = WriteVertexLabel1();
    const char *vertex_label_1 = vertex_label_1_str.c_str();
    int ret =
        GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_1, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_2_str = WriteVertexLabel2();
    const char *vertex_label_2 = vertex_label_2_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    string vertex_label_3_str = WriteVertexLabel3();
    const char *vertex_label_3 = vertex_label_3_str.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertex_label_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_2, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, from_1_to_3, LABEL_CONFIG, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 20000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel1
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "vertex_label_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_0字段值
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_container_0, &node_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_0_F0_value = 1;
    ret = yang_set_property(node_container_0,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_0_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_0,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_0_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置container_1字段值
    ret = GmcYangEditChildNode(node_container_0, "container_1", GMC_OPERATION_INSERT, &node_container_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "con_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_container_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "con_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_1节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_1", GMC_OPERATION_INSERT, &node_choice_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_1_1字段值
    ret = GmcYangEditChildNode(node_choice_1, "case_1_1", GMC_OPERATION_INSERT, &node_case_1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_1_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_1_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_1_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置choice_2节点结构
    ret = GmcYangEditChildNode(node_container_1, "choice_2", GMC_OPERATION_INSERT, &node_choice_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case_2_1字段值
    ret = GmcYangEditChildNode(node_choice_2, "case_2_1", GMC_OPERATION_INSERT, &node_case_2_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_fNum; i++) {
        string mark = "case_2_1_F" + to_string(i + 1) + ":";
        string str = WriteStringToSpecialSize(g_fSize, mark);
        char *str_value = (char *)str.c_str();
        ret = yang_set_property(node_case_2_1,
            GMC_DATATYPE_STRING,
            str_value,
            g_fSize,
            "case_2_1_F" + to_string(i + 1),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t last_str_size = g_fSize;
    string last_mark = "case_2_1_F" + to_string(g_fNum + 1) + ":";
    string last_str = WriteStringToSpecialSize(last_str_size, last_mark);
    char *last_str_value = (char *)last_str.c_str();
    ret = yang_set_property(node_case_2_1,
        GMC_DATATYPE_STRING,
        last_str_value,
        last_str_size,
        "case_2_1_F" + to_string(g_fNum + 1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertexlabel2
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_1, "vertex_label_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_1字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_1, &node_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置container_X字段值
        ret = GmcYangEditChildNode(node_list_1, "container_X", GMC_OPERATION_INSERT, &node_container_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_1_F0_value = i + 1;
        ret = yang_set_property(node_list_1,
            GMC_DATATYPE_UINT32,
            &list_1_F0_value,
            sizeof(uint32_t),
            "list_1_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < g_fNum; j++) {
            string mark = "list_1_F" + to_string(j + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_1,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_1_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "con_X_F" + to_string(j + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_container_X,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "con_X_F" + to_string(j + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表2批操作
        ret = GmcBatchAddDML(batch, stmt_list_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // vertexlabel3
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list_2, "vertex_label_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list_2字段值
        ret = GmcYangBindChild(batch, stmt_container_0, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list_2, &node_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置choice_X节点结构
        ret = GmcYangEditChildNode(node_list_2, "choice_X", GMC_OPERATION_INSERT, &node_choice_X);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case_X_1节点结构
        ret = GmcYangEditChildNode(node_choice_X, "case_X_1", GMC_OPERATION_INSERT, &node_case_X_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list主键第二字段
        uint32_t list_2_F0_value = i + 1;
        ret = yang_set_property(node_list_2,
            GMC_DATATYPE_UINT32,
            &list_2_F0_value,
            sizeof(uint32_t),
            "list_2_F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < g_fNum; i++) {
            string mark = "list_2_F" + to_string(i + 1) + ":";
            string str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value = (char *)str.c_str();
            ret = yang_set_property(node_list_2,
                GMC_DATATYPE_STRING,
                str_value,
                g_fSize,
                "list_2_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            mark = "case_X_1_F" + to_string(i + 1) + ":";
            str = WriteStringToSpecialSize(g_fSize, mark);
            char *str_value_X = (char *)str.c_str();
            ret = yang_set_property(node_case_X_1,
                GMC_DATATYPE_STRING,
                str_value_X,
                g_fSize,
                "case_X_1_F" + to_string(i + 1),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 添加表3批操作
        ret = GmcBatchAddDML(batch, stmt_list_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_BATCH_BUFFER_FULL, ret);
    }
    AddWhiteList(GMERR_BATCH_BUFFER_FULL);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 使用接口完成subtree 查询
    GmcNodeT *node_root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "vertex_label_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    con_0_F0_value = 1;
    ret = yang_set_property(node_root,
        GMC_DATATYPE_UINT32,
        &con_0_F0_value,
        sizeof(uint32_t),
        "con_0_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree =  {.obj = node_root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmtAsync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string F1_mark = "con_0_F1:";
    string expect_str = WriteStringToSpecialSize(g_fSize, F1_mark);
    ret = expect_value("con_0_F1", expect_str);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

