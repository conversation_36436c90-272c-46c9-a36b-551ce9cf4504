/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef TREE_H
#define TREE_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <string>
#include <atomic>
#include <vector>
#include <iostream>
#include <cmath>
#include <chrono>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"
#include "jansson.h"

using namespace std;
GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async1 = NULL;
GmcStmtT *g_stmt_async1 = NULL;
GmcStmtT *g_stmt[30] = {0};

const char *LABELCONFIG = "{\"max_record_count\" : 2000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                            "\"yang_model\":1}";
const char *CONTAIN_ROOT_NAME = "root";
// container--container类型的Vertex Name
const char *CONTAIN_CHILD_NAME01 = "P1";
const char *CONTAIN_CHILD_NAME02 = "P2";
const char *CONTAIN_CHILD_NAME03 = "P3";
const char *CONTAIN_CHILD_NAME04 = "P4";
const char *CONTAIN_CHILD_NAME05 = "P5";

GmcTxConfigT g_trxConfig;
GmcTxConfigT g_trxConfig1;

#define MAX_CMD_SIZE 1024
// 格式化断言，用于规避codeClean
#define AW_MACRO_EXPECT_LT_INT(a, b) EXPECT_LT(a, b)

void CreateTree_Container_Container(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/container_container.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, LABELCONFIG, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vertexSchema);
}

void DropTree_container_container(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret;
    ret = GmcClearNamespaceAsync(g_stmt_async, "yang", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void DropUndfineLabelAndNaespace(GmcStmtT *stmt)
{
    GmcUseNamespace(stmt, "yang");
    GmcDropGraphLabel(stmt, "T1");
    GmcDropGraphLabel(stmt, "T2");
    GmcDropGraphLabel(stmt, "T3");
    GmcDropGraphLabel(stmt, "T4");
    GmcDropGraphLabel(stmt, "T5");
    GmcDropGraphLabel(stmt, "root");
    GmcDropNamespace(stmt, "yang");
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void testInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}

int testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool isList = false)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    uint32_t valueF1 = i;
    char valueF2[8] = "string";
    if (!isList) {
        testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
        ret = GmcYangSetNodeProperty(node, &propValue, opType);
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testGmcGetLastError();
            return ret;
        }
    }
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, valueF2, strlen(valueF2));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}

int testBatchExecuteAndWait(GmcBatchT *batch, AsyncUserDataT data, int totalNum, int succNum)
{
    int ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(totalNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(succNum, data.succNum);
    return data.status;
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
        case GMC_DATATYPE_BYTES:
        case GMC_DATATYPE_FIXED:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_BITMAP:
            return "" + to_string(*(const int *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_BOOL:
            return "" + to_string(*(const bool *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        if (0 != strcmp(StrCmp(expectReply[i], res).c_str(), "")) {
            cout << "     >> actual diff: \n" << res;
            cout << "     >> expect diff: \n" << expectReply[i];
        }
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void testFetchAndDeparseDiff(
    GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data, int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

static vector<string> expectDiffCreateBase = {"root:create[(priKey(ID:1)),(NULL)]\n"
                                              "root.F0:create(100)\n"
                                              "root.F1:create(100)\n"
                                              "root.F2:create(string)\n"
                                              "root.P1:create\n"
                                              "P1.F0:create(100)\n"
                                              "P1.F1:create(100)\n"
                                              "P1.F2:create(string)\n"
                                              "P1.A1:create\n"
                                              "A1.F0:create(100)\n"
                                              "A1.F1:create(100)\n"
                                              "A1.F2:create(string)\n"
                                              "P1.A2:create\n"
                                              "A2.F0:create(100)\n"
                                              "A2.F1:create(100)\n"
                                              "A2.F2:create(string)\n"
                                              "root.P2:create\n"
                                              "P2.F0:create(100)\n"
                                              "P2.F1:create(100)\n"
                                              "P2.F2:create(string)\n"
                                              "root.P3:create\n"
                                              "P3.F0:create(100)\n"
                                              "P3.F1:create(100)\n"
                                              "P3.F2:create(string)\n"
                                              "root.P4:create\n"
                                              "P4.F0:create(100)\n"
                                              "P4.F1:create(100)\n"
                                              "P4.F2:create(string)\n"
                                              "root.P5:create\n"
                                              "P5.F0:create(100)\n"
                                              "P5.F1:create(100)\n"
                                              "P5.F2:create(string)\n"};

static vector<string> expectDiffCreateBase_Ext = {"root:create[(priKey(ID:1)),(NULL)]\n"
                                                  "root.F0:create(100)\n"
                                                  "root.F1:create(100)\n"
                                                  "root.F2:create(string)\n"
                                                  "root.P1:create\n"
                                                  "P1.F0:create(100)\n"
                                                  "P1.F1:create(100)\n"
                                                  "P1.F2:create(string)\n"
                                                  "P1.A1:create\n"
                                                  "A1.F0:create(100)\n"
                                                  "A1.F1:create(100)\n"
                                                  "A1.F2:create(string)\n"
                                                  "P1.A2:create\n"
                                                  "A2.F0:create(100)\n"
                                                  "A2.F1:create(100)\n"
                                                  "A2.F2:create(string)\n"
                                                  "root.P2:create\n"
                                                  "P2.F0:create(100)\n"
                                                  "P2.F1:create(100)\n"
                                                  "P2.F2:create(string)\n"
                                                  "root.P3:create\n"
                                                  "P3.F0:create(100)\n"
                                                  "P3.F1:create(100)\n"
                                                  "P3.F2:create(string)\n"
                                                  "root.P4:create\n"
                                                  "P4.F0:create(100)\n"
                                                  "P4.F1:create(100)\n"
                                                  "P4.F2:create(string)\n"
                                                  "root.P5:create\n"
                                                  "P5.F0:create(100)\n"
                                                  "P5.F1:create(100)\n"
                                                  "P5.F2:create(string)\n"};

static vector<string> expectDiffNULLBase = {};

static vector<string> expectDiffBaseList = {"root:create[(priKey(ID:1)),(NULL)]\n"
                                            "root.F0:create(100)\n"
                                            "root.F1:create(100)\n"
                                            "root.F2:create(string)\n"
                                            "root.T0:create\n"
                                            "T0.F0:create(100)\n"
                                            "T0.F1:create(100)\n"
                                            "T0.F2:create(string)\n"
                                            "T0.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                            "T1.ID:create(1)\n"
                                            "T1.F1:create(1)\n"
                                            "T1.F2:create(string)\n"
                                            "T1.P1:create\n"
                                            "P1.F0:create(1)\n"
                                            "P1.F1:create(1)\n"
                                            "P1.F2:create(string)\n"
                                            "T0.T1:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                            "T1.ID:create(2)\n"
                                            "T1.F1:create(2)\n"
                                            "T1.F2:create(string)\n"
                                            "T1.P1:create\n"
                                            "P1.F0:create(2)\n"
                                            "P1.F1:create(2)\n"
                                            "P1.F2:create(string)\n"
                                            "T0.T1:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                            "T1.ID:create(3)\n"
                                            "T1.F1:create(3)\n"
                                            "T1.F2:create(string)\n"
                                            "T1.P1:create\n"
                                            "P1.F0:create(3)\n"
                                            "P1.F1:create(3)\n"
                                            "P1.F2:create(string)\n"
                                            "T0.T1:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                            "T1.ID:create(4)\n"
                                            "T1.F1:create(4)\n"
                                            "T1.F2:create(string)\n"
                                            "T1.P1:create\n"
                                            "P1.F0:create(4)\n"
                                            "P1.F1:create(4)\n"
                                            "P1.F2:create(string)\n"
                                            "T0.T1:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                            "T1.ID:create(5)\n"
                                            "T1.F1:create(5)\n"
                                            "T1.F2:create(string)\n"
                                            "T1.P1:create\n"
                                            "P1.F0:create(5)\n"
                                            "P1.F1:create(5)\n"
                                            "P1.F2:create(string)\n"
                                            "T0.T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                            "T2.ID:create(1)\n"
                                            "T2.F1:create(1)\n"
                                            "T2.F2:create(string)\n"
                                            "T2.P2:create\n"
                                            "P2.F0:create(1)\n"
                                            "P2.F1:create(1)\n"
                                            "P2.F2:create(string)\n"
                                            "T0.T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                            "T2.ID:create(2)\n"
                                            "T2.F1:create(2)\n"
                                            "T2.F2:create(string)\n"
                                            "T2.P2:create\n"
                                            "P2.F0:create(2)\n"
                                            "P2.F1:create(2)\n"
                                            "P2.F2:create(string)\n"
                                            "T0.T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                            "T2.ID:create(3)\n"
                                            "T2.F1:create(3)\n"
                                            "T2.F2:create(string)\n"
                                            "T2.P2:create\n"
                                            "P2.F0:create(3)\n"
                                            "P2.F1:create(3)\n"
                                            "P2.F2:create(string)\n"
                                            "T0.T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                            "T2.ID:create(4)\n"
                                            "T2.F1:create(4)\n"
                                            "T2.F2:create(string)\n"
                                            "T2.P2:create\n"
                                            "P2.F0:create(4)\n"
                                            "P2.F1:create(4)\n"
                                            "P2.F2:create(string)\n"
                                            "T0.T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                            "T2.ID:create(5)\n"
                                            "T2.F1:create(5)\n"
                                            "T2.F2:create(string)\n"
                                            "T2.P2:create\n"
                                            "P2.F0:create(5)\n"
                                            "P2.F1:create(5)\n"
                                            "P2.F2:create(string)\n"
                                            "T0.T3:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                            "T3.ID:create(1)\n"
                                            "T3.F1:create(1)\n"
                                            "T3.F2:create(string)\n"
                                            "T3.P3:create\n"
                                            "P3.F0:create(1)\n"
                                            "P3.F1:create(1)\n"
                                            "P3.F2:create(string)\n"
                                            "T0.T3:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                            "T3.ID:create(2)\n"
                                            "T3.F1:create(2)\n"
                                            "T3.F2:create(string)\n"
                                            "T3.P3:create\n"
                                            "P3.F0:create(2)\n"
                                            "P3.F1:create(2)\n"
                                            "P3.F2:create(string)\n"
                                            "T0.T3:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                            "T3.ID:create(3)\n"
                                            "T3.F1:create(3)\n"
                                            "T3.F2:create(string)\n"
                                            "T3.P3:create\n"
                                            "P3.F0:create(3)\n"
                                            "P3.F1:create(3)\n"
                                            "P3.F2:create(string)\n"
                                            "T0.T3:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                            "T3.ID:create(4)\n"
                                            "T3.F1:create(4)\n"
                                            "T3.F2:create(string)\n"
                                            "T3.P3:create\n"
                                            "P3.F0:create(4)\n"
                                            "P3.F1:create(4)\n"
                                            "P3.F2:create(string)\n"
                                            "T0.T3:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                            "T3.ID:create(5)\n"
                                            "T3.F1:create(5)\n"
                                            "T3.F2:create(string)\n"
                                            "T3.P3:create\n"
                                            "P3.F0:create(5)\n"
                                            "P3.F1:create(5)\n"
                                            "P3.F2:create(string)\n"
                                            "T0.T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                            "T4.ID:create(1)\n"
                                            "T4.F1:create(1)\n"
                                            "T4.F2:create(string)\n"
                                            "T4.P4:create\n"
                                            "P4.F0:create(1)\n"
                                            "P4.F1:create(1)\n"
                                            "P4.F2:create(string)\n"
                                            "T0.T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                            "T4.ID:create(2)\n"
                                            "T4.F1:create(2)\n"
                                            "T4.F2:create(string)\n"
                                            "T4.P4:create\n"
                                            "P4.F0:create(2)\n"
                                            "P4.F1:create(2)\n"
                                            "P4.F2:create(string)\n"
                                            "T0.T4:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                            "T4.ID:create(3)\n"
                                            "T4.F1:create(3)\n"
                                            "T4.F2:create(string)\n"
                                            "T4.P4:create\n"
                                            "P4.F0:create(3)\n"
                                            "P4.F1:create(3)\n"
                                            "P4.F2:create(string)\n"
                                            "T0.T4:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                            "T4.ID:create(4)\n"
                                            "T4.F1:create(4)\n"
                                            "T4.F2:create(string)\n"
                                            "T4.P4:create\n"
                                            "P4.F0:create(4)\n"
                                            "P4.F1:create(4)\n"
                                            "P4.F2:create(string)\n"
                                            "T0.T4:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                            "T4.ID:create(5)\n"
                                            "T4.F1:create(5)\n"
                                            "T4.F2:create(string)\n"
                                            "T4.P4:create\n"
                                            "P4.F0:create(5)\n"
                                            "P4.F1:create(5)\n"
                                            "P4.F2:create(string)\n"
                                            "T0.T5:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                            "T5.ID:create(1)\n"
                                            "T5.F1:create(1)\n"
                                            "T5.F2:create(string)\n"
                                            "T5.P5:create\n"
                                            "P5.F0:create(1)\n"
                                            "P5.F1:create(1)\n"
                                            "P5.F2:create(string)\n"
                                            "T0.T5:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                            "T5.ID:create(2)\n"
                                            "T5.F1:create(2)\n"
                                            "T5.F2:create(string)\n"
                                            "T5.P5:create\n"
                                            "P5.F0:create(2)\n"
                                            "P5.F1:create(2)\n"
                                            "P5.F2:create(string)\n"
                                            "T0.T5:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                            "T5.ID:create(3)\n"
                                            "T5.F1:create(3)\n"
                                            "T5.F2:create(string)\n"
                                            "T5.P5:create\n"
                                            "P5.F0:create(3)\n"
                                            "P5.F1:create(3)\n"
                                            "P5.F2:create(string)\n"
                                            "T0.T5:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                            "T5.ID:create(4)\n"
                                            "T5.F1:create(4)\n"
                                            "T5.F2:create(string)\n"
                                            "T5.P5:create\n"
                                            "P5.F0:create(4)\n"
                                            "P5.F1:create(4)\n"
                                            "P5.F2:create(string)\n"
                                            "T0.T5:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                            "T5.ID:create(5)\n"
                                            "T5.F1:create(5)\n"
                                            "T5.F2:create(string)\n"
                                            "T5.P5:create\n"
                                            "P5.F0:create(5)\n"
                                            "P5.F1:create(5)\n"
                                            "P5.F2:create(string)\n"};

void testYangPresetDataContainerList(
    GmcConnT *conn, GmcStmtT *stmt, GmcBatchT *batch, AsyncUserDataT data, bool isChoice = false)
{
    // 启动事务
    GmcStmtT *stmt_1 = NULL;
    GmcStmtT *stmt_2 = NULL;
    GmcStmtT *stmt_3 = NULL;
    GmcStmtT *stmt_4 = NULL;
    GmcStmtT *stmt_5 = NULL;

    int ret = testTransStartAsync(conn, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, CONTAIN_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isChoice) {
        ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "P1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_2, "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_2, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "P2", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_3, "T3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_3, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "P3", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_4, "T4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_4, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "P4", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_5, "T5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_5, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "P5", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 26, 26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(stmt_1, batch, expectDiffBaseList, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt_2);
    GmcFreeStmt(stmt_3);
    GmcFreeStmt(stmt_4);
    GmcFreeStmt(stmt_5);
}

void testYangPresetDataContainer(GmcConnT *conn, GmcStmtT *stmt, GmcBatchT *batch, AsyncUserDataT data)
{
    // 启动事务
    int ret = testTransStartAsync(conn, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, CONTAIN_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME01, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P2
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME02, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P3
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME03, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P4
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME04, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P5
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME05, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(stmt, batch, expectDiffCreateBase, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
int testYangSetField(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

#endif
