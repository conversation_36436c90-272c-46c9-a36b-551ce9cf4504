/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
 // 持久化能力上车计算(15_Yang\022_TreeSupportDiff\TreeDiffTest.cpp)
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"

#include "tree.h"

class TreeDiffFunctionTest : public testing::Test {
public:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {};

    virtual void SetUp()
    {
        system("${TEST_HOME}/tools/stop.sh -f");
        system("${TEST_HOME}/tools/modifyCfg.sh recover");
        system("rm -rf gmdb");
        char dbFilePath[1024] = {0};
        char *pwdDir = getenv("PWD");
        if (pwdDir == NULL) {
            printf("get env PWD fail.\n");
        }
        (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
        (void)Rmdir(dbFilePath);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));

        system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");  // 增量持久化
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = createEpollOneThread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 创建连接
        ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < 30; i++) {
            ret = GmcAllocStmt(g_conn_async, &g_stmt[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        DropUndfineLabelAndNaespace(g_stmt_sync);
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        AsyncUserDataT data = {0};
        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &yangNspCfg, create_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        ret = GmcUseNamespaceAsync(g_stmt_async, "yang", use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_CHECK_LOG_BEGIN();
        const int errCodeLen = 1024;
        char errorMsg1[errCodeLen] = {0};
        (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 释放句柄
        for (int i = 0; i < 30; i++) {
            GmcFreeStmt(g_stmt[i]);
        }
        // 删除namespace
        AsyncUserDataT data = {0};
        int ret = GmcDropNamespaceAsync(g_stmt_async, "yang", drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        // 释放连接
        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_async, g_stmt_async);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        closeEpollOneThread();
        ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("rm -rf gmdb");
    }
};


/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) container(P1) container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                     container(A1)  container(A2)
*/
// 001 container-container树模型，无数据create操作，查询diff  // Yang_022_TreeDiffFunctionTest_001
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode[40] = {0};
    GmcNodeT *childNode1 = NULL;
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME01, GMC_OPERATION_INSERT, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode[1], 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode[1], "A1", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode[1], "A2", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P2
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME02, GMC_OPERATION_INSERT, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode[2], 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P3
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME03, GMC_OPERATION_INSERT, &childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode[3], 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P4
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME04, GMC_OPERATION_INSERT, &childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode[4], 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P5
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME05, GMC_OPERATION_INSERT, &childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode[5], 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt[1], batch, expectDiffCreateBase, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt[1], expectDiffCreateBase_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}


static vector<string> expectDiffBase04 = {
    "root:remove[(NULL),(priKey(ID:1))]\n"
    "root.F0:remove(100)\n"
    "root.F1:remove(100)\n"
    "root.F2:remove(string)\n"
    "root.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.A1:remove\n"
    "A1.F0:remove(100)\n"
    "A1.F1:remove(100)\n"
    "A1.F2:remove(string)\n"
    "P1.A2:remove\n"
    "A2.F0:remove(100)\n"
    "A2.F1:remove(100)\n"
    "A2.F2:remove(string)\n"
    "root.P2:remove\n"
    "P2.F0:remove(100)\n"
    "P2.F1:remove(100)\n"
    "P2.F2:remove(string)\n"
    "root.P3:remove\n"
    "P3.F0:remove(100)\n"
    "P3.F1:remove(100)\n"
    "P3.F2:remove(string)\n"
    "root.P4:remove\n"
    "P4.F0:remove(100)\n"
    "P4.F1:remove(100)\n"
    "P4.F2:remove(string)\n"
    "root.P5:remove\n"
    "P5.F0:remove(100)\n"
    "P5.F1:remove(100)\n"
    "P5.F2:remove(string)\n"
};


static vector<string> expectDiffBase04_Ext = {
    "root:remove[(NULL),(priKey(ID:1))]\n"
    "root.F0:remove(100)\n"
    "root.F1:remove(100)\n"
    "root.F2:remove(string)\n"
    "root.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.A1:remove\n"
    "A1.F0:remove(100)\n"
    "A1.F1:remove(100)\n"
    "A1.F2:remove(string)\n"
    "P1.A2:remove\n"
    "A2.F0:remove(100)\n"
    "A2.F1:remove(100)\n"
    "A2.F2:remove(string)\n"
    "root.P2:remove\n"
    "P2.F0:remove(100)\n"
    "P2.F1:remove(100)\n"
    "P2.F2:remove(string)\n"
    "root.P3:remove\n"
    "P3.F0:remove(100)\n"
    "P3.F1:remove(100)\n"
    "P3.F2:remove(string)\n"
    "root.P4:remove\n"
    "P4.F0:remove(100)\n"
    "P4.F1:remove(100)\n"
    "P4.F2:remove(string)\n"
    "root.P5:remove\n"
    "P5.F0:remove(100)\n"
    "P5.F1:remove(100)\n"
    "P5.F2:remove(string)\n"
};

// 004 container-container树模型，预置数据remove根节点，查询diff  //Yang_022_TreeDiffFunctionTest_004
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase04, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase04_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 container-container树模型，预置数据delete根节点，查询diff //Yang_022_TreeDiffFunctionTest_005
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase04, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase04_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

static vector<string> expectDiffBase06 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.A1:remove\n"
    "A1.F0:remove(100)\n"
    "A1.F1:remove(100)\n"
    "A1.F2:remove(string)\n"
    "P1.A2:remove\n"
    "A2.F0:remove(100)\n"
    "A2.F1:remove(100)\n"
    "A2.F2:remove(string)\n"
};


static vector<string> expectDiffBase06_Ext = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.A1:remove\n"
    "A1.F0:remove(100)\n"
    "A1.F1:remove(100)\n"
    "A1.F2:remove(string)\n"
    "P1.A2:remove\n"
    "A2.F0:remove(100)\n"
    "A2.F1:remove(100)\n"
    "A2.F2:remove(string)\n"
};

// 006 container-container树模型，预置数据remove node节点的container，查询diff  //Yang_022_TreeDiffFunctionTest_006
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *childNode = NULL;
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME01, GMC_OPERATION_REMOVE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    ret = testBatchExecuteAndWait(batch, data, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase06, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase06_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 container-container树模型，预置数据delete node节点，查询diff //Yang_022_TreeDiffFunctionTest_007
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *childNode = NULL;
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME01, GMC_OPERATION_DELETE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    ret = testBatchExecuteAndWait(batch, data, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase06, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase06_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

static vector<string> expectDiffBase08 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.F0:update(200,100)\n"
    "root.F1:update(200,100)\n"
    "root.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.A1:remove\n"
    "A1.F0:remove(100)\n"
    "A1.F1:remove(100)\n"
    "A1.F2:remove(string)\n"
    "P1.A2:remove\n"
    "A2.F0:remove(100)\n"
    "A2.F1:remove(100)\n"
    "A2.F2:remove(string)\n"
    "root.P2:remove\n"
    "P2.F0:remove(100)\n"
    "P2.F1:remove(100)\n"
    "P2.F2:remove(string)\n"
    "root.P3:remove\n"
    "P3.F0:remove(100)\n"
    "P3.F1:remove(100)\n"
    "P3.F2:remove(string)\n"
    "root.P4:remove\n"
    "P4.F0:remove(100)\n"
    "P4.F1:remove(100)\n"
    "P4.F2:remove(string)\n"
    "root.P5:remove\n"
    "P5.F0:remove(100)\n"
    "P5.F1:remove(100)\n"
    "P5.F2:remove(string)\n"
};


static vector<string> expectDiffBase08_Ext = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.F0:update(200,100)\n"
    "root.F1:update(200,100)\n"
    "root.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.A1:remove\n"
    "A1.F0:remove(100)\n"
    "A1.F1:remove(100)\n"
    "A1.F2:remove(string)\n"
    "P1.A2:remove\n"
    "A2.F0:remove(100)\n"
    "A2.F1:remove(100)\n"
    "A2.F2:remove(string)\n"
    "root.P2:remove\n"
    "P2.F0:remove(100)\n"
    "P2.F1:remove(100)\n"
    "P2.F2:remove(string)\n"
    "root.P3:remove\n"
    "P3.F0:remove(100)\n"
    "P3.F1:remove(100)\n"
    "P3.F2:remove(string)\n"
    "root.P4:remove\n"
    "P4.F0:remove(100)\n"
    "P4.F1:remove(100)\n"
    "P4.F2:remove(string)\n"
    "root.P5:remove\n"
    "P5.F0:remove(100)\n"
    "P5.F1:remove(100)\n"
    "P5.F2:remove(string)\n"
};

// 008 container-container树模型，预置数据replace根节点并更新部分字段数据，查询diff  //Yang_022_TreeDiffFunctionTest_008
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase08, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase08_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}
static vector<string> expectDiffBase09 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.F0:update(200,100)\n"
    "root.F1:update(200,100)\n"
};
static vector<string> expectDiffBase09_Ext = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.F0:update(200,100)\n"
    "root.F1:update(200,100)\n"
};

// 009 container-container树模型，预置数据merge根节点并更新部分字段数据数据，查询diff  //Yang_022_TreeDiffFunctionTest_009
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt[1], batch, expectDiffBase09, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt[1], expectDiffBase09_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 container-container树模型，预置数据merge根节点字段数据数据不变，查询diff //Yang_022_TreeDiffFunctionTest_010
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1003000", "GMERR-1004000");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt[1], batch, expectDiffNULLBase, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt[1], expectDiffNULLBase, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expectDiff = &expectDiffNULLBase;
    ret = GmcYangFetchDiffExecuteAsync(g_stmt_async, NULL, FetchDiff_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}


static vector<string> expectDiffBase014 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.P1:update\n"
    "P1.F0:update(200,100)\n"
    "P1.F1:update(200,100)\n"
    "root.P2:update\n"
    "P2.F0:update(300,100)\n"
    "P2.F1:update(300,100)\n"
    "root.P3:remove\n"
    "P3.F0:remove(100)\n"
    "P3.F1:remove(100)\n"
    "P3.F2:remove(string)\n"
    "root.P4:remove\n"
    "P4.F0:remove(100)\n"
    "P4.F1:remove(100)\n"
    "P4.F2:remove(string)\n"
};


static vector<string> expectDiffBase014_Ext = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.P1:update\n"
    "P1.F0:update(200,100)\n"
    "P1.F1:update(200,100)\n"
    "root.P2:update\n"
    "P2.F0:update(300,100)\n"
    "P2.F1:update(300,100)\n"
    "root.P3:remove\n"
    "P3.F0:remove(100)\n"
    "P3.F1:remove(100)\n"
    "P3.F2:remove(string)\n"
    "root.P4:remove\n"
    "P4.F0:remove(100)\n"
    "P4.F1:remove(100)\n"
    "P4.F2:remove(string)\n"
};


// 014 container-container树模型，不同六原语操作多个节点，查询diff // Yang_022_TreeDiffFunctionTest_014
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *childNode[5] = {0};
    // 设置node节点P1，merge
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME01, GMC_OPERATION_MERGE, &childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(childNode[0], 200, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P2，replace
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME02, GMC_OPERATION_REPLACE_GRAPH, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(childNode[1], 300, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P3，delete
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME03, GMC_OPERATION_DELETE_GRAPH, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P4，remove
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME04, GMC_OPERATION_REMOVE_GRAPH, &childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase014, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase014_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 container-container树模型，不同六原语操作多个节点，回滚后查询diff  // Yang_022_TreeDiffFunctionTest_015
TEST_F(TreeDiffFunctionTest, Compute_009_001_02_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcBatchT *batch = NULL;

    // 建表
    CreateTree_Container_Container(g_stmt_async, data);
    // 预置数据
    testYangPresetDataContainer(g_conn_async, g_stmt_async, batch, data);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CONTAIN_ROOT_NAME, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *childNode[5] = {0};
    // 设置node节点P1，merge
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME01, GMC_OPERATION_MERGE, &childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(childNode[0], 200, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P2，replace
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME02, GMC_OPERATION_REPLACE_GRAPH, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(childNode[1], 300, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P3，delete
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME03, GMC_OPERATION_DELETE_GRAPH, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P4，remove
    ret = GmcYangEditChildNode(rootNode, CONTAIN_CHILD_NAME04, GMC_OPERATION_REMOVE_GRAPH, &childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交执行
    testBatchExecuteAndWait(batch, data, 1, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_async, batch, expectDiffBase014, data);
    TestYangFetchDiffExtExecuteAsync(g_stmt_async, expectDiffBase014_Ext, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expectDiff = &expectDiffNULLBase;
    ret = GmcYangFetchDiffExecuteAsync(g_stmt_async, NULL, FetchDiff_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    DropTree_container_container(g_stmt_async, data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

