/*****************************************************************************
 Description  : PATH订阅推送
 Notes        : 本文件用例测试FIB表的PATH推送：#7->#8->#9->#10->#42
SN_010_023 订阅#8，其余表预制数据，最后写#8，commit触发推送，删除#8触发推送
SN_010_024 订阅#8，其余表预制数据，最后写#8，删除#8，再写#8，commit触发删除推送和插入推送
SN_010_025 订阅#8，写#8,其余表不写数据,预期不推送(path不完整)
SN_010_026 订阅#8，其余表预制数据，#8写的数据与#8无关联，预期不推送(path不完整)
SN_010_027 订阅#8，五个表按顺序预制数据，commit后预期推送
SN_010_028
订阅#8，五个表按顺序预制数据，最后#8删除数据，commit后预期删除推送,插入不推送（insert是before_commit类型，最后commit的时候数据已被删除，无法推送）
SN_010_029 条件插入订阅#8，全表删除订阅#8，其余表预制数据
SN_010_030 条件插入订阅#8，条件删除订阅#8，其余表预制数据
SN_010_031 订阅#8,subjson主键字段冗余
SN_010_032 订阅#8，其余表预制数据，最后批量写#8，commit触发推送，批量删除#8触发推送
SN_010_033 订阅#42，其余表预制数据，最后写#42，commit触发推送，删除#42触发推送
SN_010_034 订阅#42，其余表预制数据，最后写#42，删除#42，再写#42，commit触发删除推送和插入推送
SN_010_035 订阅#42，五个表按顺序预制数据，commit后预期推送
SN_010_036
订阅#42，五个表按顺序预制数据，最后#42删除数据，commit后预期删除推送,插入不推送（insert是before_commit类型，最后commit的时候数据已被删除，无法推送）
SN_010_037 条件插入订阅#42，全表删除订阅#42，其余表预制数据
SN_010_038 条件插入订阅#42，条件删除订阅#42，其余表预制数据
SN_010_039 订阅#42，vrtxLabelIdx超过推送表的数量时，操作正常
SN_010_040 订阅#42，其余表预制数据，最后批量写#42，commit触发推送，批量删除#42触发推送
SN_010_041 订阅#10，其余表预制数据，最后异步写#10，commit触发推送，删除#10触发推送
SN_010_042 订阅#7、#9、#42，3条PATH，写完一起commit
SN_010_043 订阅#7、#9、#42，3条PATH，写数据分批commit,循环多次
SN_010_057 多个线程创建订阅接口表的整表变更数据（包括新增、删除），然后再起多个线程做DML操作(1个线程写完1条PATH)
SN_010_060 多个线程并发对多个表同时进行订阅推送
SN_010_061 多个线程创建订阅接口表的整表变更数据（包括新增、删除），然后再起多个线程做异步DML操作(1个线程写完1条PATH)
SN_010_062
多个线程创建订阅接口表的整表变更数据（包括新增、删除），然后再起多个线程做DML操作(分2个线程写完1条PATH，线程1同步，线程2异步)
 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2020/12/11
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "sub_tools.h"

const char *g_subName_7_insert = "subPath_7_insert";
const char *g_subName_7_delete = "subPath_7_delete";
const char *g_subName_8_insert = "subPath_8_insert";
const char *g_subName_8_delete = "subPath_8_delete";
const char *g_subName_9_insert = "subPath_9_insert";
const char *g_subName_9_delete = "subPath_9_delete";
const char *g_subName_10_insert = "subPath_10_insert";
const char *g_subName_10_delete = "subPath_10_delete";
const char *g_subName_42_insert = "subPath_42_insert";
const char *g_subName_42_delete = "subPath_42_delete";
const char *g_subConnName = "subConnName";

char vertexLabelNameT7[] = "ip4forward";
char vertexLabelNameT8[] = "nhp_group";
char vertexLabelNameT9[] = "nhp_group_node";
char vertexLabelNameT10[] = "nhp";
char vertexLabelNameT42[] = "nhp_std";

char lableT7_PK_name[] = "ip4_key";
char lableT8_PK_name[] = "primary_key";
char lableT9_PK_name[] = "primary_key";
char lableT10_PK_name[] = "primary_key";
char lableT42_PK_name[] = "primary_key";

char edgeLabelName_7_8[] = "from_7_to_8";
char edgeLabelName_8_9[] = "from_8_to_9";
char edgeLabelName_9_10[] = "from_9_to_10";
char edgeLabelName_10_42[] = "from_10_to_42";

const char *vertexLabelCfg = R"({"max_record_count":1000})";
const char *edgeLabelCfg = (char *)"{\"max_record_count\":1000}";

char *vertexLabelJsonT7 = NULL;
char *vertexLabelJsonT8 = NULL;
char *vertexLabelJsonT9 = NULL;
char *vertexLabelJsonT10 = NULL;
char *vertexLabelJsonT42 = NULL;
char *edgeLabelJson_7_8 = NULL;
char *edgeLabelJson_8_9 = NULL;
char *edgeLabelJson_9_10 = NULL;
char *edgeLabelJson_10_42 = NULL;
void *labelT7 = NULL;
void *labelT8 = NULL;
void *labelT9 = NULL;
void *labelT10 = NULL;
void *labelT42 = NULL;
int g_thread_wait = 0;
int g_thr_num_sn = 10;
int g_thr_num_dml = 1;  //改成2以上加事务会涉及双向建边，会死锁，写的很慢

using namespace std;

class PATHSubscriptionAndPush : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    virtual void SetUp();
    virtual void TearDown();
};

void PATHSubscriptionAndPush::SetUp()
{
    printf("PATHSubscriptionAndPush Start.\n");
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info_insert = NULL;
    g_sub_info_delete = NULL;
    int ret;
    int chanRingLen = 256;

    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅连接
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //创建#7
    readJanssonFile("schema_file/ip4forward.gmjson", &vertexLabelJsonT7);
    EXPECT_NE((void *)NULL, vertexLabelJsonT7);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT7, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#8
    readJanssonFile("schema_file/nhp_group.gmjson", &vertexLabelJsonT8);
    EXPECT_NE((void *)NULL, vertexLabelJsonT8);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT8, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#9
    readJanssonFile("schema_file/nhp_group_node.gmjson", &vertexLabelJsonT9);
    EXPECT_NE((void *)NULL, vertexLabelJsonT9);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT9, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#10
    readJanssonFile("schema_file/nhp.gmjson", &vertexLabelJsonT10);
    EXPECT_NE((void *)NULL, vertexLabelJsonT10);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT10, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#42
    readJanssonFile("schema_file/nhp_std.gmjson", &vertexLabelJsonT42);
    EXPECT_NE((void *)NULL, vertexLabelJsonT42);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT42, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_7_8
    readJanssonFile("schema_file/edge_7_8.gmjson", &edgeLabelJson_7_8);
    EXPECT_NE((void *)NULL, edgeLabelJson_7_8);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_7_8, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_8_9
    readJanssonFile("schema_file/edge_8_9.gmjson", &edgeLabelJson_8_9);
    EXPECT_NE((void *)NULL, edgeLabelJson_8_9);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_8_9, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_9_10
    readJanssonFile("schema_file/edge_9_10.gmjson", &edgeLabelJson_9_10);
    EXPECT_NE((void *)NULL, edgeLabelJson_9_10);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_9_10, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_10_42
    readJanssonFile("schema_file/edge_10_42.gmjson", &edgeLabelJson_10_42);
    EXPECT_NE((void *)NULL, edgeLabelJson_10_42);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_10_42, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);

    g_specialDisConnect = 0;
    AW_CHECK_LOG_BEGIN();
}
void PATHSubscriptionAndPush::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;

    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_7_8);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_8_9);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_9_10);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_10_42);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    if (!g_specialDisConnect) {
        test_close_and_drop_label(g_stmt_sync, labelT7, vertexLabelNameT7);
        test_close_and_drop_label(g_stmt_sync, labelT8, vertexLabelNameT8);
        test_close_and_drop_label(g_stmt_sync, labelT9, vertexLabelNameT9);
        test_close_and_drop_label(g_stmt_sync, labelT10, vertexLabelNameT10);
        test_close_and_drop_label(g_stmt_sync, labelT42, vertexLabelNameT42);
    } else {
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT7);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT9);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT10);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT42);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //断开订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //断开同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_sub_info_insert);
    free(g_sub_info_delete);
    free(vertexLabelJsonT7);
    free(vertexLabelJsonT8);
    free(vertexLabelJsonT9);
    free(vertexLabelJsonT10);
    free(vertexLabelJsonT42);
    free(edgeLabelJson_7_8);
    free(edgeLabelJson_8_9);
    free(edgeLabelJson_9_10);
    free(edgeLabelJson_10_42);
    testSnFreeUserData(user_data);
    printf("PATHSubscriptionAndPush End.\n");
}

//订阅#8，其余表预制数据，最后写#8，commit触发推送，删除#8触发推送
TEST_F(PATHSubscriptionAndPush, SN_010_023)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#8，其余表预制数据，最后写#8，删除#8，再写#8，commit触发删除推送和插入推送
TEST_F(PATHSubscriptionAndPush, SN_010_024)
{
    int ret, i;
    int userDataIdx = 0;
    g_data_num = 50;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num * 2);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num * 2);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num * 2);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num * 2);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    for (i = 0; i < g_data_num; i++) {
        //准备好第一次插入推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        //准备好第二次插入推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num * 2);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num * 3);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
    g_data_num = 100;
}

//订阅#8，写#8,其余表不写数据,预期不推送(path不完整)
TEST_F(PATHSubscriptionAndPush, SN_010_025)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 0);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);

    g_specialDisConnect = 1;
}

//订阅#8，其余表预制数据，#8写的数据与#8无关联，预期不推送(path不完整)
TEST_F(PATHSubscriptionAndPush, SN_010_026)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num; i < g_data_num * 2; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, g_data_num, g_data_num * 2);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 0);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#8，五个表按顺序预制数据，commit后预期推送
TEST_F(PATHSubscriptionAndPush, SN_010_027)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#8，五个表按顺序预制数据，最后#8删除数据，commit后预期删除推送,插入不推送（insert是before_commit类型，最后commit的时候数据已被删除，无法推送）
TEST_F(PATHSubscriptionAndPush, SN_010_028)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

bool isMatchPushCond_vr_id(int value)
{
    return (value == 1);
}

//条件插入订阅#8，全表删除订阅#8，其余表预制数据
TEST_F(PATHSubscriptionAndPush, SN_010_029)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert_cond_vr_id.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond_vr_id(i)) {
            //准备好推送的上下文
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;
        }

        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//条件插入订阅#8，条件删除订阅#8，其余表预制数据
TEST_F(PATHSubscriptionAndPush, SN_010_030)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert_cond_vr_id.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete_cond_vr_id.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond_vr_id(i)) {
            //准备好推送的上下文
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;
        }

        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond_vr_id(i)) {
            printf("[INFO] delete i = %d\r\n", i);
            //准备好推送的上下文
            ((int *)(user_data->old_value))[userDataIdx] = i;
            userDataIdx++;
        }

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#8,subjson主键字段冗余
TEST_F(PATHSubscriptionAndPush, SN_010_031)
{
    int ret, i;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert_wr01.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    g_specialDisConnect = 1;
}

//订阅#8，其余表预制数据，最后批量写#8，commit触发推送，批量删除#8触发推送
TEST_F(PATHSubscriptionAndPush, SN_010_032)
{
    int ret, i;
    int userDataIdx = 0;
    int start_num = 0;
    int end_num = g_data_num;
    readJanssonFile("schema_file/fib_path_8_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_8_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#8的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_8_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#8的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_8_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //批量写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //批量删除#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_8_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#42，其余表预制数据，最后写#42，commit触发推送，删除#42触发推送
TEST_F(PATHSubscriptionAndPush, SN_010_033)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#42，其余表预制数据，最后写#42，删除#42，再写#42，commit触发删除推送和插入推送
TEST_F(PATHSubscriptionAndPush, SN_010_034)
{
    int ret, i;
    int userDataIdx = 0;
    g_data_num = 50;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num * 2);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num * 2);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num * 2);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num * 2);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    for (i = 0; i < g_data_num; i++) {
        //准备好第一次插入推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * 2; i++) {
        //准备好第二次插入推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num * 2);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num * 3);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
    g_data_num = 100;
}

//订阅#42，五个表按顺序预制数据，commit后预期推送
TEST_F(PATHSubscriptionAndPush, SN_010_035)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#42，五个表按顺序预制数据，最后#42删除数据，commit后预期删除推送,插入不推送（insert是before_commit类型，最后commit的时候数据已被删除，无法推送）
TEST_F(PATHSubscriptionAndPush, SN_010_036)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//条件插入订阅#42，全表删除订阅#42，其余表预制数据
TEST_F(PATHSubscriptionAndPush, SN_010_037)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert_cond_vr_id.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond_vr_id(i)) {
            //准备好推送的上下文
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;
        }

        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//条件插入订阅#42，条件删除订阅#42，其余表预制数据
TEST_F(PATHSubscriptionAndPush, SN_010_038)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert_cond_vr_id.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete_cond_vr_id.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond_vr_id(i)) {
            //准备好推送的上下文
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;
        }

        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond_vr_id(i)) {
            printf("[INFO] delete i = %d\r\n", i);
            //准备好推送的上下文
            ((int *)(user_data->old_value))[userDataIdx] = i;
            userDataIdx++;
        }

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback_01(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    printf("[INFO] info->labelCount is %d\r\n", info->labelCount);

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        char tmplabelName[] = "nhp_std";
        memcpy(labelName, tmplabelName, sizeof(tmplabelName));
        pk = ((int *)user_data->new_value)[user_data->subIndex];
        test_checkVertexProperty_42(subStmt, pk);
        printf("[INFO] check insert push #42\r\n");

        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }

    for (i = info->labelCount; i < info->labelCount * 2; i++) {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
        printf("[INFO] vrtxLabelIdx is %d : labelName is %s, labelNameLen is %d\r\n", i, labelName, labelNameLen);
    }
}

//订阅#42，vrtxLabelIdx超过推送表的数量时，操作正常
TEST_F(PATHSubscriptionAndPush, SN_010_039)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_01, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#42，其余表预制数据，最后批量写#42，commit触发推送，批量删除#42触发推送
TEST_F(PATHSubscriptionAndPush, SN_010_040)
{
    int ret, i;
    int userDataIdx = 0;
    int start_num = 0;
    int end_num = g_data_num;
    readJanssonFile("schema_file/fib_path_42_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#42的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_42_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_42_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //批量写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //批量删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#10，其余表预制数据，最后写#10，commit触发推送，删除#10触发推送
TEST_F(PATHSubscriptionAndPush, SN_010_041)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_10_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);
    readJanssonFile("schema_file/fib_path_10_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    //订阅#10的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_10_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#10的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_10_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    //全表扫描#7
    // test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    //全表扫描#8
    // test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    //全表扫描#9
    // test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    //全表扫描#42
    // test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_10(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
    //全表扫描#10
    // test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT10, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, lableT10_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_10_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_10_delete);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅#7、#9、#42，3条PATH，写完一起commit
TEST_F(PATHSubscriptionAndPush, SN_010_042)
{
    int ret, i;
    int userDataIdx = 0;
    char *sub_info_insert_7 = 0, *sub_info_insert_9 = 0, *sub_info_delete_42 = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &sub_info_insert_7);
    EXPECT_NE((void *)NULL, sub_info_insert_7);
    readJanssonFile("schema_file/fib_path_9_subinfo_insert.gmjson", &sub_info_insert_9);
    EXPECT_NE((void *)NULL, sub_info_insert_9);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &sub_info_delete_42);
    EXPECT_NE((void *)NULL, sub_info_delete_42);

    //订阅#7的insert
    GmcSubConfigT tmp_sub_info_insert_7;
    tmp_sub_info_insert_7.subsName = g_subName_7_insert;
    tmp_sub_info_insert_7.configJson = sub_info_insert_7;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info_insert_7, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#9的insert
    GmcSubConfigT tmp_sub_info_insert_9;
    tmp_sub_info_insert_9.subsName = g_subName_9_insert;
    tmp_sub_info_insert_9.configJson = sub_info_insert_9;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info_insert_9, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#42的delete
    GmcSubConfigT tmp_sub_info_delete_42;
    tmp_sub_info_delete_42.subsName = g_subName_42_delete;
    tmp_sub_info_delete_42.configJson = sub_info_delete_42;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info_delete_42, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        usleep(1000);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num * 2);
    EXPECT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_7_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_9_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info_insert_7);
    free(sub_info_insert_9);
    free(sub_info_delete_42);
}

//订阅#7、#9、#42，3条PATH，写数据分批commit,循环多次
TEST_F(PATHSubscriptionAndPush, SN_010_043)
{
    int ret, i;
    int userDataIdx = 0;
    char *sub_info_insert_7 = 0, *sub_info_insert_9 = 0, *sub_info_delete_42 = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &sub_info_insert_7);
    EXPECT_NE((void *)NULL, sub_info_insert_7);
    readJanssonFile("schema_file/fib_path_9_subinfo_insert.gmjson", &sub_info_insert_9);
    EXPECT_NE((void *)NULL, sub_info_insert_9);
    readJanssonFile("schema_file/fib_path_42_subinfo_delete.gmjson", &sub_info_delete_42);
    EXPECT_NE((void *)NULL, sub_info_delete_42);

    for (int j = 0; j < 10; j++) {
        //订阅#7的insert
        GmcSubConfigT tmp_sub_info_insert_7;
        tmp_sub_info_insert_7.subsName = g_subName_7_insert;
        tmp_sub_info_insert_7.configJson = sub_info_insert_7;
        ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info_insert_7, g_conn_sub, sn_callback, user_data);
        EXPECT_EQ(GMERR_OK, ret);
        //订阅#9的insert
        GmcSubConfigT tmp_sub_info_insert_9;
        tmp_sub_info_insert_9.subsName = g_subName_9_insert;
        tmp_sub_info_insert_9.configJson = sub_info_insert_9;
        ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info_insert_9, g_conn_sub, sn_callback, user_data);
        EXPECT_EQ(GMERR_OK, ret);
        //订阅#42的delete
        GmcSubConfigT tmp_sub_info_delete_42;
        tmp_sub_info_delete_42.subsName = g_subName_42_delete;
        tmp_sub_info_delete_42.configJson = sub_info_delete_42;
        ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info_delete_42, g_conn_sub, sn_callback, user_data);
        EXPECT_EQ(GMERR_OK, ret);

        printf("[INFO] j = %d\r\n", j);
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        EXPECT_EQ(GMERR_OK, ret);

        //写#8的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            test_setVertexProperty_8(g_stmt_sync, i);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        //全表扫描#8
        // test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

        ret = GmcTransCommit(g_conn_sync);
        EXPECT_EQ(GMERR_OK, ret);

        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        EXPECT_EQ(GMERR_OK, ret);

        //写#9的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            test_setVertexProperty_9(g_stmt_sync, i);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        //全表扫描#9
        // test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

        ret = GmcTransCommit(g_conn_sync);
        EXPECT_EQ(GMERR_OK, ret);

        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        EXPECT_EQ(GMERR_OK, ret);

        //写#10的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            test_setVertexProperty_10(g_stmt_sync, i);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        //全表扫描#10
        // test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

        ret = GmcTransCommit(g_conn_sync);
        EXPECT_EQ(GMERR_OK, ret);

        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        EXPECT_EQ(GMERR_OK, ret);

        //写#42的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            test_setVertexProperty_42(g_stmt_sync, i);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        //全表扫描#42
        // test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

        ret = GmcTransCommit(g_conn_sync);
        EXPECT_EQ(GMERR_OK, ret);

        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        EXPECT_EQ(GMERR_OK, ret);

        //写#7的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            //准备好推送的上下文
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;

            test_setVertexProperty_7(g_stmt_sync, i);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        //全表扫描#7
        // test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

        ret = GmcTransCommit(g_conn_sync);
        EXPECT_EQ(GMERR_OK, ret);

        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        EXPECT_EQ(GMERR_OK, ret);

        //删除#42的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            //准备好推送的上下文
            ((int *)(user_data->old_value))[userDataIdx] = i;
            userDataIdx++;

            uint32_t value_u32 = i;
            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(g_stmt_sync, lableT42_PK_name);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_scan_42(g_stmt_sync, labelT42, 0, 0);

        ret = GmcTransCommit(g_conn_sync);
        EXPECT_EQ(GMERR_OK, ret);

        //删除#7的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {

            uint32_t value_u32 = i;
            uint8_t value_u8 = i;
            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_scan_7(g_stmt_sync, labelT7, 0, 0);

        //删除#8的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            uint32_t value_u32 = i;
            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(g_stmt_sync, lableT8_PK_name);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_scan_8(g_stmt_sync, labelT8, 0, 0);

        //删除#9的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            uint32_t value_u32 = i;
            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 4, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 5, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 6, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(g_stmt_sync, lableT9_PK_name);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_scan_9(g_stmt_sync, labelT9, 0, 0);

        //删除#10的数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = 0; i < g_data_num; i++) {
            uint32_t value_u32 = i;
            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(g_stmt_sync, lableT10_PK_name);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        test_scan_10(g_stmt_sync, labelT10, 0, 0);

        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
        EXPECT_EQ(0, ret);

        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
        EXPECT_EQ(0, ret);

        ret = GmcUnSubscribe(g_stmt_sync, g_subName_7_insert);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(g_stmt_sync, g_subName_9_insert);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(g_stmt_sync, g_subName_42_delete);
        EXPECT_EQ(GMERR_OK, ret);

        testSnFreeUserData(user_data);
        ret = testSnMallocUserData(&user_data, g_data_num * 10);
        EXPECT_EQ(GMERR_OK, ret);

        userDataIdx = 0;
    }
    free(sub_info_insert_7);
    free(sub_info_insert_9);
    free(sub_info_delete_42);
}

void *thread_sn(void *args)
{
    int ret;
    int id = *((int *)args);
    GmcStmtT *stmt = 0, *stmt_sub = 0;
    GmcConnT *conn = 0, *conn_sub = 0;
    char subConnName[128] = "subConnName";
    char subName_insert[128] = "subPath_7_insert";
    char *sub_info_insert = NULL;
    char sub_path_insert[512] = {0};
    char subName_delete[128] = "subPath_7_delete";
    char *sub_info_delete = NULL;
    char sub_path_delete[512] = {0};

    sprintf(subName_insert, "subPath_7_insert_%d", id);
    sprintf(subConnName, "subConnName_%d", id);

    sprintf(sub_path_insert, "./multi_sub_7_insert/fib_path_7_subinfo_insert_%d.gmjson", id);
    readJanssonFile(sub_path_insert, &sub_info_insert);
    EXPECT_NE((void *)NULL, sub_info_insert);

    sprintf(subName_delete, "subPath_7_delete_%d", id);
    sprintf(subConnName, "subConnName_%d", id);

    sprintf(sub_path_delete, "./multi_sub_7_delete/fib_path_7_subinfo_delete_%d.gmjson", id);
    readJanssonFile(sub_path_delete, &sub_info_delete);
    EXPECT_NE((void *)NULL, sub_info_delete);

    SnUserDataT *user_data;
    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //订阅#7的insert
    GmcSubConfigT tmp_sub_info_insert;
    tmp_sub_info_insert.subsName = subName_insert;
    tmp_sub_info_insert.configJson = sub_info_insert;
    ret = GmcSubscribe(stmt, &tmp_sub_info_insert, conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#7的delete
    GmcSubConfigT tmp_sub_info_delete;
    tmp_sub_info_delete.subsName = subName_delete;
    tmp_sub_info_delete.configJson = sub_info_delete;
    ret = GmcSubscribe(stmt, &tmp_sub_info_delete, conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    while (g_thread_wait < g_thr_num_sn + g_thr_num_dml) {
        usleep(500000);
        printf("sn wait for dml done, g_thread_wait : %d , g_thr_num_sn+g_thr_num_dml : %d\r\n", g_thread_wait,
            g_thr_num_sn + g_thr_num_dml);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num * g_thr_num_dml);
    EXPECT_EQ(0, ret);
    // printf("[testSnCheckDelete] thread_sn id = %d\r\n", id);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num * g_thr_num_dml);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(stmt, subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info_insert);
    free(sub_info_delete);
    testSnFreeUserData(user_data);
    return NULL;
}

void *thread_dml(void *args)
{
    int ret, i;
    int id = *((int *)args);

    void *label = 0, *label_query = 0;
    GmcStmtT *stmt = 0, *stmt_query = 0;
    GmcConnT *conn = 0;
    int affectRows;
    unsigned int len;
    void *labelT7 = NULL;
    void *labelT8 = NULL;
    void *labelT9 = NULL;
    void *labelT10 = NULL;
    void *labelT42 = NULL;

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < g_thr_num_sn) {
        usleep(500000);
        printf("dml wait for subscribe, g_thread_wait : %d , g_thr_num_sn : %d\r\n", g_thread_wait, g_thr_num_sn);
    }

    // GmcTxConfigT config;
    // config.transMode = GMC_TRANS_USED_IN_CS;
    // config.type = GMC_TX_ISOLATION_COMMITTED;
    // config.readOnly = false;
    // ret = GmcTransStart(conn, &config);
    // EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#8 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_8(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#9 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_9(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#10 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_10(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#42 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_42(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#7 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_7(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // ret = GmcTransCommit(conn);
    // EXPECT_EQ(GMERR_OK, ret);

    // config.transMode = GMC_TRANS_USED_IN_CS;
    // config.type = GMC_TX_ISOLATION_COMMITTED;
    // config.readOnly = false;
    // ret = GmcTransStart(conn, &config);
    // EXPECT_EQ(GMERR_OK, ret);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#7 GmcDeleteVertexByIndexKey] : %d\r\n", i);
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // ret = GmcTransCommit(conn);
    // EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

//多个线程创建订阅接口表的整表变更数据（包括新增、删除），然后再起多个线程做DML操作
TEST_F(PATHSubscriptionAndPush, SN_010_057)
{
    int i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;

    system("sh create_multi_sub.sh 7 insert 10");
    system("sh create_multi_sub.sh 7 delete 10");

    for (i = 0; i < g_thr_num_sn; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sn, (void *)&index[i]);
    }
    for (i = g_thr_num_sn; i < g_thr_num_sn + g_thr_num_dml; i++) {
        index[i] = i - g_thr_num_sn;
        pthread_create(&thr_arr[i], NULL, thread_dml, (void *)&index[i]);
    }

    for (i = 0; i < g_thr_num_sn; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    for (i = g_thr_num_sn; i < g_thr_num_sn + g_thr_num_dml; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    g_specialDisConnect = 1;
}

void *thread_sn_dml(void *args)
{
    int ret, i;
    int id = *((int *)args);

    void *label = 0;
    GmcStmtT *stmt = 0, *stmt_sub = 0;
    GmcConnT *conn = 0, *conn_sub = 0;

    int affectRows;
    unsigned int len;
    char *vertexLabelJsonT7 = NULL;
    char *vertexLabelJsonT8 = NULL;
    char *vertexLabelJsonT9 = NULL;
    char *vertexLabelJsonT10 = NULL;
    char *vertexLabelJsonT42 = NULL;
    char *edgeLabelJson_7_8 = NULL;
    char *edgeLabelJson_8_9 = NULL;
    char *edgeLabelJson_9_10 = NULL;
    char *edgeLabelJson_10_42 = NULL;
    void *labelT7 = NULL;
    void *labelT8 = NULL;
    void *labelT9 = NULL;
    void *labelT10 = NULL;
    void *labelT42 = NULL;
    char vertexLabelNameT7[128] = "ip4forward";
    char vertexLabelNameT8[128] = "nhp_group";
    char vertexLabelNameT9[128] = "nhp_group_node";
    char vertexLabelNameT10[128] = "nhp";
    char vertexLabelNameT42[128] = "nhp_std";
    char edgeLabelName_7_8[128] = "from_7_to_8";
    char edgeLabelName_8_9[128] = "from_8_to_9";
    char edgeLabelName_9_10[128] = "from_9_to_10";
    char edgeLabelName_10_42[128] = "from_10_to_42";
    char subConnName[128] = "subConnName";
    char subName_insert[128] = "subPath_7_insert";
    char *sub_info_insert = NULL;
    char sub_path_insert[512] = {0};
    char subName_delete[128] = "subPath_7_delete";
    char *sub_info_delete = NULL;
    char sub_path_delete[512] = {0};
    char vertexLabelJsonT7Path[512] = {0};
    char vertexLabelJsonT8Path[512] = {0};
    char vertexLabelJsonT9Path[512] = {0};
    char vertexLabelJsonT10Path[512] = {0};
    char vertexLabelJsonT42Path[512] = {0};
    char edgeLabelJson_7_8_Path[512] = {0};
    char edgeLabelJson_8_9_Path[512] = {0};
    char edgeLabelJson_9_10_Path[512] = {0};
    char edgeLabelJson_10_42_Path[512] = {0};

    SnUserDataT *user_data;
    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    sprintf(subName_insert, "subPath_7_insert_%d", id);
    sprintf(subConnName, "subConnName_%d", id);

    sprintf(sub_path_insert, "./multi_sub_7_insert/fib_path_7_subinfo_insert_%d.gmjson", id);
    readJanssonFile(sub_path_insert, &sub_info_insert);
    EXPECT_NE((void *)NULL, sub_info_insert);

    sprintf(subName_delete, "subPath_7_delete_%d", id);

    sprintf(sub_path_delete, "./multi_sub_7_delete/fib_path_7_subinfo_delete_%d.gmjson", id);
    readJanssonFile(sub_path_delete, &sub_info_delete);
    EXPECT_NE((void *)NULL, sub_info_delete);

    sprintf(vertexLabelNameT7, "ip4forward_%d", id);
    sprintf(vertexLabelNameT8, "nhp_group_%d", id);
    sprintf(vertexLabelNameT9, "nhp_group_node_%d", id);
    sprintf(vertexLabelNameT10, "nhp_%d", id);
    sprintf(vertexLabelNameT42, "nhp_std_%d", id);
    sprintf(edgeLabelName_7_8, "from_7_to_8_%d", id);
    sprintf(edgeLabelName_8_9, "from_8_to_9_%d", id);
    sprintf(edgeLabelName_9_10, "from_9_to_10_%d", id);
    sprintf(edgeLabelName_10_42, "from_10_to_42_%d", id);

    sprintf(vertexLabelJsonT7Path, "./multi_vertexlabel/ip4forward_%d.gmjson", id);
    readJanssonFile(vertexLabelJsonT7Path, &vertexLabelJsonT7);
    EXPECT_NE((void *)NULL, vertexLabelJsonT7);
    sprintf(vertexLabelJsonT8Path, "./multi_vertexlabel/nhp_group_%d.gmjson", id);
    readJanssonFile(vertexLabelJsonT8Path, &vertexLabelJsonT8);
    EXPECT_NE((void *)NULL, vertexLabelJsonT8);
    sprintf(vertexLabelJsonT9Path, "./multi_vertexlabel/nhp_group_node_%d.gmjson", id);
    readJanssonFile(vertexLabelJsonT9Path, &vertexLabelJsonT9);
    EXPECT_NE((void *)NULL, vertexLabelJsonT9);
    sprintf(vertexLabelJsonT10Path, "./multi_vertexlabel/nhp_%d.gmjson", id);
    readJanssonFile(vertexLabelJsonT10Path, &vertexLabelJsonT10);
    EXPECT_NE((void *)NULL, vertexLabelJsonT10);
    sprintf(vertexLabelJsonT42Path, "./multi_vertexlabel/nhp_std_%d.gmjson", id);
    readJanssonFile(vertexLabelJsonT42Path, &vertexLabelJsonT42);
    EXPECT_NE((void *)NULL, vertexLabelJsonT42);

    sprintf(edgeLabelJson_7_8_Path, "./multi_vertexlabel/edge_7_8_%d.gmjson", id);
    readJanssonFile(edgeLabelJson_7_8_Path, &edgeLabelJson_7_8);
    EXPECT_NE((void *)NULL, edgeLabelJson_7_8);
    sprintf(edgeLabelJson_8_9_Path, "./multi_vertexlabel/edge_8_9_%d.gmjson", id);
    readJanssonFile(edgeLabelJson_8_9_Path, &edgeLabelJson_8_9);
    EXPECT_NE((void *)NULL, edgeLabelJson_8_9);
    sprintf(edgeLabelJson_9_10_Path, "./multi_vertexlabel/edge_9_10_%d.gmjson", id);
    readJanssonFile(edgeLabelJson_9_10_Path, &edgeLabelJson_9_10);
    EXPECT_NE((void *)NULL, edgeLabelJson_9_10);
    sprintf(edgeLabelJson_10_42_Path, "./multi_vertexlabel/edge_10_42_%d.gmjson", id);
    readJanssonFile(edgeLabelJson_10_42_Path, &edgeLabelJson_10_42);
    EXPECT_NE((void *)NULL, edgeLabelJson_10_42);

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int chanRingLen = 256;
    //创建订阅连接
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //创建#7
    ret = GmcCreateVertexLabel(stmt, vertexLabelJsonT7, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#8
    ret = GmcCreateVertexLabel(stmt, vertexLabelJsonT8, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#9
    ret = GmcCreateVertexLabel(stmt, vertexLabelJsonT9, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#10
    ret = GmcCreateVertexLabel(stmt, vertexLabelJsonT10, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#42
    ret = GmcCreateVertexLabel(stmt, vertexLabelJsonT42, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_7_8
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson_7_8, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_8_9
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson_8_9, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_9_10
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson_9_10, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_10_42
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson_10_42, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);

    //订阅#7的insert
    GmcSubConfigT tmp_sub_info_insert;
    tmp_sub_info_insert.subsName = subName_insert;
    tmp_sub_info_insert.configJson = sub_info_insert;
    ret = GmcSubscribe(stmt, &tmp_sub_info_insert, conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#7的delete
    GmcSubConfigT tmp_sub_info_delete;
    tmp_sub_info_delete.subsName = subName_delete;
    tmp_sub_info_delete.configJson = sub_info_delete;
    ret = GmcSubscribe(stmt, &tmp_sub_info_delete, conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[#8 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_8(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[#9 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_9(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[#10 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_10(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[#42 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_42(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[#7 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_7(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[#7 GmcDeleteVertexByIndexKey] : %d\r\n", i);
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(0, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(stmt, subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(stmt, edgeLabelName_7_8);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(stmt, edgeLabelName_8_9);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(stmt, edgeLabelName_9_10);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(stmt, edgeLabelName_10_42);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    test_close_and_drop_label(stmt, labelT7, vertexLabelNameT7);
    test_close_and_drop_label(stmt, labelT8, vertexLabelNameT8);
    test_close_and_drop_label(stmt, labelT9, vertexLabelNameT9);
    test_close_and_drop_label(stmt, labelT10, vertexLabelNameT10);
    test_close_and_drop_label(stmt, labelT42, vertexLabelNameT42);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info_insert);
    free(sub_info_delete);
    free(vertexLabelJsonT7);
    free(vertexLabelJsonT8);
    free(vertexLabelJsonT9);
    free(vertexLabelJsonT10);
    free(vertexLabelJsonT42);
    free(edgeLabelJson_7_8);
    free(edgeLabelJson_8_9);
    free(edgeLabelJson_9_10);
    free(edgeLabelJson_10_42);
    testSnFreeUserData(user_data);
    return NULL;
}

//多个线程并发对多个表进行订阅推送
TEST_F(PATHSubscriptionAndPush, SN_010_060)
{
    int i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thr_num_sn = 10;
#ifdef ENV_RTOSV2X
    g_data_num = 10;
#else
    g_data_num = 100;
#endif

    system("sh create_multi_label.sh 10");
    system("sh create_multi_sub.sh 7 insert 10");
    system("sh create_multi_sub.sh 7 delete 10");
    system("sh sed_multi_sub.sh 7 insert 10");
    system("sh sed_multi_sub.sh 7 delete 10");

    for (i = 0; i < g_thr_num_sn; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sn_dml, (void *)&index[i]);
    }

    for (i = 0; i < g_thr_num_sn; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    g_specialDisConnect = 1;
}

void *thread_dml_async(void *args)
{
    int ret, i;
    int id = *((int *)args);
    GmcConnT *g_conn_async = NULL, *conn = 0;
    GmcStmtT *g_stmt_async = NULL, *stmt = 0, *stmt_query = 0;
    void *label = 0, *label_query = 0;
    int affectRows;
    unsigned int len;
    void *labelT7 = NULL;
    void *labelT8 = NULL;
    void *labelT9 = NULL;
    void *labelT10 = NULL;
    void *labelT42 = NULL;

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < g_thr_num_sn) {
        usleep(500000);
        printf("dml wait for subscribe, g_thread_wait : %d , g_thr_num_sn : %d\r\n", g_thread_wait, g_thr_num_sn);
    }

    //写#8的数据
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#8 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_8(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#9 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_9(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#10 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_10(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#42 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_42(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#7 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_7(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#7 GmcDeleteVertexByIndexKey] : %d\r\n", i);
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_async, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

//多个线程创建订阅接口表的整表变更数据（包括新增、删除），然后再起多个线程做异步DML操作
TEST_F(PATHSubscriptionAndPush, SN_010_061)
{
    int i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;

    system("sh create_multi_sub.sh 7 insert 10");
    system("sh create_multi_sub.sh 7 delete 10");

    for (i = 0; i < g_thr_num_sn; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sn, (void *)&index[i]);
    }
    for (i = g_thr_num_sn; i < g_thr_num_sn + g_thr_num_dml; i++) {
        index[i] = i - g_thr_num_sn;
        pthread_create(&thr_arr[i], NULL, thread_dml_async, (void *)&index[i]);
    }

    for (i = 0; i < g_thr_num_sn; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    for (i = g_thr_num_sn; i < g_thr_num_sn + g_thr_num_dml; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    g_specialDisConnect = 1;
}

void *thread_sn_2(void *args)
{
    int ret;
    int id = *((int *)args);

    GmcStmtT *stmt = 0, *stmt_sub = 0;
    GmcConnT *conn_sub = 0, *conn = 0;
    char subConnName[128] = "subConnName";
    char subName_insert_7[128] = "subPath_7_insert";
    char *sub_info_insert_7 = NULL;
    char sub_path_insert_7[512] = {0};
    char subName_insert_10[128] = "subPath_10_insert";
    char *sub_info_insert_10 = NULL;
    char sub_path_insert_10[512] = {0};

    SnUserDataT *user_data;
    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    //两个线程都至少订阅1个写表
    sprintf(subName_insert_7, "subPath_7_insert_%d", id);
    sprintf(subConnName, "subConnName_%d", id);

    sprintf(sub_path_insert_7, "./multi_sub_7_insert/fib_path_7_subinfo_insert_%d.gmjson", id);
    readJanssonFile(sub_path_insert_7, &sub_info_insert_7);
    EXPECT_NE((void *)NULL, sub_info_insert_7);

    sprintf(subName_insert_10, "subPath_10_insert_%d", id);
    sprintf(subConnName, "subConnName_%d", id);

    sprintf(sub_path_insert_10, "./multi_sub_10_insert/fib_path_10_subinfo_insert_%d.gmjson", id);
    readJanssonFile(sub_path_insert_10, &sub_info_insert_10);
    EXPECT_NE((void *)NULL, sub_info_insert_10);

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //订阅#7的insert
    GmcSubConfigT tmp_sub_info_insert_7;
    tmp_sub_info_insert_7.subsName = subName_insert_7;
    tmp_sub_info_insert_7.configJson = sub_info_insert_7;
    ret = GmcSubscribe(stmt, &tmp_sub_info_insert_7, conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    //订阅#10的insert
    GmcSubConfigT tmp_sub_info_insert_10;
    tmp_sub_info_insert_10.subsName = subName_insert_10;
    tmp_sub_info_insert_10.configJson = sub_info_insert_10;
    ret = GmcSubscribe(stmt, &tmp_sub_info_insert_10, conn_sub, sn_callback_not_cmp, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    while (g_thread_wait < g_thr_num_sn + g_thr_num_dml * 2) {
        usleep(500000);
        printf("sn wait for dml done, g_thread_wait : %d , g_thr_num_sn+g_thr_num_dml*2 : %d\r\n", g_thread_wait,
            g_thr_num_sn + g_thr_num_dml * 2);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num * g_thr_num_dml);
    EXPECT_EQ(0, ret);
    ret = GmcUnSubscribe(stmt, subName_insert_7);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName_insert_10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info_insert_7);
    free(sub_info_insert_10);
    testSnFreeUserData(user_data);
    return NULL;
}

void *thread_dml_7_8(void *args)
{
    int ret, i;
    int id = *((int *)args);
    GmcStmtT *stmt = 0, *stmt_query = 0;
    GmcConnT *conn = 0;
    void *label = 0, *label_query = 0;
    int affectRows;
    unsigned int len;
    void *labelT7 = NULL;
    void *labelT8 = NULL;
    void *labelT9 = NULL;
    void *labelT10 = NULL;
    void *labelT42 = NULL;

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < g_thr_num_sn) {
        usleep(500000);
        printf("dml wait for subscribe, g_thread_wait : %d , g_thr_num_sn : %d\r\n", g_thread_wait, g_thr_num_sn);
    }

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#8 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_8(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#7 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_7(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_dml_9_10_42(void *args)
{
    int ret, i;
    int id = *((int *)args);
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL, *stmt = 0, *conn = 0, *stmt_query = 0;
    void *label = 0, *label_query = 0;
    int affectRows;
    unsigned int len;
    void *labelT7 = NULL;
    void *labelT8 = NULL;
    void *labelT9 = NULL;
    void *labelT10 = NULL;
    void *labelT42 = NULL;

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < g_thr_num_sn) {
        usleep(500000);
        printf("dml wait for subscribe, g_thread_wait : %d , g_thr_num_sn : %d\r\n", g_thread_wait, g_thr_num_sn);
    }

    AsyncUserDataT data = {0};
    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#42 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_42(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    sleep(3);  //避免双向建边卡死

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#9 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_9(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num * id; i < g_data_num + g_data_num * id; i++) {
        // printf("[#10 GmcInsertVertex] : %d\r\n", i);
        test_setVertexProperty_10(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

//多个线程创建订阅接口表的整表变更数据（包括新增、删除），然后再起多个线程做DML操作(分2个线程写完1条PATH，线程1同步，线程2异步)
TEST_F(PATHSubscriptionAndPush, SN_010_062)
{
    int i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;

    system("sh create_multi_sub.sh 7 insert 10");
    system("sh create_multi_sub.sh 10 insert 10");

    //并发创建订阅线程
    for (i = 0; i < g_thr_num_sn; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sn_2, (void *)&index[i]);
    }
    //写#7、#8并发
    for (i = g_thr_num_sn; i < g_thr_num_sn + g_thr_num_dml; i++) {
        index[i] = i - g_thr_num_sn;
        pthread_create(&thr_arr[i], NULL, thread_dml_7_8, (void *)&index[i]);
    }
    //写#9、#10、#42并发
    for (i = g_thr_num_sn + g_thr_num_dml; i < g_thr_num_sn + g_thr_num_dml * 2; i++) {
        index[i] = i - g_thr_num_sn - g_thr_num_dml;
        pthread_create(&thr_arr[i], NULL, thread_dml_9_10_42, (void *)&index[i]);
    }

    for (i = 0; i < g_thr_num_sn; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    for (i = g_thr_num_sn; i < g_thr_num_sn + g_thr_num_dml; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    for (i = g_thr_num_sn + g_thr_num_dml; i < g_thr_num_sn + g_thr_num_dml * 2; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    g_specialDisConnect = 1;
}
